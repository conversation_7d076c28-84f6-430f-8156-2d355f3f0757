#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试DDD架构配置读取
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_user_config_adapter():
    """调试UserConfigAdapter"""
    print("🔍 调试UserConfigAdapter")
    print("=" * 50)
    
    try:
        from src.mythquant.infrastructure.config.adapters.user_config_adapter import UserConfigAdapter
        
        adapter = UserConfigAdapter()
        print("✅ UserConfigAdapter创建成功")
        
        # 测试_load_user_config方法
        print("\n📋 测试_load_user_config方法")
        user_config = adapter._load_user_config()
        print(f"   user_config类型: {type(user_config)}")
        print(f"   user_config键: {list(user_config.keys()) if isinstance(user_config, dict) else 'Not a dict'}")
        
        # 检查time_ranges
        time_ranges = user_config.get('time_ranges', {})
        print(f"   time_ranges类型: {type(time_ranges)}")
        print(f"   time_ranges键: {list(time_ranges.keys()) if isinstance(time_ranges, dict) else 'Not a dict'}")
        
        # 检查internet_minute配置
        internet_minute = time_ranges.get('internet_minute', {})
        print(f"   internet_minute类型: {type(internet_minute)}")
        print(f"   internet_minute内容: {internet_minute}")
        
        # 检查enabled状态
        enabled = internet_minute.get('enabled', False)
        print(f"   enabled值: {enabled}")
        print(f"   enabled类型: {type(enabled)}")
        print(f"   布尔判断: {bool(enabled)}")
        
        # 测试get_task_configs_data方法
        print("\n📋 测试get_task_configs_data方法")
        task_configs = adapter.get_task_configs_data()
        print(f"   任务配置数量: {len(task_configs)}")
        
        for i, config in enumerate(task_configs, 1):
            print(f"   任务 {i}:")
            print(f"      名称: {config.get('name')}")
            print(f"      开始时间: {config.get('start_time')}")
            print(f"      结束时间: {config.get('end_time')}")
            print(f"      数据类型: {config.get('data_type')}")
            print(f"      启用: {config.get('enabled')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_config_facade():
    """调试ConfigFacade"""
    print("\n🔍 调试ConfigFacade")
    print("=" * 50)
    
    try:
        from src.mythquant.interfaces.config.config_facade import ConfigFacade
        
        facade = ConfigFacade()
        print("✅ ConfigFacade创建成功")
        
        # 测试get_task_configs方法
        print("\n📋 测试get_task_configs方法")
        task_configs = facade.get_task_configs()
        print(f"   任务配置数量: {len(task_configs)}")
        
        for i, config in enumerate(task_configs, 1):
            print(f"   任务 {i}:")
            print(f"      名称: {config.get('name')}")
            print(f"      开始时间: {config.get('start_time')}")
            print(f"      结束时间: {config.get('end_time')}")
            print(f"      数据类型: {config.get('data_type')}")
            print(f"      启用: {config.get('enabled')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_config_manager():
    """调试config_manager"""
    print("\n🔍 调试config_manager")
    print("=" * 50)
    
    try:
        from src.mythquant.config import config_manager
        
        print("✅ config_manager导入成功")
        print(f"   config_manager类型: {type(config_manager)}")
        
        # 测试get_task_configs方法
        print("\n📋 测试get_task_configs方法")
        task_configs = config_manager.get_task_configs()
        print(f"   任务配置数量: {len(task_configs)}")
        
        for i, config in enumerate(task_configs, 1):
            print(f"   任务 {i}:")
            print(f"      名称: {config.get('name')}")
            print(f"      开始时间: {config.get('start_time')}")
            print(f"      结束时间: {config.get('end_time')}")
            print(f"      数据类型: {config.get('data_type')}")
            print(f"      启用: {config.get('enabled')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 DDD架构配置调试")
    print("=" * 60)
    
    # 调试UserConfigAdapter
    adapter_ok = debug_user_config_adapter()
    
    # 调试ConfigFacade
    facade_ok = debug_config_facade()
    
    # 调试config_manager
    manager_ok = debug_config_manager()
    
    print("\n" + "=" * 60)
    print("🔍 调试结果总结")
    print("=" * 60)
    print(f"UserConfigAdapter: {'✅' if adapter_ok else '❌'}")
    print(f"ConfigFacade: {'✅' if facade_ok else '❌'}")
    print(f"config_manager: {'✅' if manager_ok else '❌'}")
    
    if adapter_ok and not facade_ok:
        print("\n💡 问题分析：UserConfigAdapter正常但ConfigFacade异常")
        print("   可能原因：DDD架构组件之间的依赖有问题")
    elif not adapter_ok:
        print("\n💡 问题分析：UserConfigAdapter本身有问题")
        print("   可能原因：配置文件读取或解析错误")

if __name__ == '__main__':
    main()
