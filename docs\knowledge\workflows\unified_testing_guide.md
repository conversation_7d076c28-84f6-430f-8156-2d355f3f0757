# MythQuant 统一测试环境使用指南

## 🎯 概述

本指南介绍如何使用MythQuant的统一测试环境，该环境确保：
- **测试永远使用生产函数**：测试和生产使用完全相同的代码逻辑
- **路径和素材分离**：通过配置控制环境，不修改函数逻辑
- **测试素材保鲜**：每次测试都使用相同的初始状态
- **配置文件分离**：测试配置与用户配置完全独立

## 🏗️ 架构概览

```
MythQuant/
├── test_config.py                    # 测试专用配置
├── core/environment_manager.py       # 环境管理器
├── utils/unified_interfaces.py       # 统一接口层
├── test_environments/
│   ├── fixtures/                     # 测试素材（只读）
│   │   ├── minute_data/              # 分钟数据素材
│   │   ├── day_data/                 # 日级数据素材
│   │   └── fixture_manager.py        # 素材管理器
│   ├── sandbox/                      # 测试沙盒（可写）
│   ├── test_suites/                  # 测试套件
│   │   └── test_price_consistency.py # 价格一致性测试
│   └── ci_cd/                        # CI/CD集成
│       └── test_runner.py            # 自动化测试运行器
└── knowledge_base/
    └── unified_testing_guide.md      # 本指南
```

## 🚀 快速开始

### 1. 启用测试模式

```python
# 方法1：代码中启用（推荐）
from test_config import enable_test_mode
enable_test_mode()

# 方法2：环境变量启用
import os
os.environ['MYTHQUANT_TEST_MODE'] = '1'

# 方法3：配置文件启用
# 在 test_config.py 中设置 TEST_CONFIG['enabled'] = True
```

### 2. 使用统一接口

```python
# 价格一致性检查（自动适配测试/生产环境）
from utils.unified_interfaces import check_price_consistency

result = check_price_consistency(
    file_path='1min_0_000617_sample.txt',  # 逻辑路径
    stock_code='000617'
)

# 增量下载前提条件检查
from utils.unified_interfaces import check_incremental_download_prerequisite

has_prerequisite, details = check_incremental_download_prerequisite(
    existing_file='1min_0_000617_sample.txt',
    stock_code='000617'
)
```

### 3. 管理测试素材

```python
# 获取测试素材管理器
from test_environments.fixtures.fixture_manager import get_fixture_manager

manager = get_fixture_manager()

# 准备新鲜测试数据
fresh_path = manager.prepare_fresh_fixture(
    '1min_0_000617_20250320-20250704_来源互联网.txt',
    'test_environments/sandbox'
)

# 列出可用测试素材
fixtures = manager.list_fixtures()
for fixture in fixtures:
    print(f"- {fixture['name']}: {fixture['description']}")
```

## 🧪 测试套件使用

### 运行单个测试套件

```bash
# 价格一致性测试
python test_environments/test_suites/test_price_consistency.py
```

### 运行完整测试（CI/CD模式）

```bash
# 标准测试
python test_environments/ci_cd/test_runner.py --level standard

# 快速测试（仅核心功能）
python test_environments/ci_cd/test_runner.py --level quick

# 完整测试（包括性能测试）
python test_environments/ci_cd/test_runner.py --level full
```

### 查看测试报告

测试完成后，报告会自动生成在 `test_environments/reports/` 目录：
- `test_results_YYYYMMDD_HHMMSS.json` - JSON格式结果
- `test_report_YYYYMMDD_HHMMSS.html` - HTML格式报告

## 📋 开发最佳实践

### 1. 编写测试友好的代码

```python
# ✅ 推荐：使用统一接口
from utils.unified_interfaces import check_price_consistency

def my_function():
    result = check_price_consistency('test_file.txt', '000617')
    return result

# ❌ 不推荐：直接使用测试专用接口
from test_environments.shared.utilities.test_file_api_comparator import compare_test_file_with_api
```

### 2. 测试数据管理

```python
# ✅ 推荐：使用测试素材管理器
from test_environments.fixtures.fixture_manager import prepare_fresh_fixture

def test_my_function():
    # 每次测试都使用新鲜数据
    test_file = prepare_fresh_fixture('sample_data.txt', 'sandbox')
    result = my_function(test_file)
    assert result is not None

# ❌ 不推荐：直接使用固定文件
def test_my_function():
    result = my_function('fixed_test_file.txt')  # 可能被之前的测试修改
```

### 3. 环境检测

```python
# 检测当前环境
from core.environment_manager import is_test_environment

if is_test_environment():
    print("当前在测试环境中")
    # 使用测试配置
else:
    print("当前在生产环境中")
    # 使用生产配置
```

## 🔧 配置管理

### 测试配置文件 (test_config.py)

```python
# 测试环境基础配置
TEST_CONFIG = {
    'enabled': False,  # 是否启用测试模式
    'auto_detect': True,  # 是否自动检测测试环境
    
    # 路径配置
    'paths': {
        'fixtures_dir': 'test_environments/fixtures',
        'sandbox_dir': 'test_environments/sandbox',
        'results_dir': 'test_environments/results',
    },
    
    # 测试素材保鲜机制
    'data_freshness': {
        'copy_on_test': True,
        'readonly_fixtures': True,
        'sandbox_isolation': True,
    }
}
```

### 环境切换

```python
# 运行时切换环境
from core.environment_manager import get_environment_manager

env_manager = get_environment_manager()

# 切换到测试模式
env_manager.enable_test_mode()

# 切换回生产模式
env_manager.disable_test_mode()
```

## 📊 测试报告解读

### JSON报告格式

```json
{
  "success": true,
  "test_level": "standard",
  "start_time": "2025-08-01T10:00:00",
  "end_time": "2025-08-01T10:05:00",
  "summary": {
    "total": 5,
    "passed": 4,
    "failed": 1,
    "pass_rate": 0.8,
    "duration": 300.5
  },
  "suites": [
    {
      "suite_name": "test_price_consistency",
      "success": true,
      "duration": 45.2,
      "timestamp": "2025-08-01T10:01:00"
    }
  ]
}
```

### 关键指标

- **pass_rate**: 通过率，≥80% 为成功
- **duration**: 总耗时（秒）
- **success**: 整体测试是否成功

## 🚨 故障排除

### 常见问题

1. **测试素材不存在**
   ```
   解决方案：检查 test_environments/fixtures/ 目录中是否有相应文件
   ```

2. **环境检测失败**
   ```
   解决方案：手动启用测试模式 enable_test_mode()
   ```

3. **路径解析错误**
   ```
   解决方案：检查环境管理器配置，确保路径映射正确
   ```

### 调试技巧

```python
# 查看环境状态
from test_config import get_environment_status
status = get_environment_status()
print(status)

# 查看路径解析
from core.environment_manager import get_environment_manager
env_manager = get_environment_manager()
print(env_manager.get_environment_info())
```

## 🎯 团队协作

### 代码审查检查点

1. ✅ 是否使用统一接口而非测试专用接口？
2. ✅ 是否正确处理测试素材保鲜？
3. ✅ 是否添加了相应的测试用例？
4. ✅ 是否更新了相关文档？

### 持续集成集成

```yaml
# .github/workflows/test.yml 示例
name: MythQuant Tests
on: [push, pull_request]
jobs:
  test:
    runs-on: ubuntu-latest
    steps:
    - uses: actions/checkout@v2
    - name: Set up Python
      uses: actions/setup-python@v2
      with:
        python-version: 3.8
    - name: Run tests
      run: python test_environments/ci_cd/test_runner.py --level standard
```

## 📚 相关文档

- [1min_workflow_improved.md](1min_workflow_improved.md) - 1分钟数据处理工作流
- [api_interface_documentation.md](api_interface_documentation.md) - API接口文档
- [test_config.py](../test_config.py) - 测试配置文件
- [environment_manager.py](../core/environment_manager.py) - 环境管理器

---

**文档版本**: 1.0  
**创建时间**: 2025-08-01  
**维护者**: AI Assistant  
**适用范围**: MythQuant v1.17+
