#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存性能基准测试
对比优化前后的数据访问速度，收集性能数据用于优化决策
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime
import json

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cache import CacheManager


class CachePerformanceBenchmark:
    """缓存性能基准测试器"""
    
    def __init__(self):
        self.results = {}
        self.test_data = self._create_benchmark_datasets()
        
        # 创建不同配置的缓存管理器
        self.cache_configs = {
            'small': {
                'memory_max_size': 100,
                'file_max_size': 500,
                'cache_dir': './benchmark_cache_small'
            },
            'medium': {
                'memory_max_size': 1000,
                'file_max_size': 5000,
                'cache_dir': './benchmark_cache_medium'
            },
            'large': {
                'memory_max_size': 5000,
                'file_max_size': 20000,
                'cache_dir': './benchmark_cache_large'
            }
        }
    
    def _create_benchmark_datasets(self):
        """创建基准测试数据集"""
        datasets = {}
        
        # 小数据集：模拟单只股票的除权除息数据
        small_df = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10, freq='Y'),
            'fenhong': np.random.uniform(0, 1, 10),
            'songzhuangu': np.random.uniform(0, 0.5, 10)
        })
        datasets['small'] = small_df
        
        # 中等数据集：模拟日线数据
        medium_df = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=1000, freq='D'),
            'open': np.random.uniform(10, 20, 1000),
            'close': np.random.uniform(10, 20, 1000),
            'volume': np.random.randint(1000, 100000, 1000)
        })
        datasets['medium'] = medium_df
        
        # 大数据集：模拟分钟级数据
        large_df = pd.DataFrame({
            'datetime': pd.date_range('2024-01-01 09:30:00', periods=50000, freq='1min'),
            'open': np.random.uniform(10, 20, 50000),
            'high': np.random.uniform(10, 20, 50000),
            'low': np.random.uniform(10, 20, 50000),
            'close': np.random.uniform(10, 20, 50000),
            'volume': np.random.randint(100, 10000, 50000)
        })
        datasets['large'] = large_df
        
        return datasets
    
    def benchmark_write_performance(self):
        """基准测试写入性能"""
        print("📝 测试缓存写入性能...")
        
        write_results = {}
        
        for config_name, config in self.cache_configs.items():
            print(f"\n  测试配置: {config_name}")
            cache_manager = CacheManager(config)
            config_results = {}
            
            for data_size, data in self.test_data.items():
                # 测试写入时间
                start_time = time.time()
                
                key = f"benchmark_write_{data_size}"
                success = cache_manager.put_dataframe(key, data)
                
                write_time = time.time() - start_time
                
                config_results[data_size] = {
                    'write_time_ms': write_time * 1000,
                    'success': success,
                    'data_rows': len(data),
                    'data_size_mb': data.memory_usage(deep=True).sum() / 1024 / 1024
                }
                
                print(f"    {data_size}数据集: {write_time*1000:.2f}ms ({len(data)}行)")
            
            write_results[config_name] = config_results
            cache_manager.clear()  # 清理缓存
        
        self.results['write_performance'] = write_results
        return write_results
    
    def benchmark_read_performance(self):
        """基准测试读取性能"""
        print("\n📖 测试缓存读取性能...")
        
        read_results = {}
        
        for config_name, config in self.cache_configs.items():
            print(f"\n  测试配置: {config_name}")
            cache_manager = CacheManager(config)
            config_results = {}
            
            # 先写入数据
            for data_size, data in self.test_data.items():
                key = f"benchmark_read_{data_size}"
                cache_manager.put_dataframe(key, data)
            
            # 测试读取性能
            for data_size, data in self.test_data.items():
                key = f"benchmark_read_{data_size}"
                
                # 多次读取取平均值
                read_times = []
                for _ in range(10):
                    start_time = time.time()
                    result = cache_manager.get_dataframe(key)
                    read_time = time.time() - start_time
                    read_times.append(read_time)
                
                avg_read_time = np.mean(read_times)
                
                config_results[data_size] = {
                    'avg_read_time_ms': avg_read_time * 1000,
                    'min_read_time_ms': min(read_times) * 1000,
                    'max_read_time_ms': max(read_times) * 1000,
                    'success': result is not None,
                    'data_rows': len(data)
                }
                
                print(f"    {data_size}数据集: {avg_read_time*1000:.2f}ms (平均)")
            
            read_results[config_name] = config_results
            cache_manager.clear()
        
        self.results['read_performance'] = read_results
        return read_results
    
    def benchmark_hit_rate(self):
        """基准测试缓存命中率"""
        print("\n🎯 测试缓存命中率...")
        
        hit_rate_results = {}
        
        for config_name, config in self.cache_configs.items():
            print(f"\n  测试配置: {config_name}")
            cache_manager = CacheManager(config)
            
            # 存储测试数据
            test_keys = []
            for i, (data_size, data) in enumerate(self.test_data.items()):
                key = f"hit_rate_test_{i}_{data_size}"
                test_keys.append(key)
                cache_manager.put_dataframe(key, data)
            
            # 多次访问测试命中率
            access_count = 50
            for _ in range(access_count):
                for key in test_keys:
                    cache_manager.get_dataframe(key)
            
            # 获取统计信息
            stats = cache_manager.get_cache_stats()
            global_stats = stats['global_stats']
            
            hit_rate_results[config_name] = {
                'hit_rate': global_stats['hit_rate'],
                'total_requests': global_stats['total_requests'],
                'hits': global_stats['hits'],
                'misses': global_stats['misses'],
                'avg_response_time_ms': global_stats['avg_response_time'] * 1000
            }
            
            print(f"    命中率: {global_stats['hit_rate']:.2%}")
            print(f"    平均响应时间: {global_stats['avg_response_time']*1000:.2f}ms")
            
            cache_manager.clear()
        
        self.results['hit_rate_performance'] = hit_rate_results
        return hit_rate_results
    
    def benchmark_memory_usage(self):
        """基准测试内存使用情况"""
        print("\n💾 测试内存使用情况...")
        
        memory_results = {}
        
        for config_name, config in self.cache_configs.items():
            print(f"\n  测试配置: {config_name}")
            cache_manager = CacheManager(config)
            
            # 逐步添加数据，监控内存使用
            memory_usage = []
            
            for data_size, data in self.test_data.items():
                key = f"memory_test_{data_size}"
                cache_manager.put_dataframe(key, data)
                
                stats = cache_manager.get_cache_stats()
                memory_info = {
                    'data_size': data_size,
                    'memory_cache_size': stats['memory_cache']['cache_size'],
                    'file_cache_size': stats['file_cache']['cache_size'],
                    'memory_usage_bytes': stats['memory_cache'].get('memory_usage_bytes', 0)
                }
                memory_usage.append(memory_info)
                
                print(f"    添加{data_size}数据后 - 内存缓存:{memory_info['memory_cache_size']}项, "
                      f"文件缓存:{memory_info['file_cache_size']}项")
            
            memory_results[config_name] = memory_usage
            cache_manager.clear()
        
        self.results['memory_usage'] = memory_results
        return memory_results
    
    def run_full_benchmark(self):
        """运行完整的基准测试"""
        print("🚀 开始缓存性能基准测试...")
        print("=" * 60)
        
        start_time = time.time()
        
        # 运行各项基准测试
        self.benchmark_write_performance()
        self.benchmark_read_performance()
        self.benchmark_hit_rate()
        self.benchmark_memory_usage()
        
        total_time = time.time() - start_time
        
        print(f"\n⏱️  基准测试完成，总耗时: {total_time:.2f}秒")
        
        # 生成性能报告
        self.generate_performance_report()
        
        return self.results
    
    def generate_performance_report(self):
        """生成性能测试报告"""
        report_file = f"cache_performance_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        # 添加测试环境信息
        self.results['test_info'] = {
            'test_time': datetime.now().isoformat(),
            'python_version': sys.version,
            'test_datasets': {k: len(v) for k, v in self.test_data.items()}
        }
        
        # 保存详细报告
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"\n📊 性能报告已保存: {report_file}")
        
        # 生成简要总结
        self.print_performance_summary()
    
    def print_performance_summary(self):
        """打印性能测试总结"""
        print("\n📈 性能测试总结:")
        print("=" * 40)
        
        # 写入性能总结
        if 'write_performance' in self.results:
            print("\n📝 写入性能 (平均):")
            for config_name, config_data in self.results['write_performance'].items():
                avg_write_time = np.mean([data['write_time_ms'] for data in config_data.values()])
                print(f"  {config_name:8}: {avg_write_time:.2f}ms")
        
        # 读取性能总结
        if 'read_performance' in self.results:
            print("\n📖 读取性能 (平均):")
            for config_name, config_data in self.results['read_performance'].items():
                avg_read_time = np.mean([data['avg_read_time_ms'] for data in config_data.values()])
                print(f"  {config_name:8}: {avg_read_time:.2f}ms")
        
        # 命中率总结
        if 'hit_rate_performance' in self.results:
            print("\n🎯 缓存命中率:")
            for config_name, data in self.results['hit_rate_performance'].items():
                print(f"  {config_name:8}: {data['hit_rate']:.2%}")
    
    def cleanup(self):
        """清理测试缓存"""
        import shutil
        for config in self.cache_configs.values():
            cache_dir = config['cache_dir']
            if os.path.exists(cache_dir):
                try:
                    shutil.rmtree(cache_dir)
                    print(f"✅ 清理缓存目录: {cache_dir}")
                except Exception as e:
                    print(f"⚠️  清理失败 {cache_dir}: {e}")


def main():
    """主函数"""
    benchmark = CachePerformanceBenchmark()
    
    try:
        results = benchmark.run_full_benchmark()
        return results
    finally:
        benchmark.cleanup()


if __name__ == '__main__':
    main()
