#!/bin/bash
# 项目根目录清理脚本
# 生成时间: 2025-08-04 23:57:29

echo "🧹 开始项目根目录清理..."

# 1. 创建归档目录结构
echo "📁 创建归档目录结构..."
mkdir -p archive/{backups,legacy,temp_files}

# 2. 删除临时目录
echo "🗑️ 删除临时目录..."
rm -rf __pycache__

# 3. 归档备份目录
echo "📦 归档备份目录..."
[ -d architecture_migration_final_archive_20250802_233447 ] && mv architecture_migration_final_archive_20250802_233447 archive/backups/
[ -d backup_test_integration_20250803_232702 ] && mv backup_test_integration_20250803_232702 archive/backups/

# 4. 归档遗留目录
echo "📚 归档遗留目录..."
[ -d environments ] && mv environments archive/legacy/
[ -d lib ] && mv lib archive/legacy/
[ -d assets ] && mv assets archive/legacy/

# 5. 归档临时文件
echo "📄 归档临时文件..."
[ -f comprehensive_1min_test.py ] && mv comprehensive_1min_test.py archive/temp_files/
[ -f comprehensive_test_with_proper_config.py ] && mv comprehensive_test_with_proper_config.py archive/temp_files/
[ -f comprehensive_test_report.md ] && mv comprehensive_test_report.md archive/temp_files/
[ -f architecture_cleanup_report_20241220.json ] && mv architecture_cleanup_report_20241220.json archive/temp_files/
[ -f cleanup_report_20250802_233731.txt ] && mv cleanup_report_20250802_233731.txt archive/temp_files/
[ -f test_integration_report_20250803_232702.json ] && mv test_integration_report_20250803_232702.json archive/temp_files/

# 6. 清理日志文件（保留最近30天）
echo "🧹 清理旧日志文件..."
find logs/ -name "*.log" -mtime +30 -exec mv {} archive/temp_files/ \; 2>/dev/null || true
find logs/ -name "*.json" -mtime +30 -exec mv {} archive/temp_files/ \; 2>/dev/null || true

echo "✅ 根目录清理完成！"
echo "📋 清理后的目录结构："
ls -la | grep "^d"
