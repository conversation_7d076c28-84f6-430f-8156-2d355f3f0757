#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档自动化工具

提供文档的自动化检查、更新和维护功能
"""

import os
import subprocess
from pathlib import Path
from datetime import datetime
from typing import List, Dict
import argparse


class DocumentationAutomation:
    """文档自动化管理器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.docs_root = self.project_root / "docs"
        self.tools_dir = self.project_root / "tools"
    
    def run_quality_check(self) -> Dict:
        """运行质量检查"""
        print("🔍 运行文档质量检查...")
        
        try:
            result = subprocess.run([
                'python', str(self.tools_dir / 'documentation_quality_monitor.py')
            ], capture_output=True, text=True, cwd=self.project_root)
            
            return {
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr
            }
        except Exception as e:
            return {
                'success': False,
                'output': '',
                'error': str(e)
            }
    
    def run_link_check(self) -> Dict:
        """运行链接检查"""
        print("🔗 检查文档链接...")
        
        try:
            result = subprocess.run([
                'python', str(self.tools_dir / 'documentation_search.py'), 'links'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            return {
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr
            }
        except Exception as e:
            return {
                'success': False,
                'output': '',
                'error': str(e)
            }
    
    def run_naming_check(self) -> Dict:
        """运行命名规范检查"""
        print("📝 检查文档命名规范...")
        
        try:
            result = subprocess.run([
                'python', str(self.tools_dir / 'documentation_standardizer.py')
            ], capture_output=True, text=True, cwd=self.project_root)
            
            return {
                'success': result.returncode == 0,
                'output': result.stdout,
                'error': result.stderr
            }
        except Exception as e:
            return {
                'success': False,
                'output': '',
                'error': str(e)
            }
    
    def update_documentation_index(self) -> bool:
        """更新文档索引"""
        print("📚 更新文档索引...")
        
        try:
            # 生成新的文档索引
            index_content = self._generate_index_content()
            
            # 更新索引文件
            index_file = self.docs_root / "DOCUMENTATION_INDEX.md"
            with open(index_file, 'w', encoding='utf-8') as f:
                f.write(index_content)
            
            print(f"✅ 文档索引已更新: {index_file}")
            return True
            
        except Exception as e:
            print(f"❌ 更新文档索引失败: {e}")
            return False
    
    def generate_sitemap(self) -> bool:
        """生成站点地图"""
        print("🗺️ 生成文档站点地图...")
        
        try:
            result = subprocess.run([
                'python', str(self.tools_dir / 'documentation_search.py'), 'sitemap'
            ], capture_output=True, text=True, cwd=self.project_root)
            
            if result.returncode == 0:
                # 保存站点地图到文件
                sitemap_file = self.docs_root / "SITEMAP.md"
                sitemap_content = f"""---
title: "文档站点地图"
version: "v1.0"
date: "{datetime.now().strftime('%Y-%m-%d')}"
author: "文档自动化系统"
status: "发布"
category: "管理"
tags: ["站点地图", "自动生成", "导航"]
last_updated: "{datetime.now().strftime('%Y-%m-%d')}"
---

# 文档站点地图

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

{result.stdout}

---

**自动生成**: 文档自动化系统  
**更新频率**: 每日自动更新
"""
                
                with open(sitemap_file, 'w', encoding='utf-8') as f:
                    f.write(sitemap_content)
                
                print(f"✅ 站点地图已生成: {sitemap_file}")
                return True
            else:
                print(f"❌ 生成站点地图失败: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"❌ 生成站点地图失败: {e}")
            return False
    
    def run_full_check(self) -> Dict:
        """运行完整检查"""
        print("🚀 运行完整文档检查...")
        print("=" * 50)
        
        results = {
            'quality_check': self.run_quality_check(),
            'link_check': self.run_link_check(),
            'naming_check': self.run_naming_check(),
            'index_update': self.update_documentation_index(),
            'sitemap_generation': self.generate_sitemap()
        }
        
        # 统计结果
        success_count = sum(1 for result in results.values() 
                          if (isinstance(result, dict) and result.get('success')) or 
                             (isinstance(result, bool) and result))
        
        print("\n" + "=" * 50)
        print(f"📊 检查完成: {success_count}/{len(results)} 项成功")
        
        return results
    
    def _generate_index_content(self) -> str:
        """生成索引内容"""
        # 扫描文档目录结构
        structure = self._scan_docs_structure()
        
        content = f"""---
title: "MythQuant 文档总索引"
version: "v2.1"
date: "{datetime.now().strftime('%Y-%m-%d')}"
author: "文档自动化系统"
status: "发布"
category: "管理"
tags: ["文档索引", "导航", "自动生成"]
last_updated: "{datetime.now().strftime('%Y-%m-%d')}"
---

# MythQuant 文档总索引

**自动生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

本文档索引由自动化系统生成，提供完整的文档导航和快速访问。

## 📁 文档结构

"""
        
        # 添加目录结构
        for category, files in structure.items():
            if files:
                content += f"### {category}\n\n"
                for file_info in files:
                    content += f"- [{file_info['title']}]({file_info['path']}) - {file_info['description']}\n"
                content += "\n"
        
        content += f"""
## 🔍 快速访问

- [快速导航指南](QUICK_NAVIGATION.md) - 快速访问常用文档
- [文档标准规范](DOCUMENTATION_STANDARDS.md) - 文档编写和维护标准
- [站点地图](SITEMAP.md) - 完整的文档站点地图

## 📊 文档统计

- **总文档数**: {sum(len(files) for files in structure.values())}
- **主要分类**: {len([cat for cat, files in structure.items() if files])}
- **最后更新**: {datetime.now().strftime('%Y-%m-%d')}

---

**自动维护**: 文档自动化系统  
**更新频率**: 每日自动更新  
**手动更新**: 运行 `python tools/documentation_automation.py --full-check`
"""
        
        return content
    
    def _scan_docs_structure(self) -> Dict[str, List[Dict]]:
        """扫描文档结构"""
        structure = {
            "根目录文档": [],
            "使用指南": [],
            "技术文档": [],
            "API文档": [],
            "架构文档": [],
            "项目文档": [],
            "报告文档": [],
            "知识库": [],
            "模板文档": []
        }
        
        # 扫描各个目录
        category_mapping = {
            ".": "根目录文档",
            "guides": "使用指南",
            "api": "API文档",
            "architecture": "架构文档",
            "project": "项目文档",
            "reports": "报告文档",
            "knowledge": "知识库",
            "templates": "模板文档"
        }
        
        for file_path in self.docs_root.rglob('*.md'):
            relative_path = file_path.relative_to(self.docs_root)
            category_dir = str(relative_path.parent) if relative_path.parent != Path('.') else "."
            
            # 确定分类
            category = category_mapping.get(category_dir, "其他文档")
            if category not in structure:
                structure[category] = []
            
            # 提取文件信息
            file_info = {
                'title': self._extract_title(file_path),
                'path': str(relative_path).replace('\\', '/'),
                'description': self._extract_description(file_path)
            }
            
            structure[category].append(file_info)
        
        # 排序
        for category in structure:
            structure[category].sort(key=lambda x: x['title'])
        
        return structure
    
    def _extract_title(self, file_path: Path) -> str:
        """提取文档标题"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 尝试从YAML元数据提取
            if content.startswith('---'):
                yaml_end = content.find('---', 3)
                if yaml_end != -1:
                    yaml_content = content[3:yaml_end]
                    if 'title:' in yaml_content:
                        title_match = re.search(r'title:\s*["\']?([^"\'\n]+)["\']?', yaml_content)
                        if title_match:
                            return title_match.group(1).strip()
            
            # 尝试从第一个标题提取
            title_match = re.search(r'^#\s+(.+)$', content, re.MULTILINE)
            if title_match:
                return title_match.group(1).strip()
            
            # 使用文件名
            return file_path.stem.replace('_', ' ').title()
            
        except:
            return file_path.stem.replace('_', ' ').title()
    
    def _extract_description(self, file_path: Path) -> str:
        """提取文档描述"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找概述部分
            overview_patterns = [
                r'## 概述\s*\n\s*([^\n#]+)',
                r'## Overview\s*\n\s*([^\n#]+)',
                r'## 描述\s*\n\s*([^\n#]+)',
                r'## Description\s*\n\s*([^\n#]+)'
            ]
            
            for pattern in overview_patterns:
                match = re.search(pattern, content, re.IGNORECASE)
                if match:
                    return match.group(1).strip()[:100] + "..."
            
            # 使用文件名作为描述
            return f"{file_path.stem.replace('_', ' ')}相关文档"
            
        except:
            return "文档说明"


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MythQuant 文档自动化工具')
    parser.add_argument('--quality-check', action='store_true', help='运行质量检查')
    parser.add_argument('--link-check', action='store_true', help='检查链接')
    parser.add_argument('--naming-check', action='store_true', help='检查命名规范')
    parser.add_argument('--update-index', action='store_true', help='更新文档索引')
    parser.add_argument('--generate-sitemap', action='store_true', help='生成站点地图')
    parser.add_argument('--full-check', action='store_true', help='运行完整检查')
    
    args = parser.parse_args()
    
    automation = DocumentationAutomation()
    
    if args.full_check or not any(vars(args).values()):
        # 默认运行完整检查
        automation.run_full_check()
    else:
        if args.quality_check:
            automation.run_quality_check()
        if args.link_check:
            automation.run_link_check()
        if args.naming_check:
            automation.run_naming_check()
        if args.update_index:
            automation.update_documentation_index()
        if args.generate_sitemap:
            automation.generate_sitemap()


if __name__ == "__main__":
    main()
