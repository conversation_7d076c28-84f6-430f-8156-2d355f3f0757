#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易配置实体

包含交易相关的配置信息
"""

from dataclasses import dataclass
from typing import List, Optional, Dict, Any
from ..value_objects.tdx_connection import TdxConnection
from ..value_objects.file_path import FilePath


@dataclass
class TradingConfig:
    """交易配置实体"""
    
    # TDX连接配置
    tdx_connection: TdxConnection
    
    # 输出路径配置
    output_path: FilePath
    
    # 目标股票配置
    target_stocks_file: Optional[FilePath] = None
    default_stocks: List[str] = None
    
    # GBBQ文件路径
    gbbq_path: Optional[str] = None
    
    # pytdx配置
    pytdx_config: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        if self.default_stocks is None:
            self.default_stocks = ["000617"]  # 默认股票
        
        if self.pytdx_config is None:
            self.pytdx_config = {
                'auto_detect': False,
                'show_top5': True,
                'smart_detect': True,
                'blacklist_enabled': True,
                'blacklist_timeout': 7200,
                'kline_limits': {
                    '1min': 800,
                    '5min': 800,
                    '15min': 800,
                    '30min': 800,
                    '60min': 800,
                    'daily': 800,
                },
                'data_buffer_factor': 1.0
            }
    
    def validate(self) -> bool:
        """验证配置的业务规则"""
        try:
            # 验证TDX连接
            if not self.tdx_connection.is_valid():
                return False
            
            # 验证输出路径
            if not self.output_path.path:
                return False
            
            # 验证目标股票配置
            if self.target_stocks_file and not self.target_stocks_file.exists():
                # 目标股票文件不存在，但有默认股票列表也可以
                if not self.default_stocks:
                    return False
            
            # 验证pytdx配置
            if self.pytdx_config:
                required_keys = ['kline_limits', 'data_buffer_factor']
                if not all(key in self.pytdx_config for key in required_keys):
                    return False
            
            return True
            
        except Exception:
            return False
    
    def get_target_stocks(self) -> List[str]:
        """获取目标股票列表"""
        # 如果有目标股票文件且存在，优先使用文件中的股票
        if self.target_stocks_file and self.target_stocks_file.exists():
            try:
                # 这里应该调用应用层服务来读取文件
                # 暂时返回默认股票
                pass
            except Exception:
                pass
        
        # 返回默认股票列表
        return self.default_stocks or ["000617"]
    
    def get_pytdx_ip(self) -> str:
        """获取pytdx IP地址"""
        return self.tdx_connection.ip
    
    def get_pytdx_port(self) -> int:
        """获取pytdx端口"""
        return self.tdx_connection.port
    
    def get_kline_limit(self, frequency: str) -> int:
        """获取K线数据限制"""
        if not self.pytdx_config:
            return 800  # 默认限制
        
        limits = self.pytdx_config.get('kline_limits', {})
        return limits.get(frequency, 800)
    
    def is_auto_detect_enabled(self) -> bool:
        """是否启用自动检测"""
        if not self.pytdx_config:
            return False
        return self.pytdx_config.get('auto_detect', False)
    
    def get_data_buffer_factor(self) -> float:
        """获取数据缓冲区系数"""
        if not self.pytdx_config:
            return 1.0
        return self.pytdx_config.get('data_buffer_factor', 1.0)
    
    def __str__(self) -> str:
        return f"TradingConfig(tdx={self.tdx_connection}, output={self.output_path})"
