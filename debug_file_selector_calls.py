#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试智能文件选择器调用
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def patch_smart_file_selector():
    """给智能文件选择器打补丁，追踪调用"""
    print("🔍 给智能文件选择器打补丁，追踪调用")
    print("=" * 50)
    
    try:
        from utils.smart_file_selector import SmartFileSelector
        
        # 保存原始方法
        original_find_best_file = SmartFileSelector.find_best_file
        
        def patched_find_best_file(self, stock_code, data_type="minute", target_start=None, target_end=None, strategy='smart_comprehensive'):
            """打补丁的find_best_file方法"""
            import traceback
            
            print(f"\n🚨 智能文件选择器被调用！")
            print(f"   股票代码: {stock_code}")
            print(f"   数据类型: {data_type}")
            print(f"   目标开始: {target_start}")
            print(f"   目标结束: {target_end}")
            print(f"   策略: {strategy}")
            
            print(f"\n📋 调用堆栈:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-5:], 1):  # 显示最后5个调用帧
                print(f"   {i}. {frame.strip()}")
            
            # 调用原始方法
            return original_find_best_file(self, stock_code, data_type, target_start, target_end, strategy)
        
        # 替换方法
        SmartFileSelector.find_best_file = patched_find_best_file
        
        print("✅ 智能文件选择器补丁安装成功")
        return True
        
    except Exception as e:
        print(f"❌ 安装补丁失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_incremental_download():
    """测试增量下载过程"""
    print("\n🔍 测试增量下载过程")
    print("=" * 50)
    
    try:
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        downloader = StructuredInternetMinuteDownloader()
        print("✅ 结构化下载器创建成功")
        
        # 模拟增量下载调用
        print("\n📊 模拟增量下载调用...")
        
        # 这里不实际执行，只是为了触发调用链
        print("💡 如果要实际测试，需要调用 _execute_four_step_process 方法")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_call_chain():
    """分析调用链"""
    print("\n🔍 分析可能的调用链")
    print("=" * 50)
    
    print("📋 可能的调用路径：")
    print("   1. structured_internet_minute_downloader._step4_incremental_download")
    print("   2. stock_data_downloader._save_minute_data_incremental")
    print("   3. incremental_downloader.merge_data_files")
    print("   4. smart_file_selector.should_rename_after_update")
    print("   5. ??? 可能在某个地方有隐藏的调用")
    
    print("\n💡 调试建议：")
    print("   - 在智能文件选择器的find_best_file方法中添加调用堆栈追踪")
    print("   - 检查是否在merge_data_files中有其他调用")
    print("   - 检查是否在错误处理分支中有调用")
    
    return True

def main():
    """主函数"""
    print("🚀 智能文件选择器调用调试")
    print("=" * 60)
    
    # 安装补丁
    patch_ok = patch_smart_file_selector()
    
    # 测试增量下载
    test_ok = test_incremental_download()
    
    # 分析调用链
    analyze_ok = analyze_call_chain()
    
    print("\n" + "=" * 60)
    print("🔍 调试结果总结")
    print("=" * 60)
    print(f"补丁安装: {'✅' if patch_ok else '❌'}")
    print(f"增量下载测试: {'✅' if test_ok else '❌'}")
    print(f"调用链分析: {'✅' if analyze_ok else '❌'}")
    
    if patch_ok:
        print("\n💡 下一步：")
        print("   运行 python main.py 来触发实际的调用")
        print("   观察terminal输出中的调用堆栈信息")
        print("   找到违反workflow规范的调用位置")

if __name__ == '__main__':
    main()
