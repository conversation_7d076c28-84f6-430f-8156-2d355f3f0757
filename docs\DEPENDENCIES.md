# MythQuant 依赖库统计报告

## 📊 依赖库概览

根据代码分析，MythQuant项目共依赖 **11个第三方库**：

- **必需依赖**: 9个
- **可选依赖**: 2个

## 🔧 必需依赖库 (9个)

| 库名 | 版本要求 | 用途 | 在项目中的作用 |
|------|----------|------|----------------|
| **pandas** | >=1.5.0 | 数据处理和分析 | 核心数据结构，DataFrame操作，时间序列处理 |
| **numpy** | >=1.24.0 | 数值计算 | 数组计算，数学运算，性能优化 |
| **mootdx** | >=1.2.0 | 通达信数据读取 | 读取通达信分钟数据，日线数据 |
| **pytdx** | >=1.72 | 通达信数据格式解析 | 解析gbbq除权除息文件，二进制数据读取 |
| **openpyxl** | >=3.0.0 | Excel文件读写(.xlsx) | 读取目标股票代码Excel文件 |
| **xlrd** | >=2.0.0 | Excel文件读取(.xls) | 兼容旧版Excel文件格式 |
| **requests** | >=2.28.0 | HTTP网络请求 | 网络数据获取，API调用 |
| **tqdm** | >=4.64.0 | 进度条显示 | 批量处理进度显示，用户体验优化 |
| **retry** | >=0.9.2 | 重试机制 | 网络请求重试，提高稳定性 |

## 📦 可选依赖库 (2个)

| 库名 | 版本要求 | 用途 | 说明 |
|------|----------|------|------|
| **terminaltables** | >=3.1.10 | 表格显示 | 美化控制台输出，表格格式化 |
| **tushare** | >=1.2.89 | 股票数据接口 | 第三方股票数据源，可选数据补充 |

## 🏗️ 依赖关系分析

### 核心数据处理层
```
pandas (数据结构) + numpy (数值计算)
    ↓
DataFrame操作、时间序列处理、数学运算
```

### 数据源访问层
```
mootdx (通达信读取) + pytdx (格式解析)
    ↓
分钟数据、日线数据、除权除息数据
```

### 文件处理层
```
openpyxl + xlrd (Excel处理)
    ↓
目标股票代码列表读取
```

### 网络和工具层
```
requests (网络) + retry (重试) + tqdm (进度)
    ↓
稳定的网络数据获取和用户体验
```

## 📈 依赖使用统计

### 在主要文件中的使用频率

| 文件 | 依赖数量 | 主要依赖 |
|------|----------|----------|
| `main_v20230219_optimized.py` | 6个 | pandas, numpy, mootdx, pytdx, openpyxl, xlrd |
| `func_Util.py` | 8个 | pandas, numpy, requests, tqdm, retry, pytdx |
| `func.py` | 8个 | pandas, numpy, requests, tqdm, retry, pytdx, tushare |
| `readTDX_cw.py` | 4个 | pandas, requests, pytdx |

### 依赖重要性分析

**🔴 核心依赖 (不可替代)**:
- `pandas` - 项目的数据处理基础
- `numpy` - 数值计算基础
- `mootdx` - 通达信数据读取的唯一方案
- `pytdx` - 通达信格式解析的唯一方案

**🟡 重要依赖 (可有替代方案)**:
- `requests` - 可用urllib替代，但requests更方便
- `openpyxl/xlrd` - 可用其他Excel库替代
- `tqdm` - 可移除，但影响用户体验

**🟢 辅助依赖 (可选)**:
- `retry` - 可用自定义重试逻辑替代
- `terminaltables` - 纯显示优化
- `tushare` - 可选数据源

## 🛠️ 依赖管理方案

### 1. 自动安装方案
```bash
# 一键安装所有依赖
python install_dependencies.py
```

### 2. 分层安装方案
```bash
# 核心依赖
pip install pandas numpy mootdx pytdx

# 文件处理
pip install openpyxl xlrd

# 网络和工具
pip install requests tqdm retry

# 可选依赖
pip install terminaltables tushare
```

### 3. 环境管理方案
```bash
# pip方式
pip install -r requirements.txt

# conda方式
conda env create -f environment.yml
```

## 🔍 依赖检查工具

项目提供了完整的依赖管理工具：

| 工具 | 功能 | 使用方法 |
|------|------|----------|
| `dependency_check.py` | 检查已安装依赖状态 | `python dependency_check.py` |
| `install_dependencies.py` | 自动安装缺失依赖 | `python install_dependencies.py` |
| `requirements.txt` | pip依赖列表 | `pip install -r requirements.txt` |
| `environment.yml` | conda环境配置 | `conda env create -f environment.yml` |

## ⚠️ 常见依赖问题

### 1. mootdx版本过低
```bash
# 当前检测到版本: 0.11.7
# 要求版本: >=1.2.0
pip install --upgrade mootdx
```

### 2. pytdx版本未知
```bash
# pytdx库可能没有标准版本号
pip install --upgrade pytdx
```

### 3. Excel库冲突
```bash
# 同时需要支持.xls和.xlsx
pip install openpyxl xlrd
```

## 📊 依赖大小统计

估算的依赖库安装大小：

| 类别 | 大小估算 | 说明 |
|------|----------|------|
| 核心数据处理 (pandas+numpy) | ~100MB | 最大的依赖组件 |
| 通达信库 (mootdx+pytdx) | ~10MB | 专用数据读取库 |
| Excel处理 (openpyxl+xlrd) | ~5MB | 文件处理库 |
| 网络工具 (requests+tqdm+retry) | ~3MB | 轻量级工具库 |
| 可选库 (terminaltables+tushare) | ~5MB | 可选组件 |
| **总计** | **~123MB** | 完整安装大小 |

## 🚀 优化建议

### 1. 最小化安装
如果只需要核心功能，可以只安装：
```bash
pip install pandas numpy mootdx pytdx
```

### 2. 虚拟环境隔离
```bash
python -m venv mythquant_env
source mythquant_env/bin/activate  # Linux/Mac
# 或
mythquant_env\Scripts\activate     # Windows
```

### 3. 定期更新
```bash
pip list --outdated
pip install --upgrade pandas numpy mootdx pytdx
```

## 📝 依赖许可证

所有依赖库都使用开源许可证，与项目的MIT许可证兼容：

- pandas, numpy: BSD许可证
- requests, tqdm: Apache许可证
- openpyxl: MIT许可证
- 其他库: 各自的开源许可证

## 🎯 总结

MythQuant项目的依赖管理具有以下特点：

✅ **依赖数量适中**: 11个依赖库，不算过多  
✅ **核心依赖稳定**: pandas、numpy等成熟库  
✅ **专业性强**: mootdx、pytdx等专用于通达信数据  
✅ **管理工具完善**: 提供多种安装和检查方案  
✅ **文档详细**: 每个依赖的作用都有明确说明  

通过合理的依赖管理，确保项目的稳定性和可维护性。 