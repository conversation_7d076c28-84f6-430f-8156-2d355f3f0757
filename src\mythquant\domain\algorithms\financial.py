#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金融算法模块

纯函数金融算法实现，包括收益率、风险指标、期权定价等
"""

import math
from typing import List, Optional, Tuple, Dict
from decimal import Decimal
import numpy as np
from scipy import stats
from .mathematical import standard_deviation, variance, correlation
from ..exceptions import InsufficientDataError, InvalidParameterError


def calculate_returns(prices: List[float], method: str = "simple") -> List[float]:
    """
    计算收益率
    
    Args:
        prices: 价格序列
        method: 计算方法 ("simple" 或 "log")
    
    Returns:
        收益率序列
    
    Raises:
        InsufficientDataError: 价格数据不足
        InvalidParameterError: 方法参数无效
    """
    if len(prices) < 2:
        raise InsufficientDataError("收益率计算", 2, len(prices))
    
    if method not in ["simple", "log"]:
        raise InvalidParameterError("收益率计算", "method", method, "方法必须是'simple'或'log'")
    
    returns = []
    for i in range(1, len(prices)):
        if prices[i-1] <= 0:
            returns.append(float('nan'))
            continue
            
        if method == "simple":
            ret = (prices[i] - prices[i-1]) / prices[i-1]
        else:  # log returns
            if prices[i] <= 0:
                returns.append(float('nan'))
                continue
            ret = math.log(prices[i] / prices[i-1])
        
        returns.append(ret)
    
    return returns


def calculate_log_returns(prices: List[float]) -> List[float]:
    """
    计算对数收益率
    
    Args:
        prices: 价格序列
    
    Returns:
        对数收益率序列
    """
    return calculate_returns(prices, method="log")


def calculate_volatility(returns: List[float], 
                        annualize: bool = True,
                        trading_periods: int = 252) -> float:
    """
    计算波动率
    
    Args:
        returns: 收益率序列
        annualize: 是否年化
        trading_periods: 年交易周期数
    
    Returns:
        波动率
    
    Raises:
        InsufficientDataError: 收益率数据不足
    """
    if len(returns) < 2:
        raise InsufficientDataError("波动率计算", 2, len(returns))
    
    # 过滤NaN值
    valid_returns = [r for r in returns if not math.isnan(r)]
    
    if len(valid_returns) < 2:
        raise InsufficientDataError("波动率计算", 2, len(valid_returns))
    
    vol = standard_deviation(valid_returns, ddof=1)
    
    if annualize:
        vol *= math.sqrt(trading_periods)
    
    return vol


def calculate_sharpe_ratio(returns: List[float], 
                          risk_free_rate: float = 0.0,
                          annualize: bool = True,
                          trading_periods: int = 252) -> float:
    """
    计算夏普比率
    
    Args:
        returns: 收益率序列
        risk_free_rate: 无风险利率
        annualize: 是否年化
        trading_periods: 年交易周期数
    
    Returns:
        夏普比率
    
    Raises:
        InsufficientDataError: 收益率数据不足
    """
    if len(returns) < 2:
        raise InsufficientDataError("夏普比率计算", 2, len(returns))
    
    # 过滤NaN值
    valid_returns = [r for r in returns if not math.isnan(r)]
    
    if len(valid_returns) < 2:
        raise InsufficientDataError("夏普比率计算", 2, len(valid_returns))
    
    mean_return = sum(valid_returns) / len(valid_returns)
    vol = standard_deviation(valid_returns, ddof=1)
    
    if vol == 0:
        return float('inf') if mean_return > risk_free_rate else 0.0
    
    if annualize:
        mean_return *= trading_periods
        risk_free_rate *= trading_periods
        vol *= math.sqrt(trading_periods)
    
    return (mean_return - risk_free_rate) / vol


def calculate_max_drawdown(prices: List[float]) -> Tuple[float, int, int]:
    """
    计算最大回撤
    
    Args:
        prices: 价格序列
    
    Returns:
        (最大回撤, 开始索引, 结束索引)
    
    Raises:
        InsufficientDataError: 价格数据不足
    """
    if len(prices) < 2:
        raise InsufficientDataError("最大回撤计算", 2, len(prices))
    
    max_drawdown = 0.0
    peak = prices[0]
    peak_index = 0
    start_index = 0
    end_index = 0
    
    for i, price in enumerate(prices):
        if price > peak:
            peak = price
            peak_index = i
        
        drawdown = (peak - price) / peak if peak > 0 else 0
        
        if drawdown > max_drawdown:
            max_drawdown = drawdown
            start_index = peak_index
            end_index = i
    
    return max_drawdown, start_index, end_index


def calculate_var(returns: List[float], 
                 confidence_level: float = 0.05,
                 method: str = "historical") -> float:
    """
    计算风险价值 (VaR)
    
    Args:
        returns: 收益率序列
        confidence_level: 置信水平 (如0.05表示95% VaR)
        method: 计算方法 ("historical", "parametric", "monte_carlo")
    
    Returns:
        VaR值 (负数表示损失)
    
    Raises:
        InsufficientDataError: 收益率数据不足
        InvalidParameterError: 参数无效
    """
    if len(returns) < 10:
        raise InsufficientDataError("VaR计算", 10, len(returns))
    
    if not (0 < confidence_level < 1):
        raise InvalidParameterError("VaR计算", "confidence_level", confidence_level, 
                                   "置信水平必须在(0,1)范围内")
    
    # 过滤NaN值
    valid_returns = [r for r in returns if not math.isnan(r)]
    
    if len(valid_returns) < 10:
        raise InsufficientDataError("VaR计算", 10, len(valid_returns))
    
    if method == "historical":
        # 历史模拟法
        sorted_returns = sorted(valid_returns)
        index = int(confidence_level * len(sorted_returns))
        return sorted_returns[index]
    
    elif method == "parametric":
        # 参数法 (假设正态分布)
        mean_return = sum(valid_returns) / len(valid_returns)
        std_return = standard_deviation(valid_returns, ddof=1)
        z_score = stats.norm.ppf(confidence_level)
        return mean_return + z_score * std_return
    
    else:
        raise InvalidParameterError("VaR计算", "method", method, 
                                   "方法必须是'historical'或'parametric'")


def calculate_cvar(returns: List[float], 
                  confidence_level: float = 0.05) -> float:
    """
    计算条件风险价值 (CVaR/Expected Shortfall)
    
    Args:
        returns: 收益率序列
        confidence_level: 置信水平
    
    Returns:
        CVaR值
    
    Raises:
        InsufficientDataError: 收益率数据不足
    """
    if len(returns) < 10:
        raise InsufficientDataError("CVaR计算", 10, len(returns))
    
    # 过滤NaN值
    valid_returns = [r for r in returns if not math.isnan(r)]
    
    if len(valid_returns) < 10:
        raise InsufficientDataError("CVaR计算", 10, len(valid_returns))
    
    var = calculate_var(valid_returns, confidence_level, "historical")
    
    # 计算超过VaR的损失的平均值
    tail_losses = [r for r in valid_returns if r <= var]
    
    if not tail_losses:
        return var
    
    return sum(tail_losses) / len(tail_losses)


def calculate_beta(asset_returns: List[float], 
                  market_returns: List[float]) -> float:
    """
    计算贝塔系数
    
    Args:
        asset_returns: 资产收益率序列
        market_returns: 市场收益率序列
    
    Returns:
        贝塔系数
    
    Raises:
        InvalidParameterError: 数据长度不匹配
        InsufficientDataError: 数据不足
    """
    if len(asset_returns) != len(market_returns):
        raise InvalidParameterError("贝塔计算", "market_returns", len(market_returns),
                                   f"市场收益率长度必须等于资产收益率长度({len(asset_returns)})")
    
    if len(asset_returns) < 10:
        raise InsufficientDataError("贝塔计算", 10, len(asset_returns))
    
    # 过滤NaN值
    valid_pairs = [(a, m) for a, m in zip(asset_returns, market_returns) 
                   if not (math.isnan(a) or math.isnan(m))]
    
    if len(valid_pairs) < 10:
        raise InsufficientDataError("贝塔计算", 10, len(valid_pairs))
    
    asset_clean = [pair[0] for pair in valid_pairs]
    market_clean = [pair[1] for pair in valid_pairs]
    
    market_variance = variance(market_clean, ddof=1)
    
    if market_variance == 0:
        return 0.0
    
    covariance_val = sum((a - sum(asset_clean)/len(asset_clean)) * 
                        (m - sum(market_clean)/len(market_clean)) 
                        for a, m in zip(asset_clean, market_clean)) / (len(valid_pairs) - 1)
    
    return covariance_val / market_variance


def calculate_alpha(asset_returns: List[float], 
                   market_returns: List[float],
                   risk_free_rate: float = 0.0) -> float:
    """
    计算阿尔法系数
    
    Args:
        asset_returns: 资产收益率序列
        market_returns: 市场收益率序列
        risk_free_rate: 无风险利率
    
    Returns:
        阿尔法系数
    """
    beta = calculate_beta(asset_returns, market_returns)
    
    # 过滤NaN值
    valid_pairs = [(a, m) for a, m in zip(asset_returns, market_returns) 
                   if not (math.isnan(a) or math.isnan(m))]
    
    asset_clean = [pair[0] for pair in valid_pairs]
    market_clean = [pair[1] for pair in valid_pairs]
    
    asset_mean = sum(asset_clean) / len(asset_clean)
    market_mean = sum(market_clean) / len(market_clean)
    
    return asset_mean - risk_free_rate - beta * (market_mean - risk_free_rate)


def black_scholes_option_price(spot_price: float,
                              strike_price: float,
                              time_to_expiry: float,
                              risk_free_rate: float,
                              volatility: float,
                              option_type: str = "call") -> float:
    """
    Black-Scholes期权定价
    
    Args:
        spot_price: 标的价格
        strike_price: 行权价格
        time_to_expiry: 到期时间 (年)
        risk_free_rate: 无风险利率
        volatility: 波动率
        option_type: 期权类型 ("call" 或 "put")
    
    Returns:
        期权价格
    
    Raises:
        InvalidParameterError: 参数无效
    """
    if spot_price <= 0:
        raise InvalidParameterError("BS定价", "spot_price", spot_price, "标的价格必须大于0")
    
    if strike_price <= 0:
        raise InvalidParameterError("BS定价", "strike_price", strike_price, "行权价格必须大于0")
    
    if time_to_expiry <= 0:
        raise InvalidParameterError("BS定价", "time_to_expiry", time_to_expiry, "到期时间必须大于0")
    
    if volatility < 0:
        raise InvalidParameterError("BS定价", "volatility", volatility, "波动率不能为负")
    
    if option_type not in ["call", "put"]:
        raise InvalidParameterError("BS定价", "option_type", option_type, "期权类型必须是'call'或'put'")
    
    # 计算d1和d2
    d1 = (math.log(spot_price / strike_price) + 
          (risk_free_rate + 0.5 * volatility ** 2) * time_to_expiry) / (volatility * math.sqrt(time_to_expiry))
    
    d2 = d1 - volatility * math.sqrt(time_to_expiry)
    
    # 标准正态分布累积分布函数
    N_d1 = stats.norm.cdf(d1)
    N_d2 = stats.norm.cdf(d2)
    N_minus_d1 = stats.norm.cdf(-d1)
    N_minus_d2 = stats.norm.cdf(-d2)
    
    if option_type == "call":
        price = spot_price * N_d1 - strike_price * math.exp(-risk_free_rate * time_to_expiry) * N_d2
    else:  # put
        price = strike_price * math.exp(-risk_free_rate * time_to_expiry) * N_minus_d2 - spot_price * N_minus_d1
    
    return price


def calculate_sortino_ratio(returns: List[float],
                           target_return: float = 0.0,
                           annualize: bool = True,
                           trading_periods: int = 252) -> float:
    """
    计算索提诺比率
    
    Args:
        returns: 收益率序列
        target_return: 目标收益率
        annualize: 是否年化
        trading_periods: 年交易周期数
    
    Returns:
        索提诺比率
    """
    if len(returns) < 2:
        raise InsufficientDataError("索提诺比率计算", 2, len(returns))
    
    # 过滤NaN值
    valid_returns = [r for r in returns if not math.isnan(r)]
    
    if len(valid_returns) < 2:
        raise InsufficientDataError("索提诺比率计算", 2, len(valid_returns))
    
    mean_return = sum(valid_returns) / len(valid_returns)
    
    # 计算下行偏差
    downside_returns = [min(0, r - target_return) for r in valid_returns]
    downside_variance = sum(r ** 2 for r in downside_returns) / len(downside_returns)
    downside_deviation = math.sqrt(downside_variance)
    
    if downside_deviation == 0:
        return float('inf') if mean_return > target_return else 0.0
    
    if annualize:
        mean_return *= trading_periods
        target_return *= trading_periods
        downside_deviation *= math.sqrt(trading_periods)
    
    return (mean_return - target_return) / downside_deviation


def calculate_calmar_ratio(returns: List[float],
                          prices: List[float],
                          annualize: bool = True,
                          trading_periods: int = 252) -> float:
    """
    计算卡玛比率
    
    Args:
        returns: 收益率序列
        prices: 价格序列
        annualize: 是否年化
        trading_periods: 年交易周期数
    
    Returns:
        卡玛比率
    """
    if len(returns) < 2:
        raise InsufficientDataError("卡玛比率计算", 2, len(returns))
    
    # 过滤NaN值
    valid_returns = [r for r in returns if not math.isnan(r)]
    
    if len(valid_returns) < 2:
        raise InsufficientDataError("卡玛比率计算", 2, len(valid_returns))
    
    mean_return = sum(valid_returns) / len(valid_returns)
    max_drawdown, _, _ = calculate_max_drawdown(prices)
    
    if max_drawdown == 0:
        return float('inf') if mean_return > 0 else 0.0
    
    if annualize:
        mean_return *= trading_periods
    
    return mean_return / max_drawdown


def calculate_information_ratio(portfolio_returns: List[float],
                               benchmark_returns: List[float]) -> float:
    """
    计算信息比率
    
    Args:
        portfolio_returns: 组合收益率序列
        benchmark_returns: 基准收益率序列
    
    Returns:
        信息比率
    """
    if len(portfolio_returns) != len(benchmark_returns):
        raise InvalidParameterError("信息比率计算", "benchmark_returns", len(benchmark_returns),
                                   f"基准收益率长度必须等于组合收益率长度({len(portfolio_returns)})")
    
    # 计算超额收益
    excess_returns = [p - b for p, b in zip(portfolio_returns, benchmark_returns)
                     if not (math.isnan(p) or math.isnan(b))]
    
    if len(excess_returns) < 2:
        raise InsufficientDataError("信息比率计算", 2, len(excess_returns))
    
    mean_excess = sum(excess_returns) / len(excess_returns)
    tracking_error = standard_deviation(excess_returns, ddof=1)
    
    if tracking_error == 0:
        return float('inf') if mean_excess > 0 else 0.0
    
    return mean_excess / tracking_error
