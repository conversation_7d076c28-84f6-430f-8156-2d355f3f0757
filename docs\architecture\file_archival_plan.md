# MythQuant 文件归档详细计划

## 🎯 **归档目标**

基于项目架构重构，将不再使用的源码、实验代码、测试文件、备份文件等进行系统化归档，确保项目根目录和src目录的整洁性。

## 📋 **归档分类体系**

### **archive/legacy/ - 遗留代码归档**

#### **old_versions/ - 旧版本代码**
```
archive/legacy/old_versions/
├── func.py                          # 旧版工具函数
├── func_Util.py                     # 旧版工具函数
├── main_v20230219_optimized_backup.py # 主程序备份版本
├── main_v20230219_optimized.py.backup_20250722_203950 # 时间戳备份
├── main_v20230219_optimized.py.backup_stable # 稳定版备份
├── user_config.py.backup           # 配置文件备份
└── gbbq_enhanced_integration.py     # GBBQ增强集成（旧版）
```

#### **deprecated/ - 废弃代码**
```
archive/legacy/deprecated/
├── check_last_record.py             # 废弃的记录检查
├── fix_print_calls.py               # 废弃的修复脚本
├── fix_stock_code_corruption.py     # 废弃的修复脚本
├── quick_test_fix.py                # 废弃的快速测试
├── simple_output_test.py            # 废弃的输出测试
├── simple_stdout_test.py            # 废弃的stdout测试
├── find_test_files.py               # 废弃的文件查找
└── gbbq_cache_solution.py           # 废弃的GBBQ缓存方案
```

#### **experiments/ - 实验性代码**
```
archive/legacy/experiments/
├── data_processing_config.yaml      # 实验性配置
├── cache_performance_report_20250721_222208.json # 性能实验报告
├── test_environment_migration_report_20250730_002852.json # 迁移实验报告
└── archived_files/                  # 已有的实验代码归档
    ├── analysis_tools/              # 分析工具实验
    ├── enhancement_attempts/        # 增强尝试实验
    ├── gbbq_experiments/           # GBBQ实验
    ├── precision_experiments/       # 精度实验
    ├── pytdx_experiments/          # PyTDX实验
    └── test_scripts/               # 测试脚本实验
```

### **archive/backup/ - 备份归档**

#### **config_backup/ - 配置备份**
```
archive/backup/config_backup/
├── user_config_20250802.py         # 按日期备份的配置
├── tdx_servers_20250802.json       # TDX服务器配置备份
└── environment_20250802.yml        # 环境配置备份
```

#### **data_backup/ - 数据备份**
```
archive/backup/data_backup/
├── cache/                          # 缓存数据备份
│   ├── server_blacklist.json
│   ├── server_status_cache.json
│   └── gbbq_cache_backup/
├── signal/                         # 信号数据备份
│   ├── pytdx_xdxr_000617_20250726_112733.txt
│   ├── pytdx_xdxr_000617_20250726_182055.txt
│   └── pytdx_xdxr_000617_20250726_182117.txt
└── reports/                        # 报告数据备份
    └── data_processing_report_000617_20250726_163821.json
```

#### **code_backup/ - 代码备份**
```
archive/backup/code_backup/
├── main1v202301232206.rar          # 压缩包备份
├── main1v202301250044.rar          # 压缩包备份
├── stock-analysis.zip              # 分析代码备份
└── img.png                         # 相关图片资源
```

### **archive/tests/ - 测试代码归档**

#### **legacy_tests/ - 遗留测试代码**
```
archive/tests/legacy_tests/
├── tests/                          # 原tests目录内容
│   ├── test_api_interfaces.py
│   ├── test_config.py
│   ├── test_gbbq_optimization_simple.py
│   ├── test_get_specific_minute_data_fix.py
│   ├── test_get_specific_minute_data_uncompounded.py
│   ├── test_output_duplication_fix.py
│   ├── test_price_comparison_0704.py
│   ├── test_quality_checker.py
│   ├── test_structured_output_demo.py
│   ├── test_trading_days_20250704.py
│   ├── test_unified_interfaces.py
│   └── test_upper_limit_fix.py
└── test_environments/              # 原测试环境（保留部分有价值的）
    ├── minute_data_tests/          # 分钟数据测试（保留）
    ├── integration_tests/          # 集成测试（保留）
    └── shared/                     # 共享测试工具（保留）
```

### **archive/tools/ - 工具代码归档**

#### **legacy_tools/ - 遗留工具**
```
archive/tools/legacy_tools/
├── tools/data_integrity_auditor.py # 数据完整性审计工具
├── dependency_check.py             # 依赖检查工具
├── install_dependencies.py         # 依赖安装工具
└── scripts/                        # 脚本工具
    ├── check_test_environment_compliance.py
    ├── create_unified_test_environment.py
    ├── download_internet_data.py
    ├── install_data_sources.py
    ├── integrate_test_environments.py
    ├── manage_blacklist.py
    ├── manage_test_environment.py
    ├── rebuild_test_environment.py
    └── rollback_to_original.py
```

## 🔄 **归档执行计划**

### **第一步：创建归档目录结构**
```bash
mkdir -p archive/{legacy/{old_versions,deprecated,experiments},backup/{config_backup,data_backup,code_backup},tests/legacy_tests,tools/legacy_tools}
```

### **第二步：执行文件归档**

#### **2.1 遗留代码归档**
```bash
# 旧版本代码
mv func.py archive/legacy/old_versions/
mv func_Util.py archive/legacy/old_versions/
mv main_v20230219_optimized_backup.py archive/legacy/old_versions/
mv main_v20230219_optimized.py.backup_* archive/legacy/old_versions/
mv user_config.py.backup archive/legacy/old_versions/
mv gbbq_enhanced_integration.py archive/legacy/old_versions/

# 废弃代码
mv check_last_record.py archive/legacy/deprecated/
mv fix_print_calls.py archive/legacy/deprecated/
mv fix_stock_code_corruption.py archive/legacy/deprecated/
mv quick_test_fix.py archive/legacy/deprecated/
mv simple_output_test.py archive/legacy/deprecated/
mv simple_stdout_test.py archive/legacy/deprecated/
mv find_test_files.py archive/legacy/deprecated/
mv gbbq_cache_solution.py archive/legacy/deprecated/

# 实验性代码
mv data_processing_config.yaml archive/legacy/experiments/
mv cache_performance_report_*.json archive/legacy/experiments/
mv test_environment_migration_report_*.json archive/legacy/experiments/
# archived_files目录已存在，保持现状
```

#### **2.2 备份文件归档**
```bash
# 配置备份
cp user_config.py archive/backup/config_backup/user_config_$(date +%Y%m%d).py
cp tdx_servers.json archive/backup/config_backup/tdx_servers_$(date +%Y%m%d).json
cp environment.yml archive/backup/config_backup/environment_$(date +%Y%m%d).yml

# 数据备份
mv cache/server_blacklist.json archive/backup/data_backup/cache/
mv cache/server_status_cache.json archive/backup/data_backup/cache/
mv signal/ archive/backup/data_backup/
mv reports/ archive/backup/data_backup/

# 代码备份
mv *.rar archive/backup/code_backup/
mv *.zip archive/backup/code_backup/
mv img.png archive/backup/code_backup/
```

#### **2.3 测试代码归档**
```bash
# 遗留测试代码（保留有价值的测试环境）
cp -r tests/ archive/tests/legacy_tests/
# 保留test_environments中有价值的部分，其余归档
```

#### **2.4 工具代码归档**
```bash
# 遗留工具
mv dependency_check.py archive/tools/legacy_tools/
mv install_dependencies.py archive/tools/legacy_tools/
cp -r tools/ archive/tools/legacy_tools/
cp -r scripts/ archive/tools/legacy_tools/
```

### **第三步：清理和验证**

#### **3.1 清理__pycache__目录**
```bash
find . -name "__pycache__" -type d -exec rm -rf {} +
find . -name "*.pyc" -delete
find . -name "*.pyo" -delete
```

#### **3.2 清理日志文件（可选）**
```bash
# 保留最近30天的日志，其余归档
find logs/ -name "*.log" -mtime +30 -exec mv {} archive/backup/data_backup/logs/ \;
```

#### **3.3 验证归档完整性**
- 检查所有归档文件是否正确移动
- 验证原位置文件是否已清理
- 确认核心功能文件未被误归档

## 📋 **保留在根目录的核心文件**

### **必须保留的文件**
```
MythQuant/
├── main.py                          # 主程序入口（新架构）
├── main_v20230219_optimized.py      # 主程序（当前版本，保留作为参考）
├── user_config.py                   # 用户配置（保留兼容性）
├── minute_path_helper.py            # 路径辅助（待迁移到src/mythquant/io/）
├── readTDX_cw.py                    # TDX读取（待迁移到src/mythquant/data/sources/）
├── read_dat_file.py                 # DAT文件读取（待迁移到src/mythquant/io/readers/）
├── func_Tdx.py                      # TDX函数（待迁移到src/mythquant/data/sources/）
├── func_Tdx1.py                     # TDX函数扩展（待迁移到src/mythquant/data/sources/）
├── requirements.txt                 # 依赖文件
├── environment.yml                  # Conda环境文件
├── README.md                        # 项目说明
├── setup.py                         # 安装配置（新增）
├── pyproject.toml                   # 项目配置（新增）
└── MANIFEST.in                      # 打包配置（新增）
```

### **保留的目录结构**
```
MythQuant/
├── src/mythquant/                   # 新的源码目录
├── tests/                           # 新的测试目录
├── docs/                            # 文档目录（重新组织）
├── archive/                         # 归档目录（新增）
├── tools/                           # 工具目录（重新组织）
├── environments/                    # 环境配置（新增）
├── cache/                           # 缓存目录（保留核心缓存）
├── logs/                            # 日志目录（保留最近日志）
├── algorithms/                      # 算法目录（待迁移到src/mythquant/algorithms/）
├── core/                            # 核心目录（待迁移到src/mythquant/core/）
├── file_io/                         # 文件IO（待迁移到src/mythquant/io/）
├── ui/                              # UI目录（待迁移到src/mythquant/ui/）
├── utils/                           # 工具目录（待迁移到src/mythquant/utils/）
├── custom_datacfg/                  # 自定义数据配置（保留）
├── lib/                             # 库文件（保留）
└── prompt_templates/                # 提示模板（保留）
```

## 🎯 **归档后的预期效果**

### **项目根目录简化**
- 根目录Python文件从当前的20+个减少到10个以内
- 清除所有实验性、废弃的代码文件
- 保留核心功能文件和配置文件

### **归档体系完善**
- 所有历史代码都有明确的归档位置
- 按功能和时间进行分类归档
- 提供完整的归档索引和说明

### **项目结构清晰**
- src目录包含所有新架构的源码
- tests目录包含完整的测试体系
- docs目录包含完整的文档体系
- archive目录包含所有历史归档

## 📝 **归档日志模板**

```markdown
# 归档执行日志

## 执行时间
- 开始时间: YYYY-MM-DD HH:MM:SS
- 结束时间: YYYY-MM-DD HH:MM:SS
- 执行人员: [执行人员名称]

## 归档统计
- 归档文件总数: [数量]
- 遗留代码: [数量]
- 备份文件: [数量]
- 测试代码: [数量]
- 工具代码: [数量]

## 归档详情
### 成功归档
- [文件名] → [归档位置]
- ...

### 跳过文件
- [文件名] - [跳过原因]
- ...

### 异常情况
- [异常描述]
- [处理方案]

## 验证结果
- [ ] 核心功能正常运行
- [ ] 归档文件完整性检查通过
- [ ] 项目结构符合预期
- [ ] 文档更新完成

## 备注
[其他需要说明的情况]
```

这个归档计划将确保项目在重构过程中的历史代码得到妥善保存，同时为新架构的建立提供清洁的基础环境。
