#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的项目结构清理脚本

快速清理和重组MythQuant项目结构
"""

import os
import shutil
from pathlib import Path
from datetime import datetime


def main():
    """主清理函数"""
    print("🧹 MythQuant 项目结构简化清理")
    print("=" * 50)
    
    project_root = Path(__file__).parent
    
    # 核心文件（必须保留在根目录）
    keep_in_root = {
        'config_compatibility.py',
        'data_access_compatibility.py', 
        'algorithm_compatibility.py',
        'io_compatibility.py',
        'user_config.py',
        'main.py',
        'requirements.txt',
        'README.md'
    }
    
    print("💾 创建备份...")
    backup_dir = project_root / "manual_cleanup_backup"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = backup_dir / f"backup_{timestamp}"
    backup_path.mkdir(parents=True, exist_ok=True)
    
    # 备份根目录重要文件
    backup_count = 0
    for file_path in project_root.iterdir():
        if file_path.is_file() and not file_path.name.startswith('.'):
            try:
                dst = backup_path / file_path.name
                shutil.copy2(file_path, dst)
                backup_count += 1
            except Exception as e:
                print(f"   ⚠️ 备份失败 {file_path.name}: {e}")
    
    print(f"   ✅ 已备份 {backup_count} 个文件")
    
    # 创建目标目录
    print("\n📁 创建目标目录...")
    target_dirs = ['legacy', 'tools', 'config', 'data', 'archive', 'assets']
    for dir_name in target_dirs:
        (project_root / dir_name).mkdir(exist_ok=True)
        print(f"   ✅ 创建: {dir_name}/")
    
    # 定义移动规则
    move_rules = {
        'legacy': [
            'func.py', 'func_Tdx.py', 'func_Tdx1.py', 'func_Util.py',
            'main_v20230219_optimized.py', 'main_v20230219_optimized_backup.py',
            'readTDX_cw.py', 'read_dat_file.py', 'gbbq_*.py',
            'minute_path_helper.py', 'check_last_record.py'
        ],
        'tools': [
            'create_*.py', 'install_*.py', 'fix_*.py', 'find_*.py',
            'migrate_*.py', 'prepare_*.py', 'verify_*.py', 'analyze_*.py',
            'reorganize_*.py', 'generate_*.py', 'dependency_check.py',
            'direct_import_test.py', 'clean_*.py', 'simple_*.py'
        ],
        'config': [
            'environment.yml', 'data_processing_config.yaml',
            'pyproject.toml', 'setup.py', 'tox.ini', 'MANIFEST.in'
        ],
        'data': [
            'tdx_servers.json', 'project_structure_analysis.json',
            'cache_*.json', 'cleanup_report.json', 'reorganization_report.json'
        ],
        'archive': [
            '*.rar', '*.zip', '*.docx', 'stock-analysis.zip'
        ],
        'assets': [
            'img.png'
        ]
    }
    
    # 执行移动操作
    print("\n🚚 移动文件...")
    moved_count = 0
    
    for target_dir, patterns in move_rules.items():
        print(f"\n   📂 处理 {target_dir}/ 目录:")
        
        for pattern in patterns:
            for file_path in project_root.glob(pattern):
                if file_path.is_file() and file_path.parent == project_root:
                    # 检查是否是核心文件
                    if file_path.name in keep_in_root:
                        print(f"      ⏭️ 跳过核心文件: {file_path.name}")
                        continue
                    
                    try:
                        target_path = project_root / target_dir / file_path.name
                        
                        # 避免覆盖
                        if target_path.exists():
                            target_path = project_root / target_dir / f"{file_path.stem}_moved{file_path.suffix}"
                        
                        shutil.move(str(file_path), str(target_path))
                        print(f"      ✅ 移动: {file_path.name}")
                        moved_count += 1
                        
                    except Exception as e:
                        print(f"      ❌ 移动失败 {file_path.name}: {e}")
    
    # 清理临时文件
    print(f"\n🗑️ 清理临时文件...")
    cleaned_count = 0
    
    # 删除 __pycache__ 目录
    for pycache_dir in project_root.rglob('__pycache__'):
        try:
            shutil.rmtree(pycache_dir)
            print(f"   🗑️ 删除: {pycache_dir.relative_to(project_root)}")
            cleaned_count += 1
        except Exception as e:
            print(f"   ❌ 删除失败 {pycache_dir}: {e}")
    
    # 删除 .pyc 文件
    for pyc_file in project_root.rglob('*.pyc'):
        try:
            pyc_file.unlink()
            print(f"   🗑️ 删除: {pyc_file.relative_to(project_root)}")
            cleaned_count += 1
        except Exception as e:
            print(f"   ❌ 删除失败 {pyc_file}: {e}")
    
    # 验证结果
    print(f"\n✅ 验证清理结果...")
    root_files = [f.name for f in project_root.iterdir() if f.is_file()]
    print(f"   📊 清理后根目录文件数: {len(root_files)}")
    
    # 检查核心文件
    missing_core = []
    for core_file in keep_in_root:
        if core_file not in root_files:
            missing_core.append(core_file)
    
    if missing_core:
        print(f"   ⚠️ 缺少核心文件: {missing_core}")
    else:
        print("   ✅ 所有核心文件都已保留")
    
    # 显示新目录结构
    print("\n📊 清理后项目结构:")
    for item in sorted(project_root.iterdir()):
        if item.is_dir() and not item.name.startswith('.'):
            file_count = len(list(item.iterdir()))
            print(f"   📁 {item.name}/: {file_count} 个文件")
    
    if root_files:
        print(f"   📄 根目录: {len(root_files)} 个文件")
        for file_name in sorted(root_files):
            icon = "⭐" if file_name in keep_in_root else "📄"
            print(f"      {icon} {file_name}")
    
    print(f"\n📊 清理统计:")
    print(f"   移动文件: {moved_count} 个")
    print(f"   清理临时文件: {cleaned_count} 个")
    print(f"   备份文件: {backup_count} 个")
    
    print(f"\n✅ 项目结构清理完成！")
    print(f"💾 备份位置: {backup_path}")
    
    return True


if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n🎉 清理成功完成！")
            print("\n📋 下一步建议:")
            print("1. 验证兼容性模块: python -c \"import config_compatibility; print('OK')\"")
            print("2. 运行测试: python tests/run_tests.py")
            print("3. 检查功能: python final_validation_test.py")
        else:
            print("\n❌ 清理失败！")
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
        print("请检查错误信息并手动处理")
    
    input("\n按回车键退出...")
