#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDD架构 - 共享日志模块

提供统一的日志记录功能，适配新的DDD架构
"""

import logging
import os
import sys
from datetime import datetime
from typing import Optional, Dict, Any
from pathlib import Path


class SmartLogger:
    """智能日志器 - DDD架构版本"""
    
    def __init__(self, name: str = "mythquant", log_level: str = "INFO"):
        self.name = name
        self.logger = logging.getLogger(name)
        self.logger.setLevel(getattr(logging, log_level.upper()))
        
        # 避免重复添加处理器
        if not self.logger.handlers:
            self._setup_handlers()
    
    def _setup_handlers(self):
        """设置日志处理器"""
        # 控制台处理器
        console_handler = logging.StreamHandler(sys.stdout)
        console_handler.setLevel(logging.INFO)
        
        # 文件处理器
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        log_file = log_dir / f"mythquant_{timestamp}.log"
        
        file_handler = logging.FileHandler(log_file, encoding='utf-8')
        file_handler.setLevel(logging.DEBUG)
        
        # 格式化器
        formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        )
        
        console_handler.setFormatter(formatter)
        file_handler.setFormatter(formatter)
        
        self.logger.addHandler(console_handler)
        self.logger.addHandler(file_handler)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(message, **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(message, **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(message, **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(message, **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(message, **kwargs)


# 全局日志器实例
_global_logger: Optional[SmartLogger] = None


def get_smart_logger(name: str = "mythquant", log_level: str = "INFO") -> SmartLogger:
    """获取智能日志器实例"""
    global _global_logger
    
    if _global_logger is None:
        _global_logger = SmartLogger(name, log_level)
    
    return _global_logger


def log_info(message: str, **kwargs):
    """快捷信息日志"""
    logger = get_smart_logger()
    logger.info(message, **kwargs)


def log_warning(message: str, **kwargs):
    """快捷警告日志"""
    logger = get_smart_logger()
    logger.warning(message, **kwargs)


def log_error(message: str, **kwargs):
    """快捷错误日志"""
    logger = get_smart_logger()
    logger.error(message, **kwargs)


def log_debug(message: str, **kwargs):
    """快捷调试日志"""
    logger = get_smart_logger()
    logger.debug(message, **kwargs)


# 兼容性函数 - 保持与旧代码的兼容性
class LoggingService:
    """日志服务 - 兼容性包装"""
    
    def __init__(self):
        self.logger = get_smart_logger()
    
    def log_info(self, message: str, **kwargs):
        self.logger.info(message, **kwargs)
    
    def log_warning(self, message: str, **kwargs):
        self.logger.warning(message, **kwargs)
    
    def log_error(self, message: str, **kwargs):
        self.logger.error(message, **kwargs)
    
    def log_debug(self, message: str, **kwargs):
        self.logger.debug(message, **kwargs)


# 全局日志服务实例
_logging_service: Optional[LoggingService] = None


def get_logging_service() -> LoggingService:
    """获取日志服务实例"""
    global _logging_service
    
    if _logging_service is None:
        _logging_service = LoggingService()
    
    return _logging_service
