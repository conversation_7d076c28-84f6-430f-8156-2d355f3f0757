# MythQuant项目FAQ知识库

## 使用说明
- **时间格式**: YYYY-MM-DD
- **标签格式**: [领域] [模块] [技术栈]
- **检索方式**: 使用Ctrl+F搜索关键词、时间、标签

---

## FAQ条目

### Q1: 正则表达式无法匹配带时间戳的文件名问题
**时间**: 2025-07-26  
**标签**: [文件处理] [智能文件选择器] [正则表达式] [时间戳]

**问题描述**:
用户发现正则表达式无法匹配带时间戳后缀的文件名，如`1min_0_000617_20250401-20250726_来源互联网（202507261741）.txt`，导致智能文件选择器无法正确识别文件。

**解决方案**:
1. **更新正则表达式**：在`utils/smart_file_selector.py`中修改正则表达式支持时间戳后缀
   ```python
   # 原正则
   self.filename_pattern = r'(\d+min)_0_(\d{6})_(\d{8})-(\d{8})_来源互联网\.txt'
   # 修复后
   self.filename_pattern = r'(\d+min)_0_(\d{6})_(\d{8})-(\d{8})_来源互联网(?:（\d{12}）)?\.txt'
   ```

2. **同步更新相关文件**：
   - `utils/incremental_download_validator.py`
   - 文件扫描逻辑中的模式匹配

3. **文件重命名增强**：更新后的文件自动添加时间戳后缀

**关键技术点**:
- 使用非捕获组`(?:（\d{12}）)?`支持可选时间戳
- 保持向后兼容，无时间戳文件依然可以匹配
- 时间戳格式：`（YYYYMMDDHHMM）`

---

### Q2: pytdx数据获取上限不足导致历史数据下载失败
**时间**: 2025-07-26  
**标签**: [数据下载] [pytdx] [交易日计算] [数据量优化]

**问题描述**:
用户发现20250301等历史日期无法下载pytdx数据，报错"时间范围内无数据"。经分析发现是10000条数据上限不足以覆盖长期历史数据。

**根本原因分析**:
- pytdx API返回"最新N条数据"而非"指定时间范围数据"
- 20250301到今天需要105个交易日 × 240条 × 1.2缓冲 = 30240条
- 原10000条上限只能覆盖约35个交易日
- 导致获取的数据不包含目标历史日期

**解决方案**:
1. **智能数据量计算**：基于实际交易日动态计算
   ```python
   def _calculate_smart_data_count(self, start_date: str, frequency: str) -> int:
       # 计算从目标日期到今天的交易日数量
       trading_days = self._count_trading_days(start_date, datetime.now())
       # 根据频率计算每日数据条数
       bars_per_day = 240 // int(frequency.replace('min', ''))
       # 计算总需求量（含20%缓冲）
       required_count = int(trading_days * bars_per_day * 1.2)
       # 应用合理上下限
       return max(1000, min(required_count, 50000))
   ```

2. **提升数据获取上限**：从10000条提升到50000条

3. **交易日精确计算**：排除周末，支持约8-9个月历史数据

**效果验证**:
- 20250301: 30240条（完全覆盖105个交易日）
- 20250701: 5472条（覆盖19个交易日）
- 系统可处理任何历史日期的数据下载

---

### Q3: 双入口政策废除和功能迁移
**时间**: 2025-07-26  
**标签**: [架构设计] [代码重构] [模块化] [功能迁移]

**问题背景**:
项目存在main.py和main_v20230219_optimized.py两个入口，维护成本高，功能不一致。

**决策过程**:
1. **功能对比分析**：main.py架构更优（171行 vs 4901行）
2. **功能迁移验证**：成功将pytdx除权除息对比功能迁移到core/stock_processor.py
3. **等价性测试**：确认功能完全等价且性能更好

**最终方案**:
- 统一使用main.py作为唯一入口
- 保留main_v20230219_optimized.py作为参考
- 更新项目规则，明确单一入口政策

**技术收益**:
- 代码量减少96%（4901行→171行）
- 维护成本大幅降低
- 模块化架构更易扩展
- 错误处理和日志系统更完善

---


### Q4: pytdx数据获取上限不足导致历史数据下载失败
**时间**: 2025-07-26  
**标签**: [数据处理] [pytdx下载器] [Python]

**问题描述**:
pytdx数据获取上限不足导致历史数据下载失败

**解决方案**:
通过智能数据量计算和提升上限解决

---

## 检索索引

### 按时间检索
- 2025-07-26: Q1, Q2, Q3

### 按领域检索
- **文件处理**: Q1
- **数据下载**: Q2  
- **架构设计**: Q3

### 按模块检索
- **智能文件选择器**: Q1
- **pytdx下载器**: Q2
- **主程序架构**: Q3

### 按技术栈检索
- **正则表达式**: Q1
- **交易日计算**: Q2
- **模块化重构**: Q3

---

## 待补充问题
- [ ] 前复权算法优化相关问题
- [ ] 缓存系统性能优化问题
- [ ] 配置管理体系化问题
- [ ] 数据质量验证标准问题

---

*最后更新: 2025-07-26*  
*维护者: AI Assistant*
