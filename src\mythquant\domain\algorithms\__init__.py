#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法模块 (Algorithms)

纯函数算法实现，无副作用，高度可测试
按功能分类：数学算法、金融算法、技术指标、统计算法
"""

from .mathematical import *
from .financial import *
from .technical_indicators import *
from .statistical import *
from .optimization import *
from .risk_metrics import *

__all__ = [
    # 数学算法
    'linear_interpolation',
    'exponential_smoothing',
    'moving_average',
    'weighted_average',
    'compound_growth_rate',
    'standard_deviation',
    'variance',
    'correlation',
    'covariance',
    
    # 金融算法
    'calculate_returns',
    'calculate_log_returns',
    'calculate_volatility',
    'calculate_sharpe_ratio',
    'calculate_max_drawdown',
    'calculate_var',
    'calculate_cvar',
    'calculate_beta',
    'calculate_alpha',
    'black_scholes_option_price',
    
    # 技术指标
    'sma',
    'ema',
    'rsi',
    'macd',
    'bollinger_bands',
    'stochastic_oscillator',
    'williams_r',
    'atr',
    'adx',
    'cci',
    
    # 统计算法
    'z_score',
    'percentile',
    'quantile',
    'skewness',
    'kurtosis',
    'jarque_bera_test',
    'adf_test',
    'ljung_box_test',
    
    # 优化算法
    'portfolio_optimization',
    'mean_variance_optimization',
    'risk_parity_optimization',
    'black_litterman_optimization',
    
    # 风险指标
    'value_at_risk',
    'conditional_var',
    'expected_shortfall',
    'maximum_drawdown',
    'calmar_ratio',
    'sortino_ratio'
]
