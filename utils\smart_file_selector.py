#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能文件选择器
解决多个同类型文件存在时的选择策略问题
"""

import os
import re
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple, Any
from dataclasses import dataclass
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from utils.structured_output_formatter import (
    print_step, print_action, print_result, print_info,
    print_warning, print_error, print_data_info
)

try:
    from core.logging_service import logging_service
except ImportError:
    class SimpleLogger:
        def verbose_log(self, level, msg): print(f"[{level.upper()}] {msg}")
        def log_error(self, msg): print(f"[ERROR] {msg}")
    logging_service = SimpleLogger()

# 导入环境配置
try:
    from user_config import CURRENT_ENVIRONMENT, get_environment_config
except ImportError:
    CURRENT_ENVIRONMENT = 'production'
    def get_environment_config(task_name='minute_data_download'):
        return {'base_path': '.', 'input_data_path': '.', 'output_data_path': '.'}


@dataclass
class FileInfo:
    """文件信息数据类"""
    filepath: str
    filename: str
    stock_code: str
    frequency: str
    start_date: str
    end_date: str
    file_size: int
    modify_time: datetime
    coverage_days: int
    freshness_score: float
    coverage_score: float
    total_score: float


class SmartFileSelector:
    """智能文件选择器（对所有股票生效）"""

    def __init__(self, output_dir: str):
        """
        初始化智能文件选择器

        Args:
            output_dir: 输出目录
        """
        self.output_dir = output_dir
        self.smart_logger = logging_service

        # 加载用户配置
        self.config = self._load_user_config()

        # 文件名解析正则（支持时间戳后缀，支持8位和12位时间格式）
        self.filename_pattern = r'(\d+min)_0_(\d{6})_(\d{8,12})-(\d{8,12})_来源互联网(?:（\d{12}）)?\.txt'

        # 选择策略配置
        self.selection_strategies = {
            'latest_first': self._strategy_latest_first,
            'max_coverage': self._strategy_max_coverage,
            'best_match': self._strategy_best_match,
            'smart_comprehensive': self._strategy_smart_comprehensive
        }

        # 从配置获取默认策略
        self.default_strategy = self.config.get('default_strategy', 'smart_comprehensive')

        # 评分权重配置
        weights = self.config.get('scoring_weights', {})
        self.freshness_weight = weights.get('freshness_weight', 0.3)
        self.coverage_weight = weights.get('coverage_weight', 0.4)
        self.match_weight = weights.get('match_weight', 0.3)

        # 高级设置
        advanced = self.config.get('advanced', {})
        self.freshness_max_days = advanced.get('freshness_max_days', 365)
        self.match_bonus_for_full_coverage = advanced.get('match_bonus_for_full_coverage', 0.2)
        self.min_overlap_ratio = advanced.get('min_overlap_ratio', 0.1)

    def _load_user_config(self) -> Dict[str, Any]:
        """加载用户配置"""
        try:
            import user_config

            # 检查是否启用智能文件选择器
            if hasattr(user_config, 'smart_file_selector'):
                config = user_config.smart_file_selector

                if not config.get('enabled', True):
                    self.smart_logger.verbose_log('info', "智能文件选择器已禁用，将使用传统模式")
                    return {'enabled': False}

                self.smart_logger.verbose_log('info', f"✅ 加载智能文件选择器配置: 策略={config.get('default_strategy', 'smart_comprehensive')}")
                return config
            else:
                self.smart_logger.verbose_log('warning', "未找到智能文件选择器配置，使用默认设置")
                return self._get_default_config()

        except ImportError:
            self.smart_logger.verbose_log('warning', "无法加载用户配置，使用默认设置")
            return self._get_default_config()
        except Exception as e:
            self.smart_logger.log_error(f"加载用户配置失败: {e}")
            return self._get_default_config()

    def _get_default_config(self) -> Dict[str, Any]:
        """获取默认配置"""
        return {
            'enabled': True,
            'default_strategy': 'smart_comprehensive',
            'scoring_weights': {
                'freshness_weight': 0.3,
                'coverage_weight': 0.4,
                'match_weight': 0.3
            },
            'conflict_resolution': {
                'show_candidates': True,
                'show_selection_reason': True,
                'log_detailed_analysis': True
            },
            'auto_rename': {
                'enabled': True,
                'show_rename_details': True
            },
            'advanced': {
                'freshness_max_days': 365,
                'match_bonus_for_full_coverage': 0.2,
                'min_overlap_ratio': 0.1
            }
        }

    def find_best_file(self, stock_code: str, data_type: str = "minute",
                      target_start: str = None, target_end: str = None,
                      strategy: str = None) -> Optional[str]:
        """
        查找最佳文件（环境感知版本）

        Args:
            stock_code: 股票代码
            data_type: 数据类型
            target_start: 目标开始日期
            target_end: 目标结束日期
            strategy: 选择策略

        Returns:
            最佳文件路径或None
        """
        try:
            # 环境感知处理
            env_config = get_environment_config('minute_data_download')

            print_info(f"环境检测: {CURRENT_ENVIRONMENT}", level=2)
            print_info(f"搜索路径: {env_config.get('input_data_path', '.')}", level=2)

            # 在测试环境中，优先使用测试文件
            if CURRENT_ENVIRONMENT == 'test':
                test_file_result = self._handle_test_environment(stock_code, data_type, env_config)
                if test_file_result:
                    # 如果返回的是tuple，提取文件路径
                    if isinstance(test_file_result, tuple):
                        return test_file_result[0]
                    return test_file_result

            # 检查是否启用智能文件选择器
            if not self.config.get('enabled', True):
                # 使用传统模式：返回第一个匹配的文件
                return self._fallback_to_traditional_mode(stock_code, data_type)

            # 扫描候选文件
            candidates = self.scan_candidate_files(stock_code, data_type)
            
            if not candidates:
                self.smart_logger.verbose_log('info', f"未找到{stock_code}的{data_type}数据文件")
                return None
            
            if len(candidates) == 1:
                self.smart_logger.verbose_log('info', f"找到唯一文件: {os.path.basename(candidates[0])}")
                return candidates[0]
            
            # 分析所有候选文件
            file_analysis = []
            for filepath in candidates:
                info = self.analyze_file(filepath, target_start, target_end)
                if info:
                    file_analysis.append(info)

            if not file_analysis:
                return None

            # 多文件冲突处理（使用实际分析成功的文件数量）
            self.smart_logger.verbose_log('info', f"🔍 发现{len(file_analysis)}个候选文件，开始智能选择")

            # 根据配置决定是否显示候选文件信息
            if self.config.get('conflict_resolution', {}).get('show_candidates', True):
                self._display_candidates(file_analysis)
            
            # 应用选择策略
            strategy = strategy or self.default_strategy
            best_file = self.apply_selection_strategy(file_analysis, strategy)
            
            if best_file:
                self.smart_logger.verbose_log('info', f"✅ 选择文件: {best_file.filename} (策略: {strategy}, 评分: {best_file.total_score:.2f})")

                # 根据配置决定是否显示选择原因
                if self.config.get('conflict_resolution', {}).get('show_selection_reason', True):
                    self._log_selection_reason(best_file, file_analysis, strategy)

                return best_file.filepath
            
            return None
            
        except Exception as e:
            self.smart_logger.log_error(f"智能文件选择失败: {e}")
            return None

    def _handle_test_environment(self, stock_code: str, data_type: str, env_config: dict) -> Optional[Tuple[str, dict]]:
        """
        处理测试环境的文件选择

        Args:
            stock_code: 股票代码
            data_type: 数据类型
            env_config: 环境配置

        Returns:
            (测试文件路径, 文件信息) 或 None
        """
        try:
            print_info("检测到测试环境，优先使用测试文件", level=2)

            # 获取测试文件配置
            test_files = env_config.get('test_files', {})
            stock_test_config = test_files.get(stock_code, {})

            if not stock_test_config:
                print_warning(f"未找到股票{stock_code}的测试文件配置", level=2)
                return None

            # 构建测试文件路径
            input_data_path = env_config.get('input_data_path', 'test_environments/minute_data_tests/input_data')
            target_file = stock_test_config.get('target_file')

            if not target_file:
                print_warning(f"股票{stock_code}的测试文件配置中缺少target_file", level=2)
                return None

            # 检查当前工作目录，智能构建路径
            current_dir = os.getcwd()
            if 'test_environments/minute_data_tests' in current_dir or 'test_environments\\minute_data_tests' in current_dir:
                # 已经在测试环境目录中，直接使用相对路径
                test_file_path = os.path.join('input_data', target_file)
            else:
                # 不在测试环境目录中，使用完整路径
                test_file_path = os.path.join(input_data_path, target_file)

            # 标准化路径分隔符
            test_file_path = test_file_path.replace('\\', '/')

            print_info(f"构建的测试文件路径: {test_file_path}", level=2)

            # 检查测试文件是否存在
            if os.path.exists(test_file_path):
                print_info(f"找到测试文件: {target_file}", level=2)

                # 构建文件信息
                file_info = {
                    'file_path': test_file_path,
                    'start_date': stock_test_config.get('date_range', {}).get('start_date', '20250320'),
                    'end_date': stock_test_config.get('date_range', {}).get('end_date', '20250704'),
                    'total_days': 107,
                    'file_size': os.path.getsize(test_file_path),
                    'quality_score': 100.0,
                    'selection_reason': 'Test environment forced selection'
                }

                print_result(f"强制选择测试文件: {target_file}", True, level=2)
                return test_file_path
            else:
                print_warning(f"测试文件不存在: {test_file_path}", level=2)
                return None

        except Exception as e:
            print_error(f"测试环境文件处理失败: {e}", level=2)
            return None

    def _fallback_to_traditional_mode(self, stock_code: str, data_type: str = "minute") -> Optional[str]:
        """回退到传统模式：返回第一个匹配的文件"""
        try:
            self.smart_logger.verbose_log('info', f"使用传统模式查找{stock_code}的{data_type}数据文件")

            candidates = self.scan_candidate_files(stock_code, data_type)

            if candidates:
                selected_file = candidates[0]  # 选择第一个匹配的文件
                self.smart_logger.verbose_log('info', f"传统模式选择: {os.path.basename(selected_file)}")
                return selected_file
            else:
                return None

        except Exception as e:
            self.smart_logger.log_error(f"传统模式查找失败: {e}")
            return None
    
    def scan_candidate_files(self, stock_code: str, data_type: str = "minute") -> List[str]:
        """扫描候选文件"""
        try:
            if not os.path.exists(self.output_dir):
                return []

            candidates = []

            # 构建文件名模式（支持时间戳后缀）
            if data_type == "minute":
                pattern = f"(1min|5min|15min|30min|60min)_0_{stock_code}_.*_来源互联网(?:（\\d{{12}}）)?\\.txt"
            else:
                pattern = f"day_0_{stock_code}_.*_来源互联网(?:（\\d{{12}}）)?\\.txt"

            # 扫描目录
            for filename in os.listdir(self.output_dir):
                # 排除备份文件（.bak、.backup等后缀）
                if self._is_backup_file(filename):
                    continue

                if re.match(pattern, filename):
                    filepath = os.path.join(self.output_dir, filename)
                    candidates.append(filepath)

            return candidates
            
        except Exception as e:
            self.smart_logger.log_error(f"扫描候选文件失败: {e}")
            return []

    def _is_backup_file(self, filename: str) -> bool:
        """
        检查是否为备份文件

        Args:
            filename: 文件名

        Returns:
            True if 是备份文件
        """
        # 常见的备份文件后缀
        backup_extensions = [
            '.bak',      # 最常见的备份后缀
            '.backup',   # 完整的backup后缀
            '.old',      # 旧文件后缀
            '.orig',     # 原始文件后缀
            '.save',     # 保存文件后缀
            '.tmp',      # 临时文件后缀
            '.temp',     # 临时文件后缀
            '~',         # Unix风格的备份后缀
        ]

        filename_lower = filename.lower()

        # 检查是否以备份后缀结尾
        for ext in backup_extensions:
            if filename_lower.endswith(ext):
                return True

        # 检查是否包含备份标识（如：filename.txt.bak）
        for ext in backup_extensions:
            if ext in filename_lower and not filename_lower.endswith('.txt'):
                return True

        return False
    
    def analyze_file(self, filepath: str, target_start: str = None, 
                    target_end: str = None) -> Optional[FileInfo]:
        """分析文件信息"""
        try:
            filename = os.path.basename(filepath)
            
            # 解析文件名
            match = re.match(self.filename_pattern, filename)
            if not match:
                return None
            
            frequency_str, stock_code, start_date_raw, end_date_raw = match.groups()

            # 处理时间格式（支持8位YYYYMMDD和12位YYYYMMDDHHMM）
            if len(start_date_raw) == 12:
                # 12位格式：YYYYMMDDHHMM，取前8位作为日期
                start_date = start_date_raw[:8]
                end_date = end_date_raw[:8]
            else:
                # 8位格式：YYYYMMDD
                start_date = start_date_raw
                end_date = end_date_raw

            # 获取文件属性
            stat = os.stat(filepath)
            file_size = stat.st_size
            modify_time = datetime.fromtimestamp(stat.st_mtime)

            # 计算覆盖天数
            start_dt = datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.strptime(end_date, '%Y%m%d')
            coverage_days = (end_dt - start_dt).days + 1
            
            # 计算新鲜度评分（距离现在越近越好）
            now = datetime.now()
            days_since_end = (now - end_dt).days
            freshness_score = max(0, self.freshness_max_days - days_since_end)

            # 计算覆盖评分
            coverage_score = coverage_days

            # 计算匹配度评分（如果有目标范围）
            match_score = 0
            if target_start and target_end:
                match_score = self._calculate_match_score(
                    start_date, end_date, target_start, target_end
                )

            # 使用配置的权重计算总评分
            total_score = (
                freshness_score * self.freshness_weight +
                coverage_score * self.coverage_weight +
                match_score * self.match_weight
            )
            
            return FileInfo(
                filepath=filepath,
                filename=filename,
                stock_code=stock_code,
                frequency=frequency_str,
                start_date=start_date,
                end_date=end_date,
                file_size=file_size,
                modify_time=modify_time,
                coverage_days=coverage_days,
                freshness_score=freshness_score,
                coverage_score=coverage_score,
                total_score=total_score
            )
            
        except Exception as e:
            self.smart_logger.log_error(f"分析文件失败 {filepath}: {e}")
            return None
    
    def _calculate_match_score(self, file_start: str, file_end: str, 
                              target_start: str, target_end: str) -> float:
        """计算与目标范围的匹配度评分"""
        try:
            file_start_dt = datetime.strptime(file_start, '%Y%m%d')
            file_end_dt = datetime.strptime(file_end, '%Y%m%d')
            target_start_dt = datetime.strptime(target_start, '%Y%m%d')
            target_end_dt = datetime.strptime(target_end, '%Y%m%d')
            
            # 计算重叠区间
            overlap_start = max(file_start_dt, target_start_dt)
            overlap_end = min(file_end_dt, target_end_dt)
            
            if overlap_start <= overlap_end:
                # 有重叠
                overlap_days = (overlap_end - overlap_start).days + 1
                target_days = (target_end_dt - target_start_dt).days + 1
                
                # 重叠比例作为匹配度
                match_ratio = overlap_days / target_days
                
                # 完全覆盖的额外奖励
                if file_start_dt <= target_start_dt and file_end_dt >= target_end_dt:
                    match_ratio += self.match_bonus_for_full_coverage  # 完全覆盖奖励
                
                return min(match_ratio * 100, 100)  # 最高100分
            else:
                # 无重叠
                return 0
                
        except Exception:
            return 0
    
    def apply_selection_strategy(self, file_analysis: List[FileInfo], 
                               strategy: str) -> Optional[FileInfo]:
        """应用选择策略"""
        if not file_analysis:
            return None
        
        strategy_func = self.selection_strategies.get(strategy, self._strategy_smart_comprehensive)
        return strategy_func(file_analysis)
    
    def _strategy_latest_first(self, files: List[FileInfo]) -> FileInfo:
        """最新优先策略"""
        return max(files, key=lambda f: f.end_date)
    
    def _strategy_max_coverage(self, files: List[FileInfo]) -> FileInfo:
        """最大覆盖策略"""
        return max(files, key=lambda f: f.coverage_days)
    
    def _strategy_best_match(self, files: List[FileInfo]) -> FileInfo:
        """最佳匹配策略"""
        return max(files, key=lambda f: f.total_score)
    
    def _strategy_smart_comprehensive(self, files: List[FileInfo]) -> FileInfo:
        """智能综合策略"""
        # 综合评分已在analyze_file中计算
        return max(files, key=lambda f: f.total_score)
    
    def _display_candidates(self, file_analysis: List[FileInfo]):
        """显示候选文件信息（表格形式，文件名列自适应宽度）"""
        self.smart_logger.verbose_log('info', "📋 候选文件分析:")

        # 计算文件名列的最佳宽度
        max_filename_len = max(len(info.filename) for info in file_analysis)
        # 设置合理的宽度范围：最小30，最大80
        filename_width = max(30, min(max_filename_len + 2, 80))

        # 动态计算总宽度
        total_width = 4 + filename_width + 12 + 6 + 8 + 8 + 8 + 8 + 7  # 各列宽度之和

        # 表格标题和分隔符
        header = f"{'序号':<4} {'文件名':<{filename_width}} {'时间范围':<12} {'天数':<6} {'大小':<8} {'新鲜度':<8} {'覆盖度':<8} {'总分':<8}"
        separator = "=" * total_width

        self.smart_logger.verbose_log('info', separator)
        self.smart_logger.verbose_log('info', header)
        self.smart_logger.verbose_log('info', separator)

        # 表格内容
        for i, info in enumerate(file_analysis, 1):
            # 智能处理文件名显示
            display_name = info.filename
            if len(display_name) > filename_width - 2:
                # 如果文件名太长，保留开头和结尾，中间用...替代
                keep_chars = (filename_width - 5) // 2  # 减去...的3个字符和一些边距
                start_part = display_name[:keep_chars]
                end_part = display_name[-keep_chars:]
                display_name = f"{start_part}...{end_part}"

            # 格式化时间范围（只显示月日）
            start_md = f"{info.start_date[4:6]}{info.start_date[6:8]}"
            end_md = f"{info.end_date[4:6]}{info.end_date[6:8]}"
            time_range = f"{start_md}-{end_md}"

            # 格式化文件大小
            size_kb = info.file_size / 1024
            if size_kb < 1000:
                size_str = f"{size_kb:.0f}KB"
            else:
                size_str = f"{size_kb/1024:.1f}MB"

            # 格式化行内容（使用动态宽度）
            row = f"{i:<4} {display_name:<{filename_width}} {time_range:<12} {info.coverage_days:<6} {size_str:<8} {info.freshness_score:<8.1f} {info.coverage_score:<8.1f} {info.total_score:<8.1f}"
            self.smart_logger.verbose_log('info', row)

        self.smart_logger.verbose_log('info', separator)
    
    def _log_selection_reason(self, selected: FileInfo, all_files: List[FileInfo], strategy: str):
        """记录选择原因"""
        self.smart_logger.verbose_log('info', f"🎯 选择原因 (策略: {strategy}):")
        
        if strategy == 'latest_first':
            self.smart_logger.verbose_log('info', f"  选择最新结束日期的文件: {selected.end_date}")
        elif strategy == 'max_coverage':
            self.smart_logger.verbose_log('info', f"  选择覆盖天数最多的文件: {selected.coverage_days}天")
        elif strategy == 'smart_comprehensive':
            self.smart_logger.verbose_log('info', f"  综合评分最高: {selected.total_score:.2f}")
            self.smart_logger.verbose_log('info', f"    新鲜度: {selected.freshness_score:.1f}/365")
            self.smart_logger.verbose_log('info', f"    覆盖度: {selected.coverage_score:.1f}天")
    
    def should_rename_after_update(self, original_file: str, new_start: str, new_end: str) -> Tuple[bool, str]:
        """
        判断更新后是否需要重命名文件
        
        Args:
            original_file: 原始文件路径
            new_start: 新的开始日期
            new_end: 新的结束日期
            
        Returns:
            (是否需要重命名, 新文件名)
        """
        try:
            original_filename = os.path.basename(original_file)
            
            # 解析原始文件名
            match = re.match(self.filename_pattern, original_filename)
            if not match:
                return False, original_filename
            
            frequency_str, stock_code, old_start, old_end = match.groups()
            
            # 检查时间范围是否变化
            if old_start == new_start and old_end == new_end:
                # 时间范围没有变化，不需要重命名
                return False, original_filename
            
            # 生成新文件名（添加时间戳）
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d%H%M')
            new_filename = f"{frequency_str}_0_{stock_code}_{new_start}-{new_end}_来源互联网（{timestamp}）.txt"

            # 根据配置决定是否显示重命名详情
            if self.config.get('auto_rename', {}).get('show_rename_details', True):
                self.smart_logger.verbose_log('info', f"📝 时间范围变化，需要重命名:")
                self.smart_logger.verbose_log('info', f"  原始: {old_start}-{old_end}")
                self.smart_logger.verbose_log('info', f"  更新: {new_start}-{new_end}")
                self.smart_logger.verbose_log('info', f"  新文件名: {new_filename}")

            return True, new_filename
            
        except Exception as e:
            self.smart_logger.log_error(f"判断重命名失败: {e}")
            return False, os.path.basename(original_file)


def demonstrate_smart_file_selection():
    """演示智能文件选择"""
    from utils.structured_output_formatter import print_banner
    print_banner("智能文件选择演示", "展示智能文件选择器的工作原理")
    
    # 创建模拟的文件环境
    output_dir = "demo_output"
    os.makedirs(output_dir, exist_ok=True)
    
    # 创建模拟文件
    demo_files = [
        "1min_0_000617_20250101-20250726_来源互联网.txt",
        "1min_0_000617_20250401-20250726_来源互联网.txt",
        "1min_0_000617_20250301-20250630_来源互联网.txt",
        "1min_0_000617_20250501-20250725_来源互联网.txt"
    ]
    
    for filename in demo_files:
        filepath = os.path.join(output_dir, filename)
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write("股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖\n")
            f.write("000617|20250101|0.0|10.50|10.50|1000|500|500\n")
    
    # 创建智能文件选择器
    selector = SmartFileSelector(output_dir)
    
    # 测试不同策略
    strategies = ['latest_first', 'max_coverage', 'smart_comprehensive']
    
    for strategy in strategies:
        print(f"\n🎯 测试策略: {strategy}")
        print("-" * 40)
        
        best_file = selector.find_best_file(
            stock_code='000617',
            target_start='20250401',
            target_end='20250726',
            strategy=strategy
        )
        
        if best_file:
            print(f"选择结果: {os.path.basename(best_file)}")
    
    # 测试重命名判断
    print(f"\n📝 测试重命名判断:")
    print("-" * 40)
    
    test_cases = [
        ("1min_0_000617_20250401-20250726_来源互联网.txt", "20250401", "20250731"),
        ("1min_0_000617_20250401-20250726_来源互联网.txt", "20250301", "20250726"),
        ("1min_0_000617_20250401-20250726_来源互联网.txt", "20250401", "20250726")
    ]
    
    for original, new_start, new_end in test_cases:
        should_rename, new_name = selector.should_rename_after_update(
            os.path.join(output_dir, original), new_start, new_end
        )
        print(f"原文件: {original}")
        print(f"新范围: {new_start}-{new_end}")
        print(f"需要重命名: {'是' if should_rename else '否'}")
        if should_rename:
            print(f"新文件名: {new_name}")
        print()
    
    # 清理演示文件
    import shutil
    shutil.rmtree(output_dir)
    print("🧹 演示文件已清理")


if __name__ == '__main__':
    demonstrate_smart_file_selection()
