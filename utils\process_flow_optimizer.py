#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流程优化器

统一管理程序执行流程的输出，避免混乱的跳跃和过多的技术细节

作者: AI Assistant
创建时间: 2025-07-31
"""

from typing import Optional, Dict, Any, List
from datetime import datetime
import time

from utils.structured_output_formatter import (
    print_main_process, print_sub_process, print_step, print_action,
    print_result, print_info, print_warning, print_error
)


class ProcessFlowOptimizer:
    """流程优化器"""
    
    def __init__(self):
        self.current_main_process = None
        self.current_sub_process = None
        self.current_step = None
        self.step_start_time = None
        self.process_stack = []

        # 扩展：智能输出控制
        self.context = "general"
        self.suppressed_operations = set()
        self.optimization_stats = {
            "total_optimizations": 0,
            "suppressed_details": 0,
            "enhanced_summaries": 0
        }

        # 预定义的优化场景
        self.optimization_scenarios = {
            "minute_data_update": {
                "suppress": ["除权除息历史数据详情", "详细计算步骤", "缓存状态详情", "二进制数据预览"],
                "enhance": ["进度指示", "结果摘要", "关键错误"]
            },
            "data_analysis": {
                "suppress": ["调试信息", "性能警告", "详细文件操作日志"],
                "enhance": ["数据统计", "分析结果", "质量报告"]
            },
            "file_operations": {
                "suppress": ["详细文件操作日志", "二进制数据预览", "缓存状态详情"],
                "enhance": ["文件状态", "操作结果", "错误信息"]
            },
            "error_handling": {
                "suppress": ["技术堆栈信息", "调试信息"],
                "enhance": ["用户友好错误说明", "解决建议", "回退方案"]
            }
        }
        
    def start_main_process(self, process_name: str, description: str = None):
        """开始主流程"""
        self.current_main_process = process_name
        if description:
            print_main_process(f"{process_name} - {description}")
        else:
            print_main_process(process_name)
    
    def start_sub_process(self, process_name: str, current: int = None, total: int = None):
        """开始子流程"""
        self.current_sub_process = process_name
        print_sub_process(process_name, current, total)
    
    def start_step(self, step_name: str, current: int = None, total: int = None):
        """开始步骤"""
        self.current_step = step_name
        self.step_start_time = time.time()
        print_step(step_name, current, total)
    
    def complete_step(self, success: bool = True, details: str = None):
        """完成步骤"""
        if self.step_start_time:
            elapsed = time.time() - self.step_start_time
            if details:
                result_text = f"{details} (耗时: {elapsed:.2f}秒)"
            else:
                result_text = f"步骤完成 (耗时: {elapsed:.2f}秒)"
        else:
            result_text = details or "步骤完成"
        
        print_result(result_text, success, level=2)
        self.step_start_time = None
    
    def show_cache_initialization(self, cache_type: str, mode: str = "memory"):
        """显示缓存初始化（简化版）"""
        print_action(f"初始化{cache_type}缓存系统", level=2)
        
    def show_cache_result(self, cache_type: str, success: bool, details: str = None):
        """显示缓存结果（简化版）"""
        if success:
            result_text = f"{cache_type}缓存系统就绪"
            if details:
                result_text += f" - {details}"
        else:
            result_text = f"{cache_type}缓存系统初始化失败"
            if details:
                result_text += f": {details}"
        
        print_result(result_text, success, level=2)
    
    def show_data_calculation(self, description: str, result: Any):
        """显示数据计算结果"""
        print_info(f"{description}: {result}", level=2)
    
    def show_error_with_fallback(self, error_msg: str, fallback_action: str):
        """显示错误和回退操作"""
        print_warning(f"{error_msg}", level=2)
        print_info(f"回退方案: {fallback_action}", level=2)
    
    def suppress_technical_details(self, operation: str):
        """抑制技术细节，只显示关键信息"""
        self.suppressed_operations.add(operation)
        self.optimization_stats["suppressed_details"] += 1
        print_action(f"执行{operation}", level=3)

    def set_context(self, context: str):
        """设置当前处理上下文"""
        self.context = context
        if context in self.optimization_scenarios:
            scenario = self.optimization_scenarios[context]
            for operation in scenario.get("suppress", []):
                self.suppressed_operations.add(operation)

    def should_suppress(self, operation: str) -> bool:
        """判断是否应该抑制某个操作的详细输出"""
        return operation in self.suppressed_operations

    def should_enhance(self, operation: str) -> bool:
        """判断是否应该增强某个操作的输出"""
        if self.context in self.optimization_scenarios:
            scenario = self.optimization_scenarios[self.context]
            return operation in scenario.get("enhance", [])
        return False

    def optimize_data_preview(self, data_type: str, total_rows: int, preview_rows: int = 3) -> int:
        """优化数据预览行数"""
        if self.should_suppress("数据预览详情"):
            return min(preview_rows, 3)  # 最多显示3行
        return preview_rows

    def optimize_error_display(self, error_msg: str, technical_details: str = None) -> str:
        """优化错误信息显示"""
        if self.should_suppress("技术堆栈信息") and technical_details:
            # 只显示用户友好的错误信息
            return f"❌ {error_msg}"
        elif technical_details:
            return f"❌ {error_msg}\n🔧 技术详情: {technical_details}"
        return f"❌ {error_msg}"

    def optimize_calculation_display(self, calculation_name: str, show_details: bool = True) -> bool:
        """优化计算过程显示"""
        if self.should_suppress("详细计算步骤"):
            return False
        return show_details
    
    def batch_suppress_outputs(self, operations: List[str]):
        """批量抑制多个操作的详细输出"""
        if len(operations) == 1:
            print_action(f"执行{operations[0]}", level=3)
        else:
            print_action(f"执行{len(operations)}个内部操作", level=3)


# 全局流程优化器实例
flow_optimizer = ProcessFlowOptimizer()


def optimize_cache_initialization_flow():
    """优化缓存初始化流程的显示"""
    
    def cache_init_wrapper(original_func):
        """缓存初始化包装器"""
        def wrapper(*args, **kwargs):
            # 开始缓存初始化
            flow_optimizer.show_cache_initialization("GBBQ", "memory")
            
            try:
                # 执行原始函数，但抑制其内部输出
                result = original_func(*args, **kwargs)
                
                # 显示成功结果
                flow_optimizer.show_cache_result("GBBQ", True, "内存模式")
                return result
                
            except Exception as e:
                # 显示失败结果
                flow_optimizer.show_cache_result("GBBQ", False, str(e))
                raise
        
        return wrapper
    
    return cache_init_wrapper


def optimize_data_processing_flow():
    """优化数据处理流程的显示"""
    
    def data_process_wrapper(original_func):
        """数据处理包装器"""
        def wrapper(*args, **kwargs):
            # 抑制内部技术细节
            flow_optimizer.suppress_technical_details("数据处理")
            
            try:
                result = original_func(*args, **kwargs)
                return result
            except Exception as e:
                flow_optimizer.show_error_with_fallback(
                    f"数据处理失败: {e}",
                    "使用备用方法"
                )
                raise
        
        return wrapper
    
    return data_process_wrapper


def create_unified_flow_manager():
    """创建统一的流程管理器"""
    
    class UnifiedFlowManager:
        """统一流程管理器"""
        
        def __init__(self):
            self.optimizer = ProcessFlowOptimizer()
        
        def start_data_download_flow(self, stock_code: str, date_range: str):
            """开始数据下载流程"""
            self.optimizer.start_main_process("数据下载任务")
            self.optimizer.start_sub_process(f"处理股票 {stock_code}")
            self.optimizer.show_data_calculation("数据量计算", f"{date_range} = 4800条")
        
        def handle_cache_operations(self, operations: List[str]):
            """处理缓存操作"""
            self.optimizer.start_step("缓存系统初始化")
            self.optimizer.batch_suppress_outputs(operations)
            self.optimizer.complete_step(True, "缓存系统就绪")
        
        def handle_error_recovery(self, error: str, recovery_action: str):
            """处理错误恢复"""
            self.optimizer.show_error_with_fallback(error, recovery_action)
        
        def complete_flow(self, success: bool, summary: str):
            """完成流程"""
            print_result(summary, success, level=1)
    
    return UnifiedFlowManager()


# 使用示例和最佳实践
def demonstrate_optimized_flow():
    """演示优化后的流程"""
    
    # 创建流程管理器
    flow_manager = create_unified_flow_manager()
    
    # 开始数据下载流程
    flow_manager.start_data_download_flow("000617", "20250704到现在")
    
    # 处理缓存操作（将多个技术细节合并）
    cache_operations = [
        "GBBQ缓存初始化",
        "统一缓存管理器初始化", 
        "内存预加载",
        "缓存验证"
    ]
    flow_manager.handle_cache_operations(cache_operations)
    
    # 处理错误恢复
    flow_manager.handle_error_recovery(
        "加载GBBQ数据失败: '类别'",
        "使用传统方法"
    )
    
    # 完成流程
    flow_manager.complete_flow(True, "数据处理完成")


if __name__ == "__main__":
    # 演示优化效果
    print("=== 优化前的混乱输出 ===")
    print("📋 数据量计算: 20250704到现在 = 20个交易日 × 240条/日 = 4800条")
    print("✅ GBBQ缓存初始化完成（延迟加载模式）")
    print("✅ 统一缓存管理器初始化完成")
    print("💾 启用高性能gbbq缓存系统")
    print("📊 【初始化gbbq缓存】开始")
    print("✅ 使用现有有效缓存")
    print("💾 正在预加载gbbq数据到内存...")
    print("🧠 加载缓存到内存...")
    print("✅ 内存加载完成，耗时: 0.400秒")
    print("📊 内存中股票数量: 5827")
    print("💾 gbbq内存缓存初始化完成，耗时: 0.40秒")
    print("❌ 加载GBBQ数据失败: '类别'")
    
    print("\n=== 优化后的清晰输出 ===")
    demonstrate_optimized_flow()
