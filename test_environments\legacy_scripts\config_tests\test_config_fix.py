#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试配置修复
验证时间范围配置是否正确读取user_config.py
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

def test_config_reading():
    """测试配置读取功能"""
    print("🧪 测试配置读取功能")
    print("=" * 60)
    
    try:
        # 1. 测试user_config.py读取
        print("📋 [1/4] 测试user_config.py读取...")
        import user_config
        
        time_ranges = getattr(user_config, 'time_ranges', {})
        internet_minute = time_ranges.get('internet_minute', {})
        
        print(f"   ✅ internet_minute配置:")
        print(f"      enabled: {internet_minute.get('enabled')}")
        print(f"      start_date: {internet_minute.get('start_date')}")
        print(f"      end_date: {internet_minute.get('end_date')}")
        print(f"      frequency: {internet_minute.get('frequency')}")
        
        # 2. 测试ConfigManager
        print("\n📋 [2/4] 测试ConfigManager...")
        try:
            from mythquant.config import config_manager
        except ImportError:
            # 尝试直接导入
            from mythquant.core.config_manager import config_manager
        
        # 测试时间范围获取
        config_time_ranges = config_manager.get_time_ranges()
        print(f"   ✅ ConfigManager时间范围: {len(config_time_ranges)} 个配置")
        
        # 测试任务配置获取
        task_configs = config_manager.get_task_configs()
        print(f"   ✅ 任务配置数量: {len(task_configs)}")
        
        for i, config in enumerate(task_configs, 1):
            print(f"   📋 任务{i}: {config['name']}")
            print(f"      时间范围: {config['start_time']} 至 {config['end_time']}")
            print(f"      数据类型: {config['data_type']}")
            print(f"      是否启用: {config['enabled']}")
        
        # 3. 测试highlight_critical_info方法
        print("\n📋 [3/4] 测试highlight_critical_info方法...")
        highlighted = config_manager.highlight_critical_info('测试关键信息')
        print(f"   ✅ 高亮结果: {highlighted}")
        
        # 4. 验证时间范围是否正确
        print("\n📋 [4/4] 验证时间范围正确性...")
        
        expected_start = "2025-01-01"
        expected_end = "2025-07-27"
        
        minute_task = None
        for config in task_configs:
            if config['data_type'] == 'minute':
                minute_task = config
                break
        
        if minute_task:
            actual_start = minute_task['start_time']
            actual_end = minute_task['end_time']
            
            if actual_start.startswith('2025') and actual_end.startswith('2025'):
                print(f"   ✅ 时间范围正确: {actual_start} 至 {actual_end}")
                print("   ✅ 不再使用硬编码的2024年时间")
                return True
            else:
                print(f"   ❌ 时间范围错误: {actual_start} 至 {actual_end}")
                print("   ❌ 仍在使用错误的时间配置")
                return False
        else:
            print("   ❌ 未找到分钟级任务配置")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_main_integration():
    """测试main.py集成"""
    print("\n🧪 测试main.py集成")
    print("=" * 60)
    
    try:
        print("📋 运行main.py并检查时间范围...")
        
        # 运行main.py并捕获输出
        import subprocess
        result = subprocess.run(
            [sys.executable, 'main.py'],
            capture_output=True,
            text=True,
            timeout=30,
            encoding='utf-8',
            errors='replace'
        )
        
        output = result.stdout + result.stderr
        
        # 检查输出中的时间范围
        if "2025-01-01" in output and "2025-07-27" in output:
            print("   ✅ main.py使用了正确的2025年时间范围")
            return True
        elif "2024-01-01" in output and "2024-12-31" in output:
            print("   ❌ main.py仍在使用错误的2024年时间范围")
            print("   📋 相关输出:")
            for line in output.split('\n'):
                if '2024' in line:
                    print(f"      {line}")
            return False
        else:
            print("   ⚠️ 未在输出中找到明确的时间范围信息")
            print("   📋 输出摘要:")
            lines = output.split('\n')[:10]
            for line in lines:
                if line.strip():
                    print(f"      {line}")
            return True  # 可能是其他原因，不算失败
            
    except subprocess.TimeoutExpired:
        print("   ⚠️ main.py执行超时（30秒）")
        return False
    except Exception as e:
        print(f"   ❌ main.py测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🎯 配置修复验证测试")
    print("=" * 80)
    print("📋 测试目标: 验证时间范围配置是否正确读取user_config.py")
    print("=" * 80)
    
    test_results = []
    
    # 执行测试
    test_results.append(test_config_reading())
    test_results.append(test_main_integration())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 配置修复测试结果")
    print("=" * 80)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if success_rate >= 80:
        print("\n🎉 结论: 配置修复成功！")
        print("💡 程序现在正确读取user_config.py中的时间配置")
        print("✅ 不再使用硬编码的2024年时间范围")
        print("✅ ConfigManager方法缺失问题已解决")
    else:
        print("\n❌ 结论: 配置修复不完整")
        print("💡 仍需要进一步修复配置读取问题")
    
    return success_rate >= 80


if __name__ == '__main__':
    success = main()
    if success:
        print("\n🎯 下一步: 可以进行完整的功能测试")
    else:
        print("\n🔧 下一步: 需要进一步修复配置问题")
