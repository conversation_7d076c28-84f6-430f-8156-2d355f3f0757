2025-07-28 01:23:43,775 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_012343.log
2025-07-28 01:23:43,775 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-28 01:23:43,798 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-28 01:23:43,799 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-28 01:23:43,799 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-28 01:23:43,800 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-28 01:23:43,801 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-28 01:23:44,070 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.178s
2025-07-28 01:23:44,078 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.56.198 (115.238.56.198:7709) - 0.183s
2025-07-28 01:23:44,082 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************ (************:7709) - 0.182s
2025-07-28 01:23:44,086 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.184s
2025-07-28 01:23:44,089 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.188s
2025-07-28 01:23:44,120 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.212s
2025-07-28 01:23:44,126 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-28 01:23:44,126 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-28 01:23:44,126 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-28 01:23:44,126 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-28 01:23:44,126 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-28 01:23:44,127 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-28 01:23:44,127 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-28 01:23:44,639 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-28 01:23:44,639 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-28 01:23:44,646 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-28 01:23:44,646 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-28 01:23:44,647 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-28 01:23:44,647 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-28 01:23:44,648 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-28 01:23:44,648 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-28 01:23:44,648 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-28 01:23:44,651 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-28 01:23:44,652 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-28 01:23:45,203 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-28 01:23:45,204 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 01:23:45,204 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 01:23:45,204 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 01:23:45,204 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 01:23:45,204 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 01:23:45,205 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: TDX日线级别数据生成
2025-07-28 01:23:45,205 - Main - INFO - info:307 - ℹ️ 📊 发现 4 个现有数据文件，开始稽核
2025-07-28 01:23:45,481 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 3 个文件质量不达标
2025-07-28 01:23:45,481 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-28 01:23:45,481 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-28 01:23:45,481 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-28 01:23:45,481 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-28 01:23:45,481 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-28 01:23:45,632 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-28 01:23:45,633 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-28 01:23:45,639 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-28 01:23:45,639 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-28 01:23:45,639 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-28 01:23:45,640 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-28 01:23:45,640 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-28 01:23:45,640 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-28 01:23:45,640 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-28 01:23:45,644 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-28 01:23:45,645 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-28 01:24:25,130 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-28 01:24:31,732 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-28 01:24:31,927 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-28 01:24:32,449 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-28 01:24:32,656 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-28 01:24:32,656 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-28 01:24:32,751 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-28 01:24:32,752 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-28 01:24:32,752 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-28 01:24:32,753 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-28 01:24:32,753 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-28 01:24:32,753 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-28 01:24:32,753 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-28 01:24:32,767 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 47.29 秒
2025-07-28 01:24:32,767 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-28 01:24:32,767 - Main - INFO - info:307 - ℹ️ 🔍 执行任务后数据质量稽核: TDX日线级别数据生成
2025-07-28 01:24:32,767 - Main - INFO - info:307 - ℹ️ 📊 发现 1 个新生成文件，开始稽核
2025-07-28 01:24:32,821 - Main - INFO - info:307 - ℹ️ ✅ 执行后稽核通过: 0 优秀, 1 良好
2025-07-28 01:24:32,821 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: 互联网分钟级数据下载
2025-07-28 01:24:32,821 - Main - INFO - info:307 - ℹ️ 📊 发现 5 个现有数据文件，开始稽核
2025-07-28 01:24:33,148 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 4 个文件质量不达标
2025-07-28 01:24:33,148 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 01:24:33,148 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-28 01:24:33,149 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-28 01:24:33,153 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-28 01:24:33,572 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-28 01:24:33,587 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-28 01:24:33,587 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-28 01:24:33,587 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-28 01:24:33,588 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-28 01:24:33,588 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-28 01:24:33,588 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-28 01:24:33,588 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-28 01:24:33,588 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-28 01:24:33,591 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-28 01:24:33,591 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-28 01:24:33,591 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-28 01:24:33,592 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-28 01:24:33,592 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-28 01:24:33,614 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-07-28 01:24:33,614 - Main - INFO - info:307 - ℹ️ 🚀 开始处理缺失数据: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-28 01:24:33,616 - core.logging_service - ERROR - log_error:133 - 前置验证失败: 'LoggingService' object has no attribute 'log_info'
2025-07-28 01:24:33,616 - Main - ERROR - error:315 - ❌ 处理缺失数据失败: 'SmartLogger' object has no attribute 'log_warning'
2025-07-28 01:24:33,619 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 01:24:33,619 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 01:24:33,620 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-28 01:24:33,620 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 01:24:33,790 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 01:24:33,791 - core.logging_service - ERROR - log_error:133 - 计算数据条数失败: 'LoggingService' object has no attribute 'log_info'
2025-07-28 01:24:33,791 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250704, 交易日: 17天, 频率: 1min)
2025-07-28 01:24:33,791 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 01:24:33,791 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 01:24:33,792 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 01:24:33,843 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 01:24:33,843 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 01:24:34,016 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 01:24:34,064 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 01:24:34,065 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 01:24:34,065 - Main - ERROR - error:315 - ❌ 检查数据覆盖失败: list indices must be integers or slices, not str
2025-07-28 01:24:34,065 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 01:24:34,065 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: None ~ None
2025-07-28 01:24:34,065 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250704
2025-07-28 01:24:34,068 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250704 - 20250704
2025-07-28 01:24:34,068 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-28 01:24:34,069 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-28 01:24:34,069 - Main - INFO - info:307 - ℹ️ ❌ 数据一致性验证失败: 无法获取API数据进行比对
2025-07-28 01:24:34,069 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-28 01:24:34,069 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 01:24:34,069 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 01:24:34,069 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-28 01:24:34,069 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 01:24:34,237 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 01:24:34,239 - core.logging_service - ERROR - log_error:133 - 计算数据条数失败: 'LoggingService' object has no attribute 'log_info'
2025-07-28 01:24:34,239 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250101, 交易日: 136天, 频率: 1min)
2025-07-28 01:24:34,240 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 01:24:34,240 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 01:24:34,240 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 01:24:34,294 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 01:24:34,294 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 01:24:34,470 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 01:24:34,516 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 01:24:34,516 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 01:24:34,516 - Main - ERROR - error:315 - ❌ 检查数据覆盖失败: list indices must be integers or slices, not str
2025-07-28 01:24:34,516 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 01:24:34,516 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: None ~ None
2025-07-28 01:24:34,516 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250101
2025-07-28 01:24:34,520 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 1000 条 1min 数据
2025-07-28 01:24:34,521 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共1000条记录
2025-07-28 01:24:34,521 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 01:24:34,527 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=9.380, 前复权=9.380
2025-07-28 01:24:34,528 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 01:24:34,528 - Main - INFO - info:307 - ℹ️   日期: 202507211421
2025-07-28 01:24:34,528 - Main - INFO - info:307 - ℹ️   当日收盘价C: 9.38
2025-07-28 01:24:34,528 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 9.38
2025-07-28 01:24:34,528 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 1000 >= 5
2025-07-28 01:24:34,528 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 01:24:34,529 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 01:24:34,572 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250721-20250725_来源互联网（202507280124）.txt (46853 字节)
2025-07-28 01:24:34,573 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-28 01:24:34,573 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-28 01:24:34,573 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-28 01:24:34,573 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-28 01:24:34,573 - Main - INFO - info:307 - ℹ️ 🔍 执行任务后数据质量稽核: 互联网分钟级数据下载
2025-07-28 01:24:34,574 - Main - INFO - info:307 - ℹ️ 📊 发现 2 个新生成文件，开始稽核
2025-07-28 01:24:34,630 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行后稽核发现问题: 1 个文件质量较差
2025-07-28 01:24:34,630 - Main - WARNING - warning:311 - ⚠️   - 1min_0_000617_20250721-20250725_来源互联网（202507280124）.txt: 数据完整率过低: 83.3%
2025-07-28 01:24:34,630 - Main - WARNING - warning:311 - ⚠️   - day_0_000617_20200101-20250724.txt: 数据完整率偏低: 92.8%
2025-07-28 01:24:34,631 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: 前复权数据比较分析
2025-07-28 01:24:34,631 - Main - INFO - info:307 - ℹ️ 📊 发现 6 个现有数据文件，开始稽核
2025-07-28 01:24:34,956 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 5 个文件质量不达标
2025-07-28 01:24:34,956 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 01:24:34,956 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-28 01:24:34,958 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-28 01:24:34,958 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-28 01:24:34,958 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-28 01:24:34,958 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-28 01:24:34,959 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-28 01:24:34,959 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: True, 互联网: False
2025-07-28 01:24:34,960 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250728_012434.txt
2025-07-28 01:24:34,960 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-28 01:24:34,960 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-28 01:24:34,961 - Main - INFO - info:307 - ℹ️ 🔍 执行最终数据质量稽核汇总
2025-07-28 01:24:34,961 - Main - INFO - info:307 - ℹ️ 📊 对 6 个数据文件进行最终稽核
2025-07-28 01:24:35,369 - Main - INFO - info:307 - ℹ️ 📊 最终数据质量稽核汇总:
2025-07-28 01:24:35,369 - Main - INFO - info:307 - ℹ️   总文件数: 3
2025-07-28 01:24:35,369 - Main - INFO - info:307 - ℹ️   优秀文件: 1 (33.3%)
2025-07-28 01:24:35,369 - Main - INFO - info:307 - ℹ️   良好文件: 1 (33.3%)
2025-07-28 01:24:35,369 - Main - INFO - info:307 - ℹ️   较差文件: 1 (33.3%)
2025-07-28 01:24:35,369 - Main - INFO - info:307 - ℹ️   平均完整率: 91.64%
2025-07-28 01:24:35,369 - Main - WARNING - warning:311 - ⚠️ ⚠️ 需要关注的文件 (完整率<85%):
2025-07-28 01:24:35,369 - Main - WARNING - warning:311 - ⚠️   - 1min_0_000617_20250721-20250725_来源互联网（202507280124）.txt: 83.33%
2025-07-28 01:24:35,369 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 66.7%
2025-07-28 01:24:35,370 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 01:24:35,370 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 01:24:35,371 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-28 01:24:35,371 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-28 01:24:35,371 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 01:24:35,371 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 01:24:35,371 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
