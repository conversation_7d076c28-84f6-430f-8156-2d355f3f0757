#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量下载管理器
实现分钟级数据的增量下载和智能更新策略
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal, ROUND_HALF_UP
from typing import Dict, List, Tuple, Optional, Any
import logging
import re

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.enhanced_error_handler import get_smart_logger, get_error_handler, ErrorCategory
class IncrementalDownloader:
    """增量下载管理器"""

    def __init__(self, output_dir: str = "H:/MPV1.17/T0002/signals/"):
        """
        初始化增量下载管理器

        Args:
            output_dir: 数据文件目录
        """
        self.output_dir = output_dir
        self.smart_logger = get_smart_logger("IncrementalDownloader")
        self.error_handler = get_error_handler()
        # 移除智能文件选择器依赖，遵循1min_workflow_improved.md第310行规定
        
    def find_existing_file(self, stock_code: str, data_type: str = "minute",
                          target_start: str = None, target_end: str = None) -> Optional[str]:
        """
        智能查找指定股票的现有数据文件（支持多文件冲突处理）

        Args:
            stock_code: 股票代码
            data_type: 数据类型（"minute"=分钟, "daily"=日线）
            target_start: 目标开始日期（用于智能选择）
            target_end: 目标结束日期（用于智能选择）

        Returns:
            文件路径或None
        """
        # 根据1min_workflow_improved.md第310行规定：
        # "增量更新过程不能再进行智能文件选择，而应以当前程序正在处置的文件为更新的标的"
        self.smart_logger.warning(f"⚠️ find_existing_file方法已废弃，违反workflow规范")
        self.smart_logger.warning(f"⚠️ 应该直接传递已选择的文件路径给增量下载器")
        return None
    
    def parse_existing_data_range(self, filepath: str) -> Tuple[Optional[str], Optional[str]]:
        """
        解析现有数据文件的时间范围
        
        Args:
            filepath: 文件路径
            
        Returns:
            (开始时间, 结束时间) 或 (None, None)
        """
        try:
            if not os.path.exists(filepath):
                return None, None
            
            # 从文件名中提取时间范围
            filename = os.path.basename(filepath)
            
            # 匹配时间范围模式：YYYYMMDD-YYYYMMDD
            time_pattern = r'(\d{8})-(\d{8})'
            match = re.search(time_pattern, filename)
            
            if match:
                start_date = match.group(1)
                end_date = match.group(2)
                self.smart_logger.info(f"从文件名解析时间范围: {start_date} - {end_date}")
                return start_date, end_date
            
            # 如果文件名解析失败，尝试读取文件内容
            df = pd.read_csv(filepath, sep='|', encoding='utf-8', nrows=1000)  # 只读前1000行提高效率
            
            if '时间' in df.columns and len(df) > 0:
                # 处理时间格式（可能是YYYYMMDD或YYYYMMDD HH:MM）
                time_values = df['时间'].astype(str)
                
                # 提取日期部分
                dates = []
                for time_val in time_values:
                    if ' ' in time_val:
                        # 分钟级格式：YYYYMMDD HH:MM
                        date_part = time_val.split(' ')[0]
                    else:
                        # 日线格式：YYYYMMDD
                        date_part = time_val
                    dates.append(date_part)
                
                if dates:
                    start_date = min(dates)
                    end_date = max(dates)
                    self.smart_logger.info(f"从文件内容解析时间范围: {start_date} - {end_date}")
                    return start_date, end_date
            
            return None, None
            
        except Exception as e:
            self.smart_logger.error(f"解析现有数据范围失败: {e}")
            return None, None
    
    def get_last_record(self, filepath: str) -> Optional[Dict[str, Any]]:
        """
        获取现有文件的最后一条记录
        
        Args:
            filepath: 文件路径
            
        Returns:
            最后一条记录的字典或None
        """
        try:
            if not os.path.exists(filepath):
                return None
            
            # 读取文件的最后几行
            df = pd.read_csv(filepath, sep='|', encoding='utf-8')
            
            if len(df) == 0:
                return None
            
            # 获取最后一条记录
            last_record = df.iloc[-1].to_dict()
            
            self.smart_logger.info(f"获取最后一条记录: 时间={last_record.get('时间')}, 前复权价={last_record.get('前复权收盘价C')}")
            
            return last_record
            
        except Exception as e:
            self.smart_logger.error(f"获取最后记录失败: {e}")
            return None
    
    def check_price_consistency(self, stock_code: str, last_record: Dict[str, Any],
                              downloader, frequency: str = "1") -> bool:
        """
        检查最后一条记录的前复权价是否与互联网数据一致

        Args:
            stock_code: 股票代码
            last_record: 最后一条记录
            downloader: 数据下载器实例
            frequency: 数据频率（严格使用配置的频率，不允许切换）

        Returns:
            True=一致(可增量), False=不一致(需全量)
        """
        try:
            # 解析最后记录的时间
            last_time = str(last_record.get('时间', ''))

            if ' ' in last_time:
                # 分钟级格式：YYYYMMDD HH:MM
                last_date = last_time.split(' ')[0]
            else:
                # 日线格式：YYYYMMDD
                last_date = last_time

            self.smart_logger.info(f"检查 {stock_code} 在 {last_date} 的前复权价一致性")
            self.smart_logger.info(f"🎯 严格使用配置的频率: {frequency} (不允许自动切换频率)")

            # 下载该日期的数据进行对比（严格使用配置的频率）
            current_data = downloader.download_stock_data(
                stock_code=stock_code,
                start_date=last_date,
                end_date=last_date,
                frequency=frequency,  # 严格使用传入的频率，不再硬编码为"5"
                suppress_warnings=True  # 在价格比较中抑制警告
            )
            
            if current_data is None or len(current_data) == 0:
                # 严格按照配置频率执行，如果无数据则明确报错
                self.smart_logger.error(f"❌ 无法获取 {last_date} 的{frequency}分钟数据")
                self.smart_logger.error(f"❌ 这可能是因为:")
                self.smart_logger.error(f"   1. 该日期没有{frequency}分钟级别的交易数据")
                self.smart_logger.error(f"   2. 服务器不支持该时间段的{frequency}分钟数据")
                self.smart_logger.error(f"   3. 网络连接问题或服务器限制")
                self.smart_logger.error(f"💡 建议: 检查时间范围配置或网络连接")
                # 返回False触发全量下载，让用户看到具体的错误信息
                return False
            
            # 获取最后一条记录的前复权价
            stored_adj_price = float(last_record.get('前复权收盘价C', 0))
            
            # 查找对应时间的数据
            if ' ' in last_time:
                # 分钟级数据，需要精确匹配时间
                target_time = pd.to_datetime(last_time, format='%Y%m%d %H:%M')
                current_data['datetime'] = pd.to_datetime(current_data['date'])
                
                # 找到最接近的时间点
                time_diff = abs(current_data['datetime'] - target_time)
                closest_idx = time_diff.idxmin()
                current_adj_price = float(current_data.loc[closest_idx, 'close_qfq'])
            else:
                # 日线数据，使用收盘价
                current_adj_price = float(current_data.iloc[-1]['close_qfq'])
            
            # 比较前复权价（使用容差比较）
            price_diff = abs(stored_adj_price - current_adj_price)
            tolerance = 0.001  # 0.1分的容差
            
            is_consistent = price_diff < tolerance
            
            self.smart_logger.info(f"前复权价对比: 存储={stored_adj_price:.3f}, 当前={current_adj_price:.3f}, 差异={price_diff:.6f}")
            
            if is_consistent:
                self.smart_logger.info("✅ 前复权价一致，可以使用增量下载")
            else:
                self.smart_logger.warning("⚠️ 前复权价不一致，需要全量重新下载")
            
            return is_consistent
            
        except Exception as e:
            self.smart_logger.error(f"检查价格一致性失败: {e}")
            # 出错时保守选择全量下载
            return False
    
    def calculate_incremental_range(self, existing_start: str, existing_end: str,
                                  target_start: str, target_end: str) -> List[Tuple[str, str]]:
        """
        计算需要增量下载的时间范围
        
        Args:
            existing_start: 现有数据开始时间
            existing_end: 现有数据结束时间
            target_start: 目标开始时间
            target_end: 目标结束时间
            
        Returns:
            需要下载的时间范围列表 [(start1, end1), (start2, end2), ...]
        """
        try:
            ranges_to_download = []
            
            # 转换为日期对象进行比较
            existing_start_date = datetime.strptime(existing_start, '%Y%m%d')
            existing_end_date = datetime.strptime(existing_end, '%Y%m%d')
            target_start_date = datetime.strptime(target_start, '%Y%m%d')
            target_end_date = datetime.strptime(target_end, '%Y%m%d')
            
            # 检查是否需要下载前面的数据
            if target_start_date < existing_start_date:
                # 需要下载更早的数据
                early_end = (existing_start_date - timedelta(days=1)).strftime('%Y%m%d')
                ranges_to_download.append((target_start, early_end))
                self.smart_logger.info(f"需要下载早期数据: {target_start} - {early_end}")
            
            # 检查是否需要下载后面的数据
            if target_end_date > existing_end_date:
                # 需要下载更新的数据
                late_start = (existing_end_date + timedelta(days=1)).strftime('%Y%m%d')
                ranges_to_download.append((late_start, target_end))
                self.smart_logger.info(f"需要下载最新数据: {late_start} - {target_end}")
            
            if not ranges_to_download:
                self.smart_logger.info("✅ 现有数据已覆盖目标范围，无需增量下载")

            return ranges_to_download
            
        except Exception as e:
            self.smart_logger.error(f"计算增量范围失败: {e}")
            # 出错时返回全量下载
            return [(target_start, target_end)]
    
    def merge_data_files(self, existing_file: str, new_data_ranges: List[Tuple[str, str]],
                        stock_code: str, downloader, frequency: str = "1") -> bool:
        """
        合并现有数据和新下载的数据
        
        Args:
            existing_file: 现有文件路径
            new_data_ranges: 新数据的时间范围列表
            stock_code: 股票代码
            downloader: 数据下载器实例
            frequency: 数据频率
            
        Returns:
            是否成功
        """
        try:
            # 读取现有数据
            existing_df = pd.read_csv(existing_file, sep='|', encoding='utf-8')

            # 确保现有数据的时间列为字符串类型
            if '时间' in existing_df.columns:
                existing_df['时间'] = existing_df['时间'].astype(str).str.replace('.0', '', regex=False)

            self.smart_logger.info(f"读取现有数据: {len(existing_df)} 条记录")
            
            # 下载新数据
            new_data_list = []
            
            for start_date, end_date in new_data_ranges:
                self.smart_logger.info(f"下载增量数据: {start_date} - {end_date}")
                
                new_df = downloader.download_stock_data(
                    stock_code=stock_code,
                    start_date=start_date,
                    end_date=end_date,
                    frequency=frequency,
                    suppress_warnings=True  # 在增量下载中抑制警告，避免重复输出
                )
                
                if new_df is not None and len(new_df) > 0:
                    # 转换为目标格式
                    formatted_df = downloader.convert_to_target_format(new_df, stock_code, "minute")
                    new_data_list.append(formatted_df)
                    self.smart_logger.info(f"获得新数据: {len(formatted_df)} 条记录")
            
            if not new_data_list:
                self.smart_logger.info("✅ 现有数据已覆盖目标范围，无需下载新数据")
                # 没有新数据时，为现有文件添加时间戳标识处理过程
                self._add_timestamp_to_existing_file(existing_file, stock_code)
                return True  # 没有新数据也算成功
            
            # 合并所有数据
            all_data = [existing_df] + new_data_list
            merged_df = pd.concat(all_data, ignore_index=True)

            # 修复时间列数据类型不一致问题
            try:
                # 统一时间列为字符串类型，避免字符串和整数比较错误
                merged_df['时间'] = merged_df['时间'].astype(str)

                # 处理可能的浮点数格式（如202507031404.0）
                merged_df['时间'] = merged_df['时间'].str.replace('.0', '', regex=False)

                self.smart_logger.info(f"时间列数据类型统一完成，数据类型: {merged_df['时间'].dtype}")

            except Exception as e:
                self.smart_logger.warning(f"时间列数据类型统一失败: {e}")
                # 如果统一失败，尝试逐行处理
                time_values = []
                for val in merged_df['时间']:
                    if pd.isna(val):
                        time_values.append('')
                    else:
                        time_str = str(val).replace('.0', '')
                        time_values.append(time_str)
                merged_df['时间'] = time_values

            # 按时间排序并去重（现在时间列已统一为字符串类型）
            merged_df = merged_df.sort_values('时间').drop_duplicates(subset=['股票编码', '时间'], keep='last')
            
            self.smart_logger.info(f"合并后数据: {len(merged_df)} 条记录")
            
            # 生成新的文件名
            time_values = merged_df['时间'].astype(str)
            dates = []
            for time_val in time_values:
                if ' ' in time_val:
                    date_part = time_val.split(' ')[0]
                else:
                    date_part = time_val
                dates.append(date_part)
            
            new_start = min(dates)
            new_end = max(dates)
            
            new_filename = f"min_0_{stock_code}_{new_start}-{new_end}_来源互联网.txt"
            new_filepath = os.path.join(self.output_dir, new_filename)
            
            # 使用简单的重命名逻辑，不依赖智能文件选择器
            # 根据1min_workflow_improved.md第310行规定，避免在增量更新中使用智能文件选择器
            should_rename = True  # 总是重命名以反映新的时间范围
            new_filename = f"1min_0_{stock_code}_{new_start}-{new_end}_来源互联网（{datetime.now().strftime('%Y%m%d%H%M')}）.txt"
            new_filepath = os.path.join(self.output_dir, new_filename)

            # 保存合并后的数据
            merged_df.to_csv(new_filepath, sep='|', index=False, encoding='utf-8')

            # 删除旧文件
            if os.path.exists(existing_file) and existing_file != new_filepath:
                os.remove(existing_file)
                self.smart_logger.info(f"删除旧文件: {os.path.basename(existing_file)}")

            if should_rename:
                self.smart_logger.info(f"✅ 智能重命名完成: {new_filename}")
            else:
                self.smart_logger.info(f"✅ 增量更新完成: {new_filename}")

            return True

        except Exception as e:
            self.smart_logger.error(f"合并数据文件失败: {e}")
            return False

    def _add_timestamp_to_existing_file(self, existing_file: str, stock_code: str) -> bool:
        """
        为现有文件添加时间戳标识（当没有增量数据时）

        Args:
            existing_file: 现有文件路径
            stock_code: 股票代码

        Returns:
            是否成功
        """
        try:
            from datetime import datetime

            # 生成当前时间戳（精确到分钟）
            current_time = datetime.now()
            timestamp = current_time.strftime('%Y%m%d%H%M')

            # 解析现有文件名
            existing_filename = os.path.basename(existing_file)
            existing_dir = os.path.dirname(existing_file)

            # 检查文件名是否已经包含时间戳
            if '（' in existing_filename and '）' in existing_filename:
                # 已经有时间戳，更新时间戳
                base_name = existing_filename.split('（')[0]
                extension = '.txt'
                new_filename = f"{base_name}（{timestamp}）{extension}"
            else:
                # 没有时间戳，添加时间戳
                # 移除.txt扩展名
                base_name = existing_filename.replace('.txt', '')
                new_filename = f"{base_name}（{timestamp}）.txt"

            new_filepath = os.path.join(existing_dir, new_filename)

            # 如果新文件名与旧文件名不同，重命名文件
            if new_filepath != existing_file:
                if os.path.exists(existing_file):
                    os.rename(existing_file, new_filepath)
                    self.smart_logger.info(f"✅ 添加时间戳标识: {new_filename}")
                    print(f"📝 无增量数据，文件已标记处理时间: {new_filename}")
                    return True
                else:
                    self.smart_logger.warning(f"原文件不存在: {existing_file}")
                    return False
            else:
                # 文件名没有变化，说明时间戳相同
                self.smart_logger.info(f"文件时间戳已是最新: {existing_filename}")
                return True

        except Exception as e:
            self.smart_logger.error(f"添加时间戳失败: {e}")
            return False
