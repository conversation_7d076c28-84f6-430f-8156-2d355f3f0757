#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 核心应用程序模块
负责协调各个组件，执行主要的业务逻辑
"""

import os
import time
from datetime import datetime
from typing import List, Dict, Any, Optional
import logging

# 更新导入路径以适应新的架构
from src.mythquant.config.manager import ConfigManager
from src.mythquant.core.stock_processor import StockDataProcessor
from src.mythquant.core.task_manager import TaskManager
# from src.mythquant.core.data_quality_auditor import DataQualityAuditor  # 临时屏蔽 2025-07-28

# 这些模块将在后续阶段迁移，暂时保持原有导入路径
try:
    from utils.enhanced_error_handler import get_smart_logger, get_error_handler, ErrorCategory
    from utils.structured_output_formatter import (
        print_main_process, print_sub_process, print_step, print_result,
        print_info, print_warning, print_error, print_stats_table
    )
    from utils.process_flow_optimizer import ProcessFlowOptimizer
except ImportError:
    # 临时兼容性处理
    import logging
    def get_smart_logger(name):
        return logging.getLogger(name)
    class ErrorHandler:
        def log_error(self, error, category=None, operation=None):
            logger.error(f"错误: {error} (类别: {category}, 操作: {operation})")
            return "error_id_placeholder"
        def get_error_statistics(self):
            return {'total_errors': 0}
        def get_performance_statistics(self):
            return {}
        def export_error_report(self, filename):
            pass

    def get_error_handler():
        return ErrorHandler()
    class ErrorCategory:
        SYSTEM = "SYSTEM"
        BUSINESS = "BUSINESS"
    def print_main_process(msg): print(f"🔄 {msg}")
    def print_sub_process(msg, i=1, total=1): print(f"  📋 [{i}/{total}] {msg}")
    def print_step(msg, i=1, total=1): print(f"    🔍 [{i}/{total}] {msg}")
    def print_result(msg, success=True, level=0): print(f"{'✅' if success else '❌'} {msg}")
    def print_info(msg, level=0): print(f"ℹ️ {msg}")
    def print_warning(msg, level=0): print(f"⚠️ {msg}")
    def print_error(msg, level=0): print(f"❌ {msg}")
    def print_stats_table(title, stats):
        print(f"\n📊 {title}:")
        for k, v in stats.items():
            print(f"  {k}: {v}")
    class ProcessFlowOptimizer:
        def start_main_process(self, msg): print_main_process(msg)
        def start_sub_process(self, msg, i=1, total=1): print_sub_process(msg, i, total)
        def show_error_with_fallback(self, msg, fallback): print_error(f"{msg} - {fallback}")
        def show_data_calculation(self, label, value): print_info(f"{label}: {value}")
        def suppress_technical_details(self, msg): pass

logger = logging.getLogger(__name__)


class MythQuantApplication:
    """MythQuant 核心应用程序类"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化应用程序
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.smart_logger = get_smart_logger("Application")
        self.error_handler = get_error_handler()

        # 初始化流程优化器
        self.flow_optimizer = ProcessFlowOptimizer()

        # 初始化核心组件
        self._initialize_components()
        
        # 执行统计
        self.execution_stats = {
            'start_time': None,
            'end_time': None,
            'tasks_executed': 0,
            'tasks_successful': 0,
            'total_stocks_processed': 0
        }
    
    def _initialize_components(self):
        """初始化核心组件"""
        try:
            # 初始化股票数据处理器
            tdx_path = self.config_manager.get_tdx_path()
            self.stock_processor = StockDataProcessor(tdx_path)

            # 初始化任务管理器
            self.task_manager = TaskManager(self.stock_processor)

            # 初始化数据质量稽核器 (临时屏蔽 2025-07-28)
            # self.data_quality_auditor = DataQualityAuditor()
            self.data_quality_auditor = None  # 临时屏蔽稽核功能

            self.smart_logger.info("核心组件初始化完成")
            
        except Exception as e:
            self.smart_logger.error(f"组件初始化详细错误: {e}")
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.SYSTEM,
                operation="组件初始化"
            )
            raise RuntimeError(f"组件初始化失败 [错误ID: {error_id}]: {e}") from e
    
    def display_system_overview(self):
        """显示系统概览"""
        try:
            print("📊 系统概览:")
            print("-" * 40)
            
            # 显示配置信息
            config_info = self.config_manager.get_system_info()
            print(f"📁 TDX路径: {config_info.get('tdx_path', 'Unknown')}")
            print(f"🎯 目标股票数: {config_info.get('target_stocks_count', 0)} 只")
            print(f"📋 输出目录: {config_info.get('output_dir', 'Unknown')}")
            
            # 显示处理器状态
            processor_info = self.stock_processor.get_status_info()
            print(f"💾 缓存状态: {processor_info.get('cache_status', 'Unknown')}")
            print(f"🔧 优化模块: {processor_info.get('optimization_modules', 'Unknown')}")
            
            # 显示任务配置
            task_info = self.task_manager.get_task_overview()
            print(f"📋 配置任务数: {task_info.get('total_tasks', 0)}")
            print(f"✅ 启用任务数: {task_info.get('enabled_tasks', 0)}")
            
            print("-" * 40)
            
        except Exception as e:
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.SYSTEM,
                operation="系统概览显示"
            )
            print(f"⚠️ 系统概览显示失败 [错误ID: {error_id}]")
    
    def run_all_tasks(self) -> bool:
        """
        执行所有任务

        Returns:
            是否全部成功
        """
        self.execution_stats['start_time'] = datetime.now()

        try:
            self.smart_logger.info("开始执行所有任务")

            # 使用流程优化器管理任务执行流程
            self.flow_optimizer.start_main_process("任务执行管理")

            # 获取任务列表
            tasks = self.task_manager.get_enabled_tasks()

            if not tasks:
                self.flow_optimizer.show_error_with_fallback("没有启用的任务", "请检查配置文件")
                return False
            
            # 使用流程优化器显示任务概览
            self.flow_optimizer.show_data_calculation(f"将执行任务数", f"{len(tasks)} 个任务")

            # 执行任务
            all_success = True
            output_dir = self._get_output_directory()

            for i, task in enumerate(tasks, 1):
                # Application层显示任务级别的信息，Infrastructure层显示股票级别的信息
                self.flow_optimizer.start_sub_process(f"执行任务: {task.name}", i, len(tasks))

                # 任务执行前的数据质量稽核 (临时屏蔽 2025-07-28)
                # 使用流程优化器抑制技术细节
                # self.flow_optimizer.suppress_technical_details("数据质量稽核")  # 违反workflow规范，已屏蔽

                task_start_time = time.time()

                try:
                    success = self.task_manager.execute_task(task)
                    task_duration = time.time() - task_start_time

                    self.execution_stats['tasks_executed'] += 1

                    if success:
                        self.execution_stats['tasks_successful'] += 1
                        print_result(f"任务完成: {task.name} ({task_duration:.2f}秒)", True, level=1)

                        # 任务执行后的数据质量稽核 (临时屏蔽 2025-07-28)
                        # post_audit_result = self.data_quality_auditor.audit_after_task_execution(task.name, output_dir)
                        # if post_audit_result.get('poor_count', 0) > 0:
                        #     print_warning(f"执行后稽核发现 {post_audit_result['poor_count']} 个文件质量问题", level=1)
                        #     # 质量问题不影响任务成功状态，但会记录警告
                        # elif post_audit_result.get('files_count', 0) > 0:
                        #     print_info(f"数据质量稽核: {post_audit_result.get('excellent_count', 0)} 优秀, {post_audit_result.get('good_count', 0)} 良好", level=1)
                    else:
                        all_success = False
                        print_result(f"任务失败: {task.name} ({task_duration:.2f}秒)", False, level=1)

                except Exception as e:
                    all_success = False
                    task_duration = time.time() - task_start_time

                    error_id = self.error_handler.log_error(
                        error=e,
                        category=ErrorCategory.BUSINESS,
                        context={'task_name': task.name},
                        operation="任务执行"
                    )

                    print_error(f"任务异常: {task.name} [错误ID: {error_id}] ({task_duration:.2f}秒)", level=1)
                    self.smart_logger.error(f"任务执行异常: {task.name} [错误ID: {error_id}]")
            
            self.execution_stats['end_time'] = datetime.now()
            
            # 最终数据质量稽核汇总 (临时屏蔽 2025-07-28)
            # print(f"\n📊 执行最终数据质量稽核汇总...")
            # final_audit_result = self.data_quality_auditor.audit_final_summary(output_dir)
            # if final_audit_result.get('status') == 'completed':
            #     total_files = final_audit_result.get('total_files', 0)
            #     excellent_files = final_audit_result.get('excellent_files', 0)
            #     poor_files = final_audit_result.get('poor_files', 0)
            #     avg_completeness = final_audit_result.get('avg_completeness', 0)
            #
            #     print(f"📈 数据质量汇总: {total_files} 个文件, 平均完整率 {avg_completeness:.1f}%")
            #     if poor_files > 0:
            #         print(f"⚠️ {poor_files} 个文件需要关注 (完整率<85%)")

            print(f"\n📊 稽核功能已临时屏蔽，跳过数据质量检查")

            # 记录执行结果
            success_rate = (self.execution_stats['tasks_successful'] /
                          self.execution_stats['tasks_executed'] * 100) if self.execution_stats['tasks_executed'] > 0 else 0

            self.smart_logger.info(f"任务执行完成 - 成功率: {success_rate:.1f}%")

            return all_success
            
        except Exception as e:
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.SYSTEM,
                operation="任务执行流程"
            )
            print(f"❌ 任务执行流程失败 [错误ID: {error_id}]")
            return False
    
    def display_final_statistics(self):
        """显示最终统计信息"""
        try:
            print("\n📊 执行统计:")
            print("-" * 40)
            
            # 基本统计
            # 构建统计数据
            stats = {
                "执行任务数": self.execution_stats['tasks_executed'],
                "成功任务数": self.execution_stats['tasks_successful']
            }

            if self.execution_stats['tasks_executed'] > 0:
                success_rate = (self.execution_stats['tasks_successful'] /
                              self.execution_stats['tasks_executed'] * 100)
                stats["成功率"] = f"{success_rate:.1f}%"

            # 时间统计
            if self.execution_stats['start_time'] and self.execution_stats['end_time']:
                duration = (self.execution_stats['end_time'] -
                          self.execution_stats['start_time']).total_seconds()
                stats["任务执行时间"] = f"{duration:.2f} 秒"

            print_stats_table("任务执行统计", stats)
            
            # 错误统计
            error_stats = self.error_handler.get_error_statistics()
            if error_stats['total_errors'] > 0:
                print_warning(f"错误数量: {error_stats['total_errors']}")

                # 导出错误报告
                report_file = f"logs/error_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
                self.error_handler.export_error_report(report_file)
                print_info(f"错误报告: {report_file}")
            else:
                print_result("无错误发生", True)

            # 性能统计
            performance_stats = self.error_handler.get_performance_statistics()
            if performance_stats:
                perf_stats = {}
                for operation, stats in list(performance_stats.items())[:3]:  # 只显示前3个
                    perf_stats[operation] = f"平均 {stats['avg_time']:.2f}秒"
                print_stats_table("性能统计", perf_stats)
            
        except Exception as e:
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.SYSTEM,
                operation="统计信息显示"
            )
            print(f"⚠️ 统计信息显示失败 [错误ID: {error_id}]")
    
    def cleanup(self):
        """清理资源"""
        try:
            self.smart_logger.info("开始清理应用程序资源")
            
            # 清理股票处理器
            if hasattr(self.stock_processor, 'cleanup'):
                self.stock_processor.cleanup()
            
            # 清理任务管理器
            if hasattr(self.task_manager, 'cleanup'):
                self.task_manager.cleanup()
            
            self.smart_logger.info("应用程序资源清理完成")
            
        except Exception as e:
            # 清理过程中的错误不应该影响程序退出
            self.smart_logger.error(f"资源清理过程中发生错误: {e}")
    
    def get_execution_summary(self) -> Dict[str, Any]:
        """获取执行摘要"""
        return {
            'execution_stats': self.execution_stats.copy(),
            'error_stats': self.error_handler.get_error_statistics(),
            'performance_stats': self.error_handler.get_performance_statistics()
        }

    def _get_output_directory(self) -> str:
        """获取输出目录"""
        try:
            # 尝试从配置获取输出目录
            import user_config
            output_config = getattr(user_config, 'output_storage', {})
            base_dir = output_config.get('base_directory', 'H:/MPV1.17/T0002/signals')
            return base_dir
        except Exception:
            # 使用默认输出目录
            return 'H:/MPV1.17/T0002/signals'
