#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试count_trading_days_to_now()函数对20250704的计算
验证是否正确计算交易日数量，以及pytdx下载逻辑是否合理
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append('.')

def test_trading_days_calculation():
    """测试交易日计算功能"""
    print("🧪 测试交易日计算功能")
    print("=" * 80)
    
    try:
        from utils.trading_days_calculator import count_trading_days_to_now, trading_days_calculator
        
        # 测试20250704到现在的交易日数量
        start_date = '20250704'
        current_date = datetime.now().strftime('%Y%m%d')
        
        print(f"📅 测试日期范围: {start_date} 到 {current_date}")
        
        # 使用便捷函数
        trading_days_simple = count_trading_days_to_now(start_date)
        
        # 使用类方法
        trading_days_class = trading_days_calculator.count_trading_days_to_now(start_date)
        
        # 使用详细方法获取交易日列表
        trading_days_list = trading_days_calculator.get_trading_days_list(start_date, current_date)
        
        print(f"\n📊 计算结果:")
        print(f"   便捷函数结果: {trading_days_simple} 个交易日")
        print(f"   类方法结果: {trading_days_class} 个交易日")
        print(f"   交易日列表长度: {len(trading_days_list)} 个交易日")
        
        # 显示交易日列表的前几个和后几个
        if trading_days_list:
            print(f"\n📋 交易日列表详情:")
            print(f"   开始几天: {trading_days_list[:5]}")
            print(f"   结束几天: {trading_days_list[-5:]}")
            print(f"   总计: {len(trading_days_list)} 个交易日")
        
        # 验证是否超过100个交易日
        print(f"\n⚠️ pytdx限制检查:")
        if trading_days_simple > 100:
            print(f"   ❌ 超过pytdx限制: {trading_days_simple} > 100")
            print(f"   💡 但这不应该阻止下载尝试，应该给出友好提示")
        else:
            print(f"   ✅ 在pytdx限制内: {trading_days_simple} <= 100")
        
        # 计算实际的日历天数
        start_dt = datetime.strptime(start_date, '%Y%m%d')
        current_dt = datetime.now()
        calendar_days = (current_dt - start_dt).days + 1
        
        print(f"\n📈 统计分析:")
        print(f"   日历天数: {calendar_days} 天")
        print(f"   交易日数: {trading_days_simple} 天")
        print(f"   交易日比例: {trading_days_simple/calendar_days:.2%}")
        
        return trading_days_simple
        
    except Exception as e:
        print(f"❌ 交易日计算测试失败: {e}")
        import traceback
        traceback.print_exc()
        return None


def test_pytdx_download_logic():
    """测试pytdx下载逻辑"""
    print("\n🧪 测试pytdx下载逻辑")
    print("=" * 80)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        # 创建下载器实例
        downloader = PytdxDownloader()
        
        # 测试智能数据量计算
        start_date = '20250704'
        frequency = '1min'
        
        print(f"📊 测试智能数据量计算:")
        print(f"   开始日期: {start_date}")
        print(f"   数据频率: {frequency}")
        
        # 调用智能计算方法
        estimated_count = downloader._calculate_smart_data_count(start_date, frequency)
        
        print(f"\n📈 计算结果:")
        print(f"   预估数据量: {estimated_count} 条")
        print(f"   预估交易日: {estimated_count // 240} 天")
        
        # 测试实际下载（不保存，只测试逻辑）
        print(f"\n🔄 测试下载逻辑（不实际下载）:")
        
        # 检查是否会因为超过100个交易日而直接拒绝
        trading_days = estimated_count // 240
        if trading_days > 100:
            print(f"   ⚠️ 预估需要 {trading_days} 个交易日的数据")
            print(f"   ⚠️ 超过pytdx的100个交易日限制")
            print(f"   💡 建议：应该尝试下载并给出友好提示，而不是直接拒绝")
        else:
            print(f"   ✅ 预估需要 {trading_days} 个交易日的数据，在pytdx限制内")
        
        return True
        
    except Exception as e:
        print(f"❌ pytdx下载逻辑测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_specific_minute_data():
    """测试特定分钟数据获取"""
    print("\n🧪 测试特定分钟数据获取")
    print("=" * 80)
    
    try:
        from test_environments.shared.utilities.specific_minute_data_fetcher import get_specific_minute_data
        
        target_datetime = '202507041447'
        stock_code = '000617'
        
        print(f"🎯 测试获取特定时间数据:")
        print(f"   股票代码: {stock_code}")
        print(f"   目标时间: {target_datetime}")
        
        # 尝试获取数据
        data = get_specific_minute_data(stock_code, target_datetime)
        
        if data:
            print(f"   ✅ 成功获取数据:")
            print(f"      收盘价: {data.get('close', 'N/A')}")
            print(f"      前复权收盘价: {data.get('close_qfq', 'N/A')}")
            print(f"      成交量: {data.get('volume', 'N/A')}")
        else:
            print(f"   ❌ 未获取到数据")
            print(f"   💡 可能原因:")
            print(f"      1. 超出pytdx覆盖范围（最近100个交易日）")
            print(f"      2. 非交易时间（14:47可能是交易时间）")
            print(f"      3. 数据源连接问题")
            
            # 检查20250704是否在最近100个交易日内
            from utils.trading_days_calculator import trading_days_calculator
            trading_days = trading_days_calculator.count_trading_days_to_now('20250704')
            print(f"      4. 实际交易日数: {trading_days} 天")
            
            if trading_days <= 100:
                print(f"      ⚠️ 在pytdx覆盖范围内，但仍未获取到数据")
                print(f"      💡 建议检查pytdx服务器连接或数据可用性")
            else:
                print(f"      ✅ 超出pytdx覆盖范围，这是正常的")
        
        return data is not None
        
    except Exception as e:
        print(f"❌ 特定分钟数据测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_download_strategy():
    """分析下载策略建议"""
    print("\n🧪 分析下载策略建议")
    print("=" * 80)
    
    try:
        from utils.trading_days_calculator import count_trading_days_to_now
        
        start_date = '20250704'
        trading_days = count_trading_days_to_now(start_date)
        
        print(f"📊 下载策略分析:")
        print(f"   目标日期: {start_date}")
        print(f"   交易日数: {trading_days} 天")
        print(f"   pytdx限制: 100个交易日")
        
        if trading_days <= 100:
            print(f"\n✅ 推荐策略: 使用pytdx")
            print(f"   理由: 在覆盖范围内，速度快，数据质量好")
            print(f"   操作: 直接使用pytdx下载")
        else:
            print(f"\n⚠️ 推荐策略: 混合下载")
            print(f"   理由: 超出pytdx覆盖范围")
            print(f"   操作建议:")
            print(f"      1. 先尝试pytdx下载（获取最近100个交易日）")
            print(f"      2. 给出友好提示：数据可能不完整")
            print(f"      3. 建议使用互联网数据源补充历史数据")
            print(f"      4. 不应该直接拒绝下载请求")
        
        print(f"\n💡 改进建议:")
        print(f"   1. 即使超过100个交易日，也应该尝试下载")
        print(f"   2. 下载后检查实际覆盖范围")
        print(f"   3. 给出明确的数据覆盖情况说明")
        print(f"   4. 提供替代数据源的建议")
        
        return True
        
    except Exception as e:
        print(f"❌ 下载策略分析失败: {e}")
        return False


def main():
    """主函数"""
    print("🚀 count_trading_days_to_now()函数验证测试")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    
    # 执行测试
    results = {}
    
    # 1. 测试交易日计算
    trading_days = test_trading_days_calculation()
    results['trading_days_calculation'] = trading_days is not None
    
    # 2. 测试pytdx下载逻辑
    results['pytdx_download_logic'] = test_pytdx_download_logic()
    
    # 3. 测试特定分钟数据获取
    results['specific_minute_data'] = test_specific_minute_data()
    
    # 4. 分析下载策略
    results['download_strategy'] = analyze_download_strategy()
    
    # 生成测试报告
    print(f"\n📊 测试结果汇总")
    print("=" * 100)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    # 关键发现
    print(f"\n🎯 关键发现:")
    if trading_days is not None:
        print(f"   1. 从20250704到现在有 {trading_days} 个交易日")
        if trading_days <= 100:
            print(f"   2. ✅ 在pytdx覆盖范围内，应该可以正常下载")
        else:
            print(f"   2. ⚠️ 超出pytdx覆盖范围，但不应该直接拒绝下载")
    
    print(f"\n💡 建议改进:")
    print(f"   1. 即使超过100个交易日限制，也应该尝试下载")
    print(f"   2. 下载后检查实际数据覆盖范围")
    print(f"   3. 给出友好的提示信息，而不是直接拒绝")
    print(f"   4. 提供替代数据源的建议")
    
    return 0


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
