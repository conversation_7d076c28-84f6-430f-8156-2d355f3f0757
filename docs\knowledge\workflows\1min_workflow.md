# 1分钟数据处理工作流程

> **文档版本**: 5.0  
> **最后更新**: 2025-08-09  
> **适用范围**: MythQuant 1分钟数据增量下载和处理

---

## 📋 流程概览

本工作流程包含6个核心步骤，实现1分钟数据的智能选择、增量下载、质量修复和文件生成：

1. **智能文件选择** - 自动选择最优的现有数据文件
2. **增量下载前提判断** - 验证是否具备增量下载条件
3. **数据质量检查与修复** - 检测并修复缺失数据
4. **增量数据下载** - 执行增量数据获取和合并
5. **文件生成和命名** - 生成标准格式的输出文件
6. **测试验证和确认** - 验证数据质量和功能正确性

---

## 🔍 第1步：智能文件选择

### 核心功能
自动识别和选择最适合进行增量更新的现有数据文件。

### 最优调用方案
```python
from utils.smart_file_selector import SmartFileSelector

# 创建选择器实例
selector = SmartFileSelector(output_dir)

# 执行智能文件选择（推荐使用智能综合策略）
existing_file = selector.find_best_file(
    stock_code='000617',
    data_type="minute",
    target_start='20250401',
    target_end='20250731',
    strategy='smart_comprehensive'
)
```

### 调用链详解
```
SmartFileSelector.find_best_file()
├── _scan_directory() - 扫描目录获取候选文件
├── _parse_file_info() - 解析文件名和元数据
├── _evaluate_file_quality() - 评估文件质量评分
├── _calculate_coverage_score() - 计算时间覆盖度评分
└── _select_optimal_file() - 基于综合评分选择最优文件
```

### 返回结果
- **成功**: 返回最优文件的完整路径
- **失败**: 返回 `None`，表示无合适文件，需要全量下载

### 输出示例
```
🔍 [1/6] 智能文件选择
   📁 扫描目录: H:/MPV1.17/T0002/signals
   📊 发现候选文件: 3个
   ✅ 最优文件: 1min_0_000617_20250320-20250704_来源互联网.txt
   📈 质量评分: 95.2/100
   📅 覆盖范围: 2025-03-20 至 2025-07-04
```

---

## ⚖️ 第2步：增量下载前提条件判断

### 核心功能
通过比较文件最后记录与API数据的价格一致性，判断是否具备增量下载条件。

### 最优调用方案
```python
from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader

# 创建下载器实例
downloader = StructuredInternetMinuteDownloader()

# 执行增量下载前提条件检查（推荐方法）
has_prerequisite, details = downloader.check_incremental_download_prerequisite(
    existing_file='1min_0_000617_20250320-20250704_来源互联网.txt',
    stock_code='000617'
)

# 处理结果
if has_prerequisite:
    print("✅ 具备增量下载条件，继续后续步骤")
else:
    print(f"❌ 不具备增量下载条件: {details.get('conclusion', '未知原因')}")
    print("💡 将执行全量下载，跳过后续步骤")
```

### 调用链详解
```
StructuredInternetMinuteDownloader.check_incremental_download_prerequisite()
├── UnifiedDataDownloader.check_incremental_download_prerequisite()
│   ├── _resolve_file_path() - 解析文件路径（自动适配测试/生产环境）
│   └── TestFileApiComparator.compare_last_record_close_price()
│       ├── _extract_last_record_from_file() - 提取文件最后记录
│       │   ├── 读取文件最后一行
│       │   ├── 解析时间字段（YYYYMMDDHHMM格式）
│       │   └── 提取未复权收盘价（当日收盘价C字段）
│       ├── get_specific_minute_data() - 获取API数据
│       │   └── PytdxDownloader.get_specific_minute_data()
│       └── _compare_prices() - 价格比较（使用Decimal精确计算）
└── 返回(是否具备条件, 详细信息字典)
```

### 核心逻辑
1. **文件数据提取**: 读取目标文件最后一行，提取时间和未复权收盘价
2. **API数据获取**: 通过pytdx获取相同时间点的未复权收盘价
3. **精确比较**: 使用容差0.001元进行价格比较
4. **结果判断**: 价格一致则具备增量下载条件

### 数据源限制
- **pytdx覆盖范围**: 仅提供最近约100个交易日的分钟数据
- **时间限制**: 超出范围的历史数据无法获取
- **自动处理**: 系统会自动检测并给出相应提示

### 环境自动适配
- **生产环境**: 自动解析为生产路径 `H:\MPV1.17\T0002\signals\`
- **测试环境**: 自动解析为测试路径 `test_environments/minute_data_tests/input_data/`
- **逻辑一致**: 测试和生产环境使用完全相同的比较逻辑

### 输出示例

#### 价格一致情况
```
🔍 [2/6] 增量下载前提条件判断
   📋 文件最后记录: 2025-07-04 14:47, 未复权收盘价=7.55
   🌐 API获取数据: 2025-07-04 14:47, 未复权收盘价=7.55
   ✅ 价格完全一致 (差异: 0.000, 容差: 0.001)
   💡 具备增量下载条件，继续后续步骤
```

#### 价格不一致情况
```
🔍 [2/6] 增量下载前提条件判断
   📋 文件最后记录: 2025-07-04 14:47, 未复权收盘价=7.55
   🌐 API获取数据: 2025-07-04 14:47, 未复权收盘价=7.45
   ❌ 价格不一致 (差异: 0.10, 容差: 0.001)
   💡 不具备增量下载条件，执行全量下载，跳过后续步骤
```

---

## 🔧 第3步：数据质量检查与修复

### 核心功能
检测现有数据文件的完整性，识别并修复缺失的分钟数据。

### 技术要求

#### 1. 分钟级精确稽核
- **稽核粒度**: 对每个交易日进行分钟级别的完整性检查
- **A股1分钟数据标准**:
  - 上午时段：09:31-11:30 (120分钟，第一条数据是09:31)
  - 下午时段：13:01-15:00 (120分钟，第一条数据是13:01)
  - **总计240分钟/交易日**
- **重要说明**: 1分钟数据的时间戳表示该分钟结束时的数据，因此：
  - 09:31表示09:30-09:31这一分钟的数据
  - 13:01表示13:00-13:01这一分钟的数据
- **完整性判断标准**: 每个交易日数据行数 = 240行即为完整
- **缺失识别**: 识别完全缺失的交易日和部分缺失的分钟数据
- **数据结构**: 维护精确到分钟级别的缺失时间段信息

#### 2. 智能修复机制
- **修复策略**: 基于缺失数据结构进行精准修复
- **数据源**: 使用pytdx接口从通达信官方获取缺失数据
- **插入方式**: 采用精准插入算法，保持原文件结构和时间序列完整性
- **原地修复**: 在原始文件基础上进行修复，避免重新生成整个文件

#### 3. 缺失数据结构定义
```python
missing_data_structure = {
    'missing_periods': [
        {
            'start_time': '202503201031',  # A股实际开始时间：09:31
            'end_time': '202503201130',    # 上午结束时间：11:30
            'missing_count': 60,           # 缺失分钟数
            'period_type': 'continuous'    # 连续缺失
        },
        {
            'start_time': '202507041301',  # A股中午开始时间：13:01
            'end_time': '202507041312',    # 部分缺失结束时间
            'missing_count': 12,           # 实际缺失分钟数
            'period_type': 'partial'       # 部分缺失
        }
    ],
    'total_missing_minutes': 72,              # 修正后的总缺失数
    'affected_trading_days': 2,
    'completeness_before': 98.5,
    'expected_completeness_after': 100.0,
    'data_format_note': 'A股1分钟数据时间戳表示分钟结束时间，09:31表示09:30-09:31的数据'
}
```

### 最优调用方案（基于pytdx API特性优化）
```python
from utils.missing_data_processor import MissingDataProcessor
from utils.pytdx_data_repairer import PytdxDataRepairer

# 创建缺失数据处理器实例
processor = MissingDataProcessor()

# 第一阶段：分钟级精确稽核
missing_structure = processor.analyze_minute_level_completeness(
    file_path='1min_0_000617_20250320-20250704_来源互联网.txt',
    stock_code='000617'
)

# 第二阶段：基于pytdx特性的智能修复（如果发现缺失）

#### 4. Pytdx API约束与优化策略

**核心约束**: pytdx API采用**单次全量下载**模式，不支持多段分别下载

**API特性分析**:
- **时间粒度限制**: API入参可能不支持精确到分钟级别的时间范围
- **下载范围策略**: 必须从最早缺失时间点开始，下载到当前时间的全量数据
- **数据完整性保证**: 单次下载包含所有中间时间段的数据，避免拼接问题

**优化策略设计**:
```python
# 错误的多次下载方式（不符合pytdx API特性）
for missing_date in ['20250701', '20250715', '20250720']:
    data = pytdx_api.download(stock_code, start_date=missing_date, end_date='now')
    # 多次API调用，效率低且可能失败

# 正确的单次全量下载方式（符合pytdx API特性）
earliest_missing_date = min(['20250701', '20250715', '20250720'])  # 20250701
full_data = pytdx_api.download(stock_code, start_date=earliest_missing_date, end_date='now')
# 单次API调用，获取完整数据，包含所有缺失时间段
```

**业务规则适配**:
1. **最早时间点识别**: 从所有缺失时间段中找到最早的时间点
2. **全量数据下载**: 从最早时间点下载到当前时间的完整数据
3. **智能数据筛选**: 从全量数据中提取实际需要的缺失时间段数据
4. **增量合并策略**: 将筛选后的数据与现有文件进行智能合并

if missing_structure['total_missing_minutes'] > 0:
    repairer = PytdxDataRepairer()

    # 关键优化：基于pytdx API特性计算下载策略
    download_strategy = repairer.calculate_optimal_download_strategy(missing_structure)
    print(f"📊 最早缺失时间: {download_strategy['earliest_missing_time']}")
    print(f"🌐 下载策略: 单次全量下载（{download_strategy['download_range']}）")
    print(f"⚡ 效率提升: 避免{download_strategy['avoided_api_calls']}次重复API调用")

    # 执行基于pytdx特性优化的修复
    repair_result = repairer.repair_missing_data_with_full_download(
        file_path='1min_0_000617_20250320-20250704_来源互联网.txt',
        missing_structure=missing_structure,
        stock_code='000617',
        download_strategy=download_strategy
    )

    # 处理修复结果
    if repair_result['success']:
        print(f"✅ 数据修复成功: {repair_result['repaired_count']} 条记录")
        print(f"📈 完整性提升: {repair_result['completeness_improvement']}")
        print(f"🌐 下载效率: 单次API调用，获取 {repair_result['downloaded_total']} 条数据")
        print(f"🎯 数据利用率: {repair_result['data_utilization_rate']:.1f}%")
    else:
        print(f"❌ 数据修复失败: {repair_result['error']}")
else:
    print("✅ 数据完整，无需修复")
```

### 调用链详解

#### 阶段一：分钟级精确稽核
```
MissingDataProcessor.analyze_minute_level_completeness()
├── _load_existing_data() - 加载现有数据文件
├── _generate_trading_timeline() - 生成标准交易时间线
│   ├── _get_trading_days() - 获取交易日列表
│   ├── _generate_minute_sequence() - 生成分钟序列 (9:30-11:30, 13:00-15:00)
│   └── _create_expected_timeline() - 创建完整的预期时间线
├── _perform_minute_level_audit() - 执行分钟级稽核
│   ├── _compare_actual_vs_expected() - 对比实际数据与预期时间线
│   ├── _identify_missing_minutes() - 识别缺失的具体分钟
│   └── _categorize_missing_periods() - 分类缺失时间段
└── _build_missing_data_structure() - 构建缺失数据结构
    ├── _calculate_missing_statistics() - 计算缺失统计信息
    ├── _format_missing_periods() - 格式化缺失时间段
    └── _generate_completeness_metrics() - 生成完整性指标
```

#### 阶段二：基于pytdx特性的智能修复机制
```
PytdxDataRepairer.repair_missing_data_with_full_download()
├── calculate_optimal_download_strategy() - 计算最优下载策略
│   ├── _identify_earliest_missing_time() - 识别最早缺失时间点
│   ├── _calculate_download_range() - 计算单次下载范围
│   ├── _estimate_efficiency_gain() - 评估效率提升
│   └── _validate_pytdx_api_constraints() - 验证API约束条件
├── _download_full_data_from_earliest() - 从最早时间点全量下载
│   ├── _connect_pytdx_api() - 连接pytdx接口
│   ├── _single_api_call_download() - 单次API调用下载全量数据
│   ├── _validate_downloaded_completeness() - 验证下载数据完整性
│   └── _calculate_data_utilization_metrics() - 计算数据利用率指标
├── _extract_missing_periods_from_full_data() - 从全量数据中提取缺失时间段
│   ├── _filter_by_missing_time_ranges() - 按缺失时间范围筛选
│   ├── _remove_duplicate_records() - 移除重复记录
│   └── _maintain_data_quality() - 维护数据质量
├── _perform_intelligent_merge() - 执行智能合并
│   ├── _backup_original_file() - 备份原始文件
│   ├── _merge_with_existing_data() - 与现有数据合并
│   ├── _maintain_chronological_order() - 维护时间序列顺序
│   └── _remove_overlapping_records() - 移除重叠记录
└── _verify_repair_result_with_efficiency_metrics() - 验证修复结果和效率指标
    ├── _recheck_completeness() - 重新检查完整性
    ├── _validate_data_integrity() - 验证数据完整性
    ├── _calculate_efficiency_metrics() - 计算效率指标
    └── _generate_comprehensive_repair_report() - 生成综合修复报告
```

### 输出示例

#### 标准输出格式
```
🔍 [3/6] 数据质量检查与修复
   📊 分钟级精确稽核: 检测到2个缺失时间段
   🔍 缺失时间段详情:
      • 2025-03-20 10:30-11:30 (缺失60分钟)
      • 2025-07-04 14:00-14:12 (缺失13分钟)
   📈 完整性评估: 当前98.5% (缺失73分钟/总计4800分钟)
   🔧 智能修复执行:
      • pytdx数据获取: 成功获取73条缺失记录
      • 精准插入操作: 完成2个时间段的数据插入
      • 时间序列验证: 通过完整性检查
   ✅ 修复完成: 共补充73条记录
   📈 数据完整性提升: 98.5% → 100%
```

#### 无缺失情况输出
```
🔍 [3/6] 数据质量检查与修复
   📊 分钟级精确稽核: 数据完整，无缺失时间段
   📈 完整性评估: 当前100% (4800/4800分钟)
   ✅ 数据质量良好: 无需修复操作
```

#### 修复失败情况输出
```
🔍 [3/6] 数据质量检查与修复
   📊 分钟级精确稽核: 检测到1个缺失时间段
   🔍 缺失时间段详情:
      • 2025-03-20 10:30-11:30 (缺失60分钟)
   📈 完整性评估: 当前97.5% (缺失60分钟/总计4800分钟)
   🔧 智能修复执行:
      • pytdx数据获取: 连接失败，无法获取数据
   ❌ 修复失败: pytdx接口连接超时
   💡 建议: 检查网络连接或稍后重试
```

---

## 📥 第4步：增量数据下载

### 核心功能
基于现有数据文件，下载最新的增量数据并进行合并处理。

### 最优调用方案
```python
from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader

# 创建结构化下载器实例
downloader = StructuredInternetMinuteDownloader()

# 执行增量数据下载
download_result = downloader.execute_incremental_download(
    existing_file='1min_0_000617_20250320-20250704_来源互联网.txt',
    stock_code='000617',
    target_end_date='20250809'
)

# 处理下载结果
if download_result['success']:
    print(f"✅ 增量下载成功: {download_result['new_records']} 条新记录")
else:
    print(f"❌ 增量下载失败: {download_result['error']}")
```

### 调用链详解
```
StructuredInternetMinuteDownloader.execute_incremental_download()
├── _determine_incremental_range() - 确定增量下载范围
│   ├── _get_file_last_datetime() - 获取文件最后时间
│   └── _calculate_download_range() - 计算需要下载的时间范围
├── _download_incremental_data() - 下载增量数据
│   ├── PytdxDownloader.download_minute_data() - 调用pytdx下载
│   └── _validate_downloaded_data() - 验证下载数据质量
├── _merge_data() - 合并数据
│   ├── _load_existing_data() - 加载现有数据
│   ├── _append_new_data() - 追加新数据
│   └── _remove_duplicates() - 去除重复记录
└── _save_merged_data() - 保存合并后的数据
```

### 输出示例
```
🔍 [4/6] 增量数据下载
   📊 确定增量范围: 2025-07-05 至 2025-08-09
   🌐 pytdx数据下载: 获取1,200条新记录
   🔄 数据合并处理: 合并完成，无重复记录
   ✅ 增量下载成功: 文件已更新至2025-08-09
```

---

## 📄 第5步：文件生成和命名

### 核心功能
根据标准命名规则生成最终的输出文件，确保文件名反映实际数据范围。

### 最优调用方案
```python
from utils.file_generator import MinuteDataFileGenerator

# 创建文件生成器实例
generator = MinuteDataFileGenerator()

# 生成标准格式文件
output_file = generator.generate_standard_file(
    stock_code='000617',
    data_content=merged_data,
    source_description='来源互联网',
    timestamp_suffix=True
)

print(f"✅ 文件生成完成: {output_file}")
```

### 调用链详解
```
MinuteDataFileGenerator.generate_standard_file()
├── _analyze_data_range() - 分析数据时间范围
│   ├── _get_earliest_datetime() - 获取最早时间
│   └── _get_latest_datetime() - 获取最晚时间
├── _generate_filename() - 生成标准文件名
│   ├── _format_stock_code() - 格式化股票代码
│   ├── _format_date_range() - 格式化日期范围
│   └── _add_source_suffix() - 添加来源后缀
├── _format_file_content() - 格式化文件内容
│   ├── _generate_header() - 生成文件头
│   └── _format_data_rows() - 格式化数据行
└── _write_to_file() - 写入文件
```

### 文件命名规则
- **格式**: `1min_{市场编号}_{股票代码}_{开始日期}-{结束日期}_{来源描述}（时间戳）.txt`
- **示例**: `1min_0_000617_20250320-20250809_来源互联网（202508091530）.txt`
- **说明**: 日期范围基于文件内实际数据的起止时间

### 输出示例
```
🔍 [5/6] 文件生成和命名
   📊 数据范围分析: 2025-03-20 09:31 至 2025-08-09 15:00
   📝 生成文件名: 1min_0_000617_20250320-20250809_来源互联网（202508091530）.txt
   💾 文件写入完成: 25,680条记录
   ✅ 文件生成成功
```

---

## ✅ 第6步：测试验证和确认

### 核心功能
验证生成文件的数据质量、格式正确性和功能完整性。

### 最优调用方案
```python
from utils.data_quality_validator import DataQualityValidator

# 创建数据质量验证器实例
validator = DataQualityValidator()

# 执行全面质量验证
validation_result = validator.validate_minute_data_file(
    file_path='1min_0_000617_20250320-20250809_来源互联网（202508091530）.txt',
    stock_code='000617'
)

# 处理验证结果
if validation_result['passed']:
    print("✅ 数据质量验证通过")
else:
    print(f"❌ 数据质量验证失败: {validation_result['issues']}")
```

### 调用链详解
```
DataQualityValidator.validate_minute_data_file()
├── _validate_file_format() - 验证文件格式
│   ├── _check_header_format() - 检查文件头格式
│   ├── _check_field_count() - 检查字段数量
│   └── _check_delimiter() - 检查分隔符
├── _validate_data_content() - 验证数据内容
│   ├── _check_time_continuity() - 检查时间连续性
│   ├── _check_price_reasonableness() - 检查价格合理性
│   └── _check_data_completeness() - 检查数据完整性
├── _validate_filename() - 验证文件名
│   └── _check_naming_convention() - 检查命名规范
└── _generate_validation_report() - 生成验证报告
```

### 验证标准
1. **文件格式**: 正确的表头和字段分隔符
2. **数据完整性**: 每个交易日240条记录（9:30-11:30 + 13:00-15:00）
3. **时间连续性**: 分钟数据时间序列连续无断档
4. **价格合理性**: 前复权价格与当日价格的合理差异
5. **文件命名**: 符合标准命名规范

### 输出示例
```
🔍 [6/6] 测试验证和确认
   📋 文件格式验证: ✅ 通过
   📊 数据完整性验证: ✅ 通过 (107个交易日 × 240条/日 = 25,680条)
   ⏰ 时间连续性验证: ✅ 通过
   💰 价格合理性验证: ✅ 通过
   📝 文件命名验证: ✅ 通过
   🎉 全部验证通过，工作流程完成！
```

---

## 📊 工作流程总结

### 执行条件
- **增量更新**: 当价格一致性检查通过时，执行完整的6步流程
- **全量下载**: 当价格不一致时，跳过步骤3-6，直接执行全量数据下载

### 性能优化
- **智能文件选择**: 避免重复扫描，提高文件选择效率
- **增量下载**: 只下载新增数据，减少网络传输
- **数据缓存**: 合理使用缓存机制，提高处理速度

### 错误处理
- **自动重试**: 网络异常时自动重试下载
- **降级处理**: 增量下载失败时自动切换到全量下载
- **详细日志**: 记录每个步骤的详细执行信息

### 环境适配
- **测试环境**: 使用测试数据，确保测试结果可重现
- **生产环境**: 使用实际数据，确保数据准确性
- **自动切换**: 根据运行环境自动选择相应的配置和数据源
