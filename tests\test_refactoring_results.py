#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
重构成果测试脚本

验证新架构的配置系统、核心模块、包导入等功能的正确性。
"""

import sys
import os
import traceback
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def print_test_header(test_name):
    """打印测试标题"""
    print(f"\n{'='*60}")
    print(f"🧪 {test_name}")
    print(f"{'='*60}")

def print_test_result(test_name, success, details=None):
    """打印测试结果"""
    status = "✅ 通过" if success else "❌ 失败"
    print(f"{status} {test_name}")
    if details:
        print(f"   详情: {details}")

def test_project_structure():
    """测试项目结构"""
    print_test_header("项目结构测试")
    
    required_dirs = [
        "src/mythquant",
        "src/mythquant/config",
        "src/mythquant/core",
        "src/mythquant/data",
        "src/mythquant/data/sources",
        "src/mythquant/data/processors", 
        "src/mythquant/data/downloaders",
        "src/mythquant/io",
        "src/mythquant/io/readers",
        "src/mythquant/io/writers",
        "src/mythquant/algorithms",
        "src/mythquant/utils",
        "src/mythquant/ui",
        "src/mythquant/cache",
    ]
    
    required_files = [
        "pyproject.toml",
        "setup.py",
        "MANIFEST.in",
        ".pre-commit-config.yaml",
        "tox.ini",
        "src/mythquant/__init__.py",
        "src/mythquant/config/__init__.py",
        "src/mythquant/config/manager.py",
        "src/mythquant/config/user_settings.py",
        "src/mythquant/config/validators.py",
        "src/mythquant/core/__init__.py",
        "src/mythquant/core/application.py",
    ]
    
    missing_dirs = []
    missing_files = []
    
    # 检查目录
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
    
    # 检查文件
    for file_path in required_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
    
    success = len(missing_dirs) == 0 and len(missing_files) == 0
    
    if success:
        print_test_result("项目结构完整性", True, f"检查了{len(required_dirs)}个目录和{len(required_files)}个文件")
    else:
        details = []
        if missing_dirs:
            details.append(f"缺失目录: {missing_dirs}")
        if missing_files:
            details.append(f"缺失文件: {missing_files}")
        print_test_result("项目结构完整性", False, "; ".join(details))
    
    return success

def test_package_imports():
    """测试包导入"""
    print_test_header("包导入测试")
    
    import_tests = [
        ("mythquant", "主包导入"),
        ("mythquant.config", "配置模块导入"),
        ("mythquant.config.manager", "配置管理器导入"),
        ("mythquant.config.user_settings", "用户设置导入"),
        ("mythquant.config.validators", "配置验证器导入"),
        ("mythquant.core", "核心模块导入"),
        ("mythquant.core.application", "应用程序导入"),
        ("mythquant.data", "数据模块导入"),
        ("mythquant.io", "IO模块导入"),
        ("mythquant.algorithms", "算法模块导入"),
        ("mythquant.utils", "工具模块导入"),
        ("mythquant.ui", "UI模块导入"),
        ("mythquant.cache", "缓存模块导入"),
    ]
    
    success_count = 0
    total_count = len(import_tests)
    
    for module_name, test_desc in import_tests:
        try:
            __import__(module_name)
            print_test_result(test_desc, True)
            success_count += 1
        except ImportError as e:
            print_test_result(test_desc, False, str(e))
        except Exception as e:
            print_test_result(test_desc, False, f"意外错误: {e}")
    
    overall_success = success_count == total_count
    print(f"\n📊 导入测试统计: {success_count}/{total_count} 成功")
    
    return overall_success

def test_config_system():
    """测试配置系统"""
    print_test_header("配置系统测试")
    
    try:
        # 测试配置管理器
        from mythquant.config.manager import ConfigManager, config_manager
        print_test_result("配置管理器导入", True)
        
        # 测试配置管理器实例化
        cm = ConfigManager()
        print_test_result("配置管理器实例化", True)
        
        # 测试基本配置访问
        debug_mode = cm.is_debug_enabled()
        print_test_result("调试模式配置访问", True, f"debug={debug_mode}")
        
        # 测试TDX配置访问
        tdx_path = cm.get_tdx_path()
        print_test_result("TDX路径配置访问", True, f"path={tdx_path}")
        
        # 测试配置验证器
        from mythquant.config.validators import ConfigValidator, config_validator
        print_test_result("配置验证器导入", True)
        
        # 测试配置验证
        validator = ConfigValidator()
        is_valid, errors, warnings = validator.validate_config(cm._config_cache)
        print_test_result("配置验证", True, f"有效={is_valid}, 错误={len(errors)}, 警告={len(warnings)}")
        
        # 测试用户设置
        from mythquant.config.user_settings import DEBUG, TDX_CONFIG, OUTPUT_CONFIG
        print_test_result("用户设置导入", True, f"DEBUG={DEBUG}")
        
        return True
        
    except Exception as e:
        print_test_result("配置系统测试", False, f"错误: {e}")
        traceback.print_exc()
        return False

def test_core_modules():
    """测试核心模块"""
    print_test_header("核心模块测试")
    
    try:
        # 测试核心模块导入
        from mythquant.core import MythQuantApplication, StockDataProcessor, TaskManager
        print_test_result("核心模块导入", True)
        
        # 测试配置管理器集成
        from mythquant.config.manager import ConfigManager
        config_manager = ConfigManager()
        print_test_result("配置管理器集成", True)
        
        # 测试应用程序实例化
        try:
            app = MythQuantApplication(config_manager)
            print_test_result("应用程序实例化", True)
            
            # 测试应用程序方法
            if hasattr(app, 'display_system_overview'):
                print_test_result("应用程序方法检查", True, "display_system_overview存在")
            else:
                print_test_result("应用程序方法检查", False, "display_system_overview不存在")
                
        except Exception as e:
            print_test_result("应用程序实例化", False, f"错误: {e}")
            return False
        
        return True
        
    except Exception as e:
        print_test_result("核心模块测试", False, f"错误: {e}")
        traceback.print_exc()
        return False

def test_version_info():
    """测试版本信息"""
    print_test_header("版本信息测试")
    
    try:
        import mythquant
        
        # 测试版本属性
        version_attrs = ['__version__', '__author__', '__email__', '__license__']
        for attr in version_attrs:
            if hasattr(mythquant, attr):
                value = getattr(mythquant, attr)
                print_test_result(f"版本属性 {attr}", True, f"{attr}={value}")
            else:
                print_test_result(f"版本属性 {attr}", False, "属性不存在")
        
        return True
        
    except Exception as e:
        print_test_result("版本信息测试", False, f"错误: {e}")
        return False

def test_project_configs():
    """测试项目配置文件"""
    print_test_header("项目配置文件测试")
    
    config_files = {
        "pyproject.toml": "现代Python项目配置",
        "setup.py": "包安装配置",
        "MANIFEST.in": "打包配置",
        ".pre-commit-config.yaml": "代码质量检查配置",
        "tox.ini": "多环境测试配置"
    }
    
    success_count = 0
    
    for file_path, description in config_files.items():
        if os.path.exists(file_path):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                    if len(content) > 0:
                        print_test_result(description, True, f"文件大小: {len(content)} 字符")
                        success_count += 1
                    else:
                        print_test_result(description, False, "文件为空")
            except Exception as e:
                print_test_result(description, False, f"读取错误: {e}")
        else:
            print_test_result(description, False, "文件不存在")
    
    return success_count == len(config_files)

def run_comprehensive_test():
    """运行综合测试"""
    print("🚀 MythQuant 重构成果综合测试")
    print(f"测试时间: {os.popen('date /t & time /t').read().strip()}")
    print(f"Python版本: {sys.version}")
    print(f"项目根目录: {project_root}")
    
    # 运行所有测试
    tests = [
        ("项目结构", test_project_structure),
        ("包导入", test_package_imports),
        ("配置系统", test_config_system),
        ("核心模块", test_core_modules),
        ("版本信息", test_version_info),
        ("项目配置文件", test_project_configs),
    ]
    
    results = {}
    
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ 测试 '{test_name}' 发生异常: {e}")
            traceback.print_exc()
            results[test_name] = False
    
    # 汇总结果
    print_test_header("测试结果汇总")
    
    passed_tests = sum(1 for success in results.values() if success)
    total_tests = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n📊 测试统计:")
    print(f"   通过: {passed_tests}/{total_tests} ({passed_tests/total_tests*100:.1f}%)")
    print(f"   失败: {total_tests - passed_tests}/{total_tests}")
    
    if passed_tests == total_tests:
        print(f"\n🎉 所有测试通过！新架构工作正常。")
        return True
    else:
        print(f"\n⚠️ 部分测试失败，需要修复问题后再继续重构。")
        return False

if __name__ == "__main__":
    success = run_comprehensive_test()
    sys.exit(0 if success else 1)
