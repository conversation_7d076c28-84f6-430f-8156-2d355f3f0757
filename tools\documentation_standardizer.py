#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档标准化工具

根据文档标准规范，对现有文档进行命名规范化和结构优化
"""

import os
import re
import shutil
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple


class DocumentationStandardizer:
    """文档标准化处理器"""
    
    def __init__(self, docs_root: str = "docs"):
        self.docs_root = Path(docs_root)
        self.backup_dir = Path("docs_standardization_backup")
        self.rename_map = {}
        self.issues = []
        
    def analyze_current_structure(self) -> Dict:
        """分析当前文档结构"""
        print("🔍 分析当前文档结构...")
        
        analysis = {
            'total_files': 0,
            'naming_issues': [],
            'structure_issues': [],
            'missing_metadata': [],
            'recommendations': []
        }
        
        for file_path in self.docs_root.rglob('*.md'):
            analysis['total_files'] += 1
            
            # 检查命名规范
            naming_issues = self._check_naming_convention(file_path)
            if naming_issues:
                analysis['naming_issues'].extend(naming_issues)
            
            # 检查文档元数据
            metadata_issues = self._check_metadata(file_path)
            if metadata_issues:
                analysis['missing_metadata'].extend(metadata_issues)
        
        return analysis
    
    def _check_naming_convention(self, file_path: Path) -> List[str]:
        """检查文件命名规范"""
        issues = []
        filename = file_path.name
        
        # 检查中文字符
        if re.search(r'[\u4e00-\u9fff]', filename):
            issues.append(f"包含中文字符: {file_path}")
        
        # 检查特殊字符
        if re.search(r'[^\w\-_.]', filename):
            issues.append(f"包含特殊字符: {file_path}")
        
        # 检查大写字母（除了特定文件）
        if filename not in ['README.md', 'CHANGELOG.md'] and re.search(r'[A-Z]', filename):
            issues.append(f"包含大写字母: {file_path}")
        
        # 检查过长文件名
        if len(filename) > 50:
            issues.append(f"文件名过长: {file_path}")
        
        return issues
    
    def _check_metadata(self, file_path: Path) -> List[str]:
        """检查文档元数据"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有YAML前置元数据
            if not content.startswith('---'):
                issues.append(f"缺少元数据: {file_path}")
            else:
                # 检查必要的元数据字段
                required_fields = ['title', 'version', 'date', 'status']
                for field in required_fields:
                    if f'{field}:' not in content[:500]:  # 检查前500字符
                        issues.append(f"缺少{field}字段: {file_path}")
        
        except Exception as e:
            issues.append(f"读取文件失败 {file_path}: {e}")
        
        return issues
    
    def generate_rename_suggestions(self) -> Dict[str, str]:
        """生成重命名建议"""
        print("💡 生成重命名建议...")
        
        rename_suggestions = {}
        
        # 定义重命名规则
        rename_rules = {
            # 中文文件名转换
            r'运行指南\.md$': 'operation_guide.md',
            r'精度配置使用指南\.md$': 'precision_config_guide.md',
            r'扩展GBBQ缓存系统实施指南\.md$': 'gbbq_cache_implementation_guide.md',
            r'详细计算显示控制说明\.md$': 'calculation_display_control_guide.md',
            
            # 特殊字符和格式问题
            r'AI_model_identification_request__(.+)\.md$': r'ai_model_identification_request_\1.md',
            r'PYTDX_使用指南\.md$': 'pytdx_usage_guide.md',
            
            # 报告文件标准化
            r'数据缺失透明化改进报告\.md$': 'data_transparency_improvement_report.md',
            r'文件整理完成报告\.md$': 'file_organization_completion_report.md',
            r'模块化拆分后续阶段规划\.md$': 'modularization_next_phase_plan.md',
            r'模块化重构集成报告\.md$': 'modularization_refactoring_report.md',
            r'第三阶段UI模块拆分报告\.md$': 'phase3_ui_module_split_report.md',
            r'第二阶段文件IO模块拆分报告\.md$': 'phase2_fileio_module_split_report.md',
            r'规则转换完成报告\.md$': 'rules_conversion_completion_report.md',
            r'规则转换实施完成报告\.md$': 'rules_conversion_implementation_report.md',
        }
        
        for file_path in self.docs_root.rglob('*.md'):
            filename = file_path.name
            
            for pattern, replacement in rename_rules.items():
                if re.search(pattern, filename):
                    new_name = re.sub(pattern, replacement, filename)
                    new_path = file_path.parent / new_name
                    rename_suggestions[str(file_path)] = str(new_path)
                    break
        
        return rename_suggestions
    
    def create_backup(self):
        """创建备份"""
        print("💾 创建文档备份...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"backup_{timestamp}"
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 复制整个docs目录
        shutil.copytree(self.docs_root, backup_path / "docs", dirs_exist_ok=True)
        
        print(f"   ✅ 备份已创建: {backup_path}")
        return backup_path
    
    def apply_renames(self, rename_suggestions: Dict[str, str], dry_run: bool = True):
        """应用重命名建议"""
        if dry_run:
            print("🔍 预览重命名操作（干运行模式）...")
        else:
            print("🚀 执行重命名操作...")
        
        success_count = 0
        error_count = 0
        
        for old_path, new_path in rename_suggestions.items():
            try:
                if dry_run:
                    print(f"   📝 {old_path} → {new_path}")
                else:
                    # 确保目标目录存在
                    Path(new_path).parent.mkdir(parents=True, exist_ok=True)
                    
                    # 执行重命名
                    shutil.move(old_path, new_path)
                    print(f"   ✅ {old_path} → {new_path}")
                
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ 重命名失败 {old_path}: {e}")
                error_count += 1
        
        print(f"\n📊 重命名统计:")
        print(f"   成功: {success_count}")
        print(f"   失败: {error_count}")
        
        return success_count, error_count
    
    def add_metadata_to_files(self, dry_run: bool = True):
        """为缺少元数据的文件添加标准元数据"""
        if dry_run:
            print("🔍 预览元数据添加操作（干运行模式）...")
        else:
            print("🚀 执行元数据添加操作...")
        
        success_count = 0
        error_count = 0
        
        for file_path in self.docs_root.rglob('*.md'):
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 检查是否已有元数据
                if content.startswith('---'):
                    continue
                
                # 生成标准元数据
                metadata = self._generate_metadata(file_path)
                new_content = f"{metadata}\n\n{content}"
                
                if dry_run:
                    print(f"   📝 将为 {file_path} 添加元数据")
                else:
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(new_content)
                    print(f"   ✅ 已为 {file_path} 添加元数据")
                
                success_count += 1
                
            except Exception as e:
                print(f"   ❌ 处理失败 {file_path}: {e}")
                error_count += 1
        
        print(f"\n📊 元数据添加统计:")
        print(f"   成功: {success_count}")
        print(f"   失败: {error_count}")
        
        return success_count, error_count
    
    def _generate_metadata(self, file_path: Path) -> str:
        """生成标准元数据"""
        filename = file_path.stem
        
        # 根据文件名和路径推断类别
        category = self._infer_category(file_path)
        
        # 生成标题
        title = filename.replace('_', ' ').title()
        
        metadata = f"""---
title: "{title}"
version: "v1.0"
date: "{datetime.now().strftime('%Y-%m-%d')}"
author: "MythQuant 团队"
status: "草稿"
category: "{category}"
tags: ["待分类"]
last_updated: "{datetime.now().strftime('%Y-%m-%d')}"
---"""
        
        return metadata
    
    def _infer_category(self, file_path: Path) -> str:
        """根据文件路径推断文档类别"""
        path_str = str(file_path).lower()
        
        if 'api' in path_str:
            return '技术'
        elif 'guide' in path_str or 'tutorial' in path_str:
            return '用户'
        elif 'architecture' in path_str:
            return '技术'
        elif 'report' in path_str:
            return '管理'
        elif 'knowledge' in path_str:
            return '知识'
        else:
            return '管理'
    
    def generate_report(self, analysis: Dict) -> str:
        """生成标准化报告"""
        report = f"""# 文档标准化分析报告

## 执行时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 分析结果

### 文档统计
- 总文档数: {analysis['total_files']}
- 命名问题: {len(analysis['naming_issues'])}
- 元数据缺失: {len(analysis['missing_metadata'])}

### 命名规范问题
"""
        
        for issue in analysis['naming_issues'][:10]:  # 只显示前10个
            report += f"- {issue}\n"
        
        if len(analysis['naming_issues']) > 10:
            report += f"- ... 还有 {len(analysis['naming_issues']) - 10} 个问题\n"
        
        report += f"""
### 元数据缺失问题
"""
        
        for issue in analysis['missing_metadata'][:10]:  # 只显示前10个
            report += f"- {issue}\n"
        
        if len(analysis['missing_metadata']) > 10:
            report += f"- ... 还有 {len(analysis['missing_metadata']) - 10} 个问题\n"
        
        return report


def main():
    """主函数"""
    print("📚 MythQuant 文档标准化工具")
    print("=" * 50)
    
    standardizer = DocumentationStandardizer()
    
    # 分析当前结构
    analysis = standardizer.analyze_current_structure()
    
    # 生成重命名建议
    rename_suggestions = standardizer.generate_rename_suggestions()
    
    # 显示分析结果
    print(f"\n📊 分析结果:")
    print(f"   总文档数: {analysis['total_files']}")
    print(f"   命名问题: {len(analysis['naming_issues'])}")
    print(f"   元数据缺失: {len(analysis['missing_metadata'])}")
    print(f"   重命名建议: {len(rename_suggestions)}")
    
    # 询问用户是否执行
    print(f"\n🤔 发现的主要问题:")
    for issue in analysis['naming_issues'][:5]:
        print(f"   - {issue}")
    
    if rename_suggestions:
        print(f"\n💡 重命名建议预览:")
        for old, new in list(rename_suggestions.items())[:5]:
            print(f"   - {Path(old).name} → {Path(new).name}")
    
    # 用户确认
    response = input(f"\n是否执行标准化操作？(y/N): ").lower()
    
    if response == 'y':
        # 创建备份
        backup_path = standardizer.create_backup()
        
        # 执行重命名
        if rename_suggestions:
            standardizer.apply_renames(rename_suggestions, dry_run=False)
        
        # 添加元数据
        standardizer.add_metadata_to_files(dry_run=False)
        
        print(f"\n✅ 标准化完成！")
        print(f"💾 备份位置: {backup_path}")
        
    else:
        print(f"\n📋 仅生成分析报告，未执行修改操作")
    
    # 生成报告
    report = standardizer.generate_report(analysis)
    report_path = Path("docs/reports/documentation_standardization_report.md")
    report_path.parent.mkdir(parents=True, exist_ok=True)
    
    with open(report_path, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"📄 分析报告已保存: {report_path}")


if __name__ == "__main__":
    main()
