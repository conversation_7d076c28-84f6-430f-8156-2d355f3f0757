2025-07-30 01:51:06,938 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250730_015106.log
2025-07-30 01:51:06,938 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-30 01:51:06,939 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-30 01:51:06,939 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-30 01:51:06,939 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-30 01:51:06,939 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-30 01:51:06,939 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-30 01:51:06,940 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-30 01:51:06,940 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-30 01:51:06,943 - Main - ERROR - error:315 - ❌ 智能文件选择器执行失败: 'FileInfo' object has no attribute 'actual_start'
2025-07-30 01:51:07,379 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-30 01:51:07,379 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-30 01:51:07,379 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-30 01:51:07,379 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-30 01:51:07,577 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 33120条 (目标日期: 20250101, 交易日: 138天, 频率: 1min)
2025-07-30 01:51:07,577 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计33120条 -> 实际请求800条 (配置限制:800)
2025-07-30 01:51:07,577 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-30 01:51:07,577 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-30 01:51:07,635 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32320条
2025-07-30 01:51:07,863 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-30 01:51:08,093 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-30 01:51:08,319 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-30 01:51:08,540 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-30 01:51:08,774 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-30 01:51:08,997 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-30 01:51:09,218 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-30 01:51:09,435 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-30 01:51:09,666 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-30 01:51:09,900 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-30 01:51:10,124 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-30 01:51:10,344 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-30 01:51:10,575 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-30 01:51:10,800 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-30 01:51:11,026 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-30 01:51:11,253 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-30 01:51:11,486 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-30 01:51:11,716 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-30 01:51:11,941 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-30 01:51:12,175 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-30 01:51:12,403 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-30 01:51:12,634 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-30 01:51:12,850 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-30 01:51:13,088 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-30 01:51:13,320 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-30 01:51:13,539 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-30 01:51:13,765 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +240条，总计21840条
2025-07-30 01:51:13,766 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21840条数据
2025-07-30 01:51:13,771 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要33120条，实际获取21840条
2025-07-30 01:51:13,771 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11280条数据（约47个交易日）
2025-07-30 01:51:13,771 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-30 01:51:13,771 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-30 01:51:13,771 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-30 01:51:13,822 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-07-30 01:51:13,981 - Main - INFO - info:307 - ℹ️ ✅ 数据保存完成: 1min_0_000617_20250318-20250725_来源互联网（202507300151）.txt (999617 字节)
2025-07-30 01:51:13,982 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-30 01:51:13,982 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-30 01:51:13,982 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-30 01:51:13,983 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-30 01:51:13,984 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-30 01:51:13,984 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-30 01:51:13,984 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-30 01:51:13,984 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-30 01:51:13,984 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-30 01:51:13,985 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250730_015113.txt
2025-07-30 01:51:13,985 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-30 01:51:13,985 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-30 01:51:13,985 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-30 01:51:13,986 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-30 01:51:13,986 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-30 01:51:13,986 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-30 01:51:13,986 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-30 01:51:13,986 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-30 01:51:13,987 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-30 01:51:13,987 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-30 01:51:13,987 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
