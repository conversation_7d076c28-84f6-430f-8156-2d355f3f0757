#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据完整性稽核工具
检测txt文件的数据行数是否符合交易日和K线级别的预期
"""

import sys
import os
import re
import pandas as pd
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional

# 添加项目根目录到路径
sys.path.append('.')
from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_stats_table
)

class DataIntegrityAuditor:
    """数据完整性稽核器"""
    
    def __init__(self):
        # K线级别对应的每日数据条数
        self.bars_per_day = {
            '1min': 240,    # 9:30-11:30 (120分钟) + 13:00-15:00 (120分钟)
            '5min': 48,     # 240分钟 ÷ 5分钟
            '15min': 16,    # 240分钟 ÷ 15分钟
            '30min': 8,     # 240分钟 ÷ 30分钟
            '60min': 4,     # 240分钟 ÷ 60分钟
            'day': 1        # 日线每天1条
        }
        
        # 已知的节假日（需要根据实际情况更新）
        self.holidays = {
            '2025': [
                '20250101',  # 元旦
                '20250128', '20250129', '20250130', '20250131',  # 春节
                '20250203', '20250204', '20250205',
                '20250405', '20250406', '20250407',  # 清明节
                '20250501', '20250502', '20250503',  # 劳动节
                '20250531', '20250602',  # 端午节
                '20250915',  # 中秋节
                '20251001', '20251002', '20251003', '20251004',  # 国庆节
                '20251005', '20251006', '20251007'
            ]
        }
    
    def parse_filename(self, filename: str) -> Optional[Dict[str, str]]:
        """
        解析文件名获取信息
        
        Args:
            filename: 文件名
            
        Returns:
            包含股票代码、频率、时间范围等信息的字典
        """
        try:
            # 匹配多种格式的文件名
            patterns = [
                # 标准格式：频率_0_股票代码_开始日期-结束日期_来源（时间戳）.txt
                r'(\d+min|day)_0_(\d{6})_(\d{8})-(\d{8})_.*?(?:（\d{12}）)?\.txt',
                # 简化格式：频率_0_股票代码_开始日期-结束日期.txt
                r'(\d+min|day)_0_(\d{6})_(\d{8})-(\d{8})\.txt',
                # 带标记格式：频率_0_股票代码_开始日期-结束日期（标记）.txt
                r'(\d+min|day)_0_(\d{6})_(\d{8})-(\d{8})（.*?）\.txt',
                # 详细时间格式：频率_0_股票代码_开始时间-结束时间_来源（时间戳）.txt
                r'(\d+min|day)_0_(\d{6})_(\d{12})-(\d{12})_.*?(?:（\d{12}）)?\.txt'
            ]
            # 尝试匹配各种格式
            for pattern in patterns:
                match = re.match(pattern, filename)
                if match:
                    groups = match.groups()
                    frequency, stock_code = groups[0], groups[1]

                    # 处理不同长度的日期格式
                    start_date = groups[2]
                    end_date = groups[3]

                    # 如果是12位时间格式，提取前8位作为日期
                    if len(start_date) == 12:
                        start_date = start_date[:8]
                    if len(end_date) == 12:
                        end_date = end_date[:8]

                    return {
                        'frequency': frequency,
                        'stock_code': stock_code,
                        'start_date': start_date,
                        'end_date': end_date,
                        'filename': filename
                    }

            print(f"⚠️ 无法解析文件名格式: {filename}")
            return None
                
        except Exception as e:
            print(f"❌ 解析文件名失败: {e}")
            return None
    
    def calculate_trading_days(self, start_date: str, end_date: str) -> Tuple[int, List[str]]:
        """
        计算指定日期范围内的交易日数量
        
        Args:
            start_date: 开始日期 (YYYYMMDD)
            end_date: 结束日期 (YYYYMMDD)
            
        Returns:
            (交易日数量, 交易日列表)
        """
        try:
            start_dt = datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.strptime(end_date, '%Y%m%d')
            
            trading_days = []
            current_dt = start_dt
            
            # 获取对应年份的节假日
            year_holidays = set()
            for year in range(start_dt.year, end_dt.year + 1):
                year_holidays.update(self.holidays.get(str(year), []))
            
            while current_dt <= end_dt:
                date_str = current_dt.strftime('%Y%m%d')
                
                # 排除周末和节假日
                if current_dt.weekday() < 5 and date_str not in year_holidays:
                    trading_days.append(date_str)
                
                current_dt += timedelta(days=1)
            
            return len(trading_days), trading_days
            
        except Exception as e:
            print(f"❌ 计算交易日失败: {e}")
            return 0, []
    
    def read_and_analyze_file(self, filepath: str) -> Dict[str, any]:
        """
        读取并分析txt文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            分析结果字典
        """
        try:
            if not os.path.exists(filepath):
                return {'error': f'文件不存在: {filepath}'}
            
            # 读取文件
            df = pd.read_csv(filepath, sep='|', encoding='utf-8')
            
            if df.empty:
                return {'error': '文件为空'}
            
            # 分析数据
            total_rows = len(df)
            
            # 提取时间信息
            time_column = '时间'
            if time_column not in df.columns:
                return {'error': f'缺少时间列: {time_column}'}
            
            # 统计每日数据条数
            daily_counts = {}
            date_range = set()
            
            for _, row in df.iterrows():
                time_str = str(row[time_column])
                
                # 提取日期部分
                if len(time_str) >= 8:
                    date_part = time_str[:8]
                    date_range.add(date_part)
                    daily_counts[date_part] = daily_counts.get(date_part, 0) + 1
            
            # 计算实际日期范围
            if date_range:
                actual_start = min(date_range)
                actual_end = max(date_range)
                actual_days = len(date_range)
            else:
                actual_start = actual_end = None
                actual_days = 0
            
            return {
                'total_rows': total_rows,
                'actual_start': actual_start,
                'actual_end': actual_end,
                'actual_days': actual_days,
                'daily_counts': daily_counts,
                'date_range': sorted(list(date_range))
            }
            
        except Exception as e:
            return {'error': f'读取文件失败: {e}'}
    
    def audit_single_file(self, filepath: str) -> Dict[str, any]:
        """
        稽核单个文件
        
        Args:
            filepath: 文件路径
            
        Returns:
            稽核结果
        """
        filename = os.path.basename(filepath)
        print_step(f"稽核文件: {filename}")
        
        # 解析文件名
        file_info = self.parse_filename(filename)
        if not file_info:
            return {'status': 'error', 'message': '无法解析文件名'}
        
        # 读取和分析文件
        file_analysis = self.read_and_analyze_file(filepath)
        if 'error' in file_analysis:
            return {'status': 'error', 'message': file_analysis['error']}
        
        # 计算预期数据
        expected_trading_days, trading_day_list = self.calculate_trading_days(
            file_info['start_date'], file_info['end_date']
        )
        
        frequency = file_info['frequency']
        bars_per_day = self.bars_per_day.get(frequency, 240)
        expected_total_rows = expected_trading_days * bars_per_day
        
        # 稽核结果
        actual_total_rows = file_analysis['total_rows']
        actual_days = file_analysis['actual_days']
        
        # 计算完整性指标
        completeness_ratio = (actual_total_rows / expected_total_rows * 100) if expected_total_rows > 0 else 0
        day_coverage_ratio = (actual_days / expected_trading_days * 100) if expected_trading_days > 0 else 0
        
        # 输出稽核结果
        print_info("基本信息:")
        print_data_info("股票代码", file_info['stock_code'], level=2)
        print_data_info("数据频率", frequency, level=2)
        print_data_info("文件名时间范围", f"{file_info['start_date']} ~ {file_info['end_date']}", level=2)
        print_data_info("实际数据时间范围", f"{file_analysis['actual_start']} ~ {file_analysis['actual_end']}", level=2)
        
        print(f"\n📈 数据量分析:")
        print(f"  预期交易日数: {expected_trading_days} 天")
        print(f"  实际数据日数: {actual_days} 天")
        print(f"  每日预期K线数: {bars_per_day} 条")
        print(f"  预期总数据量: {expected_total_rows} 条")
        print(f"  实际总数据量: {actual_total_rows} 条")
        
        print(f"\n📊 完整性评估:")
        print(f"  数据完整率: {completeness_ratio:.2f}%")
        print(f"  日期覆盖率: {day_coverage_ratio:.2f}%")
        
        # 判断数据质量
        if completeness_ratio >= 95:
            quality_status = "✅ 优秀"
        elif completeness_ratio >= 85:
            quality_status = "🟡 良好"
        elif completeness_ratio >= 70:
            quality_status = "🟠 一般"
        else:
            quality_status = "❌ 较差"
        
        print(f"  数据质量: {quality_status}")
        
        # 详细分析每日数据
        self._analyze_daily_data(file_analysis['daily_counts'], trading_day_list, bars_per_day)
        
        return {
            'status': 'success',
            'file_info': file_info,
            'expected_trading_days': expected_trading_days,
            'expected_total_rows': expected_total_rows,
            'actual_days': actual_days,
            'actual_total_rows': actual_total_rows,
            'completeness_ratio': completeness_ratio,
            'day_coverage_ratio': day_coverage_ratio,
            'quality_status': quality_status
        }
    
    def _analyze_daily_data(self, daily_counts: Dict[str, int], 
                          expected_trading_days: List[str], bars_per_day: int):
        """分析每日数据详情"""
        print(f"\n📅 每日数据分析:")
        
        # 检查缺失的交易日
        missing_days = []
        incomplete_days = []
        
        for trading_day in expected_trading_days:
            if trading_day not in daily_counts:
                missing_days.append(trading_day)
            elif daily_counts[trading_day] < bars_per_day:
                incomplete_days.append((trading_day, daily_counts[trading_day]))
        
        # 检查多余的非交易日数据
        extra_days = []
        for date in daily_counts:
            if date not in expected_trading_days:
                extra_days.append((date, daily_counts[date]))
        
        if missing_days:
            print(f"  ❌ 缺失交易日 ({len(missing_days)}天): {missing_days[:5]}{'...' if len(missing_days) > 5 else ''}")
        
        if incomplete_days:
            print(f"  ⚠️ 数据不完整交易日 ({len(incomplete_days)}天):")
            for day, count in incomplete_days[:5]:
                print(f"    {day}: {count}/{bars_per_day} 条 ({count/bars_per_day*100:.1f}%)")
            if len(incomplete_days) > 5:
                print(f"    ... 还有 {len(incomplete_days)-5} 天")
        
        if extra_days:
            print(f"  ℹ️ 非交易日数据 ({len(extra_days)}天): {[day for day, _ in extra_days[:3]]}{'...' if len(extra_days) > 3 else ''}")
        
        if not missing_days and not incomplete_days:
            print(f"  ✅ 所有交易日数据完整")

def main():
    """主函数"""
    print_banner("数据完整性稽核工具", "检测txt文件的数据行数是否符合交易日和K线级别的预期")

    auditor = DataIntegrityAuditor()

    print_main_process("文件搜索和识别")

    # 查找当前目录和输出目录中的txt文件
    search_dirs = ['.', 'H:/MPV1.17/T0002/signals/']
    txt_files = []

    for directory in search_dirs:
        if os.path.exists(directory):
            for filename in os.listdir(directory):
                if filename.endswith('.txt') and ('min_0_' in filename or 'day_0_' in filename):
                    filepath = os.path.join(directory, filename)
                    txt_files.append(filepath)

    if not txt_files:
        print_error("未找到符合格式的txt数据文件")
        return

    print_result(f"找到 {len(txt_files)} 个数据文件", True)

    print_main_process("数据完整性稽核执行")
    
    # 稽核所有文件
    audit_results = []
    for filepath in txt_files:
        result = auditor.audit_single_file(filepath)
        audit_results.append(result)
    
    # 生成汇总报告
    print_main_process("稽核汇总报告")
    
    successful_audits = [r for r in audit_results if r['status'] == 'success']
    
    if successful_audits:
        total_files = len(successful_audits)
        excellent_files = len([r for r in successful_audits if '✅' in r['quality_status']])
        good_files = len([r for r in successful_audits if '🟡' in r['quality_status']])
        average_files = len([r for r in successful_audits if '🟠' in r['quality_status']])
        poor_files = len([r for r in successful_audits if '❌' in r['quality_status']])
        
        avg_completeness = sum(r['completeness_ratio'] for r in successful_audits) / total_files
        
        print(f"📈 整体统计:")
        print(f"  稽核文件总数: {total_files}")
        print(f"  优秀文件: {excellent_files} ({excellent_files/total_files*100:.1f}%)")
        print(f"  良好文件: {good_files} ({good_files/total_files*100:.1f}%)")
        print(f"  一般文件: {average_files} ({average_files/total_files*100:.1f}%)")
        print(f"  较差文件: {poor_files} ({poor_files/total_files*100:.1f}%)")
        print(f"  平均完整率: {avg_completeness:.2f}%")
        
        # 显示质量较差的文件
        poor_quality_files = [r for r in successful_audits if r['completeness_ratio'] < 85]
        if poor_quality_files:
            print(f"\n⚠️ 需要关注的文件 (完整率<85%):")
            for result in poor_quality_files:
                filename = result['file_info']['filename']
                completeness = result['completeness_ratio']
                print(f"  - {filename}: {completeness:.2f}%")
    
    print(f"\n✅ 数据完整性稽核完成")

if __name__ == '__main__':
    main()
