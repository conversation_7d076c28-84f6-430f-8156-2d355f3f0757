#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
docs目录精简归档脚本
清理与当前DDD架构无关或已过时的文档，保持文档结构清晰
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class DocsCleanupManager:
    """文档清理管理器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.docs_root = self.project_root / 'docs'
        self.archive_root = self.project_root / 'archive' / 'docs_archive'
        self.backup_root = self.project_root / f'backup_docs_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        self.cleanup_log = []
        
    def analyze_docs_structure(self) -> Dict[str, Any]:
        """分析docs目录结构"""
        print("🔍 分析docs目录结构...")
        
        analysis = {
            'keep_items': {
                'description': '保留的核心文档（与DDD架构相关）',
                'directories': [
                    'architecture',      # 架构文档（包含DDD升级专题）
                    'testing',          # 测试文档（包含标准化指南）
                    'knowledge',        # 知识库（包含配置管理等）
                    'summary'           # 总结报告
                ],
                'files': [
                    'documentation_index.md',  # 文档索引
                    'README.md'                # 项目主文档
                ]
            },
            'archive_items': {
                'description': '需要归档的过时文档',
                'directories': [
                    'analysis',         # 旧的分析文档
                    'api',             # API文档（可能过时）
                    'guides',          # 旧的指南文档
                    'legacy',          # 遗留文档
                    'migration',       # 迁移文档（已完成）
                    'project',         # 项目清理文档（已完成）
                    'reports',         # 旧的报告文档
                    'templates',       # 模板文档（可能不再使用）
                    'tutorials'        # 教程文档（可能过时）
                ],
                'files': [
                    'DEPENDENCIES.md',
                    'DOCUMENTATION_STANDARDS.md',
                    'OUTPUT_CONFIG_GUIDE.md',
                    'PROJECT_STRUCTURE.md',
                    'QUICK_NAVIGATION.md',
                    'UPGRADE_REPORT.md',
                    'ai_model_identification_request_20250724.md',
                    'architecture.md',  # 重复，architecture目录已有
                    'calculation_display_control_guide.md',
                    'check_incremental_download_prerequisite_analysis.md',
                    'comprehensive_test_production_unification_plan.md',
                    'config_management_knowledge_base.md',  # 重复，knowledge目录已有
                    'flow_optimizer_best_practices_and_extensions.md',
                    'flow_optimizer_usage_guide.md',
                    'internet_data_download_guide.md',
                    'knowledge_consolidation_and_rules_update_summary_20250731.md',
                    'output_format_migration_progress.md',
                    'output_format_standards.md',
                    'precision_analysis_summary.md',
                    'precision_enhancement_summary.md',
                    'pytdx_analysis_summary.md',
                    'pytdx_usage_guide.md',
                    'test_environment_guide.md',
                    'test_production_environment_unification_proposal.md',
                    'user_guide.md',
                    'workflow_documentation_update_summary.md'
                ]
            },
            'delete_items': {
                'description': '可以直接删除的临时文件',
                'files': [
                    '*.tmp',
                    '*.bak',
                    '*~',
                    '.DS_Store'
                ]
            }
        }
        
        return analysis
    
    def create_backup(self):
        """创建完整备份"""
        print("📦 创建docs目录备份...")
        
        if self.docs_root.exists():
            shutil.copytree(self.docs_root, self.backup_root)
            print(f"   ✅ 备份创建完成: {self.backup_root}")
            self.cleanup_log.append(f"创建备份: {self.backup_root}")
        else:
            print("   ⚠️ docs目录不存在")
    
    def create_archive_structure(self):
        """创建归档目录结构"""
        print("🏗️ 创建归档目录结构...")
        
        archive_structure = {
            'legacy_docs': '遗留文档归档',
            'migration_docs': '迁移相关文档归档',
            'analysis_docs': '分析报告文档归档',
            'guide_docs': '指南文档归档',
            'template_docs': '模板文档归档',
            'misc_docs': '其他文档归档'
        }
        
        for dir_name, description in archive_structure.items():
            archive_dir = self.archive_root / dir_name
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建说明文件
            readme_file = archive_dir / 'README.md'
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(f"# {description}\n\n")
                f.write(f"归档时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"归档原因: DDD架构升级后的文档精简\n\n")
                f.write("## 归档内容\n\n")
                f.write("本目录包含与当前DDD架构无关或已过时的文档。\n")
        
        print(f"   ✅ 归档结构创建完成: {self.archive_root}")
    
    def execute_archival(self, analysis: Dict[str, Any]):
        """执行归档操作"""
        print("📁 执行文档归档...")
        
        # 归档目录
        archive_dirs = analysis['archive_items']['directories']
        for dir_name in archive_dirs:
            source_dir = self.docs_root / dir_name
            if source_dir.exists():
                # 根据目录类型选择归档位置
                if dir_name in ['legacy']:
                    target_dir = self.archive_root / 'legacy_docs' / dir_name
                elif dir_name in ['migration']:
                    target_dir = self.archive_root / 'migration_docs' / dir_name
                elif dir_name in ['analysis', 'reports']:
                    target_dir = self.archive_root / 'analysis_docs' / dir_name
                elif dir_name in ['guides', 'tutorials']:
                    target_dir = self.archive_root / 'guide_docs' / dir_name
                elif dir_name in ['templates']:
                    target_dir = self.archive_root / 'template_docs' / dir_name
                else:
                    target_dir = self.archive_root / 'misc_docs' / dir_name
                
                shutil.move(str(source_dir), str(target_dir))
                print(f"   ✅ 归档目录: {dir_name} -> {target_dir.relative_to(self.project_root)}")
                self.cleanup_log.append(f"归档目录: {dir_name} -> {target_dir}")
        
        # 归档文件
        archive_files = analysis['archive_items']['files']
        for file_name in archive_files:
            source_file = self.docs_root / file_name
            if source_file.exists():
                # 根据文件类型选择归档位置
                if 'migration' in file_name.lower():
                    target_file = self.archive_root / 'migration_docs' / file_name
                elif any(keyword in file_name.lower() for keyword in ['analysis', 'report', 'summary']):
                    target_file = self.archive_root / 'analysis_docs' / file_name
                elif any(keyword in file_name.lower() for keyword in ['guide', 'tutorial']):
                    target_file = self.archive_root / 'guide_docs' / file_name
                else:
                    target_file = self.archive_root / 'misc_docs' / file_name
                
                shutil.move(str(source_file), str(target_file))
                print(f"   ✅ 归档文件: {file_name} -> {target_file.relative_to(self.project_root)}")
                self.cleanup_log.append(f"归档文件: {file_name} -> {target_file}")
    
    def cleanup_empty_directories(self):
        """清理空目录"""
        print("🧹 清理空目录...")
        
        for root, dirs, files in os.walk(self.docs_root, topdown=False):
            for dir_name in dirs:
                dir_path = Path(root) / dir_name
                try:
                    if not any(dir_path.iterdir()):  # 目录为空
                        dir_path.rmdir()
                        print(f"   ✅ 删除空目录: {dir_path.relative_to(self.docs_root)}")
                        self.cleanup_log.append(f"删除空目录: {dir_path}")
                except OSError:
                    pass  # 目录不为空或其他错误，跳过
    
    def validate_cleanup_result(self) -> Dict[str, Any]:
        """验证清理结果"""
        print("✅ 验证清理结果...")
        
        validation_result = {
            'remaining_directories': [],
            'remaining_files': [],
            'core_docs_intact': True,
            'archive_created': self.archive_root.exists()
        }
        
        # 检查剩余的目录和文件
        if self.docs_root.exists():
            for item in self.docs_root.iterdir():
                if item.is_dir():
                    validation_result['remaining_directories'].append(item.name)
                else:
                    validation_result['remaining_files'].append(item.name)
        
        # 验证核心文档是否完整
        core_docs = [
            'architecture',
            'testing', 
            'knowledge',
            'summary',
            'documentation_index.md',
            'README.md'
        ]
        
        for doc in core_docs:
            doc_path = self.docs_root / doc
            if not doc_path.exists():
                validation_result['core_docs_intact'] = False
                print(f"   ⚠️ 核心文档缺失: {doc}")
        
        print(f"   📊 剩余目录: {len(validation_result['remaining_directories'])} 个")
        print(f"   📊 剩余文件: {len(validation_result['remaining_files'])} 个")
        print(f"   📊 核心文档完整: {'✅' if validation_result['core_docs_intact'] else '❌'}")
        print(f"   📊 归档创建成功: {'✅' if validation_result['archive_created'] else '❌'}")
        
        return validation_result
    
    def generate_cleanup_report(self, analysis: Dict[str, Any], validation: Dict[str, Any]):
        """生成清理报告"""
        print("📋 生成清理报告...")
        
        report = {
            'cleanup_time': datetime.now().isoformat(),
            'backup_location': str(self.backup_root),
            'archive_location': str(self.archive_root),
            'analysis_summary': {
                'kept_directories': len(analysis['keep_items']['directories']),
                'kept_files': len(analysis['keep_items']['files']),
                'archived_directories': len(analysis['archive_items']['directories']),
                'archived_files': len(analysis['archive_items']['files'])
            },
            'cleanup_operations': self.cleanup_log,
            'validation_result': validation,
            'final_structure': {
                'remaining_directories': validation['remaining_directories'],
                'remaining_files': validation['remaining_files']
            }
        }
        
        report_file = self.project_root / f'docs_cleanup_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ 清理报告已生成: {report_file}")
        return report_file
    
    def run_cleanup(self) -> bool:
        """运行完整的清理流程"""
        print("🎯 docs目录精简归档开始")
        print("=" * 60)
        print("📋 目标: 清理与DDD架构无关或已过时的文档")
        print("=" * 60)
        
        try:
            # 1. 分析当前结构
            analysis = self.analyze_docs_structure()
            
            # 2. 显示分析结果
            print(f"\n📊 分析结果:")
            print(f"   保留目录: {len(analysis['keep_items']['directories'])} 个")
            print(f"   保留文件: {len(analysis['keep_items']['files'])} 个")
            print(f"   归档目录: {len(analysis['archive_items']['directories'])} 个")
            print(f"   归档文件: {len(analysis['archive_items']['files'])} 个")
            
            # 3. 创建备份
            self.create_backup()
            
            # 4. 创建归档结构
            self.create_archive_structure()
            
            # 5. 执行归档
            self.execute_archival(analysis)
            
            # 6. 清理空目录
            self.cleanup_empty_directories()
            
            # 7. 验证结果
            validation = self.validate_cleanup_result()
            
            # 8. 生成报告
            report_file = self.generate_cleanup_report(analysis, validation)
            
            print(f"\n🎉 docs目录精简归档完成！")
            print(f"📦 备份位置: {self.backup_root}")
            print(f"📁 归档位置: {self.archive_root}")
            print(f"📋 清理报告: {report_file}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 清理过程出现异常: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    cleanup_manager = DocsCleanupManager()
    
    print("⚠️  重要提示:")
    print("   此操作将清理docs目录中与DDD架构无关的文档")
    print("   保留核心文档：architecture、testing、knowledge、summary")
    print("   过时文档将归档到archive/docs_archive/")
    print("   操作前会自动创建完整备份")
    print()
    
    # 显示将要保留的核心文档
    print("📋 将要保留的核心文档:")
    print("   📁 architecture/ - DDD架构升级专题文档")
    print("   📁 testing/ - 测试标准化指南")
    print("   📁 knowledge/ - 配置管理等知识库")
    print("   📁 summary/ - 综合总结报告")
    print("   📄 documentation_index.md - 文档索引")
    print("   📄 README.md - 项目主文档")
    print()
    
    response = input("是否继续执行docs目录清理? (y/N): ")
    if response.lower() != 'y':
        print("❌ 用户取消操作")
        return False
    
    success = cleanup_manager.run_cleanup()
    
    if success:
        print("\n🎉 docs目录精简归档成功完成！")
        print("\n📋 下一步建议:")
        print("1. 检查保留的核心文档是否完整")
        print("2. 验证文档索引是否需要更新")
        print("3. 确认归档的文档是否还需要")
        print("4. 可以删除备份目录（如果确认无问题）")
        return True
    else:
        print("\n❌ docs目录清理失败！")
        print("请检查错误信息，必要时从备份恢复")
        return False


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
