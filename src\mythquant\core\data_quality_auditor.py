#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量稽核集成模块
在主函数执行过程中的关键环节进行数据质量校验
"""

import os
import sys
from typing import Dict, List, Optional, Tuple
from datetime import datetime

# 添加项目根目录到路径
sys.path.append('.')

from tools.data_integrity_auditor import DataIntegrityAuditor
from utils.enhanced_error_handler import get_smart_logger, ErrorCategory


class DataQualityAuditor:
    """数据质量稽核器 - 集成到主函数执行流程"""
    
    def __init__(self):
        self.smart_logger = get_smart_logger("DataQualityAuditor")
        self.integrity_auditor = DataIntegrityAuditor()
        
        # 稽核统计
        self.audit_stats = {
            'total_audits': 0,
            'passed_audits': 0,
            'failed_audits': 0,
            'audit_results': []
        }
    
    def audit_before_task_execution(self, task_name: str, output_dir: str) -> Dict[str, any]:
        """
        任务执行前的数据质量稽核
        
        Args:
            task_name: 任务名称
            output_dir: 输出目录
            
        Returns:
            稽核结果
        """
        self.smart_logger.info(f"🔍 执行任务前数据质量稽核: {task_name}")
        
        try:
            # 查找现有的相关数据文件
            existing_files = self._find_existing_data_files(output_dir, task_name)
            
            if not existing_files:
                self.smart_logger.info("📋 未发现现有数据文件，跳过执行前稽核")
                return {
                    'status': 'no_existing_data',
                    'message': '无现有数据文件',
                    'files_count': 0
                }
            
            self.smart_logger.info(f"📊 发现 {len(existing_files)} 个现有数据文件，开始稽核")
            
            # 对现有文件进行稽核
            audit_results = []
            for filepath in existing_files:
                result = self.integrity_auditor.audit_single_file(filepath)
                audit_results.append(result)
            
            # 统计稽核结果
            passed_count = len([r for r in audit_results if r.get('status') == 'success' and r.get('completeness_ratio', 0) >= 95])
            
            result = {
                'status': 'completed',
                'files_count': len(existing_files),
                'passed_count': passed_count,
                'failed_count': len(existing_files) - passed_count,
                'audit_results': audit_results
            }
            
            if passed_count == len(existing_files):
                self.smart_logger.info(f"✅ 执行前稽核通过: {passed_count}/{len(existing_files)} 个文件质量优秀")
            else:
                self.smart_logger.warning(f"⚠️ 执行前稽核发现问题: {result['failed_count']} 个文件质量不达标")
            
            return result
            
        except Exception as e:
            self.smart_logger.error(f"❌ 执行前稽核失败: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'files_count': 0
            }
    
    def audit_after_task_execution(self, task_name: str, output_dir: str, 
                                 expected_files: Optional[List[str]] = None) -> Dict[str, any]:
        """
        任务执行后的数据质量稽核
        
        Args:
            task_name: 任务名称
            output_dir: 输出目录
            expected_files: 预期生成的文件列表
            
        Returns:
            稽核结果
        """
        self.smart_logger.info(f"🔍 执行任务后数据质量稽核: {task_name}")
        
        try:
            # 查找新生成的数据文件
            generated_files = self._find_generated_data_files(output_dir, task_name)
            
            if not generated_files:
                self.smart_logger.warning("⚠️ 未发现新生成的数据文件")
                return {
                    'status': 'no_generated_data',
                    'message': '无新生成数据文件',
                    'files_count': 0
                }
            
            self.smart_logger.info(f"📊 发现 {len(generated_files)} 个新生成文件，开始稽核")
            
            # 对新生成文件进行稽核
            audit_results = []
            critical_issues = []
            
            for filepath in generated_files:
                result = self.integrity_auditor.audit_single_file(filepath)
                audit_results.append(result)
                
                # 检查关键问题
                if result.get('status') == 'success':
                    completeness = result.get('completeness_ratio', 0)
                    if completeness < 85:
                        critical_issues.append({
                            'file': os.path.basename(filepath),
                            'issue': f'数据完整率过低: {completeness:.1f}%',
                            'severity': 'high'
                        })
                    elif completeness < 95:
                        critical_issues.append({
                            'file': os.path.basename(filepath),
                            'issue': f'数据完整率偏低: {completeness:.1f}%',
                            'severity': 'medium'
                        })
            
            # 统计稽核结果
            excellent_count = len([r for r in audit_results if r.get('status') == 'success' and r.get('completeness_ratio', 0) >= 95])
            good_count = len([r for r in audit_results if r.get('status') == 'success' and 85 <= r.get('completeness_ratio', 0) < 95])
            poor_count = len(generated_files) - excellent_count - good_count
            
            result = {
                'status': 'completed',
                'files_count': len(generated_files),
                'excellent_count': excellent_count,
                'good_count': good_count,
                'poor_count': poor_count,
                'critical_issues': critical_issues,
                'audit_results': audit_results
            }
            
            # 更新统计
            self.audit_stats['total_audits'] += len(generated_files)
            self.audit_stats['passed_audits'] += excellent_count + good_count
            self.audit_stats['failed_audits'] += poor_count
            self.audit_stats['audit_results'].extend(audit_results)
            
            # 输出稽核结果
            if poor_count == 0:
                self.smart_logger.info(f"✅ 执行后稽核通过: {excellent_count} 优秀, {good_count} 良好")
            else:
                self.smart_logger.warning(f"⚠️ 执行后稽核发现问题: {poor_count} 个文件质量较差")
                for issue in critical_issues[:3]:  # 只显示前3个问题
                    self.smart_logger.warning(f"  - {issue['file']}: {issue['issue']}")

            # 使用结果通知器输出标准化结果
            self._notify_final_validation_result(task_name, output_dir, result, audit_results)

            return result
            
        except Exception as e:
            self.smart_logger.error(f"❌ 执行后稽核失败: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'files_count': 0
            }

    def _notify_final_validation_result(self, task_name: str, output_dir: str,
                                       result: dict, audit_results: list):
        """使用结果通知器输出最终验证结果"""
        try:
            from utils.result_notifier import notify_final_validation

            # 构建文件详情
            file_details = []
            for audit_result in audit_results:
                if audit_result.get('status') == 'success':
                    file_details.append({
                        'filename': os.path.basename(audit_result.get('file_path', 'N/A')),
                        'status': 'success',
                        'completeness_rate': audit_result.get('completeness_ratio', 0) / 100,
                        'format_correctness': 1.0,  # 假设格式正确
                        'price_precision_status': '符合要求'
                    })

            # 确定验证状态
            poor_count = result.get('poor_count', 0)
            if poor_count == 0:
                validation_status = 'success'
                conclusion = '所有文件生成成功，数据质量验证全部通过'
            elif poor_count < result.get('files_count', 0):
                validation_status = 'partial'
                conclusion = f'部分文件验证通过，{poor_count}个文件需要改进'
            else:
                validation_status = 'failed'
                conclusion = '数据质量验证失败，需要重新处理'

            # 构建通知结果
            validation_result = {
                'task_name': task_name,
                'output_dir': output_dir,
                'validation_status': validation_status,
                'file_count': result.get('files_count', 0),
                'passed_count': result.get('excellent_count', 0) + result.get('good_count', 0),
                'failed_count': poor_count,
                'file_details': file_details,
                'conclusion': conclusion
            }

            notify_final_validation(validation_result)

        except Exception as e:
            # 不影响主流程，继续执行
            pass
    
    def audit_final_summary(self, output_dir: str) -> Dict[str, any]:
        """
        最终数据质量稽核汇总
        
        Args:
            output_dir: 输出目录
            
        Returns:
            汇总稽核结果
        """
        self.smart_logger.info("🔍 执行最终数据质量稽核汇总")
        
        try:
            # 查找所有数据文件
            all_files = self._find_all_data_files(output_dir)
            
            if not all_files:
                self.smart_logger.warning("⚠️ 未发现任何数据文件")
                return {
                    'status': 'no_data',
                    'message': '无数据文件',
                    'total_files': 0
                }
            
            self.smart_logger.info(f"📊 对 {len(all_files)} 个数据文件进行最终稽核")
            
            # 执行全面稽核
            audit_results = []
            for filepath in all_files:
                result = self.integrity_auditor.audit_single_file(filepath)
                audit_results.append(result)
            
            # 生成汇总统计
            successful_audits = [r for r in audit_results if r.get('status') == 'success']
            
            if successful_audits:
                total_files = len(successful_audits)
                excellent_files = len([r for r in successful_audits if r.get('completeness_ratio', 0) >= 95])
                good_files = len([r for r in successful_audits if 85 <= r.get('completeness_ratio', 0) < 95])
                poor_files = total_files - excellent_files - good_files
                
                avg_completeness = sum(r.get('completeness_ratio', 0) for r in successful_audits) / total_files
                
                result = {
                    'status': 'completed',
                    'total_files': total_files,
                    'excellent_files': excellent_files,
                    'good_files': good_files,
                    'poor_files': poor_files,
                    'avg_completeness': avg_completeness,
                    'audit_results': audit_results
                }
                
                # 输出汇总结果
                self.smart_logger.info("📊 最终数据质量稽核汇总:")
                self.smart_logger.info(f"  总文件数: {total_files}")
                self.smart_logger.info(f"  优秀文件: {excellent_files} ({excellent_files/total_files*100:.1f}%)")
                self.smart_logger.info(f"  良好文件: {good_files} ({good_files/total_files*100:.1f}%)")
                self.smart_logger.info(f"  较差文件: {poor_files} ({poor_files/total_files*100:.1f}%)")
                self.smart_logger.info(f"  平均完整率: {avg_completeness:.2f}%")
                
                if poor_files > 0:
                    poor_quality_files = [r for r in successful_audits if r.get('completeness_ratio', 0) < 85]
                    self.smart_logger.warning(f"⚠️ 需要关注的文件 (完整率<85%):")
                    for result in poor_quality_files[:5]:  # 只显示前5个
                        filename = result['file_info']['filename']
                        completeness = result['completeness_ratio']
                        self.smart_logger.warning(f"  - {filename}: {completeness:.2f}%")
                
                return result
            else:
                return {
                    'status': 'no_successful_audits',
                    'message': '无成功的稽核结果',
                    'total_files': len(all_files)
                }
            
        except Exception as e:
            self.smart_logger.error(f"❌ 最终稽核汇总失败: {e}")
            return {
                'status': 'error',
                'message': str(e),
                'total_files': 0
            }
    
    def _find_existing_data_files(self, output_dir: str, task_name: str) -> List[str]:
        """查找现有的数据文件"""
        try:
            if not os.path.exists(output_dir):
                return []
            
            files = []
            for filename in os.listdir(output_dir):
                if filename.endswith('.txt') and ('min_0_' in filename or 'day_0_' in filename):
                    filepath = os.path.join(output_dir, filename)
                    files.append(filepath)
            
            return files
            
        except Exception as e:
            self.smart_logger.error(f"查找现有数据文件失败: {e}")
            return []
    
    def _find_generated_data_files(self, output_dir: str, task_name: str) -> List[str]:
        """查找新生成的数据文件（基于修改时间）"""
        try:
            if not os.path.exists(output_dir):
                return []
            
            # 查找最近5分钟内修改的文件
            current_time = datetime.now().timestamp()
            recent_threshold = current_time - 300  # 5分钟
            
            files = []
            for filename in os.listdir(output_dir):
                if filename.endswith('.txt') and ('min_0_' in filename or 'day_0_' in filename):
                    filepath = os.path.join(output_dir, filename)
                    if os.path.getmtime(filepath) > recent_threshold:
                        files.append(filepath)
            
            return files
            
        except Exception as e:
            self.smart_logger.error(f"查找新生成数据文件失败: {e}")
            return []
    
    def _find_all_data_files(self, output_dir: str) -> List[str]:
        """查找所有数据文件"""
        try:
            if not os.path.exists(output_dir):
                return []
            
            files = []
            for filename in os.listdir(output_dir):
                if filename.endswith('.txt') and ('min_0_' in filename or 'day_0_' in filename):
                    filepath = os.path.join(output_dir, filename)
                    files.append(filepath)
            
            return files
            
        except Exception as e:
            self.smart_logger.error(f"查找所有数据文件失败: {e}")
            return []
    
    def get_audit_statistics(self) -> Dict[str, any]:
        """获取稽核统计信息"""
        return self.audit_stats.copy()
