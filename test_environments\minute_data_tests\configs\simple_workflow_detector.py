#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版测试环境Workflow违规检测器
"""

import os
import sys
import time

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
test_env_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(os.path.dirname(test_env_dir))
sys.path.insert(0, project_root)

# 全局状态
violations = []
taskmanager_time = None
workflow_start_time = None

def setup_test_env():
    """设置测试环境"""
    print("Setting up test environment...")
    
    # 切换到测试环境目录
    os.chdir(test_env_dir)
    print(f"Working directory: {os.getcwd()}")
    
    # 检查目标文件
    target_file = "input_data/test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
    if os.path.exists(target_file):
        print(f"Target file found: {target_file}")
        return True
    else:
        print(f"Target file NOT found: {target_file}")
        return False

def install_monitoring():
    """安装监控"""
    global violations, taskmanager_time, workflow_start_time
    
    try:
        # 监控TaskManager
        from src.mythquant.core.task_manager import TaskManager
        original_execute = TaskManager._execute_minute_task
        
        def monitored_execute(self, task, target_stocks):
            global taskmanager_time
            taskmanager_time = time.time()
            print(f"\n*** TASKMANAGER START: {taskmanager_time} ***")
            result = original_execute(self, task, target_stocks)
            print(f"*** TASKMANAGER END ***")
            return result
        
        TaskManager._execute_minute_task = monitored_execute
        
        # 监控五步流程
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        original_five_step = StructuredInternetMinuteDownloader._execute_five_step_process
        
        def monitored_five_step(self, stock_code, start_date, end_date, frequency, original_frequency):
            global workflow_start_time
            workflow_start_time = time.time()
            print(f"\n*** WORKFLOW START: {workflow_start_time} ***")
            return original_five_step(self, stock_code, start_date, end_date, frequency, original_frequency)
        
        StructuredInternetMinuteDownloader._execute_five_step_process = monitored_five_step
        
        # 监控pytdx调用
        from utils.pytdx_downloader import PytdxDownloader
        original_download = PytdxDownloader.download_minute_data
        
        def monitored_download(self, stock_code, start_date, end_date, frequency='1min', suppress_warnings=False):
            global violations, taskmanager_time, workflow_start_time
            current_time = time.time()
            
            print(f"\n*** PYTDX CALL: {current_time} ***")
            print(f"Stock: {stock_code}, Range: {start_date}-{end_date}")
            
            # 检查违规
            violation_detected = False
            if taskmanager_time and workflow_start_time:
                if taskmanager_time < current_time < workflow_start_time:
                    violation_detected = True
                    print("!!! VIOLATION: Called between TaskManager and Workflow start !!!")
            elif taskmanager_time and not workflow_start_time:
                time_diff = current_time - taskmanager_time
                if time_diff > 0.1:  # 100ms
                    violation_detected = True
                    print("!!! VIOLATION: Called after TaskManager but before Workflow !!!")
            
            if violation_detected:
                import traceback
                violations.append({
                    'stock_code': stock_code,
                    'date_range': f"{start_date}-{end_date}",
                    'call_time': current_time,
                    'taskmanager_time': taskmanager_time,
                    'workflow_time': workflow_start_time,
                    'stack': traceback.format_stack()
                })
            
            print("*** PYTDX CALL END ***")
            return original_download(self, stock_code, start_date, end_date, frequency, suppress_warnings)
        
        PytdxDownloader.download_minute_data = monitored_download
        
        # 监控print输出
        import builtins
        original_print = builtins.print
        
        def monitored_print(*args, **kwargs):
            message = ' '.join(str(arg) for arg in args)
            if any(keyword in message for keyword in ['时间范围内无数据', 'pytdx未获取到', 'pytdx不可用']):
                current_time = time.time()
                print(f"\n*** WARNING DETECTED: {current_time} ***", file=sys.stderr)
                print(f"Message: {message}", file=sys.stderr)
                if taskmanager_time:
                    print(f"Time since TaskManager: {current_time - taskmanager_time:.3f}s", file=sys.stderr)
                if workflow_start_time:
                    print(f"Time since Workflow: {current_time - workflow_start_time:.3f}s", file=sys.stderr)
                else:
                    print("Workflow not started yet!", file=sys.stderr)
                print("*** WARNING END ***", file=sys.stderr)
            
            return original_print(*args, **kwargs)
        
        builtins.print = monitored_print
        
        print("Monitoring installed successfully")
        return True
        
    except Exception as e:
        print(f"Monitoring installation failed: {e}")
        return False

def run_test():
    """运行测试"""
    print("\nRunning test with monitoring...")
    
    try:
        from main import main
        result = main()
        print(f"Test completed with result: {result}")
        return True
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def analyze_results():
    """分析结果"""
    global violations
    
    print(f"\n=== ANALYSIS RESULTS ===")
    print(f"Violations detected: {len(violations)}")
    
    if violations:
        print("\nVIOLATION DETAILS:")
        for i, v in enumerate(violations, 1):
            print(f"\nViolation #{i}:")
            print(f"  Stock: {v['stock_code']}")
            print(f"  Range: {v['date_range']}")
            print(f"  Call time: {v['call_time']}")
            print(f"  TaskManager time: {v['taskmanager_time']}")
            print(f"  Workflow time: {v['workflow_time']}")
            
            print("  Key stack frames:")
            for frame in v['stack']:
                if 'task_manager' in frame or 'TaskManager' in frame:
                    print(f"    >>> {frame.strip()}")
    else:
        print("No violations detected")

def main():
    """主函数"""
    print("Test Environment Workflow Violation Detector")
    print("=" * 60)
    
    if not setup_test_env():
        return 1
    
    if not install_monitoring():
        return 1
    
    success = run_test()
    analyze_results()
    
    print(f"\nDetection completed: {'SUCCESS' if success else 'FAILED'}")
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
