#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试脚本
验证算法模块与主程序的集成是否正常
"""

import sys
import os
import pandas as pd
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_main_program_integration():
    """测试主程序集成"""
    print("🔧 开始集成测试...")
    print("=" * 60)
    
    try:
        # 导入主程序类
        from main_v20230219_optimized import StockDataProcessor
        
        print("✅ 主程序导入成功")
        
        # 创建处理器实例（使用测试路径）
        test_tdx_path = r"C:\new_tdx"  # 使用配置中的路径
        processor = StockDataProcessor(test_tdx_path)
        
        print("✅ 处理器实例创建成功")
        
        # 验证算法模块是否正确初始化
        assert hasattr(processor, 'l2_calculator'), "处理器应包含L2计算器"
        assert hasattr(processor, 'resampler'), "处理器应包含重采样器"
        
        print("✅ 算法模块初始化验证通过")
        
        # 测试算法模块调用
        test_data = create_minimal_test_data()
        
        # 测试L2指标计算
        result = processor.l2_calculator.calculate_l2_metrics(test_data)
        assert len(result) > 0, "L2指标计算应返回结果"
        assert '买卖差' in result.columns, "结果应包含买卖差列"
        
        print("✅ L2指标计算集成测试通过")
        
        # 测试重采样功能
        df_daily, df_weekly = processor.resampler.resample_to_timeframes(result, 0)
        
        print("✅ 重采样功能集成测试通过")
        
        print("=" * 60)
        print("🎉 集成测试全部通过！")
        return True
        
    except ImportError as e:
        print(f"❌ 导入错误: {e}")
        return False
    except Exception as e:
        print(f"❌ 集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_minimal_test_data():
    """创建最小测试数据集"""
    data = []
    base_price = 10.0
    
    for i in range(10):
        data.append({
            '日期': f'20240101 09:{30+i}:00',
            '股票代码': '000617',
            'open': base_price + 0.1 * i,
            'high': base_price + 0.2 * i,
            'low': base_price - 0.1 * i,
            'close': base_price + 0.05 * i,
            'volume': 1000 + i * 100,
            'amount': (base_price + 0.05 * i) * (1000 + i * 100),
            '上周期C': base_price if i == 0 else base_price + 0.05 * (i-1)
        })
    
    return pd.DataFrame(data)


def test_file_generation():
    """测试文件生成功能"""
    print("\n📁 测试文件生成功能...")
    
    try:
        # 这里可以添加实际的文件生成测试
        # 由于需要真实的股票数据，暂时跳过
        print("⏭️  文件生成测试跳过（需要真实数据）")
        return True
        
    except Exception as e:
        print(f"❌ 文件生成测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 MythQuant 集成测试套件")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    success = True
    
    # 运行集成测试
    if not test_main_program_integration():
        success = False
    
    # 运行文件生成测试
    if not test_file_generation():
        success = False
    
    print("=" * 60)
    if success:
        print("🎉 所有集成测试通过！")
        print("✨ 算法模块迁移成功，系统运行正常")
    else:
        print("❌ 部分集成测试失败")
        print("🔧 请检查算法模块的集成配置")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
