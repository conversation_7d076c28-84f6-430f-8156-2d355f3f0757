#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件管理器模块

提供统一的文件操作接口，包括文件读取、写入、路径管理等功能
"""

import os
import shutil
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
import logging
import json
import pickle
from datetime import datetime

# 导入新架构的配置系统
from src.mythquant.config.manager import ConfigManager

logger = logging.getLogger(__name__)


class FileManager:
    """文件管理器 - 统一的文件操作接口"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化文件管理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.base_output_path = Path(config_manager.get_output_path())
        self.file_encoding = config_manager.get_file_encoding()
        
        # 确保输出目录存在
        self._ensure_output_directory()
    
    def _ensure_output_directory(self):
        """确保输出目录存在"""
        try:
            self.base_output_path.mkdir(parents=True, exist_ok=True)
            logger.debug(f"输出目录已确保存在: {self.base_output_path}")
        except Exception as e:
            logger.error(f"创建输出目录失败: {e}")
    
    def get_output_path(self, filename: str = None, subdirectory: str = None) -> Path:
        """
        获取输出文件路径
        
        Args:
            filename: 文件名
            subdirectory: 子目录名
            
        Returns:
            完整的文件路径
        """
        try:
            if subdirectory:
                output_path = self.base_output_path / subdirectory
                output_path.mkdir(parents=True, exist_ok=True)
            else:
                output_path = self.base_output_path
            
            if filename:
                return output_path / filename
            else:
                return output_path
                
        except Exception as e:
            logger.error(f"获取输出路径失败: {e}")
            return self.base_output_path / (filename or "")
    
    def read_text_file(self, file_path: Union[str, Path], encoding: str = None) -> Optional[str]:
        """
        读取文本文件
        
        Args:
            file_path: 文件路径
            encoding: 文件编码，默认使用配置的编码
            
        Returns:
            文件内容或None
        """
        try:
            file_path = Path(file_path)
            encoding = encoding or self.file_encoding
            
            if not file_path.exists():
                logger.warning(f"文件不存在: {file_path}")
                return None
            
            with open(file_path, 'r', encoding=encoding) as f:
                content = f.read()
            
            logger.debug(f"成功读取文本文件: {file_path}")
            return content
            
        except Exception as e:
            logger.error(f"读取文本文件失败 {file_path}: {e}")
            return None
    
    def write_text_file(self, file_path: Union[str, Path], content: str, 
                       encoding: str = None, create_dirs: bool = True) -> bool:
        """
        写入文本文件
        
        Args:
            file_path: 文件路径
            content: 文件内容
            encoding: 文件编码，默认使用配置的编码
            create_dirs: 是否自动创建目录
            
        Returns:
            是否写入成功
        """
        try:
            file_path = Path(file_path)
            encoding = encoding or self.file_encoding
            
            # 创建目录
            if create_dirs:
                file_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(file_path, 'w', encoding=encoding) as f:
                f.write(content)
            
            logger.debug(f"成功写入文本文件: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"写入文本文件失败 {file_path}: {e}")
            return False
    
    def read_json_file(self, file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        读取JSON文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            JSON数据或None
        """
        try:
            content = self.read_text_file(file_path)
            if content is None:
                return None
            
            data = json.loads(content)
            logger.debug(f"成功读取JSON文件: {file_path}")
            return data
            
        except Exception as e:
            logger.error(f"读取JSON文件失败 {file_path}: {e}")
            return None
    
    def write_json_file(self, file_path: Union[str, Path], data: Dict[str, Any], 
                       indent: int = 2, create_dirs: bool = True) -> bool:
        """
        写入JSON文件
        
        Args:
            file_path: 文件路径
            data: JSON数据
            indent: 缩进空格数
            create_dirs: 是否自动创建目录
            
        Returns:
            是否写入成功
        """
        try:
            content = json.dumps(data, indent=indent, ensure_ascii=False)
            return self.write_text_file(file_path, content, create_dirs=create_dirs)
            
        except Exception as e:
            logger.error(f"写入JSON文件失败 {file_path}: {e}")
            return False
    
    def backup_file(self, file_path: Union[str, Path], backup_suffix: str = None) -> Optional[Path]:
        """
        备份文件
        
        Args:
            file_path: 原文件路径
            backup_suffix: 备份后缀，默认使用时间戳
            
        Returns:
            备份文件路径或None
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"原文件不存在，无法备份: {file_path}")
                return None
            
            if backup_suffix is None:
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                backup_suffix = f".bak_{timestamp}"
            
            backup_path = file_path.with_suffix(file_path.suffix + backup_suffix)
            shutil.copy2(file_path, backup_path)
            
            logger.info(f"文件备份成功: {file_path} -> {backup_path}")
            return backup_path
            
        except Exception as e:
            logger.error(f"文件备份失败 {file_path}: {e}")
            return None
    
    def delete_file(self, file_path: Union[str, Path], backup_first: bool = True) -> bool:
        """
        删除文件
        
        Args:
            file_path: 文件路径
            backup_first: 是否先备份
            
        Returns:
            是否删除成功
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"文件不存在，无需删除: {file_path}")
                return True
            
            # 先备份
            if backup_first:
                backup_path = self.backup_file(file_path)
                if backup_path is None:
                    logger.warning(f"备份失败，取消删除: {file_path}")
                    return False
            
            file_path.unlink()
            logger.info(f"文件删除成功: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"文件删除失败 {file_path}: {e}")
            return False
    
    def list_files(self, directory: Union[str, Path], pattern: str = "*", 
                  recursive: bool = False) -> List[Path]:
        """
        列出目录中的文件
        
        Args:
            directory: 目录路径
            pattern: 文件模式，支持通配符
            recursive: 是否递归搜索
            
        Returns:
            文件路径列表
        """
        try:
            directory = Path(directory)
            
            if not directory.exists():
                logger.warning(f"目录不存在: {directory}")
                return []
            
            if recursive:
                files = list(directory.rglob(pattern))
            else:
                files = list(directory.glob(pattern))
            
            # 只返回文件，不包括目录
            files = [f for f in files if f.is_file()]
            
            logger.debug(f"找到{len(files)}个文件在目录 {directory}")
            return files
            
        except Exception as e:
            logger.error(f"列出文件失败 {directory}: {e}")
            return []
    
    def get_file_info(self, file_path: Union[str, Path]) -> Optional[Dict[str, Any]]:
        """
        获取文件信息
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典或None
        """
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.warning(f"文件不存在: {file_path}")
                return None
            
            stat = file_path.stat()
            
            info = {
                'path': str(file_path),
                'name': file_path.name,
                'size': stat.st_size,
                'created': datetime.fromtimestamp(stat.st_ctime),
                'modified': datetime.fromtimestamp(stat.st_mtime),
                'is_file': file_path.is_file(),
                'is_dir': file_path.is_dir(),
                'suffix': file_path.suffix,
                'parent': str(file_path.parent)
            }
            
            return info
            
        except Exception as e:
            logger.error(f"获取文件信息失败 {file_path}: {e}")
            return None
    
    def cleanup_old_files(self, directory: Union[str, Path], days_old: int = 30, 
                         pattern: str = "*.bak*") -> int:
        """
        清理旧文件
        
        Args:
            directory: 目录路径
            days_old: 文件天数阈值
            pattern: 文件模式
            
        Returns:
            清理的文件数量
        """
        try:
            directory = Path(directory)
            current_time = datetime.now()
            cutoff_time = current_time.timestamp() - (days_old * 24 * 3600)
            
            files = self.list_files(directory, pattern, recursive=True)
            cleaned_count = 0
            
            for file_path in files:
                try:
                    if file_path.stat().st_mtime < cutoff_time:
                        file_path.unlink()
                        cleaned_count += 1
                        logger.debug(f"清理旧文件: {file_path}")
                except Exception as e:
                    logger.warning(f"清理文件失败 {file_path}: {e}")
            
            logger.info(f"清理完成，删除了{cleaned_count}个旧文件")
            return cleaned_count
            
        except Exception as e:
            logger.error(f"清理旧文件失败: {e}")
            return 0


# 导出
__all__ = ['FileManager']
