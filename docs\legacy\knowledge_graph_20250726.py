#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
知识图谱系统
建立问题、解决方案、技术点之间的关联关系
"""

from typing import Dict, List, Set, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json

class RelationType(Enum):
    """关系类型"""
    CAUSES = "导致"
    SOLVES = "解决"
    RELATES_TO = "相关"
    DEPENDS_ON = "依赖"
    CONFLICTS_WITH = "冲突"
    SIMILAR_TO = "相似"
    PREREQUISITE = "前置条件"
    FOLLOWS = "后续"

@dataclass
class KnowledgeNode:
    """知识节点"""
    id: str
    title: str
    type: str  # problem, solution, technology, module, concept
    description: str
    tags: List[str]
    metadata: Dict[str, str]

@dataclass
class KnowledgeRelation:
    """知识关系"""
    from_node: str
    to_node: str
    relation_type: RelationType
    strength: float  # 0.0 - 1.0
    description: str
    metadata: Dict[str, str]

class KnowledgeGraph:
    """知识图谱"""
    
    def __init__(self):
        self.nodes: Dict[str, KnowledgeNode] = {}
        self.relations: List[KnowledgeRelation] = []
        self.adjacency_list: Dict[str, List[str]] = {}
        self._init_base_knowledge()
    
    def add_node(self, node: KnowledgeNode) -> bool:
        """添加知识节点"""
        try:
            self.nodes[node.id] = node
            if node.id not in self.adjacency_list:
                self.adjacency_list[node.id] = []
            return True
        except Exception as e:
            print(f"❌ 添加节点失败: {e}")
            return False
    
    def add_relation(self, relation: KnowledgeRelation) -> bool:
        """添加知识关系"""
        try:
            # 确保节点存在
            if relation.from_node not in self.nodes or relation.to_node not in self.nodes:
                return False
            
            self.relations.append(relation)
            
            # 更新邻接表
            if relation.to_node not in self.adjacency_list[relation.from_node]:
                self.adjacency_list[relation.from_node].append(relation.to_node)
            
            return True
        except Exception as e:
            print(f"❌ 添加关系失败: {e}")
            return False
    
    def find_related_nodes(self, node_id: str, relation_types: List[RelationType] = None,
                          max_depth: int = 2) -> List[Tuple[str, str, float]]:
        """
        查找相关节点
        
        Args:
            node_id: 起始节点ID
            relation_types: 关系类型过滤
            max_depth: 最大搜索深度
            
        Returns:
            [(节点ID, 关系路径, 相关度分数)]
        """
        if node_id not in self.nodes:
            return []
        
        visited = set()
        results = []
        
        def dfs(current_id: str, path: str, depth: int, score: float):
            if depth > max_depth or current_id in visited:
                return
            
            visited.add(current_id)
            
            if current_id != node_id:
                results.append((current_id, path, score))
            
            # 查找相关关系
            for relation in self.relations:
                if relation.from_node == current_id:
                    if relation_types is None or relation.relation_type in relation_types:
                        new_path = f"{path} -> {relation.relation_type.value}" if path else relation.relation_type.value
                        new_score = score * relation.strength
                        dfs(relation.to_node, new_path, depth + 1, new_score)
        
        dfs(node_id, "", 0, 1.0)
        
        # 按相关度排序
        results.sort(key=lambda x: x[2], reverse=True)
        return results[:10]  # 返回前10个最相关的
    
    def find_solution_path(self, problem_id: str) -> List[Tuple[str, str]]:
        """查找问题的解决路径"""
        solutions = []
        
        for relation in self.relations:
            if (relation.from_node == problem_id and 
                relation.relation_type == RelationType.SOLVES):
                solution_node = self.nodes.get(relation.to_node)
                if solution_node:
                    solutions.append((relation.to_node, solution_node.title))
        
        return solutions
    
    def suggest_similar_problems(self, problem_id: str) -> List[str]:
        """推荐相似问题"""
        if problem_id not in self.nodes:
            return []
        
        problem_node = self.nodes[problem_id]
        similar_problems = []
        
        # 基于标签相似度
        for node_id, node in self.nodes.items():
            if node_id != problem_id and node.type == "problem":
                common_tags = set(problem_node.tags) & set(node.tags)
                if len(common_tags) >= 2:  # 至少2个共同标签
                    similar_problems.append(node_id)
        
        # 基于关系相似度
        related_nodes = self.find_related_nodes(problem_id, [RelationType.SIMILAR_TO])
        for node_id, _, _ in related_nodes:
            if self.nodes[node_id].type == "problem":
                similar_problems.append(node_id)
        
        return list(set(similar_problems))[:5]
    
    def analyze_knowledge_gaps(self) -> Dict[str, List[str]]:
        """分析知识缺口"""
        gaps = {
            'isolated_nodes': [],  # 孤立节点
            'missing_solutions': [],  # 缺少解决方案的问题
            'weak_connections': []  # 连接较弱的节点
        }
        
        # 查找孤立节点
        for node_id, node in self.nodes.items():
            if not self.adjacency_list.get(node_id) and not any(
                r.to_node == node_id for r in self.relations
            ):
                gaps['isolated_nodes'].append(node_id)
        
        # 查找缺少解决方案的问题
        for node_id, node in self.nodes.items():
            if node.type == "problem":
                has_solution = any(
                    r.from_node == node_id and r.relation_type == RelationType.SOLVES
                    for r in self.relations
                )
                if not has_solution:
                    gaps['missing_solutions'].append(node_id)
        
        # 查找连接较弱的节点
        for node_id in self.nodes:
            connection_strength = sum(
                r.strength for r in self.relations 
                if r.from_node == node_id or r.to_node == node_id
            )
            if connection_strength < 1.0:
                gaps['weak_connections'].append(node_id)
        
        return gaps
    
    def export_graph(self, format: str = "json") -> str:
        """导出知识图谱"""
        if format == "json":
            graph_data = {
                'nodes': [
                    {
                        'id': node.id,
                        'title': node.title,
                        'type': node.type,
                        'description': node.description,
                        'tags': node.tags,
                        'metadata': node.metadata
                    }
                    for node in self.nodes.values()
                ],
                'relations': [
                    {
                        'from': rel.from_node,
                        'to': rel.to_node,
                        'type': rel.relation_type.value,
                        'strength': rel.strength,
                        'description': rel.description,
                        'metadata': rel.metadata
                    }
                    for rel in self.relations
                ]
            }
            return json.dumps(graph_data, ensure_ascii=False, indent=2)
        
        return ""
    
    def _init_base_knowledge(self):
        """初始化基础知识"""
        # 添加基础技术节点
        base_nodes = [
            KnowledgeNode(
                id="tech_regex",
                title="正则表达式",
                type="technology",
                description="用于模式匹配的强大工具",
                tags=["正则表达式", "模式匹配", "文本处理"],
                metadata={"complexity": "medium", "domain": "文本处理"}
            ),
            KnowledgeNode(
                id="tech_pytdx",
                title="pytdx数据接口",
                type="technology",
                description="通达信数据接口Python封装",
                tags=["pytdx", "数据获取", "股票数据"],
                metadata={"complexity": "medium", "domain": "数据处理"}
            ),
            KnowledgeNode(
                id="module_file_selector",
                title="智能文件选择器",
                type="module",
                description="智能选择和匹配文件的组件",
                tags=["文件处理", "智能选择", "模式匹配"],
                metadata={"file": "utils/smart_file_selector.py"}
            ),
            KnowledgeNode(
                id="concept_forward_adjustment",
                title="前复权处理",
                type="concept",
                description="股票价格的前复权计算方法",
                tags=["前复权", "股票数据", "价格调整"],
                metadata={"domain": "金融数据处理"}
            )
        ]
        
        for node in base_nodes:
            self.add_node(node)
        
        # 添加基础关系
        base_relations = [
            KnowledgeRelation(
                from_node="module_file_selector",
                to_node="tech_regex",
                relation_type=RelationType.DEPENDS_ON,
                strength=0.9,
                description="文件选择器依赖正则表达式进行模式匹配",
                metadata={}
            ),
            KnowledgeRelation(
                from_node="tech_pytdx",
                to_node="concept_forward_adjustment",
                relation_type=RelationType.RELATES_TO,
                strength=0.7,
                description="pytdx获取的数据需要进行前复权处理",
                metadata={}
            )
        ]
        
        for relation in base_relations:
            self.add_relation(relation)

def demonstrate_knowledge_graph():
    """演示知识图谱系统"""
    print("🚀 知识图谱系统演示")
    print("=" * 50)
    
    kg = KnowledgeGraph()
    
    # 添加问题节点
    problem_node = KnowledgeNode(
        id="prob_regex_timestamp",
        title="正则表达式无法匹配带时间戳文件名",
        type="problem",
        description="智能文件选择器无法识别带时间戳后缀的文件名",
        tags=["正则表达式", "文件匹配", "时间戳"],
        metadata={"severity": "medium", "domain": "文件处理"}
    )
    kg.add_node(problem_node)
    
    # 添加解决方案节点
    solution_node = KnowledgeNode(
        id="sol_regex_optional_group",
        title="使用可选非捕获组支持时间戳",
        type="solution",
        description="通过(?:（\\d{12}）)?模式支持可选时间戳",
        tags=["正则表达式", "非捕获组", "向后兼容"],
        metadata={"complexity": "low", "effectiveness": "high"}
    )
    kg.add_node(solution_node)
    
    # 添加关系
    kg.add_relation(KnowledgeRelation(
        from_node="sol_regex_optional_group",
        to_node="prob_regex_timestamp",
        relation_type=RelationType.SOLVES,
        strength=0.95,
        description="可选非捕获组完美解决时间戳匹配问题",
        metadata={}
    ))
    
    # 演示功能
    print(f"📊 知识图谱统计:")
    print(f"  节点数量: {len(kg.nodes)}")
    print(f"  关系数量: {len(kg.relations)}")
    
    print(f"\n🔍 相关节点查找:")
    related = kg.find_related_nodes("prob_regex_timestamp")
    for node_id, path, score in related[:3]:
        node = kg.nodes[node_id]
        print(f"  {node.title} (相关度: {score:.2f})")
    
    print(f"\n📋 知识缺口分析:")
    gaps = kg.analyze_knowledge_gaps()
    for gap_type, nodes in gaps.items():
        if nodes:
            print(f"  {gap_type}: {len(nodes)} 个")
    
    print("\n✅ 知识图谱系统初始化完成")

if __name__ == '__main__':
    demonstrate_knowledge_graph()
