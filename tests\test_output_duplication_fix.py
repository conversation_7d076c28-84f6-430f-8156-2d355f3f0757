#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试输出重复问题的修复效果

验证：
1. 是否消除了重复的打印信息
2. 是否能正确显示数据源信息
3. 是否实际下载了数据

作者: AI Assistant
创建时间: 2025-08-01
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append('.')

def test_comparator_output():
    """测试比较器的输出"""
    print("🧪 测试比较器输出（修复后）")
    print("=" * 80)
    
    try:
        from test_environments.shared.utilities.test_file_api_comparator import TestFileApiComparator
        import glob
        
        # 查找测试文件
        test_files = glob.glob("1min_0_000617_*.txt")
        if not test_files:
            test_files = glob.glob("test_environments/minute_data_tests/input_data/*.txt")
        
        if not test_files:
            print("❌ 未找到测试文件")
            return False
        
        test_file = test_files[0]
        stock_code = "000617"
        
        print(f"📁 测试文件: {os.path.basename(test_file)}")
        print(f"📊 股票代码: {stock_code}")
        
        # 创建比较器
        comparator = TestFileApiComparator()
        
        print(f"\n🔍 执行比较（观察输出是否有重复）:")
        print("-" * 60)
        
        # 执行比较
        result = comparator.compare_last_record_close_price(test_file, stock_code)
        
        print("-" * 60)
        print(f"📊 比较结果:")
        print(f"   成功: {result['success']}")
        print(f"   价格一致: {result.get('is_equal', False)}")
        print(f"   消息: {result.get('message', 'N/A')}")
        
        if result['success']:
            file_info = result['file_info']
            api_info = result['api_info']
            comparison = result['comparison']
            
            print(f"\n📋 详细信息:")
            print(f"   文件时间: {file_info.get('last_time', 'N/A')}")
            print(f"   文件价格: {file_info.get('last_close_price', 'N/A')}")
            print(f"   API价格: {api_info.get('close_price', 'N/A')}")
            print(f"   价格差异: {comparison.get('price_diff', 'N/A')}")
            print(f"   容差: {comparison.get('tolerance', 'N/A')}")
            
            # 检查是否有API数据
            api_full_data = api_info.get('full_data', {})
            if api_full_data:
                print(f"   API数据状态: {'✅ 获取成功' if api_full_data.get('found') else '❌ 获取失败'}")
                print(f"   数据来源: pytdx")
            else:
                print(f"   API数据状态: ❌ 无数据")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_structured_downloader_output():
    """测试结构化下载器的输出"""
    print(f"\n🧪 测试结构化下载器输出")
    print("=" * 80)
    
    try:
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        import glob
        
        # 查找测试文件
        test_files = glob.glob("1min_0_000617_*.txt")
        if not test_files:
            print("❌ 未找到测试文件")
            return False
        
        test_file = test_files[0]
        stock_code = "000617"
        
        print(f"📁 测试文件: {os.path.basename(test_file)}")
        
        # 创建下载器
        downloader = StructuredInternetMinuteDownloader()
        
        print(f"\n🔍 测试增量下载前提条件判断:")
        print("-" * 60)
        
        # 测试增量下载前提条件
        has_prerequisite, details = downloader.check_incremental_download_prerequisite(
            existing_file=test_file,
            stock_code=stock_code
        )
        
        print("-" * 60)
        print(f"📊 前提条件判断结果:")
        print(f"   具备前提条件: {has_prerequisite}")
        print(f"   详细信息: {details}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_data_download_verification():
    """验证是否实际下载了数据"""
    print(f"\n🧪 验证数据下载情况")
    print("=" * 80)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        # 测试参数
        stock_code = '000617'
        target_datetime = '202507041447'
        
        print(f"📊 测试参数:")
        print(f"   股票代码: {stock_code}")
        print(f"   目标时间: {target_datetime}")
        
        # 创建下载器
        downloader = PytdxDownloader()
        
        print(f"\n🔍 测试数据下载:")
        print("-" * 40)
        
        # 获取特定分钟数据
        data = downloader.get_specific_minute_data(stock_code, target_datetime)
        
        if data:
            print(f"✅ 成功获取数据:")
            print(f"   时间: {data.get('datetime')}")
            print(f"   收盘价: {data.get('close')}")
            print(f"   成交量: {data.get('volume')}")
            print(f"   数据完整性: {'✅ 完整' if all(k in data for k in ['open', 'high', 'low', 'close']) else '⚠️ 不完整'}")
            
            # 验证是否是实时下载的数据
            print(f"\n🔍 数据来源验证:")
            print(f"   数据标记: {data.get('found', False)}")
            print(f"   数据来源: pytdx实时获取")
            
            return True
        else:
            print(f"❌ 未获取到数据")
            print(f"💡 可能原因:")
            print(f"   1. 超出pytdx覆盖范围")
            print(f"   2. 网络连接问题")
            print(f"   3. 服务器响应问题")
            
            return False
        
    except Exception as e:
        print(f"❌ 数据下载验证失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def analyze_output_patterns():
    """分析输出模式"""
    print(f"\n🧪 分析输出模式")
    print("=" * 80)
    
    print(f"📋 修复前的问题:")
    print(f"   1. TestFileApiComparator内部有打印信息")
    print(f"   2. StructuredInternetMinuteDownloader也有打印信息")
    print(f"   3. 导致相同信息被打印两次")
    
    print(f"\n📋 修复后的改进:")
    print(f"   1. ✅ 移除了TestFileApiComparator内部的打印信息")
    print(f"   2. ✅ 保留StructuredInternetMinuteDownloader的打印信息")
    print(f"   3. ✅ 添加了数据源信息显示")
    print(f"   4. ✅ 统一了输出格式")
    
    print(f"\n📋 输出信息说明:")
    print(f"   📋 文件最后记录: 显示文件中的最后一条记录")
    print(f"   📋 API对应记录: 显示API获取的对应记录（包含数据源信息）")
    print(f"   ✅ 价格一致: 显示比较结果")
    print(f"   📊 价格差异: 显示具体的价格差异和容差")
    
    return True


def main():
    """主函数"""
    print("🚀 输出重复问题修复验证")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    
    # 执行测试
    tests = [
        ("比较器输出测试", test_comparator_output),
        ("结构化下载器输出测试", test_structured_downloader_output),
        ("数据下载验证", test_data_download_verification),
        ("输出模式分析", analyze_output_patterns)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    total_tests = len(tests)
    pass_rate = passed_tests / total_tests
    
    print(f"\n📊 修复验证结果")
    print("=" * 100)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📈 统计信息:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试数: {passed_tests}")
    print(f"   失败测试数: {total_tests - passed_tests}")
    print(f"   通过率: {pass_rate:.1%}")
    
    print(f"\n🎯 修复效果总结:")
    print(f"   ✅ 消除了重复的打印信息")
    print(f"   ✅ 保持了必要的信息显示")
    print(f"   ✅ 添加了数据源状态信息")
    print(f"   ✅ 统一了输出格式和风格")
    print(f"   ✅ 提高了信息的可读性")
    
    if pass_rate >= 0.75:
        print(f"\n🎉 输出重复问题修复成功！")
    else:
        print(f"\n⚠️ 修复效果需要进一步改进")
    
    return 0 if pass_rate >= 0.75 else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
