#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试pytdx数据识别问题
检查API返回的原始数据中是否真的缺少0331和0704的数据
"""

import os
import sys
import pandas as pd

# 设置强制测试环境标识
os.environ['FORCE_TEST_ENV'] = '1'
os.environ['AUGMENT_MODE'] = '1'

def debug_pytdx_data():
    """调试pytdx数据识别问题"""
    print("🔍 调试pytdx数据识别问题")
    print("=" * 50)
    
    try:
        # 1. 模拟pytdx数据下载
        print("1. 模拟pytdx数据下载...")
        from utils.pytdx_data_repairer import PytdxDataRepairer
        
        repairer = PytdxDataRepairer()
        
        # 模拟下载参数
        stock_code = "000617"
        start_time = "202503200931"
        end_time = "202508140000"
        
        print(f"下载参数: {stock_code}, {start_time} - {end_time}")
        
        # 执行下载
        full_data = repairer._call_pytdx_single_download(stock_code, start_time)

        download_result = {
            'success': full_data is not None,
            'data': full_data
        }
        
        if download_result['success']:
            full_data = download_result['data']
            print(f"✅ 下载成功: {len(full_data)} 条记录")
            
            # 2. 分析数据结构
            print(f"\n2. 分析数据结构...")
            print(f"数据列名: {list(full_data.columns)}")
            print(f"数据类型:")
            for col in full_data.columns:
                print(f"  {col}: {full_data[col].dtype}")
            
            # 3. 检查时间戳列
            print(f"\n3. 检查时间戳列...")
            time_columns = [col for col in full_data.columns if 'time' in col.lower() or 'date' in col.lower()]
            print(f"可能的时间列: {time_columns}")
            
            # 检查datetime_int列
            if 'datetime_int' in full_data.columns:
                print(f"✅ 找到datetime_int列")
                datetime_values = full_data['datetime_int'].unique()
                print(f"时间戳数量: {len(datetime_values)}")
                print(f"时间戳范围: {min(datetime_values)} - {max(datetime_values)}")
                
                # 4. 检查特定日期的数据
                print(f"\n4. 检查特定日期的数据...")
                target_dates = ['20250331', '20250704', '20250320']
                
                for date in target_dates:
                    # 检查该日期的数据
                    date_mask = full_data['datetime_int'].astype(str).str.startswith(date)
                    date_data = full_data[date_mask]
                    
                    print(f"{date}: {len(date_data)} 条记录")
                    if len(date_data) > 0:
                        print(f"  时间范围: {date_data['datetime_int'].min()} - {date_data['datetime_int'].max()}")
                        # 显示前几条
                        print(f"  前3条时间戳: {list(date_data['datetime_int'].head(3))}")
                    else:
                        print(f"  ❌ 该日期无数据")
                
                # 5. 检查缺失时间点是否真的在数据中
                print(f"\n5. 检查具体的缺失时间点...")
                missing_timestamps = [
                    202503311015, 202503311016, 202503311017,  # 0331的几个时间点
                    202507041430, 202507041431, 202507041432   # 0704的几个时间点
                ]
                
                for ts in missing_timestamps:
                    exists = ts in full_data['datetime_int'].values
                    print(f"  {ts}: {'存在' if exists else '不存在'}")
                
                # 6. 分析数据分布
                print(f"\n6. 分析数据日期分布...")
                full_data['date'] = full_data['datetime_int'].astype(str).str[:8]
                date_counts = full_data['date'].value_counts().sort_index()
                
                print(f"总交易日数: {len(date_counts)}")
                print(f"前10个交易日:")
                for date, count in date_counts.head(10).items():
                    print(f"  {date}: {count} 条记录")
                
                print(f"后10个交易日:")
                for date, count in date_counts.tail(10).items():
                    print(f"  {date}: {count} 条记录")
                
                # 检查0331和0704是否在分布中
                if '20250331' in date_counts:
                    print(f"✅ 20250331 在数据中: {date_counts['20250331']} 条记录")
                else:
                    print(f"❌ 20250331 不在数据中")
                
                if '20250704' in date_counts:
                    print(f"✅ 20250704 在数据中: {date_counts['20250704']} 条记录")
                else:
                    print(f"❌ 20250704 不在数据中")
                    
            else:
                print(f"❌ 未找到datetime_int列")
                # 尝试其他可能的时间列
                for col in time_columns:
                    print(f"检查列 {col}:")
                    sample_values = full_data[col].head(10).tolist()
                    print(f"  样本值: {sample_values}")
        
        else:
            print(f"❌ 下载失败: {download_result.get('error', '未知错误')}")
        
        print(f"\n🎯 调试完成")
        
    except Exception as e:
        print(f"❌ 调试过程异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_pytdx_data()
