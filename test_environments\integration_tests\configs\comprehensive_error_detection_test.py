#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
综合错误检测警醒测试

基于知识库规则进行全面的错误检测和代码质量验证
遵循testing_quality_rules.md和debugging_knowledge_base.md的指导原则

位置: test_environments/integration_tests/configs/
作者: AI Assistant
创建时间: 2025-07-31
"""

import sys
import os
import importlib
import traceback
import subprocess
from datetime import datetime
from typing import Dict, List, Tuple, Any

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_stats_table,
    print_completion
)


class ComprehensiveErrorDetector:
    """综合错误检测器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        self.error_count = 0
        self.warning_count = 0
        
    def test_critical_imports(self) -> bool:
        """测试关键模块导入 - 遵循边界条件强制测试原则"""
        print_step("关键模块导入测试", 1, 7)
        
        critical_modules = [
            'main',
            'core.application',
            'core.config_manager',
            'utils.structured_internet_minute_downloader',
            'utils.structured_output_formatter',
            'utils.smart_file_selector',
            'utils.missing_data_processor',
            'utils.pytdx_downloader',
            'utils.enhanced_error_handler'
        ]
        
        success_count = 0
        
        for module_name in critical_modules:
            try:
                # 临时切换到项目根目录
                original_cwd = os.getcwd()
                os.chdir(self.project_root)
                
                try:
                    module = importlib.import_module(module_name)
                    print_result(f"模块 {module_name} 导入成功", True, level=2)
                    success_count += 1
                except Exception as e:
                    print_error(f"模块 {module_name} 导入失败: {e}", level=2)
                    self.error_count += 1
                finally:
                    os.chdir(original_cwd)
                    
            except Exception as e:
                print_error(f"测试模块 {module_name} 时发生异常: {e}", level=2)
                self.error_count += 1
        
        success_rate = success_count / len(critical_modules)
        overall_success = success_rate >= 0.9  # 90%以上成功率
        
        print_info(f"导入成功率: {success_rate:.1%} ({success_count}/{len(critical_modules)})", level=2)
        
        return overall_success
    
    def test_core_functionality(self) -> bool:
        """测试核心功能 - 遵循实际文件验证原则"""
        print_step("核心功能测试", 2, 7)
        
        try:
            # 测试结构化输出格式器
            from utils.structured_output_formatter import StructuredOutputFormatter
            formatter = StructuredOutputFormatter()
            
            # 测试基本功能
            formatter.print_info("测试信息输出")
            print_result("结构化输出格式器功能正常", True, level=2)
            
            # 测试智能文件选择器
            from utils.smart_file_selector import SmartFileSelector
            selector = SmartFileSelector(".")
            print_result("智能文件选择器创建成功", True, level=2)
            
            # 测试缺失数据处理器
            from utils.missing_data_processor import MissingDataProcessor
            processor = MissingDataProcessor()
            
            # 验证240行标准
            if hasattr(processor, 'STANDARD_MINUTES_PER_DAY'):
                if processor.STANDARD_MINUTES_PER_DAY == 240:
                    print_result("240行标准验证通过", True, level=2)
                else:
                    print_error(f"240行标准错误: 期望240，实际{processor.STANDARD_MINUTES_PER_DAY}", level=2)
                    self.error_count += 1
                    return False
            else:
                print_error("缺少STANDARD_MINUTES_PER_DAY常量", level=2)
                self.error_count += 1
                return False
            
            return True
            
        except Exception as e:
            print_error(f"核心功能测试失败: {e}", level=2)
            self.error_count += 1
            return False
    
    def test_configuration_integrity(self) -> bool:
        """测试配置完整性 - 遵循严格需求验证原则"""
        print_step("配置完整性测试", 3, 7)
        
        try:
            # 检查关键配置文件
            config_files = [
                'user_config.py',
                'docs/output_format_standards.md',
                'docs/output_format_migration_progress.md'
            ]
            
            for config_file in config_files:
                config_path = os.path.join(self.project_root, config_file)
                if os.path.exists(config_path):
                    print_result(f"配置文件存在: {config_file}", True, level=2)
                else:
                    print_error(f"配置文件缺失: {config_file}", level=2)
                    self.error_count += 1
                    return False
            
            # 测试配置管理器
            from core.config_manager import ConfigManager
            config_manager = ConfigManager()
            print_result("配置管理器创建成功", True, level=2)
            
            return True
            
        except Exception as e:
            print_error(f"配置完整性测试失败: {e}", level=2)
            self.error_count += 1
            return False
    
    def test_file_naming_compliance(self) -> bool:
        """测试文件命名规范 - 遵循文件命名规则测试"""
        print_step("文件命名规范测试", 4, 7)
        
        try:
            # 检查1分钟数据文件命名
            minute_files = []
            for root, dirs, files in os.walk(self.project_root):
                for file in files:
                    if file.startswith('1min_0_') and file.endswith('.txt'):
                        minute_files.append(file)
            
            print_info(f"找到 {len(minute_files)} 个1分钟数据文件", level=2)
            
            # 验证命名格式
            naming_errors = 0
            for file in minute_files[:5]:  # 检查前5个文件
                # 检查是否以"1min"开头而不是"1"
                if not file.startswith('1min_'):
                    print_error(f"文件命名错误: {file} 应以'1min_'开头", level=2)
                    naming_errors += 1
                    self.error_count += 1
                else:
                    print_result(f"文件命名正确: {file}", True, level=2)
            
            return naming_errors == 0
            
        except Exception as e:
            print_error(f"文件命名规范测试失败: {e}", level=2)
            self.error_count += 1
            return False
    
    def test_error_handling_robustness(self) -> bool:
        """测试错误处理健壮性"""
        print_step("错误处理健壮性测试", 5, 7)
        
        try:
            from utils.enhanced_error_handler import get_error_handler, ErrorCategory
            
            error_handler = get_error_handler()
            print_result("错误处理器获取成功", True, level=2)
            
            # 测试错误分类
            test_error = Exception("测试错误")
            error_id = error_handler.log_error(
                test_error,
                ErrorCategory.SYSTEM,
                {"test": "context"},
                operation="测试操作"
            )
            
            if error_id:
                print_result("错误处理功能正常", True, level=2)
                return True
            else:
                print_error("错误处理功能异常", level=2)
                self.error_count += 1
                return False
                
        except Exception as e:
            print_error(f"错误处理健壮性测试失败: {e}", level=2)
            self.error_count += 1
            return False
    
    def test_data_integrity_standards(self) -> bool:
        """测试数据完整性标准"""
        print_step("数据完整性标准测试", 6, 7)
        
        try:
            # 检查测试数据文件
            test_data_dir = os.path.join(self.project_root, 'test_environments', 'minute_data_tests', 'input_data')
            
            if not os.path.exists(test_data_dir):
                print_warning("测试数据目录不存在", level=2)
                self.warning_count += 1
                return True
            
            test_files = [f for f in os.listdir(test_data_dir) if f.endswith('.txt')]
            print_info(f"找到 {len(test_files)} 个测试数据文件", level=2)
            
            # 验证测试文件格式
            for test_file in test_files[:2]:  # 检查前2个文件
                file_path = os.path.join(test_data_dir, test_file)
                
                try:
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    if len(lines) > 1:
                        header = lines[0].strip()
                        if '股票编码|时间|' in header:
                            print_result(f"测试文件格式正确: {test_file}", True, level=2)
                        else:
                            print_error(f"测试文件格式错误: {test_file}", level=2)
                            self.error_count += 1
                            return False
                    else:
                        print_warning(f"测试文件内容为空: {test_file}", level=2)
                        self.warning_count += 1
                        
                except Exception as e:
                    print_error(f"读取测试文件失败: {test_file} - {e}", level=2)
                    self.error_count += 1
                    return False
            
            return True
            
        except Exception as e:
            print_error(f"数据完整性标准测试失败: {e}", level=2)
            self.error_count += 1
            return False
    
    def test_runtime_execution(self) -> bool:
        """测试运行时执行 - 最关键的测试"""
        print_step("运行时执行测试", 7, 7)
        
        try:
            # 尝试运行主程序的初始化部分
            original_cwd = os.getcwd()
            os.chdir(self.project_root)
            
            try:
                from core.application import MythQuantApplication
                from core.config_manager import ConfigManager
                
                # 创建配置管理器
                config_manager = ConfigManager()
                print_result("配置管理器初始化成功", True, level=2)
                
                # 创建应用程序实例（但不运行任务）
                app = MythQuantApplication(config_manager)
                print_result("应用程序实例创建成功", True, level=2)
                
                return True
                
            finally:
                os.chdir(original_cwd)
                
        except Exception as e:
            print_error(f"运行时执行测试失败: {e}", level=2)
            print_error(f"详细错误: {traceback.format_exc()}", level=2)
            self.error_count += 1
            return False
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        print_banner("综合错误检测警醒测试", "基于知识库规则的全面代码质量验证")
        
        print_info(f"项目根目录: {self.project_root}")
        print_info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print_main_process("执行综合错误检测")
        
        # 执行所有测试
        test_methods = [
            ('关键模块导入', self.test_critical_imports),
            ('核心功能', self.test_core_functionality),
            ('配置完整性', self.test_configuration_integrity),
            ('文件命名规范', self.test_file_naming_compliance),
            ('错误处理健壮性', self.test_error_handling_robustness),
            ('数据完整性标准', self.test_data_integrity_standards),
            ('运行时执行', self.test_runtime_execution)
        ]
        
        results = {}
        passed_tests = 0
        
        for test_name, test_method in test_methods:
            try:
                result = test_method()
                results[test_name] = result
                if result:
                    passed_tests += 1
            except Exception as e:
                print_error(f"测试 {test_name} 执行异常: {e}")
                results[test_name] = False
                self.error_count += 1
        
        # 生成测试报告
        total_tests = len(test_methods)
        pass_rate = passed_tests / total_tests
        
        print_main_process("测试结果汇总")
        
        # 显示详细结果
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print_result(f"{test_name}: {status}", result, level=1)
        
        # 显示统计信息
        stats = {
            "总测试数": total_tests,
            "通过测试数": passed_tests,
            "失败测试数": total_tests - passed_tests,
            "通过率": f"{pass_rate:.1%}",
            "错误数量": self.error_count,
            "警告数量": self.warning_count
        }
        
        print_stats_table("测试统计", stats)
        
        # 判断整体结果
        overall_success = pass_rate >= 0.85 and self.error_count == 0
        
        completion_message = "所有测试通过，代码质量良好！" if overall_success else "发现问题，需要进一步修复"
        print_completion(completion_message, overall_success, stats)
        
        return {
            'overall_success': overall_success,
            'pass_rate': pass_rate,
            'error_count': self.error_count,
            'warning_count': self.warning_count,
            'detailed_results': results
        }


def main():
    """主函数"""
    detector = ComprehensiveErrorDetector()
    result = detector.run_comprehensive_test()
    
    # 根据测试结果返回适当的退出码
    exit_code = 0 if result['overall_success'] else 1
    
    print_info(f"测试完成，退出码: {exit_code}")
    
    return exit_code


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
