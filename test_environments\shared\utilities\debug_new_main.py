#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试新主程序的问题
"""

import sys
import os
import traceback

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def test_imports():
    """测试导入"""
    print("🔍 测试模块导入...")
    
    try:
        print("  测试 core.config_manager...")
        from core.config_manager import ConfigManager
        print("  ✅ ConfigManager 导入成功")
        
        config_manager = ConfigManager()
        print("  ✅ ConfigManager 实例化成功")
        
    except Exception as e:
        print(f"  ❌ ConfigManager 失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("  测试 core.stock_processor...")
        from core.stock_processor import StockDataProcessor
        print("  ✅ StockDataProcessor 导入成功")
        
    except Exception as e:
        print(f"  ❌ StockDataProcessor 失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("  测试 core.task_manager...")
        from core.task_manager import TaskManager
        print("  ✅ TaskManager 导入成功")
        
    except Exception as e:
        print(f"  ❌ TaskManager 失败: {e}")
        traceback.print_exc()
        return False
    
    try:
        print("  测试 core.application...")
        from core.application import MythQuantApplication
        print("  ✅ MythQuantApplication 导入成功")
        
    except Exception as e:
        print(f"  ❌ MythQuantApplication 失败: {e}")
        traceback.print_exc()
        return False
    
    return True

def test_initialization():
    """测试初始化"""
    print("\n🔧 测试组件初始化...")
    
    try:
        from core.config_manager import ConfigManager
        from core.application import MythQuantApplication
        
        print("  初始化 ConfigManager...")
        config_manager = ConfigManager()
        print("  ✅ ConfigManager 初始化成功")
        
        print("  初始化 MythQuantApplication...")
        app = MythQuantApplication(config_manager)
        print("  ✅ MythQuantApplication 初始化成功")
        
        return True
        
    except Exception as e:
        print(f"  ❌ 初始化失败: {e}")
        traceback.print_exc()
        return False

def test_main_entry():
    """测试主程序入口"""
    print("\n🚀 测试主程序入口...")
    
    try:
        import main
        print("  ✅ main 模块导入成功")
        
        print("  运行 main.main()...")
        result = main.main()
        print(f"  ✅ main.main() 执行完成，返回码: {result}")
        
        return result == 0
        
    except Exception as e:
        print(f"  ❌ 主程序执行失败: {e}")
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🔍 新主程序调试工具")
    print("=" * 50)
    
    # 测试导入
    if not test_imports():
        print("\n❌ 导入测试失败")
        return 1
    
    # 测试初始化
    if not test_initialization():
        print("\n❌ 初始化测试失败")
        return 1
    
    # 测试主程序入口
    if not test_main_entry():
        print("\n❌ 主程序测试失败")
        return 1
    
    print("\n✅ 所有测试通过")
    return 0

if __name__ == '__main__':
    sys.exit(main())
