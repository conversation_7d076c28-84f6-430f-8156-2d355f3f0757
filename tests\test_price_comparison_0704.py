#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试202507041447时间点的价格比较
验证现有文件与API下载文件中同一时间点的价格是否一致
"""

import os
import glob

def find_file_with_pattern(pattern):
    """查找匹配模式的文件"""
    files = glob.glob(pattern)
    if files:
        # 返回最新的文件
        return max(files, key=os.path.getmtime)
    return None

def get_price_at_time(filepath, target_time):
    """获取文件中指定时间点的价格"""
    if not os.path.exists(filepath):
        return None
    
    with open(filepath, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    for line in lines:
        line = line.strip()
        if not line or line.startswith('股票编码'):
            continue
        
        parts = line.split('|')
        if len(parts) >= 5:
            try:
                file_time = parts[1]
                if file_time == target_time:
                    return {
                        'time': file_time,
                        'close_price': float(parts[3]),
                        'adj_close_price': float(parts[4])
                    }
            except (ValueError, IndexError):
                continue
    
    return None

def main():
    print("=" * 80)
    print("测试202507041447时间点的价格比较")
    print("=" * 80)
    
    target_time = "202507041500"  # 现有文件的最后一条记录时间
    
    # 1. 查找现有文件（包含7月4日数据的文件）
    print("1️⃣ 查找现有文件")
    print("-" * 40)
    
    # 明确指定现有文件（用户提到的包含历史数据的文件）
    existing_candidates = [
        "H:/MPV1.17/T0002/signals/TestCase/01/test_1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt",
        "H:/MPV1.17/T0002/signals/1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt",
        "1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
    ]

    existing_file = None
    for candidate in existing_candidates:
        if os.path.exists(candidate):
            existing_file = candidate
            print(f"✅ 找到现有文件: {os.path.basename(existing_file)}")
            break
    
    if not existing_file:
        print("❌ 未找到包含7月4日数据的现有文件")
        return
    
    # 2. 查找API下载的文件（7月4日的数据）
    print("\n2️⃣ 查找API下载的文件")
    print("-" * 40)
    
    # 明确指定API下载的文件（最新生成的7月4日数据）
    api_candidates = [
        "H:/MPV1.17/T0002/signals/1min_0_000617_20250704-20250704_来源互联网（202507290029）.txt",
        "1min_0_000617_20250704-20250704_来源互联网（202507290029）.txt"
    ]

    api_file = None
    for candidate in api_candidates:
        if os.path.exists(candidate):
            api_file = candidate
            print(f"✅ 找到API文件: {os.path.basename(api_file)}")
            break

    # 如果找不到指定文件，尝试查找最新的7月4日文件
    if not api_file:
        api_patterns = [
            "H:/MPV1.17/T0002/signals/1min_0_000617_20250704-20250704*.txt",
            "1min_0_000617_20250704-20250704*.txt"
        ]

        for pattern in api_patterns:
            api_file = find_file_with_pattern(pattern)
            if api_file:
                print(f"✅ 找到API文件: {os.path.basename(api_file)}")
                break
    
    if not api_file:
        print("❌ 未找到API下载的7月4日文件")
        return
    
    # 3. 比较同一时间点的价格
    print("\n3️⃣ 比较同一时间点的价格")
    print("-" * 40)
    
    existing_data = get_price_at_time(existing_file, target_time)
    api_data = get_price_at_time(api_file, target_time)
    
    if existing_data and api_data:
        print(f"📊 时间点: {target_time}")
        print(f"📋 现有文件数据:")
        print(f"   收盘价: {existing_data['close_price']}")
        print(f"   前复权价: {existing_data['adj_close_price']}")
        print(f"📋 API文件数据:")
        print(f"   收盘价: {api_data['close_price']}")
        print(f"   前复权价: {api_data['adj_close_price']}")
        
        # 比较前复权价格
        price_diff = abs(existing_data['adj_close_price'] - api_data['adj_close_price'])
        print(f"📊 前复权价格差异: {price_diff:.6f}")
        
        if price_diff < 0.001:
            print("🎯 结论: ✅ 价格一致，无分红配股影响")
        else:
            print("🎯 结论: ❌ 价格不一致，存在分红配股影响")
            print("💡 建议: 需要全量重新下载，不能使用增量下载")
    
    elif existing_data:
        print(f"✅ 现有文件中找到数据: 前复权价={existing_data['adj_close_price']}")
        print(f"❌ API文件中未找到对应时间点的数据")
    
    elif api_data:
        print(f"❌ 现有文件中未找到对应时间点的数据")
        print(f"✅ API文件中找到数据: 前复权价={api_data['adj_close_price']}")
    
    else:
        print("❌ 两个文件中都未找到对应时间点的数据")
    
    print("\n" + "=" * 80)

if __name__ == "__main__":
    main()
