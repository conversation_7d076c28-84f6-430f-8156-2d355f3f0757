#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构分析脚本

分析当前项目结构问题并提供清理建议
"""

import os
from pathlib import Path
from collections import defaultdict
import json
from datetime import datetime


class ProjectStructureAnalyzer:
    """项目结构分析器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.analysis_results = {}
    
    def analyze(self):
        """执行完整分析"""
        print("🔍 MythQuant 项目结构分析")
        print("=" * 50)
        
        # 1. 分析根目录混乱程度
        self.analyze_root_directory()
        
        # 2. 分析文件分类
        self.analyze_file_categories()
        
        # 3. 分析目录结构
        self.analyze_directory_structure()
        
        # 4. 识别问题文件
        self.identify_problematic_files()
        
        # 5. 生成清理建议
        self.generate_cleanup_recommendations()
        
        # 6. 生成分析报告
        self.generate_analysis_report()
    
    def analyze_root_directory(self):
        """分析根目录"""
        print("\n📁 分析根目录...")
        
        root_files = [f for f in self.project_root.iterdir() if f.is_file()]
        root_dirs = [d for d in self.project_root.iterdir() if d.is_dir() and not d.name.startswith('.')]
        
        print(f"   📊 根目录文件数: {len(root_files)}")
        print(f"   📊 根目录子目录数: {len(root_dirs)}")
        
        # 分析根目录文件类型
        file_types = defaultdict(list)
        for file_path in root_files:
            suffix = file_path.suffix.lower()
            file_types[suffix].append(file_path.name)
        
        print("   📊 根目录文件类型:")
        for suffix, files in sorted(file_types.items()):
            print(f"      {suffix or '(无扩展名)'}: {len(files)} 个")
            if len(files) <= 5:
                for file_name in files:
                    print(f"        - {file_name}")
            else:
                print(f"        - {files[0]} ... (共{len(files)}个)")
        
        self.analysis_results['root_analysis'] = {
            'file_count': len(root_files),
            'directory_count': len(root_dirs),
            'file_types': {k: len(v) for k, v in file_types.items()}
        }
    
    def analyze_file_categories(self):
        """分析文件分类"""
        print("\n📋 分析文件分类...")
        
        categories = {
            '核心兼容性模块': ['*_compatibility.py'],
            '配置文件': ['user_config.py', '*.yml', '*.yaml', 'pyproject.toml', 'setup.py', 'requirements.txt'],
            '主程序': ['main.py', 'main_*.py'],
            '旧版本功能模块': ['func*.py', 'readTDX*.py', 'read_*.py'],
            '测试文件': ['test_*.py', '*test*.py'],
            '工具脚本': ['create_*.py', 'install_*.py', 'fix_*.py', 'migrate_*.py', 'prepare_*.py'],
            '文档': ['*.md', 'README*'],
            '缓存和临时': ['*.pyc', 'cache_*.json', '__pycache__'],
            '归档文件': ['*.rar', '*.zip', '*.backup*'],
            '其他Python文件': ['*.py']
        }
        
        categorized_files = defaultdict(list)
        uncategorized_files = []
        
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file():
                categorized = False
                for category, patterns in categories.items():
                    for pattern in patterns:
                        if file_path.match(pattern):
                            categorized_files[category].append(str(file_path.relative_to(self.project_root)))
                            categorized = True
                            break
                    if categorized:
                        break
                
                if not categorized:
                    uncategorized_files.append(str(file_path.relative_to(self.project_root)))
        
        print("   📊 文件分类统计:")
        for category, files in categorized_files.items():
            print(f"      {category}: {len(files)} 个")
        
        if uncategorized_files:
            print(f"      未分类文件: {len(uncategorized_files)} 个")
        
        self.analysis_results['file_categories'] = {
            k: len(v) for k, v in categorized_files.items()
        }
        self.analysis_results['uncategorized_count'] = len(uncategorized_files)
    
    def analyze_directory_structure(self):
        """分析目录结构"""
        print("\n🏗️ 分析目录结构...")
        
        # 分析现有目录
        existing_dirs = {}
        for item in self.project_root.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                file_count = len(list(item.rglob('*')))
                existing_dirs[item.name] = file_count
        
        print("   📊 现有目录结构:")
        for dir_name, count in sorted(existing_dirs.items()):
            print(f"      {dir_name}/: {count} 个文件")
        
        # 检查是否符合现代化架构
        expected_dirs = ['src', 'tests', 'docs', 'config', 'tools']
        missing_dirs = [d for d in expected_dirs if d not in existing_dirs]
        
        if missing_dirs:
            print(f"   ⚠️ 缺少推荐目录: {missing_dirs}")
        
        self.analysis_results['directory_structure'] = existing_dirs
        self.analysis_results['missing_directories'] = missing_dirs
    
    def identify_problematic_files(self):
        """识别问题文件"""
        print("\n🚨 识别问题文件...")
        
        problems = {
            '根目录Python文件过多': [],
            '重复或冗余文件': [],
            '临时文件未清理': [],
            '备份文件过多': [],
            '缓存文件': []
        }
        
        # 检查根目录Python文件
        root_py_files = [f for f in self.project_root.iterdir() 
                        if f.is_file() and f.suffix == '.py']
        
        # 排除核心文件
        core_files = ['main.py', 'user_config.py'] + [f.name for f in self.project_root.glob('*_compatibility.py')]
        
        for py_file in root_py_files:
            if py_file.name not in core_files:
                problems['根目录Python文件过多'].append(py_file.name)
        
        # 检查重复文件
        for pattern in ['*_backup*', '*.backup*', '*_bak*']:
            for file_path in self.project_root.glob(pattern):
                problems['备份文件过多'].append(file_path.name)
        
        # 检查临时文件
        for pattern in ['*.pyc', 'cache_*.json', '*.tmp']:
            for file_path in self.project_root.rglob(pattern):
                problems['临时文件未清理'].append(str(file_path.relative_to(self.project_root)))
        
        # 检查缓存目录
        pycache_dirs = list(self.project_root.rglob('__pycache__'))
        if pycache_dirs:
            problems['缓存文件'] = [str(d.relative_to(self.project_root)) for d in pycache_dirs]
        
        print("   📊 发现的问题:")
        for problem_type, files in problems.items():
            if files:
                print(f"      {problem_type}: {len(files)} 个")
                if len(files) <= 3:
                    for file_name in files:
                        print(f"        - {file_name}")
                else:
                    print(f"        - {files[0]} ... (共{len(files)}个)")
        
        self.analysis_results['problems'] = {k: len(v) for k, v in problems.items()}
    
    def generate_cleanup_recommendations(self):
        """生成清理建议"""
        print("\n💡 生成清理建议...")
        
        recommendations = []
        
        # 基于分析结果生成建议
        root_file_count = self.analysis_results.get('root_analysis', {}).get('file_count', 0)
        if root_file_count > 10:
            recommendations.append({
                'priority': 'HIGH',
                'action': '整理根目录文件',
                'description': f'根目录有{root_file_count}个文件，建议只保留核心文件',
                'suggested_files_to_keep': [
                    'main.py',
                    'user_config.py', 
                    '*_compatibility.py',
                    'requirements.txt',
                    'README.md'
                ]
            })
        
        # 检查是否有src目录
        if 'src' not in self.analysis_results.get('directory_structure', {}):
            recommendations.append({
                'priority': 'MEDIUM',
                'action': '创建src目录结构',
                'description': '建议创建src/mythquant目录存放新架构代码'
            })
        
        # 检查测试目录
        if 'tests' not in self.analysis_results.get('directory_structure', {}):
            recommendations.append({
                'priority': 'MEDIUM', 
                'action': '整理测试文件',
                'description': '建议将所有测试文件移动到tests目录'
            })
        
        # 清理建议
        problems = self.analysis_results.get('problems', {})
        if problems.get('临时文件未清理', 0) > 0:
            recommendations.append({
                'priority': 'LOW',
                'action': '清理临时文件',
                'description': '删除__pycache__、*.pyc等临时文件'
            })
        
        if problems.get('备份文件过多', 0) > 5:
            recommendations.append({
                'priority': 'LOW',
                'action': '整理备份文件',
                'description': '将备份文件移动到专门的backup目录'
            })
        
        print("   📋 清理建议 (按优先级排序):")
        for i, rec in enumerate(sorted(recommendations, key=lambda x: {'HIGH': 1, 'MEDIUM': 2, 'LOW': 3}[x['priority']]), 1):
            priority_icon = {'HIGH': '🔴', 'MEDIUM': '🟡', 'LOW': '🟢'}[rec['priority']]
            print(f"      {i}. {priority_icon} {rec['action']}")
            print(f"         {rec['description']}")
        
        self.analysis_results['recommendations'] = recommendations
    
    def generate_analysis_report(self):
        """生成分析报告"""
        print("\n📄 生成分析报告...")
        
        # 保存详细报告
        report_data = {
            'analysis_date': datetime.now().isoformat(),
            'project_root': str(self.project_root),
            'analysis_results': self.analysis_results,
            'summary': {
                'total_files': sum(len(list(self.project_root.rglob('*'))) for _ in [1]),
                'root_files': self.analysis_results.get('root_analysis', {}).get('file_count', 0),
                'directories': len(self.analysis_results.get('directory_structure', {})),
                'problems_found': sum(self.analysis_results.get('problems', {}).values()),
                'recommendations': len(self.analysis_results.get('recommendations', []))
            }
        }
        
        report_file = self.project_root / 'project_structure_analysis.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report_data, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ 详细报告已保存: {report_file}")
        
        # 生成简化的Markdown报告
        self.generate_markdown_report(report_data)
    
    def generate_markdown_report(self, report_data):
        """生成Markdown格式报告"""
        summary = report_data['summary']
        
        md_content = f"""# MythQuant 项目结构分析报告

**分析时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 📊 项目概况

- **总文件数**: {summary['total_files']}
- **根目录文件数**: {summary['root_files']}
- **目录数**: {summary['directories']}
- **发现问题数**: {summary['problems_found']}
- **清理建议数**: {summary['recommendations']}

## 🚨 主要问题

根目录文件过多是当前最主要的问题。建议的现代化项目结构应该是：

```
MythQuant/
├── src/mythquant/          # 新架构源码
├── *_compatibility.py      # 兼容性模块（保留在根目录）
├── main.py                 # 主程序
├── user_config.py          # 用户配置
├── tests/                  # 测试文件
├── docs/                   # 文档
├── tools/                  # 工具脚本
├── legacy/                 # 旧版本代码
├── config/                 # 配置文件
└── README.md              # 项目说明
```

## 💡 清理建议

### 高优先级
1. **整理根目录** - 只保留核心文件
2. **创建标准目录结构** - 按功能组织文件

### 中优先级  
1. **整理测试文件** - 统一到tests目录
2. **整理文档** - 统一到docs目录

### 低优先级
1. **清理临时文件** - 删除缓存和临时文件
2. **整理备份文件** - 移动到专门目录

## 🛠️ 执行建议

1. **手动整理**: 谨慎移动重要文件
2. **使用脚本**: 运行 `reorganize_project_structure.py` 自动整理
3. **分步执行**: 先备份，再分类移动文件
4. **验证功能**: 整理后运行测试确保功能正常

## ⚠️ 注意事项

- 兼容性模块必须保留在根目录
- 移动文件前请备份重要数据
- 整理后需要更新导入路径
- 建议在测试环境先验证
"""
        
        md_file = self.project_root / 'PROJECT_STRUCTURE_ANALYSIS.md'
        with open(md_file, 'w', encoding='utf-8') as f:
            f.write(md_content)
        
        print(f"   ✅ Markdown报告已保存: {md_file}")


def main():
    """主函数"""
    analyzer = ProjectStructureAnalyzer()
    analyzer.analyze()
    
    print("\n" + "=" * 50)
    print("🎯 分析完成！")
    print("\n📋 下一步建议:")
    print("1. 查看 PROJECT_STRUCTURE_ANALYSIS.md 了解详细分析")
    print("2. 根据建议手动整理文件，或运行 reorganize_project_structure.py")
    print("3. 整理后运行测试验证功能正常")
    print("4. 更新相关文档和导入路径")


if __name__ == "__main__":
    main()
