#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件与API数据比较示例

演示如何使用测试文件与API数据比较器来验证数据一致性

作者: AI Assistant
创建时间: 2025-07-29
"""

import sys
import os
import glob

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from test_environments.shared.utilities.test_file_api_comparator import (
    TestFileApiComparator,
    compare_test_file_with_api,
    get_comparison_details
)


def example_1_basic_comparison():
    """示例1：基本比较功能"""
    print("📋 示例1：基本比较功能")
    print("-" * 50)
    
    # 查找测试文件
    test_data_dir = "test_environments/minute_data_tests/input_data"
    test_files = glob.glob(os.path.join(test_data_dir, "test_1min_0_000617*.txt"))
    
    if not test_files:
        print(f"❌ 未找到测试文件，请确保 {test_data_dir} 目录下有test_1min_0_000617开头的文件")
        return
    
    test_file = test_files[0]  # 使用第一个找到的文件
    stock_code = "000617"
    
    print(f"📁 使用测试文件: {os.path.basename(test_file)}")
    
    # 方法1：使用便捷函数
    is_equal = compare_test_file_with_api(test_file, stock_code)
    print(f"✅ 便捷函数结果: 价格是否一致 = {is_equal}")
    
    # 方法2：获取详细信息
    details = get_comparison_details(test_file, stock_code)
    
    if details['success']:
        print(f"📊 详细比较结果:")
        print(f"   文件最后时间: {details['file_info']['last_time']}")
        print(f"   文件未复权收盘价: {details['file_info']['last_close_price']:.3f}")
        print(f"   API未复权收盘价: {details['api_info']['close_price']:.3f}")
        print(f"   价格差异: {details['comparison']['price_diff']:.6f}")
        print(f"   容差范围: {details['comparison']['tolerance']}")
        print(f"   是否在容差内: {details['comparison']['within_tolerance']}")
    else:
        print(f"❌ 详细比较失败: {details['message']}")


def example_2_class_usage():
    """示例2：使用类方法进行比较"""
    print("\n📋 示例2：使用类方法进行比较")
    print("-" * 50)
    
    # 创建比较器
    comparator = TestFileApiComparator()
    
    # 查找测试文件
    test_data_dir = "test_environments/minute_data_tests/input_data"
    test_files = glob.glob(os.path.join(test_data_dir, "test_*.txt"))
    
    if not test_files:
        print(f"❌ 未找到测试文件")
        return
    
    test_file = test_files[0]
    stock_code = "000617"
    
    # 使用自定义容差
    custom_tolerance = 0.01  # 1分钱容差
    
    result = comparator.compare_last_record_close_price(test_file, stock_code, custom_tolerance)
    
    print(f"📊 使用自定义容差 {custom_tolerance} 的比较结果:")
    
    if result['success']:
        file_info = result['file_info']
        api_info = result['api_info']
        comparison = result['comparison']
        
        print(f"   📁 文件信息:")
        print(f"      路径: {os.path.basename(file_info['path'])}")
        print(f"      总行数: {file_info['total_lines']}")
        print(f"      最后时间: {file_info['last_time']}")
        print(f"      最后价格: {file_info['last_close_price']:.3f}")
        
        print(f"   🌐 API信息:")
        print(f"      时间: {api_info['datetime']}")
        print(f"      价格: {api_info['close_price']:.3f}")
        print(f"      数据获取: {'成功' if api_info['found'] else '失败'}")
        
        print(f"   🔍 比较结果:")
        print(f"      价格差异: {comparison['price_diff']:.6f}")
        print(f"      容差: {comparison['tolerance']}")
        print(f"      在容差内: {'是' if comparison['within_tolerance'] else '否'}")
        print(f"      最终结论: {'价格一致' if result['is_equal'] else '价格不一致'}")
    else:
        print(f"❌ 比较失败: {result['message']}")


def example_3_batch_comparison():
    """示例3：批量比较多个文件"""
    print("\n📋 示例3：批量比较多个文件")
    print("-" * 50)
    
    # 创建比较器
    comparator = TestFileApiComparator()
    
    # 查找所有测试文件
    test_data_dir = "test_environments/minute_data_tests/input_data"
    test_files = glob.glob(os.path.join(test_data_dir, "test_*.txt"))
    
    if not test_files:
        print(f"❌ 未找到测试文件")
        return
    
    print(f"📁 找到 {len(test_files)} 个测试文件")
    
    # 批量比较
    stock_code = "000617"
    batch_result = comparator.batch_compare_files(test_files, stock_code)
    
    if 'error' in batch_result:
        print(f"❌ 批量比较失败: {batch_result['error']}")
        return
    
    # 显示详细结果
    print(f"\n📊 批量比较详细结果:")
    
    for file_name, result in batch_result['results'].items():
        status = "✅" if result.get('is_equal', False) else "❌"
        if result['success']:
            file_price = result['file_info']['last_close_price']
            api_price = result['api_info']['close_price']
            diff = result['comparison']['price_diff']
            print(f"   {status} {file_name}: 文件={file_price:.3f}, API={api_price:.3f}, 差异={diff:.6f}")
        else:
            print(f"   ❌ {file_name}: {result['message']}")
    
    # 显示汇总统计
    summary = batch_result['summary']
    print(f"\n📈 汇总统计:")
    print(f"   总文件数: {summary['total_files']}")
    print(f"   成功比较: {summary['success_count']}")
    print(f"   价格一致: {summary['equal_count']}")
    print(f"   成功率: {summary['success_rate']:.2%}")
    print(f"   一致率: {summary['equal_rate']:.2%}")


def example_4_error_handling():
    """示例4：错误处理演示"""
    print("\n📋 示例4：错误处理演示")
    print("-" * 50)
    
    comparator = TestFileApiComparator()
    
    # 测试不存在的文件
    non_existent_file = "test_environments/minute_data_tests/input_data/non_existent_file.txt"
    result = comparator.compare_last_record_close_price(non_existent_file, "000617")
    
    print(f"📁 测试不存在的文件:")
    print(f"   文件: {os.path.basename(non_existent_file)}")
    print(f"   结果: {'成功' if result['success'] else '失败'}")
    print(f"   消息: {result['message']}")
    
    # 测试错误的股票代码
    test_data_dir = "test_environments/minute_data_tests/input_data"
    test_files = glob.glob(os.path.join(test_data_dir, "test_*.txt"))
    
    if test_files:
        test_file = test_files[0]
        result = comparator.compare_last_record_close_price(test_file, "999999")  # 不存在的股票代码
        
        print(f"\n📊 测试错误的股票代码:")
        print(f"   股票代码: 999999")
        print(f"   结果: {'成功' if result['success'] else '失败'}")
        print(f"   消息: {result['message']}")


def example_5_different_tolerances():
    """示例5：不同容差的比较"""
    print("\n📋 示例5：不同容差的比较")
    print("-" * 50)
    
    # 查找测试文件
    test_data_dir = "test_environments/minute_data_tests/input_data"
    test_files = glob.glob(os.path.join(test_data_dir, "test_*.txt"))
    
    if not test_files:
        print(f"❌ 未找到测试文件")
        return
    
    test_file = test_files[0]
    stock_code = "000617"
    
    # 测试不同的容差值
    tolerances = [0.001, 0.01, 0.1, 1.0]  # 0.1分, 1分, 1角, 1元
    
    comparator = TestFileApiComparator()
    
    print(f"📁 测试文件: {os.path.basename(test_file)}")
    print(f"📊 不同容差的比较结果:")
    
    for tolerance in tolerances:
        result = comparator.compare_last_record_close_price(test_file, stock_code, tolerance)
        
        if result['success']:
            status = "✅ 一致" if result['is_equal'] else "❌ 不一致"
            diff = result['comparison']['price_diff']
            print(f"   容差 {tolerance:>5.3f}: {status} (差异: {diff:.6f})")
        else:
            print(f"   容差 {tolerance:>5.3f}: ❌ 比较失败")


def main():
    """主函数"""
    print("🧪 测试文件与API数据比较示例")
    print("=" * 80)
    
    try:
        example_1_basic_comparison()
        example_2_class_usage()
        example_3_batch_comparison()
        example_4_error_handling()
        example_5_different_tolerances()
        
        print(f"\n✅ 所有示例执行完成")
        
    except Exception as e:
        print(f"\n❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
