2025-07-26 23:35:02,585 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_233502.log
2025-07-26 23:35:02,585 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 23:35:02,603 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 23:35:02,604 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 23:35:02,605 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 23:35:02,605 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-26 23:35:02,606 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-26 23:35:02,873 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.175s
2025-07-26 23:35:02,894 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_180.153.18.170 (180.153.18.170:7709) - 0.191s
2025-07-26 23:35:02,906 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.200s
2025-07-26 23:35:02,908 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.202s
2025-07-26 23:35:02,962 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.236s
2025-07-26 23:35:02,984 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************ (************:7709) - 0.248s
2025-07-26 23:35:02,989 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-26 23:35:02,990 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-26 23:35:02,990 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-26 23:35:02,990 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 23:35:02,990 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 23:35:02,990 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 23:35:02,990 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 23:35:03,441 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 23:35:03,441 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 23:35:03,448 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 23:35:03,448 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 23:35:03,449 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 23:35:03,449 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 23:35:03,449 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 23:35:03,450 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 23:35:03,450 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 23:35:03,454 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 23:35:03,454 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 23:35:03,454 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 23:35:03,454 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 23:35:03,461 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 23:35:03,982 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.52秒
2025-07-26 23:35:03,982 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.52秒
2025-07-26 23:35:03,982 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 23:35:03,982 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 23:35:03,982 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 23:35:03,983 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 23:35:03,983 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 23:35:03,984 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 23:35:03,984 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 23:35:03,984 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 23:35:03,984 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 23:35:03,984 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 23:35:03,985 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 23:35:04,093 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 23:35:04,093 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 23:35:04,100 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 23:35:04,100 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 23:35:04,100 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 23:35:04,100 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 23:35:04,100 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 23:35:04,100 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 23:35:04,100 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 23:35:04,104 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 23:35:04,104 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 23:35:04,104 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 23:35:04,104 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 23:35:04,111 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 23:35:04,725 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.61秒
2025-07-26 23:35:04,725 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.61秒
2025-07-26 23:35:14,825 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 23:35:37,746 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 23:35:37,746 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 23:35:37,746 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 23:35:37,755 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 23:35:38,172 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 23:35:38,172 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 23:35:38,172 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 23:35:38,172 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 23:35:38,174 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 23:35:38,174 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 23:35:38,174 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 23:35:38,175 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 23:35:38,175 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 23:35:38,229 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 23:35:38,688 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 23:35:38,705 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 23:35:38,722 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 23:35:38,738 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 23:35:38,755 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 23:35:38,772 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 23:35:38,784 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 23:35:38,801 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 23:35:38,816 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 23:35:38,832 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 23:35:38,849 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 23:35:38,865 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 23:35:38,882 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 23:35:38,898 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 23:35:38,914 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 23:35:38,930 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 23:35:38,947 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 23:35:38,957 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 23:35:38,969 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 23:35:38,984 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 23:35:39,000 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 23:35:39,001 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 23:35:43,002 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 23:35:43,003 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 23:35:43,003 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 23:35:43,003 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 23:35:43,004 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 23:35:43,006 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 23:35:43,092 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-26 23:35:43,093 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 23:35:43,093 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 23:35:43,093 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 23:35:43,093 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 23:35:43,094 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 23:35:43,094 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 23:35:43,105 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 23:35:43,236 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 23:35:43,758 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 23:35:43,964 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 23:35:43,964 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 23:35:44,032 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 23:35:44,032 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 23:35:44,032 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 23:35:44,033 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-26 23:35:44,033 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-26 23:35:44,033 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-26 23:35:44,033 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 23:35:44,046 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 40.06 秒
2025-07-26 23:35:44,046 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 23:35:44,046 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 23:35:44,046 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 23:35:44,047 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 23:35:44,048 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 23:35:44,389 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 23:35:44,402 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 23:35:44,402 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 23:35:44,402 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 23:35:44,402 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-26 23:35:44,402 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 23:35:44,402 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 23:35:44,402 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 23:35:44,402 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 23:35:44,407 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-26 23:35:44,407 - core.logging_service - INFO - verbose_log:67 - ✅ 加载智能文件选择器配置: 策略=smart_comprehensive
2025-07-26 23:35:44,407 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-26 23:35:44,408 - core.logging_service - INFO - verbose_log:67 - 🔍 发现2个候选文件，开始智能选择
2025-07-26 23:35:44,408 - core.logging_service - INFO - verbose_log:67 - 📋 候选文件分析:
2025-07-26 23:35:44,409 - core.logging_service - INFO - verbose_log:67 - ==============================================================================================================
2025-07-26 23:35:44,409 - core.logging_service - INFO - verbose_log:67 - 序号   文件名                                           时间范围         天数     大小       新鲜度      覆盖度      总分      
2025-07-26 23:35:44,409 - core.logging_service - INFO - verbose_log:67 - ==============================================================================================================
2025-07-26 23:35:44,409 - core.logging_service - INFO - verbose_log:67 - 1    1min_0_000617_202503...网（202507262158）.txt    0303-0704    124    931KB    343.0    124.0    170.4   
2025-07-26 23:35:44,409 - core.logging_service - INFO - verbose_log:67 - ==============================================================================================================
2025-07-26 23:35:44,409 - core.logging_service - INFO - verbose_log:67 - ✅ 选择文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt (策略: smart_comprehensive, 评分: 170.38)
2025-07-26 23:35:44,409 - core.logging_service - INFO - verbose_log:67 - 🎯 选择原因 (策略: smart_comprehensive):
2025-07-26 23:35:44,409 - core.logging_service - INFO - verbose_log:67 -   综合评分最高: 170.38
2025-07-26 23:35:44,410 - core.logging_service - INFO - verbose_log:67 -     新鲜度: 343.0/365
2025-07-26 23:35:44,410 - core.logging_service - INFO - verbose_log:67 -     覆盖度: 124.0天
2025-07-26 23:35:44,410 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-26 23:35:44,410 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-26 23:35:44,410 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-26 23:35:44,410 - core.logging_service - ERROR - verbose_log:67 - 分析文件时间跨度失败: 'LoggingService' object has no attribute 'info'
2025-07-26 23:35:44,410 - core.logging_service - ERROR - log_error:137 - 分析文件时间跨度失败: 'LoggingService' object has no attribute 'info'
2025-07-26 23:35:44,410 - Main - INFO - info:307 - ℹ️ 无法分析现有文件时间跨度，执行全量下载
2025-07-26 23:35:44,410 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-26 23:35:44,410 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 23:35:44,410 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 23:35:44,410 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-26 23:35:44,411 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:44,588 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:44,588 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 42624条 (目标日期: 20250101, 交易日: 148天, 频率: 1min)
2025-07-26 23:35:44,588 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计42624条 -> 实际请求800条 (配置限制:800)
2025-07-26 23:35:44,588 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 23:35:44,588 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 23:35:44,640 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需41824条
2025-07-26 23:35:44,640 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:44,814 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:44,865 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 23:35:44,865 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:45,037 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:45,087 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 23:35:45,088 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:45,260 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:45,309 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 23:35:45,310 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:45,484 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:45,536 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 23:35:45,536 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:45,701 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:45,750 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 23:35:45,751 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:45,930 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:45,982 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 23:35:45,982 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:46,160 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:46,210 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 23:35:46,210 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:46,388 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:46,440 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 23:35:46,440 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:46,611 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:46,661 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 23:35:46,662 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:46,836 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:46,887 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 23:35:46,887 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:47,065 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:47,118 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 23:35:47,119 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:47,291 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:47,341 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 23:35:47,341 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:47,512 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:47,562 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 23:35:47,562 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:47,741 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:47,794 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 23:35:47,794 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:47,964 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:48,014 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 23:35:48,014 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:48,184 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:48,234 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 23:35:48,234 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:48,416 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:48,468 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 23:35:48,468 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:48,638 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:48,690 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 23:35:48,690 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:48,861 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:48,911 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 23:35:48,911 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:49,082 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:49,133 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 23:35:49,133 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:49,302 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:49,353 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 23:35:49,354 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:49,519 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:49,572 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 23:35:49,572 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:49,744 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:49,799 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 23:35:49,799 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:49,978 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:50,029 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 23:35:50,029 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:50,205 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:50,265 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 23:35:50,265 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:50,506 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:50,558 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 23:35:50,559 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:50,731 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:50,780 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 23:35:50,780 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:50,950 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:51,001 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 23:35:51,001 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:51,181 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:51,233 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 23:35:51,234 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:35:51,405 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:35:51,449 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 23:35:51,449 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要42624条，实际获取24000条
2025-07-26 23:35:51,449 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约18624条数据（约77个交易日）
2025-07-26 23:35:51,449 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-26 23:35:51,449 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-26 23:35:51,506 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 24000 条 1min 数据
2025-07-26 23:35:51,513 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共24000条记录
2025-07-26 23:35:51,513 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 23:35:51,586 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.290, 前复权=6.290
2025-07-26 23:35:51,590 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 23:35:51,591 - Main - INFO - info:307 - ℹ️   日期: 202503030931
2025-07-26 23:35:51,591 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.29
2025-07-26 23:35:51,591 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.29
2025-07-26 23:35:51,591 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 23:35:51,591 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 24000 >= 5
2025-07-26 23:35:51,591 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 23:35:51,592 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 23:35:51,592 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 23:35:51,644 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250303-20250725_来源互联网（202507262335）.txt (1123291 字节)
2025-07-26 23:35:51,644 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 23:35:51,645 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 23:35:51,645 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-26 23:35:51,645 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-26 23:35:51,645 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 23:35:51,645 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 23:35:51,646 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 23:35:51,646 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 23:35:51,646 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 23:35:51,646 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 23:35:51,646 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-26 23:35:51,646 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: True, 互联网: False
2025-07-26 23:35:51,648 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_233551.txt
2025-07-26 23:35:51,648 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-26 23:35:51,648 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-26 23:35:51,648 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 66.7%
2025-07-26 23:35:51,649 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 23:35:51,649 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 23:35:51,649 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 23:35:51,649 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 23:35:51,649 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 23:35:51,650 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 23:35:51,650 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
