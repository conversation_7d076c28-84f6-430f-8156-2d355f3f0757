#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据质量验证器模块

按照1min_workflow.md第6步要求实现数据质量验证功能

作者: AI Assistant
创建时间: 2025-08-09
"""

import os
import sys
import re
from datetime import datetime, timedelta
from typing import Dict, Any, List, Optional
from pathlib import Path
import pandas as pd

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mythquant.shared.logging import get_smart_logger


class DataQualityValidator:
    """数据质量验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.logger = get_smart_logger("DataQualityValidator")

        # A股交易时间标准
        self.MINUTES_PER_DAY = 240  # 每个交易日240分钟
        self.TRADING_SESSIONS = [
            ('09:30', '11:30'),  # 上午交易时段
            ('13:00', '15:00')   # 下午交易时段
        ]

        # 文件命名规则正则
        self.filename_pattern = r'(\d+min)_([01])_(\d{6})_(\d{8})-(\d{8})_(.+)\.txt'

        self.logger.info("✅ 数据质量验证器初始化完成")
    
    def validate_minute_data_file(self, file_path: str, stock_code: str) -> Dict[str, Any]:
        """
        验证分钟数据文件的质量
        
        Args:
            file_path: 文件路径
            stock_code: 股票代码
            
        Returns:
            验证结果字典
        """
        try:
            self.logger.info(f"✅ 开始验证数据质量: {Path(file_path).name}")

            validation_results = {
                'file_path': file_path,
                'stock_code': stock_code,
                'passed': True,
                'issues': [],
                'details': {}
            }

            # 1. 验证文件格式
            format_result = self._validate_file_format(file_path)
            validation_results['details']['format'] = format_result
            if not format_result['passed']:
                validation_results['passed'] = False
                validation_results['issues'].extend(format_result['issues'])

            # 2. 验证数据内容
            content_result = self._validate_data_content(file_path, stock_code)
            validation_results['details']['content'] = content_result
            if not content_result['passed']:
                validation_results['passed'] = False
                validation_results['issues'].extend(content_result['issues'])

            # 3. 验证文件命名
            filename_result = self._validate_filename(file_path, stock_code)
            validation_results['details']['filename'] = filename_result
            if not filename_result['passed']:
                validation_results['passed'] = False
                validation_results['issues'].extend(filename_result['issues'])

            # 生成验证报告
            self._generate_validation_report(validation_results)

            return validation_results

        except Exception as e:
            self.logger.error(f"❌ 数据质量验证失败: {e}")
            return {
                'file_path': file_path,
                'stock_code': stock_code,
                'passed': False,
                'issues': [f"验证过程异常: {e}"],
                'details': {}
            }
    
    def _validate_file_format(self, file_path: str) -> Dict[str, Any]:
        """验证文件格式"""
        try:
            result = {
                'passed': True,
                'issues': [],
                'details': {}
            }
            
            if not os.path.exists(file_path):
                result['passed'] = False
                result['issues'].append("文件不存在")
                return result
            
            # 读取文件
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if not lines:
                result['passed'] = False
                result['issues'].append("文件为空")
                return result
            
            # 检查文件头格式
            expected_header = "股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖"
            actual_header = lines[0].strip()
            
            if actual_header != expected_header:
                result['passed'] = False
                result['issues'].append(f"文件头格式错误: 期望'{expected_header}', 实际'{actual_header}'")
            
            # 检查字段数量和分隔符
            if len(lines) > 1:
                sample_line = lines[1].strip()
                fields = sample_line.split('|')
                
                if len(fields) != 8:
                    result['passed'] = False
                    result['issues'].append(f"字段数量错误: 期望8个字段, 实际{len(fields)}个字段")
            
            result['details']['total_lines'] = len(lines)
            result['details']['data_lines'] = len(lines) - 1 if len(lines) > 1 else 0
            
            return result
            
        except Exception as e:
            return {
                'passed': False,
                'issues': [f"文件格式验证异常: {e}"],
                'details': {}
            }
    
    def _validate_data_content(self, file_path: str, stock_code: str) -> Dict[str, Any]:
        """验证数据内容"""
        try:
            result = {
                'passed': True,
                'issues': [],
                'details': {}
            }
            
            # 读取数据
            df = pd.read_csv(file_path, sep='|', encoding='utf-8')
            
            if df.empty:
                result['passed'] = False
                result['issues'].append("数据为空")
                return result
            
            # 检查时间连续性
            time_continuity = self._check_time_continuity(df)
            result['details']['time_continuity'] = time_continuity
            if not time_continuity['passed']:
                result['passed'] = False
                result['issues'].extend(time_continuity['issues'])
            
            # 检查价格合理性
            price_reasonableness = self._check_price_reasonableness(df)
            result['details']['price_reasonableness'] = price_reasonableness
            if not price_reasonableness['passed']:
                result['passed'] = False
                result['issues'].extend(price_reasonableness['issues'])
            
            # 检查数据完整性
            data_completeness = self._check_data_completeness(df)
            result['details']['data_completeness'] = data_completeness
            if not data_completeness['passed']:
                result['passed'] = False
                result['issues'].extend(data_completeness['issues'])
            
            return result
            
        except Exception as e:
            return {
                'passed': False,
                'issues': [f"数据内容验证异常: {e}"],
                'details': {}
            }
    
    def _check_time_continuity(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查时间连续性"""
        try:
            result = {
                'passed': True,
                'issues': [],
                'details': {}
            }
            
            # 检查时间格式
            time_column = '时间'
            if time_column not in df.columns:
                result['passed'] = False
                result['issues'].append("缺少时间字段")
                return result
            
            # 检查时间格式是否正确（YYYYMMDDHHMM）
            time_pattern = r'^\d{12}$'
            invalid_times = df[~df[time_column].astype(str).str.match(time_pattern)]
            
            if not invalid_times.empty:
                result['passed'] = False
                result['issues'].append(f"发现{len(invalid_times)}条时间格式错误的记录")
            
            # 按日期分组检查每日数据量
            df['date'] = df[time_column].astype(str).str[:8]
            daily_counts = df.groupby('date').size()
            
            irregular_days = daily_counts[daily_counts != self.MINUTES_PER_DAY]
            if not irregular_days.empty:
                result['issues'].append(f"发现{len(irregular_days)}个交易日数据量不标准")
                result['details']['irregular_days'] = irregular_days.to_dict()
            
            result['details']['total_trading_days'] = len(daily_counts)
            result['details']['standard_days'] = len(daily_counts[daily_counts == self.MINUTES_PER_DAY])
            
            return result
            
        except Exception as e:
            return {
                'passed': False,
                'issues': [f"时间连续性检查异常: {e}"],
                'details': {}
            }
    
    def _check_price_reasonableness(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查价格合理性"""
        try:
            result = {
                'passed': True,
                'issues': [],
                'details': {}
            }
            
            # 检查必要的价格字段
            required_price_fields = ['当日收盘价C', '前复权收盘价C']
            missing_fields = [field for field in required_price_fields if field not in df.columns]
            
            if missing_fields:
                result['passed'] = False
                result['issues'].append(f"缺少价格字段: {missing_fields}")
                return result
            
            # 检查价格是否为正数
            close_price = pd.to_numeric(df['当日收盘价C'], errors='coerce')
            close_qfq_price = pd.to_numeric(df['前复权收盘价C'], errors='coerce')
            
            negative_close = close_price[close_price <= 0]
            negative_qfq = close_qfq_price[close_qfq_price <= 0]
            
            if not negative_close.empty:
                result['passed'] = False
                result['issues'].append(f"发现{len(negative_close)}条当日收盘价异常记录")
            
            if not negative_qfq.empty:
                result['passed'] = False
                result['issues'].append(f"发现{len(negative_qfq)}条前复权收盘价异常记录")
            
            # 检查前复权价格与当日价格的合理差异
            price_diff = abs(close_price - close_qfq_price) / close_price
            unreasonable_diff = price_diff[price_diff > 0.5]  # 差异超过50%认为不合理
            
            if not unreasonable_diff.empty:
                result['issues'].append(f"发现{len(unreasonable_diff)}条价格差异过大的记录")
            
            result['details']['price_range'] = {
                'close_min': float(close_price.min()) if not close_price.empty else 0,
                'close_max': float(close_price.max()) if not close_price.empty else 0,
                'qfq_min': float(close_qfq_price.min()) if not close_qfq_price.empty else 0,
                'qfq_max': float(close_qfq_price.max()) if not close_qfq_price.empty else 0
            }
            
            return result
            
        except Exception as e:
            return {
                'passed': False,
                'issues': [f"价格合理性检查异常: {e}"],
                'details': {}
            }
    
    def _check_data_completeness(self, df: pd.DataFrame) -> Dict[str, Any]:
        """检查数据完整性"""
        try:
            result = {
                'passed': True,
                'issues': [],
                'details': {}
            }
            
            # 检查必填字段
            required_fields = ['股票编码', '时间', '当日收盘价C', '前复权收盘价C']
            
            for field in required_fields:
                if field not in df.columns:
                    result['passed'] = False
                    result['issues'].append(f"缺少必填字段: {field}")
                    continue
                
                # 检查空值
                null_count = df[field].isnull().sum()
                if null_count > 0:
                    result['passed'] = False
                    result['issues'].append(f"字段'{field}'存在{null_count}个空值")
            
            # 计算完整性评分
            total_records = len(df)
            if total_records > 0:
                # 按日期分组计算完整性
                df['date'] = df['时间'].astype(str).str[:8]
                daily_counts = df.groupby('date').size()
                complete_days = len(daily_counts[daily_counts == self.MINUTES_PER_DAY])
                completeness_ratio = complete_days / len(daily_counts) if len(daily_counts) > 0 else 0
                
                result['details']['completeness_ratio'] = completeness_ratio
                result['details']['total_days'] = len(daily_counts)
                result['details']['complete_days'] = complete_days
                result['details']['total_records'] = total_records
                
                if completeness_ratio < 0.95:  # 完整性低于95%认为不合格
                    result['passed'] = False
                    result['issues'].append(f"数据完整性过低: {completeness_ratio:.1%}")
            
            return result
            
        except Exception as e:
            return {
                'passed': False,
                'issues': [f"数据完整性检查异常: {e}"],
                'details': {}
            }
    
    def _validate_filename(self, file_path: str, stock_code: str) -> Dict[str, Any]:
        """验证文件命名"""
        try:
            result = {
                'passed': True,
                'issues': [],
                'details': {}
            }
            
            filename = os.path.basename(file_path)
            
            # 检查命名规范
            match = re.match(self.filename_pattern, filename)
            
            if not match:
                result['passed'] = False
                result['issues'].append("文件名不符合命名规范")
                return result
            
            frequency, market_code, file_stock_code, start_date, end_date, source = match.groups()
            
            # 验证股票代码一致性
            if file_stock_code != str(stock_code).zfill(6):
                result['passed'] = False
                result['issues'].append(f"文件名中的股票代码({file_stock_code})与参数不一致({stock_code})")
            
            # 验证市场代码
            expected_market = '1' if str(stock_code).zfill(6).startswith('6') else '0'
            if market_code != expected_market:
                result['passed'] = False
                result['issues'].append(f"市场代码错误: 期望{expected_market}, 实际{market_code}")
            
            result['details']['parsed_info'] = {
                'frequency': frequency,
                'market_code': market_code,
                'stock_code': file_stock_code,
                'start_date': start_date,
                'end_date': end_date,
                'source': source
            }
            
            return result
            
        except Exception as e:
            return {
                'passed': False,
                'issues': [f"文件命名验证异常: {e}"],
                'details': {}
            }
    
    def _generate_validation_report(self, validation_results: Dict[str, Any]) -> None:
        """生成验证报告"""
        try:
            file_path = validation_results['file_path']
            filename = os.path.basename(file_path)
            
            if validation_results['passed']:
                self.logger.info(f"✅ 数据质量验证通过: {filename}")
            else:
                self.logger.warning(f"❌ 数据质量验证失败: {filename}")
                for issue in validation_results['issues']:
                    self.logger.warning(f"   - {issue}")

        except Exception as e:
            self.logger.error(f"生成验证报告失败: {e}")


def main():
    """测试函数"""
    validator = DataQualityValidator()
    
    # 测试文件路径（需要根据实际情况调整）
    test_file = "./test_output/1min_0_000617_20250809-20250809_来源互联网（202508091530）.txt"
    
    if os.path.exists(test_file):
        result = validator.validate_minute_data_file(test_file, '000617')
        print(f"验证结果: {result['passed']}")
        if result['issues']:
            print(f"问题列表: {result['issues']}")
    else:
        print(f"测试文件不存在: {test_file}")


if __name__ == '__main__':
    main()
