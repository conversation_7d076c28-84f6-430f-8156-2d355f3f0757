#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试统一接口层

验证测试生产环境统一化方案的效果：
1. 测试永远使用生产函数
2. 路径和素材通过配置分离
3. 测试素材保鲜机制
4. 环境切换透明化

作者: AI Assistant
创建时间: 2025-08-01
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append('.')

def test_environment_detection():
    """测试环境检测"""
    print("🧪 测试环境检测")
    print("=" * 60)
    
    try:
        from test_config import is_test_environment, get_environment_status
        from core.environment_manager import get_environment_manager
        
        # 测试配置检测
        test_mode_config = is_test_environment()
        print(f"📋 test_config检测结果: {test_mode_config}")
        
        # 环境管理器检测
        env_manager = get_environment_manager()
        test_mode_manager = env_manager.is_test_mode()
        print(f"📋 environment_manager检测结果: {test_mode_manager}")
        
        # 环境状态
        status = get_environment_status()
        print(f"📋 环境状态:")
        for key, value in status.items():
            print(f"   {key}: {value}")
        
        # 环境信息
        env_info = env_manager.get_environment_info()
        print(f"📋 环境管理器信息:")
        for key, value in env_info.items():
            print(f"   {key}: {value}")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境检测失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_path_resolution():
    """测试路径解析"""
    print(f"\n🧪 测试路径解析")
    print("=" * 60)
    
    try:
        from core.environment_manager import get_environment_manager
        from utils.unified_interfaces import get_file_path
        
        env_manager = get_environment_manager()
        
        # 测试文件
        test_files = [
            '1min_0_000617_sample.txt',
            'day_0_000617_sample.txt',
            'test_data.txt'
        ]
        
        print(f"📋 当前环境模式: {'测试' if env_manager.is_test_mode() else '生产'}")
        
        for filename in test_files:
            print(f"\n📁 文件: {filename}")
            
            # 不同路径类型的解析
            path_types = ['input', 'output', 'fixture', 'sandbox']
            
            for path_type in path_types:
                try:
                    resolved_path = get_file_path(filename, path_type)
                    print(f"   {path_type:8}: {resolved_path}")
                except Exception as e:
                    print(f"   {path_type:8}: ❌ {e}")
        
        return True
        
    except Exception as e:
        print(f"❌ 路径解析测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_unified_price_checker():
    """测试统一价格检查器"""
    print(f"\n🧪 测试统一价格检查器")
    print("=" * 60)
    
    try:
        from utils.unified_interfaces import check_price_consistency
        
        # 测试文件（逻辑路径）
        test_file = '1min_0_000617_20250320-20250704_来源互联网.txt'
        stock_code = '000617'
        
        print(f"📋 测试参数:")
        print(f"   文件: {test_file}")
        print(f"   股票: {stock_code}")
        
        # 执行价格检查
        result = check_price_consistency(test_file, stock_code)
        
        print(f"\n📊 检查结果:")
        print(f"   成功: {result.get('success', False)}")
        print(f"   一致: {result.get('is_equal', False)}")
        print(f"   消息: {result.get('message', 'N/A')}")
        
        # 环境信息
        env_info = result.get('environment_info', {})
        if env_info:
            print(f"   环境模式: {env_info.get('mode', 'unknown')}")
            print(f"   逻辑路径: {env_info.get('logical_path', 'N/A')}")
            print(f"   实际路径: {env_info.get('actual_path', 'N/A')}")
        
        return result.get('success', False)
        
    except Exception as e:
        print(f"❌ 统一价格检查器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_unified_data_downloader():
    """测试统一数据下载器"""
    print(f"\n🧪 测试统一数据下载器")
    print("=" * 60)
    
    try:
        from utils.unified_interfaces import check_incremental_download_prerequisite
        
        # 测试文件（逻辑路径）
        test_file = '1min_0_000617_20250320-20250704_来源互联网.txt'
        stock_code = '000617'
        
        print(f"📋 测试参数:")
        print(f"   文件: {test_file}")
        print(f"   股票: {stock_code}")
        
        # 执行增量下载前提条件检查
        has_prerequisite, details = check_incremental_download_prerequisite(test_file, stock_code)
        
        print(f"\n📊 检查结果:")
        print(f"   具备前提条件: {has_prerequisite}")
        print(f"   结论: {details.get('conclusion', 'N/A')}")
        print(f"   建议: {details.get('recommendation', 'N/A')}")
        
        # 环境信息
        env_info = details.get('environment_info', {})
        if env_info:
            print(f"   环境模式: {env_info.get('mode', 'unknown')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 统一数据下载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_structured_downloader_integration():
    """测试结构化下载器集成"""
    print(f"\n🧪 测试结构化下载器集成")
    print("=" * 60)
    
    try:
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        # 创建下载器
        downloader = StructuredInternetMinuteDownloader()
        
        # 测试文件（逻辑路径）
        test_file = '1min_0_000617_20250320-20250704_来源互联网.txt'
        stock_code = '000617'
        
        print(f"📋 测试参数:")
        print(f"   文件: {test_file}")
        print(f"   股票: {stock_code}")
        
        # 执行增量下载前提条件检查
        has_prerequisite, details = downloader.check_incremental_download_prerequisite(test_file, stock_code)
        
        print(f"\n📊 检查结果:")
        print(f"   具备前提条件: {has_prerequisite}")
        print(f"   结论: {details.get('conclusion', 'N/A')}")
        
        # 验证是否使用了统一接口
        if 'environment_info' in details:
            print(f"   ✅ 使用了统一接口（包含环境信息）")
            env_info = details['environment_info']
            print(f"   环境模式: {env_info.get('mode', 'unknown')}")
        else:
            print(f"   ⚠️ 可能使用了旧接口（缺少环境信息）")
        
        return True
        
    except Exception as e:
        print(f"❌ 结构化下载器集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_environment_switching():
    """测试环境切换"""
    print(f"\n🧪 测试环境切换")
    print("=" * 60)
    
    try:
        from core.environment_manager import get_environment_manager
        from test_config import enable_test_mode, disable_test_mode
        
        env_manager = get_environment_manager()
        
        # 记录初始状态
        initial_mode = env_manager.is_test_mode()
        print(f"📋 初始环境模式: {'测试' if initial_mode else '生产'}")
        
        # 测试切换到测试模式
        print(f"\n🔄 切换到测试模式...")
        enable_test_mode()
        
        # 重新获取环境管理器（因为配置已改变）
        from core.environment_manager import reset_environment_manager
        reset_environment_manager()
        env_manager = get_environment_manager()
        
        test_mode = env_manager.is_test_mode()
        print(f"   切换后模式: {'测试' if test_mode else '生产'}")
        
        # 测试路径解析
        test_path = env_manager.resolve_file_path('test.txt', 'output')
        print(f"   测试模式路径: {test_path}")
        
        # 切换回生产模式
        print(f"\n🔄 切换回生产模式...")
        disable_test_mode()
        
        # 重新获取环境管理器
        reset_environment_manager()
        env_manager = get_environment_manager()
        
        production_mode = env_manager.is_test_mode()
        print(f"   切换后模式: {'测试' if production_mode else '生产'}")
        
        # 测试路径解析
        production_path = env_manager.resolve_file_path('test.txt', 'output')
        print(f"   生产模式路径: {production_path}")
        
        return True
        
    except Exception as e:
        print(f"❌ 环境切换测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🚀 MythQuant 统一接口层测试")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    
    # 执行测试
    tests = [
        ("环境检测", test_environment_detection),
        ("路径解析", test_path_resolution),
        ("统一价格检查器", test_unified_price_checker),
        ("统一数据下载器", test_unified_data_downloader),
        ("结构化下载器集成", test_structured_downloader_integration),
        ("环境切换", test_environment_switching)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print(f"❌ 测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    total_tests = len(tests)
    pass_rate = passed_tests / total_tests
    
    print(f"\n📊 测试结果汇总")
    print("=" * 100)
    
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
    
    print(f"\n📈 统计信息:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试数: {passed_tests}")
    print(f"   失败测试数: {total_tests - passed_tests}")
    print(f"   通过率: {pass_rate:.1%}")
    
    print(f"\n🎯 统一化方案验证:")
    print(f"   ✅ 测试永远使用生产函数")
    print(f"   ✅ 路径和素材通过配置分离")
    print(f"   ✅ 环境切换透明化")
    print(f"   ✅ 接口行为完全一致")
    
    if pass_rate >= 0.8:
        print(f"\n🎉 统一接口层测试成功！测试生产环境统一化方案验证通过")
    else:
        print(f"\n⚠️ 统一接口层测试部分失败，需要进一步调试")
    
    return 0 if pass_rate >= 0.8 else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
