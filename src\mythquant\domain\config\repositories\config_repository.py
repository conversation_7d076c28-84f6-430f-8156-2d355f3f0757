#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置仓储接口

定义配置数据访问的抽象接口，遵循DDD的依赖倒置原则
"""

from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional
from ..entities.trading_config import TradingConfig
from ..entities.data_source_config import DataSourceConfig
from ..entities.processing_config import ProcessingConfig


class ConfigRepository(ABC):
    """配置仓储接口"""
    
    @abstractmethod
    def get_trading_config(self) -> TradingConfig:
        """获取交易配置"""
        pass
    
    @abstractmethod
    def get_data_source_config(self) -> DataSourceConfig:
        """获取数据源配置"""
        pass
    
    @abstractmethod
    def get_processing_config(self) -> ProcessingConfig:
        """获取数据处理配置"""
        pass
    
    @abstractmethod
    def save_trading_config(self, config: TradingConfig) -> None:
        """保存交易配置"""
        pass
    
    @abstractmethod
    def save_data_source_config(self, config: DataSourceConfig) -> None:
        """保存数据源配置"""
        pass
    
    @abstractmethod
    def save_processing_config(self, config: ProcessingConfig) -> None:
        """保存数据处理配置"""
        pass
    
    @abstractmethod
    def get_task_configs(self) -> List[Dict[str, Any]]:
        """获取任务配置列表"""
        pass
    
    @abstractmethod
    def validate_config_integrity(self) -> bool:
        """验证配置完整性"""
        pass
    
    @abstractmethod
    def backup_config(self) -> str:
        """备份配置，返回备份文件路径"""
        pass
    
    @abstractmethod
    def restore_config(self, backup_path: str) -> bool:
        """从备份恢复配置"""
        pass


class TaskConfigRepository(ABC):
    """任务配置仓储接口"""
    
    @abstractmethod
    def get_minute_task_configs(self) -> List[Dict[str, Any]]:
        """获取分钟级任务配置"""
        pass
    
    @abstractmethod
    def get_daily_task_configs(self) -> List[Dict[str, Any]]:
        """获取日级任务配置"""
        pass
    
    @abstractmethod
    def create_task_config(self, task_type: str, **kwargs) -> Dict[str, Any]:
        """创建任务配置"""
        pass
    
    @abstractmethod
    def validate_task_config(self, config: Dict[str, Any]) -> bool:
        """验证任务配置"""
        pass


class ConfigCacheRepository(ABC):
    """配置缓存仓储接口"""
    
    @abstractmethod
    def get_cached_config(self, key: str) -> Optional[Any]:
        """获取缓存的配置"""
        pass
    
    @abstractmethod
    def set_cached_config(self, key: str, value: Any, ttl: Optional[int] = None) -> None:
        """设置缓存的配置"""
        pass
    
    @abstractmethod
    def invalidate_cache(self, key: Optional[str] = None) -> None:
        """使缓存失效"""
        pass
    
    @abstractmethod
    def is_cache_valid(self, key: str) -> bool:
        """检查缓存是否有效"""
        pass
