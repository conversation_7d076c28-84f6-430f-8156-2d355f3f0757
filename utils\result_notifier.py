#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果通知器

提供标准化的结果通知功能，确保在生产和测试环境中都有一致的输出格式。
支持多种类型的结果通知：数据完整性校验、价格一致性检查、缺失数据修复等。

作者: AI Assistant
创建时间: 2025-08-01
"""

from typing import Dict, List, Any, Optional
from datetime import datetime
from decimal import Decimal


class ResultNotifier:
    """标准化结果通知器"""
    
    def __init__(self, enable_emoji: bool = True):
        """
        初始化结果通知器
        
        Args:
            enable_emoji: 是否启用emoji（某些环境可能不支持）
        """
        self.enable_emoji = enable_emoji
        self.indent_unit = "   "  # 3个空格作为缩进单位
    
    def _format_emoji(self, emoji: str) -> str:
        """格式化emoji"""
        return emoji if self.enable_emoji else ""
    
    def _print_header(self, title: str, emoji: str = "📊"):
        """打印标题头"""
        print(f"{self._format_emoji(emoji)} {title}")
    
    def _print_item(self, key: str, value: str, level: int = 1, emoji: str = ""):
        """打印项目"""
        indent = self.indent_unit * level
        emoji_str = self._format_emoji(emoji) + " " if emoji else ""
        print(f"{indent}{emoji_str}{key}: {value}")
    
    def _print_list_item(self, item: str, level: int = 2, emoji: str = ""):
        """打印列表项"""
        indent = self.indent_unit * level
        emoji_str = self._format_emoji(emoji) + " " if emoji else ""
        print(f"{indent}- {emoji_str}{item}")
    
    def notify_data_integrity_check(self, result: Dict[str, Any]):
        """
        通知数据完整性校验结果
        
        Args:
            result: 校验结果字典
                {
                    'file_path': str,
                    'stock_code': str,
                    'status': str,  # 'complete', 'incomplete', 'error'
                    'completeness_rate': float,
                    'total_trading_days': int,
                    'expected_records': int,
                    'actual_records': int,
                    'missing_records': int,
                    'missing_details': List[Dict],
                    'recommendation': str
                }
        """
        self._print_header("数据完整性校验结果")
        
        # 基本信息
        self._print_item("文件", result.get('file_path', 'N/A'))
        self._print_item("股票", result.get('stock_code', 'N/A'))
        
        # 状态
        status = result.get('status', 'unknown')
        status_emoji = "✅" if status == 'complete' else "⚠️" if status == 'incomplete' else "❌"
        status_text = {
            'complete': '数据完整',
            'incomplete': '数据不完整',
            'error': '校验异常'
        }.get(status, '未知状态')
        self._print_item("整体状态", status_text, emoji=status_emoji)
        
        # 完整率
        completeness_rate = result.get('completeness_rate', 0)
        self._print_item("完整率", f"{completeness_rate:.1%}", emoji="📈")
        
        # 详细信息
        self._print_item("详细信息", "", emoji="📋")
        self._print_item("总交易日", f"{result.get('total_trading_days', 0)}天", level=2)
        self._print_item("应有数据", f"{result.get('expected_records', 0):,}条", level=2)
        self._print_item("实际数据", f"{result.get('actual_records', 0):,}条", level=2)
        self._print_item("缺失数据", f"{result.get('missing_records', 0):,}条", level=2)
        
        # 缺失详情
        missing_details = result.get('missing_details', [])
        if missing_details:
            self._print_item("缺失详情", "", emoji="🔧")
            for detail in missing_details[:5]:  # 最多显示5个
                date = detail.get('date', 'N/A')
                missing_count = detail.get('missing_count', 0)
                description = detail.get('description', '')
                self._print_list_item(f"{date}: 缺失{missing_count}分钟 {description}", level=2)
            
            if len(missing_details) > 5:
                self._print_list_item(f"... 还有{len(missing_details) - 5}个缺失项", level=2)
        
        # 修复建议
        recommendation = result.get('recommendation', '')
        if recommendation:
            self._print_item("修复建议", recommendation, emoji="💡")
    
    def notify_price_consistency_check(self, result: Dict[str, Any]):
        """
        通知价格一致性检查结果
        
        Args:
            result: 检查结果字典
                {
                    'file_path': str,
                    'stock_code': str,
                    'file_last_record': Dict,
                    'api_data': Dict,
                    'is_consistent': bool,
                    'price_difference': float,
                    'tolerance': float,
                    'conclusion': str
                }
        """
        self._print_header("价格一致性检查结果", "🔍")
        
        # 基本信息
        self._print_item("文件", result.get('file_path', 'N/A'))
        self._print_item("股票", result.get('stock_code', 'N/A'))
        
        # 文件最后记录
        file_record = result.get('file_last_record', {})
        if file_record:
            time_str = file_record.get('datetime', 'N/A')
            price = file_record.get('close_price', 0)
            formatted_time = f"{time_str[:4]}-{time_str[4:6]}-{time_str[6:8]} {time_str[8:10]}:{time_str[10:12]}" if len(time_str) == 12 else time_str
            self._print_item("文件最后记录", f"{formatted_time}, 收盘价={price}", emoji="📋")
        
        # API获取数据
        api_data = result.get('api_data', {})
        if api_data:
            time_str = api_data.get('datetime', 'N/A')
            price = api_data.get('close_price', 0)
            formatted_time = f"{time_str[:4]}-{time_str[4:6]}-{time_str[6:8]} {time_str[8:10]}:{time_str[10:12]}" if len(time_str) == 12 else time_str
            self._print_item("API获取数据", f"{formatted_time}, 收盘价={price}", emoji="🌐")
        
        # 一致性状态
        is_consistent = result.get('is_consistent', False)
        status_emoji = "✅" if is_consistent else "❌"
        status_text = "价格完全一致" if is_consistent else "价格不一致"
        self._print_item("一致性状态", status_text, emoji=status_emoji)
        
        # 价格差异
        price_diff = result.get('price_difference', 0)
        tolerance = result.get('tolerance', 0.001)
        self._print_item("价格差异", f"{price_diff:.3f} (容差: {tolerance:.3f})", emoji="📊")
        
        # 结论
        conclusion = result.get('conclusion', '')
        if conclusion:
            conclusion_emoji = "💡" if is_consistent else "⚠️"
            self._print_item("结论", conclusion, emoji=conclusion_emoji)
    
    def notify_missing_data_repair(self, result: Dict[str, Any]):
        """
        通知缺失数据修复结果
        
        Args:
            result: 修复结果字典
                {
                    'file_path': str,
                    'stock_code': str,
                    'repair_status': str,  # 'success', 'partial', 'failed'
                    'before_missing': int,
                    'downloaded_count': int,
                    'merged_count': int,
                    'after_missing': int,
                    'completeness_before': float,
                    'completeness_after': float,
                    'repair_details': List[Dict],
                    'recommendation': str
                }
        """
        self._print_header("缺失数据修复结果", "🔧")
        
        # 基本信息
        self._print_item("文件", result.get('file_path', 'N/A'))
        self._print_item("股票", result.get('stock_code', 'N/A'))
        
        # 修复状态
        status = result.get('repair_status', 'unknown')
        status_emoji = "✅" if status == 'success' else "⚠️" if status == 'partial' else "❌"
        status_text = {
            'success': '修复成功',
            'partial': '部分修复',
            'failed': '修复失败'
        }.get(status, '未知状态')
        self._print_item("修复状态", status_text, emoji=status_emoji)
        
        # 修复统计
        self._print_item("修复统计", "", emoji="📊")
        self._print_item("修复前缺失", f"{result.get('before_missing', 0)}条数据", level=2)
        self._print_item("成功下载", f"{result.get('downloaded_count', 0)}条数据", level=2)
        self._print_item("成功合并", f"{result.get('merged_count', 0)}条数据", level=2)
        self._print_item("修复后缺失", f"{result.get('after_missing', 0)}条数据", level=2)
        
        # 完整率提升
        before_rate = result.get('completeness_before', 0)
        after_rate = result.get('completeness_after', 0)
        self._print_item("完整率提升", f"{before_rate:.1%} → {after_rate:.1%}", emoji="📈")
        
        # 修复详情
        repair_details = result.get('repair_details', [])
        if repair_details:
            self._print_item("修复详情", "", emoji="🎯")
            for detail in repair_details[:5]:  # 最多显示5个
                date = detail.get('date', 'N/A')
                status = detail.get('status', 'unknown')
                count = detail.get('repaired_count', 0)
                remaining = detail.get('remaining_missing', 0)
                
                status_emoji = "✅" if status == 'success' else "⚠️"
                if remaining > 0:
                    desc = f"部分修复{count}分钟，仍缺失{remaining}分钟"
                else:
                    desc = f"已修复{count}分钟"
                
                self._print_list_item(f"{date}: {desc}", level=2, emoji=status_emoji)
        
        # 建议
        recommendation = result.get('recommendation', '')
        if recommendation:
            self._print_item("建议", recommendation, emoji="💡")
    
    def notify_final_validation(self, result: Dict[str, Any]):
        """
        通知最终验证结果
        
        Args:
            result: 验证结果字典
                {
                    'task_name': str,
                    'output_dir': str,
                    'validation_status': str,  # 'success', 'partial', 'failed'
                    'file_count': int,
                    'passed_count': int,
                    'failed_count': int,
                    'file_details': List[Dict],
                    'conclusion': str
                }
        """
        self._print_header("最终数据质量验证结果", "🎯")
        
        # 基本信息
        self._print_item("任务", result.get('task_name', 'N/A'))
        self._print_item("输出目录", result.get('output_dir', 'N/A'))
        
        # 验证状态
        status = result.get('validation_status', 'unknown')
        status_emoji = "✅" if status == 'success' else "⚠️" if status == 'partial' else "❌"
        status_text = {
            'success': '全部通过',
            'partial': '部分通过',
            'failed': '验证失败'
        }.get(status, '未知状态')
        self._print_item("验证状态", status_text, emoji=status_emoji)
        
        # 文件统计
        self._print_item("文件统计", "", emoji="📊")
        self._print_item("生成文件数", f"{result.get('file_count', 0)}个", level=2)
        self._print_item("验证通过", f"{result.get('passed_count', 0)}个", level=2)
        self._print_item("验证失败", f"{result.get('failed_count', 0)}个", level=2)
        
        # 文件详情
        file_details = result.get('file_details', [])
        if file_details:
            self._print_item("文件详情", "", emoji="📋")
            for detail in file_details:
                filename = detail.get('filename', 'N/A')
                file_status = detail.get('status', 'unknown')
                file_emoji = "✅" if file_status == 'success' else "❌"
                
                self._print_item(filename, "", level=2, emoji=file_emoji)
                
                # 详细指标
                completeness = detail.get('completeness_rate', 0)
                format_correctness = detail.get('format_correctness', 0)
                price_precision = detail.get('price_precision_status', 'N/A')
                
                self._print_item("数据完整率", f"{completeness:.1%}", level=3)
                self._print_item("格式正确性", f"{format_correctness:.1%}", level=3)
                self._print_item("价格精度", price_precision, level=3)
        
        # 结论
        conclusion = result.get('conclusion', '')
        if conclusion:
            conclusion_emoji = "🎉" if status == 'success' else "⚠️"
            self._print_item("结论", conclusion, emoji=conclusion_emoji)


# ==================== 便捷函数 ====================
_global_notifier = None


def get_result_notifier() -> ResultNotifier:
    """获取全局结果通知器实例"""
    global _global_notifier
    if _global_notifier is None:
        _global_notifier = ResultNotifier()
    return _global_notifier


def notify_data_integrity_check(result: Dict[str, Any]):
    """便捷函数：通知数据完整性校验结果"""
    get_result_notifier().notify_data_integrity_check(result)


def notify_price_consistency_check(result: Dict[str, Any]):
    """便捷函数：通知价格一致性检查结果"""
    get_result_notifier().notify_price_consistency_check(result)


def notify_missing_data_repair(result: Dict[str, Any]):
    """便捷函数：通知缺失数据修复结果"""
    get_result_notifier().notify_missing_data_repair(result)


def notify_final_validation(result: Dict[str, Any]):
    """便捷函数：通知最终验证结果"""
    get_result_notifier().notify_final_validation(result)


# ==================== 使用示例 ====================
if __name__ == '__main__':
    print("📢 结果通知器演示")
    print("=" * 80)
    
    # 演示数据完整性校验通知
    integrity_result = {
        'file_path': '1min_0_000617_20250320-20250704.txt',
        'stock_code': '000617',
        'status': 'incomplete',
        'completeness_rate': 0.985,
        'total_trading_days': 85,
        'expected_records': 20400,
        'actual_records': 20094,
        'missing_records': 306,
        'missing_details': [
            {'date': '2025-03-21', 'missing_count': 120, 'description': '(下午时段)'},
            {'date': '2025-04-15', 'missing_count': 186, 'description': '(全天大部分时段)'}
        ],
        'recommendation': '建议使用MissingDataProcessor进行自动修复'
    }
    
    notify_data_integrity_check(integrity_result)
    
    print("\n" + "=" * 80)
    print("✅ 结果通知器演示完成")
