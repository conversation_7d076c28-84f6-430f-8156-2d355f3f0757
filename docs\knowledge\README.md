# MythQuant 知识库索引

## 📚 概述

本目录包含了MythQuant项目的完整知识库，涵盖了开发过程中积累的经验、最佳实践、问题解决方案和技术指南。

**最后更新**: 2025-08-08

---

## 📂 知识库结构

### 🗣️ 沟通协作 (`communication/`)
高效的AI协作和团队沟通指南

- **`effective_questioning_guide.md`** ⭐ **重点推荐**
  - 与AI助手高效协作的提问技巧
  - 从现象到本质的问题分析方法
  - 结构化表达和沟通策略
  - 适用场景：技术问题诊断、架构设计讨论

- **`questioning_templates.md`** 
  - 标准化提问模板库
  - 涵盖技术、调试、架构、业务等各类场景
  - 快速构建高质量问题描述的工具

### 🔄 工作流程 (`workflows/`)
项目开发和数据处理的标准化流程

- **`1min_workflow_improved.md`**
  - 分钟级数据处理的标准化四步流程
  - workflow规范和执行标准
  - 流程合理性和规范遵循要求

### 🏗️ 架构设计 (`architecture/`)
系统架构设计和演进的知识沉淀

- **`ddd_implementation_guide.md`**
  - DDD架构在MythQuant中的实施指南
  - 分层架构和依赖管理
  - 向后兼容和渐进式升级策略

### 🔧 技术实践 (`technical/`)
具体技术实现和最佳实践

- **`workflow_compliance_knowledge.md`**
  - workflow规范遵循的知识库
  - 典型违规案例分析和解决方案
  - 规范检查和预防机制

- **`process_design_best_practices.md`**
  - 流程设计的最佳实践和设计模式
  - 流程合理性检查和优化方法
  - 性能监控和错误处理机制

- **`code_review_checklist.md`**
  - 标准化的代码审查检查清单
  - 涵盖workflow规范、代码质量、架构设计等维度
  - 确保代码质量和规范遵循

### 🐛 调试诊断 (`debugging/`)
问题诊断和调试的经验总结

- **`debugging_knowledge_base.md`**
  - 常见问题的调试方法和解决方案
  - 系统性问题分析和根因定位
  - 调试工具和技巧

- **`testing_quality_rules.md`**
  - 测试质量标准和验证规则
  - 测试环境和生产环境一致性要求
  - 质量保证机制和最佳实践

### ❓ 常见问题 (`faq/`)
项目开发中的常见问题和解答

- **`faq_knowledge_base.md`** ⭐ **重点推荐**
  - 按类别组织的常见问题解答
  - 包含问题描述、解决方案、技术要点
  - 持续更新的问题解决经验库

### 📊 配置管理 (`configuration/`)
系统配置和管理的指南

- **`config_management_guide.md`**
  - 统一配置管理的最佳实践
  - 配置结构设计和验证机制
  - 用户友好的配置接口设计

---

## 🎯 快速导航

### 按使用场景导航

#### 🚀 新手入门
1. `faq_knowledge_base.md` - 了解常见问题和解决方案
2. `effective_questioning_guide.md` - 学习高效的协作方式
3. `1min_workflow_improved.md` - 理解标准工作流程

#### 🔧 日常开发
1. `code_review_checklist.md` - 代码审查标准
2. `workflow_compliance_knowledge.md` - 规范遵循检查
3. `questioning_templates.md` - 快速构建问题描述

#### 🏗️ 架构设计
1. `ddd_implementation_guide.md` - DDD架构实施
2. `process_design_best_practices.md` - 流程设计原则
3. `architecture/` - 架构设计指南

#### 🐛 问题解决
1. `debugging_knowledge_base.md` - 调试方法和工具
2. `effective_questioning_guide.md` - 高效问题描述
3. `faq_knowledge_base.md` - 常见问题解答

### 按问题类型导航

#### 架构设计问题
- `ddd_implementation_guide.md`
- `process_design_best_practices.md`
- FAQ Q1, Q2

#### 流程规范问题
- `workflow_compliance_knowledge.md`
- `1min_workflow_improved.md`
- FAQ Q4, Q5

#### 沟通协作问题
- `effective_questioning_guide.md`
- `questioning_templates.md`
- FAQ Q6

#### 配置管理问题
- `config_management_guide.md`
- FAQ Q3

---

## 📈 知识库使用统计

### 最受欢迎的文档
1. `effective_questioning_guide.md` - 高效提问指南
2. `faq_knowledge_base.md` - 常见问题解答
3. `workflow_compliance_knowledge.md` - workflow规范遵循

### 最新更新的文档
1. `effective_questioning_guide.md` (2025-08-08)
2. `questioning_templates.md` (2025-08-08)
3. `faq_knowledge_base.md` (2025-08-08)

---

## 🔄 知识库维护

### 更新原则
- **实践驱动**: 基于实际项目经验更新内容
- **持续改进**: 根据使用反馈不断优化
- **结构化组织**: 保持清晰的分类和索引
- **版本管理**: 记录重要的更新和变更

### 贡献方式
- 提交新的知识点和经验总结
- 报告文档中的问题和改进建议
- 分享成功的实践案例
- 参与知识库的评审和优化

### 质量标准
- **准确性**: 确保内容的技术准确性
- **实用性**: 提供可操作的指导和建议
- **完整性**: 包含必要的背景信息和上下文
- **可读性**: 使用清晰的结构和表达

---

## 🎓 学习路径建议

### 初级开发者
```
1. 阅读 faq_knowledge_base.md 了解常见问题
2. 学习 effective_questioning_guide.md 提升协作效率
3. 熟悉 1min_workflow_improved.md 理解工作流程
4. 使用 questioning_templates.md 规范问题描述
```

### 中级开发者
```
1. 深入学习 workflow_compliance_knowledge.md
2. 掌握 process_design_best_practices.md
3. 应用 code_review_checklist.md 提升代码质量
4. 参与 debugging_knowledge_base.md 的实践
```

### 高级开发者/架构师
```
1. 研究 ddd_implementation_guide.md 架构设计
2. 贡献 architecture/ 目录的设计文档
3. 完善 technical/ 目录的最佳实践
4. 指导团队使用知识库提升整体水平
```

---

## 📞 支持和反馈

### 获取帮助
- 查阅相关文档和FAQ
- 使用 `effective_questioning_guide.md` 中的方法提问
- 参考 `questioning_templates.md` 构建问题描述

### 提供反馈
- 报告文档中的错误或不准确信息
- 建议新的知识点和改进方向
- 分享使用经验和成功案例

---

**💡 提示**: 知识库是活的文档，会随着项目的发展不断更新和完善。建议定期查看更新，并积极参与知识的贡献和分享。

**🎯 目标**: 通过系统化的知识管理，提升团队的技术能力和协作效率，确保项目的高质量交付。
