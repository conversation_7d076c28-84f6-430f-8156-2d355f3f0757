#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
SOLID原则强化实施

提供SOLID原则的具体实现模板和检查工具
"""

from abc import ABC, abstractmethod
from typing import Protocol, TypeVar, Generic, List, Dict, Any, Optional
from dataclasses import dataclass
from enum import Enum
import inspect
import ast


# =============================================================================
# S - Single Responsibility Principle (单一职责原则)
# =============================================================================

class ResponsibilityValidator:
    """职责验证器 - 检查类是否违反SRP"""
    
    @staticmethod
    def validate_class(cls) -> List[str]:
        """验证类的单一职责"""
        violations = []
        
        # 检查方法数量
        methods = [m for m in dir(cls) if not m.startswith('_')]
        if len(methods) > 10:
            violations.append(f"类 {cls.__name__} 有 {len(methods)} 个公共方法，可能职责过多")
        
        # 检查方法名称模式
        method_patterns = {}
        for method in methods:
            if hasattr(cls, method):
                method_obj = getattr(cls, method)
                if callable(method_obj):
                    # 分析方法名称模式
                    if method.startswith('get_') or method.startswith('set_'):
                        method_patterns['accessor'] = method_patterns.get('accessor', 0) + 1
                    elif method.startswith('calculate_') or method.startswith('compute_'):
                        method_patterns['calculation'] = method_patterns.get('calculation', 0) + 1
                    elif method.startswith('save_') or method.startswith('load_'):
                        method_patterns['persistence'] = method_patterns.get('persistence', 0) + 1
                    elif method.startswith('send_') or method.startswith('notify_'):
                        method_patterns['communication'] = method_patterns.get('communication', 0) + 1
        
        # 如果有多种职责模式，可能违反SRP
        if len(method_patterns) > 2:
            violations.append(f"类 {cls.__name__} 包含多种职责: {list(method_patterns.keys())}")
        
        return violations


# 正确的SRP实现示例
class StockPriceCalculator:
    """股票价格计算器 - 单一职责：价格计算"""
    
    def calculate_adjusted_price(self, price: float, adjustment_factor: float) -> float:
        """计算调整后价格"""
        return price * adjustment_factor
    
    def calculate_return_rate(self, current_price: float, previous_price: float) -> float:
        """计算收益率"""
        return (current_price - previous_price) / previous_price


class StockDataRepository:
    """股票数据仓储 - 单一职责：数据持久化"""
    
    async def save_stock_data(self, stock_data: Dict[str, Any]) -> bool:
        """保存股票数据"""
        pass
    
    async def load_stock_data(self, stock_code: str) -> Optional[Dict[str, Any]]:
        """加载股票数据"""
        pass


class StockNotificationService:
    """股票通知服务 - 单一职责：通知发送"""
    
    async def send_price_alert(self, stock_code: str, price: float) -> bool:
        """发送价格警报"""
        pass
    
    async def send_analysis_report(self, report: Dict[str, Any]) -> bool:
        """发送分析报告"""
        pass


# =============================================================================
# O - Open/Closed Principle (开闭原则)
# =============================================================================

class TechnicalIndicator(ABC):
    """技术指标接口 - 对扩展开放，对修改关闭"""
    
    @abstractmethod
    def calculate(self, prices: List[float]) -> List[float]:
        """计算技术指标"""
        pass
    
    @abstractmethod
    def get_name(self) -> str:
        """获取指标名称"""
        pass


class MovingAverage(TechnicalIndicator):
    """移动平均线"""
    
    def __init__(self, period: int):
        self.period = period
    
    def calculate(self, prices: List[float]) -> List[float]:
        """计算移动平均线"""
        if len(prices) < self.period:
            return []
        
        result = []
        for i in range(self.period - 1, len(prices)):
            avg = sum(prices[i - self.period + 1:i + 1]) / self.period
            result.append(avg)
        return result
    
    def get_name(self) -> str:
        return f"MA{self.period}"


class RSI(TechnicalIndicator):
    """相对强弱指标"""
    
    def __init__(self, period: int = 14):
        self.period = period
    
    def calculate(self, prices: List[float]) -> List[float]:
        """计算RSI"""
        if len(prices) < self.period + 1:
            return []
        
        # RSI计算逻辑
        gains = []
        losses = []
        
        for i in range(1, len(prices)):
            change = prices[i] - prices[i-1]
            if change > 0:
                gains.append(change)
                losses.append(0)
            else:
                gains.append(0)
                losses.append(abs(change))
        
        # 简化的RSI计算
        result = []
        for i in range(self.period - 1, len(gains)):
            avg_gain = sum(gains[i - self.period + 1:i + 1]) / self.period
            avg_loss = sum(losses[i - self.period + 1:i + 1]) / self.period
            
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            
            result.append(rsi)
        
        return result
    
    def get_name(self) -> str:
        return f"RSI{self.period}"


class IndicatorCalculator:
    """指标计算器 - 遵循开闭原则"""
    
    def __init__(self):
        self._indicators: List[TechnicalIndicator] = []
    
    def add_indicator(self, indicator: TechnicalIndicator):
        """添加新指标 - 扩展功能"""
        self._indicators.append(indicator)
    
    def calculate_all(self, prices: List[float]) -> Dict[str, List[float]]:
        """计算所有指标"""
        results = {}
        for indicator in self._indicators:
            results[indicator.get_name()] = indicator.calculate(prices)
        return results


# =============================================================================
# L - Liskov Substitution Principle (里氏替换原则)
# =============================================================================

class DataSource(ABC):
    """数据源基类"""
    
    @abstractmethod
    async def fetch_data(self, symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取数据"""
        pass
    
    @abstractmethod
    def is_available(self) -> bool:
        """检查数据源是否可用"""
        pass


class TDXDataSource(DataSource):
    """TDX数据源 - 可以替换基类"""
    
    async def fetch_data(self, symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """从TDX获取数据"""
        # TDX特定的数据获取逻辑
        return [{"symbol": symbol, "price": 10.0, "date": start_date}]
    
    def is_available(self) -> bool:
        """检查TDX是否可用"""
        return True


class DatabaseDataSource(DataSource):
    """数据库数据源 - 可以替换基类"""
    
    async def fetch_data(self, symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """从数据库获取数据"""
        # 数据库特定的数据获取逻辑
        return [{"symbol": symbol, "price": 10.5, "date": start_date}]
    
    def is_available(self) -> bool:
        """检查数据库是否可用"""
        return True


class DataManager:
    """数据管理器 - 可以使用任何DataSource子类"""
    
    def __init__(self, data_source: DataSource):
        self._data_source = data_source
    
    async def get_stock_data(self, symbol: str, start_date: str, end_date: str) -> List[Dict[str, Any]]:
        """获取股票数据 - 不关心具体的数据源实现"""
        if not self._data_source.is_available():
            raise RuntimeError("数据源不可用")
        
        return await self._data_source.fetch_data(symbol, start_date, end_date)


# =============================================================================
# I - Interface Segregation Principle (接口隔离原则)
# =============================================================================

# 违反ISP的大接口
class BadStockService(Protocol):
    """违反ISP的接口 - 包含太多职责"""
    def get_price(self, symbol: str) -> float: ...
    def calculate_ma(self, prices: List[float]) -> List[float]: ...
    def save_to_database(self, data: Dict[str, Any]) -> bool: ...
    def send_email_alert(self, message: str) -> bool: ...
    def generate_pdf_report(self, data: Dict[str, Any]) -> bytes: ...


# 正确的ISP实现 - 分离接口
class PriceProvider(Protocol):
    """价格提供者接口"""
    def get_price(self, symbol: str) -> float: ...


class IndicatorCalculator(Protocol):
    """指标计算器接口"""
    def calculate_ma(self, prices: List[float]) -> List[float]: ...


class DataPersistence(Protocol):
    """数据持久化接口"""
    def save_to_database(self, data: Dict[str, Any]) -> bool: ...


class NotificationService(Protocol):
    """通知服务接口"""
    def send_email_alert(self, message: str) -> bool: ...


class ReportGenerator(Protocol):
    """报告生成器接口"""
    def generate_pdf_report(self, data: Dict[str, Any]) -> bytes: ...


# 客户端只依赖需要的接口
class PriceMonitor:
    """价格监控器 - 只依赖价格提供者接口"""
    
    def __init__(self, price_provider: PriceProvider):
        self._price_provider = price_provider
    
    def monitor_price(self, symbol: str) -> float:
        return self._price_provider.get_price(symbol)


# =============================================================================
# D - Dependency Inversion Principle (依赖倒置原则)
# =============================================================================

# 高层模块
class StockAnalysisService:
    """股票分析服务 - 高层模块，依赖抽象"""
    
    def __init__(self, 
                 data_source: DataSource,
                 calculator: IndicatorCalculator,
                 repository: DataPersistence):
        self._data_source = data_source  # 依赖抽象
        self._calculator = calculator    # 依赖抽象
        self._repository = repository    # 依赖抽象
    
    async def analyze_stock(self, symbol: str) -> Dict[str, Any]:
        """分析股票"""
        # 获取数据
        data = await self._data_source.fetch_data(symbol, "2024-01-01", "2024-12-31")
        
        # 计算指标
        prices = [item["price"] for item in data]
        ma_values = self._calculator.calculate_ma(prices)
        
        # 保存结果
        result = {"symbol": symbol, "ma": ma_values}
        self._repository.save_to_database(result)
        
        return result


# =============================================================================
# SOLID原则检查工具
# =============================================================================

class SOLIDChecker:
    """SOLID原则检查器"""
    
    @staticmethod
    def check_srp(cls) -> List[str]:
        """检查单一职责原则"""
        return ResponsibilityValidator.validate_class(cls)
    
    @staticmethod
    def check_ocp(cls) -> List[str]:
        """检查开闭原则"""
        violations = []
        
        # 检查是否有抽象方法
        if not inspect.isabstract(cls):
            abstract_methods = [m for m in dir(cls) if getattr(getattr(cls, m, None), '__isabstractmethod__', False)]
            if not abstract_methods and not hasattr(cls, '__subclasses__'):
                violations.append(f"类 {cls.__name__} 可能需要抽象化以支持扩展")
        
        return violations
    
    @staticmethod
    def check_lsp(cls, base_cls) -> List[str]:
        """检查里氏替换原则"""
        violations = []
        
        if not issubclass(cls, base_cls):
            violations.append(f"类 {cls.__name__} 不是 {base_cls.__name__} 的子类")
            return violations
        
        # 检查方法签名是否兼容
        base_methods = {name: method for name, method in inspect.getmembers(base_cls, inspect.ismethod)}
        cls_methods = {name: method for name, method in inspect.getmembers(cls, inspect.ismethod)}
        
        for method_name, base_method in base_methods.items():
            if method_name in cls_methods:
                cls_method = cls_methods[method_name]
                base_sig = inspect.signature(base_method)
                cls_sig = inspect.signature(cls_method)
                
                if base_sig != cls_sig:
                    violations.append(f"方法 {method_name} 的签名不兼容")
        
        return violations
    
    @staticmethod
    def check_isp(protocol_cls) -> List[str]:
        """检查接口隔离原则"""
        violations = []
        
        if hasattr(protocol_cls, '__annotations__'):
            methods = [name for name in protocol_cls.__annotations__.keys()]
            if len(methods) > 5:  # 经验值：接口方法不应超过5个
                violations.append(f"接口 {protocol_cls.__name__} 包含 {len(methods)} 个方法，可能需要拆分")
        
        return violations
    
    @staticmethod
    def check_dip(cls) -> List[str]:
        """检查依赖倒置原则"""
        violations = []
        
        # 检查构造函数参数是否依赖具体类
        if hasattr(cls, '__init__'):
            sig = inspect.signature(cls.__init__)
            for param_name, param in sig.parameters.items():
                if param_name != 'self' and param.annotation:
                    if inspect.isclass(param.annotation) and not inspect.isabstract(param.annotation):
                        violations.append(f"参数 {param_name} 依赖具体类 {param.annotation.__name__}")
        
        return violations


# =============================================================================
# 使用示例和测试
# =============================================================================

def demonstrate_solid_principles():
    """演示SOLID原则的使用"""
    
    # SRP示例
    calculator = StockPriceCalculator()
    adjusted_price = calculator.calculate_adjusted_price(100.0, 1.1)
    
    # OCP示例
    indicator_calc = IndicatorCalculator()
    indicator_calc.add_indicator(MovingAverage(20))
    indicator_calc.add_indicator(RSI(14))
    
    prices = [10.0, 10.5, 11.0, 10.8, 11.2]
    results = indicator_calc.calculate_all(prices)
    
    # LSP示例
    tdx_source = TDXDataSource()
    db_source = DatabaseDataSource()
    
    # 两个数据源可以互相替换
    manager1 = DataManager(tdx_source)
    manager2 = DataManager(db_source)
    
    # ISP示例 - 客户端只依赖需要的接口
    monitor = PriceMonitor(tdx_source)  # 只需要价格提供功能
    
    print("SOLID原则演示完成")


if __name__ == "__main__":
    demonstrate_solid_principles()
