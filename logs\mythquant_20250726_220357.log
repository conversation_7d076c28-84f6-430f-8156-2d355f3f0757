2025-07-26 22:03:57,907 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_220357.log
2025-07-26 22:03:57,907 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 22:03:57,932 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 22:03:57,933 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 22:03:57,934 - Main - INFO - info:307 - ℹ️ ✅ 使用缓存的最佳服务器: *************:7709
2025-07-26 22:03:57,935 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-26 22:03:57,935 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-26 22:03:57,935 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 22:03:57,936 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 22:03:57,936 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 22:03:57,936 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 22:03:58,461 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 22:03:58,461 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 22:03:58,468 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 22:03:58,468 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 22:03:58,469 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 22:03:58,469 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 22:03:58,469 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 22:03:58,469 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 22:03:58,470 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 22:03:58,474 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 22:03:58,474 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 22:03:58,474 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 22:03:58,474 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 22:03:58,481 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 22:03:59,004 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.52秒
2025-07-26 22:03:59,005 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.52秒
2025-07-26 22:03:59,005 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 22:03:59,005 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 22:03:59,005 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 22:03:59,005 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 22:03:59,005 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 22:03:59,006 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 22:03:59,006 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 22:03:59,006 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 22:03:59,006 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 22:03:59,007 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 22:03:59,007 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 22:03:59,142 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 22:03:59,142 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 22:03:59,151 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 22:03:59,151 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 22:03:59,152 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 22:03:59,152 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 22:03:59,152 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 22:03:59,152 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 22:03:59,152 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 22:03:59,158 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 22:03:59,158 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 22:03:59,159 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 22:03:59,159 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 22:03:59,166 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 22:03:59,812 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.64秒
2025-07-26 22:03:59,812 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.64秒
2025-07-26 22:04:10,595 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 22:04:36,003 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 22:04:36,003 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 22:04:36,003 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 22:04:36,013 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 22:04:36,564 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 22:04:36,565 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 22:04:36,565 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 22:04:36,565 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 22:04:36,566 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 22:04:36,566 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 22:04:36,566 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 22:04:36,566 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 22:04:36,566 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 22:04:36,609 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 22:04:37,110 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 22:04:37,120 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 22:04:37,133 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 22:04:37,145 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 22:04:37,156 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 22:04:37,168 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 22:04:37,185 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 22:04:37,202 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 22:04:37,212 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 22:04:37,225 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 22:04:37,236 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 22:04:37,247 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 22:04:37,258 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 22:04:37,269 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 22:04:37,280 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 22:04:37,291 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 22:04:37,301 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 22:04:37,313 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 22:04:37,324 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 22:04:37,334 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 22:04:37,347 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 22:04:37,347 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 22:04:41,737 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 22:04:41,737 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 22:04:41,738 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 22:04:41,738 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 22:04:41,738 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 22:04:41,740 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 22:04:41,836 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-26 22:04:41,836 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 22:04:41,837 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 22:04:41,837 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 22:04:41,837 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 22:04:41,837 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 22:04:41,837 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 22:04:41,845 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 22:04:41,983 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 22:04:42,494 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 22:04:42,663 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 22:04:42,663 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 22:04:42,729 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 22:04:42,729 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 22:04:42,730 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 22:04:42,730 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-26 22:04:42,730 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-26 22:04:42,730 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-26 22:04:42,730 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 22:04:42,745 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 43.74 秒
2025-07-26 22:04:42,745 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 22:04:42,746 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 22:04:42,746 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 22:04:42,747 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 22:04:42,748 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 22:04:43,137 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 22:04:43,151 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 22:04:43,152 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 22:04:43,152 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 22:04:43,152 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-26 22:04:43,152 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 22:04:43,152 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 22:04:43,152 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 22:04:43,152 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 22:04:43,155 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-26 22:04:43,155 - core.logging_service - INFO - verbose_log:67 - ✅ 加载智能文件选择器配置: 策略=smart_comprehensive
2025-07-26 22:04:43,155 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-26 22:04:43,155 - core.logging_service - INFO - verbose_log:67 - 🔍 发现5个候选文件，开始智能选择
2025-07-26 22:04:43,156 - core.logging_service - INFO - verbose_log:67 - 📋 候选文件分析:
2025-07-26 22:04:43,156 - core.logging_service - INFO - verbose_log:67 -   1. 1min_0_000617_20250301-20250705_来源互联网（202507262158）.txt
2025-07-26 22:04:43,156 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250301 ~ 20250705 (127天)
2025-07-26 22:04:43,156 - core.logging_service - INFO - verbose_log:67 -      文件大小: 932.2KB, 修改时间: 2025-07-26 21:31
2025-07-26 22:04:43,156 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=344.0, 覆盖度=127.0, 总分=172.3
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -   2. 1min_0_000617_20250401-20250701_来源互联网（202507262158）.txt
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250401 ~ 20250701 (92天)
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -      文件大小: 657.7KB, 修改时间: 2025-07-26 21:12
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=340.0, 覆盖度=92.0, 总分=152.1
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -   3. 1min_0_000617_20250401-20250726_来源互联网（202507262113）.txt
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250401 ~ 20250726 (117天)
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -      文件大小: 866.4KB, 修改时间: 2025-07-26 15:40
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=365.0, 覆盖度=117.0, 总分=173.2
2025-07-26 22:04:43,157 - core.logging_service - INFO - verbose_log:67 -   4. 1min_0_000617_20250416-20250703_来源互联网（202507262158）.txt
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250416 ~ 20250703 (79天)
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 -      文件大小: 571.3KB, 修改时间: 2025-07-26 17:03
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=342.0, 覆盖度=79.0, 总分=145.6
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 -   5. 1min_0_000617_20250703-20250703_来源互联网（202507262158）.txt
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250703 ~ 20250703 (1天)
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 -      文件大小: 10.1KB, 修改时间: 2025-07-26 21:06
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=342.0, 覆盖度=1.0, 总分=103.1
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 - ✅ 选择文件: 1min_0_000617_20250401-20250726_来源互联网（202507262113）.txt (策略: smart_comprehensive, 评分: 173.18)
2025-07-26 22:04:43,158 - core.logging_service - INFO - verbose_log:67 - 🎯 选择原因 (策略: smart_comprehensive):
2025-07-26 22:04:43,159 - core.logging_service - INFO - verbose_log:67 -   综合评分最高: 173.18
2025-07-26 22:04:43,159 - core.logging_service - INFO - verbose_log:67 -     新鲜度: 365.0/365
2025-07-26 22:04:43,159 - core.logging_service - INFO - verbose_log:67 -     覆盖度: 117.0天
2025-07-26 22:04:43,159 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250401-20250726_来源互联网（202507262113）.txt
2025-07-26 22:04:43,159 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250401-20250726_来源互联网（202507262113）.txt
2025-07-26 22:04:43,159 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-26 22:04:43,159 - core.logging_service - INFO - verbose_log:67 - 📊 分析现有文件时间跨度: 1min_0_000617_20250401-20250726_来源互联网（202507262113）.txt
2025-07-26 22:04:43,161 - core.logging_service - INFO - verbose_log:67 - 📅 现有文件时间跨度: 20250401 ~ 20250725 (18960条记录)
2025-07-26 22:04:43,161 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-26 22:04:43,161 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250401 ~ 20250725
2025-07-26 22:04:43,162 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-26 22:04:43,162 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-26 22:04:43,162 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-26 22:04:43,162 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-26 22:04:43,162 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-26 22:04:43,163 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-26 22:04:43,163 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250331
2025-07-26 22:04:43,163 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250726 ~ 20250727
2025-07-26 22:04:43,163 - core.logging_service - INFO - verbose_log:67 - 🔍 验证最后一条记录的数据一致性
2025-07-26 22:04:43,166 - core.logging_service - INFO - verbose_log:67 - 📋 文件最后记录: 时间=202507251500, 前复权价=8.9
2025-07-26 22:04:43,166 - core.logging_service - INFO - verbose_log:67 - 🔄 从API获取 20250725 的数据进行比对验证
2025-07-26 22:04:43,167 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 22:04:43,167 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 22:04:43,167 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-26 22:04:43,167 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:43,346 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:43,346 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 1天, 频率: 1min)
2025-07-26 22:04:43,346 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-26 22:04:43,347 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 22:04:43,347 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 22:04:43,399 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-26 22:04:43,399 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:43,570 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:43,616 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-26 22:04:43,616 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-26 22:04:43,619 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-26 22:04:43,621 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-26 22:04:43,621 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 22:04:43,623 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-26 22:04:43,623 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 22:04:43,624 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-26 22:04:43,624 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-26 22:04:43,624 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-26 22:04:43,624 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 22:04:43,624 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-26 22:04:43,624 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 22:04:43,626 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 22:04:43,626 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 22:04:43,630 - core.logging_service - INFO - verbose_log:67 - 📋 API对应记录: 时间=202507251500, 前复权价=8.9
2025-07-26 22:04:43,631 - core.logging_service - INFO - verbose_log:67 - 📊 数据一致性比对结果:
2025-07-26 22:04:43,631 - core.logging_service - INFO - verbose_log:67 -   时间比对: 文件=202507251500 vs API=202507251500 -> ✅ 一致
2025-07-26 22:04:43,631 - core.logging_service - INFO - verbose_log:67 -   价格比对: 文件=8.9 vs API=8.9 -> ✅ 一致
2025-07-26 22:04:43,631 - core.logging_service - INFO - verbose_log:67 - 🎯 最终判断: ✅ 数据一致，可以使用增量下载
2025-07-26 22:04:43,632 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-26 22:04:43,636 - core.logging_service - INFO - verbose_log:67 - 获取最后一条记录: 时间=202507251500, 前复权价=8.9
2025-07-26 22:04:43,636 - core.logging_service - INFO - verbose_log:67 - 时间格式对比: 文件=202507251500, API=202507251500, 级别=1min
2025-07-26 22:04:43,637 - core.logging_service - INFO - verbose_log:67 - ✅ 时间格式一致性验证通过
2025-07-26 22:04:43,637 - core.logging_service - INFO - verbose_log:67 - ✅ 价格一致性验证通过
2025-07-26 22:04:43,637 - core.logging_service - INFO - verbose_log:67 - ✅ 数据一致性验证通过，可以使用增量下载
2025-07-26 22:04:43,637 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-26 22:04:43,637 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250401 - 20250726
2025-07-26 22:04:43,637 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250331
2025-07-26 22:04:43,638 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250727 - 20250727
2025-07-26 22:04:43,644 - Main - INFO - info:307 - ℹ️ 读取现有数据: 18960 条记录
2025-07-26 22:04:43,645 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250331
2025-07-26 22:04:43,645 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 22:04:43,645 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 22:04:43,645 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250331
2025-07-26 22:04:43,645 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:43,819 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:43,819 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 42624条 (目标日期: 20250101, 交易日: 148天, 频率: 1min)
2025-07-26 22:04:43,819 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计42624条 -> 实际请求800条 (配置限制:800)
2025-07-26 22:04:43,819 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 22:04:43,820 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 22:04:43,873 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需41824条
2025-07-26 22:04:43,873 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:44,042 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:44,093 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 22:04:44,094 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:44,263 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:44,312 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 22:04:44,313 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:44,485 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:44,537 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 22:04:44,537 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:44,715 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:44,769 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 22:04:44,769 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:44,951 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:45,002 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 22:04:45,003 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:45,176 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:45,226 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 22:04:45,227 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:45,396 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:45,447 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 22:04:45,447 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:45,624 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:45,675 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 22:04:45,676 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:45,850 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:45,903 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 22:04:45,903 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:46,080 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:46,133 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 22:04:46,133 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:46,313 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:46,367 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 22:04:46,367 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:46,547 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:46,599 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 22:04:46,599 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:46,778 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:46,830 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 22:04:46,831 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:47,005 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:47,060 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 22:04:47,060 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:47,230 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:47,281 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 22:04:47,282 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:47,462 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:47,514 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 22:04:47,515 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:47,693 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:47,745 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 22:04:47,745 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:47,918 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:47,970 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 22:04:47,971 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:48,147 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:48,199 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 22:04:48,199 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:48,375 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:48,426 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 22:04:48,427 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:48,599 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:48,649 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 22:04:48,650 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:48,831 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:48,883 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 22:04:48,883 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:49,051 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:49,100 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 22:04:49,101 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:49,279 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:49,332 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 22:04:49,332 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:49,515 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:49,573 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 22:04:49,573 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:49,741 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:49,793 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 22:04:49,793 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:49,963 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:50,015 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 22:04:50,015 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:50,194 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:50,246 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 22:04:50,246 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:50,420 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:50,471 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 22:04:50,472 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:50,654 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:50,701 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 22:04:50,760 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 5040 条 1min 数据
2025-07-26 22:04:50,766 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共5040条记录
2025-07-26 22:04:50,766 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 22:04:50,785 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.290, 前复权=6.290
2025-07-26 22:04:50,786 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 22:04:50,786 - Main - INFO - info:307 - ℹ️   日期: 202503030931
2025-07-26 22:04:50,786 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.29
2025-07-26 22:04:50,786 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.29
2025-07-26 22:04:50,786 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 22:04:50,786 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 5040 >= 5
2025-07-26 22:04:50,787 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 22:04:50,787 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 22:04:50,787 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 22:04:50,787 - Main - INFO - info:307 - ℹ️ 获得新数据: 5040 条记录
2025-07-26 22:04:50,787 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250727 - 20250727
2025-07-26 22:04:50,787 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 22:04:50,787 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 22:04:50,787 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250727 - 20250727
2025-07-26 22:04:50,788 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:50,965 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:50,965 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250727, 交易日: 0天, 频率: 1min)
2025-07-26 22:04:50,966 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-26 22:04:50,966 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 22:04:50,966 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 22:04:51,016 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-26 22:04:51,016 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:51,197 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:51,245 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-26 22:04:51,245 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-26 22:04:51,249 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250727 - 20250727
2025-07-26 22:04:51,249 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-26 22:04:51,250 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-26 22:04:51,252 - Main - ERROR - error:315 - ❌ 合并数据文件失败: '<' not supported between instances of 'str' and 'int'
2025-07-26 22:04:51,253 - Main - ERROR - error:315 - ❌ ❌ 增量下载失败，尝试全量下载
2025-07-26 22:04:51,253 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-26 22:04:51,253 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 22:04:51,253 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 22:04:51,253 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-26 22:04:51,253 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:51,426 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:51,427 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 42624条 (目标日期: 20250101, 交易日: 148天, 频率: 1min)
2025-07-26 22:04:51,427 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计42624条 -> 实际请求800条 (配置限制:800)
2025-07-26 22:04:51,427 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 22:04:51,427 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 22:04:51,477 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需41824条
2025-07-26 22:04:51,477 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:51,660 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:51,712 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 22:04:51,713 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:51,885 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:51,936 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 22:04:51,936 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:52,104 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:52,154 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 22:04:52,154 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:52,332 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:52,386 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 22:04:52,386 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:52,557 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:52,612 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 22:04:52,612 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:52,791 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:52,845 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 22:04:52,845 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:53,021 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:53,071 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 22:04:53,071 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:53,243 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:53,294 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 22:04:53,294 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:53,473 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:53,526 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 22:04:53,526 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:53,702 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:53,753 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 22:04:53,754 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:53,923 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:53,974 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 22:04:53,974 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:54,153 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:54,206 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 22:04:54,207 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:54,381 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:54,432 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 22:04:54,432 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:54,604 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:54,654 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 22:04:54,654 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:54,828 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:54,881 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 22:04:54,881 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:55,061 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:55,113 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 22:04:55,113 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:55,288 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:55,339 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 22:04:55,339 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:55,513 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:55,564 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 22:04:55,565 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:55,741 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:55,793 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 22:04:55,793 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:55,972 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:56,024 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 22:04:56,024 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:56,201 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:56,252 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 22:04:56,252 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:56,436 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:56,488 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 22:04:56,488 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:56,667 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:56,718 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 22:04:56,719 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:56,890 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:56,943 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 22:04:56,943 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:57,109 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:57,159 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 22:04:57,159 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:57,343 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:57,398 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 22:04:57,398 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:57,576 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:57,628 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 22:04:57,628 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:57,806 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:57,863 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 22:04:57,864 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:58,043 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:58,095 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 22:04:58,095 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:04:58,275 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:04:58,321 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 22:04:58,379 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 24000 条 1min 数据
2025-07-26 22:04:58,389 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共24000条记录
2025-07-26 22:04:58,389 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 22:04:58,473 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.290, 前复权=6.290
2025-07-26 22:04:58,478 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 22:04:58,479 - Main - INFO - info:307 - ℹ️   日期: 202503030931
2025-07-26 22:04:58,479 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.29
2025-07-26 22:04:58,479 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.29
2025-07-26 22:04:58,479 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 22:04:58,479 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 24000 >= 5
2025-07-26 22:04:58,479 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 22:04:58,480 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 22:04:58,480 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 22:04:58,542 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250101-20250727_来源互联网（202507262204）.txt (1123291 字节)
2025-07-26 22:04:58,543 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 22:04:58,543 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 22:04:58,543 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-26 22:04:58,543 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-26 22:04:58,543 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 22:04:58,543 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 22:04:58,544 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 22:04:58,545 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 22:04:58,545 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 22:04:58,545 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 22:04:58,545 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: day_0_000617_20240701-20240731_来源互联网（202507262158）.txt
2025-07-26 22:04:58,564 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20200101-20250724.txt, 数据行数: 1337
2025-07-26 22:04:58,565 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20240701-20240731_来源互联网（202507262158）.txt, 数据行数: 23
2025-07-26 22:04:58,567 - Main - INFO - info:307 - ℹ️ 数据对齐完成，共同日期数: 23
2025-07-26 22:04:58,570 - Main - INFO - info:307 - ℹ️ 价格差异计算完成，数据点数: 23
2025-07-26 22:04:58,573 - Main - INFO - info:307 - ℹ️ 股票 000617 数据比较完成
2025-07-26 22:04:58,573 - Main - INFO - info:307 - ℹ️ ✅ 000617 分析完成
2025-07-26 22:04:58,576 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_220458.txt
2025-07-26 22:04:58,576 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 1/1 成功 (100.0%)
2025-07-26 22:04:58,576 - Main - INFO - info:307 - ℹ️ 任务执行成功: 前复权数据比较分析
2025-07-26 22:04:58,576 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 100.0%
2025-07-26 22:04:58,577 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 22:04:58,577 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 22:04:58,577 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 22:04:58,577 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 22:04:58,577 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 22:04:58,577 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 22:04:58,577 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
