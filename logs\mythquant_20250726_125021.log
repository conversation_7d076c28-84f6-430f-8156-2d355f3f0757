2025-07-26 12:50:21,520 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_125021.log
2025-07-26 12:50:21,520 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 12:50:21,540 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 12:50:21,542 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 12:50:21,542 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 12:50:21,542 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-26 12:50:21,542 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-26 12:50:21,803 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.12.136.250 (60.12.136.250:7709) - 0.168s
2025-07-26 12:50:21,820 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_218.75.126.9 (218.75.126.9:7709) - 0.181s
2025-07-26 12:50:21,826 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.188s
2025-07-26 12:50:21,844 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.190s
2025-07-26 12:50:21,846 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.187s
2025-07-26 12:50:21,846 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.198s
2025-07-26 12:50:21,851 - Main - WARNING - warning:311 - ⚠️ 未找到pytdx_ip配置项，尝试其他模式
2025-07-26 12:50:21,851 - Main - WARNING - warning:311 - ⚠️ 配置文件中未找到pytdx_ip配置项
2025-07-26 12:50:21,851 - Main - ERROR - error:315 - ❌ 配置文件更新失败
2025-07-26 12:50:21,851 - Main - WARNING - warning:311 - ⚠️ pytdx服务器检测失败
2025-07-26 12:50:21,852 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 12:50:21,852 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 12:50:21,852 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 12:50:21,852 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 12:50:22,359 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 12:50:22,360 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 12:50:22,366 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 12:50:22,367 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 12:50:22,367 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 12:50:22,367 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 12:50:22,367 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 12:50:22,368 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 12:50:22,368 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 12:50:22,372 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 12:50:22,372 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 12:50:22,372 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 12:50:22,373 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 12:50:22,379 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 12:50:22,832 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.45秒
2025-07-26 12:50:22,832 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.45秒
2025-07-26 12:50:22,833 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 12:50:22,833 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 12:50:22,833 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 12:50:22,833 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 12:50:22,833 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 12:50:22,834 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 12:50:22,834 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 12:50:22,834 - main_v20230219_optimized - INFO - generate_daily_data_task:3275 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 12:50:22,834 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:3814 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 12:50:22,835 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 12:50:22,835 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 12:50:22,961 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 12:50:22,961 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 12:50:22,968 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 12:50:22,968 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 12:50:22,968 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 12:50:22,969 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 12:50:22,969 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 12:50:22,969 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 12:50:22,969 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 12:50:22,972 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 12:50:22,972 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 12:50:22,973 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 12:50:22,973 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 12:50:22,979 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 12:50:23,637 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.66秒
2025-07-26 12:50:23,637 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.66秒
2025-07-26 12:50:34,170 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 12:50:57,222 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 12:50:57,223 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 12:50:57,223 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 12:50:57,231 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 12:50:57,708 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 12:50:57,709 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 12:50:57,709 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 12:50:57,709 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 12:50:57,711 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 12:50:57,711 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 12:50:57,711 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 12:50:57,712 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 12:50:57,712 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 12:50:57,768 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 12:50:58,239 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 12:50:58,255 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 12:50:58,264 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 12:50:58,283 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 12:50:58,300 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 12:50:58,316 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 12:50:58,332 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 12:50:58,348 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 12:50:58,364 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 12:50:58,374 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 12:50:58,390 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 12:50:58,401 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 12:50:58,410 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 12:50:58,425 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 12:50:58,442 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 12:50:58,459 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 12:50:58,474 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 12:50:58,490 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 12:50:58,507 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 12:50:58,524 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 12:50:58,540 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 12:50:58,541 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 12:51:02,620 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 12:51:02,621 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 12:51:02,621 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 12:51:02,621 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 12:51:02,622 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 12:51:02,624 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 12:51:02,722 - main_v20230219_optimized - INFO - apply_forward_adjustment:1911 - 保留交易时间数据后: 320880条
2025-07-26 12:51:02,723 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 12:51:02,723 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 12:51:02,723 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 12:51:02,723 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 12:51:02,723 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 12:51:02,724 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 12:51:02,735 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 12:51:02,866 - main_v20230219_optimized - INFO - load_and_process_minute_data:2380 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 12:51:03,322 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 12:51:03,607 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 12:51:03,608 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 12:51:15,171 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 12:51:15,171 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 12:51:15,172 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3993 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 12:51:15,172 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3994 - 📊 成功生成: 1 个文件
2025-07-26 12:51:15,172 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3995 - ❌ 处理失败: 0 个股票
2025-07-26 12:51:15,172 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3996 - 📈 成功率: 100.0%
2025-07-26 12:51:15,172 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3997 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 12:51:15,186 - main_v20230219_optimized - INFO - generate_daily_data_task:3285 - ✅ 日线数据生成完成，耗时: 52.35 秒
2025-07-26 12:51:15,187 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 12:51:15,187 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网日线数据下载
2025-07-26 12:51:15,187 - Main - INFO - info:307 - ℹ️ 开始执行互联网日线数据下载任务
2025-07-26 12:51:15,188 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 12:51:15,188 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 12:51:15,519 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 12:51:15,530 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 12:51:15,530 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 12:51:15,530 - Main - INFO - info:307 - ℹ️ 互联网数据下载时间范围: 20170101 - 20250720
2025-07-26 12:51:15,530 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的互联网数据
2025-07-26 12:51:15,531 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 12:51:15,531 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 12:51:15,531 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 12:51:15,531 - Main - INFO - info:307 - ℹ️ 📊 频率转换: d -> daily
2025-07-26 12:51:15,531 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 daily 数据: 20170101 - 20250720
2025-07-26 12:51:15,531 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:15,709 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:15,709 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计749520条 -> 实际请求800条 (配置限制:800)
2025-07-26 12:51:15,709 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 daily 数据
2025-07-26 12:51:15,709 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=9, market=0, code=000617, count=800
2025-07-26 12:51:15,762 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需748720条
2025-07-26 12:51:15,763 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:15,934 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:15,991 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 12:51:15,991 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:16,167 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:16,218 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 12:51:16,218 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:16,398 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:16,451 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 12:51:16,451 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:16,629 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:16,684 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 12:51:16,684 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:16,863 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:16,917 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 12:51:16,917 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:17,085 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:17,135 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 12:51:17,135 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:17,307 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:17,359 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 12:51:17,359 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:17,532 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:17,581 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +355条，总计6755条
2025-07-26 12:51:17,581 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计6755条数据
2025-07-26 12:51:17,597 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 2073 条 daily 数据
2025-07-26 12:51:17,600 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共2073条记录
2025-07-26 12:51:17,600 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 12:51:17,606 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=19.030, 前复权=19.030
2025-07-26 12:51:17,607 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 12:51:17,607 - Main - INFO - info:307 - ℹ️   日期: 20170103
2025-07-26 12:51:17,607 - Main - INFO - info:307 - ℹ️   当日收盘价C: 19.03
2025-07-26 12:51:17,608 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 19.03
2025-07-26 12:51:17,608 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 12:51:17,608 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 2073 >= 5
2025-07-26 12:51:17,608 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 12:51:17,608 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 12:51:17,608 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 12:51:17,611 - Main - INFO - info:307 - ℹ️ 数据无变化，将添加时间戳: day_0_000617_20170101-20250720_来源互联网（202507261251）.txt
2025-07-26 12:51:17,634 - Main - INFO - info:307 - ℹ️ 成功保存000617数据到: H:/MPV1.17/T0002/signals\day_0_000617_20170101-20250720_来源互联网（202507261251）.txt
2025-07-26 12:51:17,635 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 12:51:17,635 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 12:51:17,635 - Main - INFO - info:307 - ℹ️ 互联网数据下载完成: 1/1 成功 (100.0%)
2025-07-26 12:51:17,635 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网日线数据下载
2025-07-26 12:51:17,635 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 12:51:17,635 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 12:51:17,635 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️   时间范围: 20250401 - 20250726
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 12:51:17,636 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 12:51:17,637 - Main - INFO - info:307 - ℹ️ 🔄 开始分钟级数据增量下载: 000617
2025-07-26 12:51:17,637 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250101-20250726_来源互联网.txt
2025-07-26 12:51:17,637 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250101 - 20250726
2025-07-26 12:51:17,647 - Main - INFO - info:307 - ℹ️ 获取最后一条记录: 时间=202507250000.0, 前复权价=8.9
2025-07-26 12:51:17,647 - Main - INFO - info:307 - ℹ️ 检查 000617 在 202507250000.0 的前复权价一致性
2025-07-26 12:51:17,647 - Main - INFO - info:307 - ℹ️ 🎯 严格使用配置的频率: 1 (不允许自动切换频率)
2025-07-26 12:51:17,647 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 12:51:17,647 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 12:51:17,647 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 202507250000.0 - 202507250000.0
2025-07-26 12:51:17,647 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:17,821 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:17,821 - Main - ERROR - error:315 - ❌ 下载分钟数据失败: unconverted data remains: 0000.0
2025-07-26 12:51:17,821 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-26 12:51:17,821 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-26 12:51:17,822 - Main - ERROR - error:315 - ❌ ❌ 无法获取 202507250000.0 的1分钟数据
2025-07-26 12:51:17,822 - Main - ERROR - error:315 - ❌ ❌ 这可能是因为:
2025-07-26 12:51:17,822 - Main - ERROR - error:315 - ❌    1. 该日期没有1分钟级别的交易数据
2025-07-26 12:51:17,822 - Main - ERROR - error:315 - ❌    2. 服务器不支持该时间段的1分钟数据
2025-07-26 12:51:17,822 - Main - ERROR - error:315 - ❌    3. 网络连接问题或服务器限制
2025-07-26 12:51:17,822 - Main - ERROR - error:315 - ❌ 💡 建议: 检查时间范围配置或网络连接
2025-07-26 12:51:17,822 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价不一致，执行全量下载
2025-07-26 12:51:17,822 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-26 12:51:17,822 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 12:51:17,822 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 12:51:17,822 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250401 - 20250726
2025-07-26 12:51:17,823 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:18,004 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:18,005 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计28080条 -> 实际请求1000条 (配置限制:1000)
2025-07-26 12:51:18,005 - Main - INFO - info:307 - ℹ️ 📊 预计获取 1000 条 1min 数据
2025-07-26 12:51:18,005 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=1000
2025-07-26 12:51:18,049 - Main - WARNING - warning:311 - ⚠️ ⚠️ 第1次尝试未获得数据
2025-07-26 12:51:19,109 - Main - WARNING - warning:311 - ⚠️ ⚠️ 第2次尝试未获得数据
2025-07-26 12:51:20,161 - Main - WARNING - warning:311 - ⚠️ ⚠️ 第3次尝试未获得数据
2025-07-26 12:51:20,162 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未获得 000617 的数据
2025-07-26 12:51:20,162 - Main - INFO - info:307 - ℹ️ 🔄 尝试上海市场获取 000617 数据
2025-07-26 12:51:20,162 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:51:20,330 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:51:20,375 - Main - WARNING - warning:311 - ⚠️ ⚠️ 上海市场也未找到 000617 数据
2025-07-26 12:51:20,375 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-26 12:51:20,375 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-26 12:51:20,375 - Main - ERROR - error:315 - ❌ ❌ 000617 下载失败
2025-07-26 12:51:20,375 - Main - INFO - info:307 - ℹ️ 批量下载完成: 0/1 成功
2025-07-26 12:51:20,375 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 0/1 成功 (0.0%)
2025-07-26 12:51:20,375 - Main - ERROR - error:315 - ❌ 任务执行失败: 互联网分钟级数据下载
2025-07-26 12:51:20,376 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 12:51:20,376 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 12:51:20,377 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 12:51:20,377 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 12:51:20,377 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 12:51:20,377 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 12:51:20,377 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: day_0_000617_20240701-20240731_来源互联网.txt
2025-07-26 12:51:20,397 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20200101-20250724.txt, 数据行数: 1337
2025-07-26 12:51:20,398 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20240701-20240731_来源互联网.txt, 数据行数: 23
2025-07-26 12:51:20,400 - Main - INFO - info:307 - ℹ️ 数据对齐完成，共同日期数: 23
2025-07-26 12:51:20,402 - Main - INFO - info:307 - ℹ️ 价格差异计算完成，数据点数: 23
2025-07-26 12:51:20,405 - Main - INFO - info:307 - ℹ️ 股票 000617 数据比较完成
2025-07-26 12:51:20,405 - Main - INFO - info:307 - ℹ️ ✅ 000617 分析完成
2025-07-26 12:51:20,408 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_125120.txt
2025-07-26 12:51:20,408 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 1/1 成功 (100.0%)
2025-07-26 12:51:20,409 - Main - INFO - info:307 - ℹ️ 任务执行成功: 前复权数据比较分析
2025-07-26 12:51:20,409 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 75.0%
2025-07-26 12:51:20,410 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 12:51:20,410 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 12:51:20,410 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 12:51:20,410 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 12:51:20,410 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 12:51:20,410 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 12:51:20,410 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
