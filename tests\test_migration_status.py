#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试迁移状态

快速验证配置迁移和数据访问准备情况
"""

import sys
import os
from pathlib import Path

def test_config_compatibility():
    """测试配置兼容性"""
    print("🔍 测试配置兼容性...")
    
    try:
        import config_compatibility as config
        
        # 测试基本配置访问
        tdx_path = config.tdx_path
        debug_mode = config.DEBUG
        output_path = config.output_path
        
        print(f"   ✅ TDX路径: {tdx_path}")
        print(f"   ✅ 调试模式: {debug_mode}")
        print(f"   ✅ 输出路径: {output_path}")
        
        # 测试配置函数
        tdx_func = config.get_tdx_path()
        debug_func = config.is_debug_enabled()
        
        print(f"   ✅ 函数访问TDX: {tdx_func}")
        print(f"   ✅ 函数访问调试: {debug_func}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置兼容性测试失败: {e}")
        return False

def test_new_config_system():
    """测试新配置系统"""
    print("\n🔍 测试新配置系统...")
    
    try:
        # 添加src路径
        project_root = Path(__file__).parent
        src_path = project_root / "src"
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        from mythquant.config import config_manager
        
        # 测试配置管理器
        tdx_path = config_manager.get_tdx_path()
        debug_mode = config_manager.is_debug_enabled()
        
        print(f"   ✅ 新配置TDX路径: {tdx_path}")
        print(f"   ✅ 新配置调试模式: {debug_mode}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 新配置系统测试失败: {e}")
        return False

def test_data_sources():
    """测试数据源架构"""
    print("\n🔍 测试数据源架构...")
    
    try:
        # 添加src路径
        project_root = Path(__file__).parent
        src_path = project_root / "src"
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        from mythquant.data.sources import DataSourceManager
        from mythquant.config import config_manager
        
        # 创建数据源管理器
        data_manager = DataSourceManager(config_manager)
        print("   ✅ 数据源管理器创建成功")
        
        # 测试连通性
        connectivity = data_manager.test_data_source_connectivity()
        print("   📊 数据源连通性:")
        for source, status in connectivity.items():
            status_icon = "✅" if status else "❌"
            print(f"      {status_icon} {source}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 数据源架构测试失败: {e}")
        return False

def test_main_program():
    """测试主程序是否能正常启动"""
    print("\n🔍 测试主程序启动...")
    
    try:
        # 检查main.py是否存在
        main_file = Path("main.py")
        if not main_file.exists():
            print("   ❌ main.py文件不存在")
            return False
        
        print("   ✅ main.py文件存在")
        
        # 检查main.py中的配置导入
        with open(main_file, 'r', encoding='utf-8') as f:
            content = f.read()
        
        if 'config_compatibility' in content:
            print("   ✅ main.py已使用config_compatibility")
        elif 'user_config' in content:
            print("   ⚠️ main.py仍使用user_config")
        else:
            print("   ℹ️ main.py无明显配置导入")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 主程序测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 MythQuant 迁移状态测试")
    print("=" * 50)
    
    # 执行各项测试
    tests = [
        ("配置兼容性", test_config_compatibility),
        ("新配置系统", test_new_config_system),
        ("数据源架构", test_data_sources),
        ("主程序状态", test_main_program)
    ]
    
    results = {}
    for test_name, test_func in tests:
        try:
            results[test_name] = test_func()
        except Exception as e:
            print(f"\n❌ 测试 '{test_name}' 发生异常: {e}")
            results[test_name] = False
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed_tests = sum(1 for success in results.values() if success)
    total_tests = len(results)
    
    for test_name, success in results.items():
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {status} {test_name}")
    
    print(f"\n📈 测试统计: {passed_tests}/{total_tests} 通过 ({passed_tests/total_tests*100:.1f}%)")
    
    if passed_tests == total_tests:
        print("\n🎉 所有测试通过！迁移状态良好，可以继续下一阶段。")
        return True
    elif passed_tests >= total_tests * 0.75:
        print("\n✅ 大部分测试通过！可以继续迁移，但需要关注失败项。")
        return True
    else:
        print("\n⚠️ 多项测试失败！建议先修复问题再继续迁移。")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    if success:
        print("🚀 准备继续数据访问层迁移！")
    else:
        print("🔧 需要先修复测试问题！")
    exit(0 if success else 1)
