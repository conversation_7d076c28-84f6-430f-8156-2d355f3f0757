#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
新架构功能完整性测试脚本

在独立环境中测试新的src/mythquant架构的功能完整性
"""

import sys
import os
import subprocess
import tempfile
from pathlib import Path

def create_isolated_test_script():
    """创建隔离的测试脚本"""
    test_script_content = '''
import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent.parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

def test_basic_imports():
    """测试基本导入功能"""
    print("🔍 测试基本包导入...")
    
    try:
        import mythquant
        print(f"   ✅ mythquant 导入成功")
        print(f"   📦 版本: {getattr(mythquant, '__version__', 'Unknown')}")
        print(f"   👥 作者: {getattr(mythquant, '__author__', 'Unknown')}")
        return True
    except Exception as e:
        print(f"   ❌ mythquant 导入失败: {e}")
        return False

def test_config_system():
    """测试配置系统"""
    print("\\n🔍 测试配置系统...")
    
    try:
        # 测试配置模块导入
        from mythquant.config import ConfigManager, config_manager
        print(f"   ✅ 配置管理器导入成功")
        
        # 测试配置管理器实例化
        cm = ConfigManager()
        print(f"   ✅ 配置管理器实例化成功")
        
        # 测试基本配置访问
        debug_mode = cm.is_debug_enabled()
        print(f"   ✅ 调试模式配置: {debug_mode}")
        
        tdx_path = cm.get_tdx_path()
        print(f"   ✅ TDX路径配置: {tdx_path}")
        
        output_path = cm.get_output_path()
        print(f"   ✅ 输出路径配置: {output_path}")
        
        # 测试智能功能配置
        smart_selector = cm.is_smart_file_selector_enabled()
        print(f"   ✅ 智能文件选择器: {smart_selector}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置系统测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_config_validation():
    """测试配置验证"""
    print("\\n🔍 测试配置验证...")
    
    try:
        from mythquant.config import ConfigValidator, config_validator
        print(f"   ✅ 配置验证器导入成功")
        
        # 测试配置验证
        from mythquant.config import config_manager
        is_valid, errors, warnings = config_validator.validate_config(config_manager._config_cache)
        
        print(f"   ✅ 配置验证完成")
        print(f"   📊 验证结果: 有效={is_valid}")
        print(f"   📊 错误数量: {len(errors)}")
        print(f"   📊 警告数量: {len(warnings)}")
        
        if errors:
            print(f"   ⚠️ 发现错误: {errors[:3]}...")  # 只显示前3个错误
        
        if warnings:
            print(f"   ⚠️ 发现警告: {warnings[:3]}...")  # 只显示前3个警告
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置验证测试失败: {e}")
        return False

def test_core_modules():
    """测试核心模块"""
    print("\\n🔍 测试核心模块...")
    
    try:
        # 测试核心模块导入
        from mythquant.core import MythQuantApplication, StockDataProcessor, TaskManager
        print(f"   ✅ 核心模块导入成功")
        
        # 测试应用程序实例化
        from mythquant.config import ConfigManager
        config_manager = ConfigManager()
        
        app = MythQuantApplication(config_manager)
        print(f"   ✅ 应用程序实例化成功")
        
        # 测试应用程序方法
        if hasattr(app, 'display_system_overview'):
            print(f"   ✅ display_system_overview 方法存在")
        
        if hasattr(app, 'run_all_tasks'):
            print(f"   ✅ run_all_tasks 方法存在")
        
        # 测试组件初始化状态
        if hasattr(app, 'stock_processor') and app.stock_processor:
            print(f"   ✅ 股票处理器初始化成功")
        
        if hasattr(app, 'task_manager') and app.task_manager:
            print(f"   ✅ 任务管理器初始化成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 核心模块测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_user_settings():
    """测试用户设置"""
    print("\\n🔍 测试用户设置...")
    
    try:
        from mythquant.config.user_settings import (
            DEBUG, VERBOSE_MODE, SMART_FILE_SELECTOR, 
            TDX_CONFIG, OUTPUT_CONFIG, DATA_PROCESSING
        )
        
        print(f"   ✅ 用户设置导入成功")
        print(f"   📊 DEBUG: {DEBUG}")
        print(f"   📊 VERBOSE_MODE enabled: {VERBOSE_MODE.get('enabled', False)}")
        print(f"   📊 SMART_FILE_SELECTOR enabled: {SMART_FILE_SELECTOR.get('enabled', False)}")
        print(f"   📊 TDX_PATH: {TDX_CONFIG.get('tdx_path', 'Not set')}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 用户设置测试失败: {e}")
        return False

def test_backward_compatibility():
    """测试向后兼容性"""
    print("\\n🔍 测试向后兼容性...")
    
    try:
        # 测试是否能访问遗留配置
        from mythquant.config import config_manager
        
        # 测试遗留配置访问
        legacy_debug = config_manager.get_legacy_config('debug')
        print(f"   ✅ 遗留调试配置访问: {legacy_debug}")
        
        # 测试配置映射
        current_debug = config_manager.is_debug_enabled()
        print(f"   ✅ 当前调试配置访问: {current_debug}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 向后兼容性测试失败: {e}")
        return False

def main():
    """主测试函数"""
    print("🚀 MythQuant 新架构功能完整性测试")
    print("=" * 60)
    
    tests = [
        ("基本导入", test_basic_imports),
        ("配置系统", test_config_system),
        ("配置验证", test_config_validation),
        ("核心模块", test_core_modules),
        ("用户设置", test_user_settings),
        ("向后兼容性", test_backward_compatibility),
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\\n❌ 测试 '{test_name}' 发生异常: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\\n" + "=" * 60)
    print("📊 功能完整性测试结果:")
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {status} {test_name}")
        if success:
            passed += 1
    
    print(f"\\n📈 统计: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\\n🎉 所有功能测试通过！新架构功能完整。")
        return True
    else:
        print(f"\\n⚠️ {total-passed} 个功能测试失败，需要修复。")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
    
    return test_script_content

def run_isolated_test():
    """在隔离环境中运行测试"""
    print("🧪 创建隔离测试环境...")
    
    # 创建临时测试文件
    with tempfile.NamedTemporaryFile(mode='w', suffix='.py', delete=False, encoding='utf-8') as f:
        f.write(create_isolated_test_script())
        temp_test_file = f.name
    
    try:
        print(f"📝 临时测试文件: {temp_test_file}")
        print("🚀 运行隔离测试...")
        
        # 运行测试
        result = subprocess.run(
            [sys.executable, temp_test_file],
            cwd=os.getcwd(),
            capture_output=True,
            text=True,
            timeout=60
        )
        
        print("📤 测试输出:")
        print("-" * 50)
        print(result.stdout)
        
        if result.stderr:
            print("⚠️ 错误输出:")
            print(result.stderr)
        
        print("-" * 50)
        print(f"🏁 测试完成，退出码: {result.returncode}")
        
        return result.returncode == 0
        
    except subprocess.TimeoutExpired:
        print("⏰ 测试超时")
        return False
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        return False
    finally:
        # 清理临时文件
        try:
            os.unlink(temp_test_file)
        except:
            pass

def main():
    """主函数"""
    print("🎯 MythQuant 新架构功能完整性测试")
    print("=" * 60)
    print("📋 测试目标:")
    print("   • 验证新配置系统功能")
    print("   • 验证核心模块功能")
    print("   • 验证包导入机制")
    print("   • 验证向后兼容性")
    print("=" * 60)
    
    success = run_isolated_test()
    
    print("\n" + "=" * 60)
    if success:
        print("🎉 新架构功能完整性测试通过！")
        print("\n✅ 验证结果:")
        print("   • 配置系统工作正常")
        print("   • 核心模块功能完整")
        print("   • 包导入机制正确")
        print("   • 向后兼容性良好")
        print("\n🚀 新架构已准备就绪，可以安全使用！")
    else:
        print("⚠️ 新架构功能完整性测试失败！")
        print("\n🔧 需要修复的问题:")
        print("   • 检查导入路径")
        print("   • 验证模块依赖")
        print("   • 修复配置问题")
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
