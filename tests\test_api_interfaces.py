#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
API接口测试脚本

测试knowledge_base/api_interface_documentation.md中记录的所有API接口
验证接口的可用性、参数正确性和返回值格式

作者: AI Assistant
创建时间: 2025-08-01
"""

import sys
import os
from datetime import datetime
from typing import Dict, List, Any, Optional

# 添加项目根目录到路径
sys.path.append('.')

def test_core_apis():
    """测试核心服务层API"""
    print("🧪 测试核心服务层API")
    print("=" * 60)
    
    results = {}
    
    # 1. 测试配置管理器
    try:
        from core.config_manager import config_manager
        
        tdx_path = config_manager.get_tdx_path()
        verbose_enabled = config_manager.is_verbose_enabled()
        output_path = config_manager.get_base_output_path()
        
        print(f"✅ ConfigManager API测试通过")
        print(f"   TDX路径: {tdx_path}")
        print(f"   详细模式: {verbose_enabled}")
        print(f"   输出路径: {output_path}")
        
        results['config_manager'] = True
        
    except Exception as e:
        print(f"❌ ConfigManager API测试失败: {e}")
        results['config_manager'] = False
    
    # 2. 测试日志服务
    try:
        from core.logging_service import verbose_log, log_step, log_critical_info
        
        verbose_log('info', '测试日志消息', 'TEST')
        log_step('测试步骤消息')
        log_critical_info('测试关键信息')
        
        print(f"✅ LoggingService API测试通过")
        results['logging_service'] = True
        
    except Exception as e:
        print(f"❌ LoggingService API测试失败: {e}")
        results['logging_service'] = False
    
    return results


def test_cache_apis():
    """测试缓存管理层API"""
    print("\n🧪 测试缓存管理层API")
    print("=" * 60)
    
    results = {}
    
    # 1. 测试统一缓存管理器
    try:
        from cache.cache_manager import CacheManager
        
        cache_manager = CacheManager()
        
        # 测试基本缓存操作
        test_key = "test_key"
        test_value = "test_value"
        
        put_result = cache_manager.put(test_key, test_value)
        get_result = cache_manager.get(test_key)
        
        if put_result and get_result == test_value:
            print(f"✅ CacheManager API测试通过")
            print(f"   缓存写入: {put_result}")
            print(f"   缓存读取: {get_result}")
            results['cache_manager'] = True
        else:
            print(f"❌ CacheManager API功能测试失败")
            results['cache_manager'] = False
        
    except Exception as e:
        print(f"❌ CacheManager API测试失败: {e}")
        results['cache_manager'] = False
    
    return results


def test_utils_apis():
    """测试工具函数层API"""
    print("\n🧪 测试工具函数层API")
    print("=" * 60)
    
    results = {}
    
    # 1. 测试通用辅助函数
    try:
        from utils.helpers import (
            get_stock_market_info, clean_stock_code, 
            safe_filename, format_time_range
        )
        
        # 测试股票市场信息
        market, exchange = get_stock_market_info('000617')
        cleaned_code = clean_stock_code('617')
        safe_name = safe_filename('test<file>name.txt')
        time_range = format_time_range('20250101', '20250731')
        
        print(f"✅ Utils.helpers API测试通过")
        print(f"   股票市场: {market}, {exchange}")
        print(f"   代码清理: 617 -> {cleaned_code}")
        print(f"   安全文件名: {safe_name}")
        print(f"   时间范围: {time_range}")
        
        results['utils_helpers'] = True
        
    except Exception as e:
        print(f"❌ Utils.helpers API测试失败: {e}")
        results['utils_helpers'] = False
    
    # 2. 测试交易日计算器 ⭐
    try:
        from utils.trading_days_calculator import (
            count_trading_days_to_now, 
            calculate_data_count_needed,
            trading_days_calculator
        )
        
        # 测试主要的交易日天数计算函数
        start_date = '20250101'
        trading_days = count_trading_days_to_now(start_date)
        data_count = calculate_data_count_needed(start_date, '1min')
        
        # 测试类方法
        is_trading = trading_days_calculator.is_trading_day('20250801')
        recent_days = trading_days_calculator.get_recent_trading_days(5)
        
        print(f"✅ TradingDaysCalculator API测试通过")
        print(f"   从{start_date}到现在的交易日: {trading_days}天")
        print(f"   需要的1分钟数据: {data_count}条")
        print(f"   今天是否交易日: {is_trading}")
        print(f"   最近5个交易日: {recent_days[:2]}...")
        
        results['trading_days_calculator'] = True
        
    except Exception as e:
        print(f"❌ TradingDaysCalculator API测试失败: {e}")
        results['trading_days_calculator'] = False
    
    return results


def test_algorithms_apis():
    """测试算法计算层API"""
    print("\n🧪 测试算法计算层API")
    print("=" * 60)
    
    results = {}
    
    try:
        from algorithms.l2_metrics import L2MetricsCalculator
        from algorithms.buy_sell_calculator import BuySellCalculator
        
        # 创建测试数据
        import pandas as pd
        import numpy as np
        
        test_data = pd.DataFrame({
            'open': np.random.uniform(10, 12, 100),
            'high': np.random.uniform(11, 13, 100),
            'low': np.random.uniform(9, 11, 100),
            'close': np.random.uniform(10, 12, 100),
            'volume': np.random.randint(1000, 10000, 100)
        })
        
        # 测试L2指标计算器
        l2_calc = L2MetricsCalculator()
        path_length = l2_calc.calculate_path_length(test_data)
        
        # 测试主买主卖计算器
        bs_calc = BuySellCalculator()
        main_buy, main_sell = bs_calc.calculate_main_buy_sell(test_data)
        
        print(f"✅ Algorithms API测试通过")
        print(f"   路径总长计算: {len(path_length)}条记录")
        print(f"   主买主卖计算: {len(main_buy)}条记录")
        
        results['algorithms'] = True
        
    except Exception as e:
        print(f"❌ Algorithms API测试失败: {e}")
        results['algorithms'] = False
    
    return results


def test_file_io_apis():
    """测试文件IO层API"""
    print("\n🧪 测试文件IO层API")
    print("=" * 60)
    
    results = {}
    
    try:
        from file_io.excel_reader import load_target_stocks_from_excel
        from file_io.data_formatter import format_output_data
        
        # 测试数据格式化（不依赖实际文件）
        import pandas as pd
        
        test_df = pd.DataFrame({
            '股票代码': ['000617'],
            '日期': ['20250801'],
            'amount': [10000],
            'volume': [1000],
            '买卖差': [1.5]
        })
        
        formatted_df = format_output_data(test_df, 0)
        
        print(f"✅ FileIO API测试通过")
        print(f"   数据格式化: {len(formatted_df)}条记录")
        
        results['file_io'] = True
        
    except Exception as e:
        print(f"❌ FileIO API测试失败: {e}")
        results['file_io'] = False
    
    return results


def test_test_environment_apis():
    """测试测试环境API"""
    print("\n🧪 测试测试环境API")
    print("=" * 60)
    
    results = {}
    
    try:
        from test_environments.shared.utilities.specific_minute_data_fetcher import (
            get_specific_minute_data, get_price_at_time
        )
        
        # 测试特定分钟数据获取（可能返回None，这是正常的）
        test_datetime = '202507241447'
        data = get_specific_minute_data('000617', test_datetime)
        price = get_price_at_time('000617', test_datetime)
        
        print(f"✅ TestEnvironment API测试通过")
        print(f"   特定分钟数据: {'获取成功' if data else '无数据（正常，可能超出覆盖范围）'}")
        print(f"   特定时间价格: {'获取成功' if price else '无数据（正常，可能超出覆盖范围）'}")
        
        results['test_environment'] = True
        
    except Exception as e:
        print(f"❌ TestEnvironment API测试失败: {e}")
        results['test_environment'] = False
    
    return results


def generate_test_report(all_results: Dict[str, Dict[str, bool]]):
    """生成测试报告"""
    print("\n📊 API接口测试报告")
    print("=" * 80)
    
    total_tests = 0
    passed_tests = 0
    
    for category, results in all_results.items():
        print(f"\n📋 {category}:")
        for api_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {api_name}: {status}")
            total_tests += 1
            if result:
                passed_tests += 1
    
    pass_rate = passed_tests / total_tests if total_tests > 0 else 0
    
    print(f"\n📈 总体统计:")
    print(f"   总测试数: {total_tests}")
    print(f"   通过测试数: {passed_tests}")
    print(f"   失败测试数: {total_tests - passed_tests}")
    print(f"   通过率: {pass_rate:.1%}")
    
    # 重点说明交易日计算函数
    if all_results.get('工具函数层', {}).get('trading_days_calculator', False):
        print(f"\n⭐ 重点API - 基于给定日期返回交易日天数的函数:")
        print(f"   函数名: count_trading_days_to_now()")
        print(f"   模块: utils.trading_days_calculator")
        print(f"   用法: count_trading_days_to_now('20250101')")
        print(f"   功能: 计算从指定日期到现在的交易日数量")
    
    return pass_rate >= 0.8


def main():
    """主函数"""
    print("🚀 MythQuant API接口测试")
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    # 执行各层API测试
    all_results = {
        '核心服务层': test_core_apis(),
        '缓存管理层': test_cache_apis(),
        '工具函数层': test_utils_apis(),
        '算法计算层': test_algorithms_apis(),
        '文件IO层': test_file_io_apis(),
        '测试环境': test_test_environment_apis()
    }
    
    # 生成测试报告
    success = generate_test_report(all_results)
    
    if success:
        print(f"\n🎉 API接口测试完成！大部分接口工作正常")
        print(f"📖 详细接口文档: knowledge_base/api_interface_documentation.md")
    else:
        print(f"\n⚠️ API接口测试发现问题，请检查失败的接口")
    
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
