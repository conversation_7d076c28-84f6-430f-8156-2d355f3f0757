#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档质量监控工具

自动检查文档质量、生成质量报告和监控指标
"""

import os
import re
import yaml
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Tuple
from dataclasses import dataclass, asdict


@dataclass
class QualityMetrics:
    """质量指标"""
    total_files: int = 0
    files_with_metadata: int = 0
    files_with_proper_naming: int = 0
    broken_links: int = 0
    outdated_files: int = 0
    missing_descriptions: int = 0
    
    @property
    def metadata_coverage(self) -> float:
        return (self.files_with_metadata / self.total_files * 100) if self.total_files > 0 else 0
    
    @property
    def naming_compliance(self) -> float:
        return (self.files_with_proper_naming / self.total_files * 100) if self.total_files > 0 else 0
    
    @property
    def overall_quality_score(self) -> float:
        """计算总体质量分数"""
        if self.total_files == 0:
            return 0
        
        # 权重分配
        metadata_weight = 0.3
        naming_weight = 0.2
        links_weight = 0.2
        freshness_weight = 0.2
        description_weight = 0.1
        
        # 计算各项得分
        metadata_score = self.metadata_coverage
        naming_score = self.naming_compliance
        links_score = max(0, 100 - (self.broken_links / self.total_files * 100))
        freshness_score = max(0, 100 - (self.outdated_files / self.total_files * 100))
        description_score = max(0, 100 - (self.missing_descriptions / self.total_files * 100))
        
        # 加权平均
        total_score = (
            metadata_score * metadata_weight +
            naming_score * naming_weight +
            links_score * links_weight +
            freshness_score * freshness_weight +
            description_score * description_weight
        )
        
        return round(total_score, 1)


class DocumentationQualityMonitor:
    """文档质量监控器"""
    
    def __init__(self, docs_root: str = "docs"):
        self.docs_root = Path(docs_root)
        self.naming_pattern = re.compile(r'^[a-z0-9_]+\.md$')
        self.outdated_days = 90  # 90天未更新视为过期
    
    def check_quality(self) -> QualityMetrics:
        """检查文档质量"""
        metrics = QualityMetrics()
        
        for file_path in self._get_markdown_files():
            metrics.total_files += 1
            
            # 检查元数据
            if self._has_metadata(file_path):
                metrics.files_with_metadata += 1
            
            # 检查命名规范
            if self._check_naming_convention(file_path):
                metrics.files_with_proper_naming += 1
            
            # 检查是否过期
            if self._is_outdated(file_path):
                metrics.outdated_files += 1
            
            # 检查描述
            if not self._has_description(file_path):
                metrics.missing_descriptions += 1
        
        # 检查断开的链接
        metrics.broken_links = self._count_broken_links()
        
        return metrics
    
    def generate_quality_report(self) -> str:
        """生成质量报告"""
        metrics = self.check_quality()
        
        report = f"""---
title: "文档质量监控报告"
version: "v1.0"
date: "{datetime.now().strftime('%Y-%m-%d')}"
author: "文档质量监控系统"
status: "发布"
category: "管理"
tags: ["质量监控", "自动化报告", "指标分析"]
last_updated: "{datetime.now().strftime('%Y-%m-%d')}"
---

# 文档质量监控报告

## 生成信息

**生成时间**: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}  
**监控范围**: {self.docs_root}  
**检查文件数**: {metrics.total_files}  

## 📊 质量指标概览

### 总体质量评分
**{metrics.overall_quality_score}/100** {'🟢 优秀' if metrics.overall_quality_score >= 90 else '🟡 良好' if metrics.overall_quality_score >= 70 else '🔴 需改进'}

### 详细指标

| 指标 | 数值 | 百分比 | 状态 |
|------|------|--------|------|
| 元数据覆盖率 | {metrics.files_with_metadata}/{metrics.total_files} | {metrics.metadata_coverage:.1f}% | {'✅' if metrics.metadata_coverage >= 80 else '⚠️' if metrics.metadata_coverage >= 60 else '❌'} |
| 命名规范符合率 | {metrics.files_with_proper_naming}/{metrics.total_files} | {metrics.naming_compliance:.1f}% | {'✅' if metrics.naming_compliance >= 80 else '⚠️' if metrics.naming_compliance >= 60 else '❌'} |
| 断开链接数 | {metrics.broken_links} | - | {'✅' if metrics.broken_links == 0 else '⚠️' if metrics.broken_links <= 5 else '❌'} |
| 过期文件数 | {metrics.outdated_files} | {metrics.outdated_files/metrics.total_files*100:.1f}% | {'✅' if metrics.outdated_files == 0 else '⚠️' if metrics.outdated_files <= 3 else '❌'} |
| 缺少描述文件数 | {metrics.missing_descriptions} | {metrics.missing_descriptions/metrics.total_files*100:.1f}% | {'✅' if metrics.missing_descriptions == 0 else '⚠️' if metrics.missing_descriptions <= 2 else '❌'} |

## 📈 质量趋势

### 改进建议

"""
        
        # 添加具体建议
        if metrics.metadata_coverage < 80:
            report += "1. **提升元数据覆盖率**: 为缺少元数据的文档添加标准YAML前置元数据\n"
        
        if metrics.naming_compliance < 80:
            report += "2. **改进命名规范**: 将不符合规范的文件名改为小写+下划线格式\n"
        
        if metrics.broken_links > 0:
            report += f"3. **修复断开链接**: 发现 {metrics.broken_links} 个断开的链接需要修复\n"
        
        if metrics.outdated_files > 0:
            report += f"4. **更新过期文档**: {metrics.outdated_files} 个文档超过 {self.outdated_days} 天未更新\n"
        
        if metrics.missing_descriptions > 0:
            report += f"5. **补充文档描述**: {metrics.missing_descriptions} 个文档缺少描述信息\n"
        
        report += f"""
## 🎯 质量目标

### 短期目标 (1个月)
- 元数据覆盖率达到 90%
- 命名规范符合率达到 95%
- 断开链接数降至 0

### 长期目标 (3个月)
- 总体质量评分达到 95+
- 建立自动化质量检查流程
- 实现质量指标实时监控

## 📋 行动计划

### 立即行动
1. 运行文档标准化工具修复命名问题
2. 使用版本管理工具添加元数据
3. 运行链接检查工具修复断开链接

### 持续改进
1. 建立每周质量检查机制
2. 设置质量指标告警阈值
3. 培训团队成员遵循质量标准

---

**下次检查**: {(datetime.now().replace(day=1) + datetime.timedelta(days=7)).strftime('%Y-%m-%d')}  
**监控频率**: 每周自动检查  
**报告存档**: docs/reports/quality_reports/
"""
        
        return report
    
    def _get_markdown_files(self) -> List[Path]:
        """获取所有Markdown文件"""
        return list(self.docs_root.rglob('*.md'))
    
    def _has_metadata(self, file_path: Path) -> bool:
        """检查文件是否有元数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            return content.startswith('---')
        except:
            return False
    
    def _check_naming_convention(self, file_path: Path) -> bool:
        """检查命名规范"""
        filename = file_path.name
        
        # 特殊文件名例外
        special_files = {'README.md', 'CHANGELOG.md', 'LICENSE.md'}
        if filename in special_files:
            return True
        
        # 检查是否符合小写+下划线规范
        return self.naming_pattern.match(filename) is not None
    
    def _is_outdated(self, file_path: Path) -> bool:
        """检查文件是否过期"""
        try:
            # 检查文件修改时间
            mtime = datetime.fromtimestamp(file_path.stat().st_mtime)
            days_old = (datetime.now() - mtime).days
            return days_old > self.outdated_days
        except:
            return False
    
    def _has_description(self, file_path: Path) -> bool:
        """检查文件是否有描述"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有概述或描述部分
            description_patterns = [
                r'## 概述',
                r'## 描述',
                r'## Description',
                r'## Overview'
            ]
            
            for pattern in description_patterns:
                if re.search(pattern, content, re.IGNORECASE):
                    return True
            
            return False
        except:
            return False
    
    def _count_broken_links(self) -> int:
        """统计断开的链接数量"""
        broken_count = 0
        
        for file_path in self._get_markdown_files():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找Markdown链接
                link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
                matches = re.finditer(link_pattern, content)
                
                for match in matches:
                    link_url = match.group(2)
                    
                    # 跳过外部链接和锚点
                    if (link_url.startswith(('http://', 'https://', 'mailto:', '#'))):
                        continue
                    
                    # 检查相对路径链接
                    target_path = file_path.parent / link_url
                    if not target_path.exists():
                        broken_count += 1
            
            except:
                continue
        
        return broken_count
    
    def save_report(self, report: str) -> Path:
        """保存质量报告"""
        report_dir = self.docs_root / "reports" / "quality_reports"
        report_dir.mkdir(parents=True, exist_ok=True)
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = report_dir / f"quality_report_{timestamp}.md"
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(report)
        
        return report_file


def main():
    """主函数"""
    print("📊 MythQuant 文档质量监控工具")
    print("=" * 50)
    
    monitor = DocumentationQualityMonitor()
    
    print("🔍 正在检查文档质量...")
    metrics = monitor.check_quality()
    
    print(f"\n📊 质量检查结果:")
    print(f"   总文件数: {metrics.total_files}")
    print(f"   元数据覆盖率: {metrics.metadata_coverage:.1f}%")
    print(f"   命名规范符合率: {metrics.naming_compliance:.1f}%")
    print(f"   断开链接数: {metrics.broken_links}")
    print(f"   过期文件数: {metrics.outdated_files}")
    print(f"   总体质量评分: {metrics.overall_quality_score}/100")
    
    # 生成详细报告
    print(f"\n📄 正在生成详细报告...")
    report = monitor.generate_quality_report()
    report_file = monitor.save_report(report)
    
    print(f"✅ 质量报告已生成: {report_file}")
    
    # 质量评估
    if metrics.overall_quality_score >= 90:
        print("🟢 文档质量优秀！")
    elif metrics.overall_quality_score >= 70:
        print("🟡 文档质量良好，有改进空间")
    else:
        print("🔴 文档质量需要改进")


if __name__ == "__main__":
    main()
