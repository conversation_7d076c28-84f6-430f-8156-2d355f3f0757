#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据访问层迁移适配器

提供从旧数据访问方式到新数据源架构的无缝迁移支持
让现有代码可以逐步使用新的数据源管理器
"""

import sys
import os
import pandas as pd
from pathlib import Path
from typing import Optional, List, Dict, Any

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 导入新的数据源架构
try:
    from mythquant.data.sources import DataSourceManager, DataSourceType
    from mythquant.config import config_manager
    NEW_DATA_SOURCES_AVAILABLE = True
except ImportError as e:
    print(f"⚠️ 新数据源架构不可用: {e}")
    NEW_DATA_SOURCES_AVAILABLE = False
    DataSourceManager = None
    config_manager = None

# 导入配置兼容性模块
try:
    import config_compatibility as config
    CONFIG_AVAILABLE = True
except ImportError:
    CONFIG_AVAILABLE = False
    config = None

class DataAccessMigrationAdapter:
    """数据访问迁移适配器 - 提供新旧数据访问方式的桥接"""
    
    def __init__(self):
        """初始化适配器"""
        self.config = config if CONFIG_AVAILABLE else None
        self.data_manager = None
        
        # 初始化新数据源管理器
        if NEW_DATA_SOURCES_AVAILABLE and config_manager:
            try:
                self.data_manager = DataSourceManager(config_manager)
                print("✅ 新数据源管理器初始化成功")
            except Exception as e:
                print(f"⚠️ 新数据源管理器初始化失败: {e}")
                self.data_manager = None
        
        # 初始化旧数据访问方式的备用
        self._init_legacy_fallback()
    
    def _init_legacy_fallback(self):
        """初始化旧数据访问方式的备用"""
        try:
            # 导入旧的数据访问模块
            import func_Tdx
            import func_Util
            self.legacy_tdx = func_Tdx
            self.legacy_util = func_Util
            self.legacy_available = True
            print("✅ 旧数据访问模块可用作备用")
        except ImportError:
            self.legacy_available = False
            print("⚠️ 旧数据访问模块不可用")
    
    def read_stock_day_data(self, stock_code: str, use_new_source: bool = True) -> Optional[pd.DataFrame]:
        """
        读取股票日线数据（兼容新旧数据源）
        
        Args:
            stock_code: 股票代码
            use_new_source: 是否优先使用新数据源
            
        Returns:
            日线数据DataFrame或None
        """
        print(f"📊 读取{stock_code}日线数据...")
        
        # 优先尝试新数据源
        if use_new_source and self.data_manager:
            try:
                data = self.data_manager.get_stock_data(
                    stock_code=stock_code,
                    data_type="day",
                    source_priority=[DataSourceType.TDX, DataSourceType.PYTDX, DataSourceType.INTERNET]
                )
                if data is not None and not data.empty:
                    print(f"   ✅ 从新数据源获取成功: {len(data)} 条记录")
                    return data
                else:
                    print(f"   ⚠️ 新数据源返回空数据")
            except Exception as e:
                print(f"   ❌ 新数据源读取失败: {e}")
        
        # 回退到旧数据访问方式
        if self.legacy_available:
            try:
                print(f"   🔄 回退到旧数据访问方式...")
                # 这里需要调用旧的数据读取逻辑
                # 由于旧代码结构复杂，这里提供接口占位符
                print(f"   ℹ️ 旧数据访问方式需要具体实现")
                return None
            except Exception as e:
                print(f"   ❌ 旧数据访问方式也失败: {e}")
        
        print(f"   ❌ 所有数据源都无法获取{stock_code}的日线数据")
        return None
    
    def read_stock_minute_data(self, stock_code: str, frequency: int = 1, 
                              use_new_source: bool = True) -> Optional[pd.DataFrame]:
        """
        读取股票分钟数据（兼容新旧数据源）
        
        Args:
            stock_code: 股票代码
            frequency: 分钟频率
            use_new_source: 是否优先使用新数据源
            
        Returns:
            分钟数据DataFrame或None
        """
        print(f"📊 读取{stock_code} {frequency}分钟数据...")
        
        # 优先尝试新数据源
        if use_new_source and self.data_manager:
            try:
                data = self.data_manager.get_stock_data(
                    stock_code=stock_code,
                    data_type="minute",
                    source_priority=[DataSourceType.TDX, DataSourceType.PYTDX, DataSourceType.INTERNET]
                )
                if data is not None and not data.empty:
                    print(f"   ✅ 从新数据源获取成功: {len(data)} 条记录")
                    return data
                else:
                    print(f"   ⚠️ 新数据源返回空数据")
            except Exception as e:
                print(f"   ❌ 新数据源读取失败: {e}")
        
        # 回退到旧数据访问方式
        if self.legacy_available:
            try:
                print(f"   🔄 回退到旧数据访问方式...")
                # 旧数据访问方式的接口占位符
                print(f"   ℹ️ 旧数据访问方式需要具体实现")
                return None
            except Exception as e:
                print(f"   ❌ 旧数据访问方式也失败: {e}")
        
        print(f"   ❌ 所有数据源都无法获取{stock_code}的{frequency}分钟数据")
        return None
    
    def get_stock_list(self, use_new_source: bool = True) -> List[str]:
        """
        获取股票列表（兼容新旧数据源）
        
        Args:
            use_new_source: 是否优先使用新数据源
            
        Returns:
            股票代码列表
        """
        print("📋 获取股票列表...")
        
        # 优先尝试新数据源
        if use_new_source and self.data_manager:
            try:
                stock_list = self.data_manager.get_stock_list(
                    source_priority=[DataSourceType.TDX, DataSourceType.PYTDX, DataSourceType.INTERNET]
                )
                if stock_list:
                    print(f"   ✅ 从新数据源获取成功: {len(stock_list)} 只股票")
                    return stock_list
                else:
                    print(f"   ⚠️ 新数据源返回空列表")
            except Exception as e:
                print(f"   ❌ 新数据源获取失败: {e}")
        
        # 回退到旧数据访问方式
        if self.legacy_available:
            try:
                print(f"   🔄 回退到旧数据访问方式...")
                # 旧数据访问方式的接口占位符
                print(f"   ℹ️ 旧数据访问方式需要具体实现")
                return []
            except Exception as e:
                print(f"   ❌ 旧数据访问方式也失败: {e}")
        
        print(f"   ❌ 所有数据源都无法获取股票列表")
        return []
    
    def test_data_sources_connectivity(self) -> Dict[str, bool]:
        """
        测试各数据源连通性
        
        Returns:
            各数据源连通性状态
        """
        print("🔍 测试数据源连通性...")
        
        connectivity = {}
        
        if self.data_manager:
            try:
                connectivity = self.data_manager.test_data_source_connectivity()
                print("   ✅ 新数据源连通性测试完成")
                for source, status in connectivity.items():
                    status_icon = "✅" if status else "❌"
                    print(f"      {status_icon} {source}: {'可用' if status else '不可用'}")
            except Exception as e:
                print(f"   ❌ 新数据源连通性测试失败: {e}")
        
        # 测试旧数据访问方式
        if self.legacy_available:
            try:
                # 简单测试旧数据访问是否可用
                connectivity['legacy_tdx'] = True
                print("   ✅ 旧数据访问方式可用")
            except Exception as e:
                connectivity['legacy_tdx'] = False
                print(f"   ❌ 旧数据访问方式不可用: {e}")
        
        return connectivity
    
    def create_migration_example(self):
        """创建数据访问迁移示例"""
        print("📝 创建数据访问迁移示例...")
        
        example_code = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据访问迁移示例

演示如何从旧数据访问方式迁移到新数据源架构
"""

# ==================== 旧的数据访问方式 ====================

def old_way_read_data():
    """旧的数据读取方式"""
    import func_Tdx
    import func_Util
    
    # 旧方式：直接调用具体的数据读取函数
    stock_code = "000001"
    
    # 读取日线数据
    day_data = func_Tdx.read_day_data(stock_code)  # 假设的函数
    
    # 读取分钟数据
    minute_data = func_Tdx.read_minute_data(stock_code)  # 假设的函数
    
    return day_data, minute_data

# ==================== 新的数据访问方式 ====================

def new_way_read_data():
    """新的数据读取方式"""
    from mythquant.data.sources import DataSourceManager
    from mythquant.config import config_manager
    
    # 新方式：使用统一的数据源管理器
    data_manager = DataSourceManager(config_manager)
    stock_code = "000001"
    
    # 读取日线数据（自动多源回退）
    day_data = data_manager.get_stock_data(stock_code, "day")
    
    # 读取分钟数据（自动多源回退）
    minute_data = data_manager.get_stock_data(stock_code, "minute")
    
    return day_data, minute_data

# ==================== 兼容性迁移方式 ====================

def compatibility_way_read_data():
    """兼容性数据读取方式"""
    from data_access_migration_adapter import DataAccessMigrationAdapter
    
    # 兼容性方式：使用迁移适配器
    adapter = DataAccessMigrationAdapter()
    stock_code = "000001"
    
    # 读取日线数据（新数据源优先，自动回退）
    day_data = adapter.read_stock_day_data(stock_code)
    
    # 读取分钟数据（新数据源优先，自动回退）
    minute_data = adapter.read_stock_minute_data(stock_code)
    
    return day_data, minute_data

# ==================== 迁移步骤演示 ====================

if __name__ == "__main__":
    print("🔄 数据访问迁移步骤演示")
    print("=" * 50)
    
    print("\\n步骤1: 保持现有代码不变，使用适配器")
    print("# 在现有代码中添加适配器")
    print("adapter = DataAccessMigrationAdapter()")
    print("data = adapter.read_stock_day_data('000001')")
    
    print("\\n步骤2: 逐步替换为新数据源API")
    print("# 逐步采用新的数据源管理器")
    print("data_manager = DataSourceManager(config_manager)")
    print("data = data_manager.get_stock_data('000001', 'day')")
    
    print("\\n步骤3: 完全迁移到新架构")
    print("# 移除旧数据访问代码，完全使用新架构")
'''
        
        with open("data_access_migration_example.py", "w", encoding="utf-8") as f:
            f.write(example_code)
        
        print("   ✅ 迁移示例已创建: data_access_migration_example.py")
    
    def generate_migration_plan(self):
        """生成数据访问层迁移计划"""
        print("📋 生成数据访问层迁移计划...")
        
        plan = f"""# 数据访问层迁移计划

## 迁移目标
将现有的分散数据读取逻辑迁移到统一的数据源架构，实现：
- 多数据源支持（TDX、PyTDX、互联网）
- 自动回退机制
- 统一的数据访问接口
- 更好的错误处理和日志记录

## 迁移策略
采用适配器模式，提供新旧数据访问方式的桥接：

### 阶段1：适配器集成
- ✅ 创建数据访问迁移适配器
- 🔄 在关键模块中集成适配器
- 🧪 测试适配器功能正常

### 阶段2：逐步替换
- 📋 识别所有数据读取调用点
- 🔄 逐个替换为适配器调用
- 🧪 验证每个替换的正确性

### 阶段3：完全迁移
- 🚀 直接使用新数据源管理器
- 🧹 清理旧数据访问代码
- 📊 性能和功能验证

## 当前状态
- 新数据源架构: {'✅ 可用' if NEW_DATA_SOURCES_AVAILABLE else '❌ 不可用'}
- 配置系统: {'✅ 可用' if CONFIG_AVAILABLE else '❌ 不可用'}
- 旧数据访问: {'✅ 可用' if hasattr(self, 'legacy_available') and self.legacy_available else '❌ 不可用'}

## 风险评估
- 风险等级: 🟡 中等风险
- 主要风险: 数据格式不一致、性能差异
- 缓解措施: 充分测试、渐进式迁移、保留回退机制

## 成功标准
- [ ] 所有数据读取功能正常
- [ ] 数据格式和内容一致
- [ ] 性能无明显下降
- [ ] 支持多数据源回退
- [ ] 错误处理完善

## 下一步行动
1. 🧪 测试适配器基本功能
2. 📋 识别需要迁移的数据读取代码
3. 🔄 开始第一个模块的迁移
4. 🧪 验证迁移结果
"""
        
        with open("data_access_migration_plan.md", "w", encoding="utf-8") as f:
            f.write(plan)
        
        print("   ✅ 迁移计划已生成: data_access_migration_plan.md")

def main():
    """主函数"""
    print("🚀 数据访问层迁移适配器")
    print("=" * 60)
    
    # 创建迁移适配器
    adapter = DataAccessMigrationAdapter()
    
    # 测试数据源连通性
    connectivity = adapter.test_data_sources_connectivity()
    
    # 创建迁移示例和计划
    adapter.create_migration_example()
    adapter.generate_migration_plan()
    
    # 总结
    print("\n" + "=" * 60)
    available_sources = sum(1 for status in connectivity.values() if status)
    total_sources = len(connectivity)
    
    if available_sources > 0:
        print("🎉 数据访问层迁移适配器准备就绪！")
        print(f"\n📊 数据源状态: {available_sources}/{total_sources} 可用")
        print(f"\n🚀 下一步:")
        print("1. 测试适配器基本功能")
        print("2. 在关键模块中集成适配器")
        print("3. 逐步替换数据读取调用")
        return True
    else:
        print("⚠️ 数据访问层迁移需要先解决数据源问题！")
        print(f"\n🔧 需要检查:")
        print("• 新数据源架构的可用性")
        print("• 配置系统的正确性")
        print("• 旧数据访问模块的状态")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
