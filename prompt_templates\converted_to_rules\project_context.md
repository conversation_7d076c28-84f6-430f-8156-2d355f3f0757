# MythQuant 项目上下文文档

## 🎯 当前项目状态（2025-07-16更新）

### 模块化拆分进展
- ✅ **第一阶段**: 基础服务模块 (utils/, core/) - 已完成
- ✅ **第二阶段**: 文件IO模块 (file_io/) - 已完成，100%测试通过
- ✅ **第三阶段**: UI显示模块 (ui/) - 已完成，6/6测试通过
- ⏳ **第四阶段**: 算法计算模块 - **下一步目标**
- ⏳ **第五阶段**: 缓存管理模块 - 计划中
- ⏳ **第六阶段**: 前复权算法模块 - 高风险，最后进行

### 核心文件状态
- `main_v20230219_optimized.py`: 4228行 (原4833行，已减少12.5%)
- `user_config.py`: 146行 (配置文件)
- 已创建8个模块化文件，共约1000行高质量代码

## 🔍 AI工作指导原则

### 文件修改优先级判断
1. **用户提到的关键词 → 对应文件**:
   - "显示", "打印", "输出界面" → `ui/display.py`
   - "进度", "跟踪" → `ui/progress.py`
   - "写入", "保存", "文件输出" → `file_io/file_writer.py`
   - "读取", "加载", "Excel" → `file_io/excel_reader.py`
   - "格式化", "数据处理" → `file_io/data_formatter.py`
   - "配置", "设置" → `user_config.py`
   - "日志", "记录" → `core/logging_service.py`
   - "算法", "计算", "L2指标" → `main_v20230219_optimized.py` (待拆分)

2. **函数名称模式识别**:
   - `display_*` → `ui/display.py`
   - `write_*_file` → `file_io/file_writer.py`
   - `calculate_*` → 主文件算法部分
   - `_preload_*`, `_ensure_*` → 主文件缓存部分

### 模块化拆分策略
**下一步推荐**: 第四阶段算法计算模块
- **目标**: `algorithms/l2_metrics.py`
- **位置**: `main_v20230219_optimized.py` 第2120-2515行
- **包含**: `calculate_l2_metrics()`, `_calculate_main_buy_sell()`, `resample_to_timeframes()`
- **预期减少**: 400-500行

## 🎯 用户交互优化建议

### 推荐的@引用方式
1. **明确指定文件**: `@ui/display.py 修改启动信息显示`
2. **指定功能模块**: `@file_io 修改文件输出格式`
3. **指定阶段**: `@第四阶段 开始算法模块拆分`

### 有效的描述方式
- ✅ "修改进度显示功能" → 自动定位到 `ui/progress.py`
- ✅ "优化L2指标计算" → 自动定位到 主文件算法部分
- ✅ "调整配置参数" → 自动定位到 `user_config.py`
- ❌ "改进代码" → 太模糊，需要进一步确认

### 错误引用处理
如果用户@引用的不是目标文件：
1. **自动识别正确文件**并说明原因
2. **询问确认**是否使用识别的文件
3. **提供选择**让用户选择目标文件

例如：
```
用户：@main.py 修改进度显示
AI回应：检测到您想修改进度显示功能，根据项目结构，建议修改 `ui/progress.py` 而不是主文件。是否确认？
```

## 📝 最佳实践模式

### 代码修改工作流
1. **分析需求** → 确定目标文件
2. **检查依赖** → 确认是否影响其他模块
3. **备份验证** → 重要修改前建议测试
4. **模块化优先** → 优先修改已拆分的模块，避免主文件膨胀

### 新功能开发
- 优先考虑添加到现有模块
- 如需新模块，建议延续现有模块化模式
- 保持向后兼容性

### 测试验证
- 修改后建议运行相关测试
- 重要功能修改建议创建测试脚本

## 🚀 下一阶段准备

### 第四阶段：算法计算模块
**准备工作**:
1. 分析L2指标计算函数依赖关系
2. 识别可提取的纯算法函数
3. 设计算法模块接口
4. 规划测试用例

**风险评估**:
- 中等风险（涉及核心业务逻辑）
- 需要careful测试确保算法一致性
- 可能需要性能基准测试

---
*此文档应在每个阶段完成后更新* 