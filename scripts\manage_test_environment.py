#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境管理命令行工具

提供体系化的测试环境管理功能，包括：
1. 测试目录分析
2. 测试数据备份和恢复
3. 测试环境清理
4. 测试环境报告生成

使用方法:
    python scripts/manage_test_environment.py --analyze
    python scripts/manage_test_environment.py --backup --files file1.txt file2.txt
    python scripts/manage_test_environment.py --restore --backup-name backup_20250729_101530
    python scripts/manage_test_environment.py --list-backups
    python scripts/manage_test_environment.py --cleanup
    python scripts/manage_test_environment.py --report

作者: AI Assistant
创建时间: 2025-07-29
"""

import os
import sys
import argparse
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.test_environment_manager import TestEnvironmentManager


def analyze_test_environment(manager: TestEnvironmentManager):
    """分析测试环境"""
    print("🔍 测试环境分析")
    print("=" * 80)
    
    analysis = manager.analyze_test_directory_usage()
    
    if not analysis:
        print("❌ 分析失败")
        return
    
    # 主测试目录分析
    main_dir = analysis.get('main_test_dir', {})
    print(f"\n📁 主测试目录: {main_dir.get('path', 'N/A')}")
    print(f"   存在状态: {'✅ 存在' if main_dir.get('exists') else '❌ 不存在'}")
    print(f"   总文件数: {main_dir.get('total_files', 0):,}")
    print(f"   总大小: {main_dir.get('total_size_mb', 0):.2f} MB")
    
    # 子目录详情
    subdirs = main_dir.get('subdirs', {})
    if subdirs:
        print(f"\n📂 子目录详情:")
        for subdir_name, subdir_info in subdirs.items():
            status = "✅" if subdir_info.get('exists') else "❌"
            print(f"   {status} {subdir_name}: {subdir_info.get('file_count', 0)} 文件, "
                  f"{subdir_info.get('size_mb', 0):.2f} MB")
    
    # 项目测试目录分析
    project_dirs = analysis.get('project_test_dirs', {})
    if project_dirs:
        print(f"\n📁 项目测试目录:")
        for dir_name, dir_info in project_dirs.items():
            if dir_info.get('exists'):
                print(f"   ✅ {dir_name}: {dir_info.get('file_count', 0)} 文件, "
                      f"{dir_info.get('size_mb', 0):.2f} MB")
                
                # 显示文件类型分布
                file_types = dir_info.get('file_types', {})
                if file_types:
                    type_info = ", ".join([f"{ext}({count})" for ext, count in file_types.items()])
                    print(f"      文件类型: {type_info}")
            else:
                print(f"   ❌ {dir_name}: 不存在")
    
    # 建议
    recommendations = analysis.get('recommendations', [])
    if recommendations:
        print(f"\n💡 建议:")
        for i, rec in enumerate(recommendations, 1):
            print(f"   {i}. {rec}")


def backup_test_data(manager: TestEnvironmentManager, files: list, backup_name: str = None, description: str = ""):
    """备份测试数据"""
    print("💾 创建测试数据备份")
    print("=" * 80)
    
    if not files:
        print("❌ 未指定要备份的文件")
        return False
    
    # 验证文件存在
    existing_files = []
    for file_path in files:
        if os.path.exists(file_path):
            existing_files.append(file_path)
            print(f"✅ 找到文件: {file_path}")
        else:
            print(f"❌ 文件不存在: {file_path}")
    
    if not existing_files:
        print("❌ 没有有效的文件可以备份")
        return False
    
    success = manager.create_test_data_backup(existing_files, backup_name, description)
    
    if success:
        print(f"\n✅ 备份创建成功")
    else:
        print(f"\n❌ 备份创建失败")
    
    return success


def restore_test_data(manager: TestEnvironmentManager, backup_name: str, target_dir: str = None):
    """恢复测试数据"""
    print("📥 恢复测试数据备份")
    print("=" * 80)
    
    if not backup_name:
        print("❌ 未指定备份名称")
        return False
    
    success = manager.restore_test_data_backup(backup_name, target_dir)
    
    if success:
        print(f"\n✅ 数据恢复成功")
    else:
        print(f"\n❌ 数据恢复失败")
    
    return success


def list_backups(manager: TestEnvironmentManager):
    """列出所有备份"""
    print("📋 测试数据备份列表")
    print("=" * 80)
    
    backups = manager.list_test_backups()
    
    if not backups:
        print("📭 没有找到任何备份")
        return
    
    print(f"找到 {len(backups)} 个备份:\n")
    
    for i, backup in enumerate(backups, 1):
        print(f"{i}. 📦 {backup.get('backup_name', 'N/A')}")
        print(f"   📅 创建时间: {backup.get('created_time', 'N/A')}")
        print(f"   📝 描述: {backup.get('description', '无描述')}")
        print(f"   📊 文件数量: {backup.get('file_count', 0)}")
        print(f"   💾 大小: {backup.get('total_size_mb', 0):.2f} MB")
        print()


def cleanup_test_environment(manager: TestEnvironmentManager):
    """清理测试环境"""
    print("🧹 清理测试环境")
    print("=" * 80)
    
    # 清理空目录
    empty_dirs = []
    for test_type, test_dir in manager.project_test_dirs.items():
        if os.path.exists(test_dir):
            if not os.listdir(test_dir):
                empty_dirs.append((test_type, test_dir))
    
    if empty_dirs:
        print(f"📁 发现 {len(empty_dirs)} 个空目录:")
        for test_type, test_dir in empty_dirs:
            print(f"   - {test_type}: {test_dir}")
    else:
        print("✅ 没有发现空目录")
    
    # 清理旧备份
    backups = manager.list_test_backups()
    max_backups = manager.backup_config['max_backups_per_test']
    
    if len(backups) > max_backups:
        print(f"\n🗑️ 备份数量({len(backups)})超过限制({max_backups})，将清理旧备份")
        manager._cleanup_old_backups()
    else:
        print(f"\n✅ 备份数量({len(backups)})在限制范围内")


def generate_report(manager: TestEnvironmentManager):
    """生成测试环境报告"""
    print("📊 生成测试环境报告")
    print("=" * 80)
    
    # 分析测试环境
    analysis = manager.analyze_test_directory_usage()
    backups = manager.list_test_backups()
    
    # 生成报告
    report = {
        'report_time': datetime.now().isoformat(),
        'test_environment_analysis': analysis,
        'backup_summary': {
            'total_backups': len(backups),
            'backups': backups
        },
        'summary': {
            'main_test_dir_exists': analysis.get('main_test_dir', {}).get('exists', False),
            'total_test_files': analysis.get('main_test_dir', {}).get('total_files', 0),
            'total_backup_files': sum(b.get('file_count', 0) for b in backups),
            'recommendations_count': len(analysis.get('recommendations', []))
        }
    }
    
    # 保存报告
    report_file = f"test_environment_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
    with open(report_file, 'w', encoding='utf-8') as f:
        json.dump(report, f, indent=2, ensure_ascii=False)
    
    print(f"📄 报告已保存: {report_file}")
    
    # 显示摘要
    summary = report['summary']
    print(f"\n📋 测试环境摘要:")
    print(f"   主测试目录: {'✅ 存在' if summary['main_test_dir_exists'] else '❌ 不存在'}")
    print(f"   测试文件数: {summary['total_test_files']:,}")
    print(f"   备份文件数: {summary['total_backup_files']:,}")
    print(f"   建议数量: {summary['recommendations_count']}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试环境管理工具')
    
    # 操作选项
    parser.add_argument('--analyze', '-a', action='store_true', help='分析测试环境')
    parser.add_argument('--backup', '-b', action='store_true', help='创建备份')
    parser.add_argument('--restore', '-r', action='store_true', help='恢复备份')
    parser.add_argument('--list-backups', '-l', action='store_true', help='列出备份')
    parser.add_argument('--cleanup', '-c', action='store_true', help='清理环境')
    parser.add_argument('--report', action='store_true', help='生成报告')
    
    # 参数选项
    parser.add_argument('--files', nargs='+', help='要备份的文件列表')
    parser.add_argument('--backup-name', help='备份名称')
    parser.add_argument('--description', default='', help='备份描述')
    parser.add_argument('--target-dir', help='恢复目标目录')
    
    args = parser.parse_args()
    
    # 创建管理器
    manager = TestEnvironmentManager()
    
    print("🧪 测试环境管理工具")
    print("=" * 80)
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    # 执行操作
    if args.analyze:
        analyze_test_environment(manager)
    elif args.backup:
        backup_test_data(manager, args.files or [], args.backup_name, args.description)
    elif args.restore:
        restore_test_data(manager, args.backup_name, args.target_dir)
    elif args.list_backups:
        list_backups(manager)
    elif args.cleanup:
        cleanup_test_environment(manager)
    elif args.report:
        generate_report(manager)
    else:
        print("❌ 请指定操作选项，使用 --help 查看帮助")
        parser.print_help()


if __name__ == '__main__':
    main()
