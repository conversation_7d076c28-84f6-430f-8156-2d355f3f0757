#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
创建MythQuant项目的src目录结构
"""

import os

def create_directory_structure():
    """创建完整的src目录结构"""
    
    # 定义目录结构
    directories = [
        "src/mythquant",
        "src/mythquant/config",
        "src/mythquant/core", 
        "src/mythquant/data",
        "src/mythquant/data/sources",
        "src/mythquant/data/processors",
        "src/mythquant/data/downloaders",
        "src/mythquant/io",
        "src/mythquant/io/readers",
        "src/mythquant/io/writers",
        "src/mythquant/algorithms",
        "src/mythquant/utils",
        "src/mythquant/ui",
        "src/mythquant/cache",
        # 测试目录
        "tests",
        "tests/unit",
        "tests/integration", 
        "tests/fixtures",
        # 文档目录
        "docs/api",
        "docs/guides",
        "docs/architecture",
        "docs/tutorials",
        # 归档目录
        "archive",
        "archive/legacy",
        "archive/legacy/old_versions",
        "archive/legacy/deprecated",
        "archive/legacy/experiments",
        "archive/backup",
        "archive/backup/config_backup",
        "archive/backup/data_backup",
        "archive/backup/code_backup",
        "archive/tests",
        "archive/tests/legacy_tests",
        "archive/tools",
        "archive/tools/legacy_tools",
        # 工具目录
        "tools",
        "tools/development",
        "tools/deployment", 
        "tools/maintenance",
        # 环境配置目录
        "environments",
        "environments/development",
        "environments/testing",
        "environments/production",
        "environments/ci_cd"
    ]
    
    print("🏗️ 创建MythQuant项目目录结构...")
    
    created_count = 0
    skipped_count = 0
    
    for directory in directories:
        try:
            if not os.path.exists(directory):
                os.makedirs(directory, exist_ok=True)
                print(f"✅ 创建目录: {directory}")
                created_count += 1
            else:
                print(f"⏭️ 目录已存在: {directory}")
                skipped_count += 1
        except Exception as e:
            print(f"❌ 创建目录失败 {directory}: {e}")
    
    print(f"\n📊 目录创建统计:")
    print(f"   新创建: {created_count} 个目录")
    print(f"   已存在: {skipped_count} 个目录")
    print(f"   总计: {len(directories)} 个目录")
    
    # 验证关键目录
    key_directories = [
        "src/mythquant",
        "src/mythquant/config",
        "src/mythquant/core",
        "src/mythquant/data",
        "tests",
        "docs",
        "archive"
    ]
    
    print(f"\n🔍 验证关键目录:")
    all_exist = True
    for directory in key_directories:
        if os.path.exists(directory):
            print(f"✅ {directory}")
        else:
            print(f"❌ {directory}")
            all_exist = False
    
    if all_exist:
        print(f"\n🎉 目录结构创建成功！")
        return True
    else:
        print(f"\n❌ 部分目录创建失败")
        return False

if __name__ == "__main__":
    success = create_directory_structure()
    exit(0 if success else 1)
