2025-07-28 01:40:54,578 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_014054.log
2025-07-28 01:40:54,578 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-28 01:40:54,605 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-28 01:40:54,607 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-28 01:40:54,607 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-28 01:40:54,607 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-28 01:40:54,608 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-28 01:40:54,855 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.163s
2025-07-28 01:40:54,881 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_180.153.18.170 (180.153.18.170:7709) - 0.179s
2025-07-28 01:40:54,886 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.12.136.250 (60.12.136.250:7709) - 0.181s
2025-07-28 01:40:54,893 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.56.198 (115.238.56.198:7709) - 0.188s
2025-07-28 01:40:54,921 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.201s
2025-07-28 01:40:54,949 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************ (************:7709) - 0.225s
2025-07-28 01:40:54,955 - Main - INFO - info:307 - ℹ️ 配置文件已备份到: user_config.py.backup
2025-07-28 01:40:54,956 - Main - INFO - info:307 - ℹ️ 配置文件更新成功: user_config.py
2025-07-28 01:40:54,956 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已更新为: **************
2025-07-28 01:40:54,956 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器端口已更新为: 7709
2025-07-28 01:40:54,956 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-28 01:40:54,956 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: **************:7709
2025-07-28 01:40:54,956 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-28 01:40:54,957 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-28 01:40:54,957 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-28 01:40:54,957 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-28 01:40:55,454 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-28 01:40:55,454 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-28 01:40:55,462 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-28 01:40:55,462 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-28 01:40:55,463 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-28 01:40:55,463 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-28 01:40:55,463 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-28 01:40:55,463 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-28 01:40:55,464 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-28 01:40:55,470 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-28 01:40:55,470 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-28 01:40:56,014 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-28 01:40:56,014 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 01:40:56,014 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 01:40:56,014 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 01:40:56,014 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 01:40:56,015 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 01:40:56,015 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: TDX日线级别数据生成
2025-07-28 01:40:56,016 - Main - INFO - info:307 - ℹ️ 📊 发现 4 个现有数据文件，开始稽核
2025-07-28 01:40:56,329 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 3 个文件质量不达标
2025-07-28 01:40:56,329 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-28 01:40:56,329 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-28 01:40:56,329 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-28 01:40:56,330 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-28 01:40:56,330 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-28 01:40:56,498 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-28 01:40:56,498 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-28 01:40:56,505 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-28 01:40:56,505 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-28 01:40:56,505 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-28 01:40:56,505 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-28 01:40:56,505 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-28 01:40:56,505 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-28 01:40:56,506 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-28 01:40:56,510 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-28 01:40:56,511 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-28 01:41:35,685 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-28 01:41:42,125 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-28 01:41:42,299 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-28 01:41:42,838 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-28 01:41:43,021 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-28 01:41:43,021 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-28 01:41:43,085 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-28 01:41:43,086 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-28 01:41:43,086 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-28 01:41:43,087 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-28 01:41:43,087 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-28 01:41:43,087 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-28 01:41:43,087 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-28 01:41:43,102 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 46.77 秒
2025-07-28 01:41:43,102 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-28 01:41:43,102 - Main - INFO - info:307 - ℹ️ 🔍 执行任务后数据质量稽核: TDX日线级别数据生成
2025-07-28 01:41:43,103 - Main - INFO - info:307 - ℹ️ 📊 发现 1 个新生成文件，开始稽核
2025-07-28 01:41:43,148 - Main - INFO - info:307 - ℹ️ ✅ 执行后稽核通过: 0 优秀, 1 良好
2025-07-28 01:41:43,149 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: 互联网分钟级数据下载
2025-07-28 01:41:43,149 - Main - INFO - info:307 - ℹ️ 📊 发现 5 个现有数据文件，开始稽核
2025-07-28 01:41:43,501 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 4 个文件质量不达标
2025-07-28 01:41:43,501 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 01:41:43,501 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-28 01:41:43,502 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-28 01:41:43,503 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-28 01:41:43,909 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-28 01:41:43,926 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-28 01:41:43,926 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-28 01:41:43,926 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-28 01:41:43,926 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-28 01:41:43,926 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-28 01:41:43,926 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-28 01:41:43,926 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-28 01:41:43,927 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-28 01:41:43,930 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-28 01:41:43,930 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-28 01:41:43,930 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-28 01:41:43,931 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-28 01:41:43,931 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-28 01:41:43,953 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-07-28 01:41:43,954 - Main - INFO - info:307 - ℹ️ 🚀 开始处理缺失数据: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-28 01:41:43,957 - core.logging_service - ERROR - log_error:133 - 获取API验证数据失败: 'PytdxDownloader' object has no attribute 'download_stock_data'
2025-07-28 01:41:43,957 - Main - WARNING - warning:311 - ⚠️ ❌ 前置验证失败: 无法获取API对比数据
2025-07-28 01:41:43,957 - Main - WARNING - warning:311 - ⚠️ 💡 建议：需要全量重新下载数据以确保一致性
2025-07-28 01:41:43,960 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 01:41:43,960 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 01:41:43,960 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-28 01:41:43,960 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:44,135 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:44,136 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4080条 (目标日期: 20250704, 交易日: 17天, 频率: 1min)
2025-07-28 01:41:44,136 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4080条 -> 实际请求800条 (配置限制:800)
2025-07-28 01:41:44,136 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 01:41:44,136 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 01:41:44,194 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3280条
2025-07-28 01:41:44,195 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:44,375 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:44,428 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 01:41:44,428 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:44,617 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:44,670 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 01:41:44,670 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:44,855 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:44,907 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 01:41:44,908 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:45,080 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:45,135 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 01:41:45,135 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:45,305 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:45,350 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +80条，总计4080条
2025-07-28 01:41:45,350 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4080条数据
2025-07-28 01:41:45,350 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要4080条，实际获取4080条
2025-07-28 01:41:45,350 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: None ~ None
2025-07-28 01:41:45,351 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250704
2025-07-28 01:41:45,362 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 01:41:45,365 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 01:41:45,365 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 01:41:45,367 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-28 01:41:45,367 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 01:41:45,368 - Main - INFO - info:307 - ℹ️   日期: 202507040931
2025-07-28 01:41:45,368 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-28 01:41:45,368 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-28 01:41:45,368 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 01:41:45,368 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 01:41:45,368 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 01:41:45,372 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-28 01:41:45,375 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-28 01:41:45,375 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250303 - 20250704
2025-07-28 01:41:45,376 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250302
2025-07-28 01:41:45,376 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-07-28 01:41:45,388 - Main - INFO - info:307 - ℹ️ 读取现有数据: 20381 条记录
2025-07-28 01:41:45,388 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250302
2025-07-28 01:41:45,388 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 01:41:45,388 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 01:41:45,388 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250302
2025-07-28 01:41:45,389 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:45,565 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:45,568 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32640条 (目标日期: 20250101, 交易日: 136天, 频率: 1min)
2025-07-28 01:41:45,568 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32640条 -> 实际请求800条 (配置限制:800)
2025-07-28 01:41:45,568 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 01:41:45,568 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 01:41:45,620 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需31840条
2025-07-28 01:41:45,620 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:45,790 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:45,841 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 01:41:45,841 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:46,022 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:46,075 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 01:41:46,076 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:46,251 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:46,303 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 01:41:46,303 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:46,477 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:46,529 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 01:41:46,529 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:46,701 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:46,752 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-28 01:41:46,752 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:46,924 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:46,976 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-28 01:41:46,976 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:47,146 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:47,196 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-28 01:41:47,197 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:47,376 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:47,428 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-28 01:41:47,429 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:47,603 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:47,653 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-28 01:41:47,654 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:47,827 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:47,877 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-28 01:41:47,877 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:48,058 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:48,115 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-28 01:41:48,115 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:48,295 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:48,348 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-28 01:41:48,348 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:48,529 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:48,582 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-28 01:41:48,582 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:48,751 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:48,801 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-28 01:41:48,802 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:48,972 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:49,022 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-28 01:41:49,022 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:49,188 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:49,239 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-28 01:41:49,239 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:49,411 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:49,462 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-28 01:41:49,462 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:49,634 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:49,684 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-28 01:41:49,685 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:49,857 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:49,907 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-28 01:41:49,907 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:50,086 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:50,139 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-28 01:41:50,139 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:50,317 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:50,370 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-28 01:41:50,370 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:50,550 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:50,602 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-28 01:41:50,602 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:50,775 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:50,825 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-28 01:41:50,825 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:51,001 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:51,056 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-28 01:41:51,056 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:51,228 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:51,279 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-28 01:41:51,279 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:51,447 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:51,497 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-28 01:41:51,497 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:51,665 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:51,716 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-28 01:41:51,716 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:51,951 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:52,002 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-28 01:41:52,002 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:52,185 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:52,238 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-28 01:41:52,238 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:52,409 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:52,453 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-28 01:41:52,454 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32640条，实际获取24000条
2025-07-28 01:41:52,455 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约8640条数据（约36个交易日）
2025-07-28 01:41:52,455 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: None ~ None
2025-07-28 01:41:52,455 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-28 01:41:52,455 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-28 01:41:52,513 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250101 - 20250302
2025-07-28 01:41:52,524 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-28 01:41:52,555 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-28 01:41:52,564 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-07-28 01:41:52,578 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 01:41:52,588 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 01:41:52,597 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-07-28 01:41:52,598 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:52,786 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:52,787 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 3840条 (目标日期: 20250705, 交易日: 16天, 频率: 1min)
2025-07-28 01:41:52,787 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计3840条 -> 实际请求800条 (配置限制:800)
2025-07-28 01:41:52,788 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 01:41:52,788 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 01:41:52,840 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3040条
2025-07-28 01:41:52,841 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:53,011 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:53,063 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 01:41:53,063 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:53,234 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:53,287 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 01:41:53,287 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:53,464 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:53,523 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 01:41:53,523 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-28 01:41:53,701 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-28 01:41:53,752 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +640条，总计3840条
2025-07-28 01:41:53,752 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计3840条数据
2025-07-28 01:41:53,752 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要3840条，实际获取3840条
2025-07-28 01:41:53,752 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: None ~ None
2025-07-28 01:41:53,752 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250705
2025-07-28 01:41:53,762 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-07-28 01:41:53,764 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共3600条记录
2025-07-28 01:41:53,765 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 01:41:53,778 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.640, 前复权=7.640
2025-07-28 01:41:53,779 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 01:41:53,779 - Main - INFO - info:307 - ℹ️   日期: 202507070931
2025-07-28 01:41:53,779 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.64
2025-07-28 01:41:53,779 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.64
2025-07-28 01:41:53,780 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 3600 >= 5
2025-07-28 01:41:53,780 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 01:41:53,780 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 01:41:53,780 - Main - INFO - info:307 - ℹ️ 获得新数据: 3600 条记录
2025-07-28 01:41:53,783 - Main - INFO - info:307 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-07-28 01:41:53,792 - Main - INFO - info:307 - ℹ️ 合并后数据: 23981 条记录
2025-07-28 01:41:53,855 - Main - INFO - info:307 - ℹ️ 删除旧文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-28 01:41:53,855 - Main - INFO - info:307 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503030937-202507251500_来源互联网（202507280141）.txt
2025-07-28 01:41:53,856 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-28 01:41:53,856 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-28 01:41:53,856 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-28 01:41:53,857 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-28 01:41:53,857 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-28 01:41:53,857 - Main - INFO - info:307 - ℹ️ 🔍 执行任务后数据质量稽核: 互联网分钟级数据下载
2025-07-28 01:41:53,857 - Main - INFO - info:307 - ℹ️ 📊 发现 2 个新生成文件，开始稽核
2025-07-28 01:41:54,397 - Main - INFO - info:307 - ℹ️ ✅ 执行后稽核通过: 1 优秀, 1 良好
2025-07-28 01:41:54,397 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: 前复权数据比较分析
2025-07-28 01:41:54,398 - Main - INFO - info:307 - ℹ️ 📊 发现 5 个现有数据文件，开始稽核
2025-07-28 01:41:54,749 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 4 个文件质量不达标
2025-07-28 01:41:54,749 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 01:41:54,749 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-28 01:41:54,750 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-28 01:41:54,751 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-28 01:41:54,751 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-28 01:41:54,751 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-28 01:41:54,751 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-28 01:41:54,751 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: True, 互联网: False
2025-07-28 01:41:54,753 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250728_014154.txt
2025-07-28 01:41:54,753 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-28 01:41:54,753 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-28 01:41:54,753 - Main - INFO - info:307 - ℹ️ 🔍 执行最终数据质量稽核汇总
2025-07-28 01:41:54,754 - Main - INFO - info:307 - ℹ️ 📊 对 5 个数据文件进行最终稽核
2025-07-28 01:41:55,116 - Main - INFO - info:307 - ℹ️ 📊 最终数据质量稽核汇总:
2025-07-28 01:41:55,116 - Main - INFO - info:307 - ℹ️   总文件数: 2
2025-07-28 01:41:55,116 - Main - INFO - info:307 - ℹ️   优秀文件: 1 (50.0%)
2025-07-28 01:41:55,116 - Main - INFO - info:307 - ℹ️   良好文件: 1 (50.0%)
2025-07-28 01:41:55,116 - Main - INFO - info:307 - ℹ️   较差文件: 0 (0.0%)
2025-07-28 01:41:55,117 - Main - INFO - info:307 - ℹ️   平均完整率: 95.89%
2025-07-28 01:41:55,117 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 66.7%
2025-07-28 01:41:55,118 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 01:41:55,118 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 01:41:55,118 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-28 01:41:55,118 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-28 01:41:55,118 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 01:41:55,118 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 01:41:55,118 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
