#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
批量替换print调用为safe_print
"""

import re

def fix_print_calls():
    """修复structured_output_formatter.py中的print调用"""
    file_path = "utils/structured_output_formatter.py"
    
    try:
        # 读取文件
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 替换print调用为safe_print，但保留safe_print函数内部的print调用
        lines = content.split('\n')
        modified_lines = []
        in_safe_print_function = False
        
        for i, line in enumerate(lines):
            # 检查是否在safe_print函数内部
            if 'def safe_print(' in line:
                in_safe_print_function = True
            elif in_safe_print_function and line.strip() and not line.startswith(' ') and not line.startswith('\t'):
                in_safe_print_function = False
            
            # 如果不在safe_print函数内部，替换print调用
            if not in_safe_print_function and 'print(' in line and 'safe_print(' not in line:
                # 替换print为safe_print
                modified_line = line.replace('print(', 'safe_print(')
                modified_lines.append(modified_line)
                print(f"第{i+1}行: {line.strip()} -> {modified_line.strip()}")
            else:
                modified_lines.append(line)
        
        # 写回文件
        modified_content = '\n'.join(modified_lines)
        with open(file_path, 'w', encoding='utf-8') as f:
            f.write(modified_content)
        
        print(f"✅ 修复完成，已处理 {file_path}")
        return True
        
    except Exception as e:
        print(f"❌ 修复失败: {e}")
        return False

if __name__ == '__main__':
    fix_print_calls()
