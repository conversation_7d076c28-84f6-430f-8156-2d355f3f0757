#!/usr/bin/env python3
"""
代码质量检查工具

基于本次函数名称冲突问题的经验，创建自动化的代码质量检查工具。
专门检测可能导致运行时错误的代码质量问题。
"""

import os
import re
import ast
import sys
from typing import Dict, List, Any
from collections import defaultdict

class CodeQualityChecker:
    """代码质量检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.issues = []
    
    def check_duplicate_function_definitions(self, file_path: str) -> List[Dict[str, Any]]:
        """
        检查文件中的重复函数定义
        
        这是基于本次函数名称冲突问题的经验开发的检查器
        """
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 使用正则表达式查找所有函数定义
            function_pattern = r'^\s*def\s+(\w+)\s*\('
            matches = re.finditer(function_pattern, content, re.MULTILINE)
            
            # 统计函数名出现次数
            function_counts = defaultdict(list)
            for match in matches:
                func_name = match.group(1)
                line_num = content[:match.start()].count('\n') + 1
                function_counts[func_name].append(line_num)
            
            # 找出重复定义的函数
            for func_name, line_numbers in function_counts.items():
                if len(line_numbers) > 1:
                    issues.append({
                        'type': 'duplicate_function_definition',
                        'severity': 'high',
                        'function_name': func_name,
                        'line_numbers': line_numbers,
                        'file_path': file_path,
                        'description': f"函数 '{func_name}' 在第 {line_numbers} 行重复定义",
                        'impact': '可能导致Python名称解析错误和变量作用域问题',
                        'solution': '删除重复定义或使用不同的函数名'
                    })
        
        except Exception as e:
            issues.append({
                'type': 'file_analysis_error',
                'severity': 'medium',
                'file_path': file_path,
                'error': str(e),
                'description': f"文件分析失败: {e}"
            })
        
        return issues
    
    def check_variable_scope_issues(self, file_path: str) -> List[Dict[str, Any]]:
        """
        检查可能的变量作用域问题
        """
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找可能的作用域问题模式
            # 1. 在函数内部调用同名的全局函数
            function_call_pattern = r'(\w+)\s*\('
            function_def_pattern = r'def\s+(\w+)\s*\('
            
            # 提取所有函数定义
            function_defs = re.findall(function_def_pattern, content)
            function_calls = re.findall(function_call_pattern, content)
            
            # 查找可能的冲突
            for func_name in set(function_defs):
                if function_defs.count(func_name) > 1 and func_name in function_calls:
                    issues.append({
                        'type': 'potential_scope_conflict',
                        'severity': 'medium',
                        'function_name': func_name,
                        'file_path': file_path,
                        'description': f"函数 '{func_name}' 存在重复定义且被调用，可能导致作用域问题",
                        'recommendation': '检查函数调用是否指向正确的定义'
                    })
        
        except Exception as e:
            issues.append({
                'type': 'scope_analysis_error',
                'severity': 'low',
                'file_path': file_path,
                'error': str(e)
            })
        
        return issues
    
    def check_import_consistency(self, file_path: str) -> List[Dict[str, Any]]:
        """
        检查导入一致性问题
        """
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找重复的导入
            import_pattern = r'^\s*from\s+([^\s]+)\s+import\s+([^\n]+)'
            imports = re.findall(import_pattern, content, re.MULTILINE)
            
            # 检查是否有重复导入相同的函数
            imported_functions = defaultdict(list)
            for module, functions in imports:
                for func in functions.split(','):
                    func = func.strip()
                    imported_functions[func].append(module)
            
            for func_name, modules in imported_functions.items():
                if len(set(modules)) > 1:
                    issues.append({
                        'type': 'conflicting_imports',
                        'severity': 'medium',
                        'function_name': func_name,
                        'modules': modules,
                        'file_path': file_path,
                        'description': f"函数 '{func_name}' 从多个模块导入: {modules}",
                        'impact': '可能导致名称冲突和调用错误'
                    })
        
        except Exception as e:
            issues.append({
                'type': 'import_analysis_error',
                'severity': 'low',
                'file_path': file_path,
                'error': str(e)
            })
        
        return issues
    
    def scan_project(self) -> Dict[str, Any]:
        """
        扫描整个项目的代码质量问题
        """
        all_issues = []
        scanned_files = []
        
        # 扫描Python文件
        for root, dirs, files in os.walk(self.project_root):
            # 跳过虚拟环境和缓存目录
            dirs[:] = [d for d in dirs if d not in ['.venv', '__pycache__', '.git', 'node_modules']]
            
            for file in files:
                if file.endswith('.py'):
                    file_path = os.path.join(root, file)
                    scanned_files.append(file_path)
                    
                    # 检查重复函数定义
                    duplicate_issues = self.check_duplicate_function_definitions(file_path)
                    all_issues.extend(duplicate_issues)
                    
                    # 检查变量作用域问题
                    scope_issues = self.check_variable_scope_issues(file_path)
                    all_issues.extend(scope_issues)
                    
                    # 检查导入一致性
                    import_issues = self.check_import_consistency(file_path)
                    all_issues.extend(import_issues)
        
        # 按严重程度分类
        issues_by_severity = defaultdict(list)
        for issue in all_issues:
            severity = issue.get('severity', 'unknown')
            issues_by_severity[severity].append(issue)
        
        return {
            'total_files_scanned': len(scanned_files),
            'total_issues_found': len(all_issues),
            'issues_by_severity': dict(issues_by_severity),
            'all_issues': all_issues,
            'scanned_files': scanned_files
        }
    
    def generate_report(self, scan_result: Dict[str, Any]) -> str:
        """生成代码质量检查报告"""
        report = []
        report.append("🔍 代码质量检查报告")
        report.append("=" * 50)
        
        report.append(f"\n📊 扫描统计:")
        report.append(f"   扫描文件数: {scan_result['total_files_scanned']}")
        report.append(f"   发现问题数: {scan_result['total_issues_found']}")
        
        issues_by_severity = scan_result['issues_by_severity']
        
        if issues_by_severity.get('high'):
            report.append(f"\n🚨 高严重性问题 ({len(issues_by_severity['high'])}个):")
            for issue in issues_by_severity['high']:
                report.append(f"   ❌ {issue['description']}")
                report.append(f"      文件: {issue['file_path']}")
                report.append(f"      影响: {issue.get('impact', '未知')}")
                report.append(f"      解决: {issue.get('solution', '需要手动修复')}")
        
        if issues_by_severity.get('medium'):
            report.append(f"\n⚠️ 中等严重性问题 ({len(issues_by_severity['medium'])}个):")
            for issue in issues_by_severity['medium'][:5]:  # 只显示前5个
                report.append(f"   ⚠️ {issue['description']}")
                report.append(f"      文件: {issue['file_path']}")
        
        if issues_by_severity.get('low'):
            report.append(f"\n💡 低严重性问题: {len(issues_by_severity['low'])}个")
        
        if scan_result['total_issues_found'] == 0:
            report.append(f"\n✅ 未发现代码质量问题")
        
        return '\n'.join(report)

def main():
    """主函数"""
    print("🚀 MythQuant代码质量检查工具")
    print("=" * 60)
    
    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.join(current_dir, '..')
    
    # 创建检查器
    checker = CodeQualityChecker(project_root)
    
    # 执行扫描
    print("🔍 正在扫描项目代码...")
    scan_result = checker.scan_project()
    
    # 生成报告
    report = checker.generate_report(scan_result)
    print(report)
    
    # 保存报告到文件
    report_file = os.path.join(project_root, 'code_quality_report.txt')
    with open(report_file, 'w', encoding='utf-8') as f:
        f.write(report)
    
    print(f"\n📄 详细报告已保存到: {report_file}")
    
    # 返回状态码
    high_issues = len(scan_result['issues_by_severity'].get('high', []))
    return 1 if high_issues > 0 else 0

if __name__ == '__main__':
    sys.exit(main())
