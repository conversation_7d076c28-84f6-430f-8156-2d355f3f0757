#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
交易相关值对象

交易时段、市场状态、订单类型等交易相关的值对象
"""

from enum import Enum
from dataclasses import dataclass
from datetime import time, date, datetime
from typing import Optional
from ...shared.patterns.architectural_patterns import ValueObject


class MarketStatus(Enum):
    """市场状态"""
    CLOSED = "CLOSED"        # 闭市
    OPEN = "OPEN"            # 开市
    HALTED = "HALTED"        # 暂停交易
    PRE_MARKET = "PRE_MARKET"    # 盘前
    POST_MARKET = "POST_MARKET"  # 盘后
    
    @property
    def is_tradable(self) -> bool:
        """是否可交易"""
        return self == MarketStatus.OPEN
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        names = {
            MarketStatus.CLOSED: "闭市",
            MarketStatus.OPEN: "开市",
            MarketStatus.HALTED: "暂停",
            MarketStatus.PRE_MARKET: "盘前",
            MarketStatus.POST_MARKET: "盘后"
        }
        return names.get(self, self.value)


class TradingSession(Enum):
    """交易时段"""
    MORNING = "MORNING"          # 上午时段
    AFTERNOON = "AFTERNOON"      # 下午时段
    NIGHT = "NIGHT"              # 夜盘（期货）
    CONTINUOUS = "CONTINUOUS"    # 连续交易
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        names = {
            TradingSession.MORNING: "上午时段",
            TradingSession.AFTERNOON: "下午时段", 
            TradingSession.NIGHT: "夜盘",
            TradingSession.CONTINUOUS: "连续交易"
        }
        return names.get(self, self.value)


class OrderType(Enum):
    """订单类型"""
    MARKET = "MARKET"            # 市价单
    LIMIT = "LIMIT"              # 限价单
    STOP = "STOP"                # 止损单
    STOP_LIMIT = "STOP_LIMIT"    # 止损限价单
    TRAILING_STOP = "TRAILING_STOP"  # 跟踪止损单
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        names = {
            OrderType.MARKET: "市价单",
            OrderType.LIMIT: "限价单",
            OrderType.STOP: "止损单",
            OrderType.STOP_LIMIT: "止损限价单",
            OrderType.TRAILING_STOP: "跟踪止损单"
        }
        return names.get(self, self.value)
    
    @property
    def requires_price(self) -> bool:
        """是否需要指定价格"""
        return self in {OrderType.LIMIT, OrderType.STOP_LIMIT}
    
    @property
    def requires_stop_price(self) -> bool:
        """是否需要指定止损价格"""
        return self in {OrderType.STOP, OrderType.STOP_LIMIT, OrderType.TRAILING_STOP}


class OrderSide(Enum):
    """订单方向"""
    BUY = "BUY"      # 买入
    SELL = "SELL"    # 卖出
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        names = {
            OrderSide.BUY: "买入",
            OrderSide.SELL: "卖出"
        }
        return names.get(self, self.value)
    
    @property
    def opposite(self) -> 'OrderSide':
        """相反方向"""
        return OrderSide.SELL if self == OrderSide.BUY else OrderSide.BUY


class OrderStatus(Enum):
    """订单状态"""
    PENDING = "PENDING"              # 待处理
    SUBMITTED = "SUBMITTED"          # 已提交
    PARTIALLY_FILLED = "PARTIALLY_FILLED"  # 部分成交
    FILLED = "FILLED"                # 完全成交
    CANCELLED = "CANCELLED"          # 已取消
    REJECTED = "REJECTED"            # 已拒绝
    EXPIRED = "EXPIRED"              # 已过期
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        names = {
            OrderStatus.PENDING: "待处理",
            OrderStatus.SUBMITTED: "已提交",
            OrderStatus.PARTIALLY_FILLED: "部分成交",
            OrderStatus.FILLED: "完全成交",
            OrderStatus.CANCELLED: "已取消",
            OrderStatus.REJECTED: "已拒绝",
            OrderStatus.EXPIRED: "已过期"
        }
        return names.get(self, self.value)
    
    @property
    def is_active(self) -> bool:
        """是否为活跃状态"""
        return self in {OrderStatus.PENDING, OrderStatus.SUBMITTED, OrderStatus.PARTIALLY_FILLED}
    
    @property
    def is_final(self) -> bool:
        """是否为最终状态"""
        return self in {OrderStatus.FILLED, OrderStatus.CANCELLED, OrderStatus.REJECTED, OrderStatus.EXPIRED}


class TimeInForce(Enum):
    """订单有效期"""
    DAY = "DAY"                  # 当日有效
    GTC = "GTC"                  # 撤销前有效 (Good Till Cancelled)
    IOC = "IOC"                  # 立即成交或取消 (Immediate Or Cancel)
    FOK = "FOK"                  # 全部成交或取消 (Fill Or Kill)
    GTD = "GTD"                  # 指定日期前有效 (Good Till Date)
    
    @property
    def display_name(self) -> str:
        """显示名称"""
        names = {
            TimeInForce.DAY: "当日有效",
            TimeInForce.GTC: "撤销前有效",
            TimeInForce.IOC: "立即成交或取消",
            TimeInForce.FOK: "全部成交或取消",
            TimeInForce.GTD: "指定日期前有效"
        }
        return names.get(self, self.value)


@dataclass(frozen=True)
class TradingCalendar(ValueObject):
    """交易日历"""
    market_code: str
    trading_days: frozenset[date]
    holidays: frozenset[date]
    
    def is_trading_day(self, target_date: date) -> bool:
        """是否为交易日"""
        return target_date in self.trading_days
    
    def is_holiday(self, target_date: date) -> bool:
        """是否为假期"""
        return target_date in self.holidays
    
    def get_next_trading_day(self, from_date: date) -> Optional[date]:
        """获取下一个交易日"""
        current_date = from_date
        max_days = 30  # 最多查找30天
        
        for _ in range(max_days):
            current_date = date.fromordinal(current_date.toordinal() + 1)
            if self.is_trading_day(current_date):
                return current_date
        
        return None
    
    def get_previous_trading_day(self, from_date: date) -> Optional[date]:
        """获取上一个交易日"""
        current_date = from_date
        max_days = 30  # 最多查找30天
        
        for _ in range(max_days):
            current_date = date.fromordinal(current_date.toordinal() - 1)
            if self.is_trading_day(current_date):
                return current_date
        
        return None


@dataclass(frozen=True)
class TradingWindow(ValueObject):
    """交易时间窗口"""
    start_time: time
    end_time: time
    session: TradingSession
    
    def __post_init__(self):
        if self.start_time >= self.end_time:
            raise ValueError(f"开始时间{self.start_time}必须早于结束时间{self.end_time}")
    
    def contains(self, target_time: time) -> bool:
        """是否包含指定时间"""
        return self.start_time <= target_time <= self.end_time
    
    def duration_minutes(self) -> int:
        """持续时间（分钟）"""
        start_minutes = self.start_time.hour * 60 + self.start_time.minute
        end_minutes = self.end_time.hour * 60 + self.end_time.minute
        return end_minutes - start_minutes
    
    def __str__(self) -> str:
        return f"{self.session.display_name}: {self.start_time}-{self.end_time}"


@dataclass(frozen=True)
class PriceLimit(ValueObject):
    """价格限制"""
    upper_limit: Optional[float] = None  # 涨停价
    lower_limit: Optional[float] = None  # 跌停价
    limit_percentage: Optional[float] = None  # 涨跌幅限制百分比
    
    def is_within_limits(self, price: float, reference_price: float) -> bool:
        """价格是否在限制范围内"""
        if self.upper_limit is not None and price > self.upper_limit:
            return False
        if self.lower_limit is not None and price < self.lower_limit:
            return False
        
        if self.limit_percentage is not None and reference_price > 0:
            max_change = reference_price * self.limit_percentage / 100
            if abs(price - reference_price) > max_change:
                return False
        
        return True
    
    def calculate_limits(self, reference_price: float) -> tuple[Optional[float], Optional[float]]:
        """根据参考价格计算涨跌停价格"""
        if self.limit_percentage is None:
            return self.upper_limit, self.lower_limit
        
        max_change = reference_price * self.limit_percentage / 100
        upper = reference_price + max_change
        lower = reference_price - max_change
        
        return upper, lower


@dataclass(frozen=True)
class LotSize(ValueObject):
    """交易单位"""
    size: int
    minimum_quantity: int = 1
    
    def __post_init__(self):
        if self.size <= 0:
            raise ValueError("交易单位必须大于0")
        if self.minimum_quantity <= 0:
            raise ValueError("最小交易数量必须大于0")
    
    def is_valid_quantity(self, quantity: int) -> bool:
        """数量是否有效"""
        if quantity < self.minimum_quantity:
            return False
        return quantity % self.size == 0
    
    def round_to_lot(self, quantity: int) -> int:
        """向下取整到交易单位"""
        if quantity < self.minimum_quantity:
            return 0
        return (quantity // self.size) * self.size
    
    def round_up_to_lot(self, quantity: int) -> int:
        """向上取整到交易单位"""
        if quantity < self.minimum_quantity:
            return self.minimum_quantity
        
        if quantity % self.size == 0:
            return quantity
        else:
            return ((quantity // self.size) + 1) * self.size


# 工厂函数
def create_chinese_stock_trading_windows() -> list[TradingWindow]:
    """创建中国股票市场交易时间窗口"""
    return [
        TradingWindow(
            start_time=time(9, 30),
            end_time=time(11, 30),
            session=TradingSession.MORNING
        ),
        TradingWindow(
            start_time=time(13, 0),
            end_time=time(15, 0),
            session=TradingSession.AFTERNOON
        )
    ]


def create_chinese_stock_price_limit(reference_price: float, 
                                   limit_percentage: float = 10.0) -> PriceLimit:
    """创建中国股票价格限制"""
    max_change = reference_price * limit_percentage / 100
    return PriceLimit(
        upper_limit=reference_price + max_change,
        lower_limit=reference_price - max_change,
        limit_percentage=limit_percentage
    )


def create_chinese_stock_lot_size() -> LotSize:
    """创建中国股票交易单位（100股为1手）"""
    return LotSize(size=100, minimum_quantity=100)


# 验证函数
def is_valid_trading_time(target_time: time, 
                         trading_windows: list[TradingWindow]) -> bool:
    """验证是否为有效交易时间"""
    return any(window.contains(target_time) for window in trading_windows)


def get_current_trading_session(target_time: time,
                               trading_windows: list[TradingWindow]) -> Optional[TradingSession]:
    """获取当前交易时段"""
    for window in trading_windows:
        if window.contains(target_time):
            return window.session
    return None
