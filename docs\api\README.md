# MythQuant API 文档

## 概述

MythQuant 提供了现代化的Python量化交易API，支持多数据源、高精度计算和完整的向后兼容性。

## 快速开始

### 安装和导入

```python
# 方式1：使用兼容性模块（推荐，现有代码无需修改）
import config_compatibility as config
import data_access_compatibility as data_access
import algorithm_compatibility as algo
import io_compatibility as io_compat

# 方式2：使用新架构API
from mythquant.config import config_manager
from mythquant.data.sources import DataSourceManager
from mythquant.algorithms import L2MetricsCalculator
from mythquant.io import OutputWriter
```

### 基本使用示例

```python
# 配置管理
tdx_path = config.get_tdx_path()
debug_mode = config.is_debug_enabled()

# 数据访问
stock_data = data_access.read_stock_day_data("000001")

# 算法计算
l2_data = algo.calculate_l2_metrics(stock_data)

# 数据输出
output_path = io_compat.write_stock_data_file(l2_data, "000001", "day")
```

## 核心模块

### 1. 配置管理 (config_compatibility)

#### 主要函数

- `get_tdx_path()` - 获取TDX路径
- `is_debug_enabled()` - 获取调试模式
- `get_output_path()` - 获取输出路径
- `get_file_encoding()` - 获取文件编码

#### 使用示例

```python
import config_compatibility as config

# 获取配置
tdx_path = config.get_tdx_path()
output_path = config.get_output_path()
debug_mode = config.is_debug_enabled()

print(f"TDX路径: {tdx_path}")
print(f"输出路径: {output_path}")
print(f"调试模式: {debug_mode}")
```

### 2. 数据访问 (data_access_compatibility)

#### 主要函数

- `read_stock_day_data(stock_code)` - 读取日线数据
- `read_stock_minute_data(stock_code, frequency=1)` - 读取分钟数据
- `get_stock_list()` - 获取股票列表
- `test_data_sources_connectivity()` - 测试数据源连通性

#### 使用示例

```python
import data_access_compatibility as data_access

# 读取股票数据
day_data = data_access.read_stock_day_data("000001")
minute_data = data_access.read_stock_minute_data("000001", frequency=1)

# 获取股票列表
stock_list = data_access.get_stock_list()

# 测试连通性
connectivity = data_access.test_data_sources_connectivity()
```

### 3. 算法计算 (algorithm_compatibility)

#### 主要函数

- `calculate_forward_adjustment(price_data, dividend_data=None)` - 前复权计算
- `calculate_l2_metrics(df)` - L2指标计算
- `calculate_buy_sell_metrics(df)` - 主买主卖计算
- `calculate_technical_indicators(df)` - 技术指标计算

#### 使用示例

```python
import algorithm_compatibility as algo

# 前复权计算
adj_data = algo.calculate_forward_adjustment(price_data, dividend_data)

# L2指标计算
l2_data = algo.calculate_l2_metrics(stock_data)

# 主买主卖计算
buy_sell_data = algo.calculate_buy_sell_metrics(stock_data)

# 技术指标计算
tech_data = algo.calculate_technical_indicators(stock_data)
```

### 4. 数据输出 (io_compatibility)

#### 主要函数

- `write_stock_data_file(df, stock_code, data_type, start_date=None, end_date=None)` - 写入股票数据
- `format_stock_data_output(df, stock_code, data_type="day")` - 格式化数据输出
- `write_csv_file(df, filename, subdirectory=None)` - 写入CSV文件
- `read_excel_file(file_path, sheet_name=0)` - 读取Excel文件

#### 使用示例

```python
import io_compatibility as io_compat

# 写入股票数据文件
output_path = io_compat.write_stock_data_file(
    df, "000001", "day", "20230101", "20231231"
)

# 格式化数据输出
formatted_content = io_compat.format_stock_data_output(df, "000001", "day")

# 写入CSV文件
csv_path = io_compat.write_csv_file(df, "stock_data.csv")

# 读取Excel文件
excel_data = io_compat.read_excel_file("data.xlsx", sheet_name="Sheet1")
```

## 新架构API

### 配置管理器

```python
from mythquant.config import config_manager

# 获取配置
tdx_path = config_manager.get_tdx_path()
debug_mode = config_manager.is_debug_enabled()

# 验证配置
is_valid, errors = config_manager.validate_config()
```

### 数据源管理器

```python
from mythquant.data.sources import DataSourceManager, DataSourceType

data_manager = DataSourceManager(config_manager)

# 获取股票数据
data = data_manager.get_stock_data(
    stock_code="000001",
    data_type="day",
    source_priority=[DataSourceType.TDX, DataSourceType.PYTDX]
)

# 测试连通性
connectivity = data_manager.test_data_source_connectivity()
```

### 算法计算器

```python
from mythquant.algorithms import L2MetricsCalculator, ForwardAdjustmentCalculator

# L2指标计算器
l2_calculator = L2MetricsCalculator(config_manager)
l2_data = l2_calculator.calculate_l2_metrics(stock_data)

# 前复权计算器
adj_calculator = ForwardAdjustmentCalculator(config_manager)
adj_data = adj_calculator.calculate_forward_adjustment(price_data, dividend_data)
```

### 输出写入器

```python
from mythquant.io import OutputWriter

writer = OutputWriter(config_manager)

# 写入股票数据
output_path = writer.write_stock_data(df, "000001", "day")

# 批量写入
results = writer.batch_write_stock_data(data_dict, "day")
```

## 数据格式

### 输入数据格式

#### 股票数据 DataFrame

| 列名 | 类型 | 描述 |
|------|------|------|
| date/datetime | datetime | 日期时间 |
| open | float | 开盘价 |
| high | float | 最高价 |
| low | float | 最低价 |
| close | float | 收盘价 |
| volume | int | 成交量 |

#### 除权除息数据 DataFrame

| 列名 | 类型 | 描述 |
|------|------|------|
| date | datetime | 除权日期 |
| dividend | float | 分红金额 |
| split_ratio | float | 拆股比例 |

### 输出数据格式

#### 标准输出格式

```
股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
000001|2023-01-01|0.100|10.000|9.800|0.500|0.300|0.200
000001|2023-01-02|0.150|10.200|10.000|0.600|0.375|0.225
```

## 错误处理

### 常见错误类型

1. **配置错误** - TDX路径不存在、配置文件格式错误
2. **数据访问错误** - 数据源连接失败、股票代码不存在
3. **算法计算错误** - 数据格式不正确、必要列缺失
4. **IO错误** - 文件写入权限不足、磁盘空间不足

### 错误处理示例

```python
try:
    # 数据访问
    stock_data = data_access.read_stock_day_data("000001")
    if stock_data is None or stock_data.empty:
        print("警告: 未获取到股票数据")
        return
    
    # 算法计算
    l2_data = algo.calculate_l2_metrics(stock_data)
    if l2_data is None or l2_data.empty:
        print("警告: L2指标计算失败")
        return
    
    # 数据输出
    output_path = io_compat.write_stock_data_file(l2_data, "000001", "day")
    if output_path is None:
        print("错误: 数据写入失败")
        return
    
    print(f"成功: 数据已写入 {output_path}")
    
except Exception as e:
    print(f"错误: {e}")
```

## 性能优化

### 最佳实践

1. **批量处理** - 使用批量函数处理多只股票
2. **数据缓存** - 避免重复读取相同数据
3. **精度控制** - 使用Decimal进行高精度计算
4. **内存管理** - 及时释放大型DataFrame

### 性能示例

```python
# 批量处理多只股票
stock_codes = ["000001", "000002", "000003"]
data_dict = {}

for code in stock_codes:
    data_dict[code] = data_access.read_stock_day_data(code)

# 批量写入
results = io_compat.batch_write_stock_data(data_dict, "day")
```

## 版本兼容性

### 向后兼容性

- ✅ 现有代码无需修改
- ✅ 保持原有函数接口
- ✅ 支持原有数据格式
- ✅ 兼容原有配置方式

### 迁移建议

1. **第一阶段** - 使用兼容性模块，无需修改现有代码
2. **第二阶段** - 逐步采用新架构API，享受更好的功能
3. **第三阶段** - 完全迁移到新架构，获得最佳性能

## 支持和帮助

### 常见问题

1. **Q: 如何配置TDX路径？**
   A: 修改user_config.py中的tdx_path配置

2. **Q: 如何处理数据源连接失败？**
   A: 系统会自动回退到其他可用数据源

3. **Q: 如何提高计算精度？**
   A: 新架构使用Decimal进行高精度计算

### 技术支持

- 📧 邮箱支持: <EMAIL>
- 📖 文档: docs/
- 🐛 问题报告: GitHub Issues
- 💬 社区讨论: GitHub Discussions
