# 高效提问指南 - AI协作最佳实践

## 📖 概述

本文档总结了与AI助手进行高效技术协作的提问技巧和沟通策略，帮助开发者快速准确地表达问题，获得更精准的解决方案。

**最后更新**: 2025-08-08  
**适用场景**: 技术问题诊断、架构设计讨论、代码优化、系统调试

---

## 🎯 核心原则

### 1. 从现象到本质
- **避免**: 只描述表面现象
- **推荐**: 分析问题的根本原因
- **目标**: 让AI理解问题的本质而非症状

### 2. 从局部到全局  
- **避免**: 孤立地看待单个问题
- **推荐**: 考虑问题的系统性影响
- **目标**: 获得全面的解决方案

### 3. 从技术到业务
- **避免**: 纯技术角度的问题描述
- **推荐**: 结合业务价值和用户体验
- **目标**: 确保解决方案符合业务需求

---

## 🚀 高效提问方式

### 方式1: 问题描述式提问

**❌ 低效方式**:
```
[粘贴50行terminal输出]
程序有问题，帮我看看
```

**✅ 高效方式**:
```
程序在执行结构化四步流程前，还执行了一个额外的数据质量稽核步骤，
这个步骤不在workflow规范中，请帮我找到并移除这个多余的流程。

背景: 按照1min_workflow_improved.md规范，应该直接执行四步流程
现象: 在四步流程前出现了额外的稽核步骤
影响: 违反了workflow规范，产生了混乱的输出
期望: 移除多余步骤，严格按照workflow执行
```

### 方式2: 关键信息提取式提问

**❌ 低效方式**:
```
[粘贴完整程序输出200行]
```

**✅ 高效方式**:
```
程序输出中出现了两个【1/1】标识：
1. Application层：【1/1】执行任务: 分钟级数据生成  
2. Infrastructure层：【1/1】处理股票: 000617

问题: 这种重复显示不合理，用户会困惑
分析: 可能是层级职责不清晰导致的
请求: 请修复层级显示问题，明确各层职责
```

### 方式3: 对比式提问

**❌ 低效方式**:
```
程序有问题，输出很乱
```

**✅ 高效方式**:
```
期望行为: 直接执行结构化四步流程
实际行为: 在四步流程前还有额外的pytdx下载和警告
差异分析: 存在workflow规范外的多余步骤
解决目标: 移除多余步骤，提升用户体验
```

### 方式4: 具体定位式提问

**❌ 低效方式**:
```
程序运行有警告
```

**✅ 高效方式**:
```
问题定位: TaskManager执行任务时，在智能文件选择之前就出现了警告
具体警告: '数据覆盖不足'、'pytdx服务器限制'等
时机问题: 这些警告出现在workflow规定的流程之外
技术问题: 这些警告来自哪个模块？如何移除？
架构问题: 是否违反了分层架构的原则？
```

### 方式5: 架构层面提问

**❌ 低效方式**:
```
这行代码报错了
```

**✅ 高效方式**:
```
架构分析: Application层显示任务级别的【1/1】，Infrastructure层显示股票级别的【1/1】
设计质疑: 这是否符合DDD架构的分层原则？
职责划分: 各层级的显示职责应该如何划分？
用户体验: 如何避免用户看到重复的进度信息？
解决方向: 建议采用什么样的显示策略？
```

---

## 📝 实用提问模板

### 模板1: 问题定位模板
```
在[具体场景]中，出现了[具体现象]，
但按照[标准/规范]应该是[期望行为]，
请帮我[具体要求]。

示例:
在执行分钟级数据生成任务时，出现了workflow规范外的数据质量稽核，
但按照1min_workflow_improved.md应该直接执行四步流程，
请帮我移除这个多余的稽核步骤。
```

### 模板2: 架构设计模板
```
[组件A]和[组件B]都在做[相同的事情]，
这是否符合[架构原则]？
如何优化[具体方面]？

示例:
Application层和Infrastructure层都在显示【1/1】进度，
这是否符合DDD的单一职责原则？
如何优化层级显示的职责分工？
```

### 模板3: 流程优化模板
```
当前流程: [步骤1] → [步骤2] → [步骤3]
问题分析: [具体问题]
期望流程: [优化后的流程]  
请帮我: [具体行动]

示例:
当前流程: 任务开始 → 额外稽核 → 四步流程
问题分析: 额外稽核不在workflow规范中
期望流程: 任务开始 → 直接四步流程
请帮我: 移除额外稽核步骤
```

### 模板4: 根本原因分析模板
```
现象描述: [观察到的问题]
影响分析: [对系统/用户的影响]
可能原因: [初步分析的原因]
验证请求: [需要AI帮助验证的假设]
解决期望: [期望的解决方案类型]

示例:
现象描述: 程序执行时间从1秒增加到8秒
影响分析: 用户体验下降，系统效率降低
可能原因: 可能存在重复的数据下载或处理
验证请求: 请帮我分析是否有不必要的重复操作
解决期望: 优化性能，恢复到1秒左右的执行时间
```

---

## 🔍 问题分析思维框架

### 框架1: 5W1H分析法
- **What**: 具体发生了什么问题？
- **When**: 问题在什么时候/什么阶段出现？
- **Where**: 问题出现在哪个模块/组件中？
- **Who**: 哪个角色/用户会受到影响？
- **Why**: 为什么会出现这个问题？
- **How**: 如何解决这个问题？

### 框架2: 层次分析法
```
表现层: 用户看到什么？
逻辑层: 业务逻辑有什么问题？
数据层: 数据处理是否正确？
架构层: 架构设计是否合理？
```

### 框架3: 影响范围分析
```
直接影响: 立即可见的问题
间接影响: 可能引发的连锁问题  
长期影响: 对系统演进的影响
用户影响: 对用户体验的影响
```

---

## 🎨 进阶提问技巧

### 技巧1: 预设上下文
```
基于我们之前讨论的DDD架构和workflow规范，
现在发现[新问题]，请按照相同的原则解决。

优势: 减少重复解释，提高沟通效率
适用: 连续对话中的后续问题
```

### 技巧2: 多角度分析
```
从用户体验角度: [问题A]
从架构设计角度: [问题B]
从性能优化角度: [问题C]  
从维护成本角度: [问题D]
请综合考虑解决方案。

优势: 获得全面的解决方案
适用: 复杂的系统性问题
```

### 技巧3: 约束条件明确
```
在保持[约束1]和[约束2]的前提下，
如何解决[具体问题]？

示例:
在保持向后兼容和DDD架构的前提下，
如何消除重复的【1/1】显示？

优势: 确保解决方案的可行性
适用: 有明确限制条件的问题
```

### 技巧4: 优先级排序
```
问题列表:
1. [高优先级问题] - 影响: [具体影响]
2. [中优先级问题] - 影响: [具体影响]  
3. [低优先级问题] - 影响: [具体影响]

请按优先级顺序解决，并说明解决策略。

优势: 确保重要问题优先解决
适用: 多个问题需要同时处理
```

---

## 📊 问题分类与应对策略

### 类型1: 设计合理性问题
**特征**: 质疑现有设计的合理性
**提问方式**: 
- "这种设计是否合理？"
- "是否违反了某个原则？"  
- "有没有更好的方案？"

**示例**:
```
当前设计: Application层和Infrastructure层都显示【1/1】
设计质疑: 这是否违反了单一职责原则？
替代方案: 是否应该只在一个层级显示进度？
评估标准: 用户体验、架构清晰度、维护成本
```

### 类型2: 根本原因探索
**特征**: 深入分析问题的根本原因
**提问方式**:
- "为什么会出现这个现象？"
- "根本原因是什么？"
- "如何从源头解决？"

**示例**:
```
现象: 程序执行了workflow规范外的额外步骤
分析: 为什么会有这个额外步骤？
源头: 是配置问题、代码逻辑问题还是架构设计问题？
解决: 如何从根本上避免类似问题？
```

### 类型3: 系统性改进
**特征**: 关注长期的系统改进
**提问方式**:
- "如何建立预防机制？"
- "如何确保类似问题不再发生？"
- "如何提升整体质量？"

**示例**:
```
当前问题: workflow规范执行不严格
预防机制: 如何建立自动化的规范检查？
质量提升: 如何建立更好的开发流程？
长期目标: 如何确保架构的持续演进？
```

---

## ⚡ 快速提问检查清单

### 提问前自检
- [ ] 问题描述是否清晰具体？
- [ ] 是否提供了足够的上下文？
- [ ] 是否明确了期望的结果？
- [ ] 是否考虑了问题的影响范围？
- [ ] 是否从多个角度分析了问题？

### 信息完整性检查
- [ ] 问题的具体表现
- [ ] 问题出现的场景/条件
- [ ] 期望的正确行为
- [ ] 已经尝试的解决方法
- [ ] 相关的约束条件

### 沟通效率检查  
- [ ] 避免了不必要的技术细节
- [ ] 突出了问题的核心
- [ ] 使用了清晰的结构化表达
- [ ] 提供了具体的行动请求

---

## 🌟 成功案例分析

### 案例1: workflow规范违反问题

**低效提问**:
```
[粘贴大量terminal输出]
程序有很多警告，帮我看看
```

**高效提问**:
```
程序在执行结构化四步流程前，还执行了一个额外的数据质量稽核步骤，
这个步骤不在workflow规范中，请帮我找到并移除这个多余的流程。
```

**结果**: AI快速定位到问题根源，提供了精准的解决方案

### 案例2: 架构设计问题

**低效提问**:
```
有两个【1/1】显示，看起来不对
```

**高效提问**:
```
Application层显示任务级别的【1/1】，Infrastructure层显示股票级别的【1/1】，
这是否符合DDD架构的分层原则？如何优化层级显示的职责分工？
```

**结果**: AI从架构角度分析问题，提供了符合DDD原则的解决方案

---

## 🔄 持续改进机制

### 反馈收集
- 记录每次提问的效果
- 分析哪种提问方式更有效
- 总结成功和失败的案例

### 技能提升
- 定期回顾和更新提问技巧
- 学习新的分析框架和方法
- 与团队分享最佳实践

### 知识库维护
- 定期更新本文档
- 添加新的案例和模板
- 完善分类和索引

---

## 📚 相关资源

### 内部文档
- `workflow_compliance_knowledge.md` - workflow规范遵循
- `debugging_knowledge_base.md` - 调试知识库
- `faq_knowledge_base.md` - 常见问题解答

### 外部参考
- 《提问的智慧》- 经典的技术提问指南
- 《金字塔原理》- 结构化思维和表达
- 《系统思考》- 系统性问题分析方法

---

## 📝 更新日志

### 2025-08-08
- 创建初始版本
- 添加核心提问方式和模板
- 建立问题分析框架
- 提供实用的检查清单

### 待更新内容
- [ ] 添加更多实际案例
- [ ] 完善问题分类体系
- [ ] 增加团队协作的提问技巧
- [ ] 建立提问效果评估机制

---

## 🎓 实战练习

### 练习1: 问题转换
将以下低效提问转换为高效提问：

**原始问题**: "代码报错了，帮我看看"
**转换练习**:
- 具体错误是什么？
- 在什么场景下出现？
- 期望的正确行为是什么？
- 可能的原因分析？

**参考答案**:
```
在执行[具体功能]时，出现了[具体错误信息]，
预期应该[正确行为]，但实际[错误行为]，
可能原因是[初步分析]，请帮我[具体请求]。
```

### 练习2: 架构问题分析
**场景**: 发现系统中有重复的功能实现
**练习**: 如何从架构角度提问？

**参考思路**:
- 识别重复的具体内容
- 分析违反了哪些设计原则
- 评估对系统的影响
- 提出优化方向

### 练习3: 性能问题诊断
**场景**: 系统响应变慢
**练习**: 如何系统性地分析性能问题？

**参考框架**:
- 性能基线对比
- 资源使用分析
- 瓶颈点识别
- 优化策略制定

---

## 🔧 工具和技巧

### 问题描述工具

#### 1. 问题陈述模板
```
标题: [简洁的问题描述]
背景: [相关上下文信息]
现状: [当前的具体情况]
期望: [希望达到的状态]
约束: [需要考虑的限制条件]
优先级: [问题的重要程度]
```

#### 2. 影响分析矩阵
```
影响维度 | 当前状态 | 期望状态 | 差距分析
---------|----------|----------|----------
用户体验 |          |          |
系统性能 |          |          |
维护成本 |          |          |
扩展性   |          |          |
```

#### 3. 解决方案评估表
```
方案选项 | 优势 | 劣势 | 实施难度 | 风险评估
---------|------|------|----------|----------
方案A    |      |      |          |
方案B    |      |      |          |
方案C    |      |      |          |
```

### 沟通技巧

#### 1. 结构化表达
- **总分总结构**: 先总述问题，再分析细节，最后总结请求
- **递进式逻辑**: 从现象到原因，从问题到解决方案
- **对比式说明**: 通过对比突出问题的关键点

#### 2. 关键信息突出
- 使用**加粗**强调重要概念
- 使用`代码块`标识技术术语
- 使用列表组织复杂信息
- 使用图表辅助说明

#### 3. 上下文管理
- 引用之前的讨论内容
- 明确当前问题与历史问题的关系
- 提供必要的背景信息
- 避免重复已知信息

---

## 📈 效果评估

### 提问质量指标

#### 1. 响应准确度
- AI是否准确理解了问题？
- 提供的解决方案是否针对性强？
- 是否需要多轮澄清？

#### 2. 解决效率
- 从提问到解决的时间
- 需要的交互轮次
- 解决方案的完整性

#### 3. 知识传递
- 是否获得了可复用的知识？
- 是否理解了问题的本质？
- 是否能够举一反三？

### 改进反馈循环

#### 1. 即时反馈
- 每次提问后评估效果
- 记录成功和失败的案例
- 调整提问策略

#### 2. 定期回顾
- 周期性分析提问模式
- 识别常见的问题类型
- 优化提问模板

#### 3. 知识沉淀
- 将成功经验文档化
- 建立问题-解决方案知识库
- 分享最佳实践

---

## 🌐 团队协作提问

### 多人协作场景

#### 1. 问题委托
```
代表[团队成员]提问：
问题背景: [成员遇到的具体问题]
已尝试方案: [团队已经尝试的解决方法]
需要帮助: [具体需要AI协助的方面]
时间要求: [解决问题的时间限制]
```

#### 2. 技术决策
```
团队面临技术选择：
选项A: [方案描述] - 优势: [具体优势] - 风险: [潜在风险]
选项B: [方案描述] - 优势: [具体优势] - 风险: [潜在风险]
评估维度: [性能、维护性、扩展性等]
请提供决策建议和理由。
```

#### 3. 代码审查
```
代码审查发现问题：
问题类型: [架构、性能、安全等]
具体位置: [文件、方法、行数]
问题描述: [详细的问题说明]
影响评估: [对系统的潜在影响]
请提供修复建议和最佳实践。
```

### 知识共享

#### 1. 经验总结
- 将解决方案整理成可复用的知识
- 建立团队的问题解决模式库
- 定期分享成功案例

#### 2. 标准化流程
- 建立团队的提问规范
- 统一问题分类和优先级
- 制定协作沟通协议

---

## 🚀 高级应用场景

### 场景1: 系统重构规划
```
重构目标: [具体的重构目标]
当前架构: [现有架构的主要问题]
约束条件: [时间、资源、兼容性等限制]
风险评估: [重构可能带来的风险]
请提供: 重构策略、实施计划、风险控制措施
```

### 场景2: 性能优化策略
```
性能现状: [当前性能指标和瓶颈]
业务需求: [性能目标和业务场景]
资源限制: [硬件、时间、人力限制]
技术栈: [当前使用的技术栈]
请分析: 优化方向、实施优先级、预期效果
```

### 场景3: 技术债务管理
```
债务清单: [具体的技术债务项目]
影响分析: [对开发效率和系统稳定性的影响]
资源评估: [可用于偿还债务的资源]
业务优先级: [业务对不同债务的容忍度]
请制定: 债务偿还计划和优先级排序
```

---

## 📋 快速参考卡片

### 提问公式
```
问题 = 背景 + 现状 + 期望 + 约束 + 请求
```

### 质量检查
```
5个必问问题：
1. 问题是否具体明确？
2. 上下文是否充分？
3. 期望结果是否清晰？
4. 约束条件是否明确？
5. 请求行动是否具体？
```

### 常用句式
```
描述问题: "在[场景]中，出现了[问题]..."
分析原因: "可能的原因是[分析]..."
提出期望: "期望能够[目标]..."
明确约束: "在[限制]的前提下..."
请求行动: "请帮我[具体行动]..."
```

---

**💡 记住**: 好的提问是高效协作的开始。通过清晰、具体、结构化的提问，您可以获得更精准、更有价值的解决方案。

**🎯 核心要点**:
1. **问题本质** > 表面现象
2. **结构化表达** > 随意描述
3. **具体请求** > 模糊期望
4. **系统思考** > 局部视角
5. **持续改进** > 一次性解决
