#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据库连接池实现

提供高性能的数据库连接池管理
"""

import logging
import threading
import time
from abc import ABC, abstractmethod
from typing import Any, Optional, Dict, List, Callable, ContextManager
from datetime import datetime, timedelta
from queue import Queue, Empty, Full
import sqlite3
from contextlib import contextmanager

from ...domain.exceptions import DomainException

logger = logging.getLogger(__name__)


class ConnectionPoolException(DomainException):
    """连接池异常"""
    pass


class PooledConnection:
    """池化连接包装器"""
    
    def __init__(self, connection: Any, pool: 'ConnectionPool', connection_id: str):
        self._connection = connection
        self._pool = pool
        self._connection_id = connection_id
        self._created_at = time.time()
        self._last_used = time.time()
        self._in_use = False
        self._is_valid = True
        self._use_count = 0
    
    @property
    def connection(self) -> Any:
        """获取原始连接"""
        return self._connection
    
    @property
    def connection_id(self) -> str:
        """连接ID"""
        return self._connection_id
    
    @property
    def created_at(self) -> float:
        """创建时间"""
        return self._created_at
    
    @property
    def last_used(self) -> float:
        """最后使用时间"""
        return self._last_used
    
    @property
    def in_use(self) -> bool:
        """是否正在使用"""
        return self._in_use
    
    @property
    def is_valid(self) -> bool:
        """连接是否有效"""
        return self._is_valid
    
    @property
    def use_count(self) -> int:
        """使用次数"""
        return self._use_count
    
    def mark_in_use(self):
        """标记为使用中"""
        self._in_use = True
        self._last_used = time.time()
        self._use_count += 1
    
    def mark_not_in_use(self):
        """标记为未使用"""
        self._in_use = False
        self._last_used = time.time()
    
    def invalidate(self):
        """标记连接为无效"""
        self._is_valid = False
    
    def age(self) -> float:
        """连接年龄（秒）"""
        return time.time() - self._created_at
    
    def idle_time(self) -> float:
        """空闲时间（秒）"""
        return time.time() - self._last_used if not self._in_use else 0
    
    def close(self):
        """关闭连接"""
        try:
            if hasattr(self._connection, 'close'):
                self._connection.close()
            self._is_valid = False
        except Exception as e:
            logger.error(f"关闭连接失败: {self._connection_id}, 错误: {e}")


class ConnectionPool(ABC):
    """连接池抽象基类"""
    
    @abstractmethod
    def get_connection(self, timeout: Optional[float] = None) -> PooledConnection:
        """获取连接"""
        pass
    
    @abstractmethod
    def return_connection(self, connection: PooledConnection):
        """归还连接"""
        pass
    
    @abstractmethod
    def close_all(self):
        """关闭所有连接"""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        pass


class SQLiteConnectionPool(ConnectionPool):
    """SQLite连接池"""
    
    def __init__(self, 
                 database_path: str,
                 min_connections: int = 2,
                 max_connections: int = 10,
                 max_idle_time: float = 300,  # 5分钟
                 max_lifetime: float = 3600,  # 1小时
                 connection_timeout: float = 30,
                 validation_query: str = "SELECT 1"):
        
        self._database_path = database_path
        self._min_connections = min_connections
        self._max_connections = max_connections
        self._max_idle_time = max_idle_time
        self._max_lifetime = max_lifetime
        self._connection_timeout = connection_timeout
        self._validation_query = validation_query
        
        self._pool: Queue[PooledConnection] = Queue(maxsize=max_connections)
        self._all_connections: Dict[str, PooledConnection] = {}
        self._lock = threading.RLock()
        self._connection_counter = 0
        self._closed = False
        
        # 统计信息
        self._stats = {
            'created_connections': 0,
            'destroyed_connections': 0,
            'get_requests': 0,
            'get_timeouts': 0,
            'validation_failures': 0,
            'pool_hits': 0,
            'pool_misses': 0
        }
        
        # 初始化最小连接数
        self._initialize_pool()
        
        # 启动清理线程
        self._cleanup_thread = threading.Thread(target=self._cleanup_worker, daemon=True)
        self._cleanup_thread.start()
    
    def get_connection(self, timeout: Optional[float] = None) -> PooledConnection:
        """获取连接"""
        if self._closed:
            raise ConnectionPoolException("连接池已关闭")
        
        with self._lock:
            self._stats['get_requests'] += 1
        
        timeout = timeout or self._connection_timeout
        start_time = time.time()
        
        while time.time() - start_time < timeout:
            try:
                # 尝试从池中获取连接
                connection = self._pool.get_nowait()
                
                # 验证连接
                if self._validate_connection(connection):
                    connection.mark_in_use()
                    with self._lock:
                        self._stats['pool_hits'] += 1
                    logger.debug(f"从池中获取连接: {connection.connection_id}")
                    return connection
                else:
                    # 连接无效，销毁并继续
                    self._destroy_connection(connection)
                    continue
                    
            except Empty:
                # 池中没有可用连接，尝试创建新连接
                if len(self._all_connections) < self._max_connections:
                    connection = self._create_connection()
                    if connection:
                        connection.mark_in_use()
                        with self._lock:
                            self._stats['pool_misses'] += 1
                        logger.debug(f"创建新连接: {connection.connection_id}")
                        return connection
                
                # 等待一小段时间后重试
                time.sleep(0.01)
        
        # 超时
        with self._lock:
            self._stats['get_timeouts'] += 1
        raise ConnectionPoolException(f"获取连接超时: {timeout}秒")
    
    def return_connection(self, connection: PooledConnection):
        """归还连接"""
        if not connection or connection.connection_id not in self._all_connections:
            logger.warning("尝试归还无效连接")
            return
        
        connection.mark_not_in_use()
        
        # 检查连接是否仍然有效
        if (connection.is_valid and 
            connection.age() < self._max_lifetime and
            self._validate_connection(connection)):
            
            try:
                self._pool.put_nowait(connection)
                logger.debug(f"连接已归还到池: {connection.connection_id}")
            except Full:
                # 池已满，销毁连接
                self._destroy_connection(connection)
        else:
            # 连接无效或过期，销毁
            self._destroy_connection(connection)
    
    def close_all(self):
        """关闭所有连接"""
        with self._lock:
            self._closed = True
            
            # 清空池
            while not self._pool.empty():
                try:
                    connection = self._pool.get_nowait()
                    self._destroy_connection(connection)
                except Empty:
                    break
            
            # 关闭所有连接
            for connection in list(self._all_connections.values()):
                self._destroy_connection(connection)
            
            logger.info("连接池已关闭")
    
    def get_stats(self) -> Dict[str, Any]:
        """获取连接池统计"""
        with self._lock:
            return {
                'database_path': self._database_path,
                'min_connections': self._min_connections,
                'max_connections': self._max_connections,
                'current_connections': len(self._all_connections),
                'available_connections': self._pool.qsize(),
                'in_use_connections': len([c for c in self._all_connections.values() if c.in_use]),
                'closed': self._closed,
                'stats': self._stats.copy(),
                'connection_details': [
                    {
                        'id': conn.connection_id,
                        'age': conn.age(),
                        'idle_time': conn.idle_time(),
                        'use_count': conn.use_count,
                        'in_use': conn.in_use,
                        'is_valid': conn.is_valid
                    }
                    for conn in self._all_connections.values()
                ]
            }
    
    @contextmanager
    def connection(self, timeout: Optional[float] = None):
        """连接上下文管理器"""
        conn = None
        try:
            conn = self.get_connection(timeout)
            yield conn.connection
        finally:
            if conn:
                self.return_connection(conn)
    
    def _initialize_pool(self):
        """初始化连接池"""
        for _ in range(self._min_connections):
            connection = self._create_connection()
            if connection:
                try:
                    self._pool.put_nowait(connection)
                except Full:
                    self._destroy_connection(connection)
                    break
        
        logger.info(f"连接池初始化完成: {self._pool.qsize()} 个连接")
    
    def _create_connection(self) -> Optional[PooledConnection]:
        """创建新连接"""
        try:
            with self._lock:
                self._connection_counter += 1
                connection_id = f"sqlite_{self._connection_counter}"
            
            # 创建SQLite连接
            raw_connection = sqlite3.connect(
                self._database_path,
                check_same_thread=False,
                timeout=30
            )
            
            # 设置连接参数
            raw_connection.execute("PRAGMA journal_mode=WAL")
            raw_connection.execute("PRAGMA synchronous=NORMAL")
            raw_connection.execute("PRAGMA cache_size=10000")
            raw_connection.execute("PRAGMA temp_store=MEMORY")
            
            pooled_connection = PooledConnection(raw_connection, self, connection_id)
            
            with self._lock:
                self._all_connections[connection_id] = pooled_connection
                self._stats['created_connections'] += 1
            
            logger.debug(f"创建新连接: {connection_id}")
            return pooled_connection
            
        except Exception as e:
            logger.error(f"创建连接失败: {e}")
            return None
    
    def _destroy_connection(self, connection: PooledConnection):
        """销毁连接"""
        try:
            connection.close()
            
            with self._lock:
                if connection.connection_id in self._all_connections:
                    del self._all_connections[connection.connection_id]
                self._stats['destroyed_connections'] += 1
            
            logger.debug(f"销毁连接: {connection.connection_id}")
            
        except Exception as e:
            logger.error(f"销毁连接失败: {connection.connection_id}, 错误: {e}")
    
    def _validate_connection(self, connection: PooledConnection) -> bool:
        """验证连接"""
        if not connection.is_valid:
            return False
        
        try:
            cursor = connection.connection.cursor()
            cursor.execute(self._validation_query)
            cursor.fetchone()
            cursor.close()
            return True
            
        except Exception as e:
            logger.debug(f"连接验证失败: {connection.connection_id}, 错误: {e}")
            with self._lock:
                self._stats['validation_failures'] += 1
            connection.invalidate()
            return False
    
    def _cleanup_worker(self):
        """清理工作线程"""
        while not self._closed:
            try:
                time.sleep(60)  # 每分钟清理一次
                self._cleanup_idle_connections()
                self._ensure_min_connections()
            except Exception as e:
                logger.error(f"连接池清理失败: {e}")
    
    def _cleanup_idle_connections(self):
        """清理空闲连接"""
        with self._lock:
            connections_to_remove = []
            
            for connection in self._all_connections.values():
                if (not connection.in_use and 
                    (connection.idle_time() > self._max_idle_time or 
                     connection.age() > self._max_lifetime)):
                    connections_to_remove.append(connection)
            
            # 确保不会低于最小连接数
            active_connections = len(self._all_connections) - len(connections_to_remove)
            if active_connections < self._min_connections:
                # 保留一些连接
                keep_count = self._min_connections - active_connections
                connections_to_remove = connections_to_remove[keep_count:]
            
            for connection in connections_to_remove:
                # 从池中移除（如果存在）
                try:
                    temp_queue = Queue()
                    while not self._pool.empty():
                        pooled_conn = self._pool.get_nowait()
                        if pooled_conn.connection_id != connection.connection_id:
                            temp_queue.put(pooled_conn)
                    
                    # 重新放回池中
                    while not temp_queue.empty():
                        self._pool.put_nowait(temp_queue.get_nowait())
                        
                except:
                    pass
                
                self._destroy_connection(connection)
            
            if connections_to_remove:
                logger.debug(f"清理了 {len(connections_to_remove)} 个空闲连接")
    
    def _ensure_min_connections(self):
        """确保最小连接数"""
        with self._lock:
            current_count = len(self._all_connections)
            
            if current_count < self._min_connections:
                needed = self._min_connections - current_count
                
                for _ in range(needed):
                    connection = self._create_connection()
                    if connection:
                        try:
                            self._pool.put_nowait(connection)
                        except Full:
                            break
                
                logger.debug(f"补充连接到最小数量: {self._min_connections}")


class ConnectionPoolManager:
    """连接池管理器"""
    
    def __init__(self):
        self._pools: Dict[str, ConnectionPool] = {}
        self._lock = threading.RLock()
    
    def create_pool(self, name: str, pool_type: str, **kwargs) -> ConnectionPool:
        """创建连接池"""
        with self._lock:
            if name in self._pools:
                raise ConnectionPoolException(f"连接池已存在: {name}")
            
            if pool_type == "sqlite":
                pool = SQLiteConnectionPool(**kwargs)
            else:
                raise ConnectionPoolException(f"不支持的连接池类型: {pool_type}")
            
            self._pools[name] = pool
            logger.info(f"创建连接池: {name}, 类型: {pool_type}")
            return pool
    
    def get_pool(self, name: str) -> Optional[ConnectionPool]:
        """获取连接池"""
        return self._pools.get(name)
    
    def close_pool(self, name: str) -> bool:
        """关闭连接池"""
        with self._lock:
            pool = self._pools.get(name)
            if pool:
                pool.close_all()
                del self._pools[name]
                logger.info(f"关闭连接池: {name}")
                return True
            return False
    
    def close_all_pools(self):
        """关闭所有连接池"""
        with self._lock:
            for name, pool in list(self._pools.items()):
                pool.close_all()
            self._pools.clear()
            logger.info("关闭所有连接池")
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有连接池统计"""
        stats = {}
        for name, pool in self._pools.items():
            stats[name] = pool.get_stats()
        return stats


# 全局连接池管理器
_global_pool_manager = ConnectionPoolManager()


def get_pool_manager() -> ConnectionPoolManager:
    """获取全局连接池管理器"""
    return _global_pool_manager


def create_sqlite_pool(name: str, database_path: str, **kwargs) -> SQLiteConnectionPool:
    """创建SQLite连接池"""
    return _global_pool_manager.create_pool(name, "sqlite", database_path=database_path, **kwargs)


def get_sqlite_pool(name: str) -> Optional[SQLiteConnectionPool]:
    """获取SQLite连接池"""
    return _global_pool_manager.get_pool(name)


@contextmanager
def pooled_connection(pool_name: str, timeout: Optional[float] = None):
    """池化连接上下文管理器"""
    pool = get_pool_manager().get_pool(pool_name)
    if not pool:
        raise ConnectionPoolException(f"连接池不存在: {pool_name}")
    
    with pool.connection(timeout) as conn:
        yield conn
