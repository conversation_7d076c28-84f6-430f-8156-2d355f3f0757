#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件分发器实现

负责将事件分发给相应的处理器，支持同步和异步分发
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Type
from datetime import datetime
from concurrent.futures import ThreadPoolExecutor, as_completed
import threading

from ...shared.patterns.architectural_patterns import DomainEvent
from .event_bus import EventBus, get_event_bus
from .event_store import EventStore, get_event_store
from .event_handlers import EventHandler, EventHandlerRegistry, get_handler_registry
from ...domain.exceptions import DomainException


logger = logging.getLogger(__name__)


class EventDispatcherException(DomainException):
    """事件分发器异常"""
    pass


class EventDispatcher(ABC):
    """事件分发器抽象基类"""
    
    @abstractmethod
    def dispatch(self, event: DomainEvent):
        """分发单个事件"""
        pass
    
    @abstractmethod
    def dispatch_batch(self, events: List[DomainEvent]):
        """批量分发事件"""
        pass
    
    @abstractmethod
    def start(self):
        """启动分发器"""
        pass
    
    @abstractmethod
    def stop(self):
        """停止分发器"""
        pass


class SyncEventDispatcher(EventDispatcher):
    """同步事件分发器"""
    
    def __init__(self, 
                 event_bus: Optional[EventBus] = None,
                 event_store: Optional[EventStore] = None,
                 handler_registry: Optional[EventHandlerRegistry] = None,
                 max_workers: int = 4):
        self._event_bus = event_bus or get_event_bus()
        self._event_store = event_store or get_event_store()
        self._handler_registry = handler_registry or get_handler_registry()
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._is_running = False
        self._lock = threading.RLock()
        
        # 分发统计
        self._stats = {
            'events_dispatched': 0,
            'events_stored': 0,
            'events_failed': 0,
            'handlers_executed': 0,
            'handlers_failed': 0
        }
        
        # 设置事件总线订阅
        self._setup_event_bus_subscriptions()
    
    def dispatch(self, event: DomainEvent):
        """分发单个事件"""
        if not self._is_running:
            raise EventDispatcherException("事件分发器未启动")
        
        try:
            with self._lock:
                self._stats['events_dispatched'] += 1
            
            logger.debug(f"开始分发事件: {event.event_type} - {event.event_id}")
            
            # 1. 存储事件
            self._store_event(event)
            
            # 2. 获取处理器
            handlers = self._get_event_handlers(event)
            
            # 3. 执行处理器
            if handlers:
                self._execute_handlers(event, handlers)
            else:
                logger.debug(f"没有找到事件处理器: {event.event_type}")
            
            # 4. 发布到事件总线
            self._publish_to_bus(event)
            
            logger.debug(f"事件分发完成: {event.event_type} - {event.event_id}")
            
        except Exception as e:
            with self._lock:
                self._stats['events_failed'] += 1
            logger.error(f"事件分发失败: {event.event_type} - {event.event_id}, 错误: {e}")
            raise EventDispatcherException(f"事件分发失败: {e}") from e
    
    def dispatch_batch(self, events: List[DomainEvent]):
        """批量分发事件"""
        if not events:
            return
        
        logger.info(f"开始批量分发事件: {len(events)} 个")
        
        # 使用线程池并行处理
        futures = []
        for event in events:
            future = self._executor.submit(self._dispatch_single_event, event)
            futures.append(future)
        
        # 等待所有事件处理完成
        failed_count = 0
        for future in as_completed(futures):
            try:
                future.result()
            except Exception as e:
                failed_count += 1
                logger.error(f"批量分发中的事件失败: {e}")
        
        if failed_count > 0:
            logger.warning(f"批量分发完成，{failed_count} 个事件失败")
        else:
            logger.info("批量分发完成，所有事件成功")
    
    def start(self):
        """启动分发器"""
        with self._lock:
            if self._is_running:
                logger.warning("事件分发器已经启动")
                return
            
            self._is_running = True
            logger.info("事件分发器启动成功")
    
    def stop(self):
        """停止分发器"""
        with self._lock:
            if not self._is_running:
                logger.warning("事件分发器未启动")
                return
            
            self._is_running = False
            
            # 关闭线程池
            self._executor.shutdown(wait=True)
            
            logger.info("事件分发器停止成功")
    
    def _setup_event_bus_subscriptions(self):
        """设置事件总线订阅"""
        # 这里可以设置一些全局的事件总线订阅
        pass
    
    def _store_event(self, event: DomainEvent):
        """存储事件"""
        try:
            sequence_number = self._event_store.append_event(event)
            with self._lock:
                self._stats['events_stored'] += 1
            logger.debug(f"事件存储成功: {event.event_id}, 序列号: {sequence_number}")
        except Exception as e:
            logger.error(f"事件存储失败: {event.event_id}, 错误: {e}")
            # 存储失败不应该阻止事件处理
    
    def _get_event_handlers(self, event: DomainEvent) -> List[EventHandler]:
        """获取事件处理器"""
        return self._handler_registry.get_handlers(type(event))
    
    def _execute_handlers(self, event: DomainEvent, handlers: List[EventHandler]):
        """执行事件处理器"""
        for handler in handlers:
            try:
                if handler.can_handle(event):
                    handler.handle(event)
                    with self._lock:
                        self._stats['handlers_executed'] += 1
                    logger.debug(f"处理器执行成功: {handler.handler_name}")
                else:
                    logger.debug(f"处理器无法处理事件: {handler.handler_name}")
            except Exception as e:
                with self._lock:
                    self._stats['handlers_failed'] += 1
                logger.error(f"处理器执行失败: {handler.handler_name}, 错误: {e}")
                # 继续执行其他处理器
    
    def _publish_to_bus(self, event: DomainEvent):
        """发布到事件总线"""
        try:
            self._event_bus.publish(event)
            logger.debug(f"事件发布到总线成功: {event.event_id}")
        except Exception as e:
            logger.error(f"事件发布到总线失败: {event.event_id}, 错误: {e}")
            # 发布失败不应该阻止事件处理
    
    def _dispatch_single_event(self, event: DomainEvent):
        """分发单个事件（用于线程池）"""
        try:
            self.dispatch(event)
        except Exception as e:
            logger.error(f"线程池中事件分发失败: {event.event_id}, 错误: {e}")
            raise
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取分发统计"""
        with self._lock:
            return {
                **self._stats,
                'is_running': self._is_running,
                'timestamp': datetime.now().isoformat()
            }


class AsyncEventDispatcher(EventDispatcher):
    """异步事件分发器"""
    
    def __init__(self,
                 event_bus: Optional[EventBus] = None,
                 event_store: Optional[EventStore] = None,
                 handler_registry: Optional[EventHandlerRegistry] = None,
                 max_concurrent: int = 10):
        self._event_bus = event_bus or get_event_bus()
        self._event_store = event_store or get_event_store()
        self._handler_registry = handler_registry or get_handler_registry()
        self._semaphore = asyncio.Semaphore(max_concurrent)
        self._is_running = False
        
        # 分发统计
        self._stats = {
            'events_dispatched': 0,
            'events_stored': 0,
            'events_failed': 0,
            'handlers_executed': 0,
            'handlers_failed': 0
        }
    
    async def dispatch_async(self, event: DomainEvent):
        """异步分发单个事件"""
        if not self._is_running:
            raise EventDispatcherException("异步事件分发器未启动")
        
        async with self._semaphore:
            try:
                self._stats['events_dispatched'] += 1
                
                logger.debug(f"开始异步分发事件: {event.event_type} - {event.event_id}")
                
                # 1. 存储事件
                await self._store_event_async(event)
                
                # 2. 获取处理器
                handlers = self._get_event_handlers(event)
                
                # 3. 并发执行处理器
                if handlers:
                    await self._execute_handlers_async(event, handlers)
                else:
                    logger.debug(f"没有找到事件处理器: {event.event_type}")
                
                # 4. 发布到事件总线
                await self._publish_to_bus_async(event)
                
                logger.debug(f"异步事件分发完成: {event.event_type} - {event.event_id}")
                
            except Exception as e:
                self._stats['events_failed'] += 1
                logger.error(f"异步事件分发失败: {event.event_type} - {event.event_id}, 错误: {e}")
                raise EventDispatcherException(f"异步事件分发失败: {e}") from e
    
    def dispatch(self, event: DomainEvent):
        """同步分发事件（在异步分发器中创建任务）"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(self.dispatch_async(event))
        else:
            loop.run_until_complete(self.dispatch_async(event))
    
    async def dispatch_batch_async(self, events: List[DomainEvent]):
        """异步批量分发事件"""
        if not events:
            return
        
        logger.info(f"开始异步批量分发事件: {len(events)} 个")
        
        # 并发处理所有事件
        tasks = [self.dispatch_async(event) for event in events]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计失败的事件
        failed_count = sum(1 for result in results if isinstance(result, Exception))
        
        if failed_count > 0:
            logger.warning(f"异步批量分发完成，{failed_count} 个事件失败")
        else:
            logger.info("异步批量分发完成，所有事件成功")
    
    def dispatch_batch(self, events: List[DomainEvent]):
        """同步批量分发事件"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(self.dispatch_batch_async(events))
        else:
            loop.run_until_complete(self.dispatch_batch_async(events))
    
    async def start_async(self):
        """异步启动分发器"""
        if self._is_running:
            logger.warning("异步事件分发器已经启动")
            return
        
        self._is_running = True
        logger.info("异步事件分发器启动成功")
    
    def start(self):
        """启动分发器"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(self.start_async())
        else:
            loop.run_until_complete(self.start_async())
    
    async def stop_async(self):
        """异步停止分发器"""
        if not self._is_running:
            logger.warning("异步事件分发器未启动")
            return
        
        self._is_running = False
        logger.info("异步事件分发器停止成功")
    
    def stop(self):
        """停止分发器"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(self.stop_async())
        else:
            loop.run_until_complete(self.stop_async())
    
    async def _store_event_async(self, event: DomainEvent):
        """异步存储事件"""
        try:
            # 在线程池中执行同步存储操作
            loop = asyncio.get_event_loop()
            sequence_number = await loop.run_in_executor(
                None, self._event_store.append_event, event
            )
            
            self._stats['events_stored'] += 1
            logger.debug(f"异步事件存储成功: {event.event_id}, 序列号: {sequence_number}")
        except Exception as e:
            logger.error(f"异步事件存储失败: {event.event_id}, 错误: {e}")
    
    def _get_event_handlers(self, event: DomainEvent) -> List[EventHandler]:
        """获取事件处理器"""
        return self._handler_registry.get_handlers(type(event))
    
    async def _execute_handlers_async(self, event: DomainEvent, handlers: List[EventHandler]):
        """异步执行事件处理器"""
        tasks = []
        
        for handler in handlers:
            if handler.can_handle(event):
                task = self._execute_single_handler_async(handler, event)
                tasks.append(task)
        
        if tasks:
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            # 统计执行结果
            for result in results:
                if isinstance(result, Exception):
                    self._stats['handlers_failed'] += 1
                else:
                    self._stats['handlers_executed'] += 1
    
    async def _execute_single_handler_async(self, handler: EventHandler, event: DomainEvent):
        """异步执行单个处理器"""
        try:
            # 检查处理器是否支持异步
            if hasattr(handler, 'handle_async') and callable(getattr(handler, 'handle_async')):
                await handler.handle_async(event)
            else:
                # 在线程池中执行同步处理器
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, handler.handle, event)
            
            logger.debug(f"异步处理器执行成功: {handler.handler_name}")
            
        except Exception as e:
            logger.error(f"异步处理器执行失败: {handler.handler_name}, 错误: {e}")
            raise
    
    async def _publish_to_bus_async(self, event: DomainEvent):
        """异步发布到事件总线"""
        try:
            # 检查事件总线是否支持异步
            if hasattr(self._event_bus, 'publish_async') and callable(getattr(self._event_bus, 'publish_async')):
                await self._event_bus.publish_async(event)
            else:
                # 在线程池中执行同步发布
                loop = asyncio.get_event_loop()
                await loop.run_in_executor(None, self._event_bus.publish, event)
            
            logger.debug(f"异步事件发布到总线成功: {event.event_id}")
        except Exception as e:
            logger.error(f"异步事件发布到总线失败: {event.event_id}, 错误: {e}")
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取分发统计"""
        return {
            **self._stats,
            'is_running': self._is_running,
            'timestamp': datetime.now().isoformat()
        }


# 事件分发器工厂
def create_event_dispatcher(dispatcher_type: str = "sync", **kwargs) -> EventDispatcher:
    """创建事件分发器"""
    if dispatcher_type == "sync":
        return SyncEventDispatcher(**kwargs)
    elif dispatcher_type == "async":
        return AsyncEventDispatcher(**kwargs)
    else:
        raise ValueError(f"不支持的事件分发器类型: {dispatcher_type}")


# 全局事件分发器实例
_global_event_dispatcher: Optional[EventDispatcher] = None


def get_event_dispatcher() -> EventDispatcher:
    """获取全局事件分发器"""
    global _global_event_dispatcher
    if _global_event_dispatcher is None:
        _global_event_dispatcher = create_event_dispatcher("sync")
        _global_event_dispatcher.start()
    return _global_event_dispatcher


def set_event_dispatcher(event_dispatcher: EventDispatcher):
    """设置全局事件分发器"""
    global _global_event_dispatcher
    if _global_event_dispatcher and _global_event_dispatcher._is_running:
        _global_event_dispatcher.stop()
    _global_event_dispatcher = event_dispatcher
