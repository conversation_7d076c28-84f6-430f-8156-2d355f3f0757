#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源配置实体

包含数据源相关的配置信息
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional, List
from ..value_objects.tdx_connection import TdxConnection


@dataclass
class DataSourceConfig:
    """数据源配置实体"""
    
    # TDX连接配置
    tdx_connection: TdxConnection
    
    # 网络配置
    network_config: Dict[str, Any]
    
    # 错误处理配置
    error_handling: Dict[str, Any]
    
    # 监控配置
    monitoring: Dict[str, Any]
    
    # 互联网数据源配置
    internet_sources: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        """初始化后处理"""
        # 设置默认网络配置
        if not self.network_config:
            self.network_config = {
                'connection_timeout': 30,
                'read_timeout': 60,
                'max_connections': 10,
                'keep_alive': True,
                'user_agent': 'MythQuant/1.0'
            }
        
        # 设置默认错误处理配置
        if not self.error_handling:
            self.error_handling = {
                'log_all_errors': True,
                'raise_on_critical': True,
                'continue_on_partial_failure': True,
                'error_notification': False
            }
        
        # 设置默认监控配置
        if not self.monitoring:
            self.monitoring = {
                'track_api_usage': True,
                'log_request_details': False,
                'performance_metrics': True,
                'success_rate_threshold': 0.8
            }
        
        # 设置默认互联网数据源配置
        if not self.internet_sources:
            self.internet_sources = {
                'enabled': True,
                'primary_source': 'default',
                'fallback_sources': [],
                'cache_enabled': True,
                'cache_ttl': 3600
            }
    
    def validate(self) -> bool:
        """验证配置的业务规则"""
        try:
            # 验证TDX连接
            if not self.tdx_connection.is_valid():
                return False
            
            # 验证网络配置
            if not self._validate_network_config():
                return False
            
            # 验证错误处理配置
            if not self._validate_error_handling_config():
                return False
            
            # 验证监控配置
            if not self._validate_monitoring_config():
                return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_network_config(self) -> bool:
        """验证网络配置"""
        required_keys = ['connection_timeout', 'read_timeout', 'max_connections']
        if not all(key in self.network_config for key in required_keys):
            return False
        
        # 验证超时时间
        if self.network_config['connection_timeout'] <= 0:
            return False
        if self.network_config['read_timeout'] <= 0:
            return False
        
        # 验证连接数
        if self.network_config['max_connections'] <= 0:
            return False
        
        return True
    
    def _validate_error_handling_config(self) -> bool:
        """验证错误处理配置"""
        required_keys = ['log_all_errors', 'raise_on_critical']
        return all(key in self.error_handling for key in required_keys)
    
    def _validate_monitoring_config(self) -> bool:
        """验证监控配置"""
        if 'success_rate_threshold' in self.monitoring:
            threshold = self.monitoring['success_rate_threshold']
            if not (0.0 <= threshold <= 1.0):
                return False
        
        return True
    
    def get_connection_timeout(self) -> int:
        """获取连接超时时间"""
        return self.network_config.get('connection_timeout', 30)
    
    def get_read_timeout(self) -> int:
        """获取读取超时时间"""
        return self.network_config.get('read_timeout', 60)
    
    def get_max_connections(self) -> int:
        """获取最大连接数"""
        return self.network_config.get('max_connections', 10)
    
    def is_error_logging_enabled(self) -> bool:
        """是否启用错误日志"""
        return self.error_handling.get('log_all_errors', True)
    
    def should_raise_on_critical(self) -> bool:
        """关键错误时是否抛出异常"""
        return self.error_handling.get('raise_on_critical', True)
    
    def should_continue_on_partial_failure(self) -> bool:
        """部分失败时是否继续"""
        return self.error_handling.get('continue_on_partial_failure', True)
    
    def is_performance_monitoring_enabled(self) -> bool:
        """是否启用性能监控"""
        return self.monitoring.get('performance_metrics', True)
    
    def get_success_rate_threshold(self) -> float:
        """获取成功率阈值"""
        return self.monitoring.get('success_rate_threshold', 0.8)
    
    def is_internet_source_enabled(self) -> bool:
        """是否启用互联网数据源"""
        if not self.internet_sources:
            return False
        return self.internet_sources.get('enabled', True)
    
    def __str__(self) -> str:
        return f"DataSourceConfig(tdx={self.tdx_connection})"
