#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDD架构 - 分块处理器

提供数据分块处理功能
"""

from typing import List, Any, Iterator, Callable, Optional
import pandas as pd
import numpy as np
from src.mythquant.shared.logging import get_smart_logger


class ChunkProcessor:
    """分块处理器"""
    
    def __init__(self, chunk_size: int = 1000):
        self.chunk_size = chunk_size
        self.logger = get_smart_logger()
    
    def process_dataframe_chunks(self, 
                                df: pd.DataFrame, 
                                processor_func: Callable[[pd.DataFrame], pd.DataFrame],
                                chunk_size: Optional[int] = None) -> pd.DataFrame:
        """分块处理DataFrame"""
        if chunk_size is None:
            chunk_size = self.chunk_size
        
        if len(df) <= chunk_size:
            return processor_func(df)
        
        results = []
        total_chunks = (len(df) + chunk_size - 1) // chunk_size
        
        self.logger.info(f"开始分块处理，总行数: {len(df)}, 分块大小: {chunk_size}, 总块数: {total_chunks}")
        
        for i, chunk in enumerate(self._chunk_dataframe(df, chunk_size)):
            try:
                processed_chunk = processor_func(chunk)
                results.append(processed_chunk)
                
                if (i + 1) % 10 == 0:
                    self.logger.info(f"已处理 {i + 1}/{total_chunks} 个数据块")
                    
            except Exception as e:
                self.logger.error(f"处理第 {i + 1} 个数据块时出错: {e}")
                raise
        
        result_df = pd.concat(results, ignore_index=True)
        self.logger.info(f"分块处理完成，结果行数: {len(result_df)}")
        
        return result_df
    
    def process_list_chunks(self, 
                           data_list: List[Any], 
                           processor_func: Callable[[List[Any]], List[Any]],
                           chunk_size: Optional[int] = None) -> List[Any]:
        """分块处理列表"""
        if chunk_size is None:
            chunk_size = self.chunk_size
        
        if len(data_list) <= chunk_size:
            return processor_func(data_list)
        
        results = []
        total_chunks = (len(data_list) + chunk_size - 1) // chunk_size
        
        self.logger.info(f"开始分块处理列表，总长度: {len(data_list)}, 分块大小: {chunk_size}, 总块数: {total_chunks}")
        
        for i, chunk in enumerate(self._chunk_list(data_list, chunk_size)):
            try:
                processed_chunk = processor_func(chunk)
                results.extend(processed_chunk)
                
                if (i + 1) % 10 == 0:
                    self.logger.info(f"已处理 {i + 1}/{total_chunks} 个数据块")
                    
            except Exception as e:
                self.logger.error(f"处理第 {i + 1} 个数据块时出错: {e}")
                raise
        
        self.logger.info(f"分块处理完成，结果长度: {len(results)}")
        return results
    
    def _chunk_dataframe(self, df: pd.DataFrame, chunk_size: int) -> Iterator[pd.DataFrame]:
        """将DataFrame分块"""
        for i in range(0, len(df), chunk_size):
            yield df.iloc[i:i + chunk_size]
    
    def _chunk_list(self, data_list: List[Any], chunk_size: int) -> Iterator[List[Any]]:
        """将列表分块"""
        for i in range(0, len(data_list), chunk_size):
            yield data_list[i:i + chunk_size]
    
    def estimate_optimal_chunk_size(self, data_size: int, memory_limit_mb: int = 100) -> int:
        """估算最优分块大小"""
        # 简单的启发式算法
        estimated_row_size = 1024  # 假设每行1KB
        max_rows = (memory_limit_mb * 1024 * 1024) // estimated_row_size
        
        optimal_size = min(max_rows, max(100, data_size // 10))
        
        self.logger.debug(f"估算最优分块大小: {optimal_size} (数据大小: {data_size}, 内存限制: {memory_limit_mb}MB)")

        return optimal_size

    def cleanup(self):
        """清理资源"""
        self.logger.info("ChunkProcessor资源清理完成")
