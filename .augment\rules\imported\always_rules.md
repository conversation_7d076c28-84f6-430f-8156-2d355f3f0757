---
type: "always_apply"
---

# MythQuant项目始终遵循规则

## 吹哨人原则
- 如果我提的问题、要求你觉得有不合理的地方，你可以对我提的问题进行优化修改并且通过交互与我确认，征求我的同意后即可使用你优化后的问题重新提问了

## 金融计算精度要求
- 所有价格计算必须使用 `decimal.Decimal` 类型 (2025-07-23)
- 价格比较使用容差比较：`abs(价格1 - 价格2) < Decimal('0.001')` (2025-07-23)
- 统一精度标准：价格保留3位小数，比率保留6位小数 (2025-07-23)
- 避免浮点数累积误差，特别是在循环计算中 (2025-07-23)

## 代码质量标准
- 数据输入必须进行验证：检查None值、空值、数据类型 (2025-07-23)
- 优先使用pandas向量化操作而非循环 (2025-07-23)
- 实现多级缓存：内存缓存、磁盘缓存、数据库缓存 (2025-07-23)
- 大数据处理使用分块处理策略 (2025-07-23)

## 架构设计原则
- 采用分层架构：数据层、业务层、展示层 (2025-07-24)
- 实现降级处理机制：主方法失败时有备用方案 (2025-07-24)
- 保持高内聚低耦合：模块职责单一，接口清晰 (2025-07-24)
- 使用安全默认值：配置项都有合理默认值 (2025-07-24)

## DDD架构实施规则 (2025-08-06)
- **分层架构严格遵循**：领域层(Domain) → 应用层(Application) → 基础设施层(Infrastructure) → 接口层(Interface) (2025-08-06)
- **依赖倒置原则**：领域层不依赖任何外部框架，通过接口定义依赖关系 (2025-08-06)
- **向后兼容性保证**：新架构必须完全兼容现有代码，用户无需修改使用方式 (2025-08-06)
- **配置管理统一**：通过DDD架构统一管理配置，但保持user_config.py的用户友好性 (2025-08-06)
- **门面模式应用**：使用门面模式隐藏DDD架构复杂性，提供简化的外部接口 (2025-08-06)

## 主程序架构规则
- **统一入口**：使用 `main.py` 作为唯一主程序入口，采用模块化架构设计 (2025-07-26)
- **模块化设计**：核心逻辑分离到专门模块，主程序专注于启动和流程控制 (2025-07-26)
- **功能完整性**：确保所有核心功能在模块化架构中得到完整实现 (2025-07-26)
- **向后兼容**：保留 `main_v20230219_optimized.py` 作为参考，但不再维护双入口 (2025-07-26)

## 模块化架构规则
- **核心模块分离** (2025-07-24)：
  - `core/application.py` - 应用程序主控制器，负责组件协调和流程控制
  - `core/task_manager.py` - 任务管理和执行，负责任务调度和监控
  - `core/stock_processor.py` - 股票数据处理封装，负责数据处理逻辑
  - `core/config_manager.py` - 统一配置管理，负责配置访问和验证
- **模块职责边界**：每个模块只负责特定领域的功能，避免职责重叠 (2025-07-24)
- **接口稳定性**：模块间接口变更需要向后兼容，提供过渡期支持 (2025-07-24)
- **依赖注入**：使用依赖注入而非硬编码依赖关系，提高可测试性 (2025-07-24)

## 性能优化要求
- 数据类型优化：numpy数组、pandas DataFrame (2025-07-24)
- 智能缓存策略：LRU缓存、文件哈希验证 (2025-07-24)
- 向量化计算：避免Python循环，使用numpy/pandas操作 (2025-07-24)
- 异步IO处理：文件读写、网络请求使用异步模式 (2025-07-24)

## 性能监控和基准测试规则
- **基准建立**：使用 `tests/direct_performance_test.py` 建立性能基线，记录平均执行时间和资源使用 (2025-07-24)
- **回归测试**：每次优化后必须运行性能对比测试，确保性能不退化 (2025-07-24)
- **性能指标** (2025-07-24)：
  - 执行时间不得超过基线的110%
  - 内存使用不得超过1GB
  - 成功率必须保持100%
  - 性能稳定性：执行时间波动小于10%
- **监控报告**：自动生成性能报告到 `benchmark_results/` 目录，包含历史对比数据 (2025-07-24)
- **性能基线更新**：重大优化后可更新基线，但需要详细记录变更原因 (2025-07-24)

## 错误处理标准
- 所有外部数据源访问必须有异常处理 (2025-07-23)
- 记录详细的调试信息但不影响性能 (2025-07-23)
- 提供用户友好的错误信息 (2025-07-23)
- 实现优雅降级而非崩溃退出 (2025-07-23)

## 错误处理和回退规则
- **智能回退**：发现问题时自动提示使用"！！！"标记，帮助用户识别问题 (2025-07-23)
- **错误分类**：使用 `ErrorCategory` 枚举进行错误分类（SYSTEM, BUSINESS, DATA_ACCESS, FILE_IO, NETWORK） (2025-07-23)
- **错误报告**：自动生成详细错误报告到 `logs/error_report_*.json`，包含错误上下文和堆栈信息 (2025-07-23)
- **回退脚本**：提供一键回退功能 `scripts/rollback_to_original.py`，确保系统可恢复性 (2025-07-23)
- **现场保护**：修改前自动备份关键文件，使用时间戳标识备份版本 (2025-07-23)

## 测试完成标准
- **代码修改类任务**：每次代码改动后必须测试是否引入错误，程序变更完成的唯一标准是成功生成对应的前复权txt文档
- **纯测试类任务**：性能测试、功能验证等不修改核心代码的任务，重点关注测试结果和数据分析，无需重新生成txt文档
- **分析类任务**：代码分析、架构设计等任务，重点关注分析质量和设计合理性
- **混合类任务**：包含测试和优化的任务，分阶段执行：
  - 测试阶段：纯测试，关注数据收集
  - 优化阶段：如有代码修改，必须重新验证txt文档生成
- 必须实际生成文件如：`day_0_000617_20150101-20250731.txt`、`1min_0_000617_20250101-20250625.txt` 等才算是测试验证通过（结果导向原则）
- 只有真正生成出预期的输出文件才算**代码修改类任务**变更成功完成
- 如果你通过测试发现有问题请帮助智能回退到最近的无问题代码并再次测试确认无误后继续进行修复/执行现有指令的工作并用“！！！”来提示我刚才发生了什么

## 数据质量验证标准 (2025-08-10)
- **文件命名格式验证**：频率前缀_0_股票代码_时间范围_来源.txt（如1min_0_000617_...而不是1_0_000617_...）
- **A股1分钟数据格式标准**：时间戳表示分钟结束时间，09:31是第一条数据（表示09:30-09:31），13:01是中午第一条数据（表示13:00-13:01） (2025-08-10)
- **数据完整性简化标准**：每个交易日240行数据即为完整，不需要检查具体时间点，避免过度复杂化 (2025-08-10)
- **前复权价格验证**：前复权收盘价与当日收盘价必须有合理差异，不能完全相同
- **数据完整性验证**：包含所有必要字段且格式正确，字段分隔符为管道符"|"
- **数据抽样检查**：必须随机检查生成文件的前10行数据质量，验证数据准确性
- **数据质量验证失败**：即使文件生成成功，如果数据质量不合格也算任务失败

## 功能等价性验证标准
- **文件生成验证**：必须生成预期的txt文档（如 `day_0_000617_20150101-20250731.txt`） (2025-07-23)
- **文件格式验证**：包含正确的表头格式 `股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖` (2025-07-23)
- **数据准确性验证**：前复权价格计算正确，关键数据点完全一致 (2025-07-23)
- **性能回归测试**：执行时间在合理范围内，不超过基线110% (2025-07-23)

## 保存现场原则
- 每次你执行完我的指令后针对程序整体改动正确与否的测试工作中，因涉及到与上一次程序生成的成果物（比如：`day_0_000617_20150101-20250731.txt`、`1min_0_000617_20250101-20250625.txt` ）的比较，因此你需要对上一次生成的成果物进行妥当的备份保存，以便你可以顺利的完成两者的比较任务，从而发现本次代码改动后暴露出的、影响结果正确性的问题 (2025-07-24)
- 你保存上一次程序成果物的方法可以是在成果物如*.txt后缀后面再次追加一个.bak，变成*.txt.bak (2025-07-24)
- 对于代码，也是建议你能够在本次修改代码之前，先做好备份还原点，以便于当发现问题无法基于已经修改后的代码进行改进时，可以基于备份代码，做到从容不迫、有章可循 (2025-07-24)

## 咨询顾问原则
- 如果你发现当前的rules有不合理、自相矛盾、不够专业、效率不高、架构设计本来可以更好等问题请随时提出来询问我是否需要加以改进 (2025-07-24)
- 不要盲目执行你认为不合理的rules，而是要主动提出来与我交互你认为具体是哪一条rules在什么情况下有不合理问题、怎么解决 (2025-07-24)
- 如果你发现我当前的提问不够明确、专业、不够符合逻辑上下文或者跳步、存在项目管理风险、影响系统，请你主动向我提出改进建议 (2025-07-24)
- 如果你在每次主动阅读代码过程中，发现一些不合理的问题时，请主动提出来你发现的问题、以及你的意见建议 (2025-07-24)
- 我对vue等前端界面化工作不熟悉，如果你觉得有必要增加前端代码、实现前端交互，请你主动提出意见建议 (2025-07-24)

## 定期更新原则
- 请你每次回答问题的最后都进行对本次回答问题过程中发现的新知识的总结沉淀和可能涉及到rules的修改更新操作，请你协助做好举一反三和文档的沉淀！
- 你需要在每次回答问题的同时检视并按需更新rules（包括增加、调优措辞、调优顺序、增加/重新为rules分类等） (2025-07-24)
- 每条rules请在尾部增加一个最后修改时间，如（2025-01-01），请使用真实日期，如果你不知道今天是哪天，可以问我。另外，对于未修改的条目，不用修改后面的时间戳。 (2025-07-24)
- 请在真正修改/填加rules之前，先告知我并得到我的认可再操作 (2025-07-24)

## 知识库管理原则 (2025-07-26)
- **知识库文件**：`testing_quality_rules.md`、`debugging_knowledge_base.md` 等知识库文件是项目重要资产 (2025-07-26)
- **主动引用**：遇到测试、调试、质量相关问题时，必须主动查看并引用相关知识库 (2025-07-26)
- **持续更新**：发现新的问题模式或解决方案时，及时更新知识库内容 (2025-07-26)
- **经验沉淀**：每次解决重要问题后，将经验和教训沉淀到知识库中 (2025-07-26)
- **规则应用**：知识库中的规则和经验必须在实际工作中得到应用和验证 (2025-07-26)

## FAQ知识库管理规则 (2025-07-26)
- **自动FAQ更新**：每次对话结束时，AI必须主动评估是否有重要问题需要添加到FAQ知识库 (2025-07-26)
- **FAQ检索优先**：遇到问题时，AI必须首先检索FAQ知识库，查看是否有相关解决方案 (2025-07-26)
- **结构化记录**：FAQ条目必须包含问题描述、解决方案、技术要点、修改文件、时间标签、领域标签 (2025-07-26)
- **会话摘要生成**：复杂对话结束后，自动生成会话摘要并提取关键问题添加到FAQ (2025-07-26)
- **问题分类管理**：使用问题分类系统按类型、复杂度、解决状态进行分类管理 (2025-07-26)
- **解决方案模板**：为常见问题类型建立标准化解决方案模板，提高解决效率 (2025-07-26)
- **知识图谱维护**：维护问题、解决方案、技术点之间的关联关系，支持智能推荐 (2025-07-26)
- **FAQ质量控制**：定期审核FAQ条目的准确性、完整性和时效性 (2025-07-26)
- **标签体系统一**：使用统一的标签体系进行分类，包括时间、领域、模块、技术栈标签 (2025-07-26)
- **检索关键词优化**：AI在回答问题前，必须使用相关关键词检索FAQ知识库 (2025-07-26)

## 日志方法使用规范 (2025-07-26)
- **日志器类型识别**：根据导入方式明确使用的日志器类型 (2025-07-26)
- **LoggingService规范**：使用log_info(), log_warning(), log_error(), log_debug()方法 (2025-07-26)
- **SmartLogger规范**：使用info(), warning(), error(), debug()方法 (2025-07-26)
- **避免方法混用**：不同日志器不能混用方法名，避免AttributeError (2025-07-26)
- **系统性修复原则**：发现日志方法错误时进行系统性检查和修复 (2025-07-26)
- **自动化修复工具**：使用专门的修复脚本确保修复的完整性和准确性 (2025-07-26)
- **修复后验证**：修复后必须进行功能验证，确保问题真正解决 (2025-07-26)
- **数据完整性保护**：异常处理逻辑必须保护数据完整性，避免数据损坏 (2025-07-26)

## 实证验证优先原则 (2025-07-26)
- **用户指定文件验证**：当用户指出具体文件问题时，必须直接读取该文件进行验证 (2025-07-26)
- **实际数据优于逻辑推理**：不能仅基于代码逻辑判断问题是否解决，必须检查实际输出 (2025-07-26)
- **端到端验证流程**：从数据生成到最终文件的完整验证链条 (2025-07-26)
- **用户反馈最可靠**：用户观察到的实际问题比AI的理论分析更准确 (2025-07-26)
- **直接文件检查**：对于数据格式、内容问题，必须直接查看用户指定的文件 (2025-07-26)

## 数据源限制管理规范 (2025-07-26)
- **数据源能力评估**：明确每个数据源的时间范围、数据类型、频率限制 (2025-07-26)
- **pytdx限制认知**：pytdx只提供最近100个交易日的分钟数据，这是硬约束 (2025-07-26)
- **多数据源策略**：建立数据源优先级和回退机制，应对单一数据源限制 (2025-07-26)
- **时间范围适配**：请求时间范围必须与数据源能力匹配，避免无效请求 (2025-07-26)
- **限制透明化**：向用户明确说明数据源限制，不要给出无法实现的承诺 (2025-07-26)

## 数据格式源头控制规范 (2025-07-26)
- **源头格式化**：在数据生成的最初阶段就确保格式正确，而非后期修复 (2025-07-26)
- **股票代码标准化**：所有股票代码生成点都使用`str(stock_code).zfill(6)`格式化 (2025-07-26)
- **格式验证嵌入**：在每个数据转换环节都进行格式验证和修正 (2025-07-26)
- **一致性保证**：确保所有数据生成路径都使用相同的格式化逻辑 (2025-07-26)
- **防御性编程**：假设输入格式可能不正确，主动进行格式化处理 (2025-07-26)

## 数据完整性检查规范 (2025-07-26)
- **处理后验证**：数据处理后必须进行完整性验证 (2025-07-26)
- **定期稽核**：使用data_integrity_auditor.py工具进行定期稽核 (2025-07-26)
- **备份机制**：重要数据修改前必须备份，提供回滚机制 (2025-07-26)
- **关键字段保护**：股票代码等关键字段的完整性必须得到保障 (2025-07-26)
- **K线数据验证**：验证K线数据量是否符合交易日×每日K线数的预期 (2025-07-26)
- **缺失数据补全**：建立自动检测和补全缺失分钟数据的机制 (2025-07-26)

## 系统性问题修复方法论 (2025-07-26)
- **问题模式识别**：从terminal错误中识别系统性问题模式，而非单点问题 (2025-07-26)
- **影响范围分析**：全面扫描相关文件，确定问题的完整影响范围 (2025-07-26)
- **自动化修复优先**：开发专门的修复脚本，避免手动修复的遗漏和错误 (2025-07-26)
- **类型识别机制**：建立自动识别机制，根据代码模式确定修复策略 (2025-07-26)
- **批量修复执行**：统一修复所有相关文件，确保修复的一致性 (2025-07-26)
- **修复验证机制**：修复后必须进行功能验证，确保问题真正解决 (2025-07-26)
- **备份和回滚**：重要修复前必须备份，提供安全的回滚机制 (2025-07-26)
- **经验沉淀**：将修复方法论和工具沉淀到知识库，避免重复问题 (2025-07-26)

## 反复问题预防规则 (2025-07-26)
- **问题模式识别**：建立常见问题的识别模式，防止同类问题反复出现 (2025-07-26)
- **根本原因修复**：不满足于表面修复，必须找到并解决问题的根本原因 (2025-07-26)
- **系统性预防机制**：建立预防机制而非被动修复，如格式验证、数据源限制检查 (2025-07-26)
- **用户反馈重视**：用户反复提及的问题说明修复不彻底，必须重新审视解决方案 (2025-07-26)
- **实际验证强制**：声称问题"已解决"前必须通过实际数据验证，不能基于理论推测 (2025-07-26)
- **知识沉淀机制**：每次解决反复问题后，必须将经验沉淀到知识库和规则中 (2025-07-26)

## AI调试环境强制规则 (2025-08-09)
- **自动测试环境切换**：AI调试代码时必须自动走测试环境，通过user_config.py中的detect_environment()函数检测 (2025-08-09)
- **调试模式识别**：通过文件名模式识别AI调试（tools/、test_、debug_、detector、tracker、precise_等） (2025-08-09)
- **测试文件强制使用**：在测试环境中必须使用test_前缀的标准测试文件，不得使用生产环境文件 (2025-08-09)
- **环境隔离保证**：确保AI调试不影响生产环境，所有调试操作限制在测试环境中 (2025-08-09)
- **配置自动适配**：智能文件选择器等组件必须根据环境配置自动适配目录和文件选择策略 (2025-08-09)
- **测试条件一致性**：每次AI调试都使用相同的测试文件和初始状态，确保结果可比较 (2025-08-09)

## 流程合理性审查规则 (2025-08-08)
- **操作时机检查**：每个操作必须在正确的时机和上下文中执行，避免在错误阶段进行不合理操作 (2025-08-08)
- **前置条件验证**：执行任何操作前必须验证其前置条件是否满足，如文件选择前不能进行文件质量稽核 (2025-08-08)
- **流程顺序强制**：严格按照workflow文档规定的流程顺序执行，不得随意调整或跳跃 (2025-08-08)
- **上下文依赖检查**：确保操作具有必要的上下文信息，避免在缺乏上下文的情况下执行 (2025-08-08)
- **流程合理性评估**：定期评估现有流程的合理性，识别和修复不合理的流程设计 (2025-08-08)
- **用户观察重视**：高度重视用户对流程合理性的观察和质疑，及时调查和修复问题 (2025-08-08)

## 流程优化器自动应用规则 (2025-07-31)
- **自动集成原则**：AI助手会自动应用流程优化器，无需用户明确提示 (2025-07-31)
- **问题响应式优化**：发现输出混乱、流程跳跃、技术细节暴露等问题时自动应用优化 (2025-07-31)
- **代码修改时自动优化**：涉及用户界面输出的代码修改时自动使用流程优化器 (2025-07-31)
- **新功能开发自动集成**：开发新功能时自动确保符合流程优化标准 (2025-07-31)
- **测试驱动优化**：测试中发现输出质量问题时自动应用优化方案 (2025-07-31)
- **用户体验优先**：任何影响用户体验的输出问题都触发自动优化 (2025-07-31)
- **系统性优化思维**：不满足于局部优化，建立系统性的流程管理机制 (2025-07-31)
- **持续改进机制**：基于使用效果和用户反馈持续改进优化策略 (2025-07-31)
- **多模块扩展应用**：流程优化器已扩展到文件写入、错误处理、数据预览等多个模块 (2025-07-31)
- **智能场景识别**：支持minute_data_update、data_analysis、file_operations、error_handling等场景 (2025-07-31)
- **预定义优化策略**：每个场景都有预定义的抑制和增强策略，确保一致的用户体验 (2025-07-31)

## 输出质量管理规范 (2025-07-31)
- **输出质量目标**：输出质量评分必须达到95%以上，流程清晰度提升90%以上 (2025-07-31)
- **信息层级标准**：建立主流程→子流程→步骤→操作→结果的清晰层级 (2025-07-31)
- **技术细节隐藏率**：95%以上的技术细节必须隐藏或重定向到日志文件 (2025-07-31)
- **用户界面专业性**：所有用户界面输出必须使用结构化输出格式器 (2025-07-31)
- **错误处理优化**：错误信息必须明确影响和回退方案，避免混乱 (2025-07-31)
- **自动化质量检测**：建立自动化的输出质量检测和评估机制 (2025-07-31)
- **持续监控改进**：定期评估输出质量并持续改进优化策略 (2025-07-31)

## 扩展流程优化规范 (2025-07-31)
- **多模块优化覆盖**：流程优化器必须覆盖文件写入、错误处理、数据预览等所有用户界面输出模块 (2025-07-31)
- **智能数据预览控制**：数据预览行数根据场景智能调整，分钟数据更新场景最多显示3行 (2025-07-31)
- **二进制数据抑制**：在非调试场景下完全抑制二进制数据的详细显示 (2025-07-31)
- **错误信息用户化**：错误信息必须用户友好，技术堆栈信息只在调试模式下显示 (2025-07-31)
- **场景化优化策略**：每个使用场景都有预定义的抑制和增强策略，确保输出一致性 (2025-07-31)
- **优化统计监控**：实时监控优化效果，记录抑制操作和增强操作的统计数据 (2025-07-31)
- **上下文传递机制**：确保流程优化器的上下文在整个调用链中正确传递 (2025-07-31)
- **自动优化触发**：在数据下载、文件操作、错误处理等关键节点自动触发优化 (2025-07-31)

## 测试环境规范化规则 (2025-07-31)
- **测试脚本位置规范**：所有测试脚本必须位于test_environments/对应测试类型/configs/目录中 (2025-07-31)
- **测试环境隔离**：测试脚本不得位于项目根目录，必须在专门的测试环境中运行 (2025-07-31)
- **路径解析标准化**：测试脚本必须使用绝对路径和正确的项目根目录定位 (2025-07-31)
- **工作目录管理**：测试时必须正确管理工作目录，避免导入和执行问题 (2025-07-31)
- **测试文档完整性**：每个测试环境必须有完整的README文档和使用说明 (2025-07-31)

## API方法验证规范 (2025-07-31)
- **方法名准确性验证**：使用第三方模块方法前必须验证方法名的正确性 (2025-07-31)
- **API文档查阅**：不确定方法名时必须查阅官方文档或源代码 (2025-07-31)
- **动态验证优先**：静态检查不足，必须进行实际调用验证 (2025-07-31)
- **错误信息分析**：遇到AttributeError时立即检查方法名和参数 (2025-07-31)
- **测试覆盖API调用**：所有重要的API调用都必须包含在测试中 (2025-07-31)

## 输出分离规范 (2025-07-31)
- **用户界面输出**：使用结构化输出格式器，专注用户体验和专业形象 (2025-07-31)
- **调试日志输出**：使用logger记录到文件，专注调试信息和问题追踪 (2025-07-31)
- **避免重复输出**：同一信息不得同时使用print和logger输出到terminal (2025-07-31)
- **console日志禁用**：在项目日志配置中设置enable_console=False (2025-07-31)
- **职责明确分离**：用户看到的是结构化输出，开发者看到的是详细日志 (2025-07-31)
- **重复检测机制**：建立自动化的重复输出检测测试，确保输出质量 (2025-07-31)

## Workflow规范遵循规则 (2025-08-08)
- **强制文档检查**：修改任何核心流程代码前，必须先检查相关workflow文档的规定 (2025-08-08)
- **增量更新限制**：严格遵循1min_workflow_improved.md第310行规定，增量更新过程不能再进行智能文件选择 (2025-08-08)
- **流程顺序强制**：严格按照workflow文档规定的四步流程顺序执行，不得在文件选择前进行数据质量稽核 (2025-08-08)
- **依赖关系审查**：新建或修改类时，必须审查其依赖关系是否违反workflow规范 (2025-08-08)
- **规范违反检测**：建立自动化机制检测代码是否违反workflow规范，如重复调用智能文件选择器 (2025-08-08)
- **架构演进兼容**：架构升级时必须确保新架构完全符合现有workflow规范 (2025-08-08)
- **文档驱动开发**：将workflow文档作为开发的重要参考，建立规范的快速查询机制 (2025-08-08)
- **注释代码检查**：确保被注释的代码真正被注释，避免意外执行违反规范的代码 (2025-08-08)

## 重复输出消除规范 (2025-08-07)
- **静默模式参数**：为可能产生重复输出的方法添加verbose参数，支持静默模式调用 (2025-08-07)
- **调用链优化**：避免在同一流程中多次调用相同的分析或选择方法 (2025-08-07)
- **输出去重机制**：建立输出去重机制，防止相同信息在terminal中重复显示 (2025-08-07)
- **用户体验优先**：任何影响用户体验的重复输出都必须立即修复 (2025-08-07)
- **技术细节隐藏**：在用户界面中隐藏技术实现细节，只显示关键的业务信息 (2025-08-07)

## 系统级修改安全规范 (2025-08-01)
- **stdout操作禁止**：严禁在生产代码中重新包装或操作sys.stdout，避免I/O关闭错误 (2025-08-01)
- **编码处理谨慎**：Windows编码处理必须经过充分测试，避免stdout状态冲突 (2025-08-01)
- **安全输出机制**：实现safe_print等安全输出函数，在stdout不可用时自动回退 (2025-08-01)
- **完整环境测试**：系统级修改必须在与生产环境完全相同的条件下测试 (2025-08-01)
- **渐进式部署**：重要的系统级修改应该分阶段部署和验证 (2025-08-01)
- **回退机制强制**：所有系统级修改必须有完善的回退机制和应急方案 (2025-08-01)
- **状态监控机制**：在关键系统组件中添加状态检查和自动恢复机制 (2025-08-01)

## 文件命名规范 (2025-07-28)
- **实际数据范围命名**：文件名必须使用文件内实际数据的起止时间，而不是用户请求的时间范围 (2025-07-28)
- **数据源限制透明化**：当数据源（如pytdx）无法提供完整请求范围的数据时，文件名应反映实际获取的数据范围 (2025-07-28)
- **用户期望管理**：通过文件名让用户清楚知道文件中实际包含的数据时间范围，避免误解 (2025-07-28)
- **数据完整性指示**：文件名是数据完整性的重要指示器，必须准确反映文件内容的实际覆盖范围 (2025-07-28)

## 结构化任务流程设计规范 (2025-07-28)
- **四步标准流程**：复杂任务必须分解为标准化的四步流程，每步有明确职责和输出 (2025-07-28)
- **步骤标识显性化**：每个步骤必须有清晰的print标识，格式为"🔍 【第X步】步骤名称 - 对象" (2025-07-28)
- **模块化实现**：每个步骤应有独立的处理方法，避免单一方法承担多个职责 (2025-07-28)
- **错误隔离原则**：单个步骤的失败不应影响其他步骤的执行，需要优雅降级 (2025-07-28)
- **状态传递规范**：步骤间的数据传递必须明确，避免隐式依赖和信息丢失 (2025-07-28)

## 数据源限制处理规范 (2025-07-28)
- **限制认知透明化**：明确每个数据源的能力边界，如pytdx只提供最近100个交易日的分钟数据 (2025-07-28)
- **多源协调策略**：当单一数据源无法满足需求时，建立多数据源协调机制 (2025-07-28)
- **用户期望管理**：向用户明确说明数据源限制，不给出无法实现的承诺 (2025-07-28)
- **降级处理机制**：当数据源限制导致数据不完整时，提供明确的降级处理方案 (2025-07-28)

## 智能组件集成规范 (2025-07-28)
- **组件间信息传递验证**：确保智能组件（如文件选择器）的输出能被后续组件正确处理 (2025-07-28)
- **空值处理强制**：所有智能组件的输出都必须进行空值检查和异常处理 (2025-07-28)
- **失败模式设计**：智能组件失败时必须有明确的失败模式和回退策略 (2025-07-28)
- **调试信息完整性**：智能组件必须提供足够的调试信息，便于问题定位 (2025-07-28)

## Terminal输出格式规范 (2025-07-28)
- **统一输出方式**：关键流程信息使用print()直接输出，详细日志使用logger记录 (2025-07-28)
- **简洁信息原则**：避免冗余和重复信息，每条输出都应有明确价值 (2025-07-28)
- **格式标准化**：使用统一的符号和格式，如"📊 [1/4] 步骤名称"、"   ✅ 结果描述" (2025-07-28)
- **层级缩进规范**：主流程不缩进，子步骤使用3个空格缩进，保持视觉层次清晰 (2025-07-28)
- **状态标识统一**：成功用✅，失败用❌，警告用⚠️，信息用ℹ️，进行中用🔄 (2025-07-28)
- **分隔符规范**：主标题用"="分隔符(60字符)，子标题用"-"分隔符(40字符) (2025-07-28)
- **进度显示标准**：使用[当前/总数]格式显示进度，如"[1/4]"、"[2/3]" (2025-07-28)
- **初始化日志最小化**：避免显示过多的初始化信息，专注于核心流程输出 (2025-07-28)
- **参数显示优化**：多个相关参数使用管道符"|"分隔在单行显示，如"任务 | 参数1: 值1 | 参数2: 值2" (2025-07-28)
- **步骤标识简化**：使用"🔍 [1/4] 步骤名称"格式，避免冗长的中文描述 (2025-07-28)
- **信息密度控制**：每屏显示的信息量要适中，避免信息过载影响用户体验 (2025-07-28)
- **用户反馈导向**：根据用户对输出格式的反馈持续优化，保持用户友好性 (2025-07-28)

## 用户体验优化规范 (2025-07-28)
- **问题敏感性**：高度重视用户指出的界面和体验问题，即使看似细微也要认真对待 (2025-07-28)
- **系统性改进**：发现一个格式问题时，要系统性检查和修复所有相关的格式问题 (2025-07-28)
- **一致性优先**：保持整个系统输出格式的一致性，避免混合使用不同的显示方式 (2025-07-28)
- **简洁性原则**：优先选择简洁明了的表达方式，去除不必要的冗余信息 (2025-07-28)
- **专业性提升**：通过统一的格式规范提升系统的专业形象和用户信任度 (2025-07-28)
- **反馈循环建立**：建立用户反馈收集和响应机制，持续改进用户体验 (2025-07-28)

## Windows编码处理规范 (2025-07-31)
- **强制UTF-8编码**：在主程序和输出模块中必须设置UTF-8编码，解决Windows下emoji显示问题 (2025-07-31)
- **编码设置位置**：在main.py和structured_output_formatter.py开头添加编码处理代码 (2025-07-31)
- **subprocess编码**：在使用subprocess时必须指定encoding='utf-8'和errors='replace' (2025-07-31)
- **兼容性检查**：所有包含emoji或特殊字符的输出都必须在Windows环境下测试 (2025-07-31)
- **编码错误处理**：遇到编码错误时使用replace策略而非崩溃 (2025-07-31)

## 代码重构最佳实践 (2025-07-28)
- **渐进式优化**：采用渐进式重构方法，逐步改进而不是一次性大规模修改 (2025-07-28)
- **功能完整性保证**：重构过程中必须保证所有原有功能的完整性，不能因优化而丢失功能 (2025-07-28)
- **向后兼容性**：重构后的代码应保持向后兼容，不影响现有的调用方式 (2025-07-28)
- **测试验证强制**：每次重构后必须进行完整的功能测试，确保重构没有引入新问题 (2025-07-28)
- **文档同步更新**：重构代码的同时必须同步更新相关文档和注释 (2025-07-28)
- **性能影响评估**：重构前后必须评估性能影响，确保优化不会导致性能退化 (2025-07-28)

## 1分钟数据测试环境规范 (2025-07-27)
- **专属测试目录**：对于1min数据的处理必须在专属测试目录下进行测试 (2025-07-27)
- **测试目录路径**：`test_environments/minute_data_tests/` 作为1分钟数据测试专用目录 (2025-07-27)
- **测试文件识别**：测试文档通常以test_前缀，如`test_1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt` ，它等同于生产中使用的文件“1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt”，请你的测试程序在选择原始数据时对此前缀先进行处理以免找不到对应的测试原始文档(2025-07-27)
- **测试文件保护**：test_开头的文件在测试过程中不进行删除或修改，保持测试条件 (2025-07-27)
- **生产环境隔离**：避免在生产环境直接测试，因为生产文档再被程序改动后就不具备测试条件反而会引起不必要的误导 (2025-07-27)
- **测试结果独立**：测试生成的结果文件遵循现有生产环境命名规则，但存储在测试目录下 (2025-07-27)
- **功能验证要求**：每次1分钟数据相关功能开发或修改后，必须在测试环境中验证 (2025-07-27)

## 缺失数据定义和检测规范 (2025-07-27)
- **A股1分钟数据标准**：每个交易日应有240行数据（9:30-11:30 + 13:00-15:00 = 240分钟） (2025-07-27)
- **缺失数据定义**：1分钟数据少于或者大于240行就需要定义为缺失，不采用分钟级精确匹配 (2025-07-27)
- **检测逻辑标准**：基于总行数判断而非具体时间点缺失，更符合业务实际需求 (2025-07-27)
- **独立检测模块**：使用专门的缺失数据处理器，独立于增量下载逻辑 (2025-07-27)
- **测试环境应用**：缺失数据检测和补全功能必须在专属测试目录中验证 (2025-07-27)

## 集约化下载策略规范 (2025-07-27)
- **pytdx特性认知**：充分理解pytdx倒序下载特性，避免重复下载大量数据 (2025-07-27)
- **需求统一分析**：将增量下载和缺失数据补全需求统一分析，设计最优下载范围 (2025-07-27)
- **效益优先原则**：在保证逻辑清晰的前提下，优化下载策略减少重复请求 (2025-07-27)
- **一次性下载策略**：当可能时，使用统一的时间范围一次性获取所有需要的数据 (2025-07-27)
- **下载策略模块化**：建立专门的集约化下载策略模块，提供需求分析和优化建议 (2025-07-27)

## 数据质量验证标准 (2025-07-26)
- **文件命名格式验证**：频率前缀_0_股票代码_时间范围_来源.txt（如1min_0_000617_...而不是1_0_000617_...）
- **时间格式验证**：分钟数据时间格式必须包含实际分钟信息，不能全部为00:00
- **前复权价格验证**：前复权收盘价与当日收盘价必须有合理差异，不能完全相同
- **数据完整性验证**：包含所有必要字段且格式正确，字段分隔符为管道符"|"
- **数据抽样检查**：必须随机检查生成文件的前10行数据质量，验证数据准确性
- **数据质量验证失败**：即使文件生成成功，如果数据质量不合格也算任务失败

## 配置管理体系化规则 (2025-07-26)
- **配置结构原则**：按功能模块分组配置，而非按技术实现分组，相关配置集中管理 (2025-07-26)
- **统一格式规范**：所有配置模块使用统一的结构（基础设置→核心参数→子模块→高级设置→调试设置） (2025-07-26)
- **配置注释标准**：每个配置项必须包含类型说明、作用描述、默认值、可选值等详细信息 (2025-07-26)
- **配置验证机制**：实现配置类型验证、逻辑验证、兼容性验证，提供友好的错误提示 (2025-07-26)
- **AI助手配置利用**：AI助手必须主动读取和利用user_config.py中的配置，而非硬编码默认值 (2025-07-26)
- **配置模块化组织** (2025-07-26)：
  - `system_config` - 系统基础配置（调试、日志、性能监控）
  - `data_sources` - 数据源配置（TDX、pytdx、互联网数据源）
  - `data_processing` - 数据处理配置（清洗规则、透明处理、质量验证）
  - `intelligent_features` - 智能功能配置（文件选择器、增量下载、自动优化）
  - `output_storage` - 输出存储配置（路径、命名规则、备份策略）
  - `error_handling` - 错误处理配置（处理策略、回退设置、报告配置）
  - `user_interface` - 用户界面配置（显示详细程度、交互模式、进度显示）
- **配置优先级管理**：系统基础配置（最高）→数据源配置（高）→数据处理配置（高）→智能功能配置（中）→其他配置（中低） (2025-07-26)
- **配置兼容性保证**：配置结构变更时必须保持向后兼容，提供配置迁移机制和默认值填充 (2025-07-26)