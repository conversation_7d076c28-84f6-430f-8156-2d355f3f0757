#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Simple trace to find workflow violation
"""

import os
import sys

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def install_patch():
    """Install tracing patch"""
    try:
        from utils.missing_data_processor import MissingDataProcessor
        
        original_detect = MissingDataProcessor.detect_missing_minute_data
        
        def patched_detect(self, file_path, stock_code, silent=True):
            """Patched detect method"""
            import traceback
            
            print(f"\n*** MissingDataProcessor.detect_missing_minute_data CALLED ***")
            print(f"File: {file_path}")
            print(f"Stock: {stock_code}")
            print(f"Silent: {silent}")
            
            print(f"\nCall stack:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-8:], 1):  # Show last 8 frames
                if 'task_manager' in frame or 'TaskManager' in frame:
                    print(f">>> {i:2d}. {frame.strip()}")
                else:
                    print(f"    {i:2d}. {frame.strip()}")
            
            print("=" * 100)
            
            return original_detect(self, file_path, stock_code, silent)
        
        MissingDataProcessor.detect_missing_minute_data = patched_detect
        
        print("Patch installed successfully")
        return True
        
    except Exception as e:
        print(f"Patch installation failed: {e}")
        return False

def run_test():
    """Run the test"""
    print("\nRunning main.py with tracing...")
    
    try:
        from main import main
        result = main()
        print(f"Main completed with result: {result}")
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def main():
    """Main function"""
    print("Workflow Violation Tracer")
    print("=" * 80)
    
    if not install_patch():
        return 1
    
    success = run_test()
    
    print(f"\nTrace completed: {'SUCCESS' if success else 'FAILED'}")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
