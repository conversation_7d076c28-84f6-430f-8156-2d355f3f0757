#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构清理脚本

安全地清理和重组MythQuant项目结构
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json


class ProjectCleaner:
    """项目清理器 - 安全的项目结构整理"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backup_dir = self.project_root / "structure_cleanup_backup"
        
        # 定义需要保留在根目录的核心文件
        self.keep_in_root = {
            'config_compatibility.py',
            'data_access_compatibility.py', 
            'algorithm_compatibility.py',
            'io_compatibility.py',
            'user_config.py',
            'main.py',
            'requirements.txt',
            'README.md'
        }
        
        # 定义清理规则
        self.cleanup_rules = {
            # 清理临时文件
            'temp_files': {
                'patterns': ['*.pyc', '__pycache__'],
                'action': 'delete',
                'description': '删除Python缓存文件'
            },
            
            # 移动旧版本代码
            'legacy_code': {
                'patterns': [
                    'func.py', 'func_Tdx.py', 'func_Tdx1.py', 'func_Util.py',
                    'main_v*.py', 'readTDX_cw.py', 'read_dat_file.py',
                    'gbbq_*.py', 'minute_path_helper.py', 'check_last_record.py'
                ],
                'target_dir': 'legacy',
                'action': 'move',
                'description': '移动旧版本代码到legacy目录'
            },
            
            # 移动测试文件
            'test_files': {
                'patterns': [
                    'test_*.py', '*test*.py', 'quick_*.py', 'simple_*.py',
                    'run_tests.py', 'final_validation_test.py', 
                    'comprehensive_migration_test.py'
                ],
                'target_dir': 'tests',
                'action': 'move',
                'description': '整理测试文件到tests目录'
            },
            
            # 移动工具脚本
            'tool_scripts': {
                'patterns': [
                    'create_*.py', 'install_*.py', 'fix_*.py', 'find_*.py',
                    'migrate_*.py', 'prepare_*.py', 'verify_*.py', 
                    'analyze_*.py', 'reorganize_*.py', 'generate_*.py',
                    'dependency_check.py', 'direct_import_test.py'
                ],
                'target_dir': 'tools',
                'action': 'move', 
                'description': '移动工具脚本到tools目录'
            },
            
            # 移动文档文件
            'documentation': {
                'patterns': [
                    'MIGRATION_*.md', 'PROJECT_*.md'
                ],
                'target_dir': 'docs',
                'action': 'move',
                'description': '整理文档到docs目录'
            },
            
            # 移动配置文件
            'config_files': {
                'patterns': [
                    'environment.yml', 'data_processing_config.yaml',
                    'pyproject.toml', 'setup.py', 'tox.ini', 'MANIFEST.in'
                ],
                'target_dir': 'config',
                'action': 'move',
                'description': '移动配置文件到config目录'
            },
            
            # 移动数据文件
            'data_files': {
                'patterns': [
                    'tdx_servers.json', 'project_structure_analysis.json',
                    'cache_*.json'
                ],
                'target_dir': 'data',
                'action': 'move',
                'description': '移动数据文件到data目录'
            },
            
            # 移动归档文件
            'archive_files': {
                'patterns': [
                    '*.rar', '*.zip', '*.docx', 'stock-analysis.zip'
                ],
                'target_dir': 'archive',
                'action': 'move',
                'description': '移动归档文件到archive目录'
            },
            
            # 移动其他文件
            'misc_files': {
                'patterns': [
                    'img.png'
                ],
                'target_dir': 'assets',
                'action': 'move',
                'description': '移动资源文件到assets目录'
            }
        }
    
    def clean_project(self) -> bool:
        """执行项目清理"""
        print("🧹 MythQuant 项目结构清理")
        print("=" * 50)
        
        try:
            # 1. 创建备份
            self.create_backup()
            
            # 2. 分析当前状态
            self.analyze_current_state()
            
            # 3. 执行清理操作
            self.execute_cleanup()
            
            # 4. 验证清理结果
            self.verify_cleanup()
            
            # 5. 生成清理报告
            self.generate_cleanup_report()
            
            print("\n✅ 项目结构清理完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 项目清理失败: {e}")
            return False
    
    def create_backup(self):
        """创建备份"""
        print("💾 创建清理前备份...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"before_cleanup_{timestamp}"
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 备份根目录的重要文件
        backup_count = 0
        for file_path in self.project_root.iterdir():
            if file_path.is_file() and not file_path.name.startswith('.'):
                try:
                    dst = backup_path / file_path.name
                    shutil.copy2(file_path, dst)
                    backup_count += 1
                except Exception as e:
                    print(f"   ⚠️ 备份文件失败 {file_path.name}: {e}")
        
        print(f"   ✅ 已备份 {backup_count} 个文件到: {backup_path}")
    
    def analyze_current_state(self):
        """分析当前状态"""
        print("\n🔍 分析当前项目状态...")
        
        # 统计根目录文件
        root_files = [f for f in self.project_root.iterdir() if f.is_file()]
        print(f"   📊 根目录文件数: {len(root_files)}")
        
        # 分析需要处理的文件
        files_to_process = {}
        for rule_name, rule in self.cleanup_rules.items():
            matching_files = []
            for pattern in rule['patterns']:
                for file_path in self.project_root.glob(pattern):
                    if file_path.is_file() and file_path.parent == self.project_root:
                        matching_files.append(file_path.name)
            
            if matching_files:
                files_to_process[rule_name] = matching_files
                print(f"   📋 {rule['description']}: {len(matching_files)} 个文件")
        
        return files_to_process
    
    def execute_cleanup(self):
        """执行清理操作"""
        print("\n🚚 执行清理操作...")
        
        processed_count = 0
        
        for rule_name, rule in self.cleanup_rules.items():
            print(f"\n   📂 {rule['description']}")
            
            # 创建目标目录
            if rule['action'] == 'move' and 'target_dir' in rule:
                target_dir = self.project_root / rule['target_dir']
                target_dir.mkdir(exist_ok=True)
            
            # 处理匹配的文件
            for pattern in rule['patterns']:
                for file_path in self.project_root.glob(pattern):
                    if file_path.is_file() and file_path.parent == self.project_root:
                        # 检查是否是需要保留在根目录的文件
                        if file_path.name in self.keep_in_root:
                            print(f"      ⏭️ 跳过核心文件: {file_path.name}")
                            continue
                        
                        try:
                            if rule['action'] == 'delete':
                                file_path.unlink()
                                print(f"      🗑️ 删除: {file_path.name}")
                            elif rule['action'] == 'move':
                                target_path = self.project_root / rule['target_dir'] / file_path.name
                                
                                # 避免覆盖已存在的文件
                                if target_path.exists():
                                    target_path = self.project_root / rule['target_dir'] / f"{file_path.stem}_moved{file_path.suffix}"
                                
                                shutil.move(str(file_path), str(target_path))
                                print(f"      ✅ 移动: {file_path.name} -> {rule['target_dir']}/")
                            
                            processed_count += 1
                            
                        except Exception as e:
                            print(f"      ❌ 处理失败 {file_path.name}: {e}")
            
            # 处理目录模式
            for pattern in rule['patterns']:
                if pattern.endswith('/**/*'):
                    dir_pattern = pattern.replace('/**/*', '')
                    for dir_path in self.project_root.glob(dir_pattern):
                        if dir_path.is_dir() and dir_path.parent == self.project_root:
                            try:
                                if rule['action'] == 'delete':
                                    shutil.rmtree(dir_path)
                                    print(f"      🗑️ 删除目录: {dir_path.name}")
                                elif rule['action'] == 'move':
                                    target_path = self.project_root / rule['target_dir'] / dir_path.name
                                    if not target_path.exists():
                                        shutil.move(str(dir_path), str(target_path))
                                        print(f"      ✅ 移动目录: {dir_path.name} -> {rule['target_dir']}/")
                                
                                processed_count += 1
                                
                            except Exception as e:
                                print(f"      ❌ 处理目录失败 {dir_path.name}: {e}")
        
        print(f"\n   📊 总共处理了 {processed_count} 个文件/目录")
    
    def verify_cleanup(self):
        """验证清理结果"""
        print("\n✅ 验证清理结果...")
        
        # 检查根目录文件
        root_files = [f.name for f in self.project_root.iterdir() if f.is_file()]
        print(f"   📊 清理后根目录文件数: {len(root_files)}")
        
        # 检查核心文件是否保留
        missing_core_files = []
        for core_file in self.keep_in_root:
            if core_file not in root_files:
                missing_core_files.append(core_file)
        
        if missing_core_files:
            print(f"   ⚠️ 缺少核心文件: {missing_core_files}")
        else:
            print("   ✅ 所有核心文件都已保留")
        
        # 检查新创建的目录
        new_dirs = []
        for rule in self.cleanup_rules.values():
            if rule['action'] == 'move' and 'target_dir' in rule:
                target_dir = self.project_root / rule['target_dir']
                if target_dir.exists():
                    file_count = len(list(target_dir.iterdir()))
                    new_dirs.append(f"{rule['target_dir']} ({file_count} 个文件)")
        
        if new_dirs:
            print("   📁 新创建的目录:")
            for dir_info in new_dirs:
                print(f"      - {dir_info}")
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        print("\n📄 生成清理报告...")
        
        # 分析清理后的结构
        final_structure = {}
        for item in self.project_root.iterdir():
            if item.is_dir() and not item.name.startswith('.'):
                file_count = len(list(item.rglob('*')))
                final_structure[item.name] = file_count
        
        root_files = [f.name for f in self.project_root.iterdir() if f.is_file()]
        if root_files:
            final_structure['根目录文件'] = len(root_files)
        
        # 生成报告
        report = {
            'cleanup_date': datetime.now().isoformat(),
            'cleanup_rules_applied': len(self.cleanup_rules),
            'final_structure': final_structure,
            'root_files_kept': list(root_files),
            'core_files_preserved': list(self.keep_in_root),
            'recommendations': [
                '验证兼容性模块是否正常工作',
                '运行测试确保功能完整性',
                '检查导入路径是否需要更新',
                '清理完成后可删除备份文件'
            ]
        }
        
        report_file = self.project_root / 'cleanup_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ 清理报告已保存: {report_file}")
        
        # 打印最终结构
        print("\n📊 清理后项目结构:")
        for dir_name, count in sorted(final_structure.items()):
            print(f"   📁 {dir_name}: {count} 个文件")


def main():
    """主函数"""
    cleaner = ProjectCleaner()
    
    print("⚠️  重要提示:")
    print("   此操作将重新组织项目文件结构")
    print("   核心兼容性模块将保留在根目录")
    print("   其他文件将按类型移动到相应目录")
    print("   操作前会自动创建备份")
    print()
    
    response = input("是否继续执行项目清理? (y/N): ")
    if response.lower() != 'y':
        print("❌ 用户取消操作")
        return False
    
    success = cleaner.clean_project()
    
    if success:
        print("\n🎉 项目结构清理成功完成！")
        print("\n📋 下一步建议:")
        print("1. 验证兼容性模块是否正常工作")
        print("2. 运行 python run_tests.py 验证功能")
        print("3. 检查是否需要更新导入路径")
        print("4. 查看 cleanup_report.json 了解详细变更")
        return True
    else:
        print("\n❌ 项目清理失败！")
        print("请检查错误信息，必要时从备份恢复")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
