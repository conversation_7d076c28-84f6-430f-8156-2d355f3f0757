#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
问题分类系统
按问题类型、复杂度、解决状态进行分类管理
"""

from enum import Enum
from typing import Dict, List, Optional
from dataclasses import dataclass
from datetime import datetime

class ProblemType(Enum):
    """问题类型枚举"""
    BUG_FIX = "bug修复"
    FEATURE_REQUEST = "功能需求"
    PERFORMANCE_OPTIMIZATION = "性能优化"
    ARCHITECTURE_DESIGN = "架构设计"
    CONFIGURATION_ISSUE = "配置问题"
    DATA_PROCESSING = "数据处理"
    FILE_HANDLING = "文件处理"
    ERROR_HANDLING = "错误处理"
    INTEGRATION_ISSUE = "集成问题"
    DOCUMENTATION = "文档相关"

class ComplexityLevel(Enum):
    """复杂度级别"""
    LOW = "低"
    MEDIUM = "中"
    HIGH = "高"
    CRITICAL = "关键"

class SolutionStatus(Enum):
    """解决状态"""
    IDENTIFIED = "已识别"
    IN_PROGRESS = "进行中"
    SOLVED = "已解决"
    VERIFIED = "已验证"
    CLOSED = "已关闭"
    REOPENED = "重新打开"

@dataclass
class ProblemRecord:
    """问题记录"""
    id: str
    title: str
    description: str
    problem_type: ProblemType
    complexity: ComplexityLevel
    status: SolutionStatus
    domain: str
    module: str
    tech_stack: str
    created_time: datetime
    updated_time: datetime
    solution: Optional[str] = None
    files_affected: List[str] = None
    related_problems: List[str] = None
    tags: List[str] = None
    
    def __post_init__(self):
        if self.files_affected is None:
            self.files_affected = []
        if self.related_problems is None:
            self.related_problems = []
        if self.tags is None:
            self.tags = []

class ProblemClassifier:
    """问题分类器"""
    
    def __init__(self):
        self.classification_rules = self._init_classification_rules()
        self.complexity_indicators = self._init_complexity_indicators()
    
    def classify_problem(self, title: str, description: str, context: str = "") -> Dict:
        """
        分类问题
        
        Args:
            title: 问题标题
            description: 问题描述
            context: 上下文信息
            
        Returns:
            分类结果字典
        """
        full_text = f"{title} {description} {context}".lower()
        
        # 确定问题类型
        problem_type = self._determine_problem_type(full_text)
        
        # 评估复杂度
        complexity = self._assess_complexity(full_text, title, description)
        
        # 提取领域和模块
        domain = self._extract_domain(full_text)
        module = self._extract_module(full_text)
        tech_stack = self._extract_tech_stack(full_text)
        
        # 生成标签
        tags = self._generate_tags(full_text, problem_type, complexity)
        
        return {
            'problem_type': problem_type,
            'complexity': complexity,
            'domain': domain,
            'module': module,
            'tech_stack': tech_stack,
            'tags': tags,
            'priority': self._calculate_priority(problem_type, complexity),
            'estimated_effort': self._estimate_effort(complexity, problem_type)
        }
    
    def create_problem_record(self, title: str, description: str, 
                            classification: Dict, solution: str = None) -> ProblemRecord:
        """创建问题记录"""
        problem_id = self._generate_problem_id(title)
        current_time = datetime.now()
        
        return ProblemRecord(
            id=problem_id,
            title=title,
            description=description,
            problem_type=classification['problem_type'],
            complexity=classification['complexity'],
            status=SolutionStatus.SOLVED if solution else SolutionStatus.IDENTIFIED,
            domain=classification['domain'],
            module=classification['module'],
            tech_stack=classification['tech_stack'],
            created_time=current_time,
            updated_time=current_time,
            solution=solution,
            tags=classification['tags']
        )
    
    def _init_classification_rules(self) -> Dict:
        """初始化分类规则"""
        return {
            ProblemType.BUG_FIX: [
                '错误', '失败', '无法', '不能', 'bug', 'error', '异常', '问题',
                '崩溃', '报错', '不正常', '故障'
            ],
            ProblemType.FEATURE_REQUEST: [
                '需要', '希望', '建议', '增加', '添加', '实现', '功能',
                '特性', '改进', '扩展'
            ],
            ProblemType.PERFORMANCE_OPTIMIZATION: [
                '性能', '优化', '慢', '快速', '效率', '速度', '内存',
                '缓存', '响应时间', '吞吐量'
            ],
            ProblemType.ARCHITECTURE_DESIGN: [
                '架构', '设计', '重构', '模块', '组件', '接口',
                '解耦', '分层', '模式'
            ],
            ProblemType.CONFIGURATION_ISSUE: [
                '配置', 'config', '设置', '参数', '环境',
                '部署', '安装'
            ],
            ProblemType.DATA_PROCESSING: [
                '数据', '处理', '下载', '解析', '转换',
                'pytdx', 'akshare', '股票数据'
            ],
            ProblemType.FILE_HANDLING: [
                '文件', '读取', '写入', '保存', '路径',
                '格式', '编码', '正则表达式'
            ],
            ProblemType.ERROR_HANDLING: [
                '异常处理', '错误处理', '容错', '回退',
                '降级', '恢复'
            ]
        }
    
    def _init_complexity_indicators(self) -> Dict:
        """初始化复杂度指标"""
        return {
            ComplexityLevel.LOW: {
                'keywords': ['简单', '小', '快速', '直接'],
                'file_count_max': 2,
                'description_length_max': 200
            },
            ComplexityLevel.MEDIUM: {
                'keywords': ['中等', '一般', '需要'],
                'file_count_max': 5,
                'description_length_max': 500
            },
            ComplexityLevel.HIGH: {
                'keywords': ['复杂', '困难', '大量', '重构'],
                'file_count_max': 10,
                'description_length_max': 1000
            },
            ComplexityLevel.CRITICAL: {
                'keywords': ['关键', '紧急', '严重', '系统'],
                'file_count_max': float('inf'),
                'description_length_max': float('inf')
            }
        }
    
    def _determine_problem_type(self, text: str) -> ProblemType:
        """确定问题类型"""
        type_scores = {}
        
        for problem_type, keywords in self.classification_rules.items():
            score = sum(1 for keyword in keywords if keyword in text)
            if score > 0:
                type_scores[problem_type] = score
        
        if type_scores:
            return max(type_scores, key=type_scores.get)
        
        return ProblemType.BUG_FIX  # 默认类型
    
    def _assess_complexity(self, text: str, title: str, description: str) -> ComplexityLevel:
        """评估复杂度"""
        # 基于文本长度
        text_length = len(description)
        
        # 基于关键词
        complexity_scores = {}
        for level, indicators in self.complexity_indicators.items():
            score = sum(1 for keyword in indicators['keywords'] if keyword in text)
            if text_length <= indicators['description_length_max']:
                score += 1
            complexity_scores[level] = score
        
        # 基于特殊指标
        if any(keyword in text for keyword in ['架构', '重构', '系统']):
            return ComplexityLevel.HIGH
        
        if any(keyword in text for keyword in ['紧急', '关键', '严重']):
            return ComplexityLevel.CRITICAL
        
        if text_length > 1000:
            return ComplexityLevel.HIGH
        elif text_length > 500:
            return ComplexityLevel.MEDIUM
        else:
            return ComplexityLevel.LOW
    
    def _extract_domain(self, text: str) -> str:
        """提取领域"""
        domain_keywords = {
            '数据处理': ['数据', '下载', 'pytdx', 'akshare', '股票'],
            '文件处理': ['文件', '正则', '匹配', '选择器', '路径'],
            '架构设计': ['架构', '模块', '重构', '设计', '组件'],
            '性能优化': ['性能', '优化', '缓存', '速度', '内存'],
            '配置管理': ['配置', 'config', '设置', '参数'],
            '错误处理': ['错误', '异常', '调试', 'debug']
        }
        
        for domain, keywords in domain_keywords.items():
            if any(keyword in text for keyword in keywords):
                return domain
        
        return '通用'
    
    def _extract_module(self, text: str) -> str:
        """提取模块"""
        module_keywords = {
            '智能文件选择器': ['smart_file_selector', '文件选择', '文件匹配'],
            'pytdx下载器': ['pytdx_downloader', 'pytdx', '数据下载'],
            '主程序架构': ['main.py', '主程序', '入口'],
            '配置管理器': ['config_manager', '配置', 'config'],
            '数据处理器': ['data_processor', '数据处理', '前复权'],
            '缓存系统': ['cache', '缓存', '存储']
        }
        
        for module, keywords in module_keywords.items():
            if any(keyword in text for keyword in keywords):
                return module
        
        return '未分类'
    
    def _extract_tech_stack(self, text: str) -> str:
        """提取技术栈"""
        tech_keywords = {
            'Python': ['python', 'py', 'pandas', 'numpy'],
            '正则表达式': ['正则', 'regex', 're.', 'pattern'],
            '数据库': ['database', 'db', 'sql'],
            '网络请求': ['requests', 'http', 'api'],
            '文件系统': ['file', 'path', 'os.', 'filesystem'],
            '配置管理': ['config', 'yaml', 'json', 'ini']
        }
        
        for tech, keywords in tech_keywords.items():
            if any(keyword in text for keyword in keywords):
                return tech
        
        return '通用'
    
    def _generate_tags(self, text: str, problem_type: ProblemType, 
                      complexity: ComplexityLevel) -> List[str]:
        """生成标签"""
        tags = [problem_type.value, complexity.value]
        
        # 添加技术标签
        if 'pytdx' in text:
            tags.append('pytdx')
        if '正则' in text or 'regex' in text:
            tags.append('正则表达式')
        if '文件' in text:
            tags.append('文件处理')
        if '配置' in text:
            tags.append('配置管理')
        
        return tags
    
    def _calculate_priority(self, problem_type: ProblemType, 
                          complexity: ComplexityLevel) -> str:
        """计算优先级"""
        if complexity == ComplexityLevel.CRITICAL:
            return "紧急"
        elif complexity == ComplexityLevel.HIGH:
            return "高"
        elif problem_type == ProblemType.BUG_FIX:
            return "高"
        elif complexity == ComplexityLevel.MEDIUM:
            return "中"
        else:
            return "低"
    
    def _estimate_effort(self, complexity: ComplexityLevel, 
                        problem_type: ProblemType) -> str:
        """估算工作量"""
        effort_matrix = {
            (ComplexityLevel.LOW, ProblemType.BUG_FIX): "1-2小时",
            (ComplexityLevel.MEDIUM, ProblemType.BUG_FIX): "半天",
            (ComplexityLevel.HIGH, ProblemType.BUG_FIX): "1-2天",
            (ComplexityLevel.LOW, ProblemType.FEATURE_REQUEST): "半天",
            (ComplexityLevel.MEDIUM, ProblemType.FEATURE_REQUEST): "1-2天",
            (ComplexityLevel.HIGH, ProblemType.FEATURE_REQUEST): "1周",
            (ComplexityLevel.HIGH, ProblemType.ARCHITECTURE_DESIGN): "1-2周"
        }
        
        return effort_matrix.get((complexity, problem_type), "待评估")
    
    def _generate_problem_id(self, title: str) -> str:
        """生成问题ID"""
        import hashlib
        timestamp = datetime.now().strftime('%Y%m%d%H%M%S')
        title_hash = hashlib.md5(title.encode()).hexdigest()[:6]
        return f"PROB_{timestamp}_{title_hash}"

def demonstrate_problem_classifier():
    """演示问题分类系统"""
    print("🚀 问题分类系统演示")
    print("=" * 50)
    
    classifier = ProblemClassifier()
    
    # 测试问题
    test_problems = [
        {
            'title': '正则表达式无法匹配带时间戳的文件名',
            'description': '智能文件选择器无法识别带时间戳后缀的文件名格式',
            'context': 'utils/smart_file_selector.py 文件处理'
        },
        {
            'title': 'pytdx数据获取上限不足',
            'description': '历史数据下载失败，10000条上限无法覆盖长期数据',
            'context': 'pytdx下载器 数据处理 性能优化'
        }
    ]
    
    for i, problem in enumerate(test_problems, 1):
        print(f"\n📋 问题 {i}: {problem['title']}")
        
        classification = classifier.classify_problem(
            problem['title'],
            problem['description'],
            problem['context']
        )
        
        print(f"  类型: {classification['problem_type'].value}")
        print(f"  复杂度: {classification['complexity'].value}")
        print(f"  领域: {classification['domain']}")
        print(f"  模块: {classification['module']}")
        print(f"  优先级: {classification['priority']}")
        print(f"  预估工作量: {classification['estimated_effort']}")
    
    print("\n✅ 问题分类系统初始化完成")

if __name__ == '__main__':
    demonstrate_problem_classifier()
