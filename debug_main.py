#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI调试专用启动脚本
强制使用测试环境和测试文件，支持自动备份和还原
"""

import os
import sys
import atexit

# 设置强制测试环境标识
os.environ['FORCE_TEST_ENV'] = '1'
os.environ['AUGMENT_MODE'] = '1'

# 确保测试环境目录存在
test_env_path = 'test_environments/minute_data_tests'
if not os.path.exists(test_env_path):
    os.makedirs(test_env_path, exist_ok=True)
    print(f"✅ 创建测试环境目录: {test_env_path}")

print("🔧 AI调试模式启动")
print(f"📁 测试环境: {test_env_path}")
print("=" * 60)

# 设置测试环境
def setup_test_environment():
    """设置测试环境"""
    try:
        from utils.test_environment_manager import setup_test_environment

        print("🔄 设置测试环境...")
        success, msg = setup_test_environment(['000617'])

        if success:
            print(f"✅ {msg}")
        else:
            print(f"⚠️ {msg}")
            print("💡 将使用原始测试文件")

    except Exception as e:
        print(f"⚠️ 测试环境设置失败: {e}")
        print("💡 将使用原始测试文件")

# 清理测试环境
def cleanup_test_environment():
    """清理测试环境"""
    try:
        from utils.test_environment_manager import cleanup_test_environment

        print("\n🔄 清理测试环境...")
        success, msg = cleanup_test_environment(['000617'])

        if success:
            print(f"✅ {msg}")
        else:
            print(f"⚠️ {msg}")

    except Exception as e:
        print(f"⚠️ 测试环境清理失败: {e}")

# 注册退出时的清理函数
atexit.register(cleanup_test_environment)

# 导入并运行主程序
if __name__ == "__main__":
    # 验证环境检测
    import user_config
    print(f"🔍 环境检测结果: {user_config.CURRENT_ENVIRONMENT}")

    if user_config.CURRENT_ENVIRONMENT != 'test':
        print("❌ 环境检测失败，仍为生产环境")
        print("💡 请检查环境检测逻辑")
        sys.exit(1)
    else:
        print("✅ 成功切换到测试环境")

    # 设置测试环境（备份和创建工作副本）
    setup_test_environment()

    try:
        # 运行主程序
        from main import main
        main()

        # 保存测试结果
        try:
            from utils.test_environment_manager import test_env_manager

            # 这里可以添加保存测试结果的逻辑
            # result_files = [...]  # 收集生成的结果文件
            # test_env_manager.preserve_test_results('000617', result_files)

        except Exception as e:
            print(f"⚠️ 保存测试结果失败: {e}")

    except Exception as e:
        print(f"❌ 程序执行异常: {e}")
        raise
    finally:
        # 清理工作会在atexit中自动执行
        pass
