#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
AI调试专用启动脚本
强制使用测试环境和测试文件
"""

import os
import sys

# 设置强制测试环境标识
os.environ['FORCE_TEST_ENV'] = '1'
os.environ['AUGMENT_MODE'] = '1'

# 确保测试环境目录存在
test_env_path = 'test_environments/minute_data_tests'
if not os.path.exists(test_env_path):
    os.makedirs(test_env_path, exist_ok=True)
    print(f"✅ 创建测试环境目录: {test_env_path}")

# 确保测试文件存在
test_file_path = os.path.join(test_env_path, 'input_data', '1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt')
if not os.path.exists(test_file_path):
    print(f"⚠️ 测试文件不存在: {test_file_path}")
    print(f"💡 请确保测试文件已放置在正确位置")

print("🔧 AI调试模式启动")
print(f"📁 测试环境: {test_env_path}")
print(f"📄 目标测试文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt")
print("=" * 60)

# 导入并运行主程序
if __name__ == "__main__":
    # 验证环境检测
    import user_config
    print(f"🔍 环境检测结果: {user_config.CURRENT_ENVIRONMENT}")
    
    if user_config.CURRENT_ENVIRONMENT != 'test':
        print("❌ 环境检测失败，仍为生产环境")
        print("💡 请检查环境检测逻辑")
        sys.exit(1)
    else:
        print("✅ 成功切换到测试环境")
    
    # 运行主程序
    from main import main
    main()
