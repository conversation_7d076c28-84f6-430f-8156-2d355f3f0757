#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 依赖库自动安装脚本
自动检测并安装所需的第三方库
"""

import subprocess
import sys
import importlib
import os

# 必需的依赖库列表
REQUIRED_PACKAGES = {
    'pandas': 'pandas>=1.5.0',
    'numpy': 'numpy>=1.24.0',
    'mootdx': 'mootdx>=1.2.0',
    'pytdx': 'pytdx>=1.72',
    'openpyxl': 'openpyxl>=3.0.0',
    'xlrd': 'xlrd>=2.0.0',
    'requests': 'requests>=2.28.0',
    'tqdm': 'tqdm>=4.64.0',
    'retry': 'retry>=0.9.2',
}

# 可选的依赖库列表
OPTIONAL_PACKAGES = {
    'terminaltables': 'terminaltables>=3.1.10',
    'tushare': 'tushare>=1.2.89',
}

def check_package_installed(package_name):
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False

def install_package(package_spec):
    """安装单个包"""
    try:
        print(f"正在安装 {package_spec}...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", package_spec])
        print(f"✅ {package_spec} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_spec} 安装失败: {e}")
        return False

def upgrade_pip():
    """升级pip到最新版本"""
    try:
        print("正在升级pip...")
        subprocess.check_call([sys.executable, "-m", "pip", "install", "--upgrade", "pip"])
        print("✅ pip升级成功")
    except subprocess.CalledProcessError as e:
        print(f"⚠️ pip升级失败: {e}")

def main():
    """主函数"""
    print("🚀 MythQuant 依赖库自动安装程序")
    print("=" * 60)
    
    # 升级pip
    upgrade_pip()
    print()
    
    # 检查并安装必需的包
    print("📦 检查必需依赖库...")
    missing_required = []
    
    for package_name, package_spec in REQUIRED_PACKAGES.items():
        if check_package_installed(package_name):
            print(f"✅ {package_name} 已安装")
        else:
            print(f"❌ {package_name} 未安装")
            missing_required.append(package_spec)
    
    if missing_required:
        print(f"\n🔧 需要安装 {len(missing_required)} 个必需依赖库:")
        for package_spec in missing_required:
            print(f"  - {package_spec}")
        
        print("\n正在安装必需依赖库...")
        failed_packages = []
        
        for package_spec in missing_required:
            if not install_package(package_spec):
                failed_packages.append(package_spec)
        
        if failed_packages:
            print(f"\n❌ 以下必需依赖库安装失败:")
            for package in failed_packages:
                print(f"  - {package}")
            print("\n请手动安装这些依赖库:")
            print(f"pip install {' '.join(failed_packages)}")
            return False
    else:
        print("✅ 所有必需依赖库都已安装")
    
    # 检查可选的包
    print(f"\n📦 检查可选依赖库...")
    missing_optional = []
    
    for package_name, package_spec in OPTIONAL_PACKAGES.items():
        if check_package_installed(package_name):
            print(f"✅ {package_name} 已安装")
        else:
            print(f"⚠️ {package_name} 未安装（可选）")
            missing_optional.append(package_spec)
    
    if missing_optional:
        print(f"\n🔧 发现 {len(missing_optional)} 个可选依赖库未安装:")
        for package_spec in missing_optional:
            print(f"  - {package_spec}")
        
        response = input("\n是否安装可选依赖库? (y/n): ").lower().strip()
        if response in ['y', 'yes', '是']:
            print("\n正在安装可选依赖库...")
            for package_spec in missing_optional:
                install_package(package_spec)
    
    print("\n" + "=" * 60)
    print("🎉 依赖库检查完成!")
    
    # 验证核心功能
    print("\n🧪 验证核心功能...")
    try:
        import pandas as pd
        import numpy as np
        from mootdx.reader import Reader
        import pytdx.reader.gbbq_reader
        print("✅ 核心功能验证通过")
        
        # 显示版本信息
        print(f"\n📊 已安装版本信息:")
        print(f"  - pandas: {pd.__version__}")
        print(f"  - numpy: {np.__version__}")
        
        try:
            import mootdx
            print(f"  - mootdx: {mootdx.__version__}")
        except:
            print(f"  - mootdx: 版本信息不可用")
        
        print("\n🎯 MythQuant 已准备就绪!")
        
    except ImportError as e:
        print(f"❌ 核心功能验证失败: {e}")
        print("请检查依赖库安装情况")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    if not success:
        sys.exit(1) 