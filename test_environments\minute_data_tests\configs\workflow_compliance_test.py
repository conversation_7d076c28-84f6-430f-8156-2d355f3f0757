#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1分钟数据工作流程合规性测试
验证当前生产环境是否完全实现了知识库中的改进工作流程
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, List

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.insert(0, str(project_root))

# 切换到项目根目录
os.chdir(project_root)

try:
    # 导入测试配置
    import test_config
    from test_config import MinuteDataTestConfig

    # 导入核心模块
    from utils.smart_file_selector import SmartFileSelector
    from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
    from utils.missing_data_processor import MissingDataProcessor
    from utils.unified_interfaces import check_incremental_download_prerequisite
    from test_environments.shared.utilities.specific_minute_data_fetcher import get_specific_minute_data
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)


class WorkflowComplianceTest:
    """工作流程合规性测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        
        # 启用测试模式
        test_config.enable_test_mode()
        print("✅ 测试模式已启用")
        
    def test_step1_smart_file_selection(self) -> bool:
        """测试第1步：智能文件选择和分析"""
        print("\n🔍 [1/7] 测试智能文件选择和分析...")
        
        try:
            # 初始化智能文件选择器
            output_dir = str(MinuteDataTestConfig.INPUT_DATA_PATH)
            selector = SmartFileSelector(output_dir)
            
            # 测试文件选择
            existing_file = selector.find_best_file(
                stock_code='000617',
                data_type="minute",
                target_start='20250401',
                target_end='20250731',
                strategy='smart_comprehensive'
            )
            
            if existing_file:
                print(f"   ✅ 智能文件选择成功: {Path(existing_file).name}")
                
                # 测试文件分析
                file_info = selector.analyze_file(existing_file, '20250401', '20250731')
                if file_info:
                    print(f"   ✅ 文件分析成功: 评分={file_info.total_score}")
                    print(f"      时间范围: {file_info.start_date} ~ {file_info.end_date}")
                    print(f"      覆盖天数: {file_info.coverage_days}天")
                    return True
                else:
                    print(f"   ❌ 文件分析失败")
                    return False
            else:
                print(f"   ⚠️ 未找到合适的测试文件")
                return False
                
        except Exception as e:
            print(f"   ❌ 智能文件选择测试失败: {e}")
            return False
    
    def test_step2_incremental_prerequisite(self) -> bool:
        """测试第2步：增量下载前提条件判断"""
        print("\n🔍 [2/7] 测试增量下载前提条件判断...")
        
        try:
            # 测试统一接口
            has_prerequisite, details = check_incremental_download_prerequisite(
                existing_file='1min_0_000617_20250320-20250704_来源互联网.txt',
                stock_code='000617'
            )
            
            print(f"   📊 前提条件检查结果: {'✅ 具备' if has_prerequisite else '❌ 不具备'}")
            
            if details:
                print(f"   📋 详细信息:")
                if 'environment_info' in details:
                    env_info = details['environment_info']
                    print(f"      环境模式: {env_info.get('mode', 'unknown')}")
                    print(f"      逻辑路径: {env_info.get('logical_path', 'N/A')}")
                
                if 'conclusion' in details:
                    print(f"      结论: {details['conclusion']}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 增量下载前提条件测试失败: {e}")
            return False
    
    def test_step3_data_quality_check(self) -> bool:
        """测试第3步：数据质量检查与修复"""
        print("\n🔍 [3/7] 测试数据质量检查与修复...")
        
        try:
            # 查找测试文件
            test_files = list(MinuteDataTestConfig.INPUT_DATA_PATH.glob('test_*.txt'))
            if not test_files:
                print(f"   ⚠️ 未找到测试文件")
                return False
            
            test_file = test_files[0]
            print(f"   📄 使用测试文件: {test_file.name}")
            
            # 初始化缺失数据处理器
            processor = MissingDataProcessor()
            
            # 检测缺失数据
            missing_info = processor.detect_missing_minute_data(str(test_file), '000617')
            
            print(f"   📊 数据完整性检查:")
            print(f"      总记录数: {missing_info.get('total_records', 0)}")
            print(f"      覆盖天数: {missing_info.get('total_days', 0)}")
            print(f"      缺失状态: {'❌ 有缺失' if missing_info.get('has_missing', False) else '✅ 完整'}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 数据质量检查测试失败: {e}")
            return False
    
    def test_step4_specific_data_fetcher(self) -> bool:
        """测试第4步：特定分钟数据获取"""
        print("\n🔍 [4/7] 测试特定分钟数据获取...")
        
        try:
            # 测试特定时间点数据获取
            target_datetime = '202507041447'
            stock_code = '000617'
            
            data = get_specific_minute_data(stock_code, target_datetime)
            
            if data:
                print(f"   ✅ 特定数据获取成功:")
                print(f"      时间: {data.get('datetime', 'N/A')}")
                print(f"      收盘价: {data.get('close', 'N/A')}")
                print(f"      成交量: {data.get('volume', 'N/A')}")
                return True
            else:
                print(f"   ⚠️ 未获取到数据（可能超出pytdx覆盖范围）")
                return True  # 这是正常情况，不算失败
                
        except Exception as e:
            print(f"   ❌ 特定分钟数据获取测试失败: {e}")
            return False
    
    def test_step5_structured_downloader(self) -> bool:
        """测试第5步：结构化下载器"""
        print("\n🔍 [5/7] 测试结构化下载器...")
        
        try:
            downloader = StructuredInternetMinuteDownloader()
            
            # 测试四步处理流程（不实际执行下载）
            print(f"   ✅ 结构化下载器初始化成功")
            print(f"   📋 四步处理流程方法:")
            
            methods_to_check = [
                '_step1_smart_file_selection',
                '_step2_incremental_feasibility_check', 
                '_step3_missing_data_audit_and_repair',
                '_step4_incremental_data_download'
            ]
            
            for method_name in methods_to_check:
                if hasattr(downloader, method_name):
                    print(f"      ✅ {method_name}")
                else:
                    print(f"      ❌ {method_name} (缺失)")
                    return False
            
            return True
            
        except Exception as e:
            print(f"   ❌ 结构化下载器测试失败: {e}")
            return False
    
    def test_step6_output_formatting(self) -> bool:
        """测试第6步：输出格式化"""
        print("\n🔍 [6/7] 测试输出格式化...")
        
        try:
            # 检查数据质量标准
            standards = MinuteDataTestConfig.DATA_QUALITY_STANDARDS
            
            print(f"   ✅ 数据质量标准配置:")
            print(f"      表头格式: {standards['header_format']}")
            print(f"      字段数量: {standards['field_count']}")
            print(f"      每日分钟数: {standards['minutes_per_trading_day']}")
            print(f"      编码格式: {standards['encoding']}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 输出格式化测试失败: {e}")
            return False
    
    def test_step7_final_validation(self) -> bool:
        """测试第7步：最终验证"""
        print("\n🔍 [7/7] 测试最终验证...")
        
        try:
            # 检查验证配置
            validation_config = MinuteDataTestConfig.DATA_QUALITY_STANDARDS
            
            print(f"   ✅ 最终验证标准:")
            print(f"      股票代码格式: {validation_config['stock_code_format']}")
            print(f"      时间格式: {validation_config['time_format']}")
            print(f"      价格字段: {validation_config['price_fields']}")
            print(f"      指标字段: {validation_config['indicator_fields']}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 最终验证测试失败: {e}")
            return False
    
    def run_full_compliance_test(self) -> Dict[str, Any]:
        """运行完整的合规性测试"""
        print("🎯 1分钟数据工作流程合规性测试")
        print("=" * 60)
        
        test_steps = [
            ('智能文件选择和分析', self.test_step1_smart_file_selection),
            ('增量下载前提条件判断', self.test_step2_incremental_prerequisite),
            ('数据质量检查与修复', self.test_step3_data_quality_check),
            ('特定分钟数据获取', self.test_step4_specific_data_fetcher),
            ('结构化下载器', self.test_step5_structured_downloader),
            ('输出格式化', self.test_step6_output_formatting),
            ('最终验证', self.test_step7_final_validation)
        ]
        
        results = {}
        passed_count = 0
        
        for step_name, test_func in test_steps:
            try:
                success = test_func()
                results[step_name] = success
                if success:
                    passed_count += 1
            except Exception as e:
                print(f"   ❌ 测试步骤异常: {e}")
                results[step_name] = False
        
        # 生成总结
        total_steps = len(test_steps)
        success_rate = (passed_count / total_steps) * 100
        
        print(f"\n📊 合规性测试总结:")
        print(f"   总测试步骤: {total_steps}")
        print(f"   通过步骤: {passed_count}")
        print(f"   成功率: {success_rate:.1f}%")
        print(f"   整体评估: {'✅ 合规' if success_rate >= 85 else '❌ 不合规'}")
        
        return {
            'total_steps': total_steps,
            'passed_steps': passed_count,
            'success_rate': success_rate,
            'compliant': success_rate >= 85,
            'step_results': results
        }


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='1分钟数据工作流程合规性测试')
    parser.add_argument('--quick', action='store_true', help='快速测试模式')
    parser.add_argument('--step', type=int, help='测试特定步骤（1-7）')
    
    args = parser.parse_args()
    
    tester = WorkflowComplianceTest()
    
    if args.step:
        # 测试特定步骤
        step_methods = [
            tester.test_step1_smart_file_selection,
            tester.test_step2_incremental_prerequisite,
            tester.test_step3_data_quality_check,
            tester.test_step4_specific_data_fetcher,
            tester.test_step5_structured_downloader,
            tester.test_step6_output_formatting,
            tester.test_step7_final_validation
        ]
        
        if 1 <= args.step <= 7:
            step_methods[args.step - 1]()
        else:
            print(f"❌ 无效的步骤号: {args.step}，请使用1-7")
    else:
        # 运行完整测试
        results = tester.run_full_compliance_test()
        
        # 保存测试结果
        import json
        from datetime import datetime
        
        results_file = MinuteDataTestConfig.RESULTS_PATH / f'workflow_compliance_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        results_file.parent.mkdir(parents=True, exist_ok=True)
        
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        print(f"\n📄 测试结果已保存: {results_file}")


if __name__ == '__main__':
    main()
