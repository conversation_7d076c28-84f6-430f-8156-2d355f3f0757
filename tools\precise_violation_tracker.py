#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Precise violation tracker - find the exact source of early pytdx calls
"""

import os
import sys
import importlib.util

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# Global tracking state
violation_detected = False
violation_details = []

def install_comprehensive_monitoring():
    """Install comprehensive monitoring for all possible pytdx entry points"""
    global violation_detected, violation_details
    
    try:
        # 1. Monitor TaskManager execution points
        from src.mythquant.core.task_manager import TaskManager
        
        original_execute_minute = TaskManager._execute_minute_task
        
        def monitored_execute_minute(self, task, target_stocks):
            """Monitor TaskManager execution with precise line tracking"""
            print(f"\n=== TASKMANAGER EXECUTION START ===")
            print(f"About to execute _execute_minute_task")
            
            # Mark critical points
            print(f"CHECKPOINT 1: Before line 273 log")
            
            # Execute original method with line-by-line monitoring
            result = original_execute_minute(self, task, target_stocks)
            
            print(f"CHECKPOINT 2: After method completion")
            print(f"=== TASKMANAGER EXECUTION END ===")
            
            return result
        
        TaskManager._execute_minute_task = monitored_execute_minute
        
        # 2. Monitor ALL possible pytdx entry points
        pytdx_entry_points = [
            ('utils.pytdx_downloader', 'PytdxDownloader'),
            ('utils.stock_data_downloader', 'StockDataDownloader'),
            ('utils.incremental_downloader', 'IncrementalDownloader'),
            ('test_environments.shared.utilities.specific_minute_data_fetcher', 'SpecificMinuteDataFetcher'),
        ]
        
        for module_name, class_name in pytdx_entry_points:
            try:
                module = importlib.import_module(module_name)
                cls = getattr(module, class_name)
                
                # Monitor class instantiation
                original_init = cls.__init__
                
                def make_monitored_init(original_cls_name):
                    def monitored_init(self, *args, **kwargs):
                        import traceback
                        print(f"\n*** {original_cls_name} INSTANTIATION ***")
                        print("Instantiation stack:")
                        stack = traceback.format_stack()
                        for i, frame in enumerate(stack[-8:], 1):
                            if 'task_manager' in frame:
                                print(f">>> {i:2d}. {frame.strip()}")
                            else:
                                print(f"    {i:2d}. {frame.strip()}")
                        print("=" * 80)
                        
                        return original_init(self, *args, **kwargs)
                    return monitored_init
                
                cls.__init__ = make_monitored_init(class_name)
                
            except ImportError as e:
                print(f"Could not monitor {module_name}.{class_name}: {e}")
        
        # 3. Monitor specific methods that produce those warnings
        from utils.pytdx_downloader import PytdxDownloader
        
        original_download = PytdxDownloader.download_minute_data
        
        def monitored_download(self, stock_code, start_date, end_date, frequency='1min', suppress_warnings=False):
            """Monitor download_minute_data calls"""
            import traceback
            
            print(f"\n*** PYTDX DOWNLOAD_MINUTE_DATA CALLED ***")
            print(f"Stock: {stock_code}, Range: {start_date}-{end_date}")
            print(f"Suppress warnings: {suppress_warnings}")
            
            print(f"Call stack:")
            stack = traceback.format_stack()
            
            # Check if this is called from TaskManager area
            task_manager_in_stack = any('task_manager' in frame for frame in stack)
            structured_downloader_in_stack = any('structured_internet_minute_downloader' in frame for frame in stack)
            
            if task_manager_in_stack and not structured_downloader_in_stack:
                global violation_detected, violation_details
                violation_detected = True
                violation_details.append({
                    'method': 'download_minute_data',
                    'stock_code': stock_code,
                    'date_range': f"{start_date}-{end_date}",
                    'stack': stack,
                    'suppress_warnings': suppress_warnings
                })
                
                print(f"🚨 POTENTIAL VIOLATION DETECTED 🚨")
                print(f"Called from TaskManager but not from structured downloader!")
            
            for i, frame in enumerate(stack, 1):
                if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'structured_internet']):
                    print(f">>> {i:2d}. {frame.strip()}")
                else:
                    print(f"    {i:2d}. {frame.strip()}")
            
            print("=" * 120)
            
            return original_download(self, stock_code, start_date, end_date, frequency, suppress_warnings)
        
        PytdxDownloader.download_minute_data = monitored_download
        
        # 4. Monitor print statements (simplified approach)
        import builtins
        original_print = builtins.print

        def monitored_print(*args, **kwargs):
            """Monitor print outputs for pytdx warnings"""
            message = ' '.join(str(arg) for arg in args)
            if any(keyword in message for keyword in ['时间范围内无数据', 'pytdx未获取到', 'pytdx不可用']):
                import traceback
                print(f"\n*** PYTDX WARNING DETECTED IN PRINT ***", file=sys.stderr)
                print(f"Warning: {message}", file=sys.stderr)
                print("Warning call stack:", file=sys.stderr)
                stack = traceback.format_stack()
                for i, frame in enumerate(stack[-8:], 1):
                    print(f"    {i:2d}. {frame.strip()}", file=sys.stderr)
                print("=" * 80, file=sys.stderr)

            return original_print(*args, **kwargs)

        builtins.print = monitored_print
        
        print("Comprehensive violation monitoring installed")
        return True
        
    except Exception as e:
        print(f"Monitoring installation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_monitored_test():
    """Run test with comprehensive monitoring"""
    global violation_detected, violation_details
    
    print("\nRunning main.py with comprehensive violation monitoring...")
    
    try:
        from main import main
        result = main()
        
        print(f"\nMain completed with result: {result}")
        print(f"Violations detected: {violation_detected}")
        print(f"Total violation details: {len(violation_details)}")
        
        if violation_details:
            print(f"\n*** VIOLATION ANALYSIS ***")
            for i, violation in enumerate(violation_details, 1):
                print(f"Violation #{i}:")
                print(f"  Method: {violation['method']}")
                print(f"  Stock: {violation['stock_code']}")
                print(f"  Range: {violation['date_range']}")
                print(f"  Suppress warnings: {violation['suppress_warnings']}")
                print(f"  Stack frames: {len(violation['stack'])}")
                
                # Analyze the stack for the exact call path
                print(f"  Key call path:")
                for frame in violation['stack']:
                    if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'execute_minute']):
                        print(f"    >>> {frame.strip()}")
                print()
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Precise Workflow Violation Tracker")
    print("=" * 80)
    print("Finding the exact source of early pytdx calls")
    print("=" * 80)
    
    if not install_comprehensive_monitoring():
        return 1
    
    success = run_monitored_test()
    
    print(f"\nMonitoring completed: {'SUCCESS' if success else 'FAILED'}")
    
    if violation_detected:
        print(f"\n🚨 WORKFLOW VIOLATIONS CONFIRMED")
        print(f"Found {len(violation_details)} violation(s)")
        print("Detailed analysis above shows the exact call paths")
    else:
        print(f"\n✅ NO DIRECT VIOLATIONS DETECTED")
        print("The warnings may come from cached/delayed output")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
