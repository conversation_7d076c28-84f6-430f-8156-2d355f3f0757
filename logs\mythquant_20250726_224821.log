2025-07-26 22:48:21,080 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_224821.log
2025-07-26 22:48:21,080 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 22:48:21,100 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 22:48:21,102 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 22:48:21,102 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 22:48:21,118 - Main - INFO - info:307 - ℹ️ 开始并行测试 54 个服务器...
2025-07-26 22:48:24,119 - Main - WARNING - warning:311 - ⚠️ ❌ 深圳行情主站 (113.105.73.88:7711) - 连接失败
2025-07-26 22:48:24,119 - Main - WARNING - warning:311 - ⚠️ ❌ 深圳行情主站 (113.105.73.88:7709) - 连接失败
2025-07-26 22:48:24,120 - Main - WARNING - warning:311 - ⚠️ ❌ 北京行情主站1 (106.120.74.86:7711) - 连接失败
2025-07-26 22:48:24,135 - Main - WARNING - warning:311 - ⚠️ ❌ 广州行情主站 (119.147.171.206:443) - 连接失败
2025-07-26 22:48:24,135 - Main - WARNING - warning:311 - ⚠️ ❌ 杭州行情主站 (218.108.50.178:7711) - 连接失败
2025-07-26 22:48:24,135 - Main - WARNING - warning:311 - ⚠️ ❌ 移动行情主站 (117.184.140.156:7711) - 连接失败
2025-07-26 22:48:24,136 - Main - WARNING - warning:311 - ⚠️ ❌ 广州行情主站 (119.147.171.206:80) - 连接失败
2025-07-26 22:48:24,136 - Main - WARNING - warning:311 - ⚠️ ❌ 上海行情主站 (114.80.80.222:7711) - 连接失败
2025-07-26 22:48:27,126 - Main - WARNING - warning:311 - ⚠️ ❌ 北京行情主站2 (221.194.181.176:7711) - 连接失败
2025-07-26 22:48:27,126 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.74 (112.95.140.74:7709) - 连接失败
2025-07-26 22:48:27,127 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_106.120.74.86 (106.120.74.86:7709) - 连接失败
2025-07-26 22:48:27,141 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.19 (114.80.149.19:7709) - 连接失败
2025-07-26 22:48:27,141 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.67.61.70 (114.67.61.70:7709) - 连接失败
2025-07-26 22:48:27,142 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.22 (114.80.149.22:7709) - 连接失败
2025-07-26 22:48:27,142 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.93 (112.95.140.93:7709) - 连接失败
2025-07-26 22:48:27,142 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.92 (112.95.140.92:7709) - 连接失败
2025-07-26 22:48:27,433 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.56.198 (115.238.56.198:7709) - 0.204s
2025-07-26 22:48:27,452 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.90.165 (115.238.90.165:7709) - 0.207s
2025-07-26 22:48:30,137 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.80.222 (114.80.80.222:7709) - 连接失败
2025-07-26 22:48:30,137 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.84 (114.80.149.84:7709) - 连接失败
2025-07-26 22:48:30,152 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_117.184.140.156 (117.184.140.156:7709) - 连接失败
2025-07-26 22:48:30,152 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.147.171.206 (119.147.171.206:7709) - 连接失败
2025-07-26 22:48:30,152 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.29.51.30 (119.29.51.30:7709) - 连接失败
2025-07-26 22:48:30,152 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.147.164.60 (119.147.164.60:7709) - 连接失败
2025-07-26 22:48:30,444 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.104.70 (121.14.104.70:7709) - 连接失败
2025-07-26 22:48:30,459 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.104.72 (121.14.104.72:7709) - 连接失败
2025-07-26 22:48:30,745 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_180.153.18.170 (180.153.18.170:7709) - 0.199s
2025-07-26 22:48:32,255 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_123.125.108.23 (123.125.108.23:7709) - 连接失败
2025-07-26 22:48:32,255 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_123.125.108.24 (123.125.108.24:7709) - 连接失败
2025-07-26 22:48:33,145 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.110.194 (121.14.110.194:7709) - 连接失败
2025-07-26 22:48:33,145 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.2.7 (121.14.2.7:7709) - 连接失败
2025-07-26 22:48:33,160 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.18.17 (180.153.18.17:7709) - 连接失败
2025-07-26 22:48:33,160 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_124.160.88.183 (124.160.88.183:7709) - 连接失败
2025-07-26 22:48:33,438 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_218.75.126.9 (218.75.126.9:7709) - 0.195s
2025-07-26 22:48:33,469 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.18.171 (180.153.18.171:7709) - 连接失败
2025-07-26 22:48:33,747 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.39.51 (180.153.39.51:7709) - 连接失败
2025-07-26 22:48:33,747 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.185s
2025-07-26 22:48:34,048 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.191.117.167 (60.191.117.167:7709) - 0.198s
2025-07-26 22:48:35,258 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.50.178 (218.108.50.178:7709) - 连接失败
2025-07-26 22:48:35,258 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.47.69 (218.108.47.69:7709) - 连接失败
2025-07-26 22:48:36,150 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.98.244 (218.108.98.244:7709) - 连接失败
2025-07-26 22:48:36,165 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_221.194.181.176 (221.194.181.176:7709) - 连接失败
2025-07-26 22:48:36,165 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.9.148.108 (218.9.148.108:7709) - 连接失败
2025-07-26 22:48:36,439 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_59.173.18.69 (59.173.18.69:7709) - 连接失败
2025-07-26 22:48:36,760 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_60.28.29.69 (60.28.29.69:7709) - 连接失败
2025-07-26 22:48:37,051 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.135.142.73 (61.135.142.73:7709) - 连接失败
2025-07-26 22:48:38,270 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.152.107.168 (61.152.107.168:7721) - 连接失败
2025-07-26 22:48:38,270 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.135.142.88 (61.135.142.88:7709) - 连接失败
2025-07-26 22:48:39,160 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.152.249.56 (61.152.249.56:7709) - 连接失败
2025-07-26 22:48:39,176 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.153.144.179 (61.153.144.179:7709) - 连接失败
2025-07-26 22:48:39,176 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.153.209.138 (61.153.209.138:7709) - 连接失败
2025-07-26 22:48:39,453 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_************** (**************:7709) - 连接失败
2025-07-26 22:48:39,761 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_*************** (***************:7721) - 连接失败
2025-07-26 22:48:40,054 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_************** (**************:7721) - 连接失败
2025-07-26 22:48:40,106 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-26 22:48:40,106 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-26 22:48:40,107 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-26 22:48:40,107 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 22:48:40,107 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 22:48:40,107 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 22:48:40,108 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 22:48:40,615 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 22:48:40,615 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 22:48:40,621 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 22:48:40,622 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 22:48:40,623 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 22:48:40,623 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 22:48:40,623 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 22:48:40,623 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 22:48:40,624 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 22:48:40,629 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 22:48:40,629 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 22:48:40,629 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 22:48:40,630 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 22:48:40,636 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 22:48:41,097 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.46秒
2025-07-26 22:48:41,097 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.46秒
2025-07-26 22:48:41,097 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 22:48:41,097 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 22:48:41,097 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 22:48:41,097 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 22:48:41,097 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 22:48:41,098 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 22:48:41,098 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 22:48:41,099 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 22:48:41,099 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 22:48:41,099 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 22:48:41,100 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 22:48:41,245 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 22:48:41,245 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 22:48:41,255 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 22:48:41,255 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 22:48:41,255 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 22:48:41,255 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 22:48:41,255 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 22:48:41,255 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 22:48:41,255 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 22:48:41,258 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 22:48:41,259 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 22:48:41,259 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 22:48:41,259 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 22:48:41,265 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 22:48:42,030 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.76秒
2025-07-26 22:48:42,030 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.76秒
2025-07-26 22:48:54,964 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 22:49:22,292 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 22:49:22,292 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 22:49:22,292 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 22:49:22,301 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 22:49:22,979 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 22:49:22,979 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 22:49:22,979 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 22:49:22,979 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 22:49:22,981 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 22:49:22,982 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 22:49:22,982 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 22:49:22,982 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 22:49:22,983 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 22:49:23,039 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 22:49:23,577 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 22:49:23,595 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 22:49:23,607 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 22:49:23,628 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 22:49:23,645 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 22:49:23,665 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 22:49:23,680 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 22:49:23,698 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 22:49:23,715 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 22:49:23,727 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 22:49:23,745 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 22:49:23,762 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 22:49:23,785 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 22:49:23,804 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 22:49:23,816 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 22:49:23,833 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 22:49:23,845 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 22:49:23,864 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 22:49:23,880 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 22:49:23,896 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 22:49:23,911 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 22:49:23,912 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 22:49:29,274 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 22:49:29,275 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 22:49:29,275 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 22:49:29,275 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 22:49:29,276 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 22:49:29,278 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 22:49:29,380 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-26 22:49:29,381 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 22:49:29,381 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 22:49:29,381 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 22:49:29,381 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 22:49:29,382 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 22:49:29,382 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 22:49:29,393 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 22:49:29,554 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 22:49:30,085 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 22:49:30,307 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 22:49:30,307 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 22:49:41,884 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 22:49:41,884 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 22:49:41,885 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 22:49:41,885 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-26 22:49:41,885 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-26 22:49:41,885 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-26 22:49:41,886 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 22:49:41,900 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 60.80 秒
2025-07-26 22:49:41,900 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 22:49:41,900 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 22:49:41,900 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 22:49:41,901 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 22:49:41,903 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 22:49:42,291 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 22:49:42,306 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 22:49:42,306 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 22:49:42,306 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 22:49:42,306 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-26 22:49:42,306 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 22:49:42,306 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 22:49:42,306 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 22:49:42,306 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 22:49:42,311 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-26 22:49:42,312 - core.logging_service - INFO - verbose_log:67 - ✅ 加载智能文件选择器配置: 策略=smart_comprehensive
2025-07-26 22:49:42,312 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-26 22:49:42,312 - core.logging_service - INFO - verbose_log:67 - 🔍 发现7个候选文件，开始智能选择
2025-07-26 22:49:42,313 - core.logging_service - INFO - verbose_log:67 - 📋 候选文件分析:
2025-07-26 22:49:42,313 - core.logging_service - INFO - verbose_log:67 -   1. 1min_0_000617_20250101-20250727_来源互联网（202507262222）.txt
2025-07-26 22:49:42,313 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250101 ~ 20250727 (208天)
2025-07-26 22:49:42,313 - core.logging_service - INFO - verbose_log:67 -      文件大小: 918.7KB, 修改时间: 2025-07-26 22:21
2025-07-26 22:49:42,313 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=366.0, 覆盖度=208.0, 总分=223.0
2025-07-26 22:49:42,313 - core.logging_service - INFO - verbose_log:67 -   2. 1min_0_000617_20250301-20250705_来源互联网（202507262158）.txt
2025-07-26 22:49:42,313 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250301 ~ 20250705 (127天)
2025-07-26 22:49:42,314 - core.logging_service - INFO - verbose_log:67 -      文件大小: 932.2KB, 修改时间: 2025-07-26 21:31
2025-07-26 22:49:42,314 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=344.0, 覆盖度=127.0, 总分=172.3
2025-07-26 22:49:42,314 - core.logging_service - INFO - verbose_log:67 -   3. 1min_0_000617_20250303-20250704_来源互联网（202507262234）.txt
2025-07-26 22:49:42,314 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250303 ~ 20250704 (124天)
2025-07-26 22:49:42,314 - core.logging_service - INFO - verbose_log:67 -      文件大小: 929.5KB, 修改时间: 2025-07-26 22:36
2025-07-26 22:49:42,314 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=343.0, 覆盖度=124.0, 总分=170.4
2025-07-26 22:49:42,314 - core.logging_service - INFO - verbose_log:67 -   4. 1min_0_000617_20250401-20250701_来源互联网（202507262158）.txt
2025-07-26 22:49:42,314 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250401 ~ 20250701 (92天)
2025-07-26 22:49:42,315 - core.logging_service - INFO - verbose_log:67 -      文件大小: 657.7KB, 修改时间: 2025-07-26 21:12
2025-07-26 22:49:42,315 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=340.0, 覆盖度=92.0, 总分=152.1
2025-07-26 22:49:42,315 - core.logging_service - INFO - verbose_log:67 -   5. 1min_0_000617_20250401-20250726_来源互联网（202507262113）.txt
2025-07-26 22:49:42,315 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250401 ~ 20250726 (117天)
2025-07-26 22:49:42,316 - core.logging_service - INFO - verbose_log:67 -      文件大小: 866.4KB, 修改时间: 2025-07-26 15:40
2025-07-26 22:49:42,316 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=365.0, 覆盖度=117.0, 总分=173.2
2025-07-26 22:49:42,316 - core.logging_service - INFO - verbose_log:67 -   6. 1min_0_000617_20250416-20250703_来源互联网（202507262158）.txt
2025-07-26 22:49:42,316 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250416 ~ 20250703 (79天)
2025-07-26 22:49:42,317 - core.logging_service - INFO - verbose_log:67 -      文件大小: 571.3KB, 修改时间: 2025-07-26 17:03
2025-07-26 22:49:42,317 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=342.0, 覆盖度=79.0, 总分=145.6
2025-07-26 22:49:42,317 - core.logging_service - INFO - verbose_log:67 -   7. 1min_0_000617_20250703-20250703_来源互联网（202507262158）.txt
2025-07-26 22:49:42,317 - core.logging_service - INFO - verbose_log:67 -      时间范围: 20250703 ~ 20250703 (1天)
2025-07-26 22:49:42,318 - core.logging_service - INFO - verbose_log:67 -      文件大小: 10.1KB, 修改时间: 2025-07-26 21:06
2025-07-26 22:49:42,318 - core.logging_service - INFO - verbose_log:67 -      评分: 新鲜度=342.0, 覆盖度=1.0, 总分=103.1
2025-07-26 22:49:42,318 - core.logging_service - INFO - verbose_log:67 - ✅ 选择文件: 1min_0_000617_20250101-20250727_来源互联网（202507262222）.txt (策略: smart_comprehensive, 评分: 223.00)
2025-07-26 22:49:42,318 - core.logging_service - INFO - verbose_log:67 - 🎯 选择原因 (策略: smart_comprehensive):
2025-07-26 22:49:42,318 - core.logging_service - INFO - verbose_log:67 -   综合评分最高: 223.00
2025-07-26 22:49:42,319 - core.logging_service - INFO - verbose_log:67 -     新鲜度: 366.0/365
2025-07-26 22:49:42,319 - core.logging_service - INFO - verbose_log:67 -     覆盖度: 208.0天
2025-07-26 22:49:42,319 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250101-20250727_来源互联网（202507262222）.txt
2025-07-26 22:49:42,319 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250101-20250727_来源互联网（202507262222）.txt
2025-07-26 22:49:42,319 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-26 22:49:42,319 - core.logging_service - INFO - verbose_log:67 - 📊 分析现有文件时间跨度: 1min_0_000617_20250101-20250727_来源互联网（202507262222）.txt
2025-07-26 22:49:42,321 - core.logging_service - INFO - verbose_log:67 - 📅 现有文件时间跨度: 20250303 ~ 20250703 (20104条记录)
2025-07-26 22:49:42,321 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-26 22:49:42,322 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250703
2025-07-26 22:49:42,322 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-26 22:49:42,322 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-26 22:49:42,322 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-26 22:49:42,322 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-26 22:49:42,322 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-26 22:49:42,322 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-26 22:49:42,322 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-26 22:49:42,323 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250704 ~ 20250727
2025-07-26 22:49:42,323 - core.logging_service - INFO - verbose_log:67 - 🔍 验证最后一条记录的数据一致性
2025-07-26 22:49:42,324 - core.logging_service - INFO - verbose_log:67 - 📋 文件最后记录: 时间=202507031404, 前复权价=7.32
2025-07-26 22:49:42,324 - core.logging_service - INFO - verbose_log:67 - 🔄 从API获取 20250703 的数据进行比对验证
2025-07-26 22:49:42,324 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 22:49:42,325 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 22:49:42,325 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250703 - 20250703
2025-07-26 22:49:42,325 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:42,506 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:42,506 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4896条 (目标日期: 20250703, 交易日: 17天, 频率: 1min)
2025-07-26 22:49:42,506 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4896条 -> 实际请求800条 (配置限制:800)
2025-07-26 22:49:42,507 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 22:49:42,507 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 22:49:42,559 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4096条
2025-07-26 22:49:42,560 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:42,744 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:42,794 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 22:49:42,794 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:42,967 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:43,018 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 22:49:43,018 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:43,197 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:43,251 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 22:49:43,251 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:43,436 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:43,490 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 22:49:43,490 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:43,660 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:43,712 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 22:49:43,712 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:43,888 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:43,934 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +96条，总计4896条
2025-07-26 22:49:43,934 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4896条数据
2025-07-26 22:49:43,947 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-26 22:49:43,950 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-26 22:49:43,950 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 22:49:43,953 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-26 22:49:43,953 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 22:49:43,953 - Main - INFO - info:307 - ℹ️   日期: 202507030931
2025-07-26 22:49:43,953 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-26 22:49:43,954 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-26 22:49:43,954 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 22:49:43,954 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-26 22:49:43,954 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 22:49:43,954 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 22:49:43,954 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 22:49:43,957 - core.logging_service - INFO - verbose_log:67 - 📋 API对应记录: 时间=202507031404, 前复权价=7.32
2025-07-26 22:49:43,957 - core.logging_service - INFO - verbose_log:67 - 📊 数据一致性比对结果:
2025-07-26 22:49:43,957 - core.logging_service - INFO - verbose_log:67 -   时间比对: 文件=202507031404 vs API=202507031404 -> ✅ 一致
2025-07-26 22:49:43,957 - core.logging_service - INFO - verbose_log:67 -   价格比对: 文件=7.32 vs API=7.32 -> ✅ 一致
2025-07-26 22:49:43,958 - core.logging_service - INFO - verbose_log:67 - 🎯 最终判断: ✅ 数据一致，可以使用增量下载
2025-07-26 22:49:43,958 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-26 22:49:43,960 - core.logging_service - INFO - verbose_log:67 - 获取最后一条记录: 时间=202507031404, 前复权价=7.32
2025-07-26 22:49:43,960 - core.logging_service - INFO - verbose_log:67 - 时间格式对比: 文件=202507031404, API=202507031404, 级别=1min
2025-07-26 22:49:43,960 - core.logging_service - INFO - verbose_log:67 - ✅ 时间格式一致性验证通过
2025-07-26 22:49:43,961 - core.logging_service - INFO - verbose_log:67 - ✅ 价格一致性验证通过
2025-07-26 22:49:43,961 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-26 22:49:43,961 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250101 - 20250727
2025-07-26 22:49:43,961 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-26 22:49:43,961 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250703
2025-07-26 22:49:43,961 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-26 22:49:43,961 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-26 22:49:43,962 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-26 22:49:43,962 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-26 22:49:43,962 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-26 22:49:43,962 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-26 22:49:43,962 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-26 22:49:43,962 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250704 ~ 20250727
2025-07-26 22:49:43,962 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250302
2025-07-26 22:49:43,962 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250704 - 20250727
2025-07-26 22:49:43,974 - Main - INFO - info:307 - ℹ️ 读取现有数据: 20104 条记录
2025-07-26 22:49:43,975 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250302
2025-07-26 22:49:43,975 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 22:49:43,975 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 22:49:43,975 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250302
2025-07-26 22:49:43,975 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:44,152 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:44,152 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 42624条 (目标日期: 20250101, 交易日: 148天, 频率: 1min)
2025-07-26 22:49:44,152 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计42624条 -> 实际请求800条 (配置限制:800)
2025-07-26 22:49:44,152 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 22:49:44,153 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 22:49:44,205 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需41824条
2025-07-26 22:49:44,206 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:44,372 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:44,422 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 22:49:44,422 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:44,586 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:44,640 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 22:49:44,640 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:44,818 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:44,870 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 22:49:44,870 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:45,051 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:45,105 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 22:49:45,105 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:45,284 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:45,338 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 22:49:45,338 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:45,511 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:45,564 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 22:49:45,564 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:45,733 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:45,783 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 22:49:45,783 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:45,960 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:46,013 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 22:49:46,013 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:46,185 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:46,236 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 22:49:46,236 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:46,412 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:46,464 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 22:49:46,464 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:46,652 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:46,704 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 22:49:46,704 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:46,881 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:46,933 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 22:49:46,934 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:47,104 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:47,155 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 22:49:47,155 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:47,334 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:47,387 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 22:49:47,387 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:47,559 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:47,609 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 22:49:47,610 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:47,784 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:47,835 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 22:49:47,835 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:48,003 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:48,053 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 22:49:48,054 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:48,233 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:48,286 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 22:49:48,286 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:48,456 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:48,507 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 22:49:48,507 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:48,688 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:48,740 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 22:49:48,740 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:48,926 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:48,979 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 22:49:48,979 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:49,152 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:49,202 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 22:49:49,202 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:49,383 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:49,437 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 22:49:49,438 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:49,613 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:49,663 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 22:49:49,663 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:49,844 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:49,898 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 22:49:49,898 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:50,084 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:50,137 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 22:49:50,137 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:50,316 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:50,368 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 22:49:50,368 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:50,537 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:50,588 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 22:49:50,588 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:50,764 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:50,817 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 22:49:50,817 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:50,998 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:51,047 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 22:49:51,047 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要42624条，实际获取24000条
2025-07-26 22:49:51,047 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约18624条数据（约77个交易日）
2025-07-26 22:49:51,047 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-26 22:49:51,047 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-26 22:49:51,106 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250101 - 20250302
2025-07-26 22:49:51,111 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-26 22:49:51,112 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-26 22:49:51,112 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250704 - 20250727
2025-07-26 22:49:51,112 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 22:49:51,112 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 22:49:51,112 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250727
2025-07-26 22:49:51,112 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:51,296 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:51,296 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4608条 (目标日期: 20250704, 交易日: 16天, 频率: 1min)
2025-07-26 22:49:51,297 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4608条 -> 实际请求800条 (配置限制:800)
2025-07-26 22:49:51,297 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 22:49:51,297 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 22:49:51,349 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3808条
2025-07-26 22:49:51,350 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:51,530 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:51,581 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 22:49:51,581 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:51,757 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:51,809 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 22:49:51,810 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:51,990 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:52,041 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 22:49:52,041 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:52,223 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:52,276 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 22:49:52,277 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 22:49:52,459 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 22:49:52,511 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +608条，总计4608条
2025-07-26 22:49:52,511 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4608条数据
2025-07-26 22:49:52,526 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 3840 条 1min 数据
2025-07-26 22:49:52,531 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共3840条记录
2025-07-26 22:49:52,531 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 22:49:52,560 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-26 22:49:52,561 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 22:49:52,562 - Main - INFO - info:307 - ℹ️   日期: 202507040931
2025-07-26 22:49:52,562 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-26 22:49:52,562 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-26 22:49:52,562 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 22:49:52,562 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 3840 >= 5
2025-07-26 22:49:52,562 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 22:49:52,562 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 22:49:52,563 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 22:49:52,563 - Main - INFO - info:307 - ℹ️ 获得新数据: 3840 条记录
2025-07-26 22:49:52,567 - Main - WARNING - warning:311 - ⚠️ 时间列数据类型统一失败: 'SmartLogger' object has no attribute 'verbose_log'
2025-07-26 22:49:52,585 - Main - INFO - info:307 - ℹ️ 合并后数据: 23944 条记录
2025-07-26 22:49:52,589 - core.logging_service - INFO - verbose_log:67 - 📝 时间范围变化，需要重命名:
2025-07-26 22:49:52,590 - core.logging_service - INFO - verbose_log:67 -   原始: 20250101-20250727
2025-07-26 22:49:52,590 - core.logging_service - INFO - verbose_log:67 -   更新: 202503030931-202507251500
2025-07-26 22:49:52,590 - core.logging_service - INFO - verbose_log:67 -   新文件名: 1min_0_000617_202503030931-202507251500_来源互联网（202507262249）.txt
2025-07-26 22:49:52,662 - Main - INFO - info:307 - ℹ️ 删除旧文件: 1min_0_000617_20250101-20250727_来源互联网（202507262222）.txt
2025-07-26 22:49:52,663 - Main - INFO - info:307 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503030931-202507251500_来源互联网（202507262249）.txt
2025-07-26 22:49:52,663 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-26 22:49:52,664 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 22:49:52,664 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 22:49:52,664 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-26 22:49:52,664 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-26 22:49:52,664 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 22:49:52,664 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 22:49:52,665 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 22:49:52,665 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 22:49:52,665 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 22:49:52,665 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 22:49:52,666 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: day_0_000617_20240701-20240731_来源互联网（202507262158）.txt
2025-07-26 22:49:52,685 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20200101-20250724.txt, 数据行数: 1337
2025-07-26 22:49:52,686 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20240701-20240731_来源互联网（202507262158）.txt, 数据行数: 23
2025-07-26 22:49:52,688 - Main - INFO - info:307 - ℹ️ 数据对齐完成，共同日期数: 23
2025-07-26 22:49:52,692 - Main - INFO - info:307 - ℹ️ 价格差异计算完成，数据点数: 23
2025-07-26 22:49:52,696 - Main - INFO - info:307 - ℹ️ 股票 000617 数据比较完成
2025-07-26 22:49:52,696 - Main - INFO - info:307 - ℹ️ ✅ 000617 分析完成
2025-07-26 22:49:52,699 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_224952.txt
2025-07-26 22:49:52,699 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 1/1 成功 (100.0%)
2025-07-26 22:49:52,699 - Main - INFO - info:307 - ℹ️ 任务执行成功: 前复权数据比较分析
2025-07-26 22:49:52,700 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 100.0%
2025-07-26 22:49:52,700 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 22:49:52,700 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 22:49:52,700 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 22:49:52,701 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 22:49:52,701 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 22:49:52,701 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 22:49:52,701 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
