#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试方法是否存在
"""

def test_method_exists():
    """测试方法是否存在"""
    print("🔍 测试TaskManager方法是否存在")
    print("=" * 50)
    
    try:
        # 导入必要模块
        import sys
        from pathlib import Path
        
        # 添加路径
        project_root = Path(__file__).parent
        sys.path.insert(0, str(project_root))
        
        # 导入任务管理器
        from src.mythquant.core.task_manager import TaskManager
        from src.mythquant.core.stock_processor import StockDataProcessor
        
        # 创建实例
        stock_processor = StockDataProcessor("")
        task_manager = TaskManager(stock_processor)
        
        print(f"✅ TaskManager实例创建成功")
        
        # 检查方法是否存在
        methods_to_check = [
            '_execute_minute_task',
            '_execute_internet_minute_task',
            '_execute_daily_task',
            '_execute_internet_daily_task'
        ]
        
        for method_name in methods_to_check:
            if hasattr(task_manager, method_name):
                print(f"   ✅ 方法存在: {method_name}")
            else:
                print(f"   ❌ 方法缺失: {method_name}")
        
        # 特别检查internet_minute_task方法
        if hasattr(task_manager, '_execute_internet_minute_task'):
            method = getattr(task_manager, '_execute_internet_minute_task')
            print(f"   📊 _execute_internet_minute_task详情:")
            print(f"      类型: {type(method)}")
            print(f"      可调用: {callable(method)}")
            return True
        else:
            print(f"   ❌ _execute_internet_minute_task方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = test_method_exists()
    if success:
        print(f"\n🎉 方法存在，问题可能在其他地方")
    else:
        print(f"\n⚠️ 方法不存在，需要添加")
