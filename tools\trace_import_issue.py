#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
追踪导入时的意外执行

找出在导入StructuredInternetMinuteDownloader时是否有意外的代码执行
"""

import os
import sys

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def trace_import():
    """追踪导入过程"""
    print("🔍 开始追踪导入过程")
    print("=" * 80)
    
    print("📋 步骤1: 导入前的状态")
    print("   - 准备导入StructuredInternetMinuteDownloader")
    
    print("\n📋 步骤2: 执行导入")
    try:
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        print("   ✅ 导入成功")
    except Exception as e:
        print(f"   ❌ 导入失败: {e}")
        return False
    
    print("\n📋 步骤3: 创建实例")
    try:
        downloader = StructuredInternetMinuteDownloader()
        print("   ✅ 实例创建成功")
    except Exception as e:
        print(f"   ❌ 实例创建失败: {e}")
        return False
    
    print("\n📋 步骤4: 检查实例属性")
    print(f"   - 类型: {type(downloader)}")
    print(f"   - 属性: {[attr for attr in dir(downloader) if not attr.startswith('_')]}")
    
    return True

def main():
    """主函数"""
    print("🚀 追踪StructuredInternetMinuteDownloader导入问题")
    print("=" * 120)
    
    success = trace_import()
    
    print(f"\n🎯 追踪结果")
    print("=" * 80)
    
    if success:
        print("✅ 导入和实例化过程正常")
        print("💡 如果有意外输出，说明问题可能在其他地方")
    else:
        print("❌ 导入或实例化过程有问题")
    
    print("\n💡 建议:")
    print("   1. 检查是否有其他模块在导入时执行了数据处理")
    print("   2. 检查是否有全局变量初始化触发了处理逻辑")
    print("   3. 检查是否有装饰器或元类在类定义时执行了代码")

if __name__ == '__main__':
    main()
