🔍 代码质量检查报告
==================================================

📊 扫描统计:
   扫描文件数: 416
   发现问题数: 504

🚨 高严重性问题 (237个):
   ❌ 函数 'format_date' 在第 [19, 59, 114] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\test_date_format_fix.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_data_file' 在第 [133, 291] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\environment_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_output_directory' 在第 [157, 301] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\environment_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'verbose_log' 在第 [36, 140] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_step' 在第 [64, 143] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_performance_warning' 在第 [77, 147] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_forward_adj_detail' 在第 [86, 151] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_cache_status' 在第 [95, 155] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_critical_info' 在第 [104, 159] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [17, 292] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\ui\progress.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [30, 358] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\async_io_processor.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [29, 320] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\chunk_processor.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [45, 304] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\enhanced_error_handler.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'decorator' 在第 [179, 216] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\enhanced_error_handler.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'wrapper' 在第 [181, 218] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\enhanced_error_handler.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [24, 234] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\process_flow_optimizer.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'wrapper' 在第 [183, 210] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\process_flow_optimizer.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'notify_data_integrity_check' 在第 [50, 318] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\result_notifier.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'notify_price_consistency_check' 在第 [114, 323] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\result_notifier.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'notify_missing_data_repair' 在第 [170, 328] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\result_notifier.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'notify_final_validation' 在第 [241, 333] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\result_notifier.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_stage_header' 在第 [26, 151] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\stage_separator.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_stage_footer' 在第 [50, 156] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\stage_separator.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_sub_stage' 在第 [84, 161] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\stage_separator.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'should_stop_program' 在第 [124, 166] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\stage_separator.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_banner' 在第 [133, 258] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_main_process' 在第 [143, 275] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_sub_process' 在第 [150, 279] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_step' 在第 [156, 283] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_action' 在第 [162, 315] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_result' 在第 [167, 287] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_info' 在第 [173, 291] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_warning' 在第 [178, 295] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_error' 在第 [183, 299] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_data_info' 在第 [195, 311] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_stats_table' 在第 [205, 307] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_completion' 在第 [232, 303] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'count_trading_days_to_now' 在第 [117, 250] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\trading_days_calculator.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'calculate_data_count_needed' 在第 [189, 263] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\trading_days_calculator.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [24, 93, 177] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\unified_interfaces.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'check_price_consistency' 在第 [30, 268] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\unified_interfaces.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_file_path' 在第 [99, 279] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\unified_interfaces.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'check_incremental_download_prerequisite' 在第 [183, 273] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\utils\unified_interfaces.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'from_entity' 在第 [61, 144, 258] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\application\config\dto\config_dto.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'to_entity' 在第 [79, 165, 296] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\application\config\dto\config_dto.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_task_configs' 在第 [64, 104] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\config\__init__.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_data_file' 在第 [133, 291] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\environment_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_output_directory' 在第 [157, 301] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\environment_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'verbose_log' 在第 [36, 141] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_step' 在第 [65, 144] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_performance_warning' 在第 [78, 148] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_forward_adj_detail' 在第 [87, 152] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_cache_status' 在第 [96, 156] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_critical_info' 在第 [105, 160] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\logging_service.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [25, 144] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\task_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'process_stock_chunk' 在第 [512, 1355] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\core\task_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [14, 41, 54, 76, 92, 112, 128, 138, 148, 164, 178, 199, 218, 243, 261, 281, 303, 322, 340, 362, 377, 391] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\exceptions.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__post_init__' 在第 [45, 83, 98] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\aggregates\market.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'is_trading_time' 在第 [54, 349] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\aggregates\market.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__post_init__' 在第 [37, 57, 102] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\aggregates\stock.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__post_init__' 在第 [23, 46, 69, 91, 114, 136, 159, 183, 206, 230, 254, 279] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\events\market_events.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__post_init__' 在第 [24, 50, 103, 124, 145, 168, 192, 216, 240, 263, 287, 314] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\events\stock_events.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__post_init__' 在第 [21, 88, 136, 171, 199] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\identifiers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'from_string' 在第 [43, 100, 158, 186, 231] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\identifiers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__str__' 在第 [78, 126, 161, 189, 234] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\identifiers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'generate' 在第 [151, 218] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\identifiers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__post_init__' 在第 [22, 138, 209, 314] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'from_float' 在第 [35, 219, 324] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'from_string' 在第 [40, 150, 224, 329] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'zero' 在第 [48, 158, 250, 337] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'add' 在第 [51, 161, 253, 340] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'subtract' 在第 [57, 165, 257, 346] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'multiply' 在第 [66, 172, 261, 352] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'divide' 在第 [75, 358] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'is_zero' 在第 [96, 179, 275, 366] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'is_positive' 在第 [100, 183, 279, 370] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'to_float' 在第 [104, 271, 382] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__str__' 在第 [108, 187, 291, 386] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__lt__' 在第 [111, 190, 294, 389] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__le__' 在第 [116, 193, 297, 394] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__gt__' 在第 [121, 196, 300, 399] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__ge__' 在第 [126, 199, 303, 404] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'is_negative' 在第 [283, 374] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'abs' 在第 [287, 378] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\measurements.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'display_name' 在第 [30, 50, 70, 98, 123, 156] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\trading.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__post_init__' 在第 [214, 271] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\domain\value_objects\trading.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'decorator' 在第 [47, 124, 171, 216, 256, 276, 380] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_decorators.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'wrapper' 在第 [64, 129, 175, 220, 384] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_decorators.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [72, 228, 275, 314] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get' 在第 [85, 283] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'set' 在第 [105, 287] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'delete' 在第 [125, 291] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'exists' 在第 [141, 295] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_or_set' 在第 [160, 305] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get' 在第 [26, 71, 240, 380, 552] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_providers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'set' 在第 [31, 92, 262, 412, 567] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_providers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'delete' 在第 [36, 118, 286, 440, 579] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_providers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'exists' 在第 [41, 127, 305, 455, 591] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_providers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'clear' 在第 [46, 140, 317, 476, 598] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_providers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_stats' 在第 [51, 146, 332, 487, 606] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_providers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [58, 196, 360, 542] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_providers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '_is_expired' 在第 [160, 505] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\caching\cache_providers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [31, 136, 453] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\database\connection_pool.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'connection' 在第 [43, 293] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\database\connection_pool.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_connection' 在第 [114, 177] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\database\connection_pool.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'return_connection' 在第 [119, 224] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\database\connection_pool.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'close_all' 在第 [124, 247] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\database\connection_pool.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_stats' 在第 [129, 266] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\database\connection_pool.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'subscribe' 在第 [29, 64, 230] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'unsubscribe' 在第 [34, 80, 244] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'publish' 在第 [39, 102, 285] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'publish_batch' 在第 [44, 131, 310] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [51, 218] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'subscribe_all' 在第 [72, 237] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'add_middleware' 在第 [96, 251] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '_get_handlers' 在第 [161, 337] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_metrics' 在第 [189, 361] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'clear_all' 在第 [199, 370] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_bus.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'dispatch' 在第 [36, 82, 287] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_dispatcher.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'dispatch_batch' 在第 [41, 116, 314] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_dispatcher.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'start' 在第 [46, 143, 331] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_dispatcher.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'stop' 在第 [51, 153, 348] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_dispatcher.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [58, 234] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_dispatcher.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '_get_event_handlers' 在第 [183, 370] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_dispatcher.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_statistics' 在第 [221, 425] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_dispatcher.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'can_handle' 在第 [34, 58, 292] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_handlers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'handle' 在第 [39, 62, 315] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_handlers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'handler_name' 在第 [45, 90, 330] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_handlers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [52, 107, 136, 185, 219, 252, 287, 337] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_handlers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '_handle_event' 在第 [85, 110, 140, 188, 222, 255] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_handlers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '_send_market_notification' 在第 [211, 244] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_handlers.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [30, 66, 201, 366] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_sourcing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'create_snapshot' 在第 [110, 324] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_sourcing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'save' 在第 [184, 209] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_sourcing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'load' 在第 [189, 235] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_sourcing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'exists' 在第 [194, 269] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_sourcing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [30, 115, 199, 330] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_store.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'append_event' 在第 [74, 122, 209, 359] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_store.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'append_events' 在第 [79, 143, 225, 379] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_store.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_events' 在第 [84, 151, 246, 405] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_store.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_events_by_type' 在第 [90, 159, 256, 418] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_store.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_all_events' 在第 [97, 169, 269, 433] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_store.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_event_count' 在第 [103, 177, 280, 447] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_store.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_last_sequence_number' 在第 [108, 182, 291, 453] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\events\event_store.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'condition' 在第 [218, 234, 250] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\alerting.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'add_span' 在第 [57, 296] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\apm.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [110, 254, 378] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\apm.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'start' 在第 [126, 384] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\apm.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'stop' 在第 [137, 393] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\apm.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'wrapper' 在第 [439, 453] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\apm.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [50, 70, 105, 147, 228, 340, 386, 403, 472] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\metrics.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'collect' 在第 [58, 87, 129, 184, 292, 370] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\metrics.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'reset' 在第 [63, 96, 138, 216, 323, 377] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\metrics.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'inc' 在第 [74, 114] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\metrics.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_value' 在第 [82, 124] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\metrics.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'observe' 在第 [157, 239, 365] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\metrics.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_count' 在第 [169, 250] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\metrics.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_sum' 在第 [174, 255] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\metrics.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'decorator' 在第 [196, 210] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\profiling.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'wrapper' 在第 [200, 214] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\profiling.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'add_baggage' 在第 [39, 447] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\tracing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_baggage' 在第 [43, 454] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\tracing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '_generate_span_id' 在第 [49, 351] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\tracing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'to_dict' 在第 [52, 113] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\tracing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [132, 150] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\tracing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'start_span' 在第 [185, 431] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\tracing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'decorator' 在第 [361, 391] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\infrastructure\monitoring\tracing.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [19, 118] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\logging.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_info' 在第 [89, 121] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\logging.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_warning' 在第 [95, 124] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\logging.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_error' 在第 [101, 127] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\logging.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'log_debug' 在第 [107, 130] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\logging.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [24, 60, 91, 137, 181, 199, 270, 282, 294, 391, 419, 457, 484, 512, 567, 613, 637, 672, 692, 722, 738, 761] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\architectural_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'subscribe' 在第 [94, 571] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\architectural_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'register' 在第 [184, 202] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\architectural_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'is_satisfied_by' 在第 [251, 274, 286, 297] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\architectural_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'state' 在第 [728, 743, 747] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\architectural_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__post_init__' 在第 [34, 50] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\financial_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_indicator_name' 在第 [82, 105, 147] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\financial_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [89, 112, 154, 193, 253, 333, 359, 383, 438, 550] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\financial_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_strategy_name' 在第 [186, 246, 293] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\patterns\financial_patterns.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'calculate' 在第 [102, 117, 138] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_name' 在第 [107, 128, 172] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [114, 135, 179, 241, 296, 311] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'is_available' 在第 [208, 220, 233] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_price' 在第 [260, 270] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'calculate_ma' 在第 [261, 275] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'save_to_database' 在第 [262, 280] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'send_email_alert' 在第 [263, 285] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'generate_pdf_report' 在第 [264, 290] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\principles\solid_principles.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'score' 在第 [54, 97] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\quality\quality_framework.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'level' 在第 [68, 108] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\quality\quality_framework.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [210, 497] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\shared\quality\quality_framework.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'assert_dataframe_equal' 在第 [21, 284] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\tests\utils.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'assert_price_precision' 在第 [74, 287] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\tests\utils.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'assert_file_exists' 在第 [104, 291] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\tests\utils.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'assert_output_format' 在第 [130, 295] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\src\mythquant\tests\utils.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'setUp' 在第 [27, 168] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tests\test_integration.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'tearDown' 在第 [33, 174] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tests\test_integration.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'main' 在第 [186, 287] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tests\test_new_architecture_functionality.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'benchmark' 在第 [80, 178, 281, 369] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tests\performance\test_system_performance.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_fixture_path' 在第 [100, 327] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\test_environments\fixtures\fixture_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'prepare_fresh_fixture' 在第 [245, 332] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\test_environments\fixtures\fixture_manager.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [39, 131] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\test_environments\shared\utilities\performance_benchmark.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'get_price_at_time' 在第 [78, 242] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\test_environments\shared\utilities\specific_minute_data_fetcher.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [102, 121, 148] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tools\migrate_core_modules.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'cleanup' 在第 [108, 133] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tools\migrate_core_modules.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'main' 在第 [349, 545] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tools\prepare_deployment.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [91, 142, 206, 340, 431] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tools\quality_gates.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '_check' 在第 [134, 145, 209, 343] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tools\quality_gates.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [33, 206] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\tools\migration\config_migration_adapter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [45, 304] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\enhanced_error_handler.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'decorator' 在第 [179, 216] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\enhanced_error_handler.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'wrapper' 在第 [181, 218] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\enhanced_error_handler.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [24, 234] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\process_flow_optimizer.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'wrapper' 在第 [183, 210] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\process_flow_optimizer.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'notify_data_integrity_check' 在第 [50, 318] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\result_notifier.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'notify_price_consistency_check' 在第 [114, 323] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\result_notifier.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'notify_missing_data_repair' 在第 [170, 328] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\result_notifier.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'notify_final_validation' 在第 [241, 333] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\result_notifier.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_banner' 在第 [133, 299] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_main_process' 在第 [143, 316] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_sub_process' 在第 [150, 320] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_step' 在第 [156, 324] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_action' 在第 [162, 356] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_result' 在第 [167, 328] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_info' 在第 [173, 332] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_warning' 在第 [178, 336] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_error' 在第 [183, 340] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_data_info' 在第 [195, 352] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_stats_table' 在第 [205, 348] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'print_completion' 在第 [232, 344] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\structured_output_formatter.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'count_trading_days_to_now' 在第 [117, 250] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\trading_days_calculator.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'calculate_data_count_needed' 在第 [189, 263] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\trading_days_calculator.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 '__init__' 在第 [15, 117] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\unified_interfaces.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'check_incremental_download_prerequisite' 在第 [48, 82] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\unified_interfaces.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名
   ❌ 函数 'process_missing_data_for_file' 在第 [122, 151] 行重复定义
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\utils\unified_interfaces.py
      影响: 可能导致Python名称解析错误和变量作用域问题
      解决: 删除重复定义或使用不同的函数名

⚠️ 中等严重性问题 (267个):
   ⚠️ 函数 'MythQuantApplication' 从多个模块导入: ['mythquant.core', 'core.application', 'mythquant.core']
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\main.py
   ⚠️ 函数 'ConfigManager' 从多个模块导入: ['core.config_manager', 'src.mythquant.core.config_manager']
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\main.py
   ⚠️ 函数 'format_date' 存在重复定义且被调用，可能导致作用域问题
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\test_date_format_fix.py
   ⚠️ 函数 'get_output_directory' 存在重复定义且被调用，可能导致作用域问题
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\environment_manager.py
   ⚠️ 函数 'get_data_file' 存在重复定义且被调用，可能导致作用域问题
      文件: C:\Users\<USER>\PycharmProjects\MythQuant\tools\..\archive\backups\architecture_migration_final_archive_20250802_233447\legacy_code\core\environment_manager.py