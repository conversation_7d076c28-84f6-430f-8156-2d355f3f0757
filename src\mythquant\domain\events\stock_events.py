#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票领域事件

股票相关的业务事件定义
"""

from dataclasses import dataclass
from datetime import datetime, date
from decimal import Decimal
from typing import Optional
from ...shared.patterns.architectural_patterns import DomainEvent


@dataclass
class StockCreated(DomainEvent):
    """股票创建事件"""
    stock_id: str
    stock_code: str
    market_code: str
    stock_name: str
    created_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"stock_created_{self.stock_id}_{int(self.created_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockCreated",
            occurred_at=self.created_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'market_code': self.market_code,
                'stock_name': self.stock_name
            }
        )


@dataclass  
class StockPriceChanged(DomainEvent):
    """股票价格变化事件"""
    stock_id: str
    stock_code: str
    old_price: Optional[Decimal]
    new_price: Decimal
    price_date: date
    volume: int
    changed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"price_changed_{self.stock_id}_{int(self.changed_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockPriceChanged",
            occurred_at=self.changed_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'old_price': str(self.old_price) if self.old_price else None,
                'new_price': str(self.new_price),
                'price_date': self.price_date.isoformat(),
                'volume': self.volume,
                'price_change': str(self.new_price - self.old_price) if self.old_price else None,
                'price_change_percentage': str(
                    (self.new_price - self.old_price) / self.old_price * 100
                ) if self.old_price and self.old_price > 0 else None
            }
        )
    
    @property
    def price_change(self) -> Optional[Decimal]:
        """价格变化"""
        if self.old_price is None:
            return None
        return self.new_price - self.old_price
    
    @property
    def price_change_percentage(self) -> Optional[Decimal]:
        """价格变化百分比"""
        if self.old_price is None or self.old_price == 0:
            return None
        return (self.new_price - self.old_price) / self.old_price * 100
    
    @property
    def is_price_increase(self) -> bool:
        """价格是否上涨"""
        return self.old_price is not None and self.new_price > self.old_price
    
    @property
    def is_price_decrease(self) -> bool:
        """价格是否下跌"""
        return self.old_price is not None and self.new_price < self.old_price


@dataclass
class StockSuspended(DomainEvent):
    """股票停牌事件"""
    stock_id: str
    stock_code: str
    reason: str
    suspended_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"stock_suspended_{self.stock_id}_{int(self.suspended_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockSuspended",
            occurred_at=self.suspended_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'reason': self.reason
            }
        )


@dataclass
class StockResumed(DomainEvent):
    """股票复牌事件"""
    stock_id: str
    stock_code: str
    resumed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"stock_resumed_{self.stock_id}_{int(self.resumed_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockResumed",
            occurred_at=self.resumed_at,
            version=1,
            data={
                'stock_code': self.stock_code
            }
        )


@dataclass
class StockDelisted(DomainEvent):
    """股票退市事件"""
    stock_id: str
    stock_code: str
    reason: str
    delisted_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"stock_delisted_{self.stock_id}_{int(self.delisted_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockDelisted",
            occurred_at=self.delisted_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'reason': self.reason
            }
        )


@dataclass
class DividendAnnounced(DomainEvent):
    """分红公告事件"""
    stock_id: str
    stock_code: str
    dividend_per_share: Decimal
    ex_dividend_date: date
    announced_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"dividend_announced_{self.stock_id}_{int(self.announced_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="DividendAnnounced",
            occurred_at=self.announced_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'dividend_per_share': str(self.dividend_per_share),
                'ex_dividend_date': self.ex_dividend_date.isoformat()
            }
        )


@dataclass
class StockSplit(DomainEvent):
    """股票拆分事件"""
    stock_id: str
    stock_code: str
    split_ratio: str  # 例如 "1:2" 表示1股拆分为2股
    split_date: date
    announced_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"stock_split_{self.stock_id}_{int(self.announced_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockSplit",
            occurred_at=self.announced_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'split_ratio': self.split_ratio,
                'split_date': self.split_date.isoformat()
            }
        )


@dataclass
class StockMerger(DomainEvent):
    """股票合并事件"""
    stock_id: str
    stock_code: str
    merger_ratio: str  # 例如 "2:1" 表示2股合并为1股
    merger_date: date
    announced_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"stock_merger_{self.stock_id}_{int(self.announced_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockMerger",
            occurred_at=self.announced_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'merger_ratio': self.merger_ratio,
                'merger_date': self.merger_date.isoformat()
            }
        )


@dataclass
class StockNameChanged(DomainEvent):
    """股票名称变更事件"""
    stock_id: str
    stock_code: str
    old_name: str
    new_name: str
    changed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"name_changed_{self.stock_id}_{int(self.changed_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockNameChanged",
            occurred_at=self.changed_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'old_name': self.old_name,
                'new_name': self.new_name
            }
        )


@dataclass
class StockCodeChanged(DomainEvent):
    """股票代码变更事件"""
    stock_id: str
    old_stock_code: str
    new_stock_code: str
    changed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"code_changed_{self.stock_id}_{int(self.changed_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockCodeChanged",
            occurred_at=self.changed_at,
            version=1,
            data={
                'old_stock_code': self.old_stock_code,
                'new_stock_code': self.new_stock_code
            }
        )


@dataclass
class StockRatingChanged(DomainEvent):
    """股票评级变更事件"""
    stock_id: str
    stock_code: str
    old_rating: Optional[str]
    new_rating: str
    rating_agency: str
    changed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"rating_changed_{self.stock_id}_{int(self.changed_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="StockRatingChanged",
            occurred_at=self.changed_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'old_rating': self.old_rating,
                'new_rating': self.new_rating,
                'rating_agency': self.rating_agency
            }
        )


@dataclass
class EarningsAnnounced(DomainEvent):
    """财报公告事件"""
    stock_id: str
    stock_code: str
    quarter: str  # 例如 "2024Q1"
    revenue: Decimal
    net_income: Decimal
    earnings_per_share: Decimal
    announced_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"earnings_announced_{self.stock_id}_{self.quarter}_{int(self.announced_at.timestamp())}",
            aggregate_id=self.stock_id,
            event_type="EarningsAnnounced",
            occurred_at=self.announced_at,
            version=1,
            data={
                'stock_code': self.stock_code,
                'quarter': self.quarter,
                'revenue': str(self.revenue),
                'net_income': str(self.net_income),
                'earnings_per_share': str(self.earnings_per_share)
            }
        )


# 事件工厂函数
def create_stock_created_event(stock_id: str, stock_code: str, 
                              market_code: str, stock_name: str) -> StockCreated:
    """创建股票创建事件"""
    return StockCreated(
        stock_id=stock_id,
        stock_code=stock_code,
        market_code=market_code,
        stock_name=stock_name,
        created_at=datetime.now()
    )


def create_price_changed_event(stock_id: str, stock_code: str,
                              old_price: Optional[Decimal], new_price: Decimal,
                              price_date: date, volume: int) -> StockPriceChanged:
    """创建价格变化事件"""
    return StockPriceChanged(
        stock_id=stock_id,
        stock_code=stock_code,
        old_price=old_price,
        new_price=new_price,
        price_date=price_date,
        volume=volume,
        changed_at=datetime.now()
    )


def create_dividend_announced_event(stock_id: str, stock_code: str,
                                   dividend_per_share: Decimal,
                                   ex_dividend_date: date) -> DividendAnnounced:
    """创建分红公告事件"""
    return DividendAnnounced(
        stock_id=stock_id,
        stock_code=stock_code,
        dividend_per_share=dividend_per_share,
        ex_dividend_date=ex_dividend_date,
        announced_at=datetime.now()
    )
