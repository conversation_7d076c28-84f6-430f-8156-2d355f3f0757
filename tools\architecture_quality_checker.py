#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构质量检查工具

自动检查代码是否符合10/10完美架构标准
"""

import ast
import os
import sys
import inspect
import importlib.util
from pathlib import Path
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass
from enum import Enum
import json
from datetime import datetime


class QualityLevel(Enum):
    """质量等级"""
    EXCELLENT = "A+"  # 9.5-10.0
    GOOD = "A"        # 8.5-9.4
    AVERAGE = "B"     # 7.0-8.4
    POOR = "C"        # 5.0-6.9
    CRITICAL = "D"    # 0-4.9


@dataclass
class QualityMetric:
    """质量指标"""
    name: str
    score: float
    max_score: float
    level: QualityLevel
    violations: List[str]
    suggestions: List[str]


@dataclass
class ArchitectureQualityReport:
    """架构质量报告"""
    overall_score: float
    overall_level: QualityLevel
    metrics: Dict[str, QualityMetric]
    timestamp: str
    summary: str


class ArchitectureQualityChecker:
    """架构质量检查器"""
    
    def __init__(self, project_root: str = "src"):
        self.project_root = Path(project_root)
        self.quality_standards = self._load_quality_standards()
    
    def _load_quality_standards(self) -> Dict[str, Any]:
        """加载质量标准"""
        return {
            "maintainability": {
                "max_cyclomatic_complexity": 10,
                "max_method_length": 50,
                "max_class_methods": 15,
                "max_parameters": 5,
                "min_test_coverage": 95.0
            },
            "scalability": {
                "max_coupling": 0.3,
                "min_cohesion": 0.7,
                "max_depth": 6,
                "max_fan_out": 7
            },
            "testability": {
                "min_test_coverage": 95.0,
                "max_test_complexity": 5,
                "required_test_types": ["unit", "integration", "e2e"]
            },
            "performance": {
                "max_response_time": 100,  # ms
                "min_throughput": 1000,    # RPS
                "max_memory_usage": 512,   # MB
                "max_cpu_usage": 80        # %
            },
            "security": {
                "max_vulnerabilities": 0,
                "required_security_headers": True,
                "encryption_required": True,
                "audit_logging": True
            },
            "deployment": {
                "containerized": True,
                "automated_deployment": True,
                "blue_green_deployment": True,
                "rollback_capability": True
            }
        }
    
    async def check_architecture_quality(self) -> ArchitectureQualityReport:
        """检查架构质量"""
        print("🔍 开始架构质量检查...")
        
        metrics = {}
        
        # 1. 可维护性检查
        metrics["maintainability"] = await self._check_maintainability()
        
        # 2. 可扩展性检查
        metrics["scalability"] = await self._check_scalability()
        
        # 3. 可测试性检查
        metrics["testability"] = await self._check_testability()
        
        # 4. 性能效率检查
        metrics["performance"] = await self._check_performance()
        
        # 5. 安全性检查
        metrics["security"] = await self._check_security()
        
        # 6. 部署便利性检查
        metrics["deployment"] = await self._check_deployment()
        
        # 计算总体评分
        overall_score = self._calculate_overall_score(metrics)
        overall_level = self._get_quality_level(overall_score)
        
        # 生成报告
        report = ArchitectureQualityReport(
            overall_score=overall_score,
            overall_level=overall_level,
            metrics=metrics,
            timestamp=datetime.now().isoformat(),
            summary=self._generate_summary(overall_score, metrics)
        )
        
        return report
    
    async def _check_maintainability(self) -> QualityMetric:
        """检查可维护性"""
        violations = []
        suggestions = []
        score = 10.0
        
        # 检查圈复杂度
        complexity_violations = self._check_cyclomatic_complexity()
        if complexity_violations:
            violations.extend(complexity_violations)
            score -= len(complexity_violations) * 0.5
            suggestions.append("降低方法的圈复杂度，考虑拆分复杂方法")
        
        # 检查方法长度
        length_violations = self._check_method_length()
        if length_violations:
            violations.extend(length_violations)
            score -= len(length_violations) * 0.3
            suggestions.append("缩短过长的方法，提高代码可读性")
        
        # 检查类的方法数量
        class_violations = self._check_class_complexity()
        if class_violations:
            violations.extend(class_violations)
            score -= len(class_violations) * 0.4
            suggestions.append("拆分职责过多的类，遵循单一职责原则")
        
        # 检查参数数量
        param_violations = self._check_parameter_count()
        if param_violations:
            violations.extend(param_violations)
            score -= len(param_violations) * 0.2
            suggestions.append("减少方法参数数量，考虑使用对象封装")
        
        score = max(0, score)
        level = self._get_quality_level(score)
        
        return QualityMetric(
            name="可维护性",
            score=score,
            max_score=10.0,
            level=level,
            violations=violations,
            suggestions=suggestions
        )
    
    async def _check_scalability(self) -> QualityMetric:
        """检查可扩展性"""
        violations = []
        suggestions = []
        score = 10.0
        
        # 检查模块耦合度
        coupling_score = self._check_coupling()
        if coupling_score > self.quality_standards["scalability"]["max_coupling"]:
            violations.append(f"模块耦合度过高: {coupling_score:.2f}")
            score -= 2.0
            suggestions.append("降低模块间耦合，使用依赖注入和接口抽象")
        
        # 检查内聚性
        cohesion_score = self._check_cohesion()
        if cohesion_score < self.quality_standards["scalability"]["min_cohesion"]:
            violations.append(f"模块内聚性过低: {cohesion_score:.2f}")
            score -= 1.5
            suggestions.append("提高模块内聚性，确保相关功能聚集在一起")
        
        # 检查继承深度
        depth_violations = self._check_inheritance_depth()
        if depth_violations:
            violations.extend(depth_violations)
            score -= len(depth_violations) * 0.5
            suggestions.append("减少继承深度，考虑使用组合替代继承")
        
        # 检查扇出复杂度
        fanout_violations = self._check_fan_out()
        if fanout_violations:
            violations.extend(fanout_violations)
            score -= len(fanout_violations) * 0.3
            suggestions.append("减少类的依赖数量，简化类的职责")
        
        score = max(0, score)
        level = self._get_quality_level(score)
        
        return QualityMetric(
            name="可扩展性",
            score=score,
            max_score=10.0,
            level=level,
            violations=violations,
            suggestions=suggestions
        )
    
    async def _check_testability(self) -> QualityMetric:
        """检查可测试性"""
        violations = []
        suggestions = []
        score = 10.0
        
        # 检查测试覆盖率
        coverage = self._get_test_coverage()
        min_coverage = self.quality_standards["testability"]["min_test_coverage"]
        if coverage < min_coverage:
            violations.append(f"测试覆盖率不足: {coverage:.1f}% < {min_coverage}%")
            score -= (min_coverage - coverage) / 10
            suggestions.append("增加单元测试，提高代码覆盖率")
        
        # 检查测试类型完整性
        missing_test_types = self._check_test_types()
        if missing_test_types:
            violations.extend([f"缺少{test_type}测试" for test_type in missing_test_types])
            score -= len(missing_test_types) * 1.0
            suggestions.append("建立完整的测试金字塔：单元测试、集成测试、端到端测试")
        
        # 检查测试复杂度
        test_complexity_violations = self._check_test_complexity()
        if test_complexity_violations:
            violations.extend(test_complexity_violations)
            score -= len(test_complexity_violations) * 0.3
            suggestions.append("简化测试代码，每个测试只验证一个行为")
        
        # 检查Mock使用
        mock_violations = self._check_mock_usage()
        if mock_violations:
            violations.extend(mock_violations)
            score -= len(mock_violations) * 0.2
            suggestions.append("合理使用Mock对象，隔离外部依赖")
        
        score = max(0, score)
        level = self._get_quality_level(score)
        
        return QualityMetric(
            name="可测试性",
            score=score,
            max_score=10.0,
            level=level,
            violations=violations,
            suggestions=suggestions
        )
    
    async def _check_performance(self) -> QualityMetric:
        """检查性能效率"""
        violations = []
        suggestions = []
        score = 10.0
        
        # 检查响应时间
        response_time = self._get_average_response_time()
        max_response_time = self.quality_standards["performance"]["max_response_time"]
        if response_time > max_response_time:
            violations.append(f"响应时间过长: {response_time}ms > {max_response_time}ms")
            score -= 2.0
            suggestions.append("优化算法复杂度，添加缓存机制")
        
        # 检查吞吐量
        throughput = self._get_throughput()
        min_throughput = self.quality_standards["performance"]["min_throughput"]
        if throughput < min_throughput:
            violations.append(f"吞吐量不足: {throughput} RPS < {min_throughput} RPS")
            score -= 1.5
            suggestions.append("使用异步处理，优化数据库查询")
        
        # 检查内存使用
        memory_usage = self._get_memory_usage()
        max_memory = self.quality_standards["performance"]["max_memory_usage"]
        if memory_usage > max_memory:
            violations.append(f"内存使用过高: {memory_usage}MB > {max_memory}MB")
            score -= 1.0
            suggestions.append("优化内存使用，避免内存泄漏")
        
        # 检查算法复杂度
        complexity_violations = self._check_algorithm_complexity()
        if complexity_violations:
            violations.extend(complexity_violations)
            score -= len(complexity_violations) * 0.5
            suggestions.append("优化算法时间复杂度，使用更高效的数据结构")
        
        score = max(0, score)
        level = self._get_quality_level(score)
        
        return QualityMetric(
            name="性能效率",
            score=score,
            max_score=10.0,
            level=level,
            violations=violations,
            suggestions=suggestions
        )
    
    async def _check_security(self) -> QualityMetric:
        """检查安全性"""
        violations = []
        suggestions = []
        score = 10.0
        
        # 检查安全漏洞
        vulnerabilities = self._scan_security_vulnerabilities()
        if vulnerabilities:
            violations.extend(vulnerabilities)
            score -= len(vulnerabilities) * 2.0
            suggestions.append("修复安全漏洞，使用安全编码实践")
        
        # 检查输入验证
        input_validation_issues = self._check_input_validation()
        if input_validation_issues:
            violations.extend(input_validation_issues)
            score -= len(input_validation_issues) * 0.5
            suggestions.append("添加输入验证和清理，防止注入攻击")
        
        # 检查认证授权
        auth_issues = self._check_authentication_authorization()
        if auth_issues:
            violations.extend(auth_issues)
            score -= len(auth_issues) * 1.0
            suggestions.append("实施强认证和细粒度授权机制")
        
        # 检查数据加密
        encryption_issues = self._check_data_encryption()
        if encryption_issues:
            violations.extend(encryption_issues)
            score -= len(encryption_issues) * 0.8
            suggestions.append("对敏感数据进行加密存储和传输")
        
        score = max(0, score)
        level = self._get_quality_level(score)
        
        return QualityMetric(
            name="安全性",
            score=score,
            max_score=10.0,
            level=level,
            violations=violations,
            suggestions=suggestions
        )
    
    async def _check_deployment(self) -> QualityMetric:
        """检查部署便利性"""
        violations = []
        suggestions = []
        score = 10.0
        
        # 检查容器化
        if not self._check_containerization():
            violations.append("项目未容器化")
            score -= 2.0
            suggestions.append("使用Docker容器化应用")
        
        # 检查CI/CD流水线
        if not self._check_cicd_pipeline():
            violations.append("缺少CI/CD流水线")
            score -= 2.0
            suggestions.append("建立自动化CI/CD流水线")
        
        # 检查配置管理
        config_issues = self._check_configuration_management()
        if config_issues:
            violations.extend(config_issues)
            score -= len(config_issues) * 0.5
            suggestions.append("使用配置管理工具，分离配置和代码")
        
        # 检查监控和日志
        monitoring_issues = self._check_monitoring_logging()
        if monitoring_issues:
            violations.extend(monitoring_issues)
            score -= len(monitoring_issues) * 0.3
            suggestions.append("添加应用监控和结构化日志")
        
        score = max(0, score)
        level = self._get_quality_level(score)
        
        return QualityMetric(
            name="部署便利性",
            score=score,
            max_score=10.0,
            level=level,
            violations=violations,
            suggestions=suggestions
        )
    
    def _calculate_overall_score(self, metrics: Dict[str, QualityMetric]) -> float:
        """计算总体评分"""
        weights = {
            "maintainability": 0.25,
            "scalability": 0.20,
            "testability": 0.20,
            "performance": 0.15,
            "security": 0.10,
            "deployment": 0.10
        }
        
        total_score = 0.0
        for metric_name, metric in metrics.items():
            weight = weights.get(metric_name, 0.1)
            total_score += metric.score * weight
        
        return round(total_score, 1)
    
    def _get_quality_level(self, score: float) -> QualityLevel:
        """获取质量等级"""
        if score >= 9.5:
            return QualityLevel.EXCELLENT
        elif score >= 8.5:
            return QualityLevel.GOOD
        elif score >= 7.0:
            return QualityLevel.AVERAGE
        elif score >= 5.0:
            return QualityLevel.POOR
        else:
            return QualityLevel.CRITICAL
    
    def _generate_summary(self, overall_score: float, metrics: Dict[str, QualityMetric]) -> str:
        """生成总结"""
        level = self._get_quality_level(overall_score)
        
        summary = f"架构质量评分: {overall_score}/10.0 ({level.value})\n\n"
        
        # 最佳实践
        excellent_metrics = [name for name, metric in metrics.items() if metric.level == QualityLevel.EXCELLENT]
        if excellent_metrics:
            summary += f"✅ 优秀维度: {', '.join(excellent_metrics)}\n"
        
        # 需要改进的方面
        poor_metrics = [name for name, metric in metrics.items() if metric.level in [QualityLevel.POOR, QualityLevel.CRITICAL]]
        if poor_metrics:
            summary += f"❌ 需要改进: {', '.join(poor_metrics)}\n"
        
        # 总体建议
        if overall_score >= 9.5:
            summary += "\n🏆 架构质量优秀，继续保持！"
        elif overall_score >= 8.5:
            summary += "\n👍 架构质量良好，可以进一步优化"
        elif overall_score >= 7.0:
            summary += "\n⚠️ 架构质量一般，建议重点改进"
        else:
            summary += "\n🚨 架构质量需要大幅改进"
        
        return summary
    
    # 具体检查方法的简化实现（实际项目中需要完整实现）
    def _check_cyclomatic_complexity(self) -> List[str]:
        """检查圈复杂度"""
        # 简化实现，实际需要使用AST分析
        return []
    
    def _check_method_length(self) -> List[str]:
        """检查方法长度"""
        return []
    
    def _check_class_complexity(self) -> List[str]:
        """检查类复杂度"""
        return []
    
    def _check_parameter_count(self) -> List[str]:
        """检查参数数量"""
        return []
    
    def _check_coupling(self) -> float:
        """检查耦合度"""
        return 0.2  # 示例值
    
    def _check_cohesion(self) -> float:
        """检查内聚性"""
        return 0.8  # 示例值
    
    def _check_inheritance_depth(self) -> List[str]:
        """检查继承深度"""
        return []
    
    def _check_fan_out(self) -> List[str]:
        """检查扇出复杂度"""
        return []
    
    def _get_test_coverage(self) -> float:
        """获取测试覆盖率"""
        return 85.0  # 示例值
    
    def _check_test_types(self) -> List[str]:
        """检查测试类型"""
        return ["e2e"]  # 示例：缺少端到端测试
    
    def _check_test_complexity(self) -> List[str]:
        """检查测试复杂度"""
        return []
    
    def _check_mock_usage(self) -> List[str]:
        """检查Mock使用"""
        return []
    
    def _get_average_response_time(self) -> float:
        """获取平均响应时间"""
        return 150.0  # 示例值
    
    def _get_throughput(self) -> float:
        """获取吞吐量"""
        return 800.0  # 示例值
    
    def _get_memory_usage(self) -> float:
        """获取内存使用"""
        return 256.0  # 示例值
    
    def _check_algorithm_complexity(self) -> List[str]:
        """检查算法复杂度"""
        return []
    
    def _scan_security_vulnerabilities(self) -> List[str]:
        """扫描安全漏洞"""
        return []
    
    def _check_input_validation(self) -> List[str]:
        """检查输入验证"""
        return []
    
    def _check_authentication_authorization(self) -> List[str]:
        """检查认证授权"""
        return []
    
    def _check_data_encryption(self) -> List[str]:
        """检查数据加密"""
        return []
    
    def _check_containerization(self) -> bool:
        """检查容器化"""
        return Path("Dockerfile").exists()
    
    def _check_cicd_pipeline(self) -> bool:
        """检查CI/CD流水线"""
        return (Path(".github/workflows").exists() or 
                Path(".gitlab-ci.yml").exists() or
                Path("Jenkinsfile").exists())
    
    def _check_configuration_management(self) -> List[str]:
        """检查配置管理"""
        return []
    
    def _check_monitoring_logging(self) -> List[str]:
        """检查监控和日志"""
        return []
    
    def save_report(self, report: ArchitectureQualityReport, output_path: str = "architecture_quality_report.json"):
        """保存报告"""
        report_dict = {
            "overall_score": report.overall_score,
            "overall_level": report.overall_level.value,
            "timestamp": report.timestamp,
            "summary": report.summary,
            "metrics": {
                name: {
                    "score": metric.score,
                    "max_score": metric.max_score,
                    "level": metric.level.value,
                    "violations": metric.violations,
                    "suggestions": metric.suggestions
                }
                for name, metric in report.metrics.items()
            }
        }
        
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report_dict, f, ensure_ascii=False, indent=2)
        
        print(f"📄 质量报告已保存: {output_path}")


async def main():
    """主函数"""
    print("🏗️ MythQuant 架构质量检查工具")
    print("=" * 50)
    
    checker = ArchitectureQualityChecker()
    report = await checker.check_architecture_quality()
    
    # 显示结果
    print(f"\n📊 架构质量评分: {report.overall_score}/10.0 ({report.overall_level.value})")
    print("\n📋 详细指标:")
    
    for name, metric in report.metrics.items():
        print(f"  {name}: {metric.score}/10.0 ({metric.level.value})")
        if metric.violations:
            print(f"    违规项: {len(metric.violations)} 个")
        if metric.suggestions:
            print(f"    建议: {metric.suggestions[0]}")
    
    print(f"\n{report.summary}")
    
    # 保存报告
    checker.save_report(report)


if __name__ == "__main__":
    import asyncio
    asyncio.run(main())
