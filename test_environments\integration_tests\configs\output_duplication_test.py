#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出重复检测测试

验证修复后的系统是否还存在日志与terminal输出重复的问题

位置: test_environments/integration_tests/configs/
作者: AI Assistant
创建时间: 2025-07-31
"""

import sys
import os
import subprocess
import time
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_completion
)


def test_output_duplication():
    """测试输出重复问题"""
    print_step("输出重复检测测试", 1, 1)
    
    try:
        # 切换到项目根目录
        original_cwd = os.getcwd()
        os.chdir(project_root)
        
        try:
            print_info("启动主程序进行输出重复检测...", level=2)
            
            # 运行主程序，捕获输出
            process = subprocess.Popen(
                [sys.executable, 'main.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',
                cwd=project_root
            )
            
            # 等待程序执行，最多等待30秒
            try:
                stdout, stderr = process.communicate(timeout=30)
                return_code = process.returncode
                
                print_info(f"程序执行完成，返回码: {return_code}", level=2)
                
                # 分析输出重复情况
                lines = stdout.split('\n')
                
                # 检查重复的关键信息
                duplicate_patterns = [
                    "程序启动，日志文件",
                    "初始化完成",
                    "股票数据处理器初始化",
                    "正在加载任务配置",
                    "开始执行任务"
                ]
                
                duplicates_found = []
                
                for pattern in duplicate_patterns:
                    matching_lines = [line for line in lines if pattern in line]
                    if len(matching_lines) > 1:
                        duplicates_found.append({
                            'pattern': pattern,
                            'count': len(matching_lines),
                            'lines': matching_lines[:3]  # 只显示前3个
                        })
                
                # 分析结果
                if duplicates_found:
                    print_error("发现重复输出:", level=2)
                    for dup in duplicates_found:
                        print_error(f"  模式: {dup['pattern']} (出现{dup['count']}次)", level=3)
                        for line in dup['lines']:
                            print_info(f"    {line.strip()}", level=4)
                    return False
                else:
                    print_result("未发现重复输出", True, level=2)
                    
                # 检查是否有emoji显示问题
                emoji_lines = [line for line in lines if any(emoji in line for emoji in ['🚀', '📊', '✅', '❌', '⚠️', 'ℹ️'])]
                if emoji_lines:
                    print_result(f"Emoji显示正常 ({len(emoji_lines)}行包含emoji)", True, level=2)
                else:
                    print_warning("未检测到emoji输出", level=2)
                
                return True
                
            except subprocess.TimeoutExpired:
                print_warning("程序执行超时，终止进程", level=2)
                process.kill()
                return True  # 超时不算错误
                
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        print_error(f"输出重复检测测试失败: {e}", level=2)
        return False


def analyze_log_file():
    """分析最新的日志文件"""
    print_step("日志文件分析", 1, 1)
    
    try:
        logs_dir = os.path.join(project_root, 'logs')
        if not os.path.exists(logs_dir):
            print_warning("日志目录不存在", level=2)
            return True
        
        # 找到最新的日志文件
        log_files = [f for f in os.listdir(logs_dir) if f.startswith('mythquant_') and f.endswith('.log')]
        if not log_files:
            print_warning("未找到日志文件", level=2)
            return True
        
        latest_log = max(log_files, key=lambda f: os.path.getmtime(os.path.join(logs_dir, f)))
        log_path = os.path.join(logs_dir, latest_log)
        
        print_info(f"分析日志文件: {latest_log}", level=2)
        
        # 读取日志内容
        with open(log_path, 'r', encoding='utf-8') as f:
            log_content = f.read()
        
        # 统计日志条目
        log_lines = [line for line in log_content.split('\n') if line.strip()]
        info_count = len([line for line in log_lines if ' - INFO - ' in line])
        warning_count = len([line for line in log_lines if ' - WARNING - ' in line])
        error_count = len([line for line in log_lines if ' - ERROR - ' in line])
        
        print_info(f"日志统计: INFO={info_count}, WARNING={warning_count}, ERROR={error_count}", level=2)
        
        # 检查是否有emoji在日志中
        emoji_in_log = any(emoji in log_content for emoji in ['🚀', '📊', '✅', '❌', '⚠️', 'ℹ️'])
        if emoji_in_log:
            print_result("日志文件包含emoji字符", True, level=2)
        else:
            print_info("日志文件不包含emoji字符", level=2)
        
        return True
        
    except Exception as e:
        print_error(f"日志文件分析失败: {e}", level=2)
        return False


def main():
    """主函数"""
    print_banner("输出重复检测测试", "验证修复后的系统是否还存在重复输出问题")
    
    print_info(f"项目根目录: {project_root}")
    print_info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print_main_process("执行输出重复检测")
    
    # 执行测试
    tests = [
        ("输出重复检测", test_output_duplication),
        ("日志文件分析", analyze_log_file)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print_error(f"测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    total_tests = len(tests)
    pass_rate = passed_tests / total_tests
    
    print_main_process("输出重复检测结果")
    
    # 显示详细结果
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print_result(f"{test_name}: {status}", result, level=1)
    
    # 显示统计信息
    stats = {
        "总测试数": total_tests,
        "通过测试数": passed_tests,
        "失败测试数": total_tests - passed_tests,
        "通过率": f"{pass_rate:.1%}"
    }
    
    from utils.structured_output_formatter import print_stats_table
    print_stats_table("重复检测统计", stats)
    
    # 判断整体结果
    overall_success = pass_rate >= 0.8
    
    completion_message = "输出重复问题已修复！" if overall_success else "仍存在输出重复问题"
    print_completion(completion_message, overall_success, stats)
    
    return 0 if overall_success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
