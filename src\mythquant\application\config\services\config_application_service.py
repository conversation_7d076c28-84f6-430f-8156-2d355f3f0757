#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置应用服务

协调配置相关的用例，连接领域层和基础设施层
"""

from typing import List, Dict, Any, Optional
from src.mythquant.domain.config.repositories.config_repository import ConfigRepository, TaskConfigRepository
from src.mythquant.domain.config.services.config_validation_service import ConfigValidationService, ValidationResult
from src.mythquant.domain.config.entities.trading_config import TradingConfig
from src.mythquant.domain.config.entities.data_source_config import DataSourceConfig
from src.mythquant.domain.config.entities.processing_config import ProcessingConfig
from ..dto.config_dto import TradingConfigDto, DataSourceConfigDto, ProcessingConfigDto, TaskConfigDto


class ConfigApplicationService:
    """配置应用服务"""
    
    def __init__(self, config_repo: ConfigRepository, task_config_repo: Optional[TaskConfigRepository] = None):
        """初始化配置应用服务"""
        self._config_repo = config_repo
        self._task_config_repo = task_config_repo
        self._validation_service = ConfigValidationService()
    
    # ==================== 交易配置相关 ====================
    
    def get_trading_config(self) -> TradingConfigDto:
        """获取交易配置"""
        try:
            config_entity = self._config_repo.get_trading_config()
            
            # 验证配置
            validation_result = self._validation_service.validate_trading_config(config_entity)
            if not validation_result.is_valid:
                raise ValueError(f"交易配置验证失败: {', '.join(validation_result.errors)}")
            
            return TradingConfigDto.from_entity(config_entity)
            
        except Exception as e:
            raise RuntimeError(f"获取交易配置失败: {str(e)}")
    
    def update_trading_config(self, dto: TradingConfigDto) -> None:
        """更新交易配置"""
        try:
            entity = dto.to_entity()
            
            # 验证配置
            validation_result = self._validation_service.validate_trading_config(entity)
            if not validation_result.is_valid:
                raise ValueError(f"交易配置验证失败: {', '.join(validation_result.errors)}")
            
            self._config_repo.save_trading_config(entity)
            
        except Exception as e:
            raise RuntimeError(f"更新交易配置失败: {str(e)}")
    
    # ==================== 数据源配置相关 ====================
    
    def get_data_source_config(self) -> DataSourceConfigDto:
        """获取数据源配置"""
        try:
            config_entity = self._config_repo.get_data_source_config()
            
            # 验证配置
            validation_result = self._validation_service.validate_data_source_config(config_entity)
            if not validation_result.is_valid:
                raise ValueError(f"数据源配置验证失败: {', '.join(validation_result.errors)}")
            
            return DataSourceConfigDto.from_entity(config_entity)
            
        except Exception as e:
            raise RuntimeError(f"获取数据源配置失败: {str(e)}")
    
    def update_data_source_config(self, dto: DataSourceConfigDto) -> None:
        """更新数据源配置"""
        try:
            entity = dto.to_entity()
            
            # 验证配置
            validation_result = self._validation_service.validate_data_source_config(entity)
            if not validation_result.is_valid:
                raise ValueError(f"数据源配置验证失败: {', '.join(validation_result.errors)}")
            
            self._config_repo.save_data_source_config(entity)
            
        except Exception as e:
            raise RuntimeError(f"更新数据源配置失败: {str(e)}")
    
    # ==================== 数据处理配置相关 ====================
    
    def get_processing_config(self) -> ProcessingConfigDto:
        """获取数据处理配置"""
        try:
            config_entity = self._config_repo.get_processing_config()
            
            # 验证配置
            validation_result = self._validation_service.validate_processing_config(config_entity)
            if not validation_result.is_valid:
                raise ValueError(f"数据处理配置验证失败: {', '.join(validation_result.errors)}")
            
            return ProcessingConfigDto.from_entity(config_entity)
            
        except Exception as e:
            raise RuntimeError(f"获取数据处理配置失败: {str(e)}")
    
    def update_processing_config(self, dto: ProcessingConfigDto) -> None:
        """更新数据处理配置"""
        try:
            entity = dto.to_entity()
            
            # 验证配置
            validation_result = self._validation_service.validate_processing_config(entity)
            if not validation_result.is_valid:
                raise ValueError(f"数据处理配置验证失败: {', '.join(validation_result.errors)}")
            
            self._config_repo.save_processing_config(entity)
            
        except Exception as e:
            raise RuntimeError(f"更新数据处理配置失败: {str(e)}")
    
    # ==================== 任务配置相关 ====================
    
    def get_task_configs(self) -> List[TaskConfigDto]:
        """获取任务配置列表"""
        try:
            task_configs = self._config_repo.get_task_configs()
            return [TaskConfigDto.from_dict(config) for config in task_configs]
            
        except Exception as e:
            raise RuntimeError(f"获取任务配置失败: {str(e)}")
    
    def get_minute_task_configs(self) -> List[TaskConfigDto]:
        """获取分钟级任务配置"""
        try:
            if not self._task_config_repo:
                # 从主配置仓储获取
                all_configs = self._config_repo.get_task_configs()
                minute_configs = [config for config in all_configs 
                                if config.get('data_type') in ['minute', 'internet_minute']]
            else:
                minute_configs = self._task_config_repo.get_minute_task_configs()
            
            return [TaskConfigDto.from_dict(config) for config in minute_configs]
            
        except Exception as e:
            raise RuntimeError(f"获取分钟级任务配置失败: {str(e)}")
    
    def get_daily_task_configs(self) -> List[TaskConfigDto]:
        """获取日级任务配置"""
        try:
            if not self._task_config_repo:
                # 从主配置仓储获取
                all_configs = self._config_repo.get_task_configs()
                daily_configs = [config for config in all_configs 
                               if config.get('data_type') in ['daily', 'internet_daily']]
            else:
                daily_configs = self._task_config_repo.get_daily_task_configs()
            
            return [TaskConfigDto.from_dict(config) for config in daily_configs]
            
        except Exception as e:
            raise RuntimeError(f"获取日级任务配置失败: {str(e)}")
    
    # ==================== 配置验证相关 ====================
    
    def validate_all_configs(self) -> ValidationResult:
        """验证所有配置"""
        try:
            trading_config = self._config_repo.get_trading_config()
            data_source_config = self._config_repo.get_data_source_config()
            processing_config = self._config_repo.get_processing_config()
            
            return self._validation_service.validate_all_configs(
                trading_config, data_source_config, processing_config
            )
            
        except Exception as e:
            return ValidationResult(
                is_valid=False,
                errors=[f"配置验证过程中发生错误: {str(e)}"],
                warnings=[],
                suggestions=[]
            )
    
    def validate_config_integrity(self) -> bool:
        """验证配置完整性"""
        try:
            return self._config_repo.validate_config_integrity()
        except Exception:
            return False
    
    # ==================== 配置备份和恢复 ====================
    
    def backup_config(self) -> str:
        """备份配置"""
        try:
            return self._config_repo.backup_config()
        except Exception as e:
            raise RuntimeError(f"备份配置失败: {str(e)}")
    
    def restore_config(self, backup_path: str) -> bool:
        """恢复配置"""
        try:
            return self._config_repo.restore_config(backup_path)
        except Exception as e:
            raise RuntimeError(f"恢复配置失败: {str(e)}")
    
    # ==================== 便捷访问方法 ====================
    
    def get_tdx_path(self) -> str:
        """获取TDX路径"""
        config = self.get_trading_config()
        return config.tdx_path
    
    def get_output_path(self) -> str:
        """获取输出路径"""
        config = self.get_trading_config()
        return config.output_path
    
    def is_smart_file_selector_enabled(self) -> bool:
        """检查智能文件选择器是否启用"""
        config = self.get_processing_config()
        return config.smart_file_selector_enabled
    
    def is_debug_enabled(self) -> bool:
        """检查调试模式是否启用"""
        config = self.get_processing_config()
        return config.debug_enabled
    
    def is_verbose_mode_enabled(self) -> bool:
        """检查详细模式是否启用"""
        config = self.get_processing_config()
        return config.verbose_mode_enabled
    
    def get_pytdx_connection_info(self) -> Dict[str, Any]:
        """获取pytdx连接信息"""
        config = self.get_trading_config()
        return {
            'ip': config.tdx_ip,
            'port': config.tdx_port,
            'auto_detect': config.pytdx_auto_detect,
            'show_top5': config.pytdx_show_top5,
            'smart_detect': config.pytdx_smart_detect
        }
