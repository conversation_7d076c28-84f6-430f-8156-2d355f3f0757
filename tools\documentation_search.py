#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档搜索工具

提供文档内容搜索、链接检查和导航功能
"""

import os
import re
from pathlib import Path
from typing import List, Dict, Tuple
import argparse


class DocumentationSearcher:
    """文档搜索器"""
    
    def __init__(self, docs_root: str = "docs"):
        self.docs_root = Path(docs_root)
        self.supported_extensions = {'.md', '.txt', '.rst'}
    
    def search_content(self, query: str, case_sensitive: bool = False) -> List[Dict]:
        """搜索文档内容"""
        results = []
        
        flags = 0 if case_sensitive else re.IGNORECASE
        pattern = re.compile(re.escape(query), flags)
        
        for file_path in self._get_all_docs():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                matches = []
                for line_num, line in enumerate(content.split('\n'), 1):
                    if pattern.search(line):
                        matches.append({
                            'line_number': line_num,
                            'line_content': line.strip(),
                            'context': self._get_context(content.split('\n'), line_num - 1)
                        })
                
                if matches:
                    results.append({
                        'file': str(file_path.relative_to(self.docs_root)),
                        'matches': matches,
                        'total_matches': len(matches)
                    })
            
            except Exception as e:
                print(f"⚠️ 读取文件失败 {file_path}: {e}")
        
        return results
    
    def search_filenames(self, query: str) -> List[Path]:
        """搜索文件名"""
        results = []
        pattern = re.compile(re.escape(query), re.IGNORECASE)
        
        for file_path in self._get_all_docs():
            if pattern.search(file_path.name):
                results.append(file_path.relative_to(self.docs_root))
        
        return results
    
    def find_broken_links(self) -> List[Dict]:
        """查找断开的链接"""
        broken_links = []
        
        for file_path in self._get_all_docs():
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # 查找Markdown链接
                link_pattern = r'\[([^\]]+)\]\(([^)]+)\)'
                matches = re.finditer(link_pattern, content)
                
                for match in matches:
                    link_text = match.group(1)
                    link_url = match.group(2)
                    
                    # 跳过外部链接
                    if link_url.startswith(('http://', 'https://', 'mailto:')):
                        continue
                    
                    # 检查相对路径链接
                    if link_url.startswith('#'):
                        # 内部锚点链接，暂时跳过
                        continue
                    
                    # 解析相对路径
                    target_path = file_path.parent / link_url
                    target_path = target_path.resolve()
                    
                    if not target_path.exists():
                        broken_links.append({
                            'file': str(file_path.relative_to(self.docs_root)),
                            'link_text': link_text,
                            'link_url': link_url,
                            'target_path': str(target_path)
                        })
            
            except Exception as e:
                print(f"⚠️ 检查链接失败 {file_path}: {e}")
        
        return broken_links
    
    def generate_sitemap(self) -> Dict:
        """生成文档站点地图"""
        sitemap = {
            'total_files': 0,
            'categories': {},
            'files_by_type': {},
            'directory_structure': {}
        }
        
        for file_path in self._get_all_docs():
            sitemap['total_files'] += 1
            
            # 按目录分类
            relative_path = file_path.relative_to(self.docs_root)
            category = str(relative_path.parent) if relative_path.parent != Path('.') else 'root'
            
            if category not in sitemap['categories']:
                sitemap['categories'][category] = []
            sitemap['categories'][category].append(str(relative_path))
            
            # 按文件类型分类
            file_type = file_path.suffix
            if file_type not in sitemap['files_by_type']:
                sitemap['files_by_type'][file_type] = []
            sitemap['files_by_type'][file_type].append(str(relative_path))
        
        return sitemap
    
    def _get_all_docs(self) -> List[Path]:
        """获取所有文档文件"""
        docs = []
        for file_path in self.docs_root.rglob('*'):
            if file_path.is_file() and file_path.suffix in self.supported_extensions:
                docs.append(file_path)
        return docs
    
    def _get_context(self, lines: List[str], line_index: int, context_size: int = 2) -> Dict:
        """获取匹配行的上下文"""
        start = max(0, line_index - context_size)
        end = min(len(lines), line_index + context_size + 1)
        
        return {
            'before': lines[start:line_index],
            'after': lines[line_index + 1:end]
        }
    
    def print_search_results(self, results: List[Dict], query: str):
        """打印搜索结果"""
        if not results:
            print(f"🔍 未找到包含 '{query}' 的文档")
            return
        
        print(f"🔍 搜索结果: '{query}' (找到 {len(results)} 个文件)")
        print("=" * 60)
        
        for result in results:
            print(f"\n📄 {result['file']} ({result['total_matches']} 个匹配)")
            
            for match in result['matches'][:3]:  # 只显示前3个匹配
                print(f"   第 {match['line_number']} 行: {match['line_content']}")
            
            if result['total_matches'] > 3:
                print(f"   ... 还有 {result['total_matches'] - 3} 个匹配")
    
    def print_broken_links(self, broken_links: List[Dict]):
        """打印断开的链接"""
        if not broken_links:
            print("✅ 未发现断开的链接")
            return
        
        print(f"🔗 发现 {len(broken_links)} 个断开的链接:")
        print("=" * 60)
        
        for link in broken_links:
            print(f"\n📄 {link['file']}")
            print(f"   链接文本: {link['link_text']}")
            print(f"   链接地址: {link['link_url']}")
            print(f"   目标路径: {link['target_path']}")
    
    def print_sitemap(self, sitemap: Dict):
        """打印站点地图"""
        print(f"🗺️ 文档站点地图 (总计 {sitemap['total_files']} 个文件)")
        print("=" * 60)
        
        print("\n📁 按目录分类:")
        for category, files in sitemap['categories'].items():
            print(f"   {category}/ ({len(files)} 个文件)")
            for file in sorted(files)[:5]:  # 只显示前5个
                print(f"     - {Path(file).name}")
            if len(files) > 5:
                print(f"     ... 还有 {len(files) - 5} 个文件")
        
        print("\n📋 按类型分类:")
        for file_type, files in sitemap['files_by_type'].items():
            print(f"   {file_type}: {len(files)} 个文件")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='MythQuant 文档搜索工具')
    parser.add_argument('command', choices=['search', 'filename', 'links', 'sitemap'], 
                       help='执行的命令')
    parser.add_argument('query', nargs='?', help='搜索查询')
    parser.add_argument('--case-sensitive', action='store_true', help='区分大小写')
    parser.add_argument('--docs-root', default='docs', help='文档根目录')
    
    args = parser.parse_args()
    
    searcher = DocumentationSearcher(args.docs_root)
    
    if args.command == 'search':
        if not args.query:
            print("❌ 搜索命令需要提供查询内容")
            return
        
        results = searcher.search_content(args.query, args.case_sensitive)
        searcher.print_search_results(results, args.query)
    
    elif args.command == 'filename':
        if not args.query:
            print("❌ 文件名搜索需要提供查询内容")
            return
        
        results = searcher.search_filenames(args.query)
        if results:
            print(f"🔍 文件名搜索结果: '{args.query}' (找到 {len(results)} 个文件)")
            for file_path in results:
                print(f"   📄 {file_path}")
        else:
            print(f"🔍 未找到文件名包含 '{args.query}' 的文档")
    
    elif args.command == 'links':
        broken_links = searcher.find_broken_links()
        searcher.print_broken_links(broken_links)
    
    elif args.command == 'sitemap':
        sitemap = searcher.generate_sitemap()
        searcher.print_sitemap(sitemap)


if __name__ == "__main__":
    main()
