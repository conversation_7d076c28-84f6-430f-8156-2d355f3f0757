2025-07-29 10:05:02,502 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250729_100502.log
2025-07-29 10:05:02,502 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-29 10:05:02,503 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-29 10:05:02,503 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-29 10:05:02,503 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-29 10:05:02,503 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-29 10:05:02,503 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-29 10:05:02,504 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-29 10:05:02,504 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-29 10:05:02,515 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-29 10:05:02,729 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 3天, 频率: 1min)
2025-07-29 10:05:02,729 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-29 10:05:02,730 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 10:05:02,730 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 10:05:02,785 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-29 10:05:02,995 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-29 10:05:02,995 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-29 10:05:02,996 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-29 10:05:03,001 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-29 10:05:03,004 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx只提供原始价格，正在尝试计算前复权价格...
2025-07-29 10:05:03,004 - Main - INFO - info:307 - ℹ️ 🔄 pytdx前复权计算: 000617
2025-07-29 10:05:03,005 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-29 10:05:03,005 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-29 10:05:03,501 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-29 10:05:03,501 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-29 10:05:03,508 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-29 10:05:03,508 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17/T0002
2025-07-29 10:05:03,508 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-29 10:05:03,508 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-29 10:05:03,509 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 未找到任何市场的分钟数据文件
2025-07-29 10:05:03,509 - main_v20230219_optimized - WARNING - _get_optimal_tdx_path:255 - ⚠️  主路径下未找到分钟数据文件，但将继续使用配置路径
2025-07-29 10:05:03,509 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17/T0002，缓存方法: memory
2025-07-29 10:05:29,556 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-29 10:05:30,268 - main_v20230219_optimized - WARNING - _extract_stock_code_from_data:2442 - 无法从数据中提取股票代码，使用target_stocks中的第一个
2025-07-29 10:05:30,307 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 240条
2025-07-29 10:05:30,308 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功，处理了20个除权事件
2025-07-29 10:05:30,321 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功
2025-07-29 10:05:30,321 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价格与原始价格相同，可能无除权事件
2025-07-29 10:05:30,325 - Main - INFO - info:307 - ℹ️ ℹ️ 增量下载前提条件不满足: 不具备增量下载前提条件: 前复权价格不一致，存在分红配股影响
2025-07-29 10:05:30,326 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-29 10:05:30,326 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-29 10:05:30,326 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-29 10:05:30,379 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数21291, 覆盖89个交易日
2025-07-29 10:05:30,379 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-07-29 10:05:30,379 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-07-29 10:05:30,379 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-07-29 10:05:30,380 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-07-29 10:05:30,380 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-07-29 10:05:30,380 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-07-29 10:05:30,380 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-07-29 10:05:30,381 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-07-29 10:05:30,381 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-07-29 10:05:30,381 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-07-29 10:05:30,382 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-29 10:05:30,765 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-29 10:05:30,765 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 10:05:30,765 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 10:05:30,765 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-29 10:05:30,944 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32880条 (目标日期: 20250101, 交易日: 137天, 频率: 1min)
2025-07-29 10:05:30,944 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32880条 -> 实际请求800条 (配置限制:800)
2025-07-29 10:05:30,944 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 10:05:30,944 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 10:05:30,995 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32080条
2025-07-29 10:05:31,216 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-29 10:05:31,449 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-29 10:05:31,674 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-29 10:05:31,889 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-29 10:05:32,108 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-29 10:05:32,339 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-29 10:05:32,566 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-29 10:05:32,796 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-29 10:05:33,029 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-29 10:05:33,251 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-29 10:05:33,477 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-29 10:05:33,699 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-29 10:05:33,934 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-29 10:05:34,163 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-29 10:05:34,384 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-29 10:05:34,622 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-29 10:05:34,844 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-29 10:05:35,076 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-29 10:05:35,308 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-29 10:05:35,536 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-29 10:05:35,759 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-29 10:05:35,991 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-29 10:05:36,218 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-29 10:05:36,440 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-29 10:05:36,672 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-29 10:05:36,894 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-29 10:05:37,120 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +36条，总计21636条
2025-07-29 10:05:37,120 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21636条数据
2025-07-29 10:05:37,125 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32880条，实际获取21636条
2025-07-29 10:05:37,125 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11244条数据（约46个交易日）
2025-07-29 10:05:37,125 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-29 10:05:37,125 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-29 10:05:37,125 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-29 10:05:37,179 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-07-29 10:05:37,313 - Main - INFO - info:307 - ℹ️ ✅ 数据保存完成: 1min_0_000617_20250318-20250725_来源互联网（202507291005）.txt (999617 字节)
2025-07-29 10:05:37,314 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-29 10:05:37,314 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-29 10:05:37,315 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-29 10:05:37,315 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-29 10:05:37,316 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-29 10:05:37,316 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-29 10:05:37,316 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-29 10:05:37,316 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-29 10:05:37,316 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-29 10:05:37,317 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250729_100537.txt
2025-07-29 10:05:37,317 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-29 10:05:37,317 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-29 10:05:37,318 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-29 10:05:37,318 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-29 10:05:37,318 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-29 10:05:37,318 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-29 10:05:37,319 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-29 10:05:37,319 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-29 10:05:37,319 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-29 10:05:37,319 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-29 10:05:37,319 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
