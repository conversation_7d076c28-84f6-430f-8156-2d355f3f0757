#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件生成器模块

按照1min_workflow.md第5步要求实现标准格式文件生成功能

作者: AI Assistant
创建时间: 2025-08-09
"""

import os
import sys
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mythquant.shared.logging import get_smart_logger


class MinuteDataFileGenerator:
    """分钟数据文件生成器"""
    
    def __init__(self):
        """初始化文件生成器"""
        self.logger = get_smart_logger("MinuteDataFileGenerator")
        self.logger.info("📄 分钟数据文件生成器初始化完成")
    
    def generate_standard_file(self, stock_code: str, data_content: List[Dict[str, Any]], 
                             source_description: str = '来源互联网', 
                             timestamp_suffix: bool = True,
                             output_dir: str = None) -> str:
        """
        生成标准格式文件
        
        Args:
            stock_code: 股票代码
            data_content: 数据内容列表
            source_description: 来源描述
            timestamp_suffix: 是否添加时间戳后缀
            output_dir: 输出目录
            
        Returns:
            生成的文件路径
        """
        try:
            self.logger.info(f"📄 开始生成标准格式文件: {stock_code}")

            # 分析数据时间范围
            data_range = self._analyze_data_range(data_content)
            if not data_range:
                raise ValueError("无法分析数据时间范围")

            # 生成标准文件名
            filename = self._generate_filename(
                stock_code=stock_code,
                start_date=data_range['start_date'],
                end_date=data_range['end_date'],
                source_description=source_description,
                timestamp_suffix=timestamp_suffix
            )

            # 确定输出路径
            if output_dir is None:
                output_dir = self._get_default_output_dir()

            output_path = os.path.join(output_dir, filename)

            # 格式化文件内容
            formatted_content = self._format_file_content(data_content, stock_code)

            # 写入文件
            self._write_to_file(output_path, formatted_content)

            self.logger.info(f"✅ 文件生成成功: {filename}")
            return output_path

        except Exception as e:
            self.logger.error(f"❌ 文件生成失败: {e}")
            raise
    
    def _analyze_data_range(self, data_content: List[Dict[str, Any]]) -> Optional[Dict[str, str]]:
        """
        分析数据时间范围
        
        Args:
            data_content: 数据内容列表
            
        Returns:
            包含开始和结束日期的字典
        """
        try:
            if not data_content:
                return None
            
            # 获取最早和最晚时间
            datetime_ints = [record.get('datetime_int', 0) for record in data_content if record.get('datetime_int')]
            
            if not datetime_ints:
                return None
            
            earliest_datetime = min(datetime_ints)
            latest_datetime = max(datetime_ints)
            
            # 转换为日期格式
            start_date = str(earliest_datetime)[:8]  # YYYYMMDD
            end_date = str(latest_datetime)[:8]      # YYYYMMDD
            
            return {
                'start_date': start_date,
                'end_date': end_date,
                'earliest_datetime': earliest_datetime,
                'latest_datetime': latest_datetime
            }
            
        except Exception as e:
            self.logger.error(f"分析数据时间范围失败: {e}")
            return None
    
    def _generate_filename(self, stock_code: str, start_date: str, end_date: str,
                          source_description: str, timestamp_suffix: bool) -> str:
        """
        生成标准文件名
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            end_date: 结束日期
            source_description: 来源描述
            timestamp_suffix: 是否添加时间戳后缀
            
        Returns:
            标准文件名
        """
        try:
            # 格式化股票代码（确保6位数字）
            formatted_stock_code = str(stock_code).zfill(6)
            
            # 确定市场编号（0表示深圳，1表示上海）
            market_code = '1' if formatted_stock_code.startswith('6') else '0'
            
            # 基础文件名
            base_filename = f"1min_{market_code}_{formatted_stock_code}_{start_date}-{end_date}_{source_description}"
            
            # 添加时间戳后缀
            if timestamp_suffix:
                timestamp = datetime.now().strftime('%Y%m%d%H%M')
                filename = f"{base_filename}（{timestamp}）.txt"
            else:
                filename = f"{base_filename}.txt"
            
            return filename
            
        except Exception as e:
            self.logger.error(f"生成文件名失败: {e}")
            raise
    
    def _format_file_content(self, data_content: List[Dict[str, Any]], stock_code: str) -> str:
        """
        格式化文件内容
        
        Args:
            data_content: 数据内容列表
            stock_code: 股票代码
            
        Returns:
            格式化后的文件内容
        """
        try:
            # 生成文件头
            header = self._generate_header()
            
            # 格式化数据行
            data_rows = []
            formatted_stock_code = str(stock_code).zfill(6)
            
            for record in data_content:
                row = self._format_data_row(record, formatted_stock_code)
                if row:
                    data_rows.append(row)
            
            # 组合完整内容
            content = header + '\n' + '\n'.join(data_rows)
            return content
            
        except Exception as e:
            self.logger.error(f"格式化文件内容失败: {e}")
            raise
    
    def _generate_header(self) -> str:
        """生成文件头"""
        return "股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖"
    
    def _format_data_row(self, record: Dict[str, Any], stock_code: str) -> Optional[str]:
        """
        格式化数据行
        
        Args:
            record: 数据记录
            stock_code: 股票代码
            
        Returns:
            格式化后的数据行
        """
        try:
            # 提取字段值
            datetime_int = record.get('datetime_int', 0)
            buy_sell_diff = record.get('buy_sell_diff', 0)
            close = record.get('close', 0)
            close_qfq = record.get('close_qfq', close)  # 前复权收盘价，默认使用收盘价
            path_length = record.get('path_length', 0)
            main_buy = record.get('main_buy', 0)
            main_sell = record.get('main_sell', 0)
            
            # 格式化价格（保留3位小数）
            close_formatted = f"{float(close):.3f}"
            close_qfq_formatted = f"{float(close_qfq):.3f}"
            
            # 组合数据行
            row = f"{stock_code}|{datetime_int}|{buy_sell_diff}|{close_formatted}|{close_qfq_formatted}|{path_length}|{main_buy}|{main_sell}"
            
            return row
            
        except Exception as e:
            self.logger.warning(f"格式化数据行失败: {e}")
            return None
    
    def _write_to_file(self, output_path: str, content: str) -> None:
        """
        写入文件
        
        Args:
            output_path: 输出路径
            content: 文件内容
        """
        try:
            # 确保输出目录存在
            output_dir = os.path.dirname(output_path)
            os.makedirs(output_dir, exist_ok=True)
            
            # 写入文件
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            self.logger.info(f"💾 文件写入完成: {os.path.basename(output_path)}")

        except Exception as e:
            self.logger.error(f"写入文件失败: {e}")
            raise
    
    def _get_default_output_dir(self) -> str:
        """获取默认输出目录"""
        try:
            # 尝试从用户配置获取
            import user_config
            if hasattr(user_config, 'output_dir'):
                return user_config.output_dir
        except ImportError:
            pass
        
        # 默认输出目录
        return "./output"


def main():
    """测试函数"""
    generator = MinuteDataFileGenerator()
    
    # 测试数据
    test_data = [
        {
            'datetime_int': 202508090931,
            'buy_sell_diff': 0,
            'close': 7.55,
            'close_qfq': 7.55,
            'path_length': 0,
            'main_buy': 0,
            'main_sell': 0
        },
        {
            'datetime_int': 202508091500,
            'buy_sell_diff': 0,
            'close': 7.60,
            'close_qfq': 7.60,
            'path_length': 0,
            'main_buy': 0,
            'main_sell': 0
        }
    ]
    
    # 生成测试文件
    output_file = generator.generate_standard_file(
        stock_code='000617',
        data_content=test_data,
        source_description='来源互联网',
        timestamp_suffix=True,
        output_dir='./test_output'
    )
    
    print(f"✅ 测试文件生成完成: {output_file}")


if __name__ == '__main__':
    main()
