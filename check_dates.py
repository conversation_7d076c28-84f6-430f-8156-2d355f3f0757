#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查日期是否为交易日
"""

from datetime import datetime

# 检查2025-03-31和2025-07-04是否为交易日
dates_to_check = [
    (2025, 3, 31),
    (2025, 7, 4)
]

for year, month, day in dates_to_check:
    date_obj = datetime(year, month, day)
    weekday_names = ['周一', '周二', '周三', '周四', '周五', '周六', '周日']
    weekday = weekday_names[date_obj.weekday()]
    is_weekend = date_obj.weekday() >= 5
    
    print(f'{year}-{month:02d}-{day:02d}: {weekday}, 是否周末={is_weekend}')
