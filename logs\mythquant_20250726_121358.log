2025-07-26 12:13:58,426 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_121358.log
2025-07-26 12:13:58,426 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 12:13:58,446 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 12:13:58,448 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 12:13:58,448 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 12:13:58,448 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-26 12:13:58,448 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-26 12:13:58,715 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.12.136.250 (60.12.136.250:7709) - 0.173s
2025-07-26 12:13:58,735 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_180.153.18.170 (180.153.18.170:7709) - 0.187s
2025-07-26 12:13:58,739 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.187s
2025-07-26 12:13:58,754 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************ (************:7709) - 0.199s
2025-07-26 12:13:58,769 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.216s
2025-07-26 12:13:58,771 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.216s
2025-07-26 12:13:58,777 - Main - WARNING - warning:311 - ⚠️ 未找到pytdx_ip配置项，尝试其他模式
2025-07-26 12:13:58,777 - Main - WARNING - warning:311 - ⚠️ 配置文件中未找到pytdx_ip配置项
2025-07-26 12:13:58,777 - Main - ERROR - error:315 - ❌ 配置文件更新失败
2025-07-26 12:13:58,777 - Main - WARNING - warning:311 - ⚠️ pytdx服务器检测失败
2025-07-26 12:13:58,777 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 12:13:58,777 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 12:13:58,778 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 12:13:58,778 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 12:13:59,218 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 12:13:59,218 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 12:13:59,224 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 12:13:59,225 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 12:13:59,225 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 12:13:59,225 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 12:13:59,225 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 12:13:59,225 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 12:13:59,226 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 12:13:59,229 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 12:13:59,229 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 12:13:59,230 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 12:13:59,230 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 12:13:59,236 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 12:13:59,648 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.41秒
2025-07-26 12:13:59,648 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.41秒
2025-07-26 12:13:59,648 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 12:13:59,648 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 12:13:59,649 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 12:13:59,649 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 12:13:59,649 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 12:13:59,649 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 12:13:59,649 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 12:13:59,649 - main_v20230219_optimized - INFO - generate_daily_data_task:3275 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 12:13:59,650 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:3814 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 12:13:59,650 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 12:13:59,650 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 12:13:59,759 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 12:13:59,759 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 12:13:59,766 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 12:13:59,766 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 12:13:59,766 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 12:13:59,766 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 12:13:59,766 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 12:13:59,766 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 12:13:59,766 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 12:13:59,770 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 12:13:59,770 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 12:13:59,770 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 12:13:59,770 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 12:13:59,777 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 12:14:00,373 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.60秒
2025-07-26 12:14:00,373 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.60秒
2025-07-26 12:14:10,777 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
