#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试OrderedDict处理
"""

import sys
from pathlib import Path
from collections import OrderedDict

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ordered_dict_processing():
    """测试OrderedDict处理"""
    print("🔍 测试OrderedDict处理")
    print("=" * 50)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        downloader = PytdxDownloader()
        
        # 创建模拟的OrderedDict数据
        test_data = [
            OrderedDict([
                ('open', 10.0),
                ('close', 10.5),
                ('high', 10.8),
                ('low', 9.9),
                ('vol', 1000),
                ('amount', 10500),
                ('year', 2025),
                ('month', 7),
                ('day', 22),
                ('hour', 9),
                ('minute', 30),
                ('datetime', '2025-07-22 09:30:00')
            ]),
            OrderedDict([
                ('open', 11.0),
                ('close', 11.5),
                ('high', 11.8),
                ('low', 10.9),
                ('vol', 1200),
                ('amount', 13800),
                ('year', 2025),
                ('month', 8),
                ('day', 7),
                ('hour', 15),
                ('minute', 0),
                ('datetime', '2025-08-07 15:00:00')
            ])
        ]
        
        print(f"📊 测试数据:")
        for i, record in enumerate(test_data):
            print(f"   {i+1}: {dict(record)}")
        
        # 测试覆盖情况分析
        coverage = downloader._check_actual_data_coverage(test_data, '20250722')
        
        print(f"\n📊 覆盖情况分析:")
        print(f"   covers_target: {coverage.get('covers_target')}")
        print(f"   earliest_date: '{coverage.get('earliest_date')}' (长度: {len(str(coverage.get('earliest_date', '')))})")
        print(f"   latest_date: '{coverage.get('latest_date')}' (长度: {len(str(coverage.get('latest_date', '')))})")
        print(f"   reason: {coverage.get('reason')}")
        
        # 检查是否成功提取了日期
        if coverage.get('earliest_date') and coverage.get('latest_date'):
            print("✅ 成功从OrderedDict中提取了日期信息")
            return True
        else:
            print("❌ 未能从OrderedDict中提取日期信息")
            return False
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_real_pytdx_data():
    """测试真实的pytdx数据"""
    print("\n🔍 测试真实的pytdx数据")
    print("=" * 50)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        downloader = PytdxDownloader()
        
        # 下载真实数据
        df = downloader.download_minute_data('000617', '20250807', '20250807', '1min')
        
        if df is None or df.empty:
            print("❌ 无法获取真实数据")
            return False
        
        print(f"✅ 获取到 {len(df)} 条真实数据")
        print(f"📊 数据类型: {type(df)}")
        
        # 检查原始数据格式
        if hasattr(df, 'to_dict'):
            records = df.to_dict('records')
            print(f"📊 转换为records后的类型: {type(records)}")
            if len(records) > 0:
                print(f"📊 第一条记录类型: {type(records[0])}")
                print(f"📊 第一条记录内容: {records[0]}")
        
        # 测试覆盖情况分析
        coverage = downloader._check_actual_data_coverage(df, '20250807')
        
        print(f"\n📊 覆盖情况分析:")
        print(f"   covers_target: {coverage.get('covers_target')}")
        print(f"   earliest_date: '{coverage.get('earliest_date')}' (长度: {len(str(coverage.get('earliest_date', '')))})")
        print(f"   latest_date: '{coverage.get('latest_date')}' (长度: {len(str(coverage.get('latest_date', '')))})")
        print(f"   reason: {coverage.get('reason')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 OrderedDict处理测试")
    print("=" * 60)
    
    # 测试OrderedDict处理
    ordered_dict_ok = test_ordered_dict_processing()
    
    # 测试真实pytdx数据
    real_data_ok = test_real_pytdx_data()
    
    print("\n" + "=" * 60)
    print("🔍 测试结果总结")
    print("=" * 60)
    print(f"OrderedDict处理: {'✅' if ordered_dict_ok else '❌'}")
    print(f"真实数据处理: {'✅' if real_data_ok else '❌'}")
    
    if ordered_dict_ok and real_data_ok:
        print("\n💡 结论：OrderedDict处理已修复")
        print("   现在应该能正确从pytdx数据中提取日期信息")

if __name__ == '__main__':
    main()
