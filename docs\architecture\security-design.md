# 安全架构设计

## 🛡️ 安全设计概览

MythQuant采用多层安全防护架构，确保金融数据的安全性和系统的可靠性。

### 安全目标
- **数据保护**: 确保金融数据的机密性、完整性和可用性
- **访问控制**: 实施严格的身份认证和授权机制
- **审计合规**: 满足金融行业的合规要求
- **威胁防护**: 防范各种安全威胁和攻击

## 🔐 安全架构层次

### 1. 网络安全层
```mermaid
graph TB
    subgraph "网络边界"
        FW[防火墙]
        LB[负载均衡器]
        WAF[Web应用防火墙]
    end
    
    subgraph "网络隔离"
        DMZ[DMZ区域]
        INTERNAL[内网区域]
        DB[数据库区域]
    end
    
    Internet --> FW
    FW --> WAF
    WAF --> LB
    LB --> DMZ
    DMZ --> INTERNAL
    INTERNAL --> DB
```

**安全措施**:
- 防火墙规则配置，只开放必要端口
- DDoS攻击防护和流量清洗
- 网络分段隔离，限制横向移动
- VPN接入控制，加密传输通道

### 2. 应用安全层
```python
# 认证中间件
class AuthenticationMiddleware:
    async def __call__(self, request: Request, call_next):
        # JWT Token验证
        token = self.extract_token(request)
        if not token or not self.validate_token(token):
            raise HTTPException(401, "Unauthorized")
        
        # 用户信息注入
        user_info = self.decode_token(token)
        request.state.user = user_info
        
        return await call_next(request)

# 授权装饰器
def require_permission(permission: str):
    def decorator(func):
        @functools.wraps(func)
        async def wrapper(request: Request, *args, **kwargs):
            user = request.state.user
            if not self.check_permission(user, permission):
                raise HTTPException(403, "Forbidden")
            return await func(request, *args, **kwargs)
        return wrapper
    return decorator
```

**安全措施**:
- JWT Token认证机制
- 基于角色的访问控制(RBAC)
- API限流和防暴力破解
- 输入验证和SQL注入防护
- XSS和CSRF攻击防护

### 3. 数据安全层
```python
# 数据加密服务
class EncryptionService:
    def __init__(self, key_manager: KeyManager):
        self._key_manager = key_manager
    
    def encrypt_sensitive_data(self, data: str) -> str:
        """加密敏感数据"""
        key = self._key_manager.get_encryption_key()
        cipher = Fernet(key)
        return cipher.encrypt(data.encode()).decode()
    
    def decrypt_sensitive_data(self, encrypted_data: str) -> str:
        """解密敏感数据"""
        key = self._key_manager.get_encryption_key()
        cipher = Fernet(key)
        return cipher.decrypt(encrypted_data.encode()).decode()

# 数据脱敏服务
class DataMaskingService:
    def mask_personal_info(self, data: Dict[str, Any]) -> Dict[str, Any]:
        """脱敏个人信息"""
        masked_data = data.copy()
        
        # 手机号脱敏
        if 'phone' in masked_data:
            phone = masked_data['phone']
            masked_data['phone'] = phone[:3] + '****' + phone[-4:]
        
        # 身份证脱敏
        if 'id_card' in masked_data:
            id_card = masked_data['id_card']
            masked_data['id_card'] = id_card[:6] + '********' + id_card[-4:]
        
        return masked_data
```

**安全措施**:
- 数据库加密存储(TDE)
- 传输层加密(TLS 1.3)
- 敏感数据脱敏处理
- 数据备份加密
- 密钥管理和轮换

### 4. 基础设施安全层
```yaml
# 容器安全配置
security_context:
  runAsNonRoot: true
  runAsUser: 1000
  readOnlyRootFilesystem: true
  allowPrivilegeEscalation: false
  capabilities:
    drop:
      - ALL
    add:
      - NET_BIND_SERVICE

# 网络策略
apiVersion: networking.k8s.io/v1
kind: NetworkPolicy
metadata:
  name: mythquant-network-policy
spec:
  podSelector:
    matchLabels:
      app: mythquant
  policyTypes:
  - Ingress
  - Egress
  ingress:
  - from:
    - podSelector:
        matchLabels:
          app: nginx-ingress
    ports:
    - protocol: TCP
      port: 8000
```

**安全措施**:
- 容器镜像安全扫描
- 最小权限原则配置
- 网络策略和微分段
- 密钥和配置管理
- 安全基线配置

## 🔍 身份认证与授权

### 认证机制
```python
class AuthenticationService:
    def __init__(self, jwt_secret: str, token_expiry: int = 3600):
        self._jwt_secret = jwt_secret
        self._token_expiry = token_expiry
    
    async def authenticate(self, username: str, password: str) -> Optional[str]:
        """用户认证"""
        # 验证用户凭据
        user = await self._verify_credentials(username, password)
        if not user:
            return None
        
        # 生成JWT Token
        payload = {
            'user_id': user.id,
            'username': user.username,
            'roles': user.roles,
            'exp': datetime.utcnow() + timedelta(seconds=self._token_expiry)
        }
        
        token = jwt.encode(payload, self._jwt_secret, algorithm='HS256')
        
        # 记录登录日志
        await self._log_login_event(user, success=True)
        
        return token
    
    async def validate_token(self, token: str) -> Optional[Dict[str, Any]]:
        """验证Token"""
        try:
            payload = jwt.decode(token, self._jwt_secret, algorithms=['HS256'])
            return payload
        except jwt.ExpiredSignatureError:
            logger.warning("Token已过期")
            return None
        except jwt.InvalidTokenError:
            logger.warning("无效Token")
            return None
```

### 授权模型
```python
class AuthorizationService:
    def __init__(self):
        self._permissions = {
            'admin': ['*'],
            'trader': ['stock:read', 'portfolio:read', 'portfolio:write'],
            'analyst': ['stock:read', 'market:read', 'report:write'],
            'viewer': ['stock:read', 'market:read']
        }
    
    def check_permission(self, user_roles: List[str], 
                        required_permission: str) -> bool:
        """检查权限"""
        for role in user_roles:
            role_permissions = self._permissions.get(role, [])
            
            # 检查通配符权限
            if '*' in role_permissions:
                return True
            
            # 检查具体权限
            if required_permission in role_permissions:
                return True
            
            # 检查模式匹配
            for permission in role_permissions:
                if self._match_permission(permission, required_permission):
                    return True
        
        return False
```

## 📊 安全监控与审计

### 安全事件监控
```python
class SecurityMonitor:
    def __init__(self, alert_manager: AlertManager):
        self._alert_manager = alert_manager
        self._security_events = []
    
    async def log_security_event(self, event_type: str, 
                                user_id: str, 
                                details: Dict[str, Any]):
        """记录安全事件"""
        event = {
            'timestamp': datetime.utcnow(),
            'event_type': event_type,
            'user_id': user_id,
            'ip_address': details.get('ip_address'),
            'user_agent': details.get('user_agent'),
            'details': details
        }
        
        self._security_events.append(event)
        
        # 检查是否需要告警
        if self._is_suspicious_event(event):
            await self._alert_manager.send_alert(
                level='HIGH',
                message=f"检测到可疑安全事件: {event_type}",
                details=event
            )
    
    def _is_suspicious_event(self, event: Dict[str, Any]) -> bool:
        """判断是否为可疑事件"""
        # 多次登录失败
        if event['event_type'] == 'login_failed':
            recent_failures = self._count_recent_failures(
                event['user_id'], 
                minutes=15
            )
            return recent_failures >= 5
        
        # 异常IP访问
        if event['event_type'] == 'login_success':
            return self._is_unusual_ip(event['user_id'], event['ip_address'])
        
        return False
```

### 审计日志
```python
class AuditLogger:
    def __init__(self, storage: AuditStorage):
        self._storage = storage
    
    async def log_operation(self, operation: str, 
                           user_id: str,
                           resource: str,
                           details: Dict[str, Any]):
        """记录操作审计"""
        audit_record = {
            'timestamp': datetime.utcnow(),
            'operation': operation,
            'user_id': user_id,
            'resource': resource,
            'details': details,
            'request_id': details.get('request_id'),
            'session_id': details.get('session_id')
        }
        
        await self._storage.store_audit_record(audit_record)
    
    async def query_audit_logs(self, 
                              user_id: Optional[str] = None,
                              operation: Optional[str] = None,
                              start_time: Optional[datetime] = None,
                              end_time: Optional[datetime] = None) -> List[Dict[str, Any]]:
        """查询审计日志"""
        return await self._storage.query_audit_records(
            user_id=user_id,
            operation=operation,
            start_time=start_time,
            end_time=end_time
        )
```

## 🔒 数据保护策略

### 数据分类
- **公开数据**: 市场公开信息，无需特殊保护
- **内部数据**: 业务数据，需要访问控制
- **敏感数据**: 用户信息，需要加密存储
- **机密数据**: 交易策略，需要最高级别保护

### 数据生命周期管理
```python
class DataLifecycleManager:
    def __init__(self):
        self._retention_policies = {
            'audit_logs': timedelta(days=2555),  # 7年
            'transaction_data': timedelta(days=1825),  # 5年
            'user_sessions': timedelta(days=30),
            'temp_files': timedelta(hours=24)
        }
    
    async def apply_retention_policy(self, data_type: str):
        """应用数据保留策略"""
        retention_period = self._retention_policies.get(data_type)
        if not retention_period:
            return
        
        cutoff_date = datetime.utcnow() - retention_period
        
        # 删除过期数据
        await self._delete_expired_data(data_type, cutoff_date)
        
        # 记录删除操作
        logger.info(f"已删除 {data_type} 类型的过期数据，截止日期: {cutoff_date}")
```

## 🚨 威胁检测与响应

### 威胁检测
```python
class ThreatDetector:
    def __init__(self):
        self._threat_patterns = [
            {'name': 'SQL注入', 'pattern': r'(union|select|insert|update|delete).*from'},
            {'name': 'XSS攻击', 'pattern': r'<script.*?>.*?</script>'},
            {'name': '路径遍历', 'pattern': r'\.\./'},
            {'name': '命令注入', 'pattern': r'(;|\||\&).*?(rm|cat|ls|ps)'}
        ]
    
    def detect_threats(self, request_data: str) -> List[str]:
        """检测威胁"""
        detected_threats = []
        
        for threat in self._threat_patterns:
            if re.search(threat['pattern'], request_data, re.IGNORECASE):
                detected_threats.append(threat['name'])
        
        return detected_threats
```

### 事件响应
```python
class IncidentResponseManager:
    def __init__(self, alert_manager: AlertManager):
        self._alert_manager = alert_manager
        self._response_procedures = {
            'data_breach': self._handle_data_breach,
            'unauthorized_access': self._handle_unauthorized_access,
            'system_compromise': self._handle_system_compromise
        }
    
    async def handle_security_incident(self, incident_type: str, 
                                     details: Dict[str, Any]):
        """处理安全事件"""
        handler = self._response_procedures.get(incident_type)
        if handler:
            await handler(details)
        
        # 发送告警
        await self._alert_manager.send_critical_alert(
            f"安全事件: {incident_type}",
            details
        )
```

## 📋 安全合规

### 合规要求
- **数据保护法规**: GDPR、个人信息保护法
- **金融监管**: 银保监会、证监会相关规定
- **行业标准**: ISO 27001、PCI DSS
- **内部政策**: 公司安全政策和流程

### 合规检查
```python
class ComplianceChecker:
    def __init__(self):
        self._compliance_rules = [
            {'name': '密码强度', 'check': self._check_password_strength},
            {'name': '数据加密', 'check': self._check_data_encryption},
            {'name': '访问日志', 'check': self._check_access_logging},
            {'name': '权限分离', 'check': self._check_privilege_separation}
        ]
    
    async def run_compliance_check(self) -> Dict[str, bool]:
        """运行合规检查"""
        results = {}
        
        for rule in self._compliance_rules:
            try:
                results[rule['name']] = await rule['check']()
            except Exception as e:
                logger.error(f"合规检查失败: {rule['name']}, 错误: {e}")
                results[rule['name']] = False
        
        return results
```

## 🛠️ 安全工具链

### 安全扫描
- **静态代码分析**: Bandit、SonarQube
- **依赖漏洞扫描**: Safety、Snyk
- **容器镜像扫描**: Trivy、Clair
- **渗透测试**: OWASP ZAP、Burp Suite

### 安全监控
- **SIEM系统**: ELK Stack + Wazuh
- **入侵检测**: Suricata、Snort
- **文件完整性**: AIDE、Tripwire
- **网络监控**: Nagios、Zabbix

---

**文档版本**: v2.0  
**最后更新**: 2024-12-XX  
**维护者**: 安全团队
