#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
价格一致性检查测试套件

专门测试价格一致性检查功能，确保：
1. 测试使用生产函数
2. 测试素材保鲜
3. 多种场景覆盖
4. 结果准确性验证

作者: AI Assistant
创建时间: 2025-08-01
"""

import sys
import os
from datetime import datetime
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent.parent
sys.path.append(str(project_root))

# 启用测试模式
from test_config import enable_test_mode
enable_test_mode()


class PriceConsistencyTestSuite:
    """价格一致性检查测试套件"""
    
    def __init__(self):
        """初始化测试套件"""
        self.test_results = {}
        self.setup_test_environment()
    
    def setup_test_environment(self):
        """设置测试环境"""
        from test_environments.fixtures.fixture_manager import get_fixture_manager
        from core.environment_manager import get_environment_manager
        
        self.fixture_manager = get_fixture_manager()
        self.env_manager = get_environment_manager()
        
        print("🧪 价格一致性检查测试套件")
        print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🌍 环境模式: {'测试' if self.env_manager.is_test_mode() else '生产'}")
        print("=" * 80)
    
    def test_basic_price_consistency(self):
        """测试基本价格一致性检查"""
        print("🧪 测试基本价格一致性检查")
        print("-" * 60)
        
        try:
            from utils.unified_interfaces import check_price_consistency
            
            # 使用测试素材
            test_file = '1min_0_000617_20250320-20250704_来源互联网.txt'
            stock_code = '000617'
            
            print(f"📋 测试参数:")
            print(f"   文件: {test_file}")
            print(f"   股票: {stock_code}")
            
            # 准备新鲜测试数据
            fresh_path = self.fixture_manager.prepare_fresh_fixture(
                test_file, 
                self.env_manager.get_input_directory()
            )
            
            if fresh_path:
                print(f"   ✅ 测试数据已准备: {fresh_path}")
                
                # 执行价格检查
                result = check_price_consistency(test_file, stock_code)
                
                print(f"\n📊 检查结果:")
                print(f"   成功: {result.get('success', False)}")
                print(f"   一致: {result.get('is_equal', False)}")
                print(f"   环境: {result.get('environment_info', {}).get('mode', 'unknown')}")
                
                # 验证使用了测试环境
                env_info = result.get('environment_info', {})
                if env_info.get('mode') == 'test':
                    print(f"   ✅ 正确使用测试环境")
                    return True
                else:
                    print(f"   ❌ 未使用测试环境")
                    return False
            else:
                print(f"   ❌ 测试数据准备失败")
                return False
                
        except Exception as e:
            print(f"❌ 基本价格一致性检查测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_multiple_stocks(self):
        """测试多个股票的价格一致性"""
        print(f"\n🧪 测试多个股票的价格一致性")
        print("-" * 60)

        test_cases = [
            ('1min_0_000617_20250320-20250704_来源互联网.txt', '000617'),
            ('1min_0_000001_20250101-20250731_来源互联网.txt', '000001'),
        ]

        success_count = 0

        for test_file, stock_code in test_cases:
            print(f"\n📋 测试股票: {stock_code} - {test_file}")

            try:
                from utils.unified_interfaces import check_price_consistency

                # 准备测试数据
                fresh_path = self.fixture_manager.prepare_fresh_fixture(
                    test_file,
                    self.env_manager.get_input_directory()
                )

                if fresh_path:
                    result = check_price_consistency(test_file, stock_code)

                    success = result.get('success', False)
                    is_equal = result.get('is_equal', False)

                    print(f"   结果: {'✅ 成功' if success else '❌ 失败'}")
                    if success:
                        print(f"   价格一致性: {'✅ 一致' if is_equal else '❌ 不一致'}")

                    if success:
                        success_count += 1
                else:
                    print(f"   ❌ 测试数据准备失败")

            except Exception as e:
                print(f"   ❌ 测试失败: {e}")

        print(f"\n📊 多股票测试结果: {success_count}/{len(test_cases)} 成功")
        return success_count == len(test_cases)
    
    def test_incremental_download_prerequisite(self):
        """测试增量下载前提条件检查"""
        print(f"\n🧪 测试增量下载前提条件检查")
        print("-" * 60)
        
        try:
            from utils.unified_interfaces import check_incremental_download_prerequisite
            
            test_file = '1min_0_000617_20250320-20250704_来源互联网.txt'
            stock_code = '000617'
            
            # 准备测试数据
            fresh_path = self.fixture_manager.prepare_fresh_fixture(
                test_file, 
                self.env_manager.get_input_directory()
            )
            
            if fresh_path:
                has_prerequisite, details = check_incremental_download_prerequisite(
                    test_file, stock_code
                )
                
                print(f"📊 检查结果:")
                print(f"   具备前提条件: {has_prerequisite}")
                print(f"   结论: {details.get('conclusion', 'N/A')}")
                print(f"   建议: {details.get('recommendation', 'N/A')}")
                
                # 验证环境信息
                env_info = details.get('environment_info', {})
                if env_info.get('mode') == 'test':
                    print(f"   ✅ 正确使用测试环境")
                    return True
                else:
                    print(f"   ❌ 环境信息异常")
                    return False
            else:
                print(f"   ❌ 测试数据准备失败")
                return False
                
        except Exception as e:
            print(f"❌ 增量下载前提条件检查测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_structured_downloader_integration(self):
        """测试结构化下载器集成"""
        print(f"\n🧪 测试结构化下载器集成")
        print("-" * 60)
        
        try:
            from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
            
            downloader = StructuredInternetMinuteDownloader()
            
            test_file = '1min_0_000617_20250320-20250704_来源互联网.txt'
            stock_code = '000617'
            
            # 准备测试数据
            fresh_path = self.fixture_manager.prepare_fresh_fixture(
                test_file, 
                self.env_manager.get_input_directory()
            )
            
            if fresh_path:
                has_prerequisite, details = downloader.check_incremental_download_prerequisite(
                    test_file, stock_code
                )
                
                print(f"📊 检查结果:")
                print(f"   具备前提条件: {has_prerequisite}")
                print(f"   结论: {details.get('conclusion', 'N/A')}")
                
                # 验证是否使用了统一接口
                if 'environment_info' in details:
                    print(f"   ✅ 使用了统一接口")
                    env_mode = details['environment_info'].get('mode', 'unknown')
                    print(f"   环境模式: {env_mode}")
                    return env_mode == 'test'
                else:
                    print(f"   ❌ 未使用统一接口")
                    return False
            else:
                print(f"   ❌ 测试数据准备失败")
                return False
                
        except Exception as e:
            print(f"❌ 结构化下载器集成测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def test_data_freshness(self):
        """测试数据保鲜机制"""
        print(f"\n🧪 测试数据保鲜机制")
        print("-" * 60)
        
        try:
            test_file = '1min_0_000617_20250320-20250704_来源互联网.txt'
            
            # 第一次准备数据
            fresh_path1 = self.fixture_manager.prepare_fresh_fixture(
                test_file, 
                self.env_manager.get_input_directory()
            )
            
            if fresh_path1:
                print(f"   ✅ 第一次数据准备成功: {fresh_path1}")
                
                # 修改文件（模拟测试过程中的修改）
                with open(fresh_path1, 'a', encoding='utf-8') as f:
                    f.write("# 测试修改标记\n")
                
                # 第二次准备数据（应该是新鲜的）
                fresh_path2 = self.fixture_manager.prepare_fresh_fixture(
                    test_file, 
                    self.env_manager.get_input_directory()
                )
                
                if fresh_path2:
                    print(f"   ✅ 第二次数据准备成功: {fresh_path2}")
                    
                    # 验证是否是新鲜数据（不包含修改标记）
                    with open(fresh_path2, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    if "测试修改标记" not in content:
                        print(f"   ✅ 数据保鲜机制工作正常")
                        return True
                    else:
                        print(f"   ❌ 数据保鲜机制失效")
                        return False
                else:
                    print(f"   ❌ 第二次数据准备失败")
                    return False
            else:
                print(f"   ❌ 第一次数据准备失败")
                return False
                
        except Exception as e:
            print(f"❌ 数据保鲜机制测试失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def run_all_tests(self):
        """运行所有测试"""
        tests = [
            ("基本价格一致性检查", self.test_basic_price_consistency),
            ("多股票价格一致性", self.test_multiple_stocks),
            ("增量下载前提条件检查", self.test_incremental_download_prerequisite),
            ("结构化下载器集成", self.test_structured_downloader_integration),
            ("数据保鲜机制", self.test_data_freshness),
        ]
        
        passed_tests = 0
        total_tests = len(tests)
        
        for test_name, test_func in tests:
            try:
                result = test_func()
                self.test_results[test_name] = result
                if result:
                    passed_tests += 1
            except Exception as e:
                print(f"❌ 测试 {test_name} 执行异常: {e}")
                self.test_results[test_name] = False
        
        # 生成测试报告
        pass_rate = passed_tests / total_tests
        
        print(f"\n📊 测试结果汇总")
        print("=" * 80)
        
        for test_name, result in self.test_results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print(f"   {test_name}: {status}")
        
        print(f"\n📈 统计信息:")
        print(f"   总测试数: {total_tests}")
        print(f"   通过测试数: {passed_tests}")
        print(f"   失败测试数: {total_tests - passed_tests}")
        print(f"   通过率: {pass_rate:.1%}")
        
        if pass_rate >= 0.8:
            print(f"\n🎉 价格一致性检查测试套件通过！")
        else:
            print(f"\n⚠️ 价格一致性检查测试套件部分失败，需要进一步调试")
        
        return pass_rate >= 0.8


def main():
    """主函数"""
    test_suite = PriceConsistencyTestSuite()
    success = test_suite.run_all_tests()
    return 0 if success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
