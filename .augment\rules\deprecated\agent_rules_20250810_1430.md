---
归档原因: 规则文件精简重组优化
归档时间: 2025-08-10 14:30
归档说明: 本文件因存在重复内容、文件路径映射过于具体、规则层次混乱而被归档。
         新版本将重组内容结构，更新文件路径映射，增强AI自动化应用能力。
原始文件: .augment/rules/agent_rules.md
优化目标: 从96行优化到80行以内，重组内容结构，增强智能化程度
---

# MythQuant项目AI行为规则 (归档版本)

## 智能文件定位规则
当用户提及以下功能时，自动定位到对应文件：
1. **配置修改** → `user_config.py`
2. **核心任务管理** → `src/mythquant/core/task_manager.py`
3. **数据处理工具** → `utils/` 目录下对应模块
4. **测试相关** → `test_environments/` 目录下对应测试
5. **数据质量检查** → `utils/missing_data_processor.py`
6. **数据修复** → `utils/pytdx_data_repairer.py`

## 代码修改标准工作流
1. **理解需求**：分析用户描述，识别涉及的功能模块
2. **依赖检查**：确认修改不会破坏现有功能
3. **创建测试**：编写测试脚本验证修改效果
4. **备份建议**：如修改核心文件，建议先备份

## 知识库智能引用规则
### **自动判断是否需要引用知识库**
在回答以下类型问题时，**必须主动查看并引用相关知识库**：
1. **测试相关问题**：查看 `docs/knowledge/testing_quality_rules.md`
2. **调试相关问题**：查看 `docs/knowledge/troubleshooting/debugging_knowledge_base.md`
3. **代码质量问题**：查看相关知识库
4. **重复出现的问题**：查看FAQ知识库寻找已知解决方案

### **知识库引用触发词**
遇到以下关键词时，主动查看对应知识库：
- **测试、验证、检查、质量** → `testing_quality_rules.md`
- **调试、错误、问题、bug** → `debugging_knowledge_base.md`
- **文件名、时间格式、数据格式** → 两个知识库都查看
- **pytdx、数据源、配置** → `debugging_knowledge_base.md`

### **知识库引用工作流**
1. **识别问题类型**：判断是否属于需要引用知识库的问题
2. **主动查看**：使用view工具查看相关知识库文件
3. **应用规则**：将知识库中的规则和经验应用到当前问题
4. **明确说明**：告知用户正在参考哪个知识库的哪些规则

## 测试驱动质量保证规则
### **警醒测试原则**
- **主动测试**：在用户要求测试时，必须在专门的测试环境中进行全面测试
- **多层验证**：静态检查 + 功能测试 + 实际执行验证的三层测试体系
- **问题发现导向**：测试的目标是发现问题而非证明正确，保持警醒态度
- **实际运行验证**：不能仅依赖静态分析，必须进行实际程序运行测试

### **测试环境管理**
- **环境隔离**：测试必须在test_environments/目录下的专门环境中进行
- **路径规范**：测试脚本使用正确的项目根目录定位和绝对路径
- **编码处理**：测试脚本必须正确处理Windows下的Unicode编码问题
- **报告生成**：每次重要测试都必须生成详细的测试报告

## 流程优化器自动应用规则
- **智能识别触发**：AI助手必须智能识别需要流程优化的场景，无需用户明确提示
- **自动集成策略**：在代码修改、问题修复、新功能开发时自动集成流程优化器
- **用户体验导向**：以用户体验为核心，自动隐藏技术细节，突出关键信息
- **系统性优化思维**：不满足于局部优化，建立系统性的流程管理和输出协调机制
- **质量标准执行**：自动确保输出质量达到95%以上，流程清晰度提升90%以上

## 知识沉淀自动化规则
- **问题模式自动识别**：AI助手必须自动识别新的问题模式并沉淀到调试知识库
- **FAQ自动更新**：重要问题解决后必须自动评估是否需要添加到FAQ知识库
- **规则体系自动完善**：发现规则缺失或不合理时必须主动提出更新建议
- **经验教训自动提取**：每次重要工作完成后必须自动提取经验教训和最佳实践
- **举一反三自动执行**：解决一个问题后必须自动分析类似问题的预防和改进措施

## 测试与生产环境一致性保证规则
- **多环境验证强制**：重要功能修改后必须在测试环境中进行验证，避免直接在生产环境测试
- **端到端测试优先**：不能仅依赖单元测试，必须进行完整的端到端测试
- **多重实现检查**：发现方法缺失错误时，必须检查项目中是否存在多个同名类
- **依赖完整性验证**：确保所有依赖模块在测试环境中都存在且可用
- **导入路径统一**：确保所有导入路径在不同环境中都正确，优先使用绝对导入

## 复制检测和防护
进行代码修改前，检查：
1. 是否已有类似功能实现
2. 新功能是否会与现有功能冲突
3. 是否可以复用现有组件
4. 修改是否符合项目架构设计

## 用户交互优化
- 主动识别用户可能的文件定位错误
- 提供具体的修改建议而非抽象指导
- 解释技术决策的原因
- 在不确定时主动询问确认

---

**归档文件类型**: Auto规则 - AI自动检测应用 (归档版本)
**原始更新时间**: 2025-08-10
**归档时间**: 2025-08-10 14:30
**归档原因**: 精简重组优化，存在重复内容，文件路径映射需要更新
**后续版本**: 重组为智能化程度更高的版本
