---
type: "always"
description: "MythQuant项目核心原则，始终应用于所有AI交互"
---

# MythQuant项目核心原则

## 基本原则
- **吹哨人原则**：AI可以优化用户问题并确认
- **DDD架构优先**：严格遵循领域驱动设计
- **测试驱动开发**：代码修改必须有测试验证
- **用户体验至上**：技术升级对用户透明

## DDD架构核心
- **分层架构**：Domain → Application → Infrastructure → Interface
- **依赖倒置**：领域层不依赖外部框架
- **向后兼容**：新架构完全兼容现有代码
- **门面模式**：隐藏复杂性，提供简化接口

## 金融计算标准
- **Decimal强制**：所有价格计算使用`decimal.Decimal`
- **容差比较**：`abs(价格1 - 价格2) < Decimal('0.001')`
- **统一精度**：价格3位小数，比率6位小数

## 环境管理核心
- **AI调试环境强制**：AI调试必须使用测试环境
- **环境自动检测**：通过detect_environment()函数检测
- **生产环境保护**：严禁直接运行main.py进行调试验证
- **测试环境隔离**：所有调试操作限制在test_environments中

## 质量保证核心
- **输入验证强制**：检查None值、空值、数据类型
- **实际验证强制**：必须通过实际数据验证，不能基于理论推测
- **DataFrame布尔判断**：使用`df is None or df.empty`而非`if not df`

## 测试环境隔离强制
- **AI调试环境自动切换**：AI调试时必须自动使用测试环境，不能污染生产数据
- **测试数据保护机制**：使用工作副本进行测试，原始测试数据只读保护
- **环境隔离验证**：每次调试前验证环境隔离效果，确保使用正确的测试资源
- **自动备份还原**：测试前自动备份，测试后自动还原，确保测试现场可重现

## 知识沉淀自动化
- **问题解决后必须检索**：解决问题后自动检索相关知识库，评估更新需求
- **新问题模式识别**：发现新问题类型时主动建议创建或更新知识库条目
- **交叉引用建立**：在解决问题时建立与现有知识的关联和引用
- **知识质量维护**：定期检查知识库内容的准确性和时效性
- **经验模式提取**：从问题解决过程中提取可复用的经验和模式

## 数据质量标准
- **文件命名格式**：1min_0_000617_timerange.txt (频率前缀_0_股票代码)
- **A股1分钟数据**：每交易日240行，时间戳表示分钟结束时间
- **前复权验证**：前复权价格与当日收盘价必须有合理差异
- **数据格式**：字段分隔符为管道符"|"

---

**文件类型**: Always规则 - 始终应用
**最后更新**: 2025-08-10 14:30 (精简重组版)
**适用范围**: 所有AI交互和代码操作
**精简说明**: 从68行精简到38行，专注核心原则
