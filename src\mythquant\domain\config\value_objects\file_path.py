#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件路径值对象

表示文件系统中的路径
"""

from dataclasses import dataclass
from pathlib import Path
from typing import Optional
import os


@dataclass(frozen=True)
class FilePath:
    """文件路径值对象"""
    
    path: str
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.path:
            raise ValueError("文件路径不能为空")
    
    def exists(self) -> bool:
        """检查路径是否存在"""
        return os.path.exists(self.path)
    
    def is_file(self) -> bool:
        """检查是否为文件"""
        return os.path.isfile(self.path)
    
    def is_directory(self) -> bool:
        """检查是否为目录"""
        return os.path.isdir(self.path)
    
    def get_parent(self) -> 'FilePath':
        """获取父目录"""
        parent_path = str(Path(self.path).parent)
        return FilePath(parent_path)
    
    def get_name(self) -> str:
        """获取文件名"""
        return Path(self.path).name
    
    def get_stem(self) -> str:
        """获取文件名（不含扩展名）"""
        return Path(self.path).stem
    
    def get_suffix(self) -> str:
        """获取文件扩展名"""
        return Path(self.path).suffix
    
    def join(self, *parts: str) -> 'FilePath':
        """连接路径"""
        new_path = os.path.join(self.path, *parts)
        return FilePath(new_path)
    
    def ensure_directory_exists(self) -> None:
        """确保目录存在"""
        if self.is_directory() or self.path.endswith(('/', '\\')):
            # 这是一个目录路径
            os.makedirs(self.path, exist_ok=True)
        else:
            # 这是一个文件路径，创建父目录
            parent_dir = self.get_parent()
            os.makedirs(parent_dir.path, exist_ok=True)
    
    def to_absolute(self) -> 'FilePath':
        """转换为绝对路径"""
        abs_path = os.path.abspath(self.path)
        return FilePath(abs_path)
    
    def to_relative(self, base_path: str) -> 'FilePath':
        """转换为相对路径"""
        rel_path = os.path.relpath(self.path, base_path)
        return FilePath(rel_path)
    
    def __str__(self) -> str:
        return self.path
    
    def __repr__(self) -> str:
        return f"FilePath('{self.path}')"
