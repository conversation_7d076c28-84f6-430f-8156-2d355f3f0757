#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
追踪额外的数据质量稽核调用

找出在四步流程开始之前执行的额外稽核
"""

import os
import sys
from pathlib import Path

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def patch_missing_data_processor():
    """给MissingDataProcessor打补丁，追踪调用"""
    print("🔍 给MissingDataProcessor打补丁，追踪调用")
    print("=" * 80)
    
    try:
        from utils.missing_data_processor import MissingDataProcessor
        
        # 保存原始方法
        original_detect = MissingDataProcessor.detect_missing_minute_data
        
        def patched_detect(self, file_path, stock_code, silent=True):
            """打补丁的detect_missing_minute_data方法"""
            import traceback
            
            print(f"\n🚨 MissingDataProcessor.detect_missing_minute_data被调用！")
            print(f"   文件路径: {file_path}")
            print(f"   股票代码: {stock_code}")
            print(f"   静默模式: {silent}")
            
            print(f"\n📋 调用堆栈:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-8:], 1):  # 显示最后8个调用帧
                print(f"   {i}. {frame.strip()}")
            
            # 调用原始方法
            return original_detect(self, file_path, stock_code, silent)
        
        # 替换方法
        MissingDataProcessor.detect_missing_minute_data = patched_detect
        
        print("✅ MissingDataProcessor补丁安装成功")
        return True
        
    except Exception as e:
        print(f"❌ 安装补丁失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def patch_pytdx_downloader():
    """给PytdxDownloader打补丁，追踪数据覆盖不足警告"""
    print("\n🔍 给PytdxDownloader打补丁，追踪数据覆盖不足警告")
    print("=" * 80)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        # 保存原始方法
        original_download = PytdxDownloader.download_minute_data
        
        def patched_download(self, stock_code, start_date, end_date, frequency, suppress_warnings=False):
            """打补丁的download_minute_data方法"""
            import traceback
            
            print(f"\n🚨 PytdxDownloader.download_minute_data被调用！")
            print(f"   股票代码: {stock_code}")
            print(f"   开始日期: {start_date}")
            print(f"   结束日期: {end_date}")
            print(f"   频率: {frequency}")
            print(f"   抑制警告: {suppress_warnings}")
            
            print(f"\n📋 调用堆栈:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-6:], 1):  # 显示最后6个调用帧
                print(f"   {i}. {frame.strip()}")
            
            # 调用原始方法
            return original_download(self, stock_code, start_date, end_date, frequency, suppress_warnings)
        
        # 替换方法
        PytdxDownloader.download_minute_data = patched_download
        
        print("✅ PytdxDownloader补丁安装成功")
        return True
        
    except Exception as e:
        print(f"❌ 安装补丁失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_main_with_tracing():
    """运行主程序并追踪调用"""
    print("\n🚀 运行主程序并追踪调用")
    print("=" * 80)
    
    try:
        # 导入并运行主程序的核心逻辑
        from main import main
        
        print("📋 开始执行main()函数...")
        main()
        
        return True
        
    except Exception as e:
        print(f"❌ 运行主程序失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 追踪额外的数据质量稽核调用")
    print("=" * 120)
    
    # 安装补丁
    missing_data_ok = patch_missing_data_processor()
    pytdx_ok = patch_pytdx_downloader()
    
    if missing_data_ok and pytdx_ok:
        print(f"\n✅ 所有补丁安装成功，开始追踪...")
        
        # 运行主程序
        main_ok = run_main_with_tracing()
        
        print(f"\n🎯 追踪完成")
        print("=" * 80)
        print(f"补丁安装: {'✅' if missing_data_ok and pytdx_ok else '❌'}")
        print(f"主程序运行: {'✅' if main_ok else '❌'}")
        
        if main_ok:
            print(f"\n💡 通过调用堆栈分析，我们应该能找到额外稽核的来源")
        else:
            print(f"\n❌ 主程序运行失败，无法完成追踪")
    else:
        print(f"\n❌ 补丁安装失败，无法进行追踪")

if __name__ == '__main__':
    main()
