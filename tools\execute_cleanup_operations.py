#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行清理操作

删除临时文件、缓存和不需要的备份目录
"""

import os
import shutil
import glob
from datetime import datetime, timedelta
from pathlib import Path
from typing import List, Dict


class CleanupOperationsExecutor:
    """清理操作执行器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def clean_python_cache(self):
        """清理Python缓存文件"""
        print("🐍 清理Python缓存文件...")
        
        cleaned_count = 0
        total_size = 0
        
        # 清理__pycache__目录
        for pycache_dir in self.project_root.rglob("__pycache__"):
            if pycache_dir.is_dir():
                try:
                    dir_size = self._get_directory_size(pycache_dir)
                    shutil.rmtree(pycache_dir)
                    print(f"   ✅ 已删除: {pycache_dir.relative_to(self.project_root)}")
                    cleaned_count += 1
                    total_size += dir_size
                except Exception as e:
                    print(f"   ❌ 删除失败: {pycache_dir}, 错误: {e}")
        
        # 清理.pyc文件
        for pyc_file in self.project_root.rglob("*.pyc"):
            try:
                file_size = pyc_file.stat().st_size
                pyc_file.unlink()
                print(f"   ✅ 已删除: {pyc_file.relative_to(self.project_root)}")
                cleaned_count += 1
                total_size += file_size
            except Exception as e:
                print(f"   ❌ 删除失败: {pyc_file}, 错误: {e}")
        
        print(f"   📊 Python缓存清理完成: {cleaned_count} 个项目, {total_size/1024/1024:.2f} MB")
    
    def clean_backup_directories(self):
        """清理备份目录"""
        print("\n📦 清理备份目录...")
        
        backup_dirs = [
            ("archive", "旧版本归档"),
            ("archived_files", "已归档文件"),
            ("migration_backups", "迁移备份"),
            ("manual_cleanup_backup", "手动清理备份"),
            ("final_cleanup_backup", "最终清理备份"),
            ("docs_rename_backup", "文档重命名备份")
        ]
        
        cleaned_count = 0
        total_size = 0
        
        for dir_name, description in backup_dirs:
            backup_path = self.project_root / dir_name
            
            if backup_path.exists() and backup_path.is_dir():
                try:
                    dir_size = self._get_directory_size(backup_path)
                    shutil.rmtree(backup_path)
                    print(f"   ✅ 已删除: {dir_name} ({description})")
                    cleaned_count += 1
                    total_size += dir_size
                except Exception as e:
                    print(f"   ❌ 删除失败: {dir_name}, 错误: {e}")
            else:
                print(f"   ⚠️ 不存在: {dir_name}")
        
        print(f"   📊 备份目录清理完成: {cleaned_count} 个目录, {total_size/1024/1024:.2f} MB")
    
    def clean_temp_cache_files(self):
        """清理临时缓存文件"""
        print("\n🗂️ 清理临时缓存文件...")
        
        temp_items = [
            ("cache/temp", "临时缓存目录"),
            ("cache/files", "缓存文件目录"),
            ("signal", "信号文件目录")
        ]
        
        cleaned_count = 0
        total_size = 0
        
        for item_path, description in temp_items:
            full_path = self.project_root / item_path
            
            if full_path.exists():
                try:
                    if full_path.is_dir():
                        dir_size = self._get_directory_size(full_path)
                        shutil.rmtree(full_path)
                        print(f"   ✅ 已删除: {item_path} ({description})")
                        total_size += dir_size
                    else:
                        file_size = full_path.stat().st_size
                        full_path.unlink()
                        print(f"   ✅ 已删除: {item_path} ({description})")
                        total_size += file_size
                    
                    cleaned_count += 1
                except Exception as e:
                    print(f"   ❌ 删除失败: {item_path}, 错误: {e}")
            else:
                print(f"   ⚠️ 不存在: {item_path}")
        
        print(f"   📊 临时缓存清理完成: {cleaned_count} 个项目, {total_size/1024/1024:.2f} MB")
    
    def clean_test_artifacts(self):
        """清理测试产物"""
        print("\n🧪 清理测试产物...")
        
        test_dirs = [
            ("benchmark_results", "基准测试结果"),
            ("validation_results", "验证结果"),
            ("reports", "报告文件")
        ]
        
        cleaned_count = 0
        total_size = 0
        
        for dir_name, description in test_dirs:
            test_path = self.project_root / dir_name
            
            if test_path.exists() and test_path.is_dir():
                try:
                    dir_size = self._get_directory_size(test_path)
                    shutil.rmtree(test_path)
                    print(f"   ✅ 已删除: {dir_name} ({description})")
                    cleaned_count += 1
                    total_size += dir_size
                except Exception as e:
                    print(f"   ❌ 删除失败: {dir_name}, 错误: {e}")
            else:
                print(f"   ⚠️ 不存在: {dir_name}")
        
        print(f"   📊 测试产物清理完成: {cleaned_count} 个目录, {total_size/1024/1024:.2f} MB")
    
    def clean_old_logs(self, keep_days: int = 30):
        """清理旧日志文件"""
        print(f"\n📋 清理{keep_days}天前的日志文件...")
        
        logs_dir = self.project_root / "logs"
        if not logs_dir.exists():
            print("   ⚠️ 日志目录不存在")
            return
        
        cutoff_date = datetime.now() - timedelta(days=keep_days)
        cleaned_count = 0
        total_size = 0
        
        for log_file in logs_dir.glob("*.log"):
            try:
                file_mtime = datetime.fromtimestamp(log_file.stat().st_mtime)
                
                if file_mtime < cutoff_date:
                    file_size = log_file.stat().st_size
                    log_file.unlink()
                    print(f"   ✅ 已删除: {log_file.name} (修改时间: {file_mtime.strftime('%Y-%m-%d')})")
                    cleaned_count += 1
                    total_size += file_size
                else:
                    print(f"   ⏳ 保留: {log_file.name} (修改时间: {file_mtime.strftime('%Y-%m-%d')})")
                    
            except Exception as e:
                print(f"   ❌ 处理失败: {log_file.name}, 错误: {e}")
        
        print(f"   📊 日志清理完成: {cleaned_count} 个文件, {total_size/1024/1024:.2f} MB")
    
    def clean_misc_files(self):
        """清理其他杂项文件"""
        print("\n🗃️ 清理其他杂项文件...")
        
        misc_files = [
            "architecture_quality_report.json",
            "*.tmp",
            "*.temp"
        ]
        
        cleaned_count = 0
        total_size = 0
        
        for pattern in misc_files:
            if '*' in pattern:
                # 通配符模式
                for file_path in self.project_root.glob(pattern):
                    try:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        print(f"   ✅ 已删除: {file_path.name}")
                        cleaned_count += 1
                        total_size += file_size
                    except Exception as e:
                        print(f"   ❌ 删除失败: {file_path.name}, 错误: {e}")
            else:
                # 具体文件
                file_path = self.project_root / pattern
                if file_path.exists():
                    try:
                        file_size = file_path.stat().st_size
                        file_path.unlink()
                        print(f"   ✅ 已删除: {pattern}")
                        cleaned_count += 1
                        total_size += file_size
                    except Exception as e:
                        print(f"   ❌ 删除失败: {pattern}, 错误: {e}")
        
        print(f"   📊 杂项文件清理完成: {cleaned_count} 个文件, {total_size/1024/1024:.2f} MB")
    
    def _get_directory_size(self, path: Path) -> int:
        """获取目录大小（字节）"""
        total_size = 0
        try:
            for item in path.rglob("*"):
                if item.is_file():
                    total_size += item.stat().st_size
        except:
            pass
        return total_size
    
    def execute_cleanup(self):
        """执行清理操作"""
        print("🧹 开始执行清理操作")
        print("=" * 60)
        
        start_time = datetime.now()
        
        # 执行各种清理操作
        self.clean_python_cache()
        self.clean_backup_directories()
        self.clean_temp_cache_files()
        self.clean_test_artifacts()
        self.clean_old_logs(keep_days=30)
        self.clean_misc_files()
        
        end_time = datetime.now()
        duration = end_time - start_time
        
        print(f"\n🎉 清理操作完成！")
        print(f"⏱️ 耗时: {duration.total_seconds():.2f} 秒")
        
        # 生成清理报告
        self._generate_cleanup_report()
    
    def _generate_cleanup_report(self):
        """生成清理报告"""
        report_file = self.project_root / f"cleanup_report_{self.timestamp}.txt"
        
        report_content = f"""
# 架构清理操作报告

## 基本信息
- 清理时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 项目路径: {self.project_root}
- 操作类型: 临时文件和缓存清理

## 清理项目
1. Python缓存文件 (__pycache__, *.pyc)
2. 备份目录 (archive, archived_files, migration_backups等)
3. 临时缓存文件 (cache/temp, cache/files, signal)
4. 测试产物 (benchmark_results, validation_results, reports)
5. 旧日志文件 (保留最近30天)
6. 杂项文件 (临时文件等)

## 清理结果
- 所有指定的临时文件和缓存已清理
- 项目结构更加清洁，专注于DDD架构
- 磁盘空间得到释放

## 后续建议
1. 运行完整测试套件验证系统功能
2. 更新.gitignore文件排除临时文件
3. 定期执行清理操作保持项目整洁

---
生成时间: {datetime.now().isoformat()}
"""
        
        try:
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write(report_content)
            print(f"📊 清理报告已生成: {report_file}")
        except Exception as e:
            print(f"❌ 报告生成失败: {e}")


def main():
    """主函数"""
    executor = CleanupOperationsExecutor()
    
    print("⚠️ 即将执行清理操作")
    print("📋 清理内容:")
    print("   1. Python缓存文件 (__pycache__, *.pyc)")
    print("   2. 备份目录 (archive, archived_files, migration_backups等)")
    print("   3. 临时缓存文件 (cache/temp, cache/files, signal)")
    print("   4. 测试产物 (benchmark_results, validation_results, reports)")
    print("   5. 旧日志文件 (保留最近30天)")
    print("   6. 杂项临时文件")
    print()
    
    confirm = input("🤔 确认执行清理操作? (y/N): ").strip().lower()
    
    if confirm == 'y':
        executor.execute_cleanup()
    else:
        print("❌ 操作已取消")


if __name__ == "__main__":
    main()
