{"overall_score": 9.093625, "overall_level": "A", "timestamp": "2025-08-02T19:08:08.507560", "assessor": "Architecture Quality Framework", "version": "1.0", "dimensions": [{"dimension": "maintainability", "score": 9.559999999999999, "level": "A+", "weight": 0.25, "metrics": [{"name": "圈复杂度", "description": "方法的圈复杂度平均值", "current_value": 7.5, "target_value": 5.0, "score": 10.0, "level": "A++", "unit": "复杂度值"}, {"name": "代码重复率", "description": "重复代码占总代码的百分比", "current_value": 4.2, "target_value": 2.0, "score": 10.0, "level": "A++", "unit": "%"}, {"name": "方法长度", "description": "方法平均行数", "current_value": 28.0, "target_value": 20.0, "score": 10.0, "level": "A++", "unit": "行"}, {"name": "类职责单一性", "description": "类的职责单一性评分", "current_value": 8.2, "target_value": 9.0, "score": 9.049999999999999, "level": "A", "unit": "评分"}, {"name": "文档覆盖率", "description": "代码文档覆盖率", "current_value": 85.0, "target_value": 95.0, "score": 8.75, "level": "B+", "unit": "%"}]}, {"dimension": "scalability", "score": 9.125, "level": "A", "weight": 0.2, "metrics": [{"name": "模块耦合度", "description": "模块间耦合度", "current_value": 0.35, "target_value": 0.2, "score": 10.0, "level": "A++", "unit": "耦合系数"}, {"name": "内聚性", "description": "模块内聚性", "current_value": 0.75, "target_value": 0.9, "score": 8.75, "level": "B+", "unit": "内聚系数"}, {"name": "扩展点覆盖", "description": "可扩展点的覆盖率", "current_value": 75.0, "target_value": 90.0, "score": 8.75, "level": "B+", "unit": "%"}, {"name": "插件化程度", "description": "系统插件化程度", "current_value": 7.5, "target_value": 9.0, "score": 8.75, "level": "B+", "unit": "评分"}]}, {"dimension": "testability", "score": 8.8475, "level": "B+", "weight": 0.2, "metrics": [{"name": "测试覆盖率", "description": "代码测试覆盖率", "current_value": 88.0, "target_value": 95.0, "score": 9.2, "level": "A", "unit": "%"}, {"name": "单元测试质量", "description": "单元测试质量评分", "current_value": 8.0, "target_value": 9.0, "score": 8.75, "level": "B+", "unit": "评分"}, {"name": "Mock使用合理性", "description": "Mock对象使用的合理性", "current_value": 7.8, "target_value": 9.0, "score": 9.2, "level": "A", "unit": "评分"}, {"name": "测试金字塔完整性", "description": "测试金字塔结构完整性", "current_value": 7.0, "target_value": 9.0, "score": 8.0, "level": "B", "unit": "评分"}]}, {"dimension": "performance", "score": 9.1625, "level": "A", "weight": 0.15, "metrics": [{"name": "响应时间", "description": "平均响应时间", "current_value": 120.0, "target_value": 50.0, "score": 10.0, "level": "A++", "unit": "ms"}, {"name": "吞吐量", "description": "系统吞吐量", "current_value": 800.0, "target_value": 2000.0, "score": 8.9, "level": "B+", "unit": "RPS"}, {"name": "内存使用效率", "description": "内存使用效率", "current_value": 65.0, "target_value": 80.0, "score": 8.75, "level": "B+", "unit": "%"}, {"name": "算法复杂度", "description": "关键算法时间复杂度", "current_value": 7.5, "target_value": 9.0, "score": 8.75, "level": "B+", "unit": "复杂度等级"}]}, {"dimension": "security", "score": 9.25, "level": "A", "weight": 0.1, "metrics": [{"name": "安全漏洞数量", "description": "已知安全漏洞数量", "current_value": 1.0, "target_value": 0.0, "score": 10.0, "level": "A++", "unit": "个"}, {"name": "认证授权完整性", "description": "认证授权机制完整性", "current_value": 8.5, "target_value": 9.5, "score": 8.75, "level": "B+", "unit": "评分"}, {"name": "数据加密覆盖率", "description": "敏感数据加密覆盖率", "current_value": 90.0, "target_value": 100.0, "score": 8.75, "level": "B+", "unit": "%"}, {"name": "安全审计完整性", "description": "安全审计日志完整性", "current_value": 8.0, "target_value": 9.0, "score": 8.75, "level": "B+", "unit": "评分"}]}, {"dimension": "deployability", "score": 8.0975, "level": "B", "weight": 0.1, "metrics": [{"name": "部署自动化程度", "description": "部署流程自动化程度", "current_value": 75.0, "target_value": 95.0, "score": 7.5, "level": "C+", "unit": "%"}, {"name": "环境一致性", "description": "开发、测试、生产环境一致性", "current_value": 8.2, "target_value": 9.5, "score": 8.299999999999999, "level": "B", "unit": "评分"}, {"name": "回滚能力", "description": "系统回滚能力", "current_value": 7.8, "target_value": 9.0, "score": 8.45, "level": "B", "unit": "评分"}, {"name": "监控覆盖率", "description": "系统监控覆盖率", "current_value": 82.0, "target_value": 95.0, "score": 8.3, "level": "B", "unit": "%"}]}]}