#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
回退脚本 - 回退到原始主程序入口
当轻量级主程序出现问题时，快速回退到稳定版本
"""

import os
import sys
import shutil
from datetime import datetime
import logging

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class RollbackManager:
    """回退管理器"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.backup_file = os.path.join(self.project_root, "main_v20230219_optimized.py.backup_stable")
        self.current_file = os.path.join(self.project_root, "main_v20230219_optimized.py")
        self.new_main_file = os.path.join(self.project_root, "main.py")
        
        # 新增的模块文件
        self.new_modules = [
            "core/application.py",
            "core/stock_processor.py", 
            "core/task_manager.py"
        ]
    
    def check_backup_exists(self) -> bool:
        """检查备份文件是否存在"""
        exists = os.path.exists(self.backup_file)
        if exists:
            logger.info(f"✅ 找到备份文件: {self.backup_file}")
        else:
            logger.error(f"❌ 备份文件不存在: {self.backup_file}")
        return exists
    
    def backup_current_state(self) -> bool:
        """备份当前状态"""
        try:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 备份当前主文件
            if os.path.exists(self.current_file):
                backup_current = f"{self.current_file}.before_rollback_{timestamp}"
                shutil.copy2(self.current_file, backup_current)
                logger.info(f"📦 备份当前主文件: {backup_current}")
            
            # 备份新主程序
            if os.path.exists(self.new_main_file):
                backup_new_main = f"{self.new_main_file}.before_rollback_{timestamp}"
                shutil.copy2(self.new_main_file, backup_new_main)
                logger.info(f"📦 备份新主程序: {backup_new_main}")
            
            # 备份新模块
            for module_path in self.new_modules:
                full_path = os.path.join(self.project_root, module_path)
                if os.path.exists(full_path):
                    backup_module = f"{full_path}.before_rollback_{timestamp}"
                    shutil.copy2(full_path, backup_module)
                    logger.info(f"📦 备份模块: {backup_module}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 备份当前状态失败: {e}")
            return False
    
    def restore_original_main(self) -> bool:
        """恢复原始主程序"""
        try:
            if not self.check_backup_exists():
                return False
            
            # 恢复原始主文件
            shutil.copy2(self.backup_file, self.current_file)
            logger.info(f"✅ 恢复原始主程序: {self.current_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 恢复原始主程序失败: {e}")
            return False
    
    def disable_new_main(self) -> bool:
        """禁用新主程序"""
        try:
            if os.path.exists(self.new_main_file):
                disabled_file = f"{self.new_main_file}.disabled"
                shutil.move(self.new_main_file, disabled_file)
                logger.info(f"🚫 禁用新主程序: {disabled_file}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 禁用新主程序失败: {e}")
            return False
    
    def disable_new_modules(self) -> bool:
        """禁用新模块"""
        try:
            for module_path in self.new_modules:
                full_path = os.path.join(self.project_root, module_path)
                if os.path.exists(full_path):
                    disabled_path = f"{full_path}.disabled"
                    shutil.move(full_path, disabled_path)
                    logger.info(f"🚫 禁用模块: {disabled_path}")
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 禁用新模块失败: {e}")
            return False
    
    def test_rollback_success(self) -> bool:
        """测试回退是否成功"""
        try:
            logger.info("🧪 测试回退后的程序...")
            
            # 尝试导入原始主程序
            sys.path.insert(0, self.project_root)
            
            try:
                import main_v20230219_optimized
                logger.info("✅ 原始主程序导入成功")
                
                # 简单的功能测试
                if hasattr(main_v20230219_optimized, 'main'):
                    logger.info("✅ 主函数存在")
                    return True
                else:
                    logger.error("❌ 主函数不存在")
                    return False
                    
            except ImportError as e:
                logger.error(f"❌ 原始主程序导入失败: {e}")
                return False
                
        except Exception as e:
            logger.error(f"❌ 回退测试失败: {e}")
            return False
    
    def perform_rollback(self) -> bool:
        """执行完整回退"""
        logger.info("🔄 开始执行回退操作...")
        logger.info("=" * 60)
        
        try:
            # 1. 检查备份文件
            if not self.check_backup_exists():
                logger.error("❌ 无法执行回退：备份文件不存在")
                return False
            
            # 2. 备份当前状态
            logger.info("\n📦 第一步：备份当前状态")
            if not self.backup_current_state():
                logger.error("❌ 备份当前状态失败，中止回退")
                return False
            
            # 3. 恢复原始主程序
            logger.info("\n🔄 第二步：恢复原始主程序")
            if not self.restore_original_main():
                logger.error("❌ 恢复原始主程序失败")
                return False
            
            # 4. 禁用新主程序
            logger.info("\n🚫 第三步：禁用新主程序")
            if not self.disable_new_main():
                logger.warning("⚠️ 禁用新主程序失败，但不影响回退")
            
            # 5. 禁用新模块
            logger.info("\n🚫 第四步：禁用新模块")
            if not self.disable_new_modules():
                logger.warning("⚠️ 禁用新模块失败，但不影响回退")
            
            # 6. 测试回退结果
            logger.info("\n🧪 第五步：测试回退结果")
            if not self.test_rollback_success():
                logger.error("❌ 回退测试失败")
                return False
            
            logger.info("\n" + "=" * 60)
            logger.info("🎉 回退操作成功完成！")
            logger.info("✅ 系统已恢复到原始稳定版本")
            logger.info("📝 可以使用 python main_v20230219_optimized.py 运行程序")
            logger.info("=" * 60)
            
            return True
            
        except Exception as e:
            logger.error(f"❌ 回退操作失败: {e}")
            import traceback
            traceback.print_exc()
            return False
    
    def show_rollback_status(self):
        """显示回退状态"""
        logger.info("📊 当前系统状态:")
        logger.info("-" * 40)
        
        # 检查文件状态
        files_to_check = [
            ("原始主程序", self.current_file),
            ("备份文件", self.backup_file),
            ("新主程序", self.new_main_file),
            ("新主程序(禁用)", f"{self.new_main_file}.disabled")
        ]
        
        for name, path in files_to_check:
            exists = os.path.exists(path)
            status = "✅ 存在" if exists else "❌ 不存在"
            logger.info(f"  {name}: {status}")
        
        # 检查新模块状态
        logger.info("\n📋 新模块状态:")
        for module_path in self.new_modules:
            full_path = os.path.join(self.project_root, module_path)
            disabled_path = f"{full_path}.disabled"
            
            if os.path.exists(full_path):
                status = "🟢 活跃"
            elif os.path.exists(disabled_path):
                status = "🚫 已禁用"
            else:
                status = "❌ 不存在"
            
            logger.info(f"  {module_path}: {status}")
        
        logger.info("-" * 40)


def main():
    """主函数"""
    print("🔄 MythQuant 回退管理系统")
    print("=" * 60)
    
    rollback_manager = RollbackManager()
    
    # 显示当前状态
    rollback_manager.show_rollback_status()
    
    # 询问用户是否确认回退
    print("\n⚠️ 警告：此操作将回退到原始主程序版本")
    print("📝 新的轻量级主程序和相关模块将被禁用")
    print("💾 当前状态将被备份")
    
    confirm = input("\n❓ 确认执行回退操作吗？(y/N): ").strip().lower()
    
    if confirm in ['y', 'yes']:
        # 执行回退
        success = rollback_manager.perform_rollback()
        return 0 if success else 1
    else:
        print("❌ 用户取消回退操作")
        return 0


if __name__ == '__main__':
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断操作")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 回退过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
