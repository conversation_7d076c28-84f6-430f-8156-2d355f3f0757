2025-07-28 23:48:10,097 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_234810.log
2025-07-28 23:48:10,097 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-28 23:48:10,098 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-28 23:48:10,098 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 23:48:10,098 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 23:48:10,098 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 23:48:10,098 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 23:48:10,100 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 23:48:10,100 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 23:48:10,109 - Main - INFO - info:307 - ℹ️ ⚖️ 开始增量下载可行性分析
2025-07-28 23:48:10,589 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:48:10,589 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:48:10,589 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-28 23:48:10,780 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 2天, 频率: 1min)
2025-07-28 23:48:10,780 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:48:10,780 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:48:10,780 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:48:10,831 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 23:48:11,056 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 23:48:11,056 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 23:48:11,057 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 23:48:11,057 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:48:11,057 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250725
2025-07-28 23:48:11,061 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 23:48:11,063 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 23:48:11,063 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:48:11,065 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-28 23:48:11,066 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:48:11,066 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-28 23:48:11,067 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-28 23:48:11,067 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-28 23:48:11,067 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 23:48:11,067 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:48:11,068 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:48:11,073 - Main - INFO - info:307 - ℹ️ ✅ 增量下载可行性验证通过
2025-07-28 23:48:11,074 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-28 23:48:11,074 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-28 23:48:11,074 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-28 23:48:11,110 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数21291, 覆盖89个交易日
2025-07-28 23:48:11,111 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-07-28 23:48:11,111 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-07-28 23:48:11,111 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-07-28 23:48:11,112 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-07-28 23:48:11,112 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-07-28 23:48:11,112 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-07-28 23:48:11,112 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-07-28 23:48:11,112 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-07-28 23:48:11,112 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-07-28 23:48:11,112 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-07-28 23:48:11,113 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-28 23:48:11,114 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-28 23:48:11,114 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-28 23:48:11,114 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_202503180931-202507251500_来源互联网（202507282341）.txt
2025-07-28 23:48:11,114 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_202503180931-202507251500_来源互联网（202507282341）.txt
2025-07-28 23:48:11,114 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-28 23:48:11,135 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-07-28 23:48:11,365 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-07-28 23:48:11,368 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:48:11,368 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:48:11,369 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-28 23:48:11,540 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 2天, 频率: 1min)
2025-07-28 23:48:11,540 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:48:11,542 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:48:11,542 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:48:11,596 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 23:48:11,809 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 23:48:11,809 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 23:48:11,809 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 23:48:11,809 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:48:11,810 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250725
2025-07-28 23:48:11,813 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 23:48:11,814 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 23:48:11,814 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:48:11,817 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-28 23:48:11,818 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:48:11,818 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-28 23:48:11,818 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-28 23:48:11,818 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-28 23:48:11,818 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 23:48:11,818 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:48:11,819 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:48:11,823 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-28 23:48:11,823 - Main - WARNING - warning:311 - ⚠️ ⚠️ 无法解析文件名格式
2025-07-28 23:48:11,823 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-28 23:48:11,823 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:48:11,823 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:48:11,823 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-28 23:48:12,004 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32640条 (目标日期: 20250101, 交易日: 136天, 频率: 1min)
2025-07-28 23:48:12,004 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32640条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:48:12,004 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:48:12,004 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:48:12,057 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需31840条
2025-07-28 23:48:12,292 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 23:48:12,511 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 23:48:12,745 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 23:48:12,977 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 23:48:13,198 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-28 23:48:13,427 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-28 23:48:13,664 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-28 23:48:13,886 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-28 23:48:14,105 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-28 23:48:14,333 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-28 23:48:14,552 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-28 23:48:14,779 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-28 23:48:15,012 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-28 23:48:15,246 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-28 23:48:15,473 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-28 23:48:15,707 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-28 23:48:15,932 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-28 23:48:16,166 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-28 23:48:21,180 - Main - WARNING - warning:311 - ⚠️ ⚠️ 配置的最佳服务器连接失败: 60.12.136.250:7709
2025-07-28 23:48:21,182 - Main - INFO - info:307 - ℹ️ 未找到缓存的良好服务器
2025-07-28 23:48:21,182 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接备用服务器: 119.147.212.81:7709
2025-07-28 23:48:26,186 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接备用服务器: 114.80.63.12:7709
2025-07-28 23:48:31,192 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接备用服务器: 180.153.39.51:7709
2025-07-28 23:48:36,197 - Main - ERROR - error:315 - ❌ ❌ 所有通达信服务器连接失败
2025-07-28 23:48:36,197 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计15200条数据
2025-07-28 23:48:36,201 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32640条，实际获取15200条
2025-07-28 23:48:36,201 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约17440条数据（约72个交易日）
2025-07-28 23:48:36,202 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-04- ~ 2025-07-
2025-07-28 23:48:36,202 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-28 23:48:36,202 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-28 23:48:36,238 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 14960 条 1min 数据
2025-07-28 23:48:36,245 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共14960条记录
2025-07-28 23:48:36,245 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:48:36,299 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.560, 前复权=7.560
2025-07-28 23:48:36,302 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:48:36,302 - Main - INFO - info:307 - ℹ️   日期: 202504241341
2025-07-28 23:48:36,302 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.56
2025-07-28 23:48:36,302 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.56
2025-07-28 23:48:36,302 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 14960 >= 5
2025-07-28 23:48:36,303 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:48:36,303 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:48:36,548 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250424-20250725_来源互联网（202507282348）.txt (700237 字节)
2025-07-28 23:48:36,548 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-28 23:48:36,549 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 23:48:36,549 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-28 23:48:36,550 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-28 23:48:36,550 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-28 23:48:36,550 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-28 23:48:36,550 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-28 23:48:36,550 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-28 23:48:36,550 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-28 23:48:36,551 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250728_234836.txt
2025-07-28 23:48:36,551 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-28 23:48:36,552 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-28 23:48:36,552 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-28 23:48:36,553 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 23:48:36,553 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 23:48:36,553 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-28 23:48:36,553 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-28 23:48:36,553 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-28 23:48:36,553 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 23:48:36,553 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 23:48:36,553 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
