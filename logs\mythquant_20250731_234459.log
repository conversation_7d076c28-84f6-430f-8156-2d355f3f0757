2025-07-31 23:44:59,214 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250731_234459.log
2025-07-31 23:44:59,214 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-31 23:44:59,215 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-31 23:44:59,215 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-31 23:44:59,215 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-31 23:44:59,215 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-31 23:44:59,215 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-31 23:44:59,215 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-31 23:44:59,215 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-31 23:44:59,242 - Main - INFO - info:307 - ℹ️ 🎯 获取特定分钟数据: 000617 @ 202507251500
2025-07-31 23:44:59,242 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-31 23:44:59,424 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1200条 (目标日期: 20250725, 交易日: 5天, 频率: 1min)
2025-07-31 23:44:59,425 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1200条 -> 实际请求800条 (配置限制:800)
2025-07-31 23:44:59,425 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-31 23:44:59,425 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-31 23:44:59,477 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需400条
2025-07-31 23:44:59,704 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +400条，总计1200条
2025-07-31 23:44:59,704 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1200条数据
2025-07-31 23:44:59,704 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1200条，实际获取1200条
2025-07-31 23:44:59,709 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-31 23:44:59,712 - Main - INFO - info:307 - ℹ️ ℹ️ pytdx提供未复权原始价格，正在计算前复权价格以完善数据字段...
2025-07-31 23:44:59,712 - Main - INFO - info:307 - ℹ️ 🔄 pytdx前复权计算: 000617
2025-07-31 23:44:59,712 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-31 23:44:59,712 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-31 23:45:00,201 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-31 23:45:00,201 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-31 23:45:00,207 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-31 23:45:00,208 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17/T0002
2025-07-31 23:45:00,208 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-31 23:45:00,208 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-31 23:45:00,208 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 未找到任何市场的分钟数据文件
2025-07-31 23:45:00,208 - main_v20230219_optimized - WARNING - _get_optimal_tdx_path:255 - ⚠️  主路径下未找到分钟数据文件，但将继续使用配置路径
2025-07-31 23:45:00,208 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17/T0002，缓存方法: memory
2025-07-31 23:45:12,774 - cache.gbbq_cache - ERROR - _load_all_data:288 - 加载GBBQ数据失败: '类别'
2025-07-31 23:45:24,774 - main_v20230219_optimized - INFO - _ensure_pickle_cache:441 - 🔄 更新pickle缓存...
2025-07-31 23:45:24,774 - main_v20230219_optimized - ERROR - _ensure_pickle_cache:495 - ❌ pickle缓存更新失败: 'StockDataProcessor' object has no attribute 'tdx_path'
2025-07-31 23:45:25,362 - main_v20230219_optimized - WARNING - _extract_stock_code_from_data:2442 - 无法从数据中提取股票代码，使用target_stocks中的第一个
2025-07-31 23:45:25,384 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 240条
2025-07-31 23:45:25,384 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功，处理了20个除权事件
2025-07-31 23:45:25,404 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功
2025-07-31 23:45:25,404 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价格与原始价格相同，可能无除权事件
2025-07-31 23:45:25,405 - Main - INFO - info:307 - ℹ️ 📊 获取到 240 条数据，查找目标时间 202507251500
2025-07-31 23:45:25,409 - Main - INFO - info:307 - ℹ️ ✅ 找到目标数据: 收盘价=8.900, 前复权=nan
2025-07-31 23:45:25,410 - Main - INFO - info:307 - ℹ️ ✅ 增量下载前提条件验证通过
2025-07-31 23:45:25,410 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-31 23:45:25,410 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-31 23:45:25,445 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数21360, 覆盖89个交易日
2025-07-31 23:45:25,445 - Main - INFO - info:307 - ℹ️ ✅ 数据完整，无缺失
2025-07-31 23:45:25,815 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-31 23:45:25,815 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-31 23:45:25,816 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250318-20250725_来源互联网（202507312344）.txt
2025-07-31 23:45:25,816 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250318-20250725_来源互联网（202507312344）.txt
2025-07-31 23:45:25,816 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-31 23:45:25,836 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-31 23:45:25,836 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-31 23:45:25,836 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-31 23:45:26,011 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1200条 (目标日期: 20250725, 交易日: 5天, 频率: 1min)
2025-07-31 23:45:26,011 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1200条 -> 实际请求800条 (配置限制:800)
2025-07-31 23:45:26,011 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-31 23:45:26,011 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-31 23:45:26,077 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需400条
2025-07-31 23:45:26,303 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +400条，总计1200条
2025-07-31 23:45:26,303 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1200条数据
2025-07-31 23:45:26,303 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1200条，实际获取1200条
2025-07-31 23:45:26,308 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-31 23:45:26,315 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-31 23:45:26,317 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-31 23:45:26,317 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250318 - 20250725
2025-07-31 23:45:26,317 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250317
2025-07-31 23:45:26,317 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250726 - 20250727
2025-07-31 23:45:26,329 - Main - INFO - info:307 - ℹ️ 读取现有数据: 21360 条记录
2025-07-31 23:45:26,329 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250317
2025-07-31 23:45:26,329 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-31 23:45:26,329 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-31 23:45:26,329 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250317
2025-07-31 23:45:26,511 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 33360条 (目标日期: 20250101, 交易日: 139天, 频率: 1min)
2025-07-31 23:45:26,511 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计33360条 -> 实际请求800条 (配置限制:800)
2025-07-31 23:45:26,511 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-31 23:45:26,511 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-31 23:45:26,564 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32560条
2025-07-31 23:45:26,779 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-31 23:45:27,010 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-31 23:45:27,230 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-31 23:45:27,454 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-31 23:45:27,693 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-31 23:45:27,916 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-31 23:45:28,148 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-31 23:45:28,374 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-31 23:45:28,598 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-31 23:45:28,823 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-31 23:45:29,054 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-31 23:45:29,283 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-31 23:45:29,498 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-31 23:45:29,725 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-31 23:45:29,960 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-31 23:45:30,188 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-31 23:45:30,412 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-31 23:45:30,636 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-31 23:45:30,853 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-31 23:45:31,087 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-31 23:45:31,310 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-31 23:45:31,532 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-31 23:45:31,761 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-31 23:45:31,985 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-31 23:45:32,206 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-31 23:45:32,438 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-31 23:45:32,655 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +720条，总计22320条
2025-07-31 23:45:32,655 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计22320条数据
2025-07-31 23:45:32,660 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要33360条，实际获取22320条
2025-07-31 23:45:32,660 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11040条数据（约46个交易日）
2025-07-31 23:45:32,660 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-31 23:45:32,660 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-31 23:45:32,660 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-31 23:45:32,711 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250101 - 20250317
2025-07-31 23:45:32,716 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-31 23:45:32,716 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-31 23:45:32,716 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250726 - 20250727
2025-07-31 23:45:32,716 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-31 23:45:32,716 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-31 23:45:32,716 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250726 - 20250727
2025-07-31 23:45:32,884 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250726, 交易日: 4天, 频率: 1min)
2025-07-31 23:45:32,884 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-31 23:45:32,884 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-31 23:45:32,884 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-31 23:45:32,933 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-31 23:45:33,156 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-31 23:45:33,156 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-31 23:45:33,157 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-31 23:45:33,160 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250726 - 20250727
2025-07-31 23:45:33,160 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-31 23:45:33,160 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-31 23:45:33,160 - Main - INFO - info:307 - ℹ️ ✅ 现有数据已覆盖目标范围，无需下载新数据
2025-07-31 23:45:33,161 - Main - INFO - info:307 - ℹ️ ✅ 添加时间戳标识: 1min_0_000617_20250318-20250725_来源互联网（202507312345）.txt
2025-07-31 23:45:33,162 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-31 23:45:33,162 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-31 23:45:33,162 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-31 23:45:33,162 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-31 23:45:33,162 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-31 23:45:33,162 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-31 23:45:33,162 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-31 23:45:33,162 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-31 23:45:33,163 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-31 23:45:33,163 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-31 23:45:33,163 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250731_234533.txt
2025-07-31 23:45:33,163 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-31 23:45:33,163 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-31 23:45:33,163 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-31 23:45:33,163 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-31 23:45:33,163 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-31 23:45:33,163 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-31 23:45:33,163 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-31 23:45:33,164 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-31 23:45:33,164 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-31 23:45:33,164 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-31 23:45:33,164 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
