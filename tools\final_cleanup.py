#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终项目结构清理脚本

处理剩余的config_前缀、test_开头文件和md文档的合理规划
"""

import os
import shutil
from pathlib import Path
from datetime import datetime


def manual_cleanup():
    """执行最终清理"""
    print("🧹 MythQuant 最终项目结构清理")
    print("=" * 50)
    
    project_root = Path(__file__).parent.parent
    
    # 核心文件（绝对不能移动）
    keep_in_root = {
        'config_compatibility.py',
        'data_access_compatibility.py', 
        'algorithm_compatibility.py',
        'io_compatibility.py',
        'user_config.py',
        'main.py',
        'requirements.txt',
        'README.md'
    }
    
    print("💾 创建最终清理备份...")
    backup_dir = project_root / "final_cleanup_backup"
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = backup_dir / f"backup_{timestamp}"
    backup_path.mkdir(parents=True, exist_ok=True)
    
    # 创建必要的目标目录
    print("\n📁 创建目标目录...")
    target_dirs = {
        'tools/migration': '迁移工具',
        'docs/migration': '迁移文档',
        'docs/project': '项目文档',
        'docs/reports': '报告文档'
    }
    
    for dir_path, description in target_dirs.items():
        (project_root / dir_path).mkdir(parents=True, exist_ok=True)
        print(f"   ✅ 创建: {dir_path}/ ({description})")
    
    # 定义详细的移动规则
    move_rules = {
        # config_前缀文件（除了兼容性模块）
        'tools/migration': {
            'patterns': [
                'config_migration_*.py',
                'data_migration_*.py',
                'data_access_migration_*.py'
            ],
            'description': '迁移相关脚本'
        },
        
        # test_开头的文件
        'tests': {
            'patterns': [
                'test_*.py',
                'comprehensive_migration_test.py',
                'quick_*.py'
            ],
            'description': '测试文件'
        },
        
        # 迁移相关的md文档
        'docs/migration': {
            'patterns': [
                'MIGRATION_*.md',
                'config_migration_*.md'
            ],
            'description': '迁移文档'
        },
        
        # 项目相关的md文档
        'docs/project': {
            'patterns': [
                'PROJECT_*.md'
            ],
            'description': '项目文档'
        },
        
        # 备份文件
        'archive': {
            'patterns': [
                '*.backup*',
                'main_v*.py.backup*'
            ],
            'description': '备份文件'
        },
        
        # JSON报告文件
        'reports': {
            'patterns': [
                'test_environment_migration_report_*.json'
            ],
            'description': '报告文件'
        }
    }
    
    # 执行移动操作
    print("\n🚚 执行最终清理...")
    moved_count = 0
    
    for target_dir, rule_info in move_rules.items():
        print(f"\n   📂 处理 {target_dir}/ ({rule_info['description']}):")
        
        for pattern in rule_info['patterns']:
            for file_path in project_root.glob(pattern):
                if file_path.is_file() and file_path.parent == project_root:
                    # 检查是否是核心文件
                    if file_path.name in keep_in_root:
                        print(f"      ⏭️ 跳过核心文件: {file_path.name}")
                        continue
                    
                    try:
                        # 备份
                        shutil.copy2(file_path, backup_path / file_path.name)
                        
                        # 移动
                        target_path = project_root / target_dir / file_path.name
                        
                        # 避免覆盖已存在的文件
                        if target_path.exists():
                            target_path = project_root / target_dir / f"{file_path.stem}_final{file_path.suffix}"
                        
                        shutil.move(str(file_path), str(target_path))
                        print(f"      ✅ 移动: {file_path.name}")
                        moved_count += 1
                        
                    except Exception as e:
                        print(f"      ❌ 移动失败 {file_path.name}: {e}")
    
    # 清理临时文件和缓存
    print(f"\n🗑️ 清理临时文件...")
    cleaned_count = 0
    
    # 删除根目录的 __pycache__
    pycache_root = project_root / '__pycache__'
    if pycache_root.exists():
        try:
            shutil.rmtree(pycache_root)
            print(f"   🗑️ 删除: __pycache__/")
            cleaned_count += 1
        except Exception as e:
            print(f"   ❌ 删除失败 __pycache__: {e}")
    
    # 删除根目录的 .pyc 文件
    for pyc_file in project_root.glob('*.pyc'):
        try:
            pyc_file.unlink()
            print(f"   🗑️ 删除: {pyc_file.name}")
            cleaned_count += 1
        except Exception as e:
            print(f"   ❌ 删除失败 {pyc_file.name}: {e}")
    
    # 最终验证
    print(f"\n✅ 最终验证...")
    root_files = [f.name for f in project_root.iterdir() if f.is_file()]
    print(f"   📊 清理后根目录文件数: {len(root_files)}")
    
    # 检查核心文件
    missing_core = []
    for core_file in keep_in_root:
        if core_file not in root_files:
            missing_core.append(core_file)
    
    if missing_core:
        print(f"   ⚠️ 缺少核心文件: {missing_core}")
    else:
        print("   ✅ 所有核心文件都已保留")
    
    # 显示根目录最终状态
    print("\n📄 根目录最终文件列表:")
    core_files = []
    other_files = []
    
    for file_name in sorted(root_files):
        if file_name in keep_in_root:
            core_files.append(file_name)
        else:
            other_files.append(file_name)
    
    print("   🔥 核心文件:")
    for file_name in core_files:
        print(f"      ⭐ {file_name}")
    
    if other_files:
        print("   📄 其他文件:")
        for file_name in other_files:
            print(f"      📄 {file_name}")
    
    print(f"\n📊 清理统计:")
    print(f"   移动文件: {moved_count} 个")
    print(f"   清理临时文件: {cleaned_count} 个")
    print(f"   根目录文件数: {len(root_files)} 个")
    
    # 生成清理建议
    print(f"\n💡 清理建议:")
    if len(root_files) <= 8:
        print("   ✅ 根目录已达到理想状态（≤8个文件）")
    else:
        print(f"   ⚠️ 根目录还有 {len(root_files)} 个文件，建议进一步清理")
        if other_files:
            print("   📋 建议处理的文件:")
            for file_name in other_files[:5]:  # 只显示前5个
                print(f"      - {file_name}")
            if len(other_files) > 5:
                print(f"      ... 还有 {len(other_files) - 5} 个文件")
    
    print(f"\n✅ 最终清理完成！")
    print(f"💾 备份位置: {backup_path}")
    
    return True


if __name__ == "__main__":
    try:
        success = manual_cleanup()
        if success:
            print("\n🎉 最终清理成功完成！")
            print("\n📋 下一步建议:")
            print("1. 验证核心模块: python -c \"import config_compatibility; print('✅ 配置模块正常')\"")
            print("2. 运行测试: python tests/run_tests.py")
            print("3. 检查功能: python tests/final_validation_test.py")
            print("4. 查看最终结构: 根目录应该只有核心文件")
        else:
            print("\n❌ 最终清理失败！")
    except Exception as e:
        print(f"\n❌ 清理过程中发生错误: {e}")
        print("请检查错误信息并手动处理")
    
    input("\n按回车键退出...")
