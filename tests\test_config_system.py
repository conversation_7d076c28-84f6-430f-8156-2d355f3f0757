#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置系统单元测试

测试配置管理器、配置验证、配置兼容性等功能
"""

import unittest
import sys
import os
from pathlib import Path
import tempfile
import shutil

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from mythquant.tests.fixtures import test_fixtures, cleanup_test_environment
from mythquant.tests.utils import assert_config_valid


class TestConfigSystem(unittest.TestCase):
    """配置系统测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = test_fixtures.setup_temp_directory()
        self.sample_config = test_fixtures.create_sample_config()
    
    def tearDown(self):
        """测试后清理"""
        cleanup_test_environment()
    
    def test_config_compatibility_import(self):
        """测试配置兼容性模块导入"""
        try:
            import config_compatibility as config
            self.assertTrue(hasattr(config, 'get_tdx_path'))
            self.assertTrue(hasattr(config, 'is_debug_enabled'))
            self.assertTrue(hasattr(config, 'get_output_path'))
        except ImportError as e:
            self.fail(f"Failed to import config_compatibility: {e}")
    
    def test_new_config_manager_import(self):
        """测试新配置管理器导入"""
        try:
            from mythquant.config import config_manager
            self.assertTrue(hasattr(config_manager, 'get_tdx_path'))
            self.assertTrue(hasattr(config_manager, 'is_debug_enabled'))
            self.assertTrue(hasattr(config_manager, 'get_output_path'))
        except ImportError as e:
            self.fail(f"Failed to import new config manager: {e}")
    
    def test_config_consistency(self):
        """测试新旧配置系统一致性"""
        try:
            import config_compatibility as old_config
            from mythquant.config import config_manager as new_config
            
            # 比较关键配置项
            old_tdx_path = old_config.get_tdx_path()
            new_tdx_path = new_config.get_tdx_path()
            
            old_debug = old_config.is_debug_enabled()
            new_debug = new_config.is_debug_enabled()
            
            # 路径可能不同，但类型应该一致
            self.assertIsInstance(old_tdx_path, (str, type(None)))
            self.assertIsInstance(new_tdx_path, (str, type(None)))
            
            # 调试模式应该一致
            self.assertEqual(type(old_debug), type(new_debug))
            
        except Exception as e:
            self.fail(f"Config consistency check failed: {e}")
    
    def test_config_validation(self):
        """测试配置验证"""
        # 测试有效配置
        valid_config = self.sample_config
        self.assertTrue(assert_config_valid(valid_config))
        
        # 测试无效配置
        invalid_configs = [
            {},  # 空配置
            {'tdx_path': 123},  # 错误类型
            {'debug': 'true'},  # 错误类型
        ]
        
        for invalid_config in invalid_configs:
            with self.assertRaises(AssertionError):
                assert_config_valid(invalid_config, required_keys=['tdx_path'])
    
    def test_config_file_operations(self):
        """测试配置文件操作"""
        try:
            from mythquant.config.manager import ConfigManager
            
            # 创建临时配置文件
            config_file = self.temp_dir / "test_config.json"
            
            # 测试配置保存和加载
            config_manager = ConfigManager()
            
            # 这里应该测试实际的配置文件操作
            # 由于ConfigManager可能还没有完全实现，我们做基本测试
            self.assertTrue(hasattr(config_manager, 'get_tdx_path'))
            
        except Exception as e:
            # 如果新配置管理器还没完全实现，跳过此测试
            self.skipTest(f"New config manager not fully implemented: {e}")
    
    def test_environment_variable_support(self):
        """测试环境变量支持"""
        try:
            # 设置测试环境变量
            os.environ['MYTHQUANT_DEBUG'] = 'true'
            os.environ['MYTHQUANT_TDX_PATH'] = str(self.temp_dir)
            
            # 重新导入配置以获取环境变量
            import importlib
            import config_compatibility
            importlib.reload(config_compatibility)
            
            # 清理环境变量
            del os.environ['MYTHQUANT_DEBUG']
            del os.environ['MYTHQUANT_TDX_PATH']
            
        except Exception as e:
            self.skipTest(f"Environment variable test skipped: {e}")
    
    def test_config_default_values(self):
        """测试配置默认值"""
        try:
            import config_compatibility as config
            
            # 测试默认值存在
            tdx_path = config.get_tdx_path()
            debug_mode = config.is_debug_enabled()
            output_path = config.get_output_path()
            
            # 默认值应该有合理的类型
            self.assertIsInstance(debug_mode, bool)
            self.assertIsInstance(output_path, str)
            
        except Exception as e:
            self.fail(f"Default values test failed: {e}")


class TestConfigPerformance(unittest.TestCase):
    """配置系统性能测试"""
    
    def test_config_access_performance(self):
        """测试配置访问性能"""
        import time
        
        try:
            import config_compatibility as config
            
            # 测试配置访问速度
            start_time = time.time()
            for _ in range(1000):
                config.get_tdx_path()
                config.is_debug_enabled()
                config.get_output_path()
            end_time = time.time()
            
            access_time = end_time - start_time
            
            # 1000次访问应该在1秒内完成
            self.assertLess(access_time, 1.0, f"Config access too slow: {access_time:.3f}s")
            
        except Exception as e:
            self.skipTest(f"Performance test skipped: {e}")


if __name__ == '__main__':
    # 设置测试环境
    unittest.main(verbosity=2)
