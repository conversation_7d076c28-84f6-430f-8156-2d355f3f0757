2025-07-22 15:31:40,365 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250722_153140.log
2025-07-22 15:31:40,365 - Main - INFO - info:307 - ℹ️ 正在初始化数据处理器...
2025-07-22 15:31:40,365 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-22 15:31:40,365 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-22 15:31:40,475 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-22 15:31:40,476 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-22 15:31:40,482 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-22 15:31:40,482 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-22 15:31:40,483 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-22 15:31:40,483 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-22 15:31:40,483 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-22 15:31:40,483 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-22 15:31:40,483 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-22 15:31:40,486 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-22 15:31:40,486 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-22 15:31:40,486 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-22 15:31:40,487 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-22 15:31:40,492 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-22 15:31:40,829 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.34秒
2025-07-22 15:31:40,829 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.34秒
2025-07-22 15:31:40,829 - ui.display - INFO - display_startup_info:32 - ========================================================================================
2025-07-22 15:31:40,829 - ui.display - INFO - display_startup_info:33 - 🚀 MythQuant 买卖差数据生成系统
2025-07-22 15:31:40,830 - ui.display - INFO - display_startup_info:34 - ========================================================================================
2025-07-22 15:31:40,830 - core.logging_service - INFO - verbose_log:67 - 详细模式已启用
2025-07-22 15:31:40,830 - core.logging_service - INFO - verbose_log:67 - 前复权处理详情: 启用
2025-07-22 15:31:40,830 - core.logging_service - WARNING - verbose_log:67 - 性能警告显示: 启用
2025-07-22 15:31:40,830 - core.logging_service - INFO - verbose_log:67 - 【详细模式配置检查】完成 - 所有选项已加载
2025-07-22 15:31:40,830 - ui.display - INFO - display_startup_info:47 - 📋 系统配置概览
2025-07-22 15:31:40,830 - ui.display - INFO - display_startup_info:48 - ----------------------------------------------------------------------------------------
2025-07-22 15:31:40,830 - ui.display - INFO - display_startup_info:51 - 📁 TDX主路径        : H:/MPV1.17
2025-07-22 15:31:40,831 - ui.display - INFO - display_startup_info:52 - 📁 分钟线子路径配置  : /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-22 15:31:40,834 - ui.display - INFO - display_startup_info:75 - 📊 可用数据文件      : 总计 11,064 个 (深证: 4,310 个, 上证: 6,504 个, 北交所: 250 个)
2025-07-22 15:31:40,834 - ui.display - INFO - display_startup_info:80 - 🎯 目标股票          : 1 只股票
2025-07-22 15:31:40,841 - ui.display - INFO - display_startup_info:91 - 🗃️ GBBQ缓存系统      : ✅ 启用
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:112 - 
📋 任务配置
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:113 - ----------------------------------------------------------------------------------------
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:149 - 📊 任务概览          : 总计 3 个任务，启用 1 个
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:152 - ┌──────────────────┬──────────┬────────────┬────────────────────┬────────────────────┐
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:153 - │      任务名称      │   状态   │   并发模式   │       开始时间       │       结束时间       │
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:154 - ├──────────────────┼──────────┼────────────┼────────────────────┼────────────────────┤
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:162 - │     分钟级别数据     │  ❌ 禁用  │  多线程(4)  │ 2025-01-01 09:30 │ 2025-07-13 15:00 │
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:162 - │     日线级别数据     │  ✅ 启用  │  多线程(4)  │ 2015-01-01 00:00 │ 2025-07-31 23:59 │
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:168 - │                │        │          │输出: .\daily                           │
2025-07-22 15:31:40,845 - ui.display - INFO - display_task_configuration:162 - │     周线级别数据     │  ❌ 禁用  │  多线程(4)  │ 2023-12-01 00:00 │ 2023-12-31 23:59 │
2025-07-22 15:31:40,846 - ui.display - INFO - display_task_configuration:172 - └──────────────────┴──────────┴────────────┴────────────────────┴────────────────────┘
2025-07-22 15:31:40,846 - ui.display - INFO - display_execution_summary:183 - 
📈 执行预估
2025-07-22 15:31:40,846 - ui.display - INFO - display_execution_summary:184 - ----------------------------------------------------------------------------------------
2025-07-22 15:31:40,846 - ui.display - INFO - display_execution_summary:185 - 🎯 目标股票数量      : 1 只
2025-07-22 15:31:40,846 - ui.display - INFO - display_execution_summary:186 - ⚙️ 启用任务数量      : 1 个
2025-07-22 15:31:40,846 - ui.display - INFO - display_execution_summary:187 - 📋 预计生成文件      : 1 个
2025-07-22 15:31:40,846 - ui.display - INFO - display_execution_summary:188 - 📁 文件命名格式      : [市场前缀]_[股票代码].dat
2025-07-22 15:31:40,847 - ui.display - INFO - display_execution_summary:189 - 🏷️ 市场前缀说明      : 0_深证, 1_上证, 2_北交所
2025-07-22 15:31:40,847 - ui.display - INFO - display_execution_summary:191 - 
========================================================================================
2025-07-22 15:31:40,847 - ui.display - INFO - display_execution_summary:192 - 🚀 开始执行数据生成任务
2025-07-22 15:31:40,847 - ui.display - INFO - display_execution_summary:193 - ========================================================================================
2025-07-22 15:31:40,847 - main_v20230219_optimized - INFO - main:4265 - 
📅 正在执行: 日线级别数据
2025-07-22 15:31:40,847 - main_v20230219_optimized - INFO - generate_daily_data_task:3108 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-22 15:31:40,848 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:3647 - 🧵 启动多线程日线级别txt文件生成
2025-07-22 15:31:40,848 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-22 15:31:40,848 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-22 15:31:41,006 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-22 15:31:41,006 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-22 15:31:41,013 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-22 15:31:41,013 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-22 15:31:41,013 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-22 15:31:41,013 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-22 15:31:41,013 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-22 15:31:41,013 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-22 15:31:41,013 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-22 15:31:41,017 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-22 15:31:41,018 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-22 15:31:41,018 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-22 15:31:41,018 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-22 15:31:41,025 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-22 15:31:41,527 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.50秒
2025-07-22 15:31:41,527 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.50秒
2025-07-22 15:31:52,246 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-22 15:32:13,431 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-22 15:32:13,431 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-22 15:32:13,431 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-22 15:32:13,440 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-22 15:32:14,039 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-22 15:32:14,039 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-22 15:32:14,039 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 587280条
2025-07-22 15:32:14,039 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-22 15:32:14,040 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-22 15:32:14,041 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-22 15:32:14,041 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-22 15:32:14,041 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-22 15:32:14,041 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-22 15:32:14,098 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-22 15:32:14,483 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-22 15:32:14,494 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-22 15:32:14,504 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-22 15:32:14,521 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-22 15:32:14,537 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-22 15:32:14,555 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-22 15:32:14,573 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-22 15:32:14,583 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-22 15:32:14,593 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-22 15:32:14,610 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-22 15:32:14,620 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-22 15:32:14,633 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-22 15:32:14,651 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-22 15:32:14,665 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-22 15:32:14,682 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-22 15:32:14,700 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-22 15:32:14,718 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-22 15:32:14,727 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-22 15:32:14,738 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-22 15:32:14,746 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-22 15:32:14,757 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-22 15:32:14,757 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-22 15:32:27,342 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-22 15:32:27,343 - core.logging_service - INFO - verbose_log:67 -   样例调整: 9.590000 → 5.731782
2025-07-22 15:32:27,343 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.59768325
2025-07-22 15:32:27,344 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-22 15:32:27,344 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-22 15:32:27,347 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回587280条数据
2025-07-22 15:32:27,482 - main_v20230219_optimized - INFO - apply_forward_adjustment:1744 - 保留交易时间数据后: 587280条
2025-07-22 15:32:27,483 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-22 15:32:27,483 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 587280条, 调整比例: 0.59768325
2025-07-22 15:32:27,483 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-22 15:32:27,483 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 9.590000
2025-07-22 15:32:27,483 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 5.731782
2025-07-22 15:32:27,483 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-22 15:32:27,500 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 5.7497
2025-07-22 15:32:27,729 - main_v20230219_optimized - INFO - load_and_process_minute_data:2213 - sz000617读取分钟数据成功: 587280条（已前复权并重新计算L2指标）
2025-07-22 15:32:28,507 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 2447条记录
2025-07-22 15:32:28,774 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 518条记录
2025-07-22 15:32:28,774 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:2447条, 周线:518条
2025-07-22 15:32:28,887 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20150101-20250731.txt (132499字节, 2447条记录)
2025-07-22 15:32:28,887 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-22 15:32:28,888 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3826 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-22 15:32:28,888 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3827 - 📊 成功生成: 1 个文件
2025-07-22 15:32:28,888 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3828 - ❌ 处理失败: 0 个股票
2025-07-22 15:32:28,888 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3829 - 📈 成功率: 100.0%
2025-07-22 15:32:28,888 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3830 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - generate_daily_data_task:3118 - ✅ 日线数据生成完成，耗时: 48.05 秒
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4321 - 
========================================================================================
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4322 - 📊 执行结果汇总
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4323 - ========================================================================================
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4326 - ┌───────────────┬──────────┬────────────┬───────────────┬───────────────┐
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4327 - │ 任务名称        │ 执行状态 │ 总耗时(秒)   │ 平均每股(秒)    │ 处理效率        │
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4328 - ├───────────────┼──────────┼────────────┼───────────────┼───────────────┤
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4337 - │ 日线级别数据        │ ✅ 成功     │      48.05 │        48.055 │ 0.0 股/秒       │
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4343 - └───────────────┴──────────┴────────────┴───────────────┴───────────────┘
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4346 - 
📈 总体执行统计:
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4347 - ⏱️  程序总耗时    : 48.54 秒 (0.8 分钟)
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4348 - ⚙️  任务总耗时    : 48.05 秒
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4349 - 🕐 系统开销时间  : 0.48 秒
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4350 - ✅ 成功任务数    : 1/1
2025-07-22 15:32:28,902 - main_v20230219_optimized - INFO - main:4351 - 📊 任务成功率    : 100.0%
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4354 - 🎯 总体处理效率  : 0.0 文件/秒
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4355 - 📋 平均每股耗时  : 48.055 秒/文件
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4363 - 
========================================================================================
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4364 - 🎉 程序执行完成
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4365 - ========================================================================================
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4366 - 🕐 开始时间: 2025-07-22 15:31:40
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4367 - 🕐 结束时间: 2025-07-22 15:32:28
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4368 - ⏱️  总耗时  : 48.54 秒
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4381 - ✅ 无错误发生
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4390 - 🎊 所有任务执行成功！
2025-07-22 15:32:28,903 - main_v20230219_optimized - INFO - main:4396 - ========================================================================================
2025-07-22 15:32:28,916 - __main__ - INFO - run_performance_validation:291 -   ✅ 测试 1 完成: 48.55秒
2025-07-22 15:32:31,918 - __main__ - INFO - run_performance_validation:276 - 🔄 执行性能测试 2/3
