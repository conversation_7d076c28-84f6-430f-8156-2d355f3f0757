# 缺失数据修复算法设计

## 算法概述

当存在多处缺失数据时，需要按照特定的逻辑进行云端数据下载和本地文档修复。

## 核心问题

### 1. 多处缺失的挑战
- **时间不连续性**: 缺失段可能分布在不同的交易日和时间段
- **下载效率**: 如何最优化地从pytdx获取多个时间段的数据
- **插入顺序**: 多个缺失段的插入顺序会影响文件结构
- **时间序列完整性**: 插入后必须保持严格的时间顺序

### 2. 当前算法缺陷
- 云端下载逻辑不完整（仅模拟）
- 精准插入算法缺失（仅模拟）
- 时间连续性判断过于简化
- 没有处理多处缺失的插入策略

## 完整算法设计

### 阶段一：缺失数据分析与优化

#### 1.1 缺失时间段分类
```python
missing_periods = [
    {
        'start_time': '202503201030',  # 2025-03-20 10:30
        'end_time': '202503201130',    # 2025-03-20 11:30
        'missing_count': 60,
        'period_type': 'continuous',   # 连续缺失
        'trading_day': '20250320',
        'priority': 1                  # 下载优先级
    },
    {
        'start_time': '202507041400',  # 2025-07-04 14:00
        'end_time': '202507041412',    # 2025-07-04 14:12
        'missing_count': 13,
        'period_type': 'partial',      # 部分缺失
        'trading_day': '20250704',
        'priority': 2
    }
]
```

#### 1.2 下载策略优化
- **按交易日分组**: 相同交易日的缺失段合并下载
- **时间范围扩展**: 适当扩展下载范围，减少API调用次数
- **优先级排序**: 按时间顺序和重要性确定下载优先级

### 阶段二：云端数据下载

#### 2.1 pytdx接口调用策略
```python
def _download_missing_data_optimized(self, missing_periods: List[Dict], stock_code: str):
    """优化的多段数据下载"""
    
    # 第1步：按交易日分组
    grouped_by_date = self._group_periods_by_trading_day(missing_periods)
    
    # 第2步：为每个交易日确定下载范围
    download_ranges = []
    for trading_day, periods in grouped_by_date.items():
        # 找到该日最早和最晚的缺失时间
        earliest = min(period['start_time'] for period in periods)
        latest = max(period['end_time'] for period in periods)
        
        # 适当扩展范围（前后各加30分钟缓冲）
        extended_start = self._extend_time_backward(earliest, 30)
        extended_end = self._extend_time_forward(latest, 30)
        
        download_ranges.append({
            'trading_day': trading_day,
            'start_time': extended_start,
            'end_time': extended_end,
            'periods': periods
        })
    
    # 第3步：按优先级下载
    downloaded_data = {}
    for range_info in sorted(download_ranges, key=lambda x: x['trading_day']):
        data = self._call_pytdx_api(
            stock_code=stock_code,
            start_time=range_info['start_time'],
            end_time=range_info['end_time']
        )
        downloaded_data[range_info['trading_day']] = data
    
    return downloaded_data
```

#### 2.2 数据质量验证
- **完整性检查**: 验证下载的数据是否覆盖所有缺失时间点
- **数据格式验证**: 确保字段格式与原文件一致
- **时间序列验证**: 检查下载数据的时间连续性

### 阶段三：精准插入算法

#### 3.1 插入位置定位
```python
def _locate_insertion_points(self, file_lines: List[str], missing_periods: List[Dict]):
    """定位每个缺失段的精确插入位置"""
    
    insertion_points = []
    
    for period in sorted(missing_periods, key=lambda x: x['start_time']):
        start_time = int(period['start_time'])
        
        # 在现有数据中找到插入位置
        insert_after_line = -1
        insert_before_line = len(file_lines)
        
        for i, line in enumerate(file_lines[1:], 1):  # 跳过表头
            if '|' in line:
                parts = line.split('|')
                if len(parts) >= 2:
                    line_time = int(parts[1])
                    
                    if line_time < start_time:
                        insert_after_line = i
                    elif line_time > start_time:
                        insert_before_line = i
                        break
        
        insertion_points.append({
            'period': period,
            'insert_after_line': insert_after_line,
            'insert_before_line': insert_before_line
        })
    
    return insertion_points
```

#### 3.2 批量插入策略
```python
def _perform_batch_insertion(self, file_path: str, insertion_points: List[Dict], 
                           downloaded_data: Dict[str, pd.DataFrame]):
    """执行批量精准插入"""
    
    # 第1步：读取原始文件
    with open(file_path, 'r', encoding='utf-8') as f:
        original_lines = f.readlines()
    
    # 第2步：构建新的文件内容
    new_lines = [original_lines[0]]  # 保留表头
    current_line_index = 1
    
    # 按插入位置排序（从后往前插入，避免位置偏移）
    sorted_points = sorted(insertion_points, key=lambda x: x['insert_after_line'], reverse=True)
    
    for point in sorted_points:
        period = point['period']
        insert_after = point['insert_after_line']
        
        # 添加原始数据（到插入点）
        while current_line_index <= insert_after:
            if current_line_index < len(original_lines):
                new_lines.append(original_lines[current_line_index])
            current_line_index += 1
        
        # 插入缺失数据
        trading_day = period['trading_day']
        if trading_day in downloaded_data:
            missing_data_lines = self._format_data_for_insertion(
                downloaded_data[trading_day], period
            )
            new_lines.extend(missing_data_lines)
    
    # 第3步：添加剩余的原始数据
    while current_line_index < len(original_lines):
        new_lines.append(original_lines[current_line_index])
        current_line_index += 1
    
    # 第4步：按时间排序所有数据行
    header = new_lines[0]
    data_lines = new_lines[1:]
    
    # 解析时间并排序
    sorted_data_lines = sorted(data_lines, key=lambda line: self._extract_datetime_from_line(line))
    
    # 第5步：写回文件
    with open(file_path, 'w', encoding='utf-8') as f:
        f.write(header)
        f.writelines(sorted_data_lines)
```

### 阶段四：完整性验证

#### 4.1 修复结果验证
- **时间序列检查**: 确保所有数据按时间严格排序
- **缺失段填补验证**: 验证所有缺失时间点都已填补
- **数据格式一致性**: 检查新插入数据的格式是否正确
- **文件完整性**: 验证文件结构和总记录数

#### 4.2 回滚机制
- **备份文件**: 修复前自动创建备份
- **验证失败回滚**: 如果验证失败，自动恢复原文件
- **错误报告**: 详细记录修复过程中的问题

## 关键技术点

### 1. 时间连续性判断
```python
def _is_consecutive_trading_minute(self, time1: int, time2: int) -> bool:
    """正确的交易时间连续性判断"""
    
    # 解析时间
    date1, hour1, min1 = self._parse_datetime_int(time1)
    date2, hour2, min2 = self._parse_datetime_int(time2)
    
    # 不同日期不连续
    if date1 != date2:
        return False
    
    # 计算分钟差
    minutes1 = hour1 * 60 + min1
    minutes2 = hour2 * 60 + min2
    
    # 检查是否在交易时间内且连续
    if abs(minutes2 - minutes1) == 1:
        # 检查是否跨越非交易时间段
        return not self._crosses_non_trading_period(time1, time2)
    
    return False
```

### 2. 下载范围优化
- **最小API调用**: 合并相近的缺失段，减少API调用次数
- **缓冲区策略**: 适当扩展下载范围，避免边界数据缺失
- **错误重试**: 下载失败时的重试机制

### 3. 插入性能优化
- **批量操作**: 一次性处理多个插入点，避免多次文件读写
- **内存管理**: 大文件处理时的内存优化策略
- **原子操作**: 确保插入过程的原子性，避免部分失败

## 算法复杂度

- **时间复杂度**: O(n log n) - 主要来自排序操作
- **空间复杂度**: O(n) - 需要存储原始数据和下载数据
- **API调用复杂度**: O(d) - d为不同交易日的数量

其中 n 为总数据行数，d 为涉及的交易日数量。
