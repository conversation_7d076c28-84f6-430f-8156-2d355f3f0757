#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试任务时间范围问题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_task_creation():
    """调试任务创建过程"""
    print("🔍 调试任务创建过程")
    print("=" * 50)
    
    try:
        # 步骤1：测试配置管理器
        print("📋 步骤1：测试配置管理器")
        from src.mythquant.config import config_manager
        task_configs = config_manager.get_task_configs()
        
        print(f"   任务配置数量: {len(task_configs)}")
        for i, config in enumerate(task_configs, 1):
            print(f"   任务 {i}:")
            print(f"      名称: {config.get('name')}")
            print(f"      开始时间: {config.get('start_time')}")
            print(f"      结束时间: {config.get('end_time')}")
        
        # 步骤2：测试任务管理器
        print("\n📋 步骤2：测试任务管理器")
        from src.mythquant.core.stock_processor import StockDataProcessor
        from src.mythquant.core.task_manager import TaskManager, Task
        
        processor = StockDataProcessor()
        task_manager = TaskManager(processor)
        
        print(f"   任务对象数量: {len(task_manager.tasks)}")
        for i, task in enumerate(task_manager.tasks, 1):
            print(f"   任务对象 {i}:")
            print(f"      名称: {task.name}")
            print(f"      开始时间: {task.start_time}")
            print(f"      结束时间: {task.end_time}")
            print(f"      数据类型: {task.data_type}")
        
        # 步骤3：测试任务执行前的时间处理
        print("\n📋 步骤3：测试任务执行前的时间处理")
        if task_manager.tasks:
            task = task_manager.tasks[0]
            print(f"   原始任务时间: {task.start_time} ~ {task.end_time}")
            
            # 模拟任务管理器中的时间处理逻辑
            start_date = task.start_time
            end_date = task.end_time
            
            print(f"   处理前: {start_date} ~ {end_date}")
            
            # 转换时间格式：2025-01-01 -> 20250101
            if '-' in start_date:
                start_date = start_date.replace('-', '')
            if '-' in end_date:
                end_date = end_date.replace('-', '')
            
            print(f"   处理后: {start_date} ~ {end_date}")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_structured_downloader():
    """调试结构化下载器的时间处理"""
    print("\n🔍 调试结构化下载器的时间处理")
    print("=" * 50)
    
    try:
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        downloader = StructuredInternetMinuteDownloader()
        
        # 模拟调用
        stock_code = '000617'
        start_date = '20250101'  # 正确的格式
        end_date = '20250807'    # 正确的格式
        frequency = '1min'
        
        print(f"   传入参数:")
        print(f"      股票代码: {stock_code}")
        print(f"      开始日期: {start_date}")
        print(f"      结束日期: {end_date}")
        print(f"      频率: {frequency}")
        
        # 这里不实际执行下载，只是检查参数传递
        print("   ✅ 参数传递正确")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 任务时间范围调试")
    print("=" * 60)
    
    # 调试任务创建
    task_creation_ok = debug_task_creation()
    
    # 调试结构化下载器
    downloader_ok = debug_structured_downloader()
    
    print("\n" + "=" * 60)
    print("🔍 调试结果总结")
    print("=" * 60)
    print(f"任务创建: {'✅' if task_creation_ok else '❌'}")
    print(f"结构化下载器: {'✅' if downloader_ok else '❌'}")
    
    if task_creation_ok and downloader_ok:
        print("\n💡 分析结论：")
        print("   配置和任务创建都正确")
        print("   问题可能出现在任务执行过程中的某个环节")
        print("   建议检查任务执行器中的时间参数传递")

if __name__ == '__main__':
    main()
