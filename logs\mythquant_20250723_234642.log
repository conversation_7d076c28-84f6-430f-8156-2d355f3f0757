2025-07-23 23:46:42,334 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250723_234642.log
2025-07-23 23:46:42,334 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-23 23:46:42,334 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-23 23:46:42,335 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-23 23:46:42,335 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-23 23:46:42,944 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-23 23:46:42,944 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-23 23:46:42,952 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-23 23:46:42,952 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-23 23:46:42,953 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-23 23:46:42,953 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-23 23:46:42,953 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-23 23:46:42,954 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-23 23:46:42,956 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-23 23:46:43,042 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-23 23:46:43,043 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-23 23:46:43,043 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-23 23:46:43,043 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-23 23:46:43,050 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-23 23:46:43,624 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.57秒
2025-07-23 23:46:43,625 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.57秒
2025-07-23 23:46:43,625 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-23 23:46:43,625 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-23 23:46:43,642 - __main__ - ERROR - test_new_main_entry:93 - ❌ 新主程序测试失败: CRITICAL
2025-07-23 23:46:43,657 - __main__ - INFO - run_comprehensive_test:278 - ⏳ 等待5秒...
2025-07-23 23:46:48,658 - __main__ - INFO - run_comprehensive_test:282 - 
📋 第二阶段：测试原始主程序
2025-07-23 23:46:48,658 - __main__ - INFO - test_original_main_entry:105 - 🔄 测试原始主程序入口
