# 现代Python项目配置文件模板

## 📋 **pyproject.toml 配置模板**

```toml
[build-system]
requires = ["setuptools>=61.0", "wheel"]
build-backend = "setuptools.build_meta"

[project]
name = "mythquant"
version = "2.0.0"
description = "专业的A股前复权数据处理系统"
readme = "README.md"
license = {text = "MIT"}
authors = [
    {name = "MythQuant Team", email = "<EMAIL>"}
]
maintainers = [
    {name = "MythQuant Team", email = "<EMAIL>"}
]
keywords = ["stock", "finance", "data-processing", "forward-adjustment", "a-share"]
classifiers = [
    "Development Status :: 4 - Beta",
    "Intended Audience :: Financial and Insurance Industry",
    "Intended Audience :: Developers",
    "License :: OSI Approved :: MIT License",
    "Operating System :: OS Independent",
    "Programming Language :: Python :: 3",
    "Programming Language :: Python :: 3.8",
    "Programming Language :: Python :: 3.9",
    "Programming Language :: Python :: 3.10",
    "Programming Language :: Python :: 3.11",
    "Programming Language :: Python :: 3.12",
    "Topic :: Office/Business :: Financial",
    "Topic :: Scientific/Engineering :: Information Analysis",
]
requires-python = ">=3.8"
dependencies = [
    "pandas>=1.3.0",
    "numpy>=1.21.0",
    "pytdx>=1.72",
    "openpyxl>=3.0.0",
    "requests>=2.25.0",
    "tqdm>=4.60.0",
    "pyyaml>=5.4.0",
    "python-dateutil>=2.8.0",
]

[project.optional-dependencies]
dev = [
    "pytest>=6.0",
    "pytest-cov>=2.12",
    "black>=21.0",
    "flake8>=3.9",
    "mypy>=0.910",
    "pre-commit>=2.15",
]
test = [
    "pytest>=6.0",
    "pytest-cov>=2.12",
    "pytest-mock>=3.6",
]
docs = [
    "sphinx>=4.0",
    "sphinx-rtd-theme>=0.5",
    "myst-parser>=0.15",
]

[project.urls]
Homepage = "https://github.com/mythquant/mythquant"
Documentation = "https://mythquant.readthedocs.io"
Repository = "https://github.com/mythquant/mythquant.git"
"Bug Tracker" = "https://github.com/mythquant/mythquant/issues"

[project.scripts]
mythquant = "mythquant.main:main"
mythquant-config = "mythquant.config.manager:main"

[tool.setuptools]
package-dir = {"" = "src"}

[tool.setuptools.packages.find]
where = ["src"]

[tool.setuptools.package-data]
mythquant = ["config/*.yaml", "data/*.json"]

# 代码质量工具配置
[tool.black]
line-length = 88
target-version = ['py38']
include = '\.pyi?$'
extend-exclude = '''
/(
  # directories
  \.eggs
  | \.git
  | \.hg
  | \.mypy_cache
  | \.tox
  | \.venv
  | build
  | dist
)/
'''

[tool.isort]
profile = "black"
multi_line_output = 3
line_length = 88
known_first_party = ["mythquant"]

[tool.mypy]
python_version = "3.8"
warn_return_any = true
warn_unused_configs = true
disallow_untyped_defs = true
disallow_incomplete_defs = true
check_untyped_defs = true
disallow_untyped_decorators = true
no_implicit_optional = true
warn_redundant_casts = true
warn_unused_ignores = true
warn_no_return = true
warn_unreachable = true
strict_equality = true

[[tool.mypy.overrides]]
module = [
    "pytdx.*",
    "tqdm.*",
]
ignore_missing_imports = true

[tool.pytest.ini_options]
minversion = "6.0"
addopts = "-ra -q --strict-markers --strict-config"
testpaths = [
    "tests",
    "test_environments",
]
markers = [
    "slow: marks tests as slow (deselect with '-m \"not slow\"')",
    "integration: marks tests as integration tests",
    "unit: marks tests as unit tests",
    "performance: marks tests as performance tests",
]

[tool.coverage.run]
source = ["src/mythquant"]
omit = [
    "*/tests/*",
    "*/test_*",
    "*/__pycache__/*",
]

[tool.coverage.report]
exclude_lines = [
    "pragma: no cover",
    "def __repr__",
    "if self.debug:",
    "if settings.DEBUG",
    "raise AssertionError",
    "raise NotImplementedError",
    "if 0:",
    "if __name__ == .__main__.:",
    "class .*\\bProtocol\\):",
    "@(abc\\.)?abstractmethod",
]
```

## 📋 **setup.py 配置模板**

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 安装配置文件
"""

from setuptools import setup, find_packages
import os

# 读取长描述
def read_long_description():
    with open("README.md", "r", encoding="utf-8") as fh:
        return fh.read()

# 读取版本信息
def read_version():
    version_file = os.path.join("src", "mythquant", "__init__.py")
    with open(version_file, "r", encoding="utf-8") as f:
        for line in f:
            if line.startswith("__version__"):
                return line.split("=")[1].strip().strip('"').strip("'")
    return "0.0.0"

setup(
    name="mythquant",
    version=read_version(),
    author="MythQuant Team",
    author_email="<EMAIL>",
    description="专业的A股前复权数据处理系统",
    long_description=read_long_description(),
    long_description_content_type="text/markdown",
    url="https://github.com/mythquant/mythquant",
    project_urls={
        "Bug Tracker": "https://github.com/mythquant/mythquant/issues",
        "Documentation": "https://mythquant.readthedocs.io",
        "Source Code": "https://github.com/mythquant/mythquant",
    },
    package_dir={"": "src"},
    packages=find_packages(where="src"),
    classifiers=[
        "Development Status :: 4 - Beta",
        "Intended Audience :: Financial and Insurance Industry",
        "Intended Audience :: Developers",
        "License :: OSI Approved :: MIT License",
        "Operating System :: OS Independent",
        "Programming Language :: Python :: 3",
        "Programming Language :: Python :: 3.8",
        "Programming Language :: Python :: 3.9",
        "Programming Language :: Python :: 3.10",
        "Programming Language :: Python :: 3.11",
        "Programming Language :: Python :: 3.12",
        "Topic :: Office/Business :: Financial",
        "Topic :: Scientific/Engineering :: Information Analysis",
    ],
    python_requires=">=3.8",
    install_requires=[
        "pandas>=1.3.0",
        "numpy>=1.21.0",
        "pytdx>=1.72",
        "openpyxl>=3.0.0",
        "requests>=2.25.0",
        "tqdm>=4.60.0",
        "pyyaml>=5.4.0",
        "python-dateutil>=2.8.0",
    ],
    extras_require={
        "dev": [
            "pytest>=6.0",
            "pytest-cov>=2.12",
            "black>=21.0",
            "flake8>=3.9",
            "mypy>=0.910",
            "pre-commit>=2.15",
        ],
        "test": [
            "pytest>=6.0",
            "pytest-cov>=2.12",
            "pytest-mock>=3.6",
        ],
        "docs": [
            "sphinx>=4.0",
            "sphinx-rtd-theme>=0.5",
            "myst-parser>=0.15",
        ],
    },
    entry_points={
        "console_scripts": [
            "mythquant=mythquant.main:main",
            "mythquant-config=mythquant.config.manager:main",
        ],
    },
    include_package_data=True,
    package_data={
        "mythquant": [
            "config/*.yaml",
            "data/*.json",
        ],
    },
    zip_safe=False,
)
```

## 📋 **MANIFEST.in 配置模板**

```
include README.md
include LICENSE
include requirements.txt
include pyproject.toml
recursive-include src/mythquant/config *.yaml *.yml *.json
recursive-include src/mythquant/data *.json
recursive-include docs *.md *.rst *.txt
recursive-exclude * __pycache__
recursive-exclude * *.py[co]
recursive-exclude * *.so
recursive-exclude * .DS_Store
```

## 📋 **src/mythquant/__init__.py 模板**

```python
#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant - 专业的A股前复权数据处理系统

一个高性能、高精度的A股前复权数据处理系统，支持日级和分钟级数据处理。
"""

__version__ = "2.0.0"
__author__ = "MythQuant Team"
__email__ = "<EMAIL>"
__license__ = "MIT"

# 核心模块导入
from .core.application import MythQuantApplication
from .config.manager import ConfigManager
from .config.user_settings import UserConfig

# 主要功能导入
from .data.processors.forward_adj import ForwardAdjustmentProcessor
from .io.writers.file_writer import FileWriter
from .algorithms.buy_sell_calculator import BuySellCalculator

# 工具函数导入
from .utils.helpers import (
    get_output_directory,
    format_time_range,
    clean_stock_code,
)

__all__ = [
    # 版本信息
    "__version__",
    "__author__",
    "__email__",
    "__license__",
    
    # 核心类
    "MythQuantApplication",
    "ConfigManager", 
    "UserConfig",
    
    # 主要功能
    "ForwardAdjustmentProcessor",
    "FileWriter",
    "BuySellCalculator",
    
    # 工具函数
    "get_output_directory",
    "format_time_range", 
    "clean_stock_code",
]

# 设置日志
import logging
logging.getLogger(__name__).addHandler(logging.NullHandler())
```

## 🔧 **开发工具配置**

### **.pre-commit-config.yaml**
```yaml
repos:
  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v4.4.0
    hooks:
      - id: trailing-whitespace
      - id: end-of-file-fixer
      - id: check-yaml
      - id: check-added-large-files
      - id: check-merge-conflict
      - id: debug-statements

  - repo: https://github.com/psf/black
    rev: 23.3.0
    hooks:
      - id: black
        language_version: python3

  - repo: https://github.com/pycqa/isort
    rev: 5.12.0
    hooks:
      - id: isort

  - repo: https://github.com/pycqa/flake8
    rev: 6.0.0
    hooks:
      - id: flake8
        additional_dependencies: [flake8-docstrings]

  - repo: https://github.com/pre-commit/mirrors-mypy
    rev: v1.3.0
    hooks:
      - id: mypy
        additional_dependencies: [types-requests, types-PyYAML]
```

### **tox.ini**
```ini
[tox]
envlist = py38,py39,py310,py311,py312,flake8,mypy
isolated_build = true

[testenv]
deps = 
    pytest>=6.0
    pytest-cov>=2.12
    pytest-mock>=3.6
commands = 
    pytest {posargs}

[testenv:flake8]
deps = flake8
commands = flake8 src tests

[testenv:mypy]
deps = mypy
commands = mypy src

[testenv:coverage]
deps = 
    pytest>=6.0
    pytest-cov>=2.12
commands = 
    pytest --cov=mythquant --cov-report=html --cov-report=term

[flake8]
max-line-length = 88
extend-ignore = E203, W503
exclude = .git,__pycache__,build,dist,.tox
```

这些配置文件将为MythQuant项目提供现代化的Python项目管理基础设施。
