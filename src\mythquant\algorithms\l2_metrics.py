#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
L2指标计算模块

基于股票OHLCV数据，模拟Level-2市场深度数据，计算主买主卖指标
"""

import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Dict, List, Tuple
import logging

# 导入新架构的配置系统和其他算法模块
from src.mythquant.config.manager import ConfigManager
from .buy_sell_calculator import BuySellCalculator

logger = logging.getLogger(__name__)


class L2MetricsCalculator:
    """L2指标计算器 - 基于OHLCV数据模拟Level-2指标"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化L2指标计算器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.buy_sell_calculator = BuySellCalculator(config_manager)
        self.precision = Decimal('0.001')  # 价格精度：3位小数
        
        # 调试模式
        self.debug_mode = config_manager.is_debug_enabled()
    
    def calculate_l2_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算模拟L2指标 - 优化版本
        
        基于OHLCV数据，通过分析价格运行轨迹，模拟计算主买主卖指标。
        核心思想：价格上涨过程中产生主买，价格下跌过程中产生主卖。
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            包含L2指标的DataFrame
        """
        if df is None or df.empty:
            logger.warning("输入数据为空，无法计算L2指标")
            return pd.DataFrame()
        
        try:
            # 复制数据以避免修改原始数据
            result_df = df.copy()
            
            # 数据验证
            if not self._validate_input_data(result_df):
                logger.error("输入数据验证失败")
                return pd.DataFrame()
            
            # 计算基础指标
            result_df = self._calculate_basic_metrics(result_df)
            
            # 识别K线形态
            result_df = self._identify_kline_patterns(result_df)
            
            # 计算主买主卖
            result_df = self._calculate_main_buy_sell(result_df)
            
            # 计算路径总长
            result_df = self._calculate_path_length(result_df)
            
            # 应用精度控制
            result_df = self._apply_precision_control(result_df)
            
            logger.info(f"L2指标计算完成，处理了{len(result_df)}条记录")
            return result_df
            
        except Exception as e:
            logger.error(f"L2指标计算失败: {e}")
            return pd.DataFrame()
    
    def _validate_input_data(self, df: pd.DataFrame) -> bool:
        """
        验证输入数据的有效性
        
        Args:
            df: 输入数据
            
        Returns:
            是否有效
        """
        try:
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"缺少必要列: {missing_columns}")
                return False
            
            # 检查数值有效性
            for col in required_columns:
                if df[col].isnull().any():
                    logger.error(f"列{col}包含空值")
                    return False
                
                if col != 'volume' and (df[col] <= 0).any():
                    logger.error(f"列{col}包含非正值")
                    return False
            
            # 检查OHLC逻辑关系
            invalid_ohlc = (df['high'] < df['low']) | (df['high'] < df['open']) | \
                          (df['high'] < df['close']) | (df['low'] > df['open']) | \
                          (df['low'] > df['close'])
            
            if invalid_ohlc.any():
                logger.error("OHLC数据存在逻辑错误")
                return False
            
            return True
            
        except Exception as e:
            logger.error(f"数据验证异常: {e}")
            return False
    
    def _calculate_basic_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算基础指标
        
        Args:
            df: 输入数据
            
        Returns:
            包含基础指标的DataFrame
        """
        try:
            # 计算价格变化
            df['price_change'] = df['close'] - df['open']
            df['price_change_pct'] = df['price_change'] / df['open'] * 100
            
            # 计算振幅
            df['amplitude'] = (df['high'] - df['low']) / df['open'] * 100
            
            # 计算上影线和下影线
            df['upper_shadow'] = df['high'] - np.maximum(df['open'], df['close'])
            df['lower_shadow'] = np.minimum(df['open'], df['close']) - df['low']
            
            # 计算实体大小
            df['body_size'] = np.abs(df['close'] - df['open'])
            
            # 计算前一日收盘价（用于计算跳空）
            df['prev_close'] = df['close'].shift(1)
            df['gap'] = df['open'] - df['prev_close']
            
            return df
            
        except Exception as e:
            logger.error(f"计算基础指标失败: {e}")
            return df
    
    def _identify_kline_patterns(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        识别K线形态
        
        Args:
            df: 包含基础指标的DataFrame
            
        Returns:
            包含形态识别结果的DataFrame
        """
        try:
            # 识别十字星（实体很小）
            body_threshold = df['open'] * 0.005  # 实体小于开盘价的0.5%
            df['is_doji'] = df['body_size'] <= body_threshold
            
            # 识别一字板
            df['is_limit_up'] = (df['open'] == df['high']) & (df['high'] == df['low']) & (df['low'] == df['close'])
            df['is_limit_down'] = df['is_limit_up']  # 一字涨停和跌停的判断相同
            
            # 识别长上影线
            df['has_long_upper_shadow'] = df['upper_shadow'] > df['body_size'] * 2
            
            # 识别长下影线
            df['has_long_lower_shadow'] = df['lower_shadow'] > df['body_size'] * 2
            
            # 识别大阳线和大阴线
            df['is_big_yang'] = (df['close'] > df['open']) & (df['body_size'] > df['open'] * 0.05)
            df['is_big_yin'] = (df['close'] < df['open']) & (df['body_size'] > df['open'] * 0.05)
            
            return df
            
        except Exception as e:
            logger.error(f"识别K线形态失败: {e}")
            return df
    
    def _calculate_main_buy_sell(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算主买主卖指标
        
        Args:
            df: 包含形态识别的DataFrame
            
        Returns:
            包含主买主卖指标的DataFrame
        """
        try:
            # 使用买卖计算器计算主买主卖
            df = self.buy_sell_calculator.calculate_buy_sell_metrics(df)
            
            # 如果买卖计算器没有返回主买主卖列，使用简化算法
            if 'main_buy' not in df.columns or 'main_sell' not in df.columns:
                df = self._calculate_simplified_buy_sell(df)
            
            return df
            
        except Exception as e:
            logger.error(f"计算主买主卖失败: {e}")
            return self._calculate_simplified_buy_sell(df)
    
    def _calculate_simplified_buy_sell(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        简化的主买主卖计算
        
        Args:
            df: 输入数据
            
        Returns:
            包含主买主卖的DataFrame
        """
        try:
            # 初始化主买主卖
            df['main_buy'] = 0.0
            df['main_sell'] = 0.0
            
            for idx, row in df.iterrows():
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']
                
                # 特殊形态处理
                if row.get('is_limit_up', False):
                    # 一字板：全部为主买
                    df.at[idx, 'main_buy'] = high_price - low_price
                    df.at[idx, 'main_sell'] = 0.0
                elif row.get('is_doji', False):
                    # 十字星：主买主卖相等
                    amplitude = high_price - low_price
                    df.at[idx, 'main_buy'] = amplitude / 2
                    df.at[idx, 'main_sell'] = amplitude / 2
                else:
                    # 常规形态：根据价格运行轨迹计算
                    if close_price > open_price:
                        # 阳线：主买为上涨部分
                        df.at[idx, 'main_buy'] = close_price - open_price + (high_price - close_price) * 0.7
                        df.at[idx, 'main_sell'] = (open_price - low_price) * 0.7
                    else:
                        # 阴线：主卖为下跌部分
                        df.at[idx, 'main_buy'] = (high_price - open_price) * 0.7
                        df.at[idx, 'main_sell'] = open_price - close_price + (close_price - low_price) * 0.7
            
            return df
            
        except Exception as e:
            logger.error(f"简化主买主卖计算失败: {e}")
            return df
    
    def _calculate_path_length(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算路径总长
        
        Args:
            df: 包含主买主卖的DataFrame
            
        Returns:
            包含路径总长的DataFrame
        """
        try:
            # 路径总长 = 主买 + 主卖
            df['path_length'] = df.get('main_buy', 0) + df.get('main_sell', 0)
            
            # 确保路径总长不超过振幅
            amplitude = df['high'] - df['low']
            df['path_length'] = np.minimum(df['path_length'], amplitude)
            
            return df
            
        except Exception as e:
            logger.error(f"计算路径总长失败: {e}")
            return df
    
    def _apply_precision_control(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        应用精度控制
        
        Args:
            df: 输入数据
            
        Returns:
            精度控制后的DataFrame
        """
        try:
            # 对价格相关列应用精度控制
            price_columns = ['main_buy', 'main_sell', 'path_length']
            
            for col in price_columns:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: self._apply_decimal_precision(x))
            
            return df
            
        except Exception as e:
            logger.error(f"应用精度控制失败: {e}")
            return df
    
    def _apply_decimal_precision(self, value: float) -> float:
        """
        应用Decimal精度控制
        
        Args:
            value: 输入值
            
        Returns:
            精度控制后的值
        """
        try:
            decimal_value = Decimal(str(value))
            return float(decimal_value.quantize(self.precision, rounding=ROUND_HALF_UP))
        except Exception:
            return float(value)
    
    def get_metrics_summary(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        获取指标汇总统计
        
        Args:
            df: 包含L2指标的DataFrame
            
        Returns:
            指标汇总字典
        """
        try:
            if df.empty:
                return {}
            
            summary = {
                'total_records': len(df),
                'avg_main_buy': df.get('main_buy', pd.Series([0])).mean(),
                'avg_main_sell': df.get('main_sell', pd.Series([0])).mean(),
                'avg_path_length': df.get('path_length', pd.Series([0])).mean(),
                'max_amplitude': df.get('amplitude', pd.Series([0])).max(),
                'min_amplitude': df.get('amplitude', pd.Series([0])).min(),
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取指标汇总失败: {e}")
            return {}


# 导出
__all__ = ['L2MetricsCalculator']
