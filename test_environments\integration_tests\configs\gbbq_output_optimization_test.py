#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
GBBQ输出优化测试

验证在不同场景下GBBQ除权除息数据的输出优化效果

位置: test_environments/integration_tests/configs/
作者: AI Assistant
创建时间: 2025-07-31
"""

import sys
import os
import io
from contextlib import redirect_stdout
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_completion
)


def test_minute_data_context_optimization():
    """测试分钟数据场景下的输出优化"""
    print_step("分钟数据场景输出优化测试", 1, 3)
    
    try:
        # 导入必要的模块
        sys.path.append(project_root)
        from main_v20230219_optimized import StockDataProcessor
        import user_config as ucfg
        
        # 创建处理器
        processor = StockDataProcessor(ucfg.tdx['tdx_path'])
        
        # 获取测试数据
        xdxr_data = processor.load_dividend_data('000617')
        if xdxr_data is None or len(xdxr_data) == 0:
            print_warning("无法获取000617的除权除息数据，跳过测试", level=2)
            return True
        
        print_info(f"获取到除权除息数据: {len(xdxr_data)}个事件", level=2)
        
        # 测试1：分钟数据场景（应该显示简要信息）
        print_info("测试分钟数据场景输出", level=2)
        
        # 捕获输出
        output_buffer = io.StringIO()
        with redirect_stdout(output_buffer):
            processor._print_gbbq_data_summary(xdxr_data, '000617', context="minute_update")
        
        minute_output = output_buffer.getvalue()
        
        # 验证输出特征
        is_brief = True
        brief_indicators = ["除权除息数据:", "事件类型:", "加载完成"]
        verbose_indicators = ["详细除权除息事件列表", "序号", "股票代码（市场）", "=" * 50]
        
        # 检查是否包含简要信息指标
        brief_count = sum(1 for indicator in brief_indicators if indicator in minute_output)
        verbose_count = sum(1 for indicator in verbose_indicators if indicator in minute_output)
        
        if brief_count >= 2 and verbose_count == 0:
            print_result("分钟数据场景输出简化成功", True, level=2)
            print_info(f"简要信息指标: {brief_count}/3, 详细信息指标: {verbose_count}/4", level=3)
        else:
            print_result("分钟数据场景输出未充分简化", False, level=2)
            print_info(f"简要信息指标: {brief_count}/3, 详细信息指标: {verbose_count}/4", level=3)
            is_brief = False
        
        return is_brief
        
    except Exception as e:
        print_error(f"分钟数据场景测试失败: {e}", level=2)
        return False


def test_analysis_context_detailed_output():
    """测试分析场景下的详细输出"""
    print_step("分析场景详细输出测试", 2, 3)
    
    try:
        # 导入必要的模块
        from main_v20230219_optimized import StockDataProcessor
        import user_config as ucfg
        
        # 创建处理器
        processor = StockDataProcessor(ucfg.tdx['tdx_path'])
        
        # 获取测试数据
        xdxr_data = processor.load_dividend_data('000617')
        if xdxr_data is None or len(xdxr_data) == 0:
            print_warning("无法获取000617的除权除息数据，跳过测试", level=2)
            return True
        
        # 测试2：分析场景（应该显示详细信息）
        print_info("测试分析场景输出", level=2)
        
        # 捕获输出
        output_buffer = io.StringIO()
        with redirect_stdout(output_buffer):
            processor._print_gbbq_data_summary(xdxr_data, '000617', context="analysis")
        
        analysis_output = output_buffer.getvalue()
        
        # 验证输出特征
        is_detailed = True
        detailed_indicators = ["GBBQ除权除息数据总览", "详细除权除息事件列表", "序号", "股票代码（市场）"]
        
        # 检查是否包含详细信息指标
        detailed_count = sum(1 for indicator in detailed_indicators if indicator in analysis_output)
        
        if detailed_count >= 3:
            print_result("分析场景详细输出正常", True, level=2)
            print_info(f"详细信息指标: {detailed_count}/4", level=3)
        else:
            print_result("分析场景详细输出不足", False, level=2)
            print_info(f"详细信息指标: {detailed_count}/4", level=3)
            is_detailed = False
        
        return is_detailed
        
    except Exception as e:
        print_error(f"分析场景测试失败: {e}", level=2)
        return False


def test_context_switching_effectiveness():
    """测试上下文切换的有效性"""
    print_step("上下文切换有效性测试", 3, 3)
    
    try:
        # 导入必要的模块
        from main_v20230219_optimized import StockDataProcessor
        import user_config as ucfg
        
        # 创建处理器
        processor = StockDataProcessor(ucfg.tdx['tdx_path'])
        
        # 测试上下文设置和检测
        print_info("测试处理上下文设置", level=2)
        
        # 设置分钟数据上下文
        processor._current_processing_context = "minute_data"
        
        # 验证上下文检测
        has_context = hasattr(processor, '_current_processing_context')
        context_value = getattr(processor, '_current_processing_context', None)
        
        if has_context and context_value == "minute_data":
            print_result("上下文设置成功", True, level=2)
            print_info(f"当前上下文: {context_value}", level=3)
        else:
            print_result("上下文设置失败", False, level=2)
            return False
        
        # 清理上下文
        if hasattr(processor, '_current_processing_context'):
            delattr(processor, '_current_processing_context')
        
        # 验证清理效果
        has_context_after = hasattr(processor, '_current_processing_context')
        
        if not has_context_after:
            print_result("上下文清理成功", True, level=2)
        else:
            print_result("上下文清理失败", False, level=2)
            return False
        
        return True
        
    except Exception as e:
        print_error(f"上下文切换测试失败: {e}", level=2)
        return False


def main():
    """主函数"""
    print_banner("GBBQ输出优化测试", "验证不同场景下除权除息数据的输出优化效果")
    
    print_info(f"项目根目录: {project_root}")
    print_info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print_main_process("执行GBBQ输出优化测试")
    
    # 执行测试
    tests = [
        ("分钟数据场景优化", test_minute_data_context_optimization),
        ("分析场景详细输出", test_analysis_context_detailed_output),
        ("上下文切换有效性", test_context_switching_effectiveness)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print_error(f"测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    total_tests = len(tests)
    pass_rate = passed_tests / total_tests
    
    print_main_process("GBBQ输出优化测试结果")
    
    # 显示详细结果
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print_result(f"{test_name}: {status}", result, level=1)
    
    # 显示统计信息
    stats = {
        "总测试数": total_tests,
        "通过测试数": passed_tests,
        "失败测试数": total_tests - passed_tests,
        "通过率": f"{pass_rate:.1%}"
    }
    
    from utils.structured_output_formatter import print_stats_table
    print_stats_table("优化测试统计", stats)
    
    # 显示优化效果说明
    print_main_process("优化效果说明")
    
    optimization_effects = [
        "✅ 分钟数据更新场景：只显示简要的除权除息摘要",
        "✅ 分析调试场景：显示完整的详细信息表格",
        "✅ 智能场景识别：根据处理上下文自动调整输出",
        "✅ 用户体验提升：避免不必要的技术细节暴露",
        "✅ 流程优化器集成：符合统一的输出管理标准"
    ]
    
    for effect in optimization_effects:
        print_info(effect, level=1)
    
    # 判断整体结果
    overall_success = pass_rate >= 0.8
    
    completion_message = "GBBQ输出优化成功！" if overall_success else "GBBQ输出优化需要改进"
    print_completion(completion_message, overall_success, stats)
    
    return 0 if overall_success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
