2025-07-28 23:53:39,729 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_235339.log
2025-07-28 23:53:39,729 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-28 23:53:39,730 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-28 23:53:39,730 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 23:53:39,730 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 23:53:39,730 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 23:53:39,730 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 23:53:39,731 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 23:53:39,731 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 23:53:39,735 - Main - INFO - info:307 - ℹ️ ⚖️ 开始增量下载可行性分析
2025-07-28 23:53:40,173 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:53:40,173 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:53:40,173 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-28 23:53:40,377 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4080条 (目标日期: 20250704, 交易日: 17天, 频率: 1min)
2025-07-28 23:53:40,377 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4080条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:53:40,377 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:53:40,378 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:53:40,432 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3280条
2025-07-28 23:53:40,664 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 23:53:40,879 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 23:53:41,113 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 23:53:41,335 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 23:53:41,563 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +80条，总计4080条
2025-07-28 23:53:41,563 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4080条数据
2025-07-28 23:53:41,564 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要4080条，实际获取4080条
2025-07-28 23:53:41,564 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:53:41,564 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250704
2025-07-28 23:53:41,577 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 23:53:41,579 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 23:53:41,579 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:53:41,581 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-28 23:53:41,581 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:53:41,582 - Main - INFO - info:307 - ℹ️   日期: 202507040931
2025-07-28 23:53:41,582 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-28 23:53:41,582 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-28 23:53:41,582 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 23:53:41,582 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:53:41,583 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:53:41,594 - Main - INFO - info:307 - ℹ️ ✅ 增量下载可行性验证通过
2025-07-28 23:53:41,595 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-28 23:53:41,595 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-28 23:53:41,595 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-28 23:53:41,627 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数17211, 覆盖72个交易日
2025-07-28 23:53:41,627 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-07-28 23:53:41,627 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-07-28 23:53:41,628 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-07-28 23:53:41,628 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-07-28 23:53:41,628 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-07-28 23:53:41,628 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-07-28 23:53:41,629 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-07-28 23:53:41,629 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-07-28 23:53:41,629 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-07-28 23:53:41,629 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-07-28 23:53:41,629 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-28 23:53:41,630 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-28 23:53:41,630 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-28 23:53:41,631 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-07-28 23:53:41,631 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-07-28 23:53:41,631 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-28 23:53:41,656 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-07-28 23:53:41,882 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-07-28 23:53:41,884 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:53:41,885 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:53:41,885 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-28 23:53:42,056 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4080条 (目标日期: 20250704, 交易日: 17天, 频率: 1min)
2025-07-28 23:53:42,056 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4080条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:53:42,056 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:53:42,056 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:53:42,108 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3280条
2025-07-28 23:53:42,340 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 23:53:42,573 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 23:53:42,808 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 23:53:43,042 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 23:53:43,252 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +80条，总计4080条
2025-07-28 23:53:43,252 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4080条数据
2025-07-28 23:53:43,254 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要4080条，实际获取4080条
2025-07-28 23:53:43,254 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:53:43,254 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250704
2025-07-28 23:53:43,264 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 23:53:43,266 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 23:53:43,267 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:53:43,268 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-28 23:53:43,269 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:53:43,269 - Main - INFO - info:307 - ℹ️   日期: 202507040931
2025-07-28 23:53:43,269 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-28 23:53:43,269 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-28 23:53:43,269 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 23:53:43,269 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:53:43,270 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:53:43,279 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-28 23:53:43,282 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-28 23:53:43,282 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250320 - 20250704
2025-07-28 23:53:43,282 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250319
2025-07-28 23:53:43,282 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-07-28 23:53:43,292 - Main - INFO - info:307 - ℹ️ 读取现有数据: 17211 条记录
2025-07-28 23:53:43,292 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250319
2025-07-28 23:53:43,292 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:53:43,293 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:53:43,293 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250319
2025-07-28 23:53:43,471 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32640条 (目标日期: 20250101, 交易日: 136天, 频率: 1min)
2025-07-28 23:53:43,471 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32640条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:53:43,471 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:53:43,471 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:53:43,524 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需31840条
2025-07-28 23:53:43,759 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 23:53:43,984 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 23:53:44,205 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 23:53:44,434 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 23:53:44,670 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-28 23:53:44,888 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-28 23:53:45,118 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-28 23:53:45,342 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-28 23:53:45,565 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-28 23:53:45,798 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-28 23:53:46,017 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-28 23:53:46,253 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-28 23:53:46,473 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-28 23:53:46,696 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-28 23:53:46,919 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-28 23:53:47,140 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-28 23:53:47,364 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-28 23:53:47,597 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-28 23:53:47,817 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-28 23:53:48,044 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-28 23:53:48,282 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-28 23:53:48,513 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-28 23:53:48,736 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-28 23:53:48,967 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-28 23:53:49,195 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-28 23:53:49,425 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-28 23:53:49,642 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21600条数据
2025-07-28 23:53:49,647 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32640条，实际获取21600条
2025-07-28 23:53:49,648 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11040条数据（约46个交易日）
2025-07-28 23:53:49,648 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-28 23:53:49,648 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-28 23:53:49,648 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-28 23:53:49,698 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 480 条 1min 数据
2025-07-28 23:53:49,705 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共480条记录
2025-07-28 23:53:49,705 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:53:49,708 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.600, 前复权=6.600
2025-07-28 23:53:49,709 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:53:49,709 - Main - INFO - info:307 - ℹ️   日期: 202503180931
2025-07-28 23:53:49,709 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.6
2025-07-28 23:53:49,709 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.6
2025-07-28 23:53:49,710 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 480 >= 5
2025-07-28 23:53:49,710 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:53:49,710 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:53:49,711 - Main - INFO - info:307 - ℹ️ 获得新数据: 480 条记录
2025-07-28 23:53:49,711 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-07-28 23:53:49,711 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:53:49,711 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:53:49,711 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-07-28 23:53:49,886 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 3840条 (目标日期: 20250705, 交易日: 16天, 频率: 1min)
2025-07-28 23:53:49,886 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计3840条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:53:49,886 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:53:49,886 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:53:49,937 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3040条
2025-07-28 23:53:50,173 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 23:53:50,393 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 23:53:50,625 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 23:53:50,847 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +640条，总计3840条
2025-07-28 23:53:50,847 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计3840条数据
2025-07-28 23:53:50,848 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要3840条，实际获取3840条
2025-07-28 23:53:50,848 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:53:50,848 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250705
2025-07-28 23:53:50,872 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-07-28 23:53:50,875 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共3600条记录
2025-07-28 23:53:50,875 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:53:50,887 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.640, 前复权=7.640
2025-07-28 23:53:50,889 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:53:50,889 - Main - INFO - info:307 - ℹ️   日期: 202507070931
2025-07-28 23:53:50,889 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.64
2025-07-28 23:53:50,889 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.64
2025-07-28 23:53:50,889 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 3600 >= 5
2025-07-28 23:53:50,889 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:53:50,890 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:53:50,891 - Main - INFO - info:307 - ℹ️ 获得新数据: 3600 条记录
2025-07-28 23:53:50,894 - Main - INFO - info:307 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-07-28 23:53:50,904 - Main - INFO - info:307 - ℹ️ 合并后数据: 21291 条记录
2025-07-28 23:53:50,958 - Main - INFO - info:307 - ℹ️ 删除旧文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-07-28 23:53:50,958 - Main - INFO - info:307 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503180931-202507251500_来源互联网（202507282353）.txt
2025-07-28 23:53:50,959 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-28 23:53:50,959 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-28 23:53:50,960 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 23:53:50,960 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-28 23:53:50,961 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-28 23:53:50,961 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-28 23:53:50,961 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-28 23:53:50,961 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-28 23:53:50,961 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-28 23:53:50,961 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-28 23:53:50,962 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250728_235350.txt
2025-07-28 23:53:50,962 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-28 23:53:50,962 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-28 23:53:50,963 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-28 23:53:50,963 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 23:53:50,963 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 23:53:50,963 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-28 23:53:50,963 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-28 23:53:50,964 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-28 23:53:50,964 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 23:53:50,964 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 23:53:50,964 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
