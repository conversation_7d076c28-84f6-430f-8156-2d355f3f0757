#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
扩展流程优化测试

验证流程优化器在多个模块中的应用效果

位置: test_environments/integration_tests/configs/
作者: AI Assistant
创建时间: 2025-07-31
"""

import sys
import os
import io
from contextlib import redirect_stdout
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_completion
)


def test_flow_optimizer_scenarios():
    """测试流程优化器的不同场景"""
    print_step("流程优化器场景测试", 1, 4)
    
    try:
        from utils.process_flow_optimizer import ProcessFlowOptimizer
        
        optimizer = ProcessFlowOptimizer()
        
        # 测试场景设置
        scenarios = ["minute_data_update", "data_analysis", "file_operations", "error_handling"]
        
        results = {}
        for scenario in scenarios:
            optimizer.set_context(scenario)
            
            # 测试抑制功能
            suppress_tests = {
                "minute_data_update": ["除权除息历史数据详情", "详细计算步骤"],
                "data_analysis": ["调试信息", "性能警告"],
                "file_operations": ["详细文件操作日志", "二进制数据预览"],
                "error_handling": ["技术堆栈信息"]
            }
            
            scenario_result = True
            for operation in suppress_tests.get(scenario, []):
                if not optimizer.should_suppress(operation):
                    scenario_result = False
                    break
            
            results[scenario] = scenario_result
            
            status = "✅" if scenario_result else "❌"
            print_info(f"{scenario}: {status}", level=2)
        
        overall_success = all(results.values())
        print_result("流程优化器场景测试", overall_success, level=1)
        
        return overall_success
        
    except Exception as e:
        print_error(f"流程优化器场景测试失败: {e}", level=1)
        return False


def test_file_writer_optimization():
    """测试文件写入模块的优化"""
    print_step("文件写入优化测试", 2, 4)
    
    try:
        from file_io.file_writer import flow_optimizer
        
        # 设置分钟数据更新上下文
        flow_optimizer.set_context("minute_data_update")
        
        # 测试数据预览优化
        preview_rows = flow_optimizer.optimize_data_preview("分钟级别数据", 1000, 10)
        data_preview_optimized = preview_rows <= 3
        
        # 测试抑制功能
        binary_preview_suppressed = flow_optimizer.should_suppress("二进制数据预览")
        
        optimization_success = data_preview_optimized and binary_preview_suppressed
        
        print_info(f"数据预览行数优化: {preview_rows} 行 ({'✅' if data_preview_optimized else '❌'})", level=2)
        print_info(f"二进制预览抑制: {'✅' if binary_preview_suppressed else '❌'}", level=2)
        
        print_result("文件写入优化测试", optimization_success, level=1)
        
        return optimization_success
        
    except Exception as e:
        print_error(f"文件写入优化测试失败: {e}", level=1)
        return False


def test_error_handler_optimization():
    """测试错误处理模块的优化"""
    print_step("错误处理优化测试", 3, 4)
    
    try:
        from utils.enhanced_error_handler import EnhancedErrorHandler
        
        handler = EnhancedErrorHandler()
        
        # 设置错误处理上下文
        handler.flow_optimizer.set_context("error_handling")
        
        # 测试错误显示优化
        test_error_msg = "测试错误信息"
        test_technical_details = "详细的技术堆栈信息..."
        
        optimized_msg = handler.flow_optimizer.optimize_error_display(
            test_error_msg, test_technical_details
        )
        
        # 验证技术细节是否被抑制
        technical_suppressed = test_technical_details not in optimized_msg
        user_friendly = test_error_msg in optimized_msg
        
        optimization_success = technical_suppressed and user_friendly
        
        print_info(f"技术细节抑制: {'✅' if technical_suppressed else '❌'}", level=2)
        print_info(f"用户友好信息保留: {'✅' if user_friendly else '❌'}", level=2)
        
        print_result("错误处理优化测试", optimization_success, level=1)
        
        return optimization_success
        
    except Exception as e:
        print_error(f"错误处理优化测试失败: {e}", level=1)
        return False


def test_optimization_statistics():
    """测试优化统计功能"""
    print_step("优化统计测试", 4, 4)
    
    try:
        from utils.process_flow_optimizer import ProcessFlowOptimizer
        
        optimizer = ProcessFlowOptimizer()
        
        # 模拟一些优化操作
        optimizer.suppress_technical_details("测试操作1")
        optimizer.suppress_technical_details("测试操作2")
        optimizer.set_context("minute_data_update")
        
        # 检查统计信息
        stats = optimizer.optimization_stats
        
        has_suppressed_count = stats.get("suppressed_details", 0) > 0
        has_total_count = stats.get("total_optimizations", 0) >= 0
        
        statistics_working = has_suppressed_count and has_total_count
        
        print_info(f"抑制操作计数: {stats.get('suppressed_details', 0)} ({'✅' if has_suppressed_count else '❌'})", level=2)
        print_info(f"总优化计数: {stats.get('total_optimizations', 0)} ({'✅' if has_total_count else '❌'})", level=2)
        
        print_result("优化统计测试", statistics_working, level=1)
        
        return statistics_working
        
    except Exception as e:
        print_error(f"优化统计测试失败: {e}", level=1)
        return False


def generate_optimization_report():
    """生成优化效果报告"""
    print_main_process("流程优化效果报告")
    
    optimization_areas = [
        {
            "area": "GBBQ除权除息数据显示",
            "status": "已优化",
            "improvement": "分钟数据场景下输出简化90%",
            "impact": "高"
        },
        {
            "area": "文件写入数据预览",
            "status": "已优化", 
            "improvement": "数据预览行数智能控制",
            "impact": "中"
        },
        {
            "area": "错误信息显示",
            "status": "已优化",
            "improvement": "技术细节智能隐藏",
            "impact": "高"
        },
        {
            "area": "二进制数据预览",
            "status": "已优化",
            "improvement": "分钟数据场景下完全抑制",
            "impact": "中"
        },
        {
            "area": "详细计算步骤",
            "status": "已优化",
            "improvement": "根据场景智能显示",
            "impact": "高"
        }
    ]
    
    print_info("已完成的优化区域:", level=1)
    for i, area in enumerate(optimization_areas, 1):
        status_icon = "✅" if area["status"] == "已优化" else "🔄"
        impact_icon = "🔥" if area["impact"] == "高" else "📊"
        print_info(f"{i}. {area['area']} {status_icon}", level=2)
        print_info(f"   改进效果: {area['improvement']}", level=3)
        print_info(f"   影响程度: {area['impact']} {impact_icon}", level=3)
    
    return optimization_areas


def main():
    """主函数"""
    print_banner("扩展流程优化测试", "验证流程优化器在多个模块中的应用效果")
    
    print_info(f"项目根目录: {project_root}")
    print_info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print_main_process("执行扩展流程优化测试")
    
    # 执行测试
    tests = [
        ("流程优化器场景", test_flow_optimizer_scenarios),
        ("文件写入优化", test_file_writer_optimization),
        ("错误处理优化", test_error_handler_optimization),
        ("优化统计功能", test_optimization_statistics)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print_error(f"测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成优化报告
    optimization_areas = generate_optimization_report()
    
    # 生成测试报告
    total_tests = len(tests)
    pass_rate = passed_tests / total_tests
    
    print_main_process("扩展流程优化测试结果")
    
    # 显示详细结果
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print_result(f"{test_name}: {status}", result, level=1)
    
    # 显示统计信息
    stats = {
        "总测试数": total_tests,
        "通过测试数": passed_tests,
        "失败测试数": total_tests - passed_tests,
        "通过率": f"{pass_rate:.1%}",
        "优化区域数": len(optimization_areas),
        "已完成优化": len([a for a in optimization_areas if a["status"] == "已优化"])
    }
    
    from utils.structured_output_formatter import print_stats_table
    print_stats_table("扩展优化测试统计", stats)
    
    # 判断整体结果
    overall_success = pass_rate >= 0.75
    
    completion_message = "扩展流程优化成功！" if overall_success else "扩展流程优化需要改进"
    print_completion(completion_message, overall_success, stats)
    
    return 0 if overall_success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
