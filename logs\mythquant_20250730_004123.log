2025-07-30 00:41:23,895 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250730_004123.log
2025-07-30 00:41:23,895 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-30 00:41:23,895 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-30 00:41:23,895 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-30 00:41:23,896 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-30 00:41:23,896 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-30 00:41:23,896 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-30 00:41:23,896 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-30 00:41:23,897 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-30 00:41:23,903 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-30 00:41:24,101 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4560条 (目标日期: 20250704, 交易日: 19天, 频率: 1min)
2025-07-30 00:41:24,101 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4560条 -> 实际请求800条 (配置限制:800)
2025-07-30 00:41:24,101 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-30 00:41:24,101 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-30 00:41:24,154 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3760条
2025-07-30 00:41:24,386 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-30 00:41:24,619 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-30 00:41:24,836 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-30 00:41:25,059 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-30 00:41:25,285 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +560条，总计4560条
2025-07-30 00:41:25,285 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4560条数据
2025-07-30 00:41:25,290 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要4560条，实际获取4560条
2025-07-30 00:41:25,305 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-30 00:41:25,309 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx只提供原始价格，正在尝试计算前复权价格...
2025-07-30 00:41:25,309 - Main - INFO - info:307 - ℹ️ 🔄 pytdx前复权计算: 000617
2025-07-30 00:41:25,309 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-30 00:41:25,309 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-30 00:41:25,827 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-30 00:41:25,827 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-30 00:41:25,838 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-30 00:41:25,838 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17/T0002
2025-07-30 00:41:25,838 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-30 00:41:25,839 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-30 00:41:25,839 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 未找到任何市场的分钟数据文件
2025-07-30 00:41:25,840 - main_v20230219_optimized - WARNING - _get_optimal_tdx_path:255 - ⚠️  主路径下未找到分钟数据文件，但将继续使用配置路径
2025-07-30 00:41:25,840 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17/T0002，缓存方法: memory
2025-07-30 00:42:48,468 - main_v20230219_optimized - INFO - _ensure_pickle_cache:441 - 🔄 更新pickle缓存...
2025-07-30 00:42:48,468 - main_v20230219_optimized - ERROR - _ensure_pickle_cache:495 - ❌ pickle缓存更新失败: 'StockDataProcessor' object has no attribute 'tdx_path'
2025-07-30 00:42:49,145 - main_v20230219_optimized - WARNING - _extract_stock_code_from_data:2442 - 无法从数据中提取股票代码，使用target_stocks中的第一个
2025-07-30 00:42:49,174 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 240条
2025-07-30 00:42:49,175 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功，处理了20个除权事件
2025-07-30 00:42:49,203 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功
2025-07-30 00:42:49,203 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价格与原始价格相同，可能无除权事件
2025-07-30 00:42:49,207 - Main - INFO - info:307 - ℹ️ ℹ️ 增量下载前提条件不满足: 不具备增量下载前提条件: 前复权价格不一致，存在分红配股影响
2025-07-30 00:42:49,208 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-30 00:42:49,208 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-30 00:42:49,208 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-30 00:42:49,237 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数17211, 覆盖72个交易日
2025-07-30 00:42:49,237 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-07-30 00:42:49,237 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-07-30 00:42:49,237 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-07-30 00:42:49,238 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-07-30 00:42:49,238 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-07-30 00:42:49,238 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-07-30 00:42:49,238 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-07-30 00:42:49,238 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-07-30 00:42:49,238 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-07-30 00:42:49,238 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-07-30 00:42:49,239 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-30 00:42:49,761 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-30 00:42:49,761 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-30 00:42:49,761 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-30 00:42:49,761 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-30 00:42:49,943 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 33120条 (目标日期: 20250101, 交易日: 138天, 频率: 1min)
2025-07-30 00:42:49,943 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计33120条 -> 实际请求800条 (配置限制:800)
2025-07-30 00:42:49,943 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-30 00:42:49,943 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-30 00:42:49,995 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32320条
2025-07-30 00:42:50,224 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-30 00:42:50,457 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-30 00:42:50,673 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-30 00:42:50,891 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-30 00:42:51,127 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-30 00:42:51,358 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-30 00:42:51,582 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-30 00:42:51,807 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-30 00:42:52,031 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-30 00:42:52,259 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-30 00:42:52,498 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-30 00:42:52,718 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-30 00:42:52,954 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-30 00:42:53,176 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-30 00:42:53,404 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-30 00:42:53,634 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-30 00:42:53,867 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-30 00:42:54,087 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-30 00:42:54,315 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-30 00:42:54,535 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-30 00:42:54,762 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-30 00:42:54,997 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-30 00:42:55,216 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-30 00:42:55,436 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-30 00:42:55,663 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-30 00:42:55,890 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-30 00:42:56,102 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +240条，总计21840条
2025-07-30 00:42:56,102 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21840条数据
2025-07-30 00:42:56,106 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要33120条，实际获取21840条
2025-07-30 00:42:56,107 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11280条数据（约47个交易日）
2025-07-30 00:42:56,107 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-30 00:42:56,107 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-30 00:42:56,107 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-30 00:42:56,156 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-07-30 00:42:56,276 - Main - INFO - info:307 - ℹ️ ✅ 数据保存完成: 1min_0_000617_20250318-20250725_来源互联网（202507300042）.txt (999617 字节)
2025-07-30 00:42:56,277 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-30 00:42:56,277 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-30 00:42:56,277 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-30 00:42:56,278 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-30 00:42:56,278 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-30 00:42:56,278 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-30 00:42:56,278 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-30 00:42:56,279 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-30 00:42:56,279 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-30 00:42:56,280 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250730_004256.txt
2025-07-30 00:42:56,280 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-30 00:42:56,280 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-30 00:42:56,280 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-30 00:42:56,281 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-30 00:42:56,281 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-30 00:42:56,281 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-30 00:42:56,281 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-30 00:42:56,281 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-30 00:42:56,281 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-30 00:42:56,281 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-30 00:42:56,281 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
