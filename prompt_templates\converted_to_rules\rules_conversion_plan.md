# MythQuant 规则转换计划

## 🎯 转换策略分析

### 应该转换为规则的内容（高频操作类）

#### 1. **ALWAYS** 规则类型
- **来源**: `best_practices.md` 中的编码规范
- **内容**: 始终应该遵循的编码和架构原则
- **例子**: "总是使用Decimal进行金融计算"、"始终进行浮点数精度检查"

#### 2. **USER_RULES** 规则类型  
- **来源**: `project_context.md` 中的用户偏好
- **内容**: 用户明确表达的工作方式偏好
- **例子**: "用户偏好功能描述而非文件名"、"显示中文响应"

#### 3. **AGENT** 规则类型
- **来源**: `process_improvements.md` 中的AI行为指导
- **内容**: AI助手的具体工作方式和决策逻辑
- **例子**: "优先并行工具调用"、"文件修改优先级判断规则"

### 保持为文档的内容（知识库类）

#### 1. **技术细节知识** 
- **保持**: `technical_points.md`、`stock_python.md`
- **原因**: 详细的技术实现，作为参考文档更合适

#### 2. **历史案例记录**
- **保持**: `bug_cases.md`、`session_summary.md`  
- **原因**: 具体案例和历史记录，按需查阅

#### 3. **长篇指南**
- **保持**: `ai_knowledge_base.md`
- **原因**: 综合性知识，规则形式会过于冗长

## 🔄 具体转换建议

### 转换为 USER_RULES
```markdown
- 模块化拆分时遵循既定的文件定位规则[[memory:3564322]]
- 优先使用功能描述而非文件名进行交互
- 在前复权计算中避免使用人工距离补偿[[memory:3125115]]
- 研究文档时完整阅读避免断章取义[[memory:3125122]]
```

### 转换为 AGENT 规则
```markdown
- 当用户提及"显示"、"打印"相关需求时，优先检查ui/display.py模块
- 当用户提及"读取"、"写入"相关需求时，优先检查file_io/目录下模块
- 算法计算相关修改定位到main_v20230219_optimized.py第2120-3154行
- 配置相关修改定位到user_config.py
- 进行模块化拆分时，严格遵循测试驱动的验证流程
```

### 转换为 ALWAYS 规则
```markdown
- 金融计算中总是使用高精度Decimal而非float
- 文件修改前总是先进行完整的代码理解和影响分析
- 模块化拆分后总是创建完整的测试验证脚本
- 浮点数比较总是使用容差检查而非直接等值比较
```

## 📋 实施计划

### 阶段一：核心操作规则转换
1. 从`project_context.md`提取文件定位规则 → USER_RULES
2. 从`best_practices.md`提取编码规范 → ALWAYS
3. 从`pitfall_guide.md`提取避坑原则 → ALWAYS

### 阶段二：知识库保留优化  
1. 保留`technical_points.md`等详细技术文档
2. 优化文档结构，便于AI按需查找
3. 建立文档索引和交叉引用

### 阶段三：混合使用验证
1. 测试规则转换效果
2. 根据使用频率调整转换策略
3. 建立规则与文档的互补机制 