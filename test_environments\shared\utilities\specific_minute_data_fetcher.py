#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特定分钟数据获取器

专门用于获取某个特定分钟（如202507241447）的pytdx 1分钟数据的便捷封装

作者: AI Assistant
创建时间: 2025-07-29
"""

import sys
import os
from typing import Dict, List, Optional
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from utils.pytdx_downloader import PytdxDownloader


class SpecificMinuteDataFetcher:
    """特定分钟数据获取器"""
    
    def __init__(self):
        """初始化获取器"""
        self.downloader = PytdxDownloader()
    
    def get_minute_data(self, stock_code: str, target_datetime: str,
                       suppress_warnings: bool = False) -> Optional[Dict]:
        """
        获取特定分钟的数据

        Args:
            stock_code: 股票代码（如'000617'或'617'）
            target_datetime: 目标时间（YYYYMMDDHHMM格式，如'202507241447'）
            suppress_warnings: 是否抑制数据覆盖不足的警告（用于价格比较等场景）

        Returns:
            数据字典或None

        Example:
            >>> fetcher = SpecificMinuteDataFetcher()
            >>> data = fetcher.get_minute_data('000617', '202507241447')
            >>> if data:
            ...     print(f"收盘价: {data['close']}")
            ...     print(f"前复权价: {data['close_qfq']}")
        """
        # 确保股票代码格式正确
        formatted_stock_code = str(stock_code).zfill(6)

        return self.downloader.get_specific_minute_data(formatted_stock_code, target_datetime,
                                                       suppress_warnings=suppress_warnings)
    
    def get_multiple_minutes(self, stock_code: str, target_datetimes: List[str]) -> Dict[str, Optional[Dict]]:
        """
        批量获取多个特定分钟的数据
        
        Args:
            stock_code: 股票代码
            target_datetimes: 目标时间列表
            
        Returns:
            字典，键为时间，值为数据字典或None
            
        Example:
            >>> fetcher = SpecificMinuteDataFetcher()
            >>> times = ['202507241447', '202507241448', '202507241449']
            >>> data = fetcher.get_multiple_minutes('000617', times)
            >>> for time, info in data.items():
            ...     if info:
            ...         print(f"{time}: 收盘价={info['close']}")
        """
        formatted_stock_code = str(stock_code).zfill(6)
        
        return self.downloader.get_minute_data_batch(formatted_stock_code, target_datetimes)
    
    def get_price_at_time(self, stock_code: str, target_datetime: str, price_type: str = 'close_qfq') -> Optional[float]:
        """
        获取特定时间的特定价格
        
        Args:
            stock_code: 股票代码
            target_datetime: 目标时间
            price_type: 价格类型（'close', 'close_qfq', 'open', 'high', 'low'）
            
        Returns:
            价格值或None
            
        Example:
            >>> fetcher = SpecificMinuteDataFetcher()
            >>> price = fetcher.get_price_at_time('000617', '202507241447', 'close_qfq')
            >>> if price:
            ...     print(f"前复权收盘价: {price}")
        """
        data = self.get_minute_data(stock_code, target_datetime)
        
        if data and price_type in data:
            return data[price_type]
        
        return None
    
    def compare_prices_at_time(self, stock_code: str, target_datetime: str) -> Optional[Dict]:
        """
        比较特定时间的原始价格和前复权价格
        
        Args:
            stock_code: 股票代码
            target_datetime: 目标时间
            
        Returns:
            价格比较字典或None
            
        Example:
            >>> fetcher = SpecificMinuteDataFetcher()
            >>> comparison = fetcher.compare_prices_at_time('000617', '202507241447')
            >>> if comparison:
            ...     print(f"原始价格: {comparison['original_price']}")
            ...     print(f"前复权价格: {comparison['adjusted_price']}")
            ...     print(f"调整比例: {comparison['adjustment_ratio']:.4f}")
        """
        data = self.get_minute_data(stock_code, target_datetime)
        
        if not data:
            return None
        
        original_price = data.get('close', 0)
        adjusted_price = data.get('close_qfq', 0)
        
        if original_price == 0:
            return None
        
        adjustment_ratio = adjusted_price / original_price
        adjustment_factor = original_price - adjusted_price
        
        return {
            'stock_code': data['stock_code'],
            'datetime': target_datetime,
            'original_price': original_price,
            'adjusted_price': adjusted_price,
            'adjustment_ratio': adjustment_ratio,
            'adjustment_factor': adjustment_factor,
            'has_adjustment': abs(adjustment_factor) > 0.001
        }
    
    def validate_time_format(self, target_datetime: str) -> bool:
        """
        验证时间格式是否正确
        
        Args:
            target_datetime: 目标时间字符串
            
        Returns:
            是否格式正确
        """
        if len(target_datetime) != 12:
            return False
        
        try:
            # 尝试解析时间
            datetime.strptime(target_datetime, '%Y%m%d%H%M')
            return True
        except ValueError:
            return False
    
    def get_trading_session_data(self, stock_code: str, target_date: str, 
                                session: str = 'morning') -> List[Dict]:
        """
        获取特定交易时段的数据
        
        Args:
            stock_code: 股票代码
            target_date: 目标日期（YYYYMMDD）
            session: 交易时段（'morning'=上午, 'afternoon'=下午, 'all'=全天）
            
        Returns:
            数据列表
        """
        if session == 'morning':
            # 上午9:30-11:30
            start_time = f"{target_date}0930"
            end_time = f"{target_date}1130"
        elif session == 'afternoon':
            # 下午13:00-15:00
            start_time = f"{target_date}1300"
            end_time = f"{target_date}1500"
        else:
            # 全天
            start_time = f"{target_date}0930"
            end_time = f"{target_date}1500"
        
        # 生成时间列表（每分钟）
        time_list = []
        current_time = datetime.strptime(start_time, '%Y%m%d%H%M')
        end_datetime = datetime.strptime(end_time, '%Y%m%d%H%M')
        
        while current_time <= end_datetime:
            # 跳过中午休市时间
            if not (1130 < int(current_time.strftime('%H%M')) < 1300):
                time_list.append(current_time.strftime('%Y%m%d%H%M'))
            
            # 增加1分钟
            current_time = current_time.replace(minute=current_time.minute + 1)
            if current_time.minute == 60:
                current_time = current_time.replace(hour=current_time.hour + 1, minute=0)
        
        # 批量获取数据
        batch_data = self.get_multiple_minutes(stock_code, time_list)
        
        # 过滤有效数据
        valid_data = [data for data in batch_data.values() if data is not None]
        
        return valid_data


def get_specific_minute_data(stock_code: str, target_datetime: str) -> Optional[Dict]:
    """
    便捷函数：获取特定分钟的未复权数据

    Args:
        stock_code: 股票代码（如'000617'）
        target_datetime: 目标时间（如'202507241447'）

    Returns:
        未复权数据字典或None

    Note:
        ⚠️ 数据源限制：pytdx只提供最近约100个交易日的分钟数据
        ⚠️ 专注未复权数据：此函数只返回原始的未复权价格数据
        如果需要前复权数据，请使用其他专门的前复权处理函数

    Example:
        >>> data = get_specific_minute_data('000617', '202507241447')
        >>> if data:
        ...     print(f"未复权收盘价: {data['close']}")
        >>> else:
        ...     print("数据不存在（可能超出pytdx覆盖范围）")
    """
    fetcher = SpecificMinuteDataFetcher()
    return fetcher.get_minute_data(stock_code, target_datetime)


def get_price_at_time(stock_code: str, target_datetime: str, price_type: str = 'close') -> Optional[float]:
    """
    便捷函数：获取特定时间的未复权价格

    Args:
        stock_code: 股票代码
        target_datetime: 目标时间
        price_type: 价格类型（'close', 'open', 'high', 'low'）

    Returns:
        指定类型的未复权价格值或None

    Note:
        ⚠️ 默认返回未复权价格：price_type='close'返回未复权收盘价

    Example:
        >>> price = get_price_at_time('000617', '202507241447')
        >>> if price:
        ...     print(f"未复权收盘价: {price}")
    """
    fetcher = SpecificMinuteDataFetcher()
    return fetcher.get_price_at_time(stock_code, target_datetime, price_type)


if __name__ == '__main__':
    # 测试功能
    print("🧪 特定分钟数据获取器测试")
    print("=" * 50)
    
    # 创建获取器
    fetcher = SpecificMinuteDataFetcher()
    
    # 测试获取特定分钟数据
    test_stock = '000617'
    test_time = '202507241447'
    
    print(f"📊 测试获取 {test_stock} @ {test_time} 的数据")
    
    data = fetcher.get_minute_data(test_stock, test_time)
    
    if data:
        print(f"✅ 获取成功:")
        print(f"   股票代码: {data['stock_code']}")
        print(f"   时间: {data['datetime']}")
        print(f"   收盘价: {data['close']:.3f}")
        print(f"   前复权价: {data['close_qfq']:.3f}")
        
        if 'open' in data:
            print(f"   开盘价: {data['open']:.3f}")
            print(f"   最高价: {data['high']:.3f}")
            print(f"   最低价: {data['low']:.3f}")
            print(f"   成交量: {data['volume']:,}")
    else:
        print(f"❌ 未获取到数据")
    
    # 测试价格比较
    print(f"\n📊 测试价格比较")
    comparison = fetcher.compare_prices_at_time(test_stock, test_time)
    
    if comparison:
        print(f"✅ 价格比较:")
        print(f"   原始价格: {comparison['original_price']:.3f}")
        print(f"   前复权价: {comparison['adjusted_price']:.3f}")
        print(f"   调整比例: {comparison['adjustment_ratio']:.4f}")
        print(f"   有除权调整: {'是' if comparison['has_adjustment'] else '否'}")
    else:
        print(f"❌ 价格比较失败")
