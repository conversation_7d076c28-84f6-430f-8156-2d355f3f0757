#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证领域服务

提供配置验证的业务逻辑
"""

from typing import List, Tuple, Dict, Any
from dataclasses import dataclass
from ..entities.trading_config import TradingConfig
from ..entities.data_source_config import DataSourceConfig
from ..entities.processing_config import ProcessingConfig


@dataclass
class ValidationResult:
    """验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    suggestions: List[str]


class ConfigValidationService:
    """配置验证领域服务"""
    
    def validate_trading_config(self, config: TradingConfig) -> ValidationResult:
        """验证交易配置"""
        errors = []
        warnings = []
        suggestions = []
        
        try:
            # 验证TDX连接
            if not config.tdx_connection.is_valid():
                errors.append("TDX连接配置无效")
            
            # 验证输出路径
            if not config.output_path.path:
                errors.append("输出路径不能为空")
            elif not config.output_path.exists():
                warnings.append(f"输出路径不存在: {config.output_path.path}")
                suggestions.append("建议创建输出目录或检查路径配置")
            
            # 验证目标股票配置
            if config.target_stocks_file:
                if not config.target_stocks_file.exists():
                    warnings.append(f"目标股票文件不存在: {config.target_stocks_file.path}")
                    if not config.default_stocks:
                        errors.append("目标股票文件不存在且没有默认股票列表")
                    else:
                        suggestions.append("将使用默认股票列表")
            
            # 验证pytdx配置
            if config.pytdx_config:
                self._validate_pytdx_config(config.pytdx_config, errors, warnings, suggestions)
            
            # 验证GBBQ路径
            if config.gbbq_path:
                gbbq_full_path = config.tdx_connection.get_full_path(config.gbbq_path)
                import os
                if not os.path.exists(gbbq_full_path):
                    warnings.append(f"GBBQ文件不存在: {gbbq_full_path}")
                    suggestions.append("检查GBBQ文件路径配置")
            
        except Exception as e:
            errors.append(f"验证过程中发生错误: {str(e)}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions
        )
    
    def validate_data_source_config(self, config: DataSourceConfig) -> ValidationResult:
        """验证数据源配置"""
        errors = []
        warnings = []
        suggestions = []
        
        try:
            # 验证TDX连接
            if not config.tdx_connection.is_valid():
                errors.append("TDX连接配置无效")
            
            # 验证网络配置
            network_result = self._validate_network_config(config.network_config)
            errors.extend(network_result['errors'])
            warnings.extend(network_result['warnings'])
            suggestions.extend(network_result['suggestions'])
            
            # 验证错误处理配置
            error_handling_result = self._validate_error_handling_config(config.error_handling)
            errors.extend(error_handling_result['errors'])
            warnings.extend(error_handling_result['warnings'])
            
            # 验证监控配置
            monitoring_result = self._validate_monitoring_config(config.monitoring)
            errors.extend(monitoring_result['errors'])
            warnings.extend(monitoring_result['warnings'])
            
        except Exception as e:
            errors.append(f"验证过程中发生错误: {str(e)}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions
        )
    
    def validate_processing_config(self, config: ProcessingConfig) -> ValidationResult:
        """验证数据处理配置"""
        errors = []
        warnings = []
        suggestions = []
        
        try:
            # 验证智能文件选择器配置
            selector_result = self._validate_smart_file_selector_config(config.smart_file_selector)
            errors.extend(selector_result['errors'])
            warnings.extend(selector_result['warnings'])
            suggestions.extend(selector_result['suggestions'])
            
            # 验证数据处理配置
            processing_result = self._validate_data_processing_config(config.data_processing)
            errors.extend(processing_result['errors'])
            warnings.extend(processing_result['warnings'])
            
            # 验证智能功能配置
            features_result = self._validate_intelligent_features_config(config.intelligent_features)
            errors.extend(features_result['errors'])
            warnings.extend(features_result['warnings'])
            
            # 验证用户界面配置
            ui_result = self._validate_user_interface_config(config.user_interface)
            errors.extend(ui_result['errors'])
            warnings.extend(ui_result['warnings'])
            
        except Exception as e:
            errors.append(f"验证过程中发生错误: {str(e)}")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions
        )
    
    def validate_all_configs(self, trading: TradingConfig, data_source: DataSourceConfig, 
                           processing: ProcessingConfig) -> ValidationResult:
        """验证所有配置的一致性"""
        errors = []
        warnings = []
        suggestions = []
        
        # 分别验证各个配置
        trading_result = self.validate_trading_config(trading)
        data_source_result = self.validate_data_source_config(data_source)
        processing_result = self.validate_processing_config(processing)
        
        # 合并结果
        errors.extend(trading_result.errors)
        errors.extend(data_source_result.errors)
        errors.extend(processing_result.errors)
        
        warnings.extend(trading_result.warnings)
        warnings.extend(data_source_result.warnings)
        warnings.extend(processing_result.warnings)
        
        suggestions.extend(trading_result.suggestions)
        suggestions.extend(data_source_result.suggestions)
        suggestions.extend(processing_result.suggestions)
        
        # 验证配置间的一致性
        if trading.tdx_connection != data_source.tdx_connection:
            errors.append("交易配置和数据源配置中的TDX连接不一致")
        
        return ValidationResult(
            is_valid=len(errors) == 0,
            errors=errors,
            warnings=warnings,
            suggestions=suggestions
        )
    
    def _validate_pytdx_config(self, pytdx_config: Dict[str, Any], errors: List[str], 
                              warnings: List[str], suggestions: List[str]) -> None:
        """验证pytdx配置"""
        # 验证K线限制
        if 'kline_limits' in pytdx_config:
            limits = pytdx_config['kline_limits']
            for freq, limit in limits.items():
                if not isinstance(limit, int) or limit <= 0:
                    errors.append(f"K线限制配置无效: {freq}={limit}")
                elif limit > 1000:
                    warnings.append(f"K线限制过高可能导致请求失败: {freq}={limit}")
                    suggestions.append(f"建议将{freq}的限制设置为800以下")
        
        # 验证数据缓冲区系数
        if 'data_buffer_factor' in pytdx_config:
            factor = pytdx_config['data_buffer_factor']
            if not isinstance(factor, (int, float)) or factor <= 0:
                errors.append(f"数据缓冲区系数无效: {factor}")
            elif factor > 2.0:
                warnings.append(f"数据缓冲区系数过高: {factor}")
                suggestions.append("建议将缓冲区系数设置在1.0-1.5之间")
    
    def _validate_network_config(self, network_config: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证网络配置"""
        errors = []
        warnings = []
        suggestions = []
        
        # 验证超时时间
        if 'connection_timeout' in network_config:
            timeout = network_config['connection_timeout']
            if timeout <= 0:
                errors.append("连接超时时间必须大于0")
            elif timeout > 300:
                warnings.append(f"连接超时时间过长: {timeout}秒")
                suggestions.append("建议将连接超时设置在30-60秒之间")
        
        if 'read_timeout' in network_config:
            timeout = network_config['read_timeout']
            if timeout <= 0:
                errors.append("读取超时时间必须大于0")
            elif timeout > 600:
                warnings.append(f"读取超时时间过长: {timeout}秒")
        
        # 验证连接数
        if 'max_connections' in network_config:
            max_conn = network_config['max_connections']
            if max_conn <= 0:
                errors.append("最大连接数必须大于0")
            elif max_conn > 100:
                warnings.append(f"最大连接数过高: {max_conn}")
                suggestions.append("建议将最大连接数设置在10-50之间")
        
        return {'errors': errors, 'warnings': warnings, 'suggestions': suggestions}
    
    def _validate_error_handling_config(self, error_config: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证错误处理配置"""
        errors = []
        warnings = []
        
        required_keys = ['log_all_errors', 'raise_on_critical']
        for key in required_keys:
            if key not in error_config:
                errors.append(f"缺少必需的错误处理配置: {key}")
            elif not isinstance(error_config[key], bool):
                errors.append(f"错误处理配置类型错误: {key}应为布尔值")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_monitoring_config(self, monitoring_config: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证监控配置"""
        errors = []
        warnings = []
        
        if 'success_rate_threshold' in monitoring_config:
            threshold = monitoring_config['success_rate_threshold']
            if not isinstance(threshold, (int, float)):
                errors.append("成功率阈值必须是数字")
            elif not (0.0 <= threshold <= 1.0):
                errors.append("成功率阈值必须在0.0-1.0之间")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_smart_file_selector_config(self, selector_config: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证智能文件选择器配置"""
        errors = []
        warnings = []
        suggestions = []
        
        # 验证评分权重
        if 'scoring_weights' in selector_config:
            weights = selector_config['scoring_weights']
            total_weight = sum(weights.values())
            if not (0.9 <= total_weight <= 1.1):
                errors.append(f"评分权重总和应接近1.0，当前为: {total_weight}")
                suggestions.append("调整权重配置使总和等于1.0")
        
        return {'errors': errors, 'warnings': warnings, 'suggestions': suggestions}
    
    def _validate_data_processing_config(self, processing_config: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证数据处理配置"""
        errors = []
        warnings = []
        
        # 基本验证
        if 'transparent_processing' not in processing_config:
            errors.append("缺少透明数据处理配置")
        
        if 'quality_verification' not in processing_config:
            errors.append("缺少数据质量验证配置")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_intelligent_features_config(self, features_config: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证智能功能配置"""
        errors = []
        warnings = []
        
        required_features = ['incremental_download', 'missing_data_processor']
        for feature in required_features:
            if feature not in features_config:
                errors.append(f"缺少智能功能配置: {feature}")
        
        return {'errors': errors, 'warnings': warnings}
    
    def _validate_user_interface_config(self, ui_config: Dict[str, Any]) -> Dict[str, List[str]]:
        """验证用户界面配置"""
        errors = []
        warnings = []
        
        if 'display_level' in ui_config:
            level = ui_config['display_level']
            if level not in ['minimal', 'normal', 'detailed']:
                errors.append(f"无效的显示级别: {level}")
        
        return {'errors': errors, 'warnings': warnings}
