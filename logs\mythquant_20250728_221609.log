2025-07-28 22:16:09,899 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_221609.log
2025-07-28 22:16:09,900 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-28 22:16:09,926 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-28 22:16:09,928 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-28 22:16:09,928 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-28 22:16:09,928 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-28 22:16:09,928 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-28 22:16:10,203 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.181s
2025-07-28 22:16:10,234 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_218.75.126.9 (218.75.126.9:7709) - 0.196s
2025-07-28 22:16:10,239 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.204s
2025-07-28 22:16:10,241 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.200s
2025-07-28 22:16:10,267 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.224s
2025-07-28 22:16:10,291 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.240s
2025-07-28 22:16:10,297 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-28 22:16:10,298 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-28 22:16:10,298 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-28 22:16:10,298 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-28 22:16:10,298 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-28 22:16:10,299 - Main - INFO - info:307 - ℹ️ 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-28 22:16:10,299 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 22:16:10,299 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 22:16:10,299 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 22:16:10,299 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 22:16:10,300 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 22:16:10,300 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-28 22:16:10,302 - MythQuant - ERROR - log_error:132 - 🚨 [BUSINESS] RecursionError: maximum recursion depth exceeded | 操作: 任务执行 | 上下文: task_name=TDX日线级别数据生成, task_type=daily
2025-07-28 22:16:10,303 - Main - ERROR - error:315 - ❌ 任务执行异常: TDX日线级别数据生成 [错误ID: BUSINESS_1753712170301]
2025-07-28 22:16:10,303 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 22:16:10,304 - MythQuant - ERROR - log_error:132 - 🚨 [BUSINESS] RecursionError: maximum recursion depth exceeded | 操作: 任务执行 | 上下文: task_name=互联网分钟级数据下载, task_type=internet_minute
2025-07-28 22:16:10,305 - Main - ERROR - error:315 - ❌ 任务执行异常: 互联网分钟级数据下载 [错误ID: BUSINESS_1753712170303]
2025-07-28 22:16:10,305 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 22:16:10,306 - MythQuant - ERROR - log_error:132 - 🚨 [BUSINESS] RecursionError: maximum recursion depth exceeded | 操作: 任务执行 | 上下文: task_name=前复权数据比较分析, task_type=data_comparison
2025-07-28 22:16:10,306 - Main - ERROR - error:315 - ❌ 任务执行异常: 前复权数据比较分析 [错误ID: BUSINESS_1753712170305]
2025-07-28 22:16:10,307 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 0.0%
2025-07-28 22:16:10,309 - MythQuant - INFO - export_error_report:279 - 📊 错误报告已导出: logs/error_report_20250728_221610.json
2025-07-28 22:16:10,309 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 22:16:10,310 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 22:16:10,310 - Main - ERROR - error:315 - ❌ 资源清理失败: maximum recursion depth exceeded
2025-07-28 22:16:10,310 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 22:16:10,310 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 22:16:10,310 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
