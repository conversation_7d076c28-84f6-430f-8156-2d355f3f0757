2025-08-01 23:12:42,784 - Main - INFO - info:316 - ℹ️ 程序启动，日志文件: logs\mythquant_20250801_231242.log
2025-08-01 23:12:42,784 - Main - INFO - info:316 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-08-01 23:12:42,785 - Main - INFO - info:316 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-08-01 23:12:42,785 - Main - INFO - info:316 - ℹ️ 正在加载任务配置...
2025-08-01 23:12:42,785 - Main - INFO - info:316 - ℹ️ 加载了 7 个任务配置
2025-08-01 23:12:42,785 - Main - INFO - info:316 - ℹ️ 核心组件初始化完成
2025-08-01 23:12:42,785 - Main - INFO - info:316 - ℹ️ 应用程序初始化完成
2025-08-01 23:12:42,785 - Main - INFO - info:316 - ℹ️ 开始执行所有任务
2025-08-01 23:12:42,786 - Main - INFO - info:316 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-08-01 23:12:42,812 - Main - INFO - info:316 - ℹ️ 加载生产环境配置
2025-08-01 23:12:42,812 - Main - INFO - info:316 - ℹ️ 生产环境初始化完成
2025-08-01 23:12:42,814 - Main - INFO - info:316 - ℹ️ 🎯 获取特定分钟数据: 000617 @ 202507041447
2025-08-01 23:12:42,814 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-08-01 23:12:43,000 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 5040条 (目标日期: 20250704, 交易日: 21天, 频率: 1min)
2025-08-01 23:12:43,000 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计5040条 -> 实际请求800条 (配置限制:800)
2025-08-01 23:12:43,000 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 23:12:43,000 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 23:12:43,050 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4240条
2025-08-01 23:12:43,282 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 23:12:43,502 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 23:12:43,737 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 23:12:43,966 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 23:12:44,193 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 23:12:44,416 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +240条，总计5040条
2025-08-01 23:12:44,416 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计5040条数据
2025-08-01 23:12:44,417 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要5040条，实际获取5040条
2025-08-01 23:12:44,430 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-01 23:12:44,431 - Main - INFO - info:316 - ℹ️ 📊 获取到 240 条原始数据，查找目标时间 202507041447
2025-08-01 23:12:44,436 - Main - INFO - info:316 - ℹ️ ✅ 找到目标数据: 未复权收盘价=7.550
2025-08-01 23:12:44,436 - Main - INFO - info:316 - ℹ️ ✅ 增量下载前提条件验证通过
2025-08-01 23:12:44,436 - Main - INFO - info:316 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-08-01 23:12:44,436 - Main - INFO - info:316 - ℹ️ 🔍 开始检测000617的缺失数据
2025-08-01 23:12:44,465 - Main - INFO - info:316 - ℹ️ 📊 数据统计: 总记录数17211, 覆盖72个交易日
2025-08-01 23:12:44,465 - Main - WARNING - warning:320 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-08-01 23:12:44,465 - Main - WARNING - warning:320 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-08-01 23:12:44,465 - Main - WARNING - warning:320 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-08-01 23:12:44,465 - Main - INFO - info:316 - ℹ️ 🔧 开始修复000617的缺失数据
2025-08-01 23:12:44,465 - Main - INFO - info:316 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-08-01 23:12:44,465 - Main - INFO - info:316 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-08-01 23:12:44,465 - Main - INFO - info:316 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-08-01 23:12:44,465 - Main - INFO - info:316 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-08-01 23:12:44,465 - Main - INFO - info:316 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-08-01 23:12:44,465 - Main - INFO - info:316 - ℹ️ ✅ 缺失数据修复完成
2025-08-01 23:12:44,844 - Main - INFO - info:316 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-08-01 23:12:44,844 - Main - INFO - info:316 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-08-01 23:12:44,844 - Main - INFO - info:316 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-01 23:12:44,845 - Main - INFO - info:316 - ℹ️ 找到现有文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-01 23:12:44,845 - Main - INFO - info:316 - ℹ️ 📊 开始智能时间范围分析
2025-08-01 23:12:44,860 - Main - INFO - info:316 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-08-01 23:12:45,089 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-08-01 23:12:45,090 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 23:12:45,090 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 23:12:45,090 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-08-01 23:12:45,257 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 5040条 (目标日期: 20250704, 交易日: 21天, 频率: 1min)
2025-08-01 23:12:45,257 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计5040条 -> 实际请求800条 (配置限制:800)
2025-08-01 23:12:45,257 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 23:12:45,257 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 23:12:45,306 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4240条
2025-08-01 23:12:45,543 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 23:12:45,761 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 23:12:45,997 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 23:12:46,223 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 23:12:46,446 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 23:12:46,661 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +240条，总计5040条
2025-08-01 23:12:46,661 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计5040条数据
2025-08-01 23:12:46,663 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要5040条，实际获取5040条
2025-08-01 23:12:46,674 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-01 23:12:46,681 - Main - INFO - info:316 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-08-01 23:12:46,683 - Main - INFO - info:316 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-08-01 23:12:46,683 - Main - INFO - info:316 - ℹ️ 从文件名解析时间范围: 20250320 - 20250704
2025-08-01 23:12:46,683 - Main - INFO - info:316 - ℹ️ 需要下载早期数据: 20250101 - 20250319
2025-08-01 23:12:46,683 - Main - INFO - info:316 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-08-01 23:12:46,692 - Main - INFO - info:316 - ℹ️ 读取现有数据: 17211 条记录
2025-08-01 23:12:46,692 - Main - INFO - info:316 - ℹ️ 下载增量数据: 20250101 - 20250319
2025-08-01 23:12:46,692 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 23:12:46,692 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 23:12:46,692 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250319
2025-08-01 23:12:46,868 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 33600条 (目标日期: 20250101, 交易日: 140天, 频率: 1min)
2025-08-01 23:12:46,868 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计33600条 -> 实际请求800条 (配置限制:800)
2025-08-01 23:12:46,868 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 23:12:46,868 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 23:12:46,919 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32800条
2025-08-01 23:12:47,150 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 23:12:47,374 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 23:12:47,595 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 23:12:47,823 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 23:12:48,047 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 23:12:48,283 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-08-01 23:12:48,504 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-08-01 23:12:48,724 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-08-01 23:12:48,954 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-08-01 23:12:49,169 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-08-01 23:12:49,385 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-08-01 23:12:49,605 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-08-01 23:12:49,824 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-08-01 23:12:50,057 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-08-01 23:12:50,276 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-08-01 23:12:50,500 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-08-01 23:12:50,714 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-08-01 23:12:50,934 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-08-01 23:12:51,152 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-08-01 23:12:51,386 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-08-01 23:12:51,604 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-08-01 23:12:51,837 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-08-01 23:12:52,070 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-08-01 23:12:52,305 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-08-01 23:12:52,529 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-08-01 23:12:52,762 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-08-01 23:12:52,995 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-08-01 23:12:53,223 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +160条，总计22560条
2025-08-01 23:12:53,223 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计22560条数据
2025-08-01 23:12:53,228 - Main - WARNING - warning:320 - ⚠️ ⚠️ 数据覆盖不足: 需要33600条，实际获取22560条
2025-08-01 23:12:53,228 - Main - WARNING - warning:320 - ⚠️ ⚠️ 缺少约11040条数据（约46个交易日）
2025-08-01 23:12:53,228 - Main - WARNING - warning:320 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-08-
2025-08-01 23:12:53,228 - Main - WARNING - warning:320 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-08-01 23:12:53,228 - Main - WARNING - warning:320 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-08-01 23:12:53,291 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 480 条 1min 数据
2025-08-01 23:12:53,301 - Main - INFO - info:316 - ℹ️ 获得新数据: 480 条记录
2025-08-01 23:12:53,301 - Main - INFO - info:316 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-08-01 23:12:53,301 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 23:12:53,301 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 23:12:53,301 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-08-01 23:12:53,472 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 4800条 (目标日期: 20250705, 交易日: 20天, 频率: 1min)
2025-08-01 23:12:53,472 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计4800条 -> 实际请求800条 (配置限制:800)
2025-08-01 23:12:53,472 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 23:12:53,472 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 23:12:53,522 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4000条
2025-08-01 23:12:53,741 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 23:12:53,973 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 23:12:54,210 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 23:12:54,433 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 23:12:54,662 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 23:12:54,662 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计4800条数据
2025-08-01 23:12:54,663 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要4800条，实际获取4800条
2025-08-01 23:12:54,675 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-08-01 23:12:54,690 - Main - INFO - info:316 - ℹ️ 获得新数据: 3600 条记录
2025-08-01 23:12:54,693 - Main - INFO - info:316 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-08-01 23:12:54,701 - Main - INFO - info:316 - ℹ️ 合并后数据: 21291 条记录
2025-08-01 23:12:54,776 - Main - INFO - info:316 - ℹ️ 删除旧文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-01 23:12:54,776 - Main - INFO - info:316 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503180931-202507251500_来源互联网（202508012312）.txt
2025-08-01 23:12:54,777 - Main - INFO - info:316 - ℹ️ ✅ 智能增量下载完成
2025-08-01 23:12:54,777 - Main - INFO - info:316 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-08-01 23:12:54,777 - Main - INFO - info:316 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-08-01 23:12:54,777 - Main - INFO - info:316 - ℹ️ 开始执行前复权数据比较分析任务
2025-08-01 23:12:54,778 - Main - INFO - info:316 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-08-01 23:12:54,778 - Main - INFO - info:316 - ℹ️ 正在分析股票: 000617
2025-08-01 23:12:54,778 - Main - INFO - info:316 - ℹ️ 开始比较股票 000617 的前复权数据
2025-08-01 23:12:54,778 - Main - INFO - info:316 - ℹ️ 找到文件 - TDX: 未找到
2025-08-01 23:12:54,778 - Main - INFO - info:316 - ℹ️ 找到文件 - 互联网: 未找到
2025-08-01 23:12:54,778 - Main - WARNING - warning:320 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250801_231254.txt
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-08-01 23:12:54,779 - Main - ERROR - error:324 - ❌ 任务执行失败: 前复权数据比较分析
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 开始清理应用程序资源
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 开始清理股票处理器资源
2025-08-01 23:12:54,779 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 异步IO处理器资源清理完成
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 股票处理器资源清理完成
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 开始清理任务管理器资源
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 任务管理器资源清理完成
2025-08-01 23:12:54,779 - Main - INFO - info:316 - ℹ️ 应用程序资源清理完成
