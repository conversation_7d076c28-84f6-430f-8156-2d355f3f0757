#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境中的Workflow违规检测器
专门针对test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt文件
"""

import os
import sys
import importlib.util

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
test_env_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(os.path.dirname(test_env_dir))
sys.path.insert(0, project_root)

# 全局状态跟踪
workflow_violations = []
taskmanager_log_time = None
four_step_start_time = None

def setup_test_environment():
    """设置测试环境"""
    print("设置测试环境")
    print("=" * 80)
    
    # 切换工作目录到测试环境
    os.chdir(test_env_dir)
    print(f"工作目录切换到: {os.getcwd()}")

    # 确认目标测试文件存在
    target_file = "input_data/test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
    if os.path.exists(target_file):
        print(f"目标测试文件存在: {target_file}")

        # 检查文件基本信息
        file_size = os.path.getsize(target_file)
        with open(target_file, 'r', encoding='utf-8') as f:
            lines = sum(1 for _ in f)
        print(f"文件信息: {file_size} bytes, {lines} 行")

        return True
    else:
        print(f"目标测试文件不存在: {target_file}")
        return False

def install_workflow_monitoring():
    """安装workflow监控"""
    global workflow_violations, taskmanager_log_time, four_step_start_time
    
    try:
        # 1. 监控TaskManager的关键时间点
        from src.mythquant.core.task_manager import TaskManager
        
        original_execute_minute = TaskManager._execute_minute_task
        
        def monitored_execute_minute(self, task, target_stocks):
            """监控TaskManager执行"""
            global taskmanager_log_time
            import time
            
            print(f"\n🎯 TASKMANAGER EXECUTION START")
            print(f"Task: {task.name}")
            print(f"Target stocks: {target_stocks}")
            
            # 记录关键时间点
            taskmanager_log_time = time.time()
            print(f"⏰ TaskManager log time recorded: {taskmanager_log_time}")
            
            result = original_execute_minute(self, task, target_stocks)
            
            print(f"🎯 TASKMANAGER EXECUTION END")
            return result
        
        TaskManager._execute_minute_task = monitored_execute_minute
        
        # 2. 监控五步流程开始时间
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        original_five_step = StructuredInternetMinuteDownloader._execute_five_step_process
        
        def monitored_five_step(self, stock_code, start_date, end_date, frequency, original_frequency):
            """监控五步流程开始"""
            global four_step_start_time
            import time
            
            four_step_start_time = time.time()
            print(f"\n🚀 FIVE-STEP PROCESS START")
            print(f"Stock: {stock_code}")
            print(f"⏰ Five-step start time recorded: {four_step_start_time}")
            
            return original_five_step(self, stock_code, start_date, end_date, frequency, original_frequency)
        
        StructuredInternetMinuteDownloader._execute_five_step_process = monitored_five_step
        
        # 3. 监控所有pytdx相关调用
        from utils.pytdx_downloader import PytdxDownloader
        
        original_download = PytdxDownloader.download_minute_data
        
        def monitored_download(self, stock_code, start_date, end_date, frequency='1min', suppress_warnings=False):
            """监控pytdx下载调用"""
            global workflow_violations, taskmanager_log_time, four_step_start_time
            import time
            import traceback
            
            current_time = time.time()
            
            print(f"\n🔍 PYTDX DOWNLOAD CALLED")
            print(f"Stock: {stock_code}, Range: {start_date}-{end_date}")
            print(f"Current time: {current_time}")
            print(f"TaskManager log time: {taskmanager_log_time}")
            print(f"Five-step start time: {four_step_start_time}")
            
            # 检查是否违规
            is_violation = False
            violation_reason = ""
            
            if taskmanager_log_time and four_step_start_time:
                if taskmanager_log_time < current_time < four_step_start_time:
                    is_violation = True
                    violation_reason = "Called between TaskManager log and five-step start"
            elif taskmanager_log_time and not four_step_start_time:
                # 如果五步流程还没开始，但TaskManager已经记录了时间
                time_since_taskmanager = current_time - taskmanager_log_time
                if time_since_taskmanager > 0.1:  # 100ms后还没开始五步流程就算违规
                    is_violation = True
                    violation_reason = "Called after TaskManager log but before five-step process"
            
            if is_violation:
                print(f"🚨 WORKFLOW VIOLATION DETECTED!")
                print(f"Reason: {violation_reason}")
                
                # 记录违规详情
                stack = traceback.format_stack()
                violation = {
                    'stock_code': stock_code,
                    'date_range': f"{start_date}-{end_date}",
                    'frequency': frequency,
                    'suppress_warnings': suppress_warnings,
                    'violation_reason': violation_reason,
                    'call_time': current_time,
                    'taskmanager_time': taskmanager_log_time,
                    'five_step_time': four_step_start_time,
                    'stack': stack
                }
                workflow_violations.append(violation)
                
                print(f"Call stack:")
                for i, frame in enumerate(stack[-8:], 1):
                    if any(keyword in frame for keyword in ['task_manager', 'TaskManager']):
                        print(f">>> {i:2d}. {frame.strip()}")
                    else:
                        print(f"    {i:2d}. {frame.strip()}")
            else:
                print(f"✅ Normal pytdx call (no violation)")
            
            print("=" * 100)
            
            return original_download(self, stock_code, start_date, end_date, frequency, suppress_warnings)
        
        PytdxDownloader.download_minute_data = monitored_download
        
        # 4. 监控警告输出
        import builtins
        original_print = builtins.print
        
        def monitored_print(*args, **kwargs):
            """监控print输出中的pytdx警告"""
            message = ' '.join(str(arg) for arg in args)
            
            if any(keyword in message for keyword in ['时间范围内无数据', 'pytdx未获取到', 'pytdx不可用']):
                global taskmanager_log_time, four_step_start_time
                import time
                current_time = time.time()
                
                print(f"\n🚨 PYTDX WARNING DETECTED IN OUTPUT", file=sys.stderr)
                print(f"Warning: {message}", file=sys.stderr)
                print(f"Warning time: {current_time}", file=sys.stderr)
                print(f"TaskManager time: {taskmanager_log_time}", file=sys.stderr)
                print(f"Five-step time: {four_step_start_time}", file=sys.stderr)
                
                # 检查时机
                if taskmanager_log_time and four_step_start_time:
                    if taskmanager_log_time < current_time < four_step_start_time:
                        print(f"🚨 WARNING TIMING VIOLATION!", file=sys.stderr)
                elif taskmanager_log_time and not four_step_start_time:
                    print(f"🚨 WARNING BEFORE FIVE-STEP START!", file=sys.stderr)
                
                print("=" * 80, file=sys.stderr)
            
            return original_print(*args, **kwargs)
        
        builtins.print = monitored_print
        
        print("✅ Workflow monitoring installed successfully")
        return True
        
    except Exception as e:
        print(f"❌ Monitoring installation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_test_with_monitoring():
    """在测试环境中运行监控测试"""
    print("\n🧪 在测试环境中运行workflow违规检测")
    print("=" * 80)
    
    try:
        # 导入并运行main
        from main import main
        
        print("🚀 开始执行main函数...")
        result = main()
        
        print(f"\n✅ Main函数执行完成，结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 测试执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_violations():
    """分析检测到的违规"""
    global workflow_violations
    
    print(f"\n📊 WORKFLOW违规分析报告")
    print("=" * 80)
    
    if not workflow_violations:
        print("✅ 未检测到workflow违规")
        return
    
    print(f"🚨 检测到 {len(workflow_violations)} 个workflow违规")
    
    for i, violation in enumerate(workflow_violations, 1):
        print(f"\n违规 #{i}:")
        print(f"  股票代码: {violation['stock_code']}")
        print(f"  时间范围: {violation['date_range']}")
        print(f"  频率: {violation['frequency']}")
        print(f"  违规原因: {violation['violation_reason']}")
        print(f"  调用时间: {violation['call_time']}")
        print(f"  TaskManager时间: {violation['taskmanager_time']}")
        print(f"  五步流程时间: {violation['five_step_time']}")
        
        print(f"  关键调用栈:")
        for frame in violation['stack']:
            if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'structured_internet']):
                print(f"    >>> {frame.strip()}")

def main():
    """主函数"""
    print("测试环境Workflow违规检测器")
    print("=" * 80)
    print("目标文件: test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt")
    print("=" * 80)
    
    # 1. 设置测试环境
    if not setup_test_environment():
        return 1
    
    # 2. 安装监控
    if not install_workflow_monitoring():
        return 1
    
    # 3. 运行测试
    success = run_test_with_monitoring()
    
    # 4. 分析结果
    analyze_violations()
    
    print(f"\n🎯 检测完成: {'SUCCESS' if success else 'FAILED'}")
    print(f"违规数量: {len(workflow_violations)}")
    
    return 0 if success and len(workflow_violations) == 0 else 1

if __name__ == '__main__':
    sys.exit(main())
