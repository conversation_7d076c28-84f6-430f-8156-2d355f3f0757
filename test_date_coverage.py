#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试日期覆盖情况
"""

import sys
from pathlib import Path
import pandas as pd

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_date_coverage_with_real_data():
    """使用真实数据测试日期覆盖情况"""
    print("🔍 测试日期覆盖情况")
    print("=" * 50)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        downloader = PytdxDownloader()
        print("✅ PytdxDownloader创建成功")
        
        # 测试一个较长的时间范围，模拟数据覆盖不足的情况
        print("\n📊 测试较长时间范围的数据下载...")
        df = downloader.download_minute_data('000617', '20250101', '20250807', '1min')
        
        if df is None or df.empty:
            print("❌ 无法获取测试数据")
            return False
        
        print(f"✅ 获取到 {len(df)} 条数据")
        
        # 检查实际的时间范围
        if 'datetime' in df.columns:
            earliest = df['datetime'].min()
            latest = df['datetime'].max()
            
            print(f"📅 实际数据时间范围:")
            print(f"   最早: {earliest}")
            print(f"   最晚: {latest}")
            
            # 转换为YYYYMMDD格式
            earliest_date = earliest.strftime('%Y%m%d')
            latest_date = latest.strftime('%Y%m%d')
            
            print(f"📅 转换后的日期格式:")
            print(f"   最早: {earliest_date}")
            print(f"   最晚: {latest_date}")
        
        # 测试_check_actual_data_coverage方法
        print(f"\n🔍 测试_check_actual_data_coverage方法...")
        coverage = downloader._check_actual_data_coverage(df, '20250101')
        
        print(f"📊 覆盖情况分析:")
        print(f"   covers_target: {coverage.get('covers_target')}")
        print(f"   earliest_date: '{coverage.get('earliest_date')}' (长度: {len(str(coverage.get('earliest_date', '')))})")
        print(f"   latest_date: '{coverage.get('latest_date')}' (长度: {len(str(coverage.get('latest_date', '')))})")
        print(f"   reason: {coverage.get('reason')}")
        
        # 检查是否有格式问题
        earliest = coverage.get('earliest_date')
        latest = coverage.get('latest_date')
        
        if earliest and not (len(str(earliest)) == 8 and str(earliest).isdigit()):
            print(f"❌ earliest_date格式错误: '{earliest}'")
        
        if latest and not (len(str(latest)) == 8 and str(latest).isdigit()):
            print(f"❌ latest_date格式错误: '{latest}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_problematic_datetime_formats():
    """测试可能有问题的datetime格式"""
    print("\n🔍 测试可能有问题的datetime格式")
    print("=" * 50)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        downloader = PytdxDownloader()
        
        # 创建一些可能有问题的测试数据
        test_data = pd.DataFrame({
            'datetime': [
                pd.Timestamp('2025-03-20 09:30:00'),
                pd.Timestamp('2025-08-06 15:00:00'),
                '2025-07-',  # 可能的问题格式
                '2025-08-',  # 可能的问题格式
                pd.Timestamp('2025-07-25 14:30:00'),
            ],
            'close': [10.0, 11.0, 12.0, 13.0, 14.0]
        })
        
        print(f"📊 测试数据:")
        for i, dt in enumerate(test_data['datetime']):
            print(f"   {i+1}: {dt} (类型: {type(dt)})")
        
        # 测试覆盖情况分析
        coverage = downloader._check_actual_data_coverage(test_data, '20250320')
        
        print(f"\n📊 覆盖情况分析:")
        print(f"   covers_target: {coverage.get('covers_target')}")
        print(f"   earliest_date: '{coverage.get('earliest_date')}' (长度: {len(str(coverage.get('earliest_date', '')))})")
        print(f"   latest_date: '{coverage.get('latest_date')}' (长度: {len(str(coverage.get('latest_date', '')))})")
        print(f"   reason: {coverage.get('reason')}")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 日期覆盖测试")
    print("=" * 60)
    
    # 测试真实数据
    real_data_ok = test_date_coverage_with_real_data()
    
    # 测试问题格式
    problem_format_ok = test_problematic_datetime_formats()
    
    print("\n" + "=" * 60)
    print("🔍 测试结果总结")
    print("=" * 60)
    print(f"真实数据测试: {'✅' if real_data_ok else '❌'}")
    print(f"问题格式测试: {'✅' if problem_format_ok else '❌'}")
    
    if real_data_ok and problem_format_ok:
        print("\n💡 结论：日期格式处理已修复")
        print("   现在应该能正确显示具体的日期而不是只到月份")

if __name__ == '__main__':
    main()
