#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理配置实体

包含数据处理相关的配置信息
"""

from dataclasses import dataclass
from typing import Dict, Any, Optional


@dataclass
class ProcessingConfig:
    """数据处理配置实体"""
    
    # 智能文件选择器配置
    smart_file_selector: Dict[str, Any]
    
    # 数据处理配置
    data_processing: Dict[str, Any]
    
    # 智能功能配置
    intelligent_features: Dict[str, Any]
    
    # 用户界面配置
    user_interface: Dict[str, Any]
    
    # 详细模式配置
    verbose_mode: Optional[Dict[str, Any]] = None
    
    # 调试配置
    debug_enabled: bool = False
    
    def __post_init__(self):
        """初始化后处理"""
        # 设置默认智能文件选择器配置
        if not self.smart_file_selector:
            self.smart_file_selector = {
                'enabled': True,
                'default_strategy': 'smart_comprehensive',
                'scoring_weights': {
                    'freshness_weight': 0.3,
                    'coverage_weight': 0.4,
                    'match_weight': 0.3,
                },
                'conflict_resolution': {
                    'auto_resolve': True,
                    'prefer_newer': True,
                    'min_score_threshold': 0.6,
                }
            }
        
        # 设置默认数据处理配置
        if not self.data_processing:
            self.data_processing = {
                'transparent_processing': {
                    'enabled': True,
                    'auto_backup': True,
                    'validation_enabled': True,
                },
                'quality_verification': {
                    'enabled': True,
                    'strict_mode': False,
                    'auto_fix': True,
                }
            }
        
        # 设置默认智能功能配置
        if not self.intelligent_features:
            self.intelligent_features = {
                'incremental_download': {
                    'enabled': True,
                    'auto_detect': True,
                    'validation_enabled': True,
                },
                'missing_data_processor': {
                    'enabled': True,
                    'auto_fill': False,
                    'notification_enabled': True,
                }
            }
        
        # 设置默认用户界面配置
        if not self.user_interface:
            self.user_interface = {
                'display_level': 'normal',
                'progress_bar': True,
                'color_output': True,
                'emoji_enabled': True,
            }
        
        # 设置默认详细模式配置
        if not self.verbose_mode:
            self.verbose_mode = {
                'enabled': False,
                'show_forward_adj_details': False,
                'show_performance_warnings': False,
                'show_data_processing_steps': False,
                'show_cache_status': False,
                'highlight_critical_info': False,
                'show_detailed_calculations': False,
            }
    
    def validate(self) -> bool:
        """验证配置的业务规则"""
        try:
            # 验证智能文件选择器配置
            if not self._validate_smart_file_selector():
                return False
            
            # 验证数据处理配置
            if not self._validate_data_processing():
                return False
            
            # 验证智能功能配置
            if not self._validate_intelligent_features():
                return False
            
            # 验证用户界面配置
            if not self._validate_user_interface():
                return False
            
            return True
            
        except Exception:
            return False
    
    def _validate_smart_file_selector(self) -> bool:
        """验证智能文件选择器配置"""
        if not isinstance(self.smart_file_selector.get('enabled'), bool):
            return False
        
        # 验证评分权重
        weights = self.smart_file_selector.get('scoring_weights', {})
        if weights:
            total_weight = sum(weights.values())
            if not (0.9 <= total_weight <= 1.1):  # 允许小的浮点误差
                return False
        
        return True
    
    def _validate_data_processing(self) -> bool:
        """验证数据处理配置"""
        transparent = self.data_processing.get('transparent_processing', {})
        if not isinstance(transparent.get('enabled'), bool):
            return False
        
        quality = self.data_processing.get('quality_verification', {})
        if not isinstance(quality.get('enabled'), bool):
            return False
        
        return True
    
    def _validate_intelligent_features(self) -> bool:
        """验证智能功能配置"""
        incremental = self.intelligent_features.get('incremental_download', {})
        if not isinstance(incremental.get('enabled'), bool):
            return False
        
        missing_data = self.intelligent_features.get('missing_data_processor', {})
        if not isinstance(missing_data.get('enabled'), bool):
            return False
        
        return True
    
    def _validate_user_interface(self) -> bool:
        """验证用户界面配置"""
        display_level = self.user_interface.get('display_level')
        if display_level not in ['minimal', 'normal', 'detailed']:
            return False
        
        return True
    
    # 智能文件选择器相关方法
    def is_smart_file_selector_enabled(self) -> bool:
        """是否启用智能文件选择器"""
        return self.smart_file_selector.get('enabled', True)
    
    def get_file_selector_strategy(self) -> str:
        """获取文件选择策略"""
        return self.smart_file_selector.get('default_strategy', 'smart_comprehensive')
    
    def get_scoring_weights(self) -> Dict[str, float]:
        """获取评分权重"""
        return self.smart_file_selector.get('scoring_weights', {})
    
    # 数据处理相关方法
    def is_transparent_processing_enabled(self) -> bool:
        """是否启用透明数据处理"""
        transparent = self.data_processing.get('transparent_processing', {})
        return transparent.get('enabled', True)
    
    def is_auto_backup_enabled(self) -> bool:
        """是否启用自动备份"""
        transparent = self.data_processing.get('transparent_processing', {})
        return transparent.get('auto_backup', True)
    
    def is_quality_verification_enabled(self) -> bool:
        """是否启用数据质量验证"""
        quality = self.data_processing.get('quality_verification', {})
        return quality.get('enabled', True)
    
    # 智能功能相关方法
    def is_incremental_download_enabled(self) -> bool:
        """是否启用增量下载"""
        incremental = self.intelligent_features.get('incremental_download', {})
        return incremental.get('enabled', True)
    
    def is_missing_data_processor_enabled(self) -> bool:
        """是否启用缺失数据处理"""
        missing_data = self.intelligent_features.get('missing_data_processor', {})
        return missing_data.get('enabled', True)
    
    # 用户界面相关方法
    def get_display_level(self) -> str:
        """获取显示级别"""
        return self.user_interface.get('display_level', 'normal')
    
    def is_progress_bar_enabled(self) -> bool:
        """是否启用进度条"""
        return self.user_interface.get('progress_bar', True)
    
    def is_emoji_enabled(self) -> bool:
        """是否启用表情符号"""
        return self.user_interface.get('emoji_enabled', True)
    
    # 详细模式相关方法
    def is_verbose_mode_enabled(self) -> bool:
        """是否启用详细模式"""
        if not self.verbose_mode:
            return False
        return self.verbose_mode.get('enabled', False)
    
    def should_show_forward_adj_details(self) -> bool:
        """是否显示前复权处理详情"""
        if not self.verbose_mode:
            return False
        return self.verbose_mode.get('show_forward_adj_details', False)
    
    def __str__(self) -> str:
        return f"ProcessingConfig(smart_selector={self.is_smart_file_selector_enabled()}, debug={self.debug_enabled})"
