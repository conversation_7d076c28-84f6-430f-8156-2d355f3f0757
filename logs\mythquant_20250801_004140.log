2025-08-01 00:41:40,876 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250801_004140.log
2025-08-01 00:41:40,876 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-08-01 00:41:40,877 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-08-01 00:41:40,877 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-08-01 00:41:40,877 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-08-01 00:41:40,877 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-08-01 00:41:40,877 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-08-01 00:41:40,877 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-08-01 00:41:40,877 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-08-01 00:41:40,910 - Main - INFO - info:307 - ℹ️ 🎯 获取特定分钟数据: 000617 @ 202507251500
2025-08-01 00:41:40,910 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-08-01 00:41:41,095 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1440条 (目标日期: 20250725, 交易日: 6天, 频率: 1min)
2025-08-01 00:41:41,095 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1440条 -> 实际请求800条 (配置限制:800)
2025-08-01 00:41:41,095 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 00:41:41,095 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 00:41:41,145 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需640条
2025-08-01 00:41:41,370 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +640条，总计1440条
2025-08-01 00:41:41,370 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1440条数据
2025-08-01 00:41:41,371 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1440条，实际获取1440条
2025-08-01 00:41:41,376 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-01 00:41:41,379 - Main - INFO - info:307 - ℹ️ ℹ️ pytdx提供未复权原始价格，正在计算前复权价格以完善数据字段...
2025-08-01 00:41:41,379 - Main - INFO - info:307 - ℹ️ 🔄 pytdx前复权计算: 000617
2025-08-01 00:41:41,379 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-08-01 00:41:41,379 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-08-01 00:41:41,826 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-08-01 00:41:41,826 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-08-01 00:41:41,833 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-08-01 00:41:41,833 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17/T0002
2025-08-01 00:41:41,833 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-08-01 00:41:41,833 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-08-01 00:41:41,834 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 未找到任何市场的分钟数据文件
2025-08-01 00:41:41,834 - main_v20230219_optimized - WARNING - _get_optimal_tdx_path:255 - ⚠️  主路径下未找到分钟数据文件，但将继续使用配置路径
2025-08-01 00:41:41,834 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17/T0002，缓存方法: memory
