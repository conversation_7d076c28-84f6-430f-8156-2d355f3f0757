{"timestamp": "2025-08-10T02:15:44.583184", "error_statistics": {"error_counts": {"SYSTEM": 1}, "total_errors": 1, "recent_errors": [{"error_id": "SYSTEM_1754763337252", "timestamp": "2025-08-10T02:15:37.252809", "category": "SYSTEM", "error_type": "ValueError", "error_message": "The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().", "stock_code": null, "operation": "基于pytdx特性的数据修复", "context": {"file_path": "H:/MPV1.17/T0002/signals\\1min_0_000617_timerange.txt", "stock_code": "000617"}, "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\pytdx_data_repairer.py\", line 78, in repair_missing_data_with_full_download\n    if not full_data:\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\.venv\\lib\\site-packages\\pandas\\core\\generic.py\", line 1577, in __nonzero__\n    raise ValueError(\nValueError: The truth value of a DataFrame is ambiguous. Use a.empty, a.bool(), a.item(), a.any() or a.all().\n"}], "error_history_size": 1}, "performance_statistics": {}}