#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标计算模块

提供常用技术指标的计算功能，包括移动平均线、RSI、MACD等
"""

import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Dict, List, Tuple
import logging

# 导入新架构的配置系统
from src.mythquant.config.manager import ConfigManager

logger = logging.getLogger(__name__)


class TechnicalIndicatorCalculator:
    """技术指标计算器 - 提供常用技术指标计算"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化技术指标计算器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.precision = Decimal('0.001')  # 价格精度：3位小数
        
        # 调试模式
        self.debug_mode = config_manager.is_debug_enabled()
    
    def calculate_moving_averages(self, df: pd.DataFrame, periods: List[int] = None) -> pd.DataFrame:
        """
        计算移动平均线
        
        Args:
            df: 包含价格数据的DataFrame
            periods: 移动平均周期列表，默认为[5, 10, 20, 60]
            
        Returns:
            包含移动平均线的DataFrame
        """
        if df is None or df.empty:
            logger.warning("输入数据为空，无法计算移动平均线")
            return pd.DataFrame()
        
        if periods is None:
            periods = [5, 10, 20, 60]
        
        try:
            result_df = df.copy()
            
            # 验证必要列存在
            if 'close' not in result_df.columns:
                logger.error("缺少收盘价列，无法计算移动平均线")
                return result_df
            
            # 计算各周期移动平均线
            for period in periods:
                if period > 0:
                    ma_column = f'ma_{period}'
                    result_df[ma_column] = result_df['close'].rolling(
                        window=period, min_periods=1
                    ).mean()
                    
                    # 应用精度控制
                    result_df[ma_column] = result_df[ma_column].apply(
                        lambda x: self._apply_decimal_precision(x)
                    )
            
            logger.info(f"移动平均线计算完成，周期: {periods}")
            return result_df
            
        except Exception as e:
            logger.error(f"计算移动平均线失败: {e}")
            return df
    
    def calculate_rsi(self, df: pd.DataFrame, period: int = 14) -> pd.DataFrame:
        """
        计算相对强弱指标(RSI)
        
        Args:
            df: 包含价格数据的DataFrame
            period: RSI计算周期，默认14
            
        Returns:
            包含RSI的DataFrame
        """
        if df is None or df.empty:
            logger.warning("输入数据为空，无法计算RSI")
            return pd.DataFrame()
        
        try:
            result_df = df.copy()
            
            # 验证必要列存在
            if 'close' not in result_df.columns:
                logger.error("缺少收盘价列，无法计算RSI")
                return result_df
            
            # 计算价格变化
            price_change = result_df['close'].diff()
            
            # 分离上涨和下跌
            gains = price_change.where(price_change > 0, 0)
            losses = -price_change.where(price_change < 0, 0)
            
            # 计算平均收益和平均损失
            avg_gains = gains.rolling(window=period, min_periods=1).mean()
            avg_losses = losses.rolling(window=period, min_periods=1).mean()
            
            # 计算RSI
            rs = avg_gains / (avg_losses + 1e-10)  # 避免除零
            result_df['rsi'] = 100 - (100 / (1 + rs))
            
            # 应用精度控制
            result_df['rsi'] = result_df['rsi'].apply(
                lambda x: self._apply_decimal_precision(x)
            )
            
            logger.info(f"RSI计算完成，周期: {period}")
            return result_df
            
        except Exception as e:
            logger.error(f"计算RSI失败: {e}")
            return df
    
    def calculate_macd(self, df: pd.DataFrame, fast_period: int = 12, 
                      slow_period: int = 26, signal_period: int = 9) -> pd.DataFrame:
        """
        计算MACD指标
        
        Args:
            df: 包含价格数据的DataFrame
            fast_period: 快线周期，默认12
            slow_period: 慢线周期，默认26
            signal_period: 信号线周期，默认9
            
        Returns:
            包含MACD的DataFrame
        """
        if df is None or df.empty:
            logger.warning("输入数据为空，无法计算MACD")
            return pd.DataFrame()
        
        try:
            result_df = df.copy()
            
            # 验证必要列存在
            if 'close' not in result_df.columns:
                logger.error("缺少收盘价列，无法计算MACD")
                return result_df
            
            # 计算指数移动平均线
            ema_fast = result_df['close'].ewm(span=fast_period).mean()
            ema_slow = result_df['close'].ewm(span=slow_period).mean()
            
            # 计算MACD线
            result_df['macd'] = ema_fast - ema_slow
            
            # 计算信号线
            result_df['macd_signal'] = result_df['macd'].ewm(span=signal_period).mean()
            
            # 计算MACD柱状图
            result_df['macd_histogram'] = result_df['macd'] - result_df['macd_signal']
            
            # 应用精度控制
            for col in ['macd', 'macd_signal', 'macd_histogram']:
                result_df[col] = result_df[col].apply(
                    lambda x: self._apply_decimal_precision(x)
                )
            
            logger.info(f"MACD计算完成，参数: {fast_period}, {slow_period}, {signal_period}")
            return result_df
            
        except Exception as e:
            logger.error(f"计算MACD失败: {e}")
            return df
    
    def calculate_bollinger_bands(self, df: pd.DataFrame, period: int = 20, 
                                 std_dev: float = 2.0) -> pd.DataFrame:
        """
        计算布林带
        
        Args:
            df: 包含价格数据的DataFrame
            period: 移动平均周期，默认20
            std_dev: 标准差倍数，默认2.0
            
        Returns:
            包含布林带的DataFrame
        """
        if df is None or df.empty:
            logger.warning("输入数据为空，无法计算布林带")
            return pd.DataFrame()
        
        try:
            result_df = df.copy()
            
            # 验证必要列存在
            if 'close' not in result_df.columns:
                logger.error("缺少收盘价列，无法计算布林带")
                return result_df
            
            # 计算中轨（移动平均线）
            result_df['bb_middle'] = result_df['close'].rolling(
                window=period, min_periods=1
            ).mean()
            
            # 计算标准差
            rolling_std = result_df['close'].rolling(
                window=period, min_periods=1
            ).std()
            
            # 计算上轨和下轨
            result_df['bb_upper'] = result_df['bb_middle'] + (rolling_std * std_dev)
            result_df['bb_lower'] = result_df['bb_middle'] - (rolling_std * std_dev)
            
            # 计算布林带宽度
            result_df['bb_width'] = result_df['bb_upper'] - result_df['bb_lower']
            
            # 计算价格在布林带中的位置
            result_df['bb_position'] = (result_df['close'] - result_df['bb_lower']) / (
                result_df['bb_width'] + 1e-10
            )
            
            # 应用精度控制
            bb_columns = ['bb_middle', 'bb_upper', 'bb_lower', 'bb_width', 'bb_position']
            for col in bb_columns:
                result_df[col] = result_df[col].apply(
                    lambda x: self._apply_decimal_precision(x)
                )
            
            logger.info(f"布林带计算完成，周期: {period}, 标准差: {std_dev}")
            return result_df
            
        except Exception as e:
            logger.error(f"计算布林带失败: {e}")
            return df
    
    def calculate_volume_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算成交量指标
        
        Args:
            df: 包含价格和成交量数据的DataFrame
            
        Returns:
            包含成交量指标的DataFrame
        """
        if df is None or df.empty:
            logger.warning("输入数据为空，无法计算成交量指标")
            return pd.DataFrame()
        
        try:
            result_df = df.copy()
            
            # 验证必要列存在
            required_columns = ['close', 'volume']
            missing_columns = [col for col in required_columns if col not in result_df.columns]
            if missing_columns:
                logger.error(f"缺少必要列: {missing_columns}")
                return result_df
            
            # 计算成交量移动平均
            result_df['volume_ma5'] = result_df['volume'].rolling(window=5, min_periods=1).mean()
            result_df['volume_ma10'] = result_df['volume'].rolling(window=10, min_periods=1).mean()
            
            # 计算成交量比率
            result_df['volume_ratio'] = result_df['volume'] / (result_df['volume_ma5'] + 1e-10)
            
            # 计算价量关系
            price_change = result_df['close'].pct_change()
            volume_change = result_df['volume'].pct_change()
            
            # 价量背离指标
            result_df['price_volume_divergence'] = np.where(
                (price_change > 0) & (volume_change < 0), -1,  # 价涨量缩
                np.where(
                    (price_change < 0) & (volume_change > 0), 1,  # 价跌量增
                    0  # 正常
                )
            )
            
            # OBV (On Balance Volume)
            obv = []
            obv_value = 0
            for i, row in result_df.iterrows():
                if i == 0:
                    obv_value = row['volume']
                else:
                    prev_close = result_df.iloc[i-1]['close']
                    if row['close'] > prev_close:
                        obv_value += row['volume']
                    elif row['close'] < prev_close:
                        obv_value -= row['volume']
                obv.append(obv_value)
            
            result_df['obv'] = obv
            
            logger.info("成交量指标计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算成交量指标失败: {e}")
            return df
    
    def _apply_decimal_precision(self, value: float) -> float:
        """
        应用Decimal精度控制
        
        Args:
            value: 输入值
            
        Returns:
            精度控制后的值
        """
        try:
            if pd.isna(value):
                return value
            decimal_value = Decimal(str(value))
            return float(decimal_value.quantize(self.precision, rounding=ROUND_HALF_UP))
        except Exception:
            return float(value)
    
    def calculate_all_indicators(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算所有技术指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            包含所有技术指标的DataFrame
        """
        if df is None or df.empty:
            logger.warning("输入数据为空，无法计算技术指标")
            return pd.DataFrame()
        
        try:
            result_df = df.copy()
            
            # 计算移动平均线
            result_df = self.calculate_moving_averages(result_df)
            
            # 计算RSI
            result_df = self.calculate_rsi(result_df)
            
            # 计算MACD
            result_df = self.calculate_macd(result_df)
            
            # 计算布林带
            result_df = self.calculate_bollinger_bands(result_df)
            
            # 计算成交量指标
            result_df = self.calculate_volume_indicators(result_df)
            
            logger.info("所有技术指标计算完成")
            return result_df
            
        except Exception as e:
            logger.error(f"计算所有技术指标失败: {e}")
            return df


# 导出
__all__ = ['TechnicalIndicatorCalculator']
