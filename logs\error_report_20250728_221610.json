{"timestamp": "2025-07-28T22:16:10.308650", "error_statistics": {"error_counts": {"BUSINESS": 3}, "total_errors": 3, "recent_errors": [{"error_id": "BUSINESS_1753712170301", "timestamp": "2025-07-28T22:16:10.301884", "category": "BUSINESS", "error_type": "RecursionError", "error_message": "maximum recursion depth exceeded", "stock_code": null, "operation": "任务执行", "context": {"task_name": "TDX日线级别数据生成", "task_type": "daily"}, "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\task_manager.py\", line 146, in execute_task\n    target_stocks = self.stock_processor.get_target_stocks()\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 158, in get_target_stocks\n    return getattr(self._processor, 'target_stocks', None)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  [Previous line repeated 992 more times]\nRecursionError: maximum recursion depth exceeded\n"}, {"error_id": "BUSINESS_1753712170303", "timestamp": "2025-07-28T22:16:10.303884", "category": "BUSINESS", "error_type": "RecursionError", "error_message": "maximum recursion depth exceeded", "stock_code": null, "operation": "任务执行", "context": {"task_name": "互联网分钟级数据下载", "task_type": "internet_minute"}, "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\task_manager.py\", line 146, in execute_task\n    target_stocks = self.stock_processor.get_target_stocks()\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 158, in get_target_stocks\n    return getattr(self._processor, 'target_stocks', None)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  [Previous line repeated 992 more times]\nRecursionError: maximum recursion depth exceeded\n"}, {"error_id": "BUSINESS_1753712170305", "timestamp": "2025-07-28T22:16:10.305884", "category": "BUSINESS", "error_type": "RecursionError", "error_message": "maximum recursion depth exceeded", "stock_code": null, "operation": "任务执行", "context": {"task_name": "前复权数据比较分析", "task_type": "data_comparison"}, "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\task_manager.py\", line 146, in execute_task\n    target_stocks = self.stock_processor.get_target_stocks()\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 158, in get_target_stocks\n    return getattr(self._processor, 'target_stocks', None)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\stock_processor.py\", line 307, in __getattr__\n    return getattr(self._processor, name)\n  [Previous line repeated 992 more times]\nRecursionError: maximum recursion depth exceeded\n"}], "error_history_size": 3}, "performance_statistics": {}}