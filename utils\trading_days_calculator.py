#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
A股交易日计算工具
提供准确的A股交易日计算功能，考虑节假日和周末
"""

from datetime import datetime, timedelta
from typing import List, Tuple, Dict, Set
import os
import sys

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from core.logging_service import logging_service


class TradingDaysCalculator:
    """A股交易日计算器"""
    
    def __init__(self):
        """初始化交易日计算器"""
        self.smart_logger = logging_service
        
        # A股节假日数据（需要定期更新）
        self.holidays = {
            '2025': [
                # 元旦
                '20250101',
                # 春节
                '20250128', '20250129', '20250130', '20250131', 
                '20250203', '20250204', '20250205',
                # 清明节
                '20250404', '20250405', '20250406',
                # 劳动节
                '20250501', '20250502', '20250503', '20250504', '20250505',
                # 端午节
                '20250609',
                # 中秋节
                '20251006', '20251007', '20251008',
                # 国庆节
                '20251001', '20251002', '20251003', '20251004', '20251005'
            ],
            '2024': [
                # 2024年节假日（用于历史计算）
                '20240101',  # 元旦
                '20240210', '20240211', '20240212', '20240213', '20240214', '20240215', '20240216', '20240217',  # 春节
                '20240404', '20240405', '20240406',  # 清明节
                '20240501', '20240502', '20240503',  # 劳动节
                '20240610',  # 端午节
                '20240915', '20240916', '20240917',  # 中秋节
                '20241001', '20241002', '20241003', '20241004', '20241005', '20241006', '20241007'  # 国庆节
            ]
        }
    
    def is_trading_day(self, date_str: str) -> bool:
        """
        判断指定日期是否为交易日
        
        Args:
            date_str: 日期字符串 YYYYMMDD
            
        Returns:
            True if 是交易日
        """
        try:
            date_obj = datetime.strptime(date_str, '%Y%m%d')
            
            # 检查是否为周末
            if date_obj.weekday() >= 5:  # 周六=5, 周日=6
                return False
            
            # 检查是否为节假日
            year = str(date_obj.year)
            if year in self.holidays and date_str in self.holidays[year]:
                return False
            
            return True
            
        except Exception as e:
            self.smart_logger.log_error(f"判断交易日失败 {date_str}: {e}")
            return False
    
    def count_trading_days(self, start_date: str, end_date: str) -> int:
        """
        计算指定日期范围内的交易日数量
        
        Args:
            start_date: 开始日期 YYYYMMDD
            end_date: 结束日期 YYYYMMDD
            
        Returns:
            交易日数量
        """
        try:
            start_dt = datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.strptime(end_date, '%Y%m%d')
            
            if start_dt > end_dt:
                start_dt, end_dt = end_dt, start_dt
            
            trading_days = 0
            current_dt = start_dt
            
            while current_dt <= end_dt:
                date_str = current_dt.strftime('%Y%m%d')
                if self.is_trading_day(date_str):
                    trading_days += 1
                current_dt += timedelta(days=1)
            
            return trading_days
            
        except Exception as e:
            self.smart_logger.log_error(f"计算交易日数量失败: {e}")
            return 0
    
    def count_trading_days_to_now(self, start_date: str) -> int:
        """
        计算从指定日期到现在的交易日数量
        
        Args:
            start_date: 开始日期 YYYYMMDD
            
        Returns:
            交易日数量
        """
        end_date = datetime.now().strftime('%Y%m%d')
        return self.count_trading_days(start_date, end_date)
    
    def get_trading_days_list(self, start_date: str, end_date: str) -> List[str]:
        """
        获取指定日期范围内的交易日列表
        
        Args:
            start_date: 开始日期 YYYYMMDD
            end_date: 结束日期 YYYYMMDD
            
        Returns:
            交易日列表
        """
        try:
            start_dt = datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.strptime(end_date, '%Y%m%d')
            
            if start_dt > end_dt:
                start_dt, end_dt = end_dt, start_dt
            
            trading_days = []
            current_dt = start_dt
            
            while current_dt <= end_dt:
                date_str = current_dt.strftime('%Y%m%d')
                if self.is_trading_day(date_str):
                    trading_days.append(date_str)
                current_dt += timedelta(days=1)
            
            return trading_days
            
        except Exception as e:
            self.smart_logger.log_error(f"获取交易日列表失败: {e}")
            return []
    
    def get_recent_trading_days(self, count: int) -> List[str]:
        """
        获取最近N个交易日
        
        Args:
            count: 需要的交易日数量
            
        Returns:
            最近的交易日列表（倒序，最新的在前）
        """
        try:
            trading_days = []
            current_dt = datetime.now()
            
            while len(trading_days) < count:
                date_str = current_dt.strftime('%Y%m%d')
                if self.is_trading_day(date_str):
                    trading_days.append(date_str)
                current_dt -= timedelta(days=1)
            
            return trading_days
            
        except Exception as e:
            self.smart_logger.log_error(f"获取最近交易日失败: {e}")
            return []
    
    def calculate_data_count_needed(self, start_date: str, frequency: str = '1min') -> int:
        """
        计算从指定日期到现在需要下载的数据条数
        
        Args:
            start_date: 开始日期 YYYYMMDD
            frequency: 数据频率 ('1min', '5min', '15min', '30min', '60min')
            
        Returns:
            需要的数据条数
        """
        try:
            # 计算交易日数量
            trading_days = self.count_trading_days_to_now(start_date)
            
            # 根据频率计算每日数据条数
            # A股交易时间：上午9:30-11:30，下午13:00-15:00，共4小时=240分钟
            if frequency == '1min':
                bars_per_day = 240  # 1分钟数据
            elif frequency == '5min':
                bars_per_day = 48   # 5分钟数据
            elif frequency == '15min':
                bars_per_day = 16   # 15分钟数据
            elif frequency == '30min':
                bars_per_day = 8    # 30分钟数据
            elif frequency == '60min':
                bars_per_day = 4    # 60分钟数据
            else:
                bars_per_day = 240  # 默认1分钟数据
            
            # 计算总需求量
            total_count = trading_days * bars_per_day

            # 使用verbose_log记录重要计算信息
            self.smart_logger.verbose_log('info', f"数据量计算: {start_date}到现在 = {trading_days}个交易日 × {bars_per_day}条/日 = {total_count}条")

            return total_count
            
        except Exception as e:
            self.smart_logger.log_error(f"计算数据条数失败: {e}")
            return 0
    
    def add_holiday(self, year: str, holiday_date: str):
        """
        添加节假日
        
        Args:
            year: 年份字符串
            holiday_date: 节假日日期 YYYYMMDD
        """
        if year not in self.holidays:
            self.holidays[year] = []
        
        if holiday_date not in self.holidays[year]:
            self.holidays[year].append(holiday_date)
            self.smart_logger.verbose_log('info', f"添加节假日: {holiday_date}")


# 创建全局实例
trading_days_calculator = TradingDaysCalculator()


def count_trading_days_to_now(start_date: str) -> int:
    """
    便捷函数：计算从指定日期到现在的交易日数量
    
    Args:
        start_date: 开始日期 YYYYMMDD
        
    Returns:
        交易日数量
    """
    return trading_days_calculator.count_trading_days_to_now(start_date)


def calculate_data_count_needed(start_date: str, frequency: str = '1min') -> int:
    """
    便捷函数：计算从指定日期到现在需要下载的数据条数
    
    Args:
        start_date: 开始日期 YYYYMMDD
        frequency: 数据频率
        
    Returns:
        需要的数据条数
    """
    return trading_days_calculator.calculate_data_count_needed(start_date, frequency)


if __name__ == '__main__':
    # 测试功能
    calculator = TradingDaysCalculator()
    
    print("🧪 测试A股交易日计算器")
    print("=" * 50)
    
    # 测试用例
    test_cases = [
        ("20250303", "3月3日到现在"),
        ("20250101", "1月1日到现在"),
        ("20250701", "7月1日到现在"),
    ]
    
    for start_date, description in test_cases:
        trading_days = calculator.count_trading_days_to_now(start_date)
        data_count = calculator.calculate_data_count_needed(start_date, '1min')
        
        print(f"\n📅 {description}:")
        print(f"  交易日数量: {trading_days} 天")
        print(f"  1分钟数据: {data_count} 条")
        print(f"  验算: {trading_days} × 240 = {trading_days * 240}")
