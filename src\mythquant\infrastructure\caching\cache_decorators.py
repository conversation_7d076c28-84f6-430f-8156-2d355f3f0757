#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存装饰器实现

提供方便的缓存装饰器，简化缓存使用
"""

import functools
import logging
import time
from typing import Any, Optional, Callable, Union, Dict, List
import inspect
import hashlib
import json

from .cache_manager import get_default_cache, CacheManager, CacheKey
from ...domain.exceptions import DomainException

logger = logging.getLogger(__name__)


class CacheDecoratorException(DomainException):
    """缓存装饰器异常"""
    pass


def cached(ttl: Optional[int] = None,
          key_prefix: Optional[str] = None,
          cache_manager: Optional[CacheManager] = None,
          ignore_args: Optional[List[str]] = None,
          condition: Optional[Callable] = None,
          serializer: Optional[Callable] = None,
          deserializer: Optional[Callable] = None):
    """
    缓存装饰器
    
    Args:
        ttl: 缓存过期时间（秒）
        key_prefix: 缓存键前缀
        cache_manager: 缓存管理器
        ignore_args: 忽略的参数名列表
        condition: 缓存条件函数
        serializer: 序列化函数
        deserializer: 反序列化函数
    """
    def decorator(func: Callable) -> Callable:
        # 获取函数签名
        sig = inspect.signature(func)
        func_name = func.__name__
        module_name = func.__module__
        
        # 默认缓存键前缀
        default_prefix = f"{module_name}.{func_name}"
        cache_prefix = key_prefix or default_prefix
        
        # 默认缓存管理器
        manager = cache_manager or get_default_cache()
        
        # 忽略的参数
        ignored_args = set(ignore_args or [])
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 检查缓存条件
            if condition and not condition(*args, **kwargs):
                logger.debug(f"缓存条件不满足，直接执行函数: {func_name}")
                return func(*args, **kwargs)
            
            try:
                # 生成缓存键
                cache_key = _generate_cache_key(
                    cache_prefix, sig, args, kwargs, ignored_args
                )
                
                # 尝试从缓存获取
                cached_value = manager.get(cache_key)
                if cached_value is not None:
                    logger.debug(f"缓存命中: {func_name}, key: {cache_key}")
                    
                    # 反序列化
                    if deserializer:
                        return deserializer(cached_value)
                    return cached_value
                
                # 缓存未命中，执行函数
                logger.debug(f"缓存未命中，执行函数: {func_name}, key: {cache_key}")
                result = func(*args, **kwargs)
                
                # 序列化并缓存结果
                cache_value = serializer(result) if serializer else result
                manager.set(cache_key, cache_value, ttl)
                
                return result
                
            except Exception as e:
                logger.error(f"缓存装饰器执行失败: {func_name}, 错误: {e}")
                # 缓存失败时直接执行函数
                return func(*args, **kwargs)
        
        # 添加缓存控制方法
        wrapper.cache_clear = lambda: _clear_function_cache(manager, cache_prefix)
        wrapper.cache_info = lambda: _get_function_cache_info(manager, cache_prefix)
        wrapper.cache_key = lambda *args, **kwargs: _generate_cache_key(
            cache_prefix, sig, args, kwargs, ignored_args
        )
        
        return wrapper
    
    return decorator


def cache_result(ttl: Optional[int] = None,
                key_template: Optional[str] = None,
                cache_manager: Optional[CacheManager] = None):
    """
    简单的结果缓存装饰器
    
    Args:
        ttl: 缓存过期时间
        key_template: 缓存键模板，支持 {arg_name} 占位符
        cache_manager: 缓存管理器
    """
    def decorator(func: Callable) -> Callable:
        manager = cache_manager or get_default_cache()
        sig = inspect.signature(func)
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 生成缓存键
                if key_template:
                    # 绑定参数
                    bound_args = sig.bind(*args, **kwargs)
                    bound_args.apply_defaults()
                    
                    # 格式化键模板
                    cache_key = key_template.format(**bound_args.arguments)
                else:
                    cache_key = CacheKey.generate(f"{func.__module__}.{func.__name__}", *args, **kwargs)
                
                # 尝试从缓存获取
                result = manager.get(cache_key)
                if result is not None:
                    return result
                
                # 执行函数并缓存结果
                result = func(*args, **kwargs)
                manager.set(cache_key, result, ttl)
                
                return result
                
            except Exception as e:
                logger.error(f"结果缓存装饰器失败: {func.__name__}, 错误: {e}")
                return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


def invalidate_cache(cache_keys: Union[str, List[str], Callable],
                    cache_manager: Optional[CacheManager] = None):
    """
    缓存失效装饰器
    
    Args:
        cache_keys: 要失效的缓存键或键列表，或生成键的函数
        cache_manager: 缓存管理器
    """
    def decorator(func: Callable) -> Callable:
        manager = cache_manager or get_default_cache()
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 执行原函数
            result = func(*args, **kwargs)
            
            try:
                # 确定要失效的缓存键
                if callable(cache_keys):
                    keys_to_invalidate = cache_keys(*args, **kwargs)
                elif isinstance(cache_keys, str):
                    keys_to_invalidate = [cache_keys]
                else:
                    keys_to_invalidate = cache_keys
                
                # 失效缓存
                for key in keys_to_invalidate:
                    manager.delete(key)
                    logger.debug(f"缓存失效: {key}")
                
            except Exception as e:
                logger.error(f"缓存失效失败: {func.__name__}, 错误: {e}")
            
            return result
        
        return wrapper
    
    return decorator


def cache_aside(get_key: Callable,
               load_func: Callable,
               ttl: Optional[int] = None,
               cache_manager: Optional[CacheManager] = None):
    """
    Cache-Aside模式装饰器
    
    Args:
        get_key: 获取缓存键的函数
        load_func: 加载数据的函数
        ttl: 缓存过期时间
        cache_manager: 缓存管理器
    """
    def decorator(func: Callable) -> Callable:
        manager = cache_manager or get_default_cache()
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            try:
                # 获取缓存键
                cache_key = get_key(*args, **kwargs)
                
                # 尝试从缓存获取
                cached_value = manager.get(cache_key)
                if cached_value is not None:
                    return cached_value
                
                # 缓存未命中，加载数据
                data = load_func(*args, **kwargs)
                
                # 缓存数据
                manager.set(cache_key, data, ttl)
                
                return data
                
            except Exception as e:
                logger.error(f"Cache-Aside模式失败: {func.__name__}, 错误: {e}")
                # 回退到原函数
                return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


def memoize(maxsize: int = 128, ttl: Optional[int] = None):
    """
    记忆化装饰器（基于缓存）
    
    Args:
        maxsize: 最大缓存条目数
        ttl: 缓存过期时间
    """
    def decorator(func: Callable) -> Callable:
        # 使用内存缓存提供者
        from .cache_providers import MemoryCacheProvider
        from .cache_manager import CacheManager
        
        provider = MemoryCacheProvider(max_size=maxsize, default_ttl=ttl or 3600)
        manager = CacheManager(provider, ttl or 3600)
        
        return cached(ttl=ttl, cache_manager=manager)(func)
    
    return decorator


def cache_property(ttl: Optional[int] = None):
    """
    缓存属性装饰器
    
    Args:
        ttl: 缓存过期时间
    """
    def decorator(func: Callable) -> property:
        cache_attr = f"_cached_{func.__name__}"
        expire_attr = f"_cached_{func.__name__}_expires"
        
        def getter(self):
            now = time.time()
            
            # 检查是否有缓存且未过期
            if hasattr(self, cache_attr):
                if ttl is None:  # 永不过期
                    return getattr(self, cache_attr)
                
                expire_time = getattr(self, expire_attr, 0)
                if now < expire_time:
                    return getattr(self, cache_attr)
            
            # 计算新值
            value = func(self)
            setattr(self, cache_attr, value)
            
            if ttl is not None:
                setattr(self, expire_attr, now + ttl)
            
            return value
        
        def deleter(self):
            if hasattr(self, cache_attr):
                delattr(self, cache_attr)
            if hasattr(self, expire_attr):
                delattr(self, expire_attr)
        
        return property(getter, None, deleter)
    
    return decorator


def _generate_cache_key(prefix: str, sig: inspect.Signature, 
                       args: tuple, kwargs: dict, 
                       ignored_args: set) -> str:
    """生成缓存键"""
    try:
        # 绑定参数
        bound_args = sig.bind(*args, **kwargs)
        bound_args.apply_defaults()
        
        # 过滤忽略的参数
        filtered_args = {
            name: value for name, value in bound_args.arguments.items()
            if name not in ignored_args
        }
        
        return CacheKey.generate(prefix, **filtered_args)
        
    except Exception as e:
        logger.warning(f"缓存键生成失败，使用简单键: {e}")
        # 回退到简单的键生成
        return CacheKey.generate(prefix, *args, **kwargs)


def _clear_function_cache(manager: CacheManager, prefix: str) -> bool:
    """清除函数缓存"""
    try:
        # 这需要缓存提供者支持模式删除
        logger.warning(f"函数缓存清除需要提供者支持模式删除: {prefix}")
        return False
    except Exception as e:
        logger.error(f"清除函数缓存失败: {prefix}, 错误: {e}")
        return False


def _get_function_cache_info(manager: CacheManager, prefix: str) -> Dict[str, Any]:
    """获取函数缓存信息"""
    try:
        stats = manager.get_stats()
        return {
            'prefix': prefix,
            'manager_stats': stats.get('manager_stats', {}),
            'provider_stats': stats.get('provider_stats', {})
        }
    except Exception as e:
        logger.error(f"获取函数缓存信息失败: {prefix}, 错误: {e}")
        return {'error': str(e)}


# 便捷的装饰器别名
cache = cached
memo = memoize
cache_prop = cache_property


# 批量缓存操作装饰器
def batch_cache(key_func: Callable,
               batch_load_func: Callable,
               ttl: Optional[int] = None,
               cache_manager: Optional[CacheManager] = None):
    """
    批量缓存装饰器
    
    Args:
        key_func: 生成缓存键的函数
        batch_load_func: 批量加载数据的函数
        ttl: 缓存过期时间
        cache_manager: 缓存管理器
    """
    def decorator(func: Callable) -> Callable:
        manager = cache_manager or get_default_cache()
        
        @functools.wraps(func)
        def wrapper(items: List[Any], *args, **kwargs):
            try:
                # 生成所有缓存键
                cache_keys = [key_func(item, *args, **kwargs) for item in items]
                
                # 批量获取缓存
                cached_data = manager.mget(cache_keys)
                
                # 找出缓存未命中的项
                missing_items = []
                missing_keys = []
                
                for i, (item, key) in enumerate(zip(items, cache_keys)):
                    if key not in cached_data:
                        missing_items.append(item)
                        missing_keys.append(key)
                
                # 如果有缓存未命中，批量加载
                if missing_items:
                    loaded_data = batch_load_func(missing_items, *args, **kwargs)
                    
                    # 批量设置缓存
                    cache_mapping = dict(zip(missing_keys, loaded_data))
                    manager.mset(cache_mapping, ttl)
                    
                    # 合并结果
                    cached_data.update(cache_mapping)
                
                # 按原始顺序返回结果
                return [cached_data.get(key) for key in cache_keys]
                
            except Exception as e:
                logger.error(f"批量缓存装饰器失败: {func.__name__}, 错误: {e}")
                return func(items, *args, **kwargs)
        
        return wrapper
    
    return decorator


# 条件缓存装饰器
def conditional_cache(condition_func: Callable,
                     ttl: Optional[int] = None,
                     cache_manager: Optional[CacheManager] = None):
    """
    条件缓存装饰器
    
    Args:
        condition_func: 判断是否缓存的函数
        ttl: 缓存过期时间
        cache_manager: 缓存管理器
    """
    return cached(
        ttl=ttl,
        cache_manager=cache_manager,
        condition=condition_func
    )
