#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 依赖库检查脚本
检查当前环境中已安装的依赖库版本和状态
"""

import sys
import importlib
import subprocess
from packaging import version

def get_package_version(package_name):
    """获取包的版本信息"""
    try:
        # 优先使用pkg_resources获取版本，更准确
        try:
            import pkg_resources
            return pkg_resources.get_distribution(package_name).version
        except:
            pass
        
        # 如果pkg_resources失败，尝试从模块获取
        package = importlib.import_module(package_name)
        version = getattr(package, '__version__', 'Unknown')
        
        return version
    except ImportError:
        return None

def check_pip_list():
    """通过pip list获取包信息"""
    try:
        result = subprocess.run([sys.executable, "-m", "pip", "list"], 
                              capture_output=True, text=True)
        return result.stdout
    except:
        return None

def main():
    """主函数"""
    print("🔍 MythQuant 依赖库检查报告")
    print("=" * 60)
    
    # 必需依赖库检查
    required_packages = {
        'pandas': '>=2.3.0',
        'numpy': '>=2.0.2',
        'mootdx': '>=0.11.7',
        'pytdx': '>=1.72',
        'openpyxl': '>=3.1.5',
        'xlrd': '>=2.0.2',
        'requests': '>=2.32.4',
        'tqdm': '>=4.67.1',
        'retry': '>=0.9.2',
    }
    
    print("📦 必需依赖库状态:")
    print("-" * 60)
    
    all_required_ok = True
    
    for package_name, min_version in required_packages.items():
        current_version = get_package_version(package_name)
        
        if current_version is None:
            print(f"❌ {package_name:<15} 未安装")
            all_required_ok = False
        elif current_version == 'Unknown':
            print(f"⚠️ {package_name:<15} 已安装 (版本未知)")
        else:
            try:
                min_ver = min_version.replace('>=', '').replace('>', '').replace('=', '')
                if version.parse(current_version) >= version.parse(min_ver):
                    print(f"✅ {package_name:<15} {current_version} (满足 {min_version})")
                else:
                    print(f"⚠️ {package_name:<15} {current_version} (需要 {min_version})")
                    all_required_ok = False
            except:
                print(f"⚠️ {package_name:<15} {current_version} (版本比较失败)")
    
    # 可选依赖库检查
    optional_packages = {
        'terminaltables': '>=3.1.10',
        'tushare': '>=1.4.21',
    }
    
    print(f"\n📦 可选依赖库状态:")
    print("-" * 60)
    
    for package_name, min_version in optional_packages.items():
        current_version = get_package_version(package_name)
        
        if current_version is None:
            print(f"⚪ {package_name:<15} 未安装 (可选)")
        elif current_version == 'Unknown':
            print(f"✅ {package_name:<15} 已安装 (版本未知)")
        else:
            try:
                min_ver = min_version.replace('>=', '').replace('>', '').replace('=', '')
                if version.parse(current_version) >= version.parse(min_ver):
                    print(f"✅ {package_name:<15} {current_version} (满足 {min_version})")
                else:
                    print(f"⚠️ {package_name:<15} {current_version} (建议 {min_version})")
            except:
                print(f"✅ {package_name:<15} {current_version} (版本比较失败)")
    
    # Python环境信息
    print(f"\n🐍 Python环境信息:")
    print("-" * 60)
    print(f"Python版本: {sys.version}")
    print(f"Python路径: {sys.executable}")
    
    # 核心功能验证
    print(f"\n🧪 核心功能验证:")
    print("-" * 60)
    
    tests = [
        ("pandas数据处理", "import pandas as pd; pd.DataFrame({'test': [1, 2, 3]})"),
        ("numpy数组计算", "import numpy as np; np.array([1, 2, 3])"),
        ("mootdx数据读取", "from mootdx.reader import Reader"),
        ("pytdx数据读取", "import pytdx.reader.gbbq_reader"),
        ("Excel文件处理", "import openpyxl; import xlrd"),
        ("网络请求", "import requests"),
    ]
    
    for test_name, test_code in tests:
        try:
            exec(test_code)
            print(f"✅ {test_name:<20} 正常")
        except Exception as e:
            print(f"❌ {test_name:<20} 失败: {str(e)[:30]}...")
            all_required_ok = False
    
    # 总结
    print(f"\n" + "=" * 60)
    if all_required_ok:
        print("🎉 所有必需依赖库都已正确安装，MythQuant 可以正常运行!")
    else:
        print("⚠️ 发现依赖库问题，请运行以下命令修复:")
        print("python install_dependencies.py")
        print("或手动安装:")
        print("pip install -r requirements.txt")
    
    # 显示完整的pip list（可选）
    pip_list = check_pip_list()
    if pip_list and len(sys.argv) > 1 and sys.argv[1] == '--full':
        print(f"\n📋 完整的已安装包列表:")
        print("-" * 60)
        print(pip_list)

if __name__ == "__main__":
    try:
        from packaging import version
    except ImportError:
        print("⚠️ 需要安装 packaging 库进行版本比较")
        print("运行: pip install packaging")
        sys.exit(1)
    
    main() 