#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行架构清理归档

安全地归档遗留代码，清理临时文件
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path
from typing import List, Dict


class ArchitectureCleanupExecutor:
    """架构清理执行器"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        self.archive_root = self.project_root / f"architecture_migration_final_archive_{self.timestamp}"
        
    def create_archive_structure(self):
        """创建归档目录结构"""
        print("📁 创建归档目录结构...")
        
        archive_dirs = [
            "legacy_code",
            "compatibility_files", 
            "old_configs",
            "metadata"
        ]
        
        for dir_name in archive_dirs:
            archive_dir = self.archive_root / dir_name
            archive_dir.mkdir(parents=True, exist_ok=True)
            print(f"   ✅ 创建目录: {archive_dir}")
        
        return self.archive_root
    
    def archive_legacy_code(self):
        """归档遗留代码"""
        print("\n📦 归档遗留代码模块...")
        
        legacy_items = [
            ("algorithms", "旧算法模块"),
            ("core", "旧核心模块"),
            ("cache", "旧缓存模块"), 
            ("file_io", "旧IO模块"),
            ("ui", "旧UI模块"),
            ("utils", "旧工具模块"),
            ("legacy", "明确标记的遗留代码")
        ]
        
        archive_dir = self.archive_root / "legacy_code"
        archived_count = 0
        
        for item_name, description in legacy_items:
            source_path = self.project_root / item_name
            
            if source_path.exists():
                target_path = archive_dir / item_name
                
                try:
                    if source_path.is_dir():
                        shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                        shutil.rmtree(source_path)
                    else:
                        shutil.copy2(source_path, target_path)
                        source_path.unlink()
                    
                    print(f"   ✅ 已归档: {item_name} ({description})")
                    archived_count += 1
                    
                except Exception as e:
                    print(f"   ❌ 归档失败: {item_name}, 错误: {e}")
            else:
                print(f"   ⚠️ 不存在: {item_name}")
        
        print(f"   📊 遗留代码归档完成: {archived_count} 个项目")
    
    def archive_compatibility_files(self):
        """归档兼容性文件"""
        print("\n📦 归档兼容性文件...")
        
        compatibility_files = [
            "algorithm_compatibility.py",
            "config_compatibility.py",
            "data_access_compatibility.py", 
            "io_compatibility.py"
        ]
        
        archive_dir = self.archive_root / "compatibility_files"
        archived_count = 0
        
        for file_name in compatibility_files:
            source_path = self.project_root / file_name
            
            if source_path.exists():
                target_path = archive_dir / file_name
                
                try:
                    shutil.copy2(source_path, target_path)
                    source_path.unlink()
                    print(f"   ✅ 已归档: {file_name}")
                    archived_count += 1
                    
                except Exception as e:
                    print(f"   ❌ 归档失败: {file_name}, 错误: {e}")
            else:
                print(f"   ⚠️ 不存在: {file_name}")
        
        print(f"   📊 兼容性文件归档完成: {archived_count} 个文件")
    
    def archive_old_configs(self):
        """归档旧配置"""
        print("\n📦 归档旧配置目录...")
        
        config_items = [
            ("config", "旧配置目录"),
            ("custom_datacfg", "自定义数据配置")
        ]
        
        archive_dir = self.archive_root / "old_configs"
        archived_count = 0
        
        for item_name, description in config_items:
            source_path = self.project_root / item_name
            
            if source_path.exists():
                target_path = archive_dir / item_name
                
                try:
                    if source_path.is_dir():
                        shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                        shutil.rmtree(source_path)
                    else:
                        shutil.copy2(source_path, target_path)
                        source_path.unlink()
                    
                    print(f"   ✅ 已归档: {item_name} ({description})")
                    archived_count += 1
                    
                except Exception as e:
                    print(f"   ❌ 归档失败: {item_name}, 错误: {e}")
            else:
                print(f"   ⚠️ 不存在: {item_name}")
        
        print(f"   📊 旧配置归档完成: {archived_count} 个项目")
    
    def save_archive_metadata(self):
        """保存归档元数据"""
        print("\n📋 保存归档元数据...")
        
        metadata = {
            "archive_timestamp": self.timestamp,
            "archive_date": datetime.now().isoformat(),
            "project_root": str(self.project_root),
            "archive_location": str(self.archive_root),
            "architecture_status": "DDD架构完成，遗留代码已归档",
            "archived_categories": [
                "legacy_code",
                "compatibility_files", 
                "old_configs"
            ],
            "notes": [
                "所有重要功能已迁移到src/mythquant/",
                "DDD架构和Clean Architecture已完全实施",
                "事件驱动架构和多级缓存系统已激活",
                "归档的代码仅作历史参考，不再维护"
            ]
        }
        
        metadata_file = self.archive_root / "metadata" / "archive_info.json"
        
        try:
            with open(metadata_file, 'w', encoding='utf-8') as f:
                json.dump(metadata, f, indent=2, ensure_ascii=False)
            print(f"   ✅ 元数据已保存: {metadata_file}")
        except Exception as e:
            print(f"   ❌ 元数据保存失败: {e}")
    
    def execute_archive(self):
        """执行归档操作"""
        print("🏗️ 开始执行架构清理归档")
        print("=" * 60)
        
        # 创建归档结构
        self.create_archive_structure()
        
        # 执行归档操作
        self.archive_legacy_code()
        self.archive_compatibility_files()
        self.archive_old_configs()
        
        # 保存元数据
        self.save_archive_metadata()
        
        print(f"\n🎉 归档操作完成！")
        print(f"📁 归档位置: {self.archive_root}")
        print(f"📊 归档大小: {self._get_directory_size(self.archive_root):.2f} MB")
    
    def _get_directory_size(self, path: Path) -> float:
        """获取目录大小（MB）"""
        total_size = 0
        try:
            for item in path.rglob("*"):
                if item.is_file():
                    total_size += item.stat().st_size
        except:
            pass
        return total_size / (1024 * 1024)


def main():
    """主函数"""
    executor = ArchitectureCleanupExecutor()
    
    print("⚠️ 即将执行架构清理归档操作")
    print("📋 操作内容:")
    print("   1. 归档遗留代码模块 (algorithms, core, cache, file_io, ui, utils, legacy)")
    print("   2. 归档兼容性文件 (*_compatibility.py)")
    print("   3. 归档旧配置目录 (config, custom_datacfg)")
    print("   4. 保存归档元数据")
    print()
    
    confirm = input("🤔 确认执行归档操作? (y/N): ").strip().lower()
    
    if confirm == 'y':
        executor.execute_archive()
    else:
        print("❌ 操作已取消")


if __name__ == "__main__":
    main()
