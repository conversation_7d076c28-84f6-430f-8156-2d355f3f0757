# 输出目录配置指南

## 概述

现在您可以在 `user_config.py` 中灵活配置输出目录，无需修改主程序代码。

## 配置选项

### 1. 基础配置

在 `user_config.py` 中找到 `output_config` 配置节：

```python
# 输出目录配置
output_config = {
    'base_output_path': 'H:/MPV1.17/T0002/signals',  # 基础输出路径
    'use_subdirectories': False,  # 是否使用子目录
    'custom_subdirs': {  # 自定义子目录名称
        'minute': 'minute_data',
        'daily': 'daily_data', 
        'weekly': 'weekly_data'
    }
}
```

### 2. 配置说明

#### `base_output_path`
- **作用**: 设置基础输出路径
- **默认值**: `'H:/MPV1.17/T0002/signals'`
- **示例**: 
  - `'D:/MyData/signals'` - 输出到D盘自定义目录
  - `'C:/TradingData/output'` - 输出到C盘交易数据目录

#### `use_subdirectories`
- **作用**: 控制是否为不同数据类型创建子目录
- **默认值**: `False` (直接输出到基础路径)
- **选项**:
  - `False`: 所有文件输出到同一个目录
  - `True`: 为不同数据类型创建子目录

#### `custom_subdirs`
- **作用**: 当 `use_subdirectories=True` 时，定义子目录名称
- **默认值**: 
  ```python
  {
      'minute': 'minute_data',
      'daily': 'daily_data', 
      'weekly': 'weekly_data'
  }
  ```

## 使用示例

### 示例1: 直接输出到基础目录（当前配置）

```python
output_config = {
    'base_output_path': 'H:/MPV1.17/T0002/signals',
    'use_subdirectories': False,
    'custom_subdirs': {
        'minute': 'minute_data',
        'daily': 'daily_data', 
        'weekly': 'weekly_data'
    }
}
```

**输出结果**:
- 分钟级别文件: `H:/MPV1.17/T0002/signals/min_0_000617_20250101-20250625.txt`
- 日线级别文件: `H:/MPV1.17/T0002/signals/day_0_000617_20150101-20250731.txt`
- 周线级别文件: `H:/MPV1.17/T0002/signals/week_0_000617_20230101-20231231.txt`

### 示例2: 使用子目录分类

```python
output_config = {
    'base_output_path': 'H:/MPV1.17/T0002/signals',
    'use_subdirectories': True,
    'custom_subdirs': {
        'minute': 'minute_data',
        'daily': 'daily_data', 
        'weekly': 'weekly_data'
    }
}
```

**输出结果**:
- 分钟级别文件: `H:/MPV1.17/T0002/signals/minute_data/min_0_000617_20250101-20250625.txt`
- 日线级别文件: `H:/MPV1.17/T0002/signals/daily_data/day_0_000617_20150101-20250731.txt`
- 周线级别文件: `H:/MPV1.17/T0002/signals/weekly_data/week_0_000617_20230101-20231231.txt`

### 示例3: 自定义路径和子目录

```python
output_config = {
    'base_output_path': 'D:/MyTradingData/results',
    'use_subdirectories': True,
    'custom_subdirs': {
        'minute': '分钟数据',
        'daily': '日线数据', 
        'weekly': '周线数据'
    }
}
```

**输出结果**:
- 分钟级别文件: `D:/MyTradingData/results/分钟数据/min_0_000617_20250101-20250625.txt`
- 日线级别文件: `D:/MyTradingData/results/日线数据/day_0_000617_20150101-20250731.txt`
- 周线级别文件: `D:/MyTradingData/results/周线数据/week_0_000617_20230101-20231231.txt`

## 文件命名规则

生成的文件遵循以下命名规则：

### 日线级别文件
- **格式**: `day_{市场编号}_{股票代码}_{开始日期}-{结束日期}.txt`
- **示例**: `day_0_000617_20150101-20250731.txt`
- **说明**: 
  - `day`: 日线级别标识
  - `0`: 市场编号 (0=深证, 1=上证, 2=北交所)
  - `000617`: 股票代码
  - `20150101-20250731`: 实际数据的时间范围

### 分钟级别文件
- **格式**: `min_{市场编号}_{股票代码}_{开始日期}-{结束日期}.txt`
- **示例**: `min_0_000617_20250101-20250625.txt`

### 周线级别文件
- **格式**: `week_{市场编号}_{股票代码}_{开始日期}-{结束日期}.txt`
- **示例**: `week_0_000617_20230101-20231231.txt`

## 市场编号对应表

| 市场编号 | 交易所 | 股票代码前缀 |
|---------|--------|-------------|
| 0 | 深圳证券交易所 | 000, 001, 002, 300, 301, 200 |
| 1 | 上海证券交易所 | 600, 601, 603, 605, 688, 900 |
| 2 | 北京证券交易所 | 430, 831-839 |

## 注意事项

1. **目录自动创建**: 程序会自动创建不存在的目录
2. **路径格式**: 支持Windows和Unix风格的路径分隔符
3. **权限要求**: 确保程序对输出目录有写入权限
4. **磁盘空间**: 确保输出目录所在磁盘有足够空间

## 常见问题

### Q: 如何更改输出目录？
A: 修改 `user_config.py` 中的 `base_output_path` 即可。

### Q: 如何让不同类型的数据输出到不同目录？
A: 设置 `use_subdirectories = True` 并配置 `custom_subdirs`。

### Q: 可以使用中文目录名吗？
A: 可以，程序支持中文路径和目录名。

### Q: 如何恢复默认设置？
A: 将配置恢复为：
```python
output_config = {
    'base_output_path': 'H:/MPV1.17/T0002/signals',
    'use_subdirectories': False,
    'custom_subdirs': {
        'minute': 'minute_data',
        'daily': 'daily_data', 
        'weekly': 'weekly_data'
    }
}
```

## 当前测试结果

根据您的当前配置，程序成功生成了：
- 输出目录: `H:/MPV1.17/T0002/signals`
- 生成文件: `day_0_000617_20150101-20250731.txt` (129,082 字节)
- 数据范围: 2015-01-01 至 2025-07-31
- 记录数量: 2,447 条日线数据

### 字段格式更新

日线级别txt文件现已与分钟级别格式保持一致：
- **格式**: `股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖`
- **分隔符**: 使用管道符 `|`
- **时间格式**: 日线使用8位格式 `YYYYMMDD`
- **编码**: UTF-8-BOM 编码

### 示例数据
```
股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
000617|20150105|43466.65|0.00|0.00|0.00|0.00|0.00
000617|20150106|262642.87|0.00|0.00|0.00|0.00|0.00
```

配置修改已成功实现，现在您可以根据需要灵活调整输出目录！ 