# MythQuant 互联网股票数据下载指南

## 📋 概述

本功能允许您从互联网免费下载股票的前复权数据，作为现有TDX数据的补充。下载的数据将保存在与现有txt文件相同的目录中，文件名会自动添加"来源互联网"标识。

## 🎯 支持的数据源

### 1. BaoStock（推荐）
- **优势**: 完全免费，无需注册，专门针对中国A股
- **数据质量**: 提供前复权、后复权数据
- **稳定性**: 由证券宝维护，相对稳定
- **覆盖范围**: A股全市场数据
- **安装**: `pip install baostock`

### 2. AKShare
- **优势**: 功能强大，数据源丰富，18.4k+ GitHub stars
- **数据质量**: 支持多种复权方式
- **更新频率**: 活跃维护
- **覆盖范围**: 股票、期货、基金等
- **安装**: `pip install akshare`

### 3. yfinance
- **优势**: 全球股票数据，包括港股、美股
- **数据质量**: Yahoo Finance官方数据
- **覆盖范围**: 全球主要交易所
- **安装**: `pip install yfinance`

## 🚀 快速开始

### 第一步：安装数据源
```bash
# 自动安装所有数据源
python scripts/install_data_sources.py

# 或手动安装
pip install baostock akshare yfinance
```

### 第二步：下载数据
```bash
# 下载目标股票的前复权数据
python scripts/download_internet_data.py
```

## 📁 输出文件格式

### 文件命名规则
```
day_0_{股票代码}_{开始日期}-{结束日期}_来源互联网.txt
```

示例：
- `day_0_000617_20150101-20250724_来源互联网.txt`
- `day_0_000001_20150101-20250724_来源互联网.txt`

### 文件内容格式
```
股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
000617|20150105|0.0|10.01|5.98|0.0|0.0|0.0
000617|20150106|0.0|9.85|5.89|0.0|0.0|0.0
```

**注意**: 
- 互联网数据源无法提供买卖差、路径总长、主买、主卖等L2指标，这些字段将填充为0.0
- 前复权收盘价C就是下载的前复权数据

## ⚙️ 配置说明

### 目标股票配置
程序会自动读取现有的目标股票配置：

1. **从Excel文件读取**（如果配置了`target_stocks_file`）
2. **使用默认股票列表**（如果没有配置）

### 日期范围
- **开始日期**: 2015-01-01（与现有数据保持一致）
- **结束日期**: 当前日期

### 输出目录
数据保存在与现有txt文件相同的目录：`H:/MPV1.17/T0002/signals/`

## 🔧 使用示例

### 基本使用
```bash
# 1. 安装数据源
python scripts/install_data_sources.py

# 2. 下载数据
python scripts/download_internet_data.py
```

### 程序化使用
```python
from utils.stock_data_downloader import StockDataDownloader

# 初始化下载器
downloader = StockDataDownloader()

# 下载单只股票
success = downloader.save_stock_data("000617", "20240101", "20241231")

# 批量下载
stock_codes = ["000617", "000001", "000002"]
results = downloader.batch_download(stock_codes, "20240101", "20241231")
```

## 📊 数据质量说明

### 优势
- ✅ **前复权数据**: 自动处理除权除息，价格连续性好
- ✅ **免费获取**: 无需付费token或API key
- ✅ **数据完整**: 覆盖较长的历史时间段
- ✅ **格式统一**: 自动转换为项目标准格式

### 限制
- ❌ **无L2数据**: 无法提供买卖差、主买主卖等高频数据
- ❌ **延迟更新**: 数据可能有1-2天的延迟
- ❌ **网络依赖**: 需要稳定的网络连接
- ❌ **请求限制**: 需要控制请求频率避免被限制

## 🛠️ 故障排除

### 常见问题

#### 1. 安装失败
```bash
# 问题：pip install 失败
# 解决：使用国内镜像
pip install baostock -i https://pypi.tuna.tsinghua.edu.cn/simple/
```

#### 2. 网络连接失败
```bash
# 问题：无法连接数据源
# 解决：检查网络连接，可能需要VPN
```

#### 3. 数据下载失败
```bash
# 问题：特定股票下载失败
# 解决：检查股票代码格式，确认股票是否存在
```

#### 4. 权限错误
```bash
# 问题：无法写入文件
# 解决：检查输出目录权限
```

### 日志查看
程序运行时会生成详细日志：
```
logs/enhanced_error_handler_YYYYMMDD_HHMMSS.log
```

## 📈 性能优化

### 批量下载优化
- **请求间隔**: 默认2秒间隔，避免被限制
- **错误重试**: 自动尝试多个数据源
- **进度显示**: 实时显示下载进度

### 数据缓存
- **本地缓存**: 避免重复下载相同数据
- **增量更新**: 支持只下载新增数据（待实现）

## 🔮 未来计划

### 功能扩展
- [ ] 支持分钟级数据下载
- [ ] 增量数据更新
- [ ] 数据质量验证
- [ ] 多线程并发下载
- [ ] 数据去重和合并

### 数据源扩展
- [ ] 集成更多免费数据源
- [ ] 支持期货、基金数据
- [ ] 港股、美股数据支持

## 📞 技术支持

如果遇到问题，请：

1. **查看日志文件** 获取详细错误信息
2. **检查网络连接** 确保能访问数据源
3. **验证股票代码** 确认代码格式正确
4. **更新数据源包** 使用最新版本

## 📄 许可证

本功能遵循项目的整体许可证。使用的第三方数据源请遵守其各自的使用条款。
