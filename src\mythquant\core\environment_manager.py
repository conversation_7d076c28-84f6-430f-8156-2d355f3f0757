#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 环境管理器

统一管理测试和生产环境，确保：
1. 测试永远使用生产函数
2. 路径和配置通过环境管理器自动适配
3. 测试素材保鲜机制
4. 环境切换透明化

作者: AI Assistant
创建时间: 2025-08-01
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional, Union, Any
from src.mythquant.shared.logging import get_smart_logger


class EnvironmentManager:
    """统一的环境管理器"""
    
    def __init__(self):
        """初始化环境管理器"""
        self.smart_logger = get_smart_logger("EnvironmentManager")
        self.test_mode = self._detect_test_mode()
        self.config = self._load_config()
        self._initialized = False
        
        # 初始化环境
        self._initialize_environment()
    
    def _detect_test_mode(self) -> bool:
        """自动检测测试模式"""
        try:
            from test_config import is_test_environment
            return is_test_environment()
        except ImportError:
            self.smart_logger.warning("无法导入test_config，默认为生产模式")
            return False
    
    def _load_config(self) -> Dict[str, Any]:
        """加载配置"""
        if self.test_mode:
            try:
                from test_config import TEST_CONFIG, get_test_config
                self.smart_logger.info("加载测试环境配置")
                return TEST_CONFIG
            except ImportError:
                self.smart_logger.error("无法加载测试配置，回退到生产配置")
                return self._load_production_config()
        else:
            return self._load_production_config()
    
    def _load_production_config(self) -> Dict[str, Any]:
        """加载生产环境配置"""
        try:
            # import user_config  # 将使用新的配置系统
            self.smart_logger.info("加载生产环境配置")
            return {
                'base_output_path': './output',  # 默认输出路径
                'tdx_path': '',  # TDX路径
                'gbbq_path': '',  # GBBQ路径
                'mode': 'production'
            }
        except (ImportError, KeyError) as e:
            self.smart_logger.error(f"无法加载生产配置: {e}")
            # 使用默认配置
            return {
                'base_output_path': r'H:\MPV1.17\T0002\signals',
                'tdx_path': r'H:\MPV1.17\T0002\signals\TDX',
                'gbbq_path': r'H:\MPV1.17\T0002\signals\GBBQ',
                'mode': 'production'
            }
    
    def _initialize_environment(self):
        """初始化环境"""
        if self._initialized:
            return
        
        if self.test_mode:
            self._initialize_test_environment()
        else:
            self._initialize_production_environment()
        
        self._initialized = True
    
    def _initialize_test_environment(self):
        """初始化测试环境"""
        try:
            from test_config import initialize_test_environment
            initialize_test_environment()
            self.smart_logger.info("测试环境初始化完成")
        except Exception as e:
            self.smart_logger.error(f"测试环境初始化失败: {e}")
    
    def _initialize_production_environment(self):
        """初始化生产环境"""
        # 检查生产环境路径是否存在
        base_path = self.config.get('base_output_path')
        if base_path and not os.path.exists(base_path):
            self.smart_logger.warning(f"生产环境路径不存在: {base_path}")
        else:
            self.smart_logger.info("生产环境初始化完成")
    
    # ==================== 路径管理 ====================
    
    def resolve_file_path(self, logical_path: str, path_type: str = 'output') -> str:
        """
        解析文件路径
        
        Args:
            logical_path: 逻辑路径（如 '1min_0_000617.txt'）
            path_type: 路径类型（'output', 'input', 'fixture', 'sandbox'）
            
        Returns:
            str: 实际文件路径
        """
        if self.test_mode:
            from test_config import resolve_path
            resolved_path = resolve_path(logical_path, path_type)
            self.smart_logger.debug(f"测试环境路径解析: {logical_path} -> {resolved_path}")
            return resolved_path
        else:
            # 生产环境直接使用基础路径
            base_path = self.config.get('base_output_path', '')
            resolved_path = os.path.join(base_path, logical_path)
            self.smart_logger.debug(f"生产环境路径解析: {logical_path} -> {resolved_path}")
            return resolved_path
    
    def get_data_file(self, filename: str, ensure_fresh: bool = True) -> str:
        """
        获取数据文件路径（自动处理测试素材保鲜）
        
        Args:
            filename: 文件名
            ensure_fresh: 是否确保数据新鲜（仅测试环境有效）
            
        Returns:
            str: 数据文件路径
        """
        if self.test_mode and ensure_fresh:
            try:
                from test_config import prepare_test_data
                fresh_path = prepare_test_data(filename)
                self.smart_logger.info(f"准备新鲜测试数据: {filename} -> {fresh_path}")
                return fresh_path
            except Exception as e:
                self.smart_logger.warning(f"无法准备新鲜测试数据: {e}")
                # 回退到普通路径解析
                return self.resolve_file_path(filename, 'input')
        else:
            return self.resolve_file_path(filename)
    
    def get_output_directory(self) -> str:
        """获取输出目录"""
        if self.test_mode:
            return self.config['paths']['output_data']
        else:
            return self.config['base_output_path']
    
    def get_input_directory(self) -> str:
        """获取输入目录"""
        if self.test_mode:
            return self.config['paths']['input_data']
        else:
            return self.config['base_output_path']
    
    # ==================== 配置管理 ====================
    
    def get_config(self, key: str = None) -> Union[Dict, Any]:
        """
        获取配置值
        
        Args:
            key: 配置键名，支持点分隔的嵌套键（如 'paths.output_data'）
            
        Returns:
            配置值或全部配置
        """
        if key is None:
            return self.config
        
        keys = key.split('.')
        config = self.config
        
        for k in keys:
            if isinstance(config, dict) and k in config:
                config = config[k]
            else:
                return None
        
        return config
    
    def is_test_mode(self) -> bool:
        """是否为测试模式"""
        return self.test_mode
    
    def is_production_mode(self) -> bool:
        """是否为生产模式"""
        return not self.test_mode
    
    def get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        return {
            'mode': 'test' if self.test_mode else 'production',
            'initialized': self._initialized,
            'config_keys': list(self.config.keys()) if isinstance(self.config, dict) else [],
            'output_directory': self.get_output_directory(),
            'input_directory': self.get_input_directory(),
        }
    
    # ==================== 测试支持 ====================
    
    def enable_test_mode(self):
        """启用测试模式（运行时切换）"""
        if not self.test_mode:
            try:
                from test_config import enable_test_mode
                enable_test_mode()
                self.test_mode = True
                self.config = self._load_config()
                self._initialized = False
                self._initialize_environment()
                self.smart_logger.info("已切换到测试模式")
            except Exception as e:
                self.smart_logger.error(f"切换到测试模式失败: {e}")
    
    def disable_test_mode(self):
        """禁用测试模式（运行时切换）"""
        if self.test_mode:
            try:
                from test_config import disable_test_mode
                disable_test_mode()
                self.test_mode = False
                self.config = self._load_config()
                self._initialized = False
                self._initialize_environment()
                self.smart_logger.info("已切换到生产模式")
            except Exception as e:
                self.smart_logger.error(f"切换到生产模式失败: {e}")
    
    def cleanup_test_environment(self):
        """清理测试环境"""
        if self.test_mode:
            try:
                from test_config import cleanup_test_sandbox
                cleanup_test_sandbox()
                self.smart_logger.info("测试环境已清理")
            except Exception as e:
                self.smart_logger.error(f"清理测试环境失败: {e}")
    
    # ==================== 上下文管理器支持 ====================
    
    def __enter__(self):
        """进入上下文管理器"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """退出上下文管理器"""
        if self.test_mode:
            self.cleanup_test_environment()


# ==================== 全局环境管理器实例 ====================
_global_env_manager = None


def get_environment_manager() -> EnvironmentManager:
    """获取全局环境管理器实例"""
    global _global_env_manager
    if _global_env_manager is None:
        _global_env_manager = EnvironmentManager()
    return _global_env_manager


def reset_environment_manager():
    """重置全局环境管理器（主要用于测试）"""
    global _global_env_manager
    _global_env_manager = None


# ==================== 便捷函数 ====================

def resolve_path(logical_path: str, path_type: str = 'output') -> str:
    """便捷函数：解析路径"""
    return get_environment_manager().resolve_file_path(logical_path, path_type)


def get_data_file(filename: str, ensure_fresh: bool = True) -> str:
    """便捷函数：获取数据文件"""
    return get_environment_manager().get_data_file(filename, ensure_fresh)


def is_test_environment() -> bool:
    """便捷函数：检查是否为测试环境"""
    return get_environment_manager().is_test_mode()


def get_output_directory() -> str:
    """便捷函数：获取输出目录"""
    return get_environment_manager().get_output_directory()


# ==================== 使用示例 ====================
if __name__ == '__main__':
    print("🌍 MythQuant 环境管理器演示")
    print("=" * 60)
    
    # 创建环境管理器
    with EnvironmentManager() as env_manager:
        # 显示环境信息
        info = env_manager.get_environment_info()
        print(f"环境信息:")
        for key, value in info.items():
            print(f"  {key}: {value}")
        
        # 演示路径解析
        print(f"\n路径解析演示:")
        test_file = "1min_0_000617_sample.txt"
        print(f"  逻辑路径: {test_file}")
        print(f"  输出路径: {env_manager.resolve_file_path(test_file, 'output')}")
        print(f"  输入路径: {env_manager.resolve_file_path(test_file, 'input')}")
        
        # 演示数据文件获取
        print(f"\n数据文件获取演示:")
        data_file = env_manager.get_data_file(test_file)
        print(f"  数据文件路径: {data_file}")
        
        # 演示配置获取
        print(f"\n配置获取演示:")
        base_path = env_manager.get_config('base_output_path')
        print(f"  基础输出路径: {base_path}")
    
    print(f"\n✅ 环境管理器演示完成")
