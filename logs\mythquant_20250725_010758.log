2025-07-25 01:07:58,079 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250725_010758.log
2025-07-25 01:07:58,079 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-25 01:07:58,079 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-25 01:07:58,079 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-25 01:07:58,080 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-25 01:07:58,551 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-25 01:07:58,552 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-25 01:07:58,558 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-25 01:07:58,558 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-25 01:07:58,559 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-25 01:07:58,559 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-25 01:07:58,559 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-25 01:07:58,559 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-25 01:07:58,560 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-25 01:07:58,563 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-25 01:07:58,563 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-25 01:07:58,563 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-25 01:07:58,563 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-25 01:07:58,570 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-25 01:07:58,988 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.42秒
2025-07-25 01:07:58,989 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.42秒
2025-07-25 01:07:58,989 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-25 01:07:58,989 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-25 01:07:58,989 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-25 01:07:58,989 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-25 01:07:58,989 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-25 01:07:58,990 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-25 01:07:58,990 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-25 01:07:58,990 - main_v20230219_optimized - INFO - generate_daily_data_task:3108 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-25 01:07:58,990 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:3647 - 🧵 启动多线程日线级别txt文件生成
2025-07-25 01:07:58,990 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-25 01:07:58,991 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-25 01:07:59,101 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-25 01:07:59,101 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-25 01:07:59,107 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-25 01:07:59,108 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-25 01:07:59,108 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-25 01:07:59,108 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-25 01:07:59,108 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-25 01:07:59,108 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-25 01:07:59,108 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-25 01:07:59,111 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-25 01:07:59,112 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-25 01:07:59,112 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-25 01:07:59,112 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-25 01:07:59,119 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-25 01:07:59,753 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.63秒
2025-07-25 01:07:59,753 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.63秒
2025-07-25 01:08:08,827 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-25 01:08:32,565 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-25 01:08:32,565 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-25 01:08:32,565 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-25 01:08:32,571 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-25 01:08:32,972 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-25 01:08:32,973 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-25 01:08:32,973 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-25 01:08:32,973 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-25 01:08:32,974 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-25 01:08:32,974 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-25 01:08:32,974 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-25 01:08:32,975 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-25 01:08:32,975 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-25 01:08:33,007 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-25 01:08:33,392 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-25 01:08:33,406 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-25 01:08:33,415 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-25 01:08:33,424 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-25 01:08:33,433 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-25 01:08:33,442 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-25 01:08:33,451 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-25 01:08:33,460 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-25 01:08:33,470 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-25 01:08:33,479 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-25 01:08:33,489 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-25 01:08:33,498 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-25 01:08:33,508 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-25 01:08:33,523 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-25 01:08:33,534 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-25 01:08:33,545 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-25 01:08:33,555 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-25 01:08:33,570 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-25 01:08:33,579 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-25 01:08:33,589 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-25 01:08:33,606 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-25 01:08:33,607 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-25 01:08:37,911 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-25 01:08:37,911 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-25 01:08:37,912 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-25 01:08:37,912 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-25 01:08:37,912 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-25 01:08:37,914 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-25 01:08:37,988 - main_v20230219_optimized - INFO - apply_forward_adjustment:1744 - 保留交易时间数据后: 320880条
2025-07-25 01:08:37,989 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-25 01:08:37,989 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-25 01:08:37,989 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-25 01:08:37,989 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-25 01:08:37,990 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-25 01:08:37,990 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-25 01:08:37,997 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-25 01:08:38,116 - main_v20230219_optimized - INFO - load_and_process_minute_data:2213 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-25 01:08:38,590 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-25 01:08:38,840 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-25 01:08:38,841 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-25 01:08:38,945 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-25 01:08:38,945 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-25 01:08:38,946 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3826 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-25 01:08:38,946 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3827 - 📊 成功生成: 1 个文件
2025-07-25 01:08:38,946 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3828 - ❌ 处理失败: 0 个股票
2025-07-25 01:08:38,946 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3829 - 📈 成功率: 100.0%
2025-07-25 01:08:38,946 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3830 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-25 01:08:38,963 - main_v20230219_optimized - INFO - generate_daily_data_task:3118 - ✅ 日线数据生成完成，耗时: 39.97 秒
2025-07-25 01:08:38,963 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-25 01:08:38,963 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网日线数据下载
2025-07-25 01:08:38,963 - Main - INFO - info:307 - ℹ️ 开始执行互联网日线数据下载任务
2025-07-25 01:08:38,964 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-25 01:08:38,975 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-25 01:08:39,301 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-25 01:08:39,301 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['baostock', 'akshare']
2025-07-25 01:08:39,301 - Main - INFO - info:307 - ℹ️ 互联网数据下载时间范围: 20170101 - 20250720
2025-07-25 01:08:39,301 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的互联网数据
2025-07-25 01:08:39,301 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 2.0秒
2025-07-25 01:08:39,302 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-25 01:08:39,302 - Main - INFO - info:307 - ℹ️ 尝试使用baostock下载000617数据
2025-07-25 01:08:46,856 - Main - INFO - info:307 - ℹ️ BaoStock成功获取000617数据，共2074条记录（包含不复权和前复权价格）
2025-07-25 01:08:46,857 - Main - INFO - info:307 - ℹ️ 使用baostock成功下载000617数据
2025-07-25 01:08:46,863 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-25 01:08:46,864 - Main - INFO - info:307 - ℹ️   日期: 20170103
2025-07-25 01:08:46,864 - Main - INFO - info:307 - ℹ️   当日收盘价C: 19.03
2025-07-25 01:08:46,864 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 11.35883573
2025-07-25 01:08:46,864 - Main - INFO - info:307 - ℹ️ ✅ 成功获取不同的不复权和前复权价格，差异: 7.6712
2025-07-25 01:08:46,864 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 2074 >= 5
2025-07-25 01:08:46,864 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-25 01:08:46,865 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-25 01:08:46,865 - Main - INFO - info:307 - ℹ️ ✅ 前复权价格差异检查通过，平均差异: 2.3674
2025-07-25 01:08:46,897 - Main - INFO - info:307 - ℹ️ 成功保存000617数据到: H:/MPV1.17/T0002/signals/day_0_000617_20170101-20250720_来源互联网.txt
2025-07-25 01:08:46,897 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-25 01:08:46,897 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-25 01:08:46,897 - Main - INFO - info:307 - ℹ️ 互联网数据下载完成: 1/1 成功 (100.0%)
2025-07-25 01:08:46,897 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网日线数据下载
2025-07-25 01:08:46,898 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-25 01:08:46,898 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-25 01:08:46,898 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-25 01:08:46,898 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-25 01:08:46,899 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-25 01:08:46,899 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-25 01:08:46,899 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: day_0_000617_20240701-20240731_来源互联网.txt
2025-07-25 01:08:46,916 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20200101-20250724.txt, 数据行数: 1337
2025-07-25 01:08:46,918 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20240701-20240731_来源互联网.txt, 数据行数: 23
2025-07-25 01:08:46,919 - Main - INFO - info:307 - ℹ️ 数据对齐完成，共同日期数: 23
2025-07-25 01:08:46,921 - Main - INFO - info:307 - ℹ️ 价格差异计算完成，数据点数: 23
2025-07-25 01:08:46,923 - Main - INFO - info:307 - ℹ️ 股票 000617 数据比较完成
2025-07-25 01:08:46,924 - Main - INFO - info:307 - ℹ️ ✅ 000617 分析完成
2025-07-25 01:08:46,937 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250725_010846.txt
2025-07-25 01:08:46,937 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 1/1 成功 (100.0%)
2025-07-25 01:08:46,937 - Main - INFO - info:307 - ℹ️ 任务执行成功: 前复权数据比较分析
2025-07-25 01:08:46,937 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 100.0%
2025-07-25 01:08:46,938 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-25 01:08:46,938 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-25 01:08:46,938 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-25 01:08:46,938 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-25 01:08:46,938 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-25 01:08:46,938 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-25 01:08:46,938 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
