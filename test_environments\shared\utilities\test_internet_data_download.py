#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互联网股票数据下载功能测试
"""

import sys
import os
import pandas as pd
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.stock_data_downloader import StockDataDownloader


def test_single_stock_download():
    """测试单只股票下载"""
    print("🧪 测试单只股票下载...")
    
    downloader = StockDataDownloader()
    
    # 测试下载000617的近期数据
    success = downloader.save_stock_data("000617", "20240701", "20240731")
    
    if success:
        print("✅ 单只股票下载测试通过")
        
        # 验证文件内容
        filepath = "H:/MPV1.17/T0002/signals/day_0_000617_20240701-20240731_来源互联网.txt"
        if os.path.exists(filepath):
            df = pd.read_csv(filepath, sep='|')
            print(f"📊 文件包含 {len(df)} 行数据")
            print(f"📋 列名: {list(df.columns)}")
            print(f"📅 日期范围: {df['时间'].min()} - {df['时间'].max()}")
            
            # 验证数据格式
            required_columns = ['股票编码', '时间', '买卖差', '当日收盘价C', '前复权收盘价C', '路径总长', '主买', '主卖']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if not missing_columns:
                print("✅ 数据格式验证通过")
            else:
                print(f"❌ 缺少列: {missing_columns}")
            
            return True
        else:
            print("❌ 输出文件不存在")
            return False
    else:
        print("❌ 单只股票下载测试失败")
        return False


def test_batch_download():
    """测试批量下载"""
    print("\n🧪 测试批量下载...")
    
    downloader = StockDataDownloader()
    
    # 测试下载多只股票的近期数据
    stock_codes = ["000617", "000001"]
    results = downloader.batch_download(stock_codes, "20240701", "20240731", delay=1.0)
    
    success_count = sum(results.values())
    print(f"📊 批量下载结果: {success_count}/{len(stock_codes)} 成功")
    
    if success_count > 0:
        print("✅ 批量下载测试通过")
        return True
    else:
        print("❌ 批量下载测试失败")
        return False


def test_data_sources():
    """测试数据源可用性"""
    print("\n🧪 测试数据源可用性...")
    
    downloader = StockDataDownloader()
    
    print(f"📋 可用数据源: {[ds['name'] for ds in downloader.data_sources]}")
    
    # 测试每个数据源
    for data_source in downloader.data_sources:
        if not data_source['available']:
            continue
        
        print(f"\n🔍 测试 {data_source['name']}...")
        
        try:
            if data_source['name'] == 'baostock':
                df = downloader.download_stock_data_baostock("000617", "20240701", "20240705")
            elif data_source['name'] == 'akshare':
                df = downloader.download_stock_data_akshare("000617", "20240701", "20240705")
            else:
                continue
            
            if df is not None and not df.empty:
                print(f"✅ {data_source['name']} 测试通过，获取 {len(df)} 条数据")
            else:
                print(f"⚠️ {data_source['name']} 未获取到数据")
                
        except Exception as e:
            print(f"❌ {data_source['name']} 测试失败: {e}")


def test_file_format():
    """测试文件格式"""
    print("\n🧪 测试文件格式...")
    
    # 查找最新的互联网数据文件
    output_dir = "H:/MPV1.17/T0002/signals/"
    internet_files = [f for f in os.listdir(output_dir) if f.endswith("_来源互联网.txt")]
    
    if not internet_files:
        print("❌ 未找到互联网数据文件")
        return False
    
    # 测试最新文件
    latest_file = max(internet_files, key=lambda x: os.path.getmtime(os.path.join(output_dir, x)))
    filepath = os.path.join(output_dir, latest_file)
    
    print(f"📁 测试文件: {latest_file}")
    
    try:
        df = pd.read_csv(filepath, sep='|')
        
        # 验证基本格式
        expected_columns = ['股票编码', '时间', '买卖差', '当日收盘价C', '前复权收盘价C', '路径总长', '主买', '主卖']
        
        format_checks = {
            '列数正确': len(df.columns) == len(expected_columns),
            '列名正确': list(df.columns) == expected_columns,
            '有数据行': len(df) > 0,
            '股票编码非空': df['股票编码'].notna().all(),
            '股票编码格式正确': df['股票编码'].astype(str).str.len().eq(6).all(),
            '时间格式正确': df['时间'].astype(str).str.len().eq(8).all(),
            '价格为数值': pd.api.types.is_numeric_dtype(df['当日收盘价C']),
            '前复权价格为数值': pd.api.types.is_numeric_dtype(df['前复权收盘价C']),
            '价格有差异': abs(df['当日收盘价C'].iloc[0] - df['前复权收盘价C'].iloc[0]) > 0.01 if len(df) > 0 else False
        }
        
        print("📋 格式检查结果:")
        all_passed = True
        for check_name, passed in format_checks.items():
            status = "✅" if passed else "❌"
            print(f"   {status} {check_name}")
            if not passed:
                all_passed = False
        
        if all_passed:
            print("✅ 文件格式测试通过")
            return True
        else:
            print("❌ 文件格式测试失败")
            return False
            
    except Exception as e:
        print(f"❌ 文件格式测试异常: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 MythQuant 互联网股票数据下载功能测试")
    print("=" * 80)
    
    test_results = []
    
    # 运行各项测试
    test_results.append(("数据源可用性", test_data_sources()))
    test_results.append(("单只股票下载", test_single_stock_download()))
    test_results.append(("批量下载", test_batch_download()))
    test_results.append(("文件格式", test_file_format()))
    
    # 统计结果
    passed_count = sum(1 for _, passed in test_results if passed)
    total_count = len(test_results)
    
    print("\n" + "=" * 80)
    print("📊 测试结果统计")
    print("=" * 80)
    
    for test_name, passed in test_results:
        status = "✅ 通过" if passed else "❌ 失败"
        print(f"{status} {test_name}")
    
    print(f"\n📈 总体结果: {passed_count}/{total_count} 测试通过")
    print(f"🎯 通过率: {passed_count/total_count*100:.1f}%")
    
    if passed_count == total_count:
        print("\n🎉 所有测试通过！互联网数据下载功能正常工作")
        return 0
    else:
        print(f"\n⚠️ 有 {total_count - passed_count} 个测试失败，请检查相关功能")
        return 1


if __name__ == '__main__':
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
