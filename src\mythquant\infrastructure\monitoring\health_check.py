#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
健康检查模块

提供系统健康状态检查功能
"""

import time
from enum import Enum
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


class HealthStatus(Enum):
    """健康状态枚举"""
    HEALTHY = "healthy"
    DEGRADED = "degraded"
    UNHEALTHY = "unhealthy"
    UNKNOWN = "unknown"


@dataclass
class HealthCheck:
    """健康检查结果"""
    name: str
    status: HealthStatus
    message: str = ""
    details: Dict[str, Any] = None
    timestamp: float = None
    duration: float = 0.0
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
        if self.details is None:
            self.details = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'status': self.status.value,
            'message': self.message,
            'details': self.details,
            'timestamp': self.timestamp,
            'duration': self.duration
        }


class HealthChecker:
    """健康检查器"""
    
    def __init__(self):
        self._checks: Dict[str, Callable[[], HealthCheck]] = {}
        self._last_results: Dict[str, HealthCheck] = {}
    
    def register_check(self, name: str, check_func: Callable[[], HealthCheck]):
        """注册健康检查"""
        self._checks[name] = check_func
        logger.info(f"注册健康检查: {name}")
    
    def unregister_check(self, name: str):
        """取消注册健康检查"""
        if name in self._checks:
            del self._checks[name]
            if name in self._last_results:
                del self._last_results[name]
            logger.info(f"取消注册健康检查: {name}")
    
    def run_check(self, name: str) -> Optional[HealthCheck]:
        """运行单个健康检查"""
        if name not in self._checks:
            return None
        
        start_time = time.time()
        try:
            result = self._checks[name]()
            result.duration = time.time() - start_time
            self._last_results[name] = result
            return result
        except Exception as e:
            duration = time.time() - start_time
            result = HealthCheck(
                name=name,
                status=HealthStatus.UNHEALTHY,
                message=f"健康检查执行失败: {e}",
                duration=duration
            )
            self._last_results[name] = result
            logger.error(f"健康检查失败: {name}, 错误: {e}")
            return result
    
    def run_all_checks(self) -> Dict[str, HealthCheck]:
        """运行所有健康检查"""
        results = {}
        for name in self._checks:
            result = self.run_check(name)
            if result:
                results[name] = result
        return results
    
    def get_overall_status(self) -> HealthStatus:
        """获取整体健康状态"""
        if not self._last_results:
            return HealthStatus.UNKNOWN
        
        statuses = [check.status for check in self._last_results.values()]
        
        if any(status == HealthStatus.UNHEALTHY for status in statuses):
            return HealthStatus.UNHEALTHY
        elif any(status == HealthStatus.DEGRADED for status in statuses):
            return HealthStatus.DEGRADED
        elif all(status == HealthStatus.HEALTHY for status in statuses):
            return HealthStatus.HEALTHY
        else:
            return HealthStatus.UNKNOWN
    
    def get_health_report(self) -> Dict[str, Any]:
        """获取健康报告"""
        overall_status = self.get_overall_status()
        
        return {
            'overall_status': overall_status.value,
            'timestamp': time.time(),
            'checks': {name: check.to_dict() for name, check in self._last_results.items()},
            'summary': {
                'total_checks': len(self._checks),
                'healthy': len([c for c in self._last_results.values() if c.status == HealthStatus.HEALTHY]),
                'degraded': len([c for c in self._last_results.values() if c.status == HealthStatus.DEGRADED]),
                'unhealthy': len([c for c in self._last_results.values() if c.status == HealthStatus.UNHEALTHY])
            }
        }


# 预定义的健康检查函数
def database_health_check() -> HealthCheck:
    """数据库健康检查"""
    try:
        # 简化的数据库检查
        return HealthCheck(
            name="database",
            status=HealthStatus.HEALTHY,
            message="数据库连接正常"
        )
    except Exception as e:
        return HealthCheck(
            name="database",
            status=HealthStatus.UNHEALTHY,
            message=f"数据库连接失败: {e}"
        )


def cache_health_check() -> HealthCheck:
    """缓存健康检查"""
    try:
        # 简化的缓存检查
        return HealthCheck(
            name="cache",
            status=HealthStatus.HEALTHY,
            message="缓存系统正常"
        )
    except Exception as e:
        return HealthCheck(
            name="cache",
            status=HealthStatus.UNHEALTHY,
            message=f"缓存系统异常: {e}"
        )


def memory_health_check() -> HealthCheck:
    """内存健康检查"""
    try:
        # 简化的内存检查
        return HealthCheck(
            name="memory",
            status=HealthStatus.HEALTHY,
            message="内存使用正常",
            details={'usage_percent': 0.0}
        )
    except Exception as e:
        return HealthCheck(
            name="memory",
            status=HealthStatus.UNHEALTHY,
            message=f"内存检查失败: {e}"
        )


def disk_health_check() -> HealthCheck:
    """磁盘健康检查"""
    try:
        # 简化的磁盘检查
        return HealthCheck(
            name="disk",
            status=HealthStatus.HEALTHY,
            message="磁盘空间充足",
            details={'usage_percent': 0.0}
        )
    except Exception as e:
        return HealthCheck(
            name="disk",
            status=HealthStatus.UNHEALTHY,
            message=f"磁盘检查失败: {e}"
        )


# 全局健康检查器
_global_health_checker: Optional[HealthChecker] = None


def get_health_checker() -> HealthChecker:
    """获取全局健康检查器"""
    global _global_health_checker
    if _global_health_checker is None:
        _global_health_checker = HealthChecker()
        
        # 注册默认健康检查
        _global_health_checker.register_check("database", database_health_check)
        _global_health_checker.register_check("cache", cache_health_check)
        _global_health_checker.register_check("memory", memory_health_check)
        _global_health_checker.register_check("disk", disk_health_check)
    
    return _global_health_checker
