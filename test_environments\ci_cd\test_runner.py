#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
CI/CD 测试运行器

自动化测试运行器，用于持续集成环境，确保：
1. 自动发现和运行所有测试套件
2. 生成详细的测试报告
3. 支持不同的测试级别
4. 集成测试结果通知

作者: AI Assistant
创建时间: 2025-08-01
"""

import sys
import os
import json
import subprocess
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any, Optional


class CICDTestRunner:
    """CI/CD 测试运行器"""
    
    def __init__(self, project_root: str = None):
        """
        初始化测试运行器
        
        Args:
            project_root: 项目根目录
        """
        if project_root is None:
            project_root = Path(__file__).parent.parent.parent
        
        self.project_root = Path(project_root)
        self.test_suites_dir = self.project_root / 'test_environments' / 'test_suites'
        self.reports_dir = self.project_root / 'test_environments' / 'reports'
        
        # 确保报告目录存在
        self.reports_dir.mkdir(parents=True, exist_ok=True)
        
        self.test_results = {}
        self.start_time = datetime.now()
    
    def discover_test_suites(self) -> List[str]:
        """
        发现测试套件
        
        Returns:
            List: 测试套件文件列表
        """
        test_suites = []
        
        if self.test_suites_dir.exists():
            for file in self.test_suites_dir.glob('test_*.py'):
                test_suites.append(str(file))
        
        return test_suites
    
    def run_test_suite(self, test_suite_path: str) -> Dict[str, Any]:
        """
        运行单个测试套件
        
        Args:
            test_suite_path: 测试套件路径
            
        Returns:
            Dict: 测试结果
        """
        suite_name = Path(test_suite_path).stem
        
        print(f"🧪 运行测试套件: {suite_name}")
        print("-" * 60)
        
        try:
            # 运行测试套件（处理Windows编码问题）
            result = subprocess.run(
                [sys.executable, test_suite_path],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                encoding='utf-8',
                errors='replace',
                timeout=300  # 5分钟超时
            )
            
            return {
                'suite_name': suite_name,
                'success': result.returncode == 0,
                'return_code': result.returncode,
                'stdout': result.stdout,
                'stderr': result.stderr,
                'duration': 0,  # 实际运行时间需要单独计算
                'timestamp': datetime.now().isoformat()
            }
            
        except subprocess.TimeoutExpired:
            return {
                'suite_name': suite_name,
                'success': False,
                'return_code': -1,
                'stdout': '',
                'stderr': '测试套件运行超时',
                'duration': 300,
                'timestamp': datetime.now().isoformat()
            }
        except Exception as e:
            return {
                'suite_name': suite_name,
                'success': False,
                'return_code': -1,
                'stdout': '',
                'stderr': f'运行测试套件时出错: {e}',
                'duration': 0,
                'timestamp': datetime.now().isoformat()
            }
    
    def run_all_tests(self, test_level: str = 'full') -> Dict[str, Any]:
        """
        运行所有测试
        
        Args:
            test_level: 测试级别 ('quick', 'standard', 'full')
            
        Returns:
            Dict: 完整测试结果
        """
        print(f"🚀 开始CI/CD测试运行")
        print(f"📅 开始时间: {self.start_time.strftime('%Y-%m-%d %H:%M:%S')}")
        print(f"🎯 测试级别: {test_level}")
        print("=" * 100)
        
        # 发现测试套件
        test_suites = self.discover_test_suites()
        
        if not test_suites:
            print("⚠️ 未发现任何测试套件")
            return {
                'success': False,
                'message': '未发现测试套件',
                'suites': [],
                'summary': {
                    'total': 0,
                    'passed': 0,
                    'failed': 0,
                    'pass_rate': 0.0
                }
            }
        
        print(f"📋 发现 {len(test_suites)} 个测试套件:")
        for suite in test_suites:
            print(f"   - {Path(suite).stem}")
        print()
        
        # 运行测试套件
        suite_results = []
        passed_suites = 0
        
        for suite_path in test_suites:
            suite_start = datetime.now()
            result = self.run_test_suite(suite_path)
            suite_end = datetime.now()
            
            result['duration'] = (suite_end - suite_start).total_seconds()
            suite_results.append(result)
            
            if result['success']:
                passed_suites += 1
                print(f"✅ {result['suite_name']} 通过 ({result['duration']:.1f}s)")
            else:
                print(f"❌ {result['suite_name']} 失败 ({result['duration']:.1f}s)")
                if result['stderr']:
                    print(f"   错误: {result['stderr'][:200]}...")
            print()
        
        # 生成汇总结果
        total_suites = len(suite_results)
        pass_rate = passed_suites / total_suites if total_suites > 0 else 0.0
        
        summary = {
            'total': total_suites,
            'passed': passed_suites,
            'failed': total_suites - passed_suites,
            'pass_rate': pass_rate,
            'duration': (datetime.now() - self.start_time).total_seconds()
        }
        
        overall_result = {
            'success': pass_rate >= 0.8,  # 80%通过率为成功
            'test_level': test_level,
            'start_time': self.start_time.isoformat(),
            'end_time': datetime.now().isoformat(),
            'suites': suite_results,
            'summary': summary
        }
        
        # 保存结果
        self.save_test_results(overall_result)
        
        # 显示汇总
        self.print_summary(overall_result)
        
        return overall_result
    
    def save_test_results(self, results: Dict[str, Any]):
        """
        保存测试结果
        
        Args:
            results: 测试结果
        """
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        
        # 保存JSON格式结果
        json_file = self.reports_dir / f'test_results_{timestamp}.json'
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(results, f, indent=2, ensure_ascii=False)
        
        # 保存HTML格式报告
        html_file = self.reports_dir / f'test_report_{timestamp}.html'
        html_content = self.generate_html_report(results)
        with open(html_file, 'w', encoding='utf-8') as f:
            f.write(html_content)
        
        print(f"📊 测试结果已保存:")
        print(f"   JSON: {json_file}")
        print(f"   HTML: {html_file}")
    
    def generate_html_report(self, results: Dict[str, Any]) -> str:
        """
        生成HTML测试报告
        
        Args:
            results: 测试结果
            
        Returns:
            str: HTML内容
        """
        summary = results['summary']
        
        html = f"""
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MythQuant 测试报告</title>
    <style>
        body {{ font-family: Arial, sans-serif; margin: 20px; }}
        .header {{ background: #f5f5f5; padding: 20px; border-radius: 5px; }}
        .summary {{ margin: 20px 0; }}
        .suite {{ margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }}
        .success {{ background: #d4edda; border-color: #c3e6cb; }}
        .failure {{ background: #f8d7da; border-color: #f5c6cb; }}
        .details {{ margin-top: 10px; font-family: monospace; font-size: 12px; }}
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 MythQuant 测试报告</h1>
        <p><strong>测试时间:</strong> {results['start_time']} - {results['end_time']}</p>
        <p><strong>测试级别:</strong> {results['test_level']}</p>
        <p><strong>总体结果:</strong> {'✅ 通过' if results['success'] else '❌ 失败'}</p>
    </div>
    
    <div class="summary">
        <h2>📊 测试汇总</h2>
        <ul>
            <li>总测试套件数: {summary['total']}</li>
            <li>通过: {summary['passed']}</li>
            <li>失败: {summary['failed']}</li>
            <li>通过率: {summary['pass_rate']:.1%}</li>
            <li>总耗时: {summary['duration']:.1f}秒</li>
        </ul>
    </div>
    
    <div class="suites">
        <h2>🧪 测试套件详情</h2>
"""
        
        for suite in results['suites']:
            status_class = 'success' if suite['success'] else 'failure'
            status_icon = '✅' if suite['success'] else '❌'
            
            html += f"""
        <div class="suite {status_class}">
            <h3>{status_icon} {suite['suite_name']}</h3>
            <p><strong>结果:</strong> {'通过' if suite['success'] else '失败'}</p>
            <p><strong>耗时:</strong> {suite['duration']:.1f}秒</p>
            <p><strong>返回码:</strong> {suite['return_code']}</p>
"""
            
            if suite['stderr']:
                html += f"""
            <div class="details">
                <strong>错误信息:</strong><br>
                <pre>{suite['stderr'][:1000]}</pre>
            </div>
"""
            
            html += "        </div>\n"
        
        html += """
    </div>
</body>
</html>
"""
        
        return html
    
    def print_summary(self, results: Dict[str, Any]):
        """
        打印测试汇总
        
        Args:
            results: 测试结果
        """
        summary = results['summary']
        
        print("📊 CI/CD测试汇总")
        print("=" * 100)
        
        print(f"🎯 总体结果: {'✅ 通过' if results['success'] else '❌ 失败'}")
        print(f"📈 统计信息:")
        print(f"   总测试套件数: {summary['total']}")
        print(f"   通过: {summary['passed']}")
        print(f"   失败: {summary['failed']}")
        print(f"   通过率: {summary['pass_rate']:.1%}")
        print(f"   总耗时: {summary['duration']:.1f}秒")
        
        if results['success']:
            print(f"\n🎉 所有测试通过！系统状态良好")
        else:
            print(f"\n⚠️ 部分测试失败，需要检查和修复")


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='MythQuant CI/CD 测试运行器')
    parser.add_argument('--level', choices=['quick', 'standard', 'full'], 
                       default='standard', help='测试级别')
    parser.add_argument('--project-root', help='项目根目录')
    
    args = parser.parse_args()
    
    # 创建测试运行器
    runner = CICDTestRunner(args.project_root)
    
    # 运行测试
    results = runner.run_all_tests(args.level)
    
    # 返回适当的退出码
    return 0 if results['success'] else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
