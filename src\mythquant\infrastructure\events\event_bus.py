#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件总线实现

提供同步和异步的事件发布订阅机制
"""

import asyncio
import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Type, Callable, Any, Optional, Set
from datetime import datetime
from collections import defaultdict
import threading
from concurrent.futures import ThreadPoolExecutor

from ...shared.patterns.architectural_patterns import DomainEvent
from ...domain.exceptions import DomainException


logger = logging.getLogger(__name__)


class EventBus(ABC):
    """事件总线抽象基类"""
    
    @abstractmethod
    def subscribe(self, event_type: Type[DomainEvent], handler: Callable[[DomainEvent], None]):
        """订阅事件"""
        pass
    
    @abstractmethod
    def unsubscribe(self, event_type: Type[DomainEvent], handler: Callable[[DomainEvent], None]):
        """取消订阅"""
        pass
    
    @abstractmethod
    def publish(self, event: DomainEvent):
        """发布事件"""
        pass
    
    @abstractmethod
    def publish_batch(self, events: List[DomainEvent]):
        """批量发布事件"""
        pass


class InMemoryEventBus(EventBus):
    """内存事件总线"""
    
    def __init__(self, max_workers: int = 4):
        self._handlers: Dict[Type[DomainEvent], List[Callable]] = defaultdict(list)
        self._global_handlers: List[Callable] = []
        self._middleware: List[Callable] = []
        self._lock = threading.RLock()
        self._executor = ThreadPoolExecutor(max_workers=max_workers)
        self._metrics = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0,
            'handlers_count': 0
        }
    
    def subscribe(self, event_type: Type[DomainEvent], handler: Callable[[DomainEvent], None]):
        """订阅特定类型的事件"""
        with self._lock:
            if handler not in self._handlers[event_type]:
                self._handlers[event_type].append(handler)
                self._metrics['handlers_count'] += 1
                logger.debug(f"订阅事件处理器: {event_type.__name__} -> {handler.__name__}")
    
    def subscribe_all(self, handler: Callable[[DomainEvent], None]):
        """订阅所有事件"""
        with self._lock:
            if handler not in self._global_handlers:
                self._global_handlers.append(handler)
                self._metrics['handlers_count'] += 1
                logger.debug(f"订阅全局事件处理器: {handler.__name__}")
    
    def unsubscribe(self, event_type: Type[DomainEvent], handler: Callable[[DomainEvent], None]):
        """取消订阅"""
        with self._lock:
            if handler in self._handlers[event_type]:
                self._handlers[event_type].remove(handler)
                self._metrics['handlers_count'] -= 1
                logger.debug(f"取消订阅事件处理器: {event_type.__name__} -> {handler.__name__}")
    
    def unsubscribe_all(self, handler: Callable[[DomainEvent], None]):
        """取消全局订阅"""
        with self._lock:
            if handler in self._global_handlers:
                self._global_handlers.remove(handler)
                self._metrics['handlers_count'] -= 1
                logger.debug(f"取消全局事件处理器: {handler.__name__}")
    
    def add_middleware(self, middleware: Callable[[DomainEvent], DomainEvent]):
        """添加中间件"""
        with self._lock:
            self._middleware.append(middleware)
            logger.debug(f"添加事件中间件: {middleware.__name__}")
    
    def publish(self, event: DomainEvent):
        """发布单个事件"""
        try:
            self._metrics['events_published'] += 1
            
            # 应用中间件
            processed_event = self._apply_middleware(event)
            if processed_event is None:
                logger.debug(f"事件被中间件过滤: {event.event_type}")
                return
            
            # 获取处理器
            handlers = self._get_handlers(type(processed_event))
            
            if not handlers:
                logger.debug(f"没有找到事件处理器: {processed_event.event_type}")
                return
            
            # 执行处理器
            for handler in handlers:
                self._execute_handler(handler, processed_event)
            
            logger.debug(f"事件发布成功: {processed_event.event_type}, 处理器数量: {len(handlers)}")
            
        except Exception as e:
            self._metrics['events_failed'] += 1
            logger.error(f"事件发布失败: {event.event_type}, 错误: {e}")
            raise
    
    def publish_batch(self, events: List[DomainEvent]):
        """批量发布事件"""
        if not events:
            return
        
        logger.debug(f"批量发布事件: {len(events)} 个")
        
        for event in events:
            try:
                self.publish(event)
            except Exception as e:
                logger.error(f"批量发布中的事件失败: {event.event_type}, 错误: {e}")
                # 继续处理其他事件
                continue
    
    def _apply_middleware(self, event: DomainEvent) -> Optional[DomainEvent]:
        """应用中间件"""
        current_event = event
        
        for middleware in self._middleware:
            try:
                current_event = middleware(current_event)
                if current_event is None:
                    return None
            except Exception as e:
                logger.error(f"中间件执行失败: {middleware.__name__}, 错误: {e}")
                raise
        
        return current_event
    
    def _get_handlers(self, event_type: Type[DomainEvent]) -> List[Callable]:
        """获取事件处理器"""
        with self._lock:
            handlers = []
            
            # 特定类型的处理器
            handlers.extend(self._handlers[event_type])
            
            # 全局处理器
            handlers.extend(self._global_handlers)
            
            return handlers
    
    def _execute_handler(self, handler: Callable, event: DomainEvent):
        """执行事件处理器"""
        try:
            # 提交到线程池执行
            future = self._executor.submit(handler, event)
            # 可以选择等待结果或异步处理
            future.result(timeout=30)  # 30秒超时
            
            self._metrics['events_processed'] += 1
            
        except Exception as e:
            self._metrics['events_failed'] += 1
            logger.error(f"事件处理器执行失败: {handler.__name__}, 事件: {event.event_type}, 错误: {e}")
            # 不重新抛出异常，避免影响其他处理器
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取事件总线指标"""
        with self._lock:
            return {
                **self._metrics,
                'active_subscriptions': sum(len(handlers) for handlers in self._handlers.values()),
                'global_handlers': len(self._global_handlers),
                'middleware_count': len(self._middleware)
            }
    
    def clear_all(self):
        """清除所有订阅"""
        with self._lock:
            self._handlers.clear()
            self._global_handlers.clear()
            self._middleware.clear()
            self._metrics['handlers_count'] = 0
            logger.info("清除所有事件订阅")
    
    def shutdown(self):
        """关闭事件总线"""
        logger.info("关闭事件总线...")
        self._executor.shutdown(wait=True)
        self.clear_all()


class AsyncEventBus(EventBus):
    """异步事件总线"""
    
    def __init__(self, max_concurrent_handlers: int = 10):
        self._handlers: Dict[Type[DomainEvent], List[Callable]] = defaultdict(list)
        self._global_handlers: List[Callable] = []
        self._middleware: List[Callable] = []
        self._semaphore = asyncio.Semaphore(max_concurrent_handlers)
        self._metrics = {
            'events_published': 0,
            'events_processed': 0,
            'events_failed': 0,
            'handlers_count': 0
        }
    
    def subscribe(self, event_type: Type[DomainEvent], handler: Callable[[DomainEvent], None]):
        """订阅事件"""
        if handler not in self._handlers[event_type]:
            self._handlers[event_type].append(handler)
            self._metrics['handlers_count'] += 1
            logger.debug(f"异步订阅事件处理器: {event_type.__name__} -> {handler.__name__}")
    
    def subscribe_all(self, handler: Callable[[DomainEvent], None]):
        """订阅所有事件"""
        if handler not in self._global_handlers:
            self._global_handlers.append(handler)
            self._metrics['handlers_count'] += 1
            logger.debug(f"异步订阅全局事件处理器: {handler.__name__}")
    
    def unsubscribe(self, event_type: Type[DomainEvent], handler: Callable[[DomainEvent], None]):
        """取消订阅"""
        if handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            self._metrics['handlers_count'] -= 1
            logger.debug(f"取消异步事件处理器: {event_type.__name__} -> {handler.__name__}")
    
    def add_middleware(self, middleware: Callable[[DomainEvent], DomainEvent]):
        """添加中间件"""
        self._middleware.append(middleware)
        logger.debug(f"添加异步事件中间件: {middleware.__name__}")
    
    async def publish_async(self, event: DomainEvent):
        """异步发布事件"""
        try:
            self._metrics['events_published'] += 1
            
            # 应用中间件
            processed_event = await self._apply_middleware_async(event)
            if processed_event is None:
                logger.debug(f"事件被中间件过滤: {event.event_type}")
                return
            
            # 获取处理器
            handlers = self._get_handlers(type(processed_event))
            
            if not handlers:
                logger.debug(f"没有找到事件处理器: {processed_event.event_type}")
                return
            
            # 并发执行处理器
            tasks = [self._execute_handler_async(handler, processed_event) for handler in handlers]
            await asyncio.gather(*tasks, return_exceptions=True)
            
            logger.debug(f"异步事件发布成功: {processed_event.event_type}, 处理器数量: {len(handlers)}")
            
        except Exception as e:
            self._metrics['events_failed'] += 1
            logger.error(f"异步事件发布失败: {event.event_type}, 错误: {e}")
            raise
    
    def publish(self, event: DomainEvent):
        """同步发布事件（在异步总线中创建任务）"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            # 如果在异步上下文中，创建任务
            asyncio.create_task(self.publish_async(event))
        else:
            # 如果在同步上下文中，运行事件循环
            loop.run_until_complete(self.publish_async(event))
    
    async def publish_batch_async(self, events: List[DomainEvent]):
        """异步批量发布事件"""
        if not events:
            return
        
        logger.debug(f"异步批量发布事件: {len(events)} 个")
        
        tasks = [self.publish_async(event) for event in events]
        results = await asyncio.gather(*tasks, return_exceptions=True)
        
        # 统计失败的事件
        failed_count = sum(1 for result in results if isinstance(result, Exception))
        if failed_count > 0:
            logger.warning(f"批量发布中有 {failed_count} 个事件失败")
    
    def publish_batch(self, events: List[DomainEvent]):
        """同步批量发布事件"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(self.publish_batch_async(events))
        else:
            loop.run_until_complete(self.publish_batch_async(events))
    
    async def _apply_middleware_async(self, event: DomainEvent) -> Optional[DomainEvent]:
        """异步应用中间件"""
        current_event = event
        
        for middleware in self._middleware:
            try:
                if asyncio.iscoroutinefunction(middleware):
                    current_event = await middleware(current_event)
                else:
                    current_event = middleware(current_event)
                
                if current_event is None:
                    return None
            except Exception as e:
                logger.error(f"异步中间件执行失败: {middleware.__name__}, 错误: {e}")
                raise
        
        return current_event
    
    def _get_handlers(self, event_type: Type[DomainEvent]) -> List[Callable]:
        """获取事件处理器"""
        handlers = []
        handlers.extend(self._handlers[event_type])
        handlers.extend(self._global_handlers)
        return handlers
    
    async def _execute_handler_async(self, handler: Callable, event: DomainEvent):
        """异步执行事件处理器"""
        async with self._semaphore:  # 限制并发数
            try:
                if asyncio.iscoroutinefunction(handler):
                    await handler(event)
                else:
                    # 在线程池中执行同步处理器
                    loop = asyncio.get_event_loop()
                    await loop.run_in_executor(None, handler, event)
                
                self._metrics['events_processed'] += 1
                
            except Exception as e:
                self._metrics['events_failed'] += 1
                logger.error(f"异步事件处理器执行失败: {handler.__name__}, 事件: {event.event_type}, 错误: {e}")
    
    def get_metrics(self) -> Dict[str, Any]:
        """获取事件总线指标"""
        return {
            **self._metrics,
            'active_subscriptions': sum(len(handlers) for handlers in self._handlers.values()),
            'global_handlers': len(self._global_handlers),
            'middleware_count': len(self._middleware)
        }
    
    def clear_all(self):
        """清除所有订阅"""
        self._handlers.clear()
        self._global_handlers.clear()
        self._middleware.clear()
        self._metrics['handlers_count'] = 0
        logger.info("清除所有异步事件订阅")


# 事件总线工厂
def create_event_bus(bus_type: str = "memory", **kwargs) -> EventBus:
    """创建事件总线"""
    if bus_type == "memory":
        return InMemoryEventBus(**kwargs)
    elif bus_type == "async":
        return AsyncEventBus(**kwargs)
    else:
        raise ValueError(f"不支持的事件总线类型: {bus_type}")


# 全局事件总线实例
_global_event_bus: Optional[EventBus] = None


def get_event_bus() -> EventBus:
    """获取全局事件总线"""
    global _global_event_bus
    if _global_event_bus is None:
        _global_event_bus = create_event_bus("memory")
    return _global_event_bus


def set_event_bus(event_bus: EventBus):
    """设置全局事件总线"""
    global _global_event_bus
    _global_event_bus = event_bus
