# MythQuant REST API 文档

## 📋 API 概览

MythQuant提供RESTful API接口，支持股票数据查询、算法计算、监控管理等功能。所有API都遵循OpenAPI 3.0规范，支持自动文档生成。

### 🔗 API 访问地址
- **开发环境**: `http://localhost:8000`
- **测试环境**: `https://test-api.mythquant.com`
- **生产环境**: `https://api.mythquant.com`

### 📚 文档地址
- **Swagger UI**: `/docs`
- **ReDoc**: `/redoc`
- **OpenAPI Schema**: `/openapi.json`

## 🔐 认证授权

### API Key 认证
```http
GET /api/v1/stocks/000001
Authorization: Bearer your-api-key-here
Content-Type: application/json
```

### JWT Token 认证
```http
POST /api/v1/auth/login
Content-Type: application/json

{
  "username": "your-username",
  "password": "your-password"
}
```

响应:
```json
{
  "access_token": "eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9...",
  "token_type": "bearer",
  "expires_in": 3600
}
```

## 📊 核心API接口

### 1. 股票数据API

#### 获取股票信息
```http
GET /api/v1/stocks/{stock_code}
```

**参数**:
- `stock_code` (string): 股票代码，如 "000001"

**响应**:
```json
{
  "stock_code": "000001",
  "stock_name": "平安银行",
  "market": "SZ",
  "current_price": 12.50,
  "change": 0.15,
  "change_percent": 1.22,
  "volume": 1234567,
  "market_cap": 2345678901,
  "pe_ratio": 8.5,
  "pb_ratio": 0.85,
  "updated_at": "2024-12-20T15:00:00Z"
}
```

#### 获取股票历史数据
```http
GET /api/v1/stocks/{stock_code}/history
```

**查询参数**:
- `start_date` (string): 开始日期，格式 YYYY-MM-DD
- `end_date` (string): 结束日期，格式 YYYY-MM-DD
- `frequency` (string): 数据频率，可选值: day, hour, minute
- `adjust` (string): 复权类型，可选值: none, forward, backward

**响应**:
```json
{
  "stock_code": "000001",
  "frequency": "day",
  "adjust": "forward",
  "data": [
    {
      "date": "2024-12-20",
      "open": 12.35,
      "high": 12.60,
      "low": 12.30,
      "close": 12.50,
      "volume": 1234567,
      "amount": 15432109.50
    }
  ],
  "total_count": 100,
  "page": 1,
  "page_size": 50
}
```

#### 获取股票实时数据
```http
GET /api/v1/stocks/{stock_code}/realtime
```

**响应**:
```json
{
  "stock_code": "000001",
  "current_price": 12.50,
  "change": 0.15,
  "change_percent": 1.22,
  "volume": 1234567,
  "amount": 15432109.50,
  "bid_price": 12.49,
  "ask_price": 12.51,
  "bid_volume": 1000,
  "ask_volume": 2000,
  "timestamp": "2024-12-20T15:00:00Z"
}
```

### 2. 算法计算API

#### 计算技术指标
```http
POST /api/v1/algorithms/indicators
```

**请求体**:
```json
{
  "stock_code": "000001",
  "indicators": [
    {
      "name": "sma",
      "period": 20
    },
    {
      "name": "rsi",
      "period": 14
    },
    {
      "name": "macd",
      "fast_period": 12,
      "slow_period": 26,
      "signal_period": 9
    }
  ],
  "start_date": "2024-01-01",
  "end_date": "2024-12-20"
}
```

**响应**:
```json
{
  "stock_code": "000001",
  "calculation_id": "calc_123456789",
  "results": {
    "sma": [12.1, 12.2, 12.3, 12.4, 12.5],
    "rsi": [45.2, 48.1, 52.3, 55.7, 58.9],
    "macd": {
      "macd_line": [0.05, 0.08, 0.12, 0.15, 0.18],
      "signal_line": [0.03, 0.06, 0.09, 0.12, 0.15],
      "histogram": [0.02, 0.02, 0.03, 0.03, 0.03]
    }
  },
  "metadata": {
    "calculation_time": "2024-12-20T15:00:00Z",
    "data_points": 100,
    "execution_time_ms": 150
  }
}
```

#### 获取算法计算状态
```http
GET /api/v1/algorithms/calculations/{calculation_id}
```

**响应**:
```json
{
  "calculation_id": "calc_123456789",
  "status": "completed",
  "progress": 100,
  "started_at": "2024-12-20T14:59:30Z",
  "completed_at": "2024-12-20T15:00:00Z",
  "execution_time_ms": 150,
  "result_url": "/api/v1/algorithms/calculations/calc_123456789/result"
}
```

### 3. 监控管理API

#### 获取系统健康状态
```http
GET /api/v1/health
```

**响应**:
```json
{
  "status": "healthy",
  "timestamp": "2024-12-20T15:00:00Z",
  "version": "2.0.0",
  "uptime": 86400,
  "checks": {
    "database": {
      "status": "healthy",
      "response_time_ms": 5,
      "last_check": "2024-12-20T15:00:00Z"
    },
    "cache": {
      "status": "healthy",
      "hit_rate": 0.95,
      "last_check": "2024-12-20T15:00:00Z"
    },
    "external_apis": {
      "status": "healthy",
      "available_sources": 3,
      "last_check": "2024-12-20T15:00:00Z"
    }
  }
}
```

#### 获取性能指标
```http
GET /api/v1/metrics
```

**查询参数**:
- `metric_names` (array): 指标名称列表
- `start_time` (string): 开始时间，ISO 8601格式
- `end_time` (string): 结束时间，ISO 8601格式
- `step` (string): 时间步长，如 "1m", "5m", "1h"

**响应**:
```json
{
  "metrics": {
    "cpu_usage": {
      "values": [
        ["2024-12-20T14:55:00Z", 45.2],
        ["2024-12-20T14:56:00Z", 47.1],
        ["2024-12-20T14:57:00Z", 44.8]
      ],
      "unit": "percent"
    },
    "memory_usage": {
      "values": [
        ["2024-12-20T14:55:00Z", 1024],
        ["2024-12-20T14:56:00Z", 1056],
        ["2024-12-20T14:57:00Z", 1032]
      ],
      "unit": "MB"
    }
  },
  "query_time_ms": 25
}
```

### 4. 配置管理API

#### 获取系统配置
```http
GET /api/v1/config
```

**响应**:
```json
{
  "system": {
    "debug_mode": false,
    "log_level": "INFO",
    "max_workers": 4
  },
  "data_sources": {
    "primary_source": "tdx",
    "backup_sources": ["pytdx", "tushare"],
    "cache_ttl": 300
  },
  "algorithms": {
    "default_period": 20,
    "max_calculation_time": 30,
    "cache_results": true
  }
}
```

#### 更新系统配置
```http
PUT /api/v1/config
```

**请求体**:
```json
{
  "system": {
    "log_level": "DEBUG",
    "max_workers": 8
  },
  "data_sources": {
    "cache_ttl": 600
  }
}
```

## 📝 数据模型

### Stock 模型
```json
{
  "stock_code": "string",
  "stock_name": "string", 
  "market": "string",
  "industry": "string",
  "sector": "string",
  "listing_date": "string",
  "total_shares": "number",
  "float_shares": "number",
  "market_cap": "number",
  "created_at": "string",
  "updated_at": "string"
}
```

### PriceData 模型
```json
{
  "stock_code": "string",
  "date": "string",
  "open": "number",
  "high": "number", 
  "low": "number",
  "close": "number",
  "volume": "number",
  "amount": "number",
  "adjust_factor": "number"
}
```

### Indicator 模型
```json
{
  "name": "string",
  "display_name": "string",
  "description": "string",
  "parameters": {
    "period": "number",
    "additional_params": "object"
  },
  "output_type": "string",
  "category": "string"
}
```

## 🚨 错误处理

### 错误响应格式
```json
{
  "error": {
    "code": "STOCK_NOT_FOUND",
    "message": "Stock with code '999999' not found",
    "details": {
      "stock_code": "999999",
      "suggestion": "Please check the stock code and try again"
    },
    "timestamp": "2024-12-20T15:00:00Z",
    "request_id": "req_123456789"
  }
}
```

### 常见错误码
- `400 BAD_REQUEST`: 请求参数错误
- `401 UNAUTHORIZED`: 认证失败
- `403 FORBIDDEN`: 权限不足
- `404 NOT_FOUND`: 资源不存在
- `429 RATE_LIMIT_EXCEEDED`: 请求频率超限
- `500 INTERNAL_SERVER_ERROR`: 服务器内部错误
- `503 SERVICE_UNAVAILABLE`: 服务不可用

---

**API版本**: v1.0  
**最后更新**: 2024-12-XX  
**维护者**: API团队
