2025-07-31 23:20:55,413 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250731_232055.log
2025-07-31 23:20:55,413 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-31 23:20:55,413 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-31 23:20:55,413 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-31 23:20:55,413 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-31 23:20:55,413 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-31 23:20:55,414 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-31 23:20:55,414 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-31 23:20:55,414 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-31 23:20:55,441 - Main - INFO - info:307 - ℹ️ 🎯 获取特定分钟数据: 000617 @ 202507251500
2025-07-31 23:20:55,441 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-31 23:20:55,636 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1200条 (目标日期: 20250725, 交易日: 5天, 频率: 1min)
2025-07-31 23:20:55,636 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1200条 -> 实际请求800条 (配置限制:800)
2025-07-31 23:20:55,636 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-31 23:20:55,636 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-31 23:20:55,690 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需400条
2025-07-31 23:20:55,920 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +400条，总计1200条
2025-07-31 23:20:55,920 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1200条数据
2025-07-31 23:20:55,921 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1200条，实际获取1200条
2025-07-31 23:20:55,926 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-31 23:20:55,929 - Main - INFO - info:307 - ℹ️ ℹ️ pytdx提供未复权原始价格，正在计算前复权价格以完善数据字段...
2025-07-31 23:20:55,929 - Main - INFO - info:307 - ℹ️ 🔄 pytdx前复权计算: 000617
2025-07-31 23:20:55,929 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-31 23:20:55,929 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-31 23:20:56,416 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-31 23:20:56,416 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-31 23:20:56,423 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-31 23:20:56,423 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17/T0002
2025-07-31 23:20:56,423 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-31 23:20:56,424 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-31 23:20:56,424 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 未找到任何市场的分钟数据文件
2025-07-31 23:20:56,424 - main_v20230219_optimized - WARNING - _get_optimal_tdx_path:255 - ⚠️  主路径下未找到分钟数据文件，但将继续使用配置路径
2025-07-31 23:20:56,424 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17/T0002，缓存方法: memory
2025-07-31 23:21:21,612 - main_v20230219_optimized - INFO - _ensure_pickle_cache:441 - 🔄 更新pickle缓存...
2025-07-31 23:21:21,612 - main_v20230219_optimized - ERROR - _ensure_pickle_cache:495 - ❌ pickle缓存更新失败: 'StockDataProcessor' object has no attribute 'tdx_path'
2025-07-31 23:21:22,171 - main_v20230219_optimized - WARNING - _extract_stock_code_from_data:2442 - 无法从数据中提取股票代码，使用target_stocks中的第一个
2025-07-31 23:21:22,194 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 240条
2025-07-31 23:21:22,194 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功，处理了20个除权事件
2025-07-31 23:21:22,206 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功
2025-07-31 23:21:22,206 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价格与原始价格相同，可能无除权事件
2025-07-31 23:21:22,206 - Main - INFO - info:307 - ℹ️ 📊 获取到 240 条数据，查找目标时间 202507251500
2025-07-31 23:21:22,211 - Main - INFO - info:307 - ℹ️ ✅ 找到目标数据: 收盘价=8.900, 前复权=nan
2025-07-31 23:21:22,211 - Main - INFO - info:307 - ℹ️ ✅ 增量下载前提条件验证通过
2025-07-31 23:21:22,212 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-31 23:21:22,212 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-31 23:21:22,267 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数21291, 覆盖89个交易日
2025-07-31 23:21:22,267 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-07-31 23:21:22,267 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-07-31 23:21:22,267 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-07-31 23:21:22,268 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-07-31 23:21:22,268 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-07-31 23:21:22,268 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-07-31 23:21:22,268 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-07-31 23:21:22,268 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-07-31 23:21:22,268 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-07-31 23:21:22,268 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-07-31 23:21:22,633 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-31 23:21:22,633 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-31 23:21:22,634 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_202503180931-202507251500_来源互联网（202507312316）.txt
2025-07-31 23:21:22,634 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_202503180931-202507251500_来源互联网（202507312316）.txt
2025-07-31 23:21:22,634 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-31 23:21:22,652 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-07-31 23:21:22,871 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-07-31 23:21:22,873 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-31 23:21:22,873 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-31 23:21:22,873 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-31 23:21:23,056 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1200条 (目标日期: 20250725, 交易日: 5天, 频率: 1min)
2025-07-31 23:21:23,056 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1200条 -> 实际请求800条 (配置限制:800)
2025-07-31 23:21:23,056 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-31 23:21:23,056 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-31 23:21:23,109 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需400条
2025-07-31 23:21:23,337 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +400条，总计1200条
2025-07-31 23:21:23,337 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1200条数据
2025-07-31 23:21:23,337 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1200条，实际获取1200条
2025-07-31 23:21:23,341 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-31 23:21:23,348 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-31 23:21:23,348 - Main - WARNING - warning:311 - ⚠️ ⚠️ 无法解析文件名格式
2025-07-31 23:21:23,348 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-31 23:21:23,348 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-31 23:21:23,348 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-31 23:21:23,348 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-31 23:21:23,525 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 33360条 (目标日期: 20250101, 交易日: 139天, 频率: 1min)
2025-07-31 23:21:23,525 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计33360条 -> 实际请求800条 (配置限制:800)
2025-07-31 23:21:23,525 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-31 23:21:23,525 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-31 23:21:23,576 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32560条
2025-07-31 23:21:23,809 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-31 23:21:24,031 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-31 23:21:24,262 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-31 23:21:24,479 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
