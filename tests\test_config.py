#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 测试环境专用配置

与 user_config.py 分离，专门用于测试环境管理
确保测试永远使用生产函数，但使用测试路径和测试素材

设计原则：
1. 测试永远使用生产函数
2. 路径和素材通过配置分离
3. 测试素材保鲜（每次测试都是新鲜状态）
4. 配置文件与用户配置完全分离

作者: AI Assistant
创建时间: 2025-08-01
"""

import os
import sys
from pathlib import Path
from typing import Dict, Optional, Union

# ==================== 测试环境基础配置 ====================
TEST_CONFIG = {
    # 测试模式控制
    'enabled': False,  # 是否启用测试模式（默认关闭，通过环境变量或代码控制）
    'auto_detect': True,  # 是否自动检测测试环境
    'debug_mode': True,  # 测试环境调试模式
    
    # 测试环境路径配置
    'paths': {
        'base_dir': 'test_environments',
        'fixtures_dir': 'test_environments/fixtures',      # 测试素材（只读）
        'sandbox_dir': 'test_environments/sandbox',        # 测试沙盒（可写）
        'results_dir': 'test_environments/results',        # 测试结果
        'reports_dir': 'test_environments/reports',        # 测试报告
        'minute_data_tests': 'test_environments/minute_data_tests',  # 分钟数据测试
        'input_data': 'test_environments/minute_data_tests/input_data',  # 输入数据
        'output_data': 'test_environments/minute_data_tests/output_data',  # 输出数据
    },
    
    # 生产环境路径映射（从user_config.py获取）
    'production_mapping': {
        'base_output_path': r'H:\MPV1.17\T0002\signals',
        'tdx_path': r'H:\MPV1.17\T0002\signals\TDX',
        'gbbq_path': r'H:\MPV1.17\T0002\signals\GBBQ',
    },
    
    # 测试素材管理
    'fixtures': {
        'auto_refresh': True,       # 是否自动刷新测试素材
        'preserve_original': True,  # 是否保护原始测试数据
        'backup_before_test': True, # 测试前是否备份
        'restore_after_test': True, # 测试后是否恢复
        'readonly_protection': True, # 只读保护
    },
    
    # 测试数据保鲜机制
    'data_freshness': {
        'copy_on_test': True,       # 每次测试时复制新的数据
        'readonly_fixtures': True,  # 测试素材只读保护
        'sandbox_isolation': True,  # 沙盒环境隔离
        'auto_cleanup': True,       # 自动清理临时文件
        'preserve_test_results': True,  # 保留测试结果
    },
    
    # 测试环境检测规则
    'detection_rules': {
        'env_variable': 'MYTHQUANT_TEST_MODE',  # 环境变量名
        'path_keywords': ['test_environments', 'test_', 'testing'],  # 路径关键词
        'script_patterns': ['test_*.py', '*_test.py'],  # 测试脚本模式
    }
}

# ==================== 测试环境检测 ====================
def is_test_environment() -> bool:
    """
    检测当前是否在测试环境中运行
    
    Returns:
        bool: 是否为测试环境
    """
    # 检测方法1：环境变量
    if os.environ.get(TEST_CONFIG['detection_rules']['env_variable']) == '1':
        return True
    
    # 检测方法2：运行路径
    current_path = Path.cwd()
    for keyword in TEST_CONFIG['detection_rules']['path_keywords']:
        if keyword in str(current_path):
            return True
    
    # 检测方法3：运行脚本名称
    if len(sys.argv) > 0:
        script_name = os.path.basename(sys.argv[0])
        for pattern in TEST_CONFIG['detection_rules']['script_patterns']:
            if pattern.replace('*', '') in script_name:
                return True
    
    # 检测方法4：配置开关
    return TEST_CONFIG['enabled']


def enable_test_mode():
    """启用测试模式"""
    TEST_CONFIG['enabled'] = True
    os.environ[TEST_CONFIG['detection_rules']['env_variable']] = '1'


def disable_test_mode():
    """禁用测试模式"""
    TEST_CONFIG['enabled'] = False
    if TEST_CONFIG['detection_rules']['env_variable'] in os.environ:
        del os.environ[TEST_CONFIG['detection_rules']['env_variable']]


# ==================== 路径解析器 ====================
def resolve_path(logical_path: str, path_type: str = 'output') -> str:
    """
    解析逻辑路径为实际路径
    
    Args:
        logical_path: 逻辑路径（如 'output/1min_data.txt'）
        path_type: 路径类型（'output', 'input', 'fixture', 'sandbox'）
        
    Returns:
        str: 实际路径（根据环境自动选择测试或生产路径）
    """
    if is_test_environment():
        # 测试环境：映射到测试路径
        if path_type == 'fixture':
            base_dir = TEST_CONFIG['paths']['fixtures_dir']
        elif path_type == 'sandbox':
            base_dir = TEST_CONFIG['paths']['sandbox_dir']
        elif path_type == 'input':
            base_dir = TEST_CONFIG['paths']['input_data']
        elif path_type == 'output':
            base_dir = TEST_CONFIG['paths']['output_data']
        else:
            base_dir = TEST_CONFIG['paths']['sandbox_dir']
        
        return os.path.join(base_dir, logical_path)
    else:
        # 生产环境：映射到生产路径
        try:
            import user_config
            base_dir = user_config.output_config['base_output_path']
        except (ImportError, KeyError):
            base_dir = TEST_CONFIG['production_mapping']['base_output_path']
        
        return os.path.join(base_dir, logical_path)


def resolve_production_path(logical_path: str) -> str:
    """
    强制解析为生产环境路径（用于对比测试）
    
    Args:
        logical_path: 逻辑路径
        
    Returns:
        str: 生产环境路径
    """
    try:
        import user_config
        base_dir = user_config.output_config['base_output_path']
    except (ImportError, KeyError):
        base_dir = TEST_CONFIG['production_mapping']['base_output_path']
    
    return os.path.join(base_dir, logical_path)


# ==================== 测试素材管理 ====================
def get_test_fixture(fixture_name: str) -> str:
    """
    获取测试素材路径
    
    Args:
        fixture_name: 测试素材名称
        
    Returns:
        str: 测试素材的完整路径
    """
    fixtures_dir = TEST_CONFIG['paths']['fixtures_dir']
    return os.path.join(fixtures_dir, fixture_name)


def prepare_test_data(fixture_name: str, preserve_original: bool = True) -> str:
    """
    准备测试数据（保鲜机制）
    
    Args:
        fixture_name: 原始测试素材名称
        preserve_original: 是否保护原始数据
        
    Returns:
        str: 可用于测试的数据文件路径
        
    Raises:
        RuntimeError: 如果不在测试环境中调用
        FileNotFoundError: 如果测试素材不存在
    """
    if not is_test_environment():
        raise RuntimeError("prepare_test_data() 只能在测试环境中调用")
    
    # 获取原始素材路径
    fixture_path = get_test_fixture(fixture_name)
    
    if not os.path.exists(fixture_path):
        raise FileNotFoundError(f"测试素材不存在: {fixture_path}")
    
    # 复制到沙盒环境（保鲜）
    sandbox_dir = TEST_CONFIG['paths']['sandbox_dir']
    os.makedirs(sandbox_dir, exist_ok=True)
    
    sandbox_path = os.path.join(sandbox_dir, fixture_name)
    
    # 每次都复制新的数据（保鲜）
    import shutil
    shutil.copy2(fixture_path, sandbox_path)
    
    # 如果需要保护原始数据，设置只读权限
    if preserve_original and TEST_CONFIG['fixtures']['readonly_protection']:
        os.chmod(fixture_path, 0o444)  # 只读权限
    
    return sandbox_path


def cleanup_test_sandbox():
    """清理测试沙盒环境"""
    if not is_test_environment():
        return
    
    sandbox_dir = TEST_CONFIG['paths']['sandbox_dir']
    if os.path.exists(sandbox_dir):
        import shutil
        shutil.rmtree(sandbox_dir)
        print(f"🧹 测试沙盒已清理: {sandbox_dir}")


# ==================== 配置获取器 ====================
def get_test_config(key: str = None) -> Union[Dict, any]:
    """
    获取测试配置
    
    Args:
        key: 配置键名，如果为None则返回全部配置
        
    Returns:
        配置值或全部配置
    """
    if key is None:
        return TEST_CONFIG
    
    keys = key.split('.')
    config = TEST_CONFIG
    
    for k in keys:
        if isinstance(config, dict) and k in config:
            config = config[k]
        else:
            return None
    
    return config


def update_production_mapping():
    """从user_config.py更新生产环境路径映射"""
    try:
        import user_config
        
        TEST_CONFIG['production_mapping'].update({
            'base_output_path': user_config.output_config.get('base_output_path', 
                                                            TEST_CONFIG['production_mapping']['base_output_path']),
            'tdx_path': user_config.tdx.get('tdx_path', 
                                          TEST_CONFIG['production_mapping']['tdx_path']),
        })
        
        print("✅ 生产环境路径映射已更新")
        
    except ImportError:
        print("⚠️ 无法导入user_config，使用默认生产环境路径")


# ==================== 测试环境状态 ====================
def get_environment_status() -> Dict[str, any]:
    """
    获取当前环境状态
    
    Returns:
        Dict: 环境状态信息
    """
    return {
        'is_test_environment': is_test_environment(),
        'test_mode_enabled': TEST_CONFIG['enabled'],
        'current_working_directory': str(Path.cwd()),
        'script_name': os.path.basename(sys.argv[0]) if sys.argv else 'unknown',
        'environment_variable': os.environ.get(TEST_CONFIG['detection_rules']['env_variable']),
        'test_paths_exist': {
            'fixtures': os.path.exists(TEST_CONFIG['paths']['fixtures_dir']),
            'sandbox': os.path.exists(TEST_CONFIG['paths']['sandbox_dir']),
            'results': os.path.exists(TEST_CONFIG['paths']['results_dir']),
        }
    }


# ==================== 初始化检查 ====================
def initialize_test_environment():
    """初始化测试环境"""
    if not is_test_environment():
        return
    
    # 创建必要的目录
    for path_key, path_value in TEST_CONFIG['paths'].items():
        os.makedirs(path_value, exist_ok=True)
    
    # 更新生产环境映射
    update_production_mapping()
    
    print("🧪 测试环境已初始化")


# Windows编码处理
import sys
if sys.platform.startswith('win'):
    try:
        # 设置控制台编码为UTF-8
        import io
        sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8', errors='replace')
        sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8', errors='replace')
    except Exception:
        pass  # 如果设置失败，继续使用默认编码

# 自动初始化（如果在测试环境中）
if is_test_environment():
    initialize_test_environment()


# ==================== 使用示例 ====================
if __name__ == '__main__':
    print("🧪 MythQuant 测试配置演示")
    print("=" * 60)
    
    # 显示环境状态
    status = get_environment_status()
    print(f"当前环境状态:")
    for key, value in status.items():
        print(f"  {key}: {value}")
    
    # 演示路径解析
    print(f"\n路径解析演示:")
    logical_path = "1min_0_000617_sample.txt"
    print(f"  逻辑路径: {logical_path}")
    print(f"  解析结果: {resolve_path(logical_path)}")
    print(f"  生产路径: {resolve_production_path(logical_path)}")
    
    # 演示测试模式切换
    print(f"\n测试模式切换演示:")
    print(f"  当前测试模式: {is_test_environment()}")
    enable_test_mode()
    print(f"  启用后: {is_test_environment()}")
    disable_test_mode()
    print(f"  禁用后: {is_test_environment()}")
