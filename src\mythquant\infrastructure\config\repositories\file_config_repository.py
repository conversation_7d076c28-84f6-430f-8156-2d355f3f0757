#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于文件的配置仓储实现

实现ConfigRepository接口，从user_config.py加载配置
"""

import json
import shutil
import os
from datetime import datetime
from typing import List, Dict, Any, Optional
from pathlib import Path

from src.mythquant.domain.config.repositories.config_repository import ConfigRepository
from src.mythquant.domain.config.entities.trading_config import TradingConfig
from src.mythquant.domain.config.entities.data_source_config import DataSourceConfig
from src.mythquant.domain.config.entities.processing_config import ProcessingConfig
from src.mythquant.domain.config.value_objects.tdx_connection import TdxConnection
from src.mythquant.domain.config.value_objects.file_path import FilePath
from ..adapters.user_config_adapter import UserConfigAdapter


class FileConfigRepository(ConfigRepository):
    """基于文件的配置仓储实现"""
    
    def __init__(self, user_config_adapter: Optional[UserConfigAdapter] = None):
        """初始化文件配置仓储"""
        self._adapter = user_config_adapter or UserConfigAdapter()
        self._backup_dir = Path("backups/config")
        self._backup_dir.mkdir(parents=True, exist_ok=True)
    
    def get_trading_config(self) -> TradingConfig:
        """获取交易配置"""
        try:
            config_data = self._adapter.get_trading_config_data()
            
            # 创建TDX连接值对象
            tdx_connection = TdxConnection(
                path=config_data['tdx_path'],
                ip=config_data['tdx_ip'],
                port=config_data['tdx_port'],
                test_path=config_data.get('tdx_test_path'),
                min_path=config_data.get('tdx_min_path'),
                day_path=config_data.get('tdx_day_path')
            )
            
            # 创建输出路径值对象
            output_path = FilePath(config_data['output_path'])
            
            # 创建目标股票文件路径值对象
            target_stocks_file = None
            if config_data.get('target_stocks_file'):
                target_stocks_file = FilePath(config_data['target_stocks_file'])
            
            # 构建pytdx配置
            pytdx_config = {
                'auto_detect': config_data.get('pytdx_auto_detect', False),
                'show_top5': config_data.get('pytdx_show_top5', True),
                'smart_detect': config_data.get('pytdx_smart_detect', True),
                'blacklist_enabled': config_data.get('pytdx_blacklist_enabled', True),
                'blacklist_timeout': config_data.get('pytdx_blacklist_timeout', 7200),
                'kline_limits': config_data.get('kline_limits', {}),
                'data_buffer_factor': config_data.get('data_buffer_factor', 1.0)
            }
            
            return TradingConfig(
                tdx_connection=tdx_connection,
                output_path=output_path,
                target_stocks_file=target_stocks_file,
                default_stocks=["000617"],  # 默认股票
                gbbq_path=config_data.get('gbbq_path'),
                pytdx_config=pytdx_config
            )
            
        except Exception as e:
            raise RuntimeError(f"加载交易配置失败: {str(e)}")
    
    def get_data_source_config(self) -> DataSourceConfig:
        """获取数据源配置"""
        try:
            config_data = self._adapter.get_data_source_config_data()
            
            # 创建TDX连接值对象
            tdx_connection = TdxConnection(
                path=config_data['tdx_path'],
                ip=config_data['tdx_ip'],
                port=config_data['tdx_port']
            )
            
            # 网络配置
            network_config = config_data.get('network_config', {})
            if not network_config:
                network_config = {
                    'connection_timeout': 30,
                    'read_timeout': 60,
                    'max_connections': 10,
                    'keep_alive': True,
                    'user_agent': 'MythQuant/1.0'
                }
            
            # 错误处理配置
            error_handling = config_data.get('error_handling', {})
            if not error_handling:
                error_handling = {
                    'log_all_errors': True,
                    'raise_on_critical': True,
                    'continue_on_partial_failure': True,
                    'error_notification': False
                }
            
            # 监控配置
            monitoring = config_data.get('monitoring', {})
            if not monitoring:
                monitoring = {
                    'track_api_usage': True,
                    'log_request_details': False,
                    'performance_metrics': True,
                    'success_rate_threshold': 0.8
                }
            
            # 互联网数据源配置
            internet_sources = config_data.get('internet_sources', {})
            
            return DataSourceConfig(
                tdx_connection=tdx_connection,
                network_config=network_config,
                error_handling=error_handling,
                monitoring=monitoring,
                internet_sources=internet_sources
            )
            
        except Exception as e:
            raise RuntimeError(f"加载数据源配置失败: {str(e)}")
    
    def get_processing_config(self) -> ProcessingConfig:
        """获取数据处理配置"""
        try:
            config_data = self._adapter.get_processing_config_data()
            
            return ProcessingConfig(
                smart_file_selector=config_data.get('smart_file_selector', {}),
                data_processing=config_data.get('data_processing', {}),
                intelligent_features=config_data.get('intelligent_features', {}),
                user_interface=config_data.get('user_interface', {}),
                verbose_mode=config_data.get('verbose_mode', {}),
                debug_enabled=config_data.get('debug_enabled', False)
            )
            
        except Exception as e:
            raise RuntimeError(f"加载数据处理配置失败: {str(e)}")
    
    def save_trading_config(self, config: TradingConfig) -> None:
        """保存交易配置"""
        # 注意：由于user_config.py是用户直接编辑的文件，
        # 这里不实现自动保存功能，而是提供配置验证和建议
        raise NotImplementedError("交易配置保存功能未实现，请直接编辑user_config.py文件")
    
    def save_data_source_config(self, config: DataSourceConfig) -> None:
        """保存数据源配置"""
        raise NotImplementedError("数据源配置保存功能未实现，请直接编辑user_config.py文件")
    
    def save_processing_config(self, config: ProcessingConfig) -> None:
        """保存数据处理配置"""
        raise NotImplementedError("数据处理配置保存功能未实现，请直接编辑user_config.py文件")
    
    def get_task_configs(self) -> List[Dict[str, Any]]:
        """获取任务配置列表"""
        try:
            return self._adapter.get_task_configs_data()
        except Exception as e:
            raise RuntimeError(f"加载任务配置失败: {str(e)}")
    
    def validate_config_integrity(self) -> bool:
        """验证配置完整性"""
        try:
            validation_result = self._adapter.validate_config_structure()
            return validation_result['is_valid']
        except Exception:
            return False
    
    def backup_config(self) -> str:
        """备份配置"""
        try:
            # 获取配置文件路径
            config_file_path = self._adapter.get_config_file_path()
            
            if not Path(config_file_path).exists():
                raise FileNotFoundError(f"配置文件不存在: {config_file_path}")
            
            # 生成备份文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            backup_filename = f"user_config_backup_{timestamp}.py"
            backup_path = self._backup_dir / backup_filename
            
            # 复制配置文件
            shutil.copy2(config_file_path, backup_path)
            
            # 创建备份信息文件
            backup_info = {
                'backup_time': timestamp,
                'original_file': config_file_path,
                'backup_file': str(backup_path),
                'file_size': backup_path.stat().st_size
            }
            
            info_file = backup_path.with_suffix('.json')
            with open(info_file, 'w', encoding='utf-8') as f:
                json.dump(backup_info, f, indent=2, ensure_ascii=False)
            
            return str(backup_path)
            
        except Exception as e:
            raise RuntimeError(f"备份配置失败: {str(e)}")
    
    def restore_config(self, backup_path: str) -> bool:
        """从备份恢复配置"""
        try:
            backup_file = Path(backup_path)
            
            if not backup_file.exists():
                raise FileNotFoundError(f"备份文件不存在: {backup_path}")
            
            # 获取当前配置文件路径
            config_file_path = self._adapter.get_config_file_path()
            
            # 先备份当前配置
            current_backup = self.backup_config()
            
            try:
                # 恢复配置文件
                shutil.copy2(backup_file, config_file_path)
                
                # 重新加载配置
                self._adapter.reload_config()
                
                return True
                
            except Exception as e:
                # 恢复失败，回滚到之前的配置
                shutil.copy2(current_backup, config_file_path)
                self._adapter.reload_config()
                raise e
                
        except Exception as e:
            raise RuntimeError(f"恢复配置失败: {str(e)}")
    
    def get_config_file_info(self) -> Dict[str, Any]:
        """获取配置文件信息"""
        try:
            config_file_path = self._adapter.get_config_file_path()
            config_file = Path(config_file_path)
            
            if not config_file.exists():
                return {
                    'exists': False,
                    'path': config_file_path
                }
            
            stat = config_file.stat()
            
            return {
                'exists': True,
                'path': config_file_path,
                'size': stat.st_size,
                'modified_time': datetime.fromtimestamp(stat.st_mtime).isoformat(),
                'is_readable': os.access(config_file_path, os.R_OK),
                'is_writable': os.access(config_file_path, os.W_OK)
            }
            
        except Exception as e:
            return {
                'exists': False,
                'error': str(e)
            }
