# 测试生产环境全面统一化方案

## 🎯 问题全面分析

### 当前存在测试生产不一致的接口

经过代码分析，发现以下接口存在测试生产不一致问题：

1. **价格比较接口**：
   - `test_environments.shared.utilities.test_file_api_comparator.compare_test_file_with_api()`
   - 硬编码测试路径，无法用于生产环境

2. **测试环境专用工具**：
   - `utils.test_environment_validator.py` - 测试环境验证器
   - `utils.test_environment_manager.py` - 测试环境管理器
   - `test_environments.shared.utilities.*` - 所有共享测试工具

3. **路径硬编码问题**：
   - 多个模块中硬编码了 `test_environments` 路径
   - 生产环境使用不同的路径逻辑

4. **配置分离问题**：
   - 测试配置混在 `user_config.py` 中
   - 缺乏专门的测试配置管理

## 🏗️ 全面统一化方案

### 核心设计原则

1. **测试永远使用生产函数**：测试环境调用与生产环境完全相同的代码
2. **路径和素材分离**：通过配置控制路径，不修改函数逻辑
3. **测试素材保鲜**：测试数据只读，每次测试都使用相同的初始状态
4. **配置文件分离**：专门的测试配置文件，不影响用户配置

### 方案架构

```
MythQuant/
├── test_config.py              # 专门的测试配置文件
├── core/
│   └── environment_manager.py  # 环境管理器
├── utils/
│   └── unified_interfaces.py   # 统一接口层
└── test_environments/
    ├── fixtures/               # 测试素材（只读）
    └── sandbox/               # 测试沙盒（可写）
```

## 📋 具体实施方案

### 第1步：创建专门的测试配置文件

```python
# test_config.py - 专门的测试配置文件
"""
MythQuant 测试环境专用配置
与 user_config.py 分离，专门用于测试环境管理
"""

import os
from pathlib import Path

# ==================== 测试环境基础配置 ====================
TEST_CONFIG = {
    # 测试模式控制
    'enabled': False,  # 是否启用测试模式（默认关闭）
    'auto_detect': True,  # 是否自动检测测试环境
    
    # 测试环境路径配置
    'paths': {
        'base_dir': 'test_environments',
        'fixtures_dir': 'test_environments/fixtures',  # 测试素材（只读）
        'sandbox_dir': 'test_environments/sandbox',    # 测试沙盒（可写）
        'results_dir': 'test_environments/results',    # 测试结果
        'reports_dir': 'test_environments/reports',    # 测试报告
    },
    
    # 生产环境路径映射
    'production_mapping': {
        'base_output_path': r'H:\MPV1.17\T0002\signals',
        'tdx_path': r'H:\MPV1.17\T0002\signals\TDX',
        'gbbq_path': r'H:\MPV1.17\T0002\signals\GBBQ',
    },
    
    # 测试素材管理
    'fixtures': {
        'auto_refresh': True,      # 是否自动刷新测试素材
        'preserve_original': True, # 是否保护原始测试数据
        'backup_before_test': True, # 测试前是否备份
        'restore_after_test': True, # 测试后是否恢复
    },
    
    # 测试数据保鲜机制
    'data_freshness': {
        'copy_on_test': True,      # 每次测试时复制新的数据
        'readonly_fixtures': True, # 测试素材只读保护
        'sandbox_isolation': True, # 沙盒环境隔离
        'auto_cleanup': True,      # 自动清理临时文件
    }
}

# ==================== 测试环境检测 ====================
def is_test_environment() -> bool:
    """检测当前是否在测试环境中运行"""
    # 检测方法1：环境变量
    if os.environ.get('MYTHQUANT_TEST_MODE') == '1':
        return True
    
    # 检测方法2：运行路径
    current_path = Path.cwd()
    if 'test_environments' in str(current_path):
        return True
    
    # 检测方法3：配置开关
    return TEST_CONFIG['enabled']

# ==================== 路径解析器 ====================
def resolve_path(logical_path: str) -> str:
    """
    解析逻辑路径为实际路径
    
    Args:
        logical_path: 逻辑路径（如 'output/1min_data.txt'）
        
    Returns:
        实际路径（根据环境自动选择测试或生产路径）
    """
    if is_test_environment():
        # 测试环境：映射到测试路径
        base_dir = TEST_CONFIG['paths']['sandbox_dir']
        return os.path.join(base_dir, logical_path)
    else:
        # 生产环境：映射到生产路径
        base_dir = TEST_CONFIG['production_mapping']['base_output_path']
        return os.path.join(base_dir, logical_path)

# ==================== 测试素材管理 ====================
def get_test_fixture(fixture_name: str) -> str:
    """
    获取测试素材路径
    
    Args:
        fixture_name: 测试素材名称
        
    Returns:
        测试素材的完整路径
    """
    fixtures_dir = TEST_CONFIG['paths']['fixtures_dir']
    return os.path.join(fixtures_dir, fixture_name)

def prepare_test_data(fixture_name: str) -> str:
    """
    准备测试数据（保鲜机制）
    
    Args:
        fixture_name: 原始测试素材名称
        
    Returns:
        可用于测试的数据文件路径
    """
    if not is_test_environment():
        raise RuntimeError("prepare_test_data() 只能在测试环境中调用")
    
    # 获取原始素材路径
    fixture_path = get_test_fixture(fixture_name)
    
    if not os.path.exists(fixture_path):
        raise FileNotFoundError(f"测试素材不存在: {fixture_path}")
    
    # 复制到沙盒环境（保鲜）
    sandbox_dir = TEST_CONFIG['paths']['sandbox_dir']
    os.makedirs(sandbox_dir, exist_ok=True)
    
    sandbox_path = os.path.join(sandbox_dir, fixture_name)
    
    # 每次都复制新的数据（保鲜）
    import shutil
    shutil.copy2(fixture_path, sandbox_path)
    
    return sandbox_path
```

### 第2步：创建环境管理器

```python
# core/environment_manager.py
class EnvironmentManager:
    """统一的环境管理器"""
    
    def __init__(self):
        """初始化环境管理器"""
        self.test_mode = self._detect_test_mode()
        self.config = self._load_config()
    
    def _detect_test_mode(self) -> bool:
        """自动检测测试模式"""
        from test_config import is_test_environment
        return is_test_environment()
    
    def _load_config(self) -> dict:
        """加载配置"""
        if self.test_mode:
            from test_config import TEST_CONFIG
            return TEST_CONFIG
        else:
            import user_config
            return {
                'base_output_path': user_config.output_config['base_output_path'],
                'tdx_path': user_config.tdx['tdx_path'],
                # ... 其他生产配置
            }
    
    def resolve_file_path(self, logical_path: str) -> str:
        """解析文件路径"""
        if self.test_mode:
            from test_config import resolve_path
            return resolve_path(logical_path)
        else:
            # 生产环境直接使用逻辑路径
            return logical_path
    
    def get_data_file(self, filename: str) -> str:
        """获取数据文件路径（自动处理测试素材保鲜）"""
        if self.test_mode:
            from test_config import prepare_test_data
            return prepare_test_data(filename)
        else:
            # 生产环境直接返回文件路径
            return os.path.join(self.config['base_output_path'], filename)
```

### 第3步：创建统一接口层

```python
# utils/unified_interfaces.py
class UnifiedPriceChecker:
    """统一的价格检查器"""
    
    def __init__(self):
        """初始化检查器"""
        from core.environment_manager import EnvironmentManager
        self.env_manager = EnvironmentManager()
    
    def check_price_consistency(self, file_path: str, stock_code: str, 
                               tolerance: float = 0.001) -> Dict[str, any]:
        """
        统一的价格一致性检查接口
        
        Args:
            file_path: 逻辑文件路径（环境管理器会自动解析）
            stock_code: 股票代码
            tolerance: 容差
            
        Returns:
            统一格式的比较结果
        """
        # 解析实际文件路径
        actual_path = self.env_manager.resolve_file_path(file_path)
        
        # 使用生产环境的比较逻辑（测试和生产完全一致）
        from test_environments.shared.utilities.test_file_api_comparator import TestFileApiComparator
        
        comparator = TestFileApiComparator()
        return comparator.compare_last_record_close_price(actual_path, stock_code, tolerance)
```

## 🎯 实施目标达成

### 1. 测试永远使用生产函数 ✅
- 所有接口都调用相同的生产代码
- 只通过环境管理器控制路径和配置

### 2. 测试路径和素材分离 ✅
- 通过 `test_config.py` 管理测试路径
- 通过环境管理器自动解析路径

### 3. 测试素材保鲜 ✅
- 测试素材存储在 `fixtures/` 目录（只读）
- 每次测试复制到 `sandbox/` 目录（可写）
- 测试完成后自动清理，保证下次测试使用新鲜数据

### 4. 配置文件分离 ✅
- `test_config.py` 专门用于测试配置
- `user_config.py` 保持纯净，只用于客户配置

## 📋 迁移计划

### 阶段1：基础设施建设（第1周）
1. 创建 `test_config.py`
2. 创建 `core/environment_manager.py`
3. 创建 `utils/unified_interfaces.py`

### 阶段2：接口统一化（第2周）
1. 重构所有测试专用接口
2. 更新现有的生产接口以支持环境管理器
3. 测试验证统一接口的正确性

### 阶段3：测试素材管理（第3周）
1. 建立测试素材库（fixtures）
2. 实现测试数据保鲜机制
3. 建立自动化测试流程

### 阶段4：文档和培训（第4周）
1. 更新所有相关文档
2. 创建使用指南
3. 进行团队培训

## 🎉 预期效果

1. **代码一致性**：测试和生产使用完全相同的代码
2. **维护简化**：只需维护一套接口代码
3. **测试可靠性**：测试结果完全代表生产环境
4. **素材保鲜**：每次测试都使用相同的初始状态
5. **配置清晰**：测试配置与用户配置完全分离

---
**方案设计**: AI Assistant  
**创建时间**: 2025-08-01  
**预计实施周期**: 4周
