#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 主程序入口 (轻量级版本)
专注于程序启动和流程控制，核心逻辑委托给专门的模块处理
"""

import sys
import os
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 导入核心模块
from core.application import MythQuantApplication
from core.config_manager import ConfigManager
from utils.enhanced_error_handler import setup_project_logging, get_smart_logger, get_error_handler, ErrorCategory


def display_banner():
    """显示程序启动横幅"""
    print("=" * 80)
    print("🚀 MythQuant 量化交易数据处理系统")
    print("📊 前复权数据生成 & L2指标计算")
    print("⚡ 高性能优化版本")
    print("=" * 80)
    print(f"🕐 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()


def check_and_update_pytdx_servers():
    """检查并更新pytdx服务器配置"""
    try:
        smart_logger = get_smart_logger("PytdxServerCheck")

        # 导入配置
        import user_config

        # 检查是否启用自动检测
        tdx_config = getattr(user_config, 'tdx', {})
        pytdx_auto_detect = tdx_config.get('pytdx_auto_detect', False)
        pytdx_show_top5 = tdx_config.get('pytdx_show_top5', True)

        if not pytdx_auto_detect:
            smart_logger.info("pytdx自动检测已禁用，跳过服务器检测")
            return

        # 检查pytdx是否可用
        try:
            import pytdx  # noqa: F401
            smart_logger.info("pytdx库可用，开始服务器检测")
        except ImportError:
            smart_logger.info("pytdx库未安装，跳过服务器检测")
            return

        # 执行服务器检测
        from utils.tdx_server_finder import TdxServerFinder

        finder = TdxServerFinder()
        best_server = finder.auto_detect_and_update_config(show_top5=pytdx_show_top5)

        if best_server:
            smart_logger.info(f"pytdx服务器配置已更新: {best_server['ip']}:{best_server['port']}")
        else:
            smart_logger.warning("pytdx服务器检测失败")

    except Exception as e:
        smart_logger = get_smart_logger("PytdxServerCheck")
        smart_logger.error(f"pytdx服务器检测异常: {e}")
        print(f"⚠️ pytdx服务器检测失败: {e}")


def initialize_application() -> MythQuantApplication:
    """初始化应用程序"""
    try:
        # 设置日志系统
        log_file = setup_project_logging("logs")
        smart_logger = get_smart_logger("Main")
        smart_logger.info(f"程序启动，日志文件: {log_file}")

        # 检查并更新pytdx服务器配置
        check_and_update_pytdx_servers()

        # 初始化配置管理器
        config_manager = ConfigManager()

        # 初始化应用程序
        app = MythQuantApplication(config_manager)

        smart_logger.info("应用程序初始化完成")
        return app
        
    except Exception as e:
        error_handler = get_error_handler()
        error_id = error_handler.log_error(
            error=e,
            category=ErrorCategory.SYSTEM,
            operation="应用程序初始化"
        )
        print(f"❌ 应用程序初始化失败 [错误ID: {error_id}]: {e}")
        raise


def main():
    """主函数 - 轻量级入口"""
    program_start_time = datetime.now()
    
    try:
        # 显示启动横幅
        display_banner()
        
        # 初始化应用程序
        print("🔧 正在初始化应用程序...")
        app = initialize_application()
        
        # 显示系统信息
        print("📋 正在加载系统信息...")
        app.display_system_overview()
        
        # 执行主要任务
        print("🚀 开始执行数据处理任务...")
        success = app.run_all_tasks()
        
        # 程序结束
        program_end_time = datetime.now()
        total_duration = (program_end_time - program_start_time).total_seconds()
        
        print("\n" + "=" * 80)
        if success:
            print("🎉 程序执行完成！所有任务成功")
        else:
            print("⚠️ 程序执行完成，但部分任务失败")
        
        print(f"⏱️ 总执行时间: {total_duration:.2f} 秒")
        print(f"📅 执行时间: {program_start_time.strftime('%H:%M:%S')} - {program_end_time.strftime('%H:%M:%S')}")
        
        # 显示统计信息
        app.display_final_statistics()
        
        print("=" * 80)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序执行")
        return 130
        
    except Exception as e:
        error_handler = get_error_handler()
        error_id = error_handler.log_error(
            error=e,
            category=ErrorCategory.SYSTEM,
            operation="主程序执行"
        )
        print(f"\n❌ 程序执行失败 [错误ID: {error_id}]: {e}")
        return 1
        
    finally:
        # 清理资源
        try:
            if 'app' in locals():
                app.cleanup()
        except Exception as e:
            print(f"⚠️ 资源清理警告: {e}")


if __name__ == '__main__':
    sys.exit(main())
