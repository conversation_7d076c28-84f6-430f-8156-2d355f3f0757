2025-07-28 22:50:04,985 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_225004.log
2025-07-28 22:50:04,985 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-28 22:50:04,985 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-28 22:50:04,985 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-28 22:50:04,986 - Main - INFO - info:307 - ℹ️ 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-28 22:50:04,986 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 22:50:04,986 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 22:50:04,987 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 22:50:04,987 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 22:50:04,987 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 22:50:04,988 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 22:50:04,988 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-28 22:50:04,992 - Main - ERROR - error:315 - ❌ 互联网分钟级数据下载任务执行失败: No module named 'utils.smart_logger'
2025-07-28 22:50:04,992 - Main - ERROR - error:315 - ❌ 任务执行失败: 互联网分钟级数据下载
2025-07-28 22:50:04,992 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 22:50:04,992 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-28 22:50:04,994 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-28 22:50:04,994 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-28 22:50:04,994 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-28 22:50:04,994 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-28 22:50:04,995 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-28 22:50:04,995 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-28 22:50:04,997 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250728_225004.txt
2025-07-28 22:50:04,997 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-28 22:50:04,997 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-28 22:50:04,998 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 0.0%
2025-07-28 22:50:04,999 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 22:50:05,000 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 22:50:05,000 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-28 22:50:05,000 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-28 22:50:05,000 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-28 22:50:05,000 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 22:50:05,000 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 22:50:05,001 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
