#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
追踪MissingDataProcessor.detect_missing_minute_data的所有调用

找出是哪里在TaskManager日志输出后、四步流程开始前调用了数据处理
"""

import os
import sys

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def install_missing_data_patch():
    """安装MissingDataProcessor补丁"""
    try:
        from utils.missing_data_processor import MissingDataProcessor
        
        # 保存原始方法
        original_detect = MissingDataProcessor.detect_missing_minute_data
        
        def patched_detect(self, file_path, stock_code, silent=True):
            """打补丁的detect_missing_minute_data方法"""
            import traceback
            
            print(f"\n🚨 MissingDataProcessor.detect_missing_minute_data被调用！")
            print(f"   文件路径: {file_path}")
            print(f"   股票代码: {stock_code}")
            print(f"   静默模式: {silent}")
            
            print(f"\n📋 调用堆栈:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-10:], 1):  # 显示最后10个调用帧
                print(f"   {i:2d}. {frame.strip()}")
            
            print(f"\n" + "="*120)
            
            # 调用原始方法
            return original_detect(self, file_path, stock_code, silent)
        
        # 替换方法
        MissingDataProcessor.detect_missing_minute_data = patched_detect
        
        print("✅ MissingDataProcessor补丁安装成功")
        return True
        
    except Exception as e:
        print(f"❌ 安装补丁失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_main_execution():
    """测试主程序执行"""
    print("\n🔍 开始测试主程序执行")
    print("=" * 80)

    try:
        print("📋 步骤1: 直接执行main.py的逻辑")

        # 直接导入并执行main.py的逻辑
        import subprocess
        import sys

        print("   🔄 运行main.py...")

        # 运行main.py，但限制输出行数以避免过多输出
        result = subprocess.run([
            sys.executable, "main.py"
        ], capture_output=True, text=True, timeout=30)

        print("   ✅ main.py执行完成")
        print(f"   📋 返回码: {result.returncode}")

        # 只显示前50行输出，避免过多信息
        if result.stdout:
            lines = result.stdout.split('\n')[:50]
            print(f"   📋 输出前50行:")
            for line in lines:
                print(f"      {line}")

        if result.stderr:
            print(f"   ⚠️ 错误输出: {result.stderr[:500]}...")

        return result.returncode == 0

    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 追踪MissingDataProcessor调用来源")
    print("=" * 120)
    
    # 安装补丁
    if not install_missing_data_patch():
        return 1
    
    # 执行测试
    success = test_main_execution()
    
    print(f"\n🎯 追踪结果")
    print("=" * 80)
    
    if success:
        print("✅ 测试执行完成")
        print("💡 如果看到了调用堆栈，说明成功捕获了MissingDataProcessor的调用")
        print("💡 重点关注调用堆栈中TaskManager相关的调用")
    else:
        print("❌ 测试执行失败")
    
    print("\n💡 分析重点:")
    print("   1. 查看调用堆栈中是否有TaskManager.execute_task")
    print("   2. 查看是否有在四步流程开始前的意外调用")
    print("   3. 查看是否有模块导入时的意外执行")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
