#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试价格比较问题
检查202507041447时间点的价格数据
"""

import os
import glob
from datetime import datetime

def find_latest_file(pattern):
    """查找最新的文件"""
    files = glob.glob(pattern)
    if files:
        return max(files, key=os.path.getmtime)
    return None

def check_price_at_time(filepath, target_time):
    """检查文件中指定时间的价格"""
    if not os.path.exists(filepath):
        print(f"❌ 文件不存在: {filepath}")
        return None
    
    print(f"🔍 检查文件: {os.path.basename(filepath)}")
    
    with open(filepath, 'r', encoding='utf-8') as f:
        lines = f.readlines()
    
    print(f"📊 文件总行数: {len(lines)}")
    
    # 查找指定时间的记录
    found_records = []
    for i, line in enumerate(lines):
        line = line.strip()
        if not line or line.startswith('股票编码'):
            continue
        
        parts = line.split('|')
        if len(parts) >= 5:
            try:
                file_time = parts[1]
                if target_time in file_time:  # 模糊匹配
                    found_records.append({
                        'line_num': i + 1,
                        'time': file_time,
                        'close_price': parts[3],
                        'adj_close_price': parts[4],
                        'full_line': line
                    })
            except (ValueError, IndexError):
                continue
    
    if found_records:
        print(f"✅ 找到 {len(found_records)} 条匹配记录:")
        for record in found_records:
            print(f"  行{record['line_num']}: 时间={record['time']}, 收盘价={record['close_price']}, 前复权价={record['adj_close_price']}")
    else:
        print(f"❌ 未找到时间包含 '{target_time}' 的记录")
        
        # 显示文件中的时间范围
        first_data_line = None
        last_data_line = None
        
        for line in lines:
            line = line.strip()
            if not line or line.startswith('股票编码'):
                continue
            
            parts = line.split('|')
            if len(parts) >= 5:
                if first_data_line is None:
                    first_data_line = parts[1]
                last_data_line = parts[1]
        
        if first_data_line and last_data_line:
            print(f"📅 文件时间范围: {first_data_line} ~ {last_data_line}")
    
    return found_records

def main():
    print("=" * 80)
    print("调试价格比较问题")
    print("=" * 80)
    
    target_time = "202507041447"
    print(f"🎯 目标时间: {target_time}")
    print()
    
    # 1. 检查现有文件
    print("1️⃣ 检查现有文件")
    print("-" * 40)
    
    # 查找现有的1min文件
    existing_pattern = "H:/MPV1.17/T0002/signals/1min_0_000617_*.txt"
    existing_file = find_latest_file(existing_pattern)
    
    if not existing_file:
        # 尝试当前目录
        existing_pattern = "1min_0_000617_*.txt"
        existing_file = find_latest_file(existing_pattern)
    
    if existing_file:
        existing_records = check_price_at_time(existing_file, target_time)
    else:
        print(f"❌ 未找到现有文件，搜索模式: {existing_pattern}")
        existing_records = []
    
    print()
    
    # 2. 检查API生成的文件
    print("2️⃣ 检查API生成的文件")
    print("-" * 40)
    
    # 查找最新生成的API文件
    api_pattern = "H:/MPV1.17/T0002/signals/1min_0_000617_20250704-20250704_*.txt"
    api_file = find_latest_file(api_pattern)
    
    if not api_file:
        # 尝试当前目录
        api_pattern = "1min_0_000617_20250704-20250704_*.txt"
        api_file = find_latest_file(api_pattern)
    
    if api_file:
        api_records = check_price_at_time(api_file, target_time)
    else:
        print(f"❌ 未找到API文件，搜索模式: {api_pattern}")
        api_records = []
    
    print()
    
    # 3. 比较结果
    print("3️⃣ 比较结果")
    print("-" * 40)
    
    if existing_records and api_records:
        existing_price = float(existing_records[0]['adj_close_price'])
        api_price = float(api_records[0]['adj_close_price'])
        
        print(f"📊 价格比较:")
        print(f"  现有文件: {existing_price}")
        print(f"  API文件:  {api_price}")
        print(f"  差异:     {abs(existing_price - api_price):.6f}")
        
        if abs(existing_price - api_price) < 0.001:
            print(f"  结论:     ✅ 价格一致")
        else:
            print(f"  结论:     ❌ 价格不一致")
    else:
        print("❌ 无法进行比较，缺少必要的数据")
    
    print()
    print("=" * 80)

if __name__ == "__main__":
    main()
