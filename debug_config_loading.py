#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试配置加载过程
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_user_config_loading():
    """调试user_config.py加载"""
    print("🔍 调试user_config.py加载")
    print("=" * 50)
    
    try:
        import user_config
        print("✅ user_config.py导入成功")
        
        # 检查time_ranges属性
        if hasattr(user_config, 'time_ranges'):
            time_ranges = user_config.time_ranges
            print(f"✅ time_ranges属性存在，类型: {type(time_ranges)}")
            
            # 检查internet_minute配置
            if 'internet_minute' in time_ranges:
                internet_minute = time_ranges['internet_minute']
                print(f"✅ internet_minute配置存在")
                print(f"   配置内容: {internet_minute}")
                
                # 检查enabled状态
                enabled = internet_minute.get('enabled', False)
                print(f"   enabled状态: {enabled} (类型: {type(enabled)})")
                
                if enabled:
                    print("✅ internet_minute配置已启用")
                    return True
                else:
                    print("❌ internet_minute配置未启用")
                    return False
            else:
                print("❌ internet_minute配置不存在")
                return False
        else:
            print("❌ time_ranges属性不存在")
            return False
            
    except Exception as e:
        print(f"❌ 加载user_config.py失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_config_manager_loading():
    """调试配置管理器加载"""
    print("\n🔍 调试配置管理器加载")
    print("=" * 50)
    
    try:
        from src.mythquant.core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        print("✅ ConfigManager创建成功")
        
        # 调用get_task_configs方法
        print("🔄 调用get_task_configs()...")
        task_configs = config_manager.get_task_configs()
        
        print(f"📊 返回的任务配置:")
        print(f"   任务数量: {len(task_configs)}")
        
        if len(task_configs) > 0:
            for i, config in enumerate(task_configs, 1):
                print(f"\n   📋 任务 {i}:")
                print(f"      名称: {config.get('name')}")
                print(f"      类型: {config.get('data_type')}")
                print(f"      启用: {config.get('enabled')}")
                print(f"      开始时间: {config.get('start_time')}")
                print(f"      结束时间: {config.get('end_time')}")
                print(f"      频率: {config.get('frequency')}")
            return True
        else:
            print("❌ 没有返回任何任务配置")
            print("💡 这意味着internet_minute配置可能未启用或读取失败")
            return False
            
    except Exception as e:
        print(f"❌ 配置管理器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_step_by_step():
    """逐步调试配置加载"""
    print("\n🔍 逐步调试配置加载")
    print("=" * 50)
    
    try:
        # 步骤1：直接导入user_config
        print("📋 步骤1：导入user_config")
        import user_config
        print("   ✅ 导入成功")
        
        # 步骤2：获取time_ranges
        print("📋 步骤2：获取time_ranges")
        time_ranges = getattr(user_config, 'time_ranges', {})
        print(f"   ✅ time_ranges获取成功，键: {list(time_ranges.keys())}")
        
        # 步骤3：获取internet_minute配置
        print("📋 步骤3：获取internet_minute配置")
        minute_config = time_ranges.get('internet_minute', {})
        print(f"   ✅ internet_minute配置: {minute_config}")
        
        # 步骤4：检查enabled状态
        print("📋 步骤4：检查enabled状态")
        enabled = minute_config.get('enabled', False)
        print(f"   enabled值: {enabled}")
        print(f"   enabled类型: {type(enabled)}")
        print(f"   布尔判断: {bool(enabled)}")
        
        # 步骤5：模拟配置管理器的判断逻辑
        print("📋 步骤5：模拟配置管理器判断")
        if minute_config.get('enabled', False):
            print("   ✅ 条件满足，应该创建任务配置")
            
            start_date = minute_config.get('start_date', '20250101')
            end_date = minute_config.get('end_date', '20250807')
            print(f"   开始日期: {start_date}")
            print(f"   结束日期: {end_date}")
            
            start_time = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
            end_time = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"
            print(f"   转换后开始时间: {start_time}")
            print(f"   转换后结束时间: {end_time}")
            
            return True
        else:
            print("   ❌ 条件不满足，不会创建任务配置")
            return False
            
    except Exception as e:
        print(f"❌ 逐步调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 配置加载调试")
    print("=" * 60)
    
    # 调试user_config.py加载
    user_config_ok = debug_user_config_loading()
    
    # 调试配置管理器加载
    config_manager_ok = debug_config_manager_loading()
    
    # 逐步调试
    step_by_step_ok = debug_step_by_step()
    
    print("\n" + "=" * 60)
    print("🔍 调试结果总结")
    print("=" * 60)
    print(f"user_config.py加载: {'✅' if user_config_ok else '❌'}")
    print(f"配置管理器加载: {'✅' if config_manager_ok else '❌'}")
    print(f"逐步调试: {'✅' if step_by_step_ok else '❌'}")
    
    if user_config_ok and not config_manager_ok:
        print("\n💡 问题分析：user_config.py正常但配置管理器异常")
        print("   可能原因：配置管理器内部逻辑有问题")
    elif not user_config_ok:
        print("\n💡 问题分析：user_config.py本身有问题")
        print("   可能原因：配置文件格式错误或enabled设置错误")

if __name__ == '__main__':
    main()
