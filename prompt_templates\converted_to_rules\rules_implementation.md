# MythQuant 规则转换实施

## 🎯 第一阶段：USER_RULES 转换

### 原始来源：project_context.md + 用户历史偏好

```markdown
## USER_RULES（用户规则）

### 交互偏好规则
- 始终使用中文简体回应
- 优先使用功能描述而非具体文件名进行交互（如"修改进度显示"而非"@main.py"）
- 当用户@引用错误文件时，主动识别正确文件并询问确认
- 遵循模块化拆分的既定文件定位规则[[memory:3564322]]

### 技术偏好规则  
- 在前复权计算中避免使用人工距离补偿，因为可能掩盖潜在问题[[memory:3125115]]
- 研究文档时完整阅读避免断章取义[[memory:3125122]]
- 优先对已拆分模块进行修改，避免主文件继续膨胀

### 工作流程偏好
- 重要修改前先进行依赖关系分析
- 模块化拆分时严格遵循测试驱动的验证流程
- 新功能开发优先考虑添加到现有模块而非创建新模块
```

## 🎯 第二阶段：AGENT 规则转换

### 原始来源：project_context.md + process_improvements.md

```markdown
## AGENT RULES（AI行为规则）

### 文件定位智能规则
- 当用户提及"显示"、"打印"、"输出界面"相关需求时，优先检查ui/display.py模块
- 当用户提及"进度"、"跟踪"相关需求时，优先检查ui/progress.py模块
- 当用户提及"写入"、"保存"、"文件输出"相关需求时，优先检查file_io/file_writer.py模块
- 当用户提及"读取"、"加载"、"Excel"相关需求时，优先检查file_io/excel_reader.py模块
- 当用户提及"格式化"、"数据处理"相关需求时，优先检查file_io/data_formatter.py模块
- 当用户提及"配置"、"设置"相关需求时，优先检查user_config.py
- 当用户提及"日志"、"记录"相关需求时，优先检查core/logging_service.py
- 当用户提及"算法"、"计算"、"L2指标"相关需求时，定位到main_v20230219_optimized.py第2120-3154行（待拆分）

### 代码修改工作流规则
- 始终先进行完整的代码理解和影响分析
- 修改前检查依赖关系，确认是否影响其他模块
- 模块化拆分后必须创建完整的测试验证脚本
- 重要功能修改时建议用户先备份或测试

### 复刻机制规则（基于IMP-20250710-001）
- 执行6步复刻检查清单：逐行分析→识别参数→保持一致性→复现边界条件→验证算法→确保可执行性
- 严禁断章取义、擅自简化、更改核心算法、忽略异常处理
- 使用谦逊确认原则：避免"已完成"表述，使用"请您验证确认"
```

## 🎯 第三阶段：ALWAYS 规则转换  

### 原始来源：best_practices.md + pitfall_guide.md

```markdown
## ALWAYS RULES（始终遵循规则）

### 金融计算精度规则
- 金融计算中总是使用高精度Decimal而非float
- 浮点数比较总是使用容差检查而非直接等值比较（tolerance=1e-9）
- 价格数据总是统一精度标准（价格4位小数，比率6位小数）
- 累积计算总是使用Decimal避免误差累积

### 代码质量规则
- 总是为金融计算添加数据验证规则（价格>0，异常检测）
- 总是使用向量化操作替代循环（优先pandas/numpy向量化）
- 总是采用多级缓存策略提升性能（内存>文件>数据库>原始数据源）
- 总是为大数据处理实现分块机制（chunk_size=10000）

### 架构设计规则
- 总是采用分层架构：数据层→计算层→策略层→交易层
- 总是实现降级处理机制：主要方法→备用方法→最小处理→安全默认值
- 总是保持模块高内聚、低耦合的设计原则
- 总是为异常情况提供安全的默认返回值

### 性能优化规则
- 总是优化数据类型（float32替代float64节省50%内存）
- 总是实现智能缓存失效机制
- 总是对性能敏感的计算使用向量化
- 总是为IO操作实现异步处理机制
```

## 🔄 规则应用示例

### 用户交互示例
```
用户：@main.py 修改进度显示的颜色
AI应用USER_RULES：检测到您想修改进度显示功能，根据项目结构，建议修改ui/progress.py而不是主文件。是否确认？

用户：优化L2指标计算性能
AI应用AGENT_RULES：定位到main_v20230219_optimized.py第2120-3154行的L2指标计算部分，这是第四阶段拆分的目标区域。建议先分析算法依赖关系。
```

### 代码修改示例
```python
# AI应用ALWAYS规则
def calculate_adjustment_factor(self, price_data):
    # ✅ 应用精度规则
    from decimal import Decimal
    
    # ✅ 应用数据验证规则
    if price_data <= 0:
        raise ValueError("价格数据必须大于0")
    
    # ✅ 应用高精度计算规则
    price = Decimal(str(price_data))
    factor = price / Decimal('100')
    
    # ✅ 应用容差比较规则
    if abs(float(factor) - 1.0) < 1e-9:
        return 1.0
    
    return float(factor)
```

## 📋 转换后文档策略

### 保留为文档的内容
- **技术细节知识**：technical_points.md、stock_python.md（详细实现参考）
- **历史案例记录**：bug_cases.md、session_summary.md（具体案例查阅）
- **长篇指南**：ai_knowledge_base.md（综合性知识参考）

### 文档与规则协同机制
- 规则处理高频、标准化操作
- 文档提供深度技术细节和历史经验
- AI根据需求自动在规则和文档间切换

## 🚀 实施步骤

### 第一步：验证转换效果
1. 创建测试对话验证USER_RULES效果
2. 测试AGENT_RULES的文件定位准确性
3. 验证ALWAYS_RULES在代码生成中的应用

### 第二步：调整优化
1. 根据实际使用效果调整规则表述
2. 补充遗漏的关键规则
3. 优化规则之间的协调性

### 第三步：文档精简
1. 从原文档中移除已转换为规则的内容
2. 保留深度技术内容作为参考文档
3. 建立规则与文档的交叉索引

---

**转换完成后，AI将在高频操作中自动应用规则，在需要深度技术细节时自动查找相关文档，实现效率与深度的最佳平衡。** 