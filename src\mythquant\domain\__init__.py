#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
领域层 (Domain Layer)

Clean Architecture的核心层，包含业务逻辑和领域模型
不依赖任何外部框架或基础设施
"""

# 只导入实际存在的配置相关模块
try:
    from .config import *
except ImportError:
    # 配置模块导入失败时的处理
    pass

# 注意：其他领域模块（aggregates, entities等）暂时不导入
# 因为它们可能还没有完全实现或存在导入依赖问题
# from .aggregates import *
# from .entities import *
# from .value_objects import *
# from .domain_services import *
# from .events import *
# from .specifications import *
# from .exceptions import *

__all__ = [
    # 配置相关（实际存在的）
    'TradingConfig',
    'DataSourceConfig',
    'ProcessingConfig',
    'TdxConnection',
    'FilePath',
    'TimeRange',
    'ConfigRepository',
    'ConfigValidationService',

    # 其他领域概念（暂时注释，等待实现）
    # 'Stock',
    # 'Market',
    # 'Portfolio',
    # 'StockEntity',
    # 'MarketEntity',
    # 'PositionEntity',
    # 'StockCode',
    # 'Price',
    # 'Volume',
    # 'DateRange',
    # 'TradingSession',
    # 'PricingService',
    # 'RiskService',
    # 'AnalyticsService',
    # 'StockPriceChanged',
    # 'MarketOpened',
    # 'MarketClosed',
    # 'PositionCreated',
    # 'TradingTimeSpecification',
    # 'ValidStockCodeSpecification',
    # 'DomainException',
    # 'InvalidStockCodeError',
    # 'InvalidPriceError',
    # 'MarketClosedError'
]
