# MythQuant项目知识库索引配置
# 用于AI助手智能检索和知识库管理

version: "1.0"
created: "2025-08-12"
description: "知识库分类索引和检索策略配置"

# 知识库分类配置
knowledge_categories:
  
  # 技术问题和错误处理
  technical_issues:
    description: "技术错误、调试问题、代码问题"
    keywords: 
      - "错误"
      - "异常" 
      - "bug"
      - "调试"
      - "修复"
      - "KeyError"
      - "AttributeError"
      - "ImportError"
      - "pandas"
      - "DataFrame"
    primary_sources:
      - "docs/knowledge/troubleshooting/debugging_knowledge_base.md"
      - "docs/knowledge/faq_knowledge_base.md"
    secondary_sources:
      - "docs/troubleshooting/*.md"
    priority: "high"
    
  # 工作流设计和流程问题  
  workflow_design:
    description: "工作流设计、6步法、流程原子性问题"
    keywords:
      - "工作流"
      - "6步法" 
      - "流程"
      - "步骤"
      - "原子性"
      - "多线程"
      - "时序"
      - "嵌套调用"
    primary_sources:
      - "docs/troubleshooting/workflow_atomicity_issue_analysis.md"
      - "docs/workflow/1min_workflow.md"
    secondary_sources:
      - "docs/implementation/*_report.md"
    priority: "high"
    
  # 架构设计和DDD相关
  architecture_design:
    description: "DDD架构、设计模式、系统架构问题"
    keywords:
      - "DDD"
      - "架构"
      - "设计"
      - "模块"
      - "分层"
      - "依赖"
      - "门面模式"
      - "适配器"
      - "向后兼容"
    primary_sources:
      - "docs/implementation/rules_*_report.md"
      - "docs/architecture/*.md"
    secondary_sources:
      - "docs/knowledge/faq_knowledge_base.md"
    priority: "medium"
    
  # 质量规范和测试
  quality_standards:
    description: "测试规范、代码质量、质量保证"
    keywords:
      - "测试"
      - "验证"
      - "质量"
      - "规范"
      - "标准"
      - "代码审查"
      - "最佳实践"
    primary_sources:
      - "docs/knowledge/testing_quality_rules.md"
    secondary_sources:
      - "docs/quality/*.md"
      - "docs/standards/*.md"
    priority: "medium"
    
  # 实施经验和最佳实践
  implementation_experience:
    description: "项目实施经验、最佳实践、经验总结"
    keywords:
      - "实施"
      - "经验"
      - "最佳实践"
      - "总结"
      - "优化"
      - "改进"
    primary_sources:
      - "docs/implementation/*.md"
      - "docs/best_practices/*.md"
    secondary_sources:
      - "docs/knowledge/faq_knowledge_base.md"
    priority: "low"

# 检索策略配置
search_strategies:
  
  # 精确匹配策略
  exact_match:
    description: "精确匹配特定错误代码和异常类型"
    patterns:
      - "KeyError"
      - "AttributeError" 
      - "ImportError"
      - "ValueError"
      - "TypeError"
      - "FileNotFoundError"
    weight: 1.0
    
  # 语义匹配策略  
  semantic_match:
    description: "基于语义的模糊匹配"
    patterns:
      - "数据质量"
      - "性能优化"
      - "用户体验"
      - "工作流设计"
      - "架构升级"
      - "问题排查"
    weight: 0.8
    
  # 模式匹配策略
  pattern_match:
    description: "基于正则表达式的模式匹配"
    patterns:
      - ".*_rules.*"
      - ".*工作流.*"
      - ".*架构.*"
      - ".*6步法.*"
      - ".*DDD.*"
    weight: 0.6
    
  # 交叉引用策略
  cross_reference:
    description: "基于交叉引用的关联检索"
    enabled: true
    max_depth: 3
    weight: 0.7

# 检索结果排序配置
result_ranking:
  factors:
    relevance: 0.4      # 相关性权重
    recency: 0.3        # 时效性权重  
    completeness: 0.2   # 完整性权重
    usage_frequency: 0.1 # 使用频率权重
    
  # 时效性衰减配置
  recency_decay:
    enabled: true
    half_life_days: 90  # 90天半衰期
    
  # 使用频率统计
  usage_tracking:
    enabled: true
    track_file: ".augment/knowledge_usage_stats.json"

# 自动化配置
automation:
  
  # 自动索引更新
  auto_index_update:
    enabled: true
    triggers:
      - "knowledge_base_modified"
      - "new_document_added"
      - "cross_reference_changed"
      
  # 质量检查
  quality_check:
    enabled: true
    checks:
      - "broken_links"
      - "outdated_content"
      - "missing_cross_references"
    schedule: "weekly"
    
  # 使用统计
  usage_analytics:
    enabled: true
    metrics:
      - "search_frequency"
      - "hit_rate"
      - "user_satisfaction"
    report_frequency: "monthly"

# 特殊规则配置
special_rules:
  
  # 紧急问题优先级
  urgent_issues:
    keywords: ["生产环境", "数据丢失", "系统崩溃", "安全漏洞"]
    priority_boost: 2.0
    
  # 新手友好内容
  beginner_friendly:
    tags: ["入门", "基础", "教程", "指南"]
    boost_for_new_users: 1.5
    
  # 专家级内容
  expert_level:
    tags: ["高级", "深入", "源码", "架构"]
    require_experience_level: "advanced"

# 维护配置
maintenance:
  
  # 定期维护任务
  scheduled_tasks:
    - name: "index_validation"
      frequency: "daily"
      description: "验证索引完整性和准确性"
      
    - name: "content_freshness_check"  
      frequency: "weekly"
      description: "检查内容时效性"
      
    - name: "usage_analysis"
      frequency: "monthly" 
      description: "分析使用情况和效果"
      
  # 更新触发条件
  update_triggers:
    - "new_knowledge_base_added"
    - "existing_content_modified"
    - "user_feedback_received"
    - "performance_degradation_detected"

---
# 配置文件说明
# 1. 本配置文件定义了知识库的分类、检索策略和自动化规则
# 2. AI助手应根据此配置进行智能检索和知识库管理
# 3. 配置更新时需要同步更新相关的rules和文档
# 4. 定期根据使用效果和反馈优化配置参数
