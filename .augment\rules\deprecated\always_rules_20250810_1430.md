---
归档原因: 规则文件精简重组优化
归档时间: 2025-08-10 14:30
归档说明: 本文件因内容过于庞大(68行)、包含过多技术细节、与其他文件存在重复内容而被归档。
         新版本将专注于核心原则，技术细节移至agent_rules.md，重复内容已合并。
原始文件: .augment/rules/always_rules.md
优化目标: 从68行精简到40行以内，突出核心原则，提升可维护性
---

# MythQuant项目核心规则 (归档版本)

## 基本原则
- **吹哨人原则**：如果我提的问题、要求你觉得有不合理的地方，你可以对我提的问题进行优化修改并且通过交互与我确认，征求我的同意后即可使用你优化后的问题重新提问了
- **DDD架构优先**：严格遵循领域驱动设计架构
- **测试驱动开发**：代码修改必须有测试验证
- **用户体验至上**：技术升级对用户透明，保持向后兼容

## DDD架构实施规则
- **分层架构严格遵循**：领域层(Domain) → 应用层(Application) → 基础设施层(Infrastructure) → 接口层(Interface)
- **依赖倒置原则**：领域层不依赖任何外部框架，通过接口定义依赖关系
- **向后兼容性保证**：新架构必须完全兼容现有代码，用户无需修改使用方式
- **配置管理统一**：通过DDD架构统一管理配置，但保持user_config.py的用户友好性
- **门面模式应用**：使用门面模式隐藏DDD架构复杂性，提供简化的外部接口

## 金融计算精度要求
- **Decimal类型强制**：所有价格计算必须使用 `decimal.Decimal` 类型
- **容差比较**：价格比较使用容差比较：`abs(价格1 - 价格2) < Decimal('0.001')`
- **统一精度标准**：价格保留3位小数，比率保留6位小数
- **避免累积误差**：避免浮点数累积误差，特别是在循环计算中

## 代码质量标准
- **输入验证强制**：数据输入必须进行验证：检查None值、空值、数据类型
- **向量化优先**：优先使用pandas向量化操作而非循环
- **DataFrame布尔判断**：使用`df is None or df.empty`而非`if not df`
- **多级缓存**：实现内存缓存、磁盘缓存、数据库缓存
- **分块处理**：大数据处理使用分块处理策略

## 环境管理核心规则
- **AI调试环境强制**：AI调试代码时必须自动走测试环境，通过user_config.py中的detect_environment()函数检测
- **调试模式识别**：通过文件名模式识别AI调试（tools/、test_、debug_、detector、tracker、precise_等）
- **测试文件强制使用**：在测试环境中必须使用test_前缀的标准测试文件，不得使用生产环境文件
- **环境隔离保证**：确保AI调试不影响生产环境，所有调试操作限制在测试环境中
- **禁止生产环境验证**：严禁直接运行main.py进行修复验证，必须在test_environments中创建专门的测试脚本

## 数据质量验证标准
- **文件命名格式**：频率前缀_0_股票代码_时间范围_来源.txt（如1min_0_000617_...而不是1_0_000617_...）
- **A股1分钟数据标准**：时间戳表示分钟结束时间，09:31是第一条数据（表示09:30-09:31），13:01是中午第一条数据（表示13:00-13:01）
- **数据完整性标准**：每个交易日240行数据即为完整，不需要检查具体时间点
- **前复权价格验证**：前复权收盘价与当日收盘价必须有合理差异，不能完全相同
- **数据格式验证**：包含所有必要字段且格式正确，字段分隔符为管道符"|"

## 反复问题预防规则
- **问题模式识别**：建立常见问题的识别模式，防止同类问题反复出现
- **根本原因修复**：不满足于表面修复，必须找到并解决问题的根本原因
- **系统性预防机制**：建立预防机制而非被动修复，如格式验证、数据源限制检查
- **实际验证强制**：声称问题"已解决"前必须通过实际数据验证，不能基于理论推测
- **知识沉淀机制**：每次解决反复问题后，必须将经验沉淀到知识库和规则中
- **函数名称冲突预防**：严禁在同一模块中定义重复的函数名，建立自动化检测机制
- **集成测试强制**：单元测试无法发现的集成问题必须通过专门的集成测试发现和预防

## 主程序架构规则
- **统一入口**：使用 `main.py` 作为唯一主程序入口，采用模块化架构设计
- **模块化设计**：核心逻辑分离到专门模块，主程序专注于启动和流程控制
- **功能完整性**：确保所有核心功能在模块化架构中得到完整实现
- **向后兼容**：保持现有接口不变，新功能通过扩展实现

---

**归档文件类型**: Always规则 - 始终应用 (归档版本)
**原始更新时间**: 2025-08-10
**归档时间**: 2025-08-10 14:30
**归档原因**: 精简重组优化，内容过于庞大，存在重复内容
**后续版本**: 精简为核心原则版本
