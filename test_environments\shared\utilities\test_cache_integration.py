#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存模块完整集成测试
验证缓存模块不影响数据准确性，能正常生成前复权txt文档
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_cache_integration_with_main_program():
    """测试缓存模块与主程序的集成"""
    print("🧪 测试缓存模块与主程序的集成...")
    print("=" * 60)
    
    try:
        # 导入主程序和缓存模块
        from cache import CacheManager
        print("✅ 缓存模块导入成功")
        
        # 测试缓存管理器创建
        cache_config = {
            'memory_max_size': 1000,
            'memory_ttl': 1800,
            'file_max_size': 5000,
            'file_ttl': 7200,
            'cache_dir': './test_integration_cache'
        }
        cache_manager = CacheManager(cache_config)
        print("✅ 缓存管理器创建成功")
        
        # 测试股票除权除息数据缓存
        test_stock_code = "000617"
        test_dividend_data = create_mock_dividend_data()
        
        # 存储测试数据
        success = cache_manager.put_stock_dividend_data(test_stock_code, test_dividend_data)
        print(f"✅ 除权除息数据存储{'成功' if success else '失败'}")
        
        # 读取测试数据
        retrieved_data = cache_manager.get_stock_dividend_data(test_stock_code)
        if retrieved_data is not None and not retrieved_data.empty:
            print(f"✅ 除权除息数据读取成功 - {len(retrieved_data)}条记录")
            
            # 验证数据一致性
            if len(retrieved_data) == len(test_dividend_data):
                print("✅ 数据一致性验证通过")
            else:
                print("❌ 数据一致性验证失败")
                return False
        else:
            print("❌ 除权除息数据读取失败")
            return False
        
        # 测试缓存统计
        stats = cache_manager.get_cache_stats()
        print(f"✅ 缓存统计信息获取成功")
        print(f"   总请求数: {stats['global_stats']['total_requests']}")
        print(f"   命中率: {stats['global_stats']['hit_rate']:.2%}")
        
        # 测试缓存健康状态
        health = cache_manager.get_cache_health()
        print(f"✅ 缓存健康状态: {health['health_status']} (评分: {health['health_score']}/100)")
        
        return True
        
    except Exception as e:
        print(f"❌ 缓存集成测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_mock_dividend_data():
    """创建模拟的除权除息数据"""
    dates = pd.date_range('2020-01-01', periods=5, freq='Y')
    
    data = pd.DataFrame({
        'fenhong': [0.5, 0.3, 0.8, 0.2, 0.6],      # 分红
        'songzhuangu': [0.2, 0.0, 0.5, 0.0, 0.3],  # 送转股
        'peigu': [0.0, 0.1, 0.0, 0.2, 0.0],        # 配股
        'peigujia': [0.0, 8.5, 0.0, 12.3, 0.0],    # 配股价
        'category': [1.0] * 5
    }, index=dates)
    
    return data


def test_cache_with_mock_main_program():
    """使用模拟主程序测试缓存功能"""
    print("\n🧪 使用模拟主程序测试缓存功能...")
    print("=" * 60)
    
    try:
        # 模拟主程序的数据处理流程
        from cache import CacheManager
        
        # 创建缓存管理器
        cache_manager = CacheManager({
            'memory_max_size': 500,
            'file_max_size': 2000,
            'cache_dir': './mock_program_cache'
        })
        
        # 模拟处理多只股票
        stock_codes = ['000001', '000002', '600000', '600036', '000617']
        
        print(f"📊 模拟处理 {len(stock_codes)} 只股票...")
        
        for i, stock_code in enumerate(stock_codes):
            print(f"   处理股票 {stock_code} ({i+1}/{len(stock_codes)})")
            
            # 模拟生成股票数据
            mock_data = create_mock_stock_data(stock_code)
            
            # 存储到缓存
            cache_key = f"stock_data_{stock_code}"
            cache_manager.put_dataframe(cache_key, mock_data)
            
            # 模拟除权除息数据
            dividend_data = create_mock_dividend_data()
            cache_manager.put_stock_dividend_data(stock_code, dividend_data)
            
            # 验证数据可以正确读取
            retrieved_stock_data = cache_manager.get_dataframe(cache_key)
            retrieved_dividend_data = cache_manager.get_stock_dividend_data(stock_code)
            
            if retrieved_stock_data is None or retrieved_dividend_data is None:
                print(f"❌ 股票 {stock_code} 数据读取失败")
                return False
        
        # 获取最终统计
        final_stats = cache_manager.get_cache_stats()
        print(f"\n📈 处理完成统计:")
        print(f"   内存缓存大小: {final_stats['memory_cache']['cache_size']}")
        print(f"   文件缓存大小: {final_stats['file_cache']['cache_size']}")
        print(f"   总命中率: {final_stats['global_stats']['hit_rate']:.2%}")
        print(f"   平均响应时间: {final_stats['global_stats']['avg_response_time']*1000:.2f}ms")
        
        # 测试缓存清理
        expired_count = cache_manager.cleanup_expired()
        print(f"   清理过期缓存: {sum(expired_count.values())} 个")
        
        print("✅ 模拟主程序测试通过")
        return True
        
    except Exception as e:
        print(f"❌ 模拟主程序测试失败: {e}")
        return False


def create_mock_stock_data(stock_code):
    """创建模拟股票数据"""
    dates = pd.date_range('2024-01-01 09:30:00', periods=240, freq='1min')
    
    # 模拟价格数据
    base_price = 10.0 + hash(stock_code) % 20  # 基于股票代码生成基准价格
    
    data = pd.DataFrame({
        '日期': [d.strftime('%Y%m%d %H:%M:%S') for d in dates],
        '股票代码': [stock_code] * len(dates),
        'open': np.random.uniform(base_price * 0.95, base_price * 1.05, len(dates)),
        'high': np.random.uniform(base_price * 1.0, base_price * 1.1, len(dates)),
        'low': np.random.uniform(base_price * 0.9, base_price * 1.0, len(dates)),
        'close': np.random.uniform(base_price * 0.95, base_price * 1.05, len(dates)),
        'volume': np.random.randint(1000, 10000, len(dates)),
        'amount': np.random.uniform(10000, 100000, len(dates))
    })
    
    # 确保价格关系合理
    for i in range(len(data)):
        high = max(data.loc[i, 'open'], data.loc[i, 'high'], data.loc[i, 'close'])
        low = min(data.loc[i, 'open'], data.loc[i, 'low'], data.loc[i, 'close'])
        data.loc[i, 'high'] = high
        data.loc[i, 'low'] = low
    
    return data


def test_cache_data_accuracy():
    """测试缓存数据准确性"""
    print("\n🧪 测试缓存数据准确性...")
    print("=" * 60)
    
    try:
        from cache import CacheManager
        
        cache_manager = CacheManager({'cache_dir': './accuracy_test_cache'})
        
        # 创建精确的测试数据
        original_data = pd.DataFrame({
            'price': [12.345, 23.456, 34.567, 45.678, 56.789],
            'volume': [1000, 2000, 3000, 4000, 5000],
            'ratio': [0.123456, 0.234567, 0.345678, 0.456789, 0.567890]
        })
        
        # 存储和读取
        cache_manager.put_dataframe('accuracy_test', original_data)
        retrieved_data = cache_manager.get_dataframe('accuracy_test')
        
        # 精确比较
        if retrieved_data is not None:
            # 比较数值精度
            price_diff = abs(original_data['price'] - retrieved_data['price']).max()
            volume_diff = abs(original_data['volume'] - retrieved_data['volume']).max()
            ratio_diff = abs(original_data['ratio'] - retrieved_data['ratio']).max()
            
            print(f"   价格数据最大差异: {price_diff}")
            print(f"   成交量数据最大差异: {volume_diff}")
            print(f"   比率数据最大差异: {ratio_diff}")
            
            # 验证精度
            if price_diff < 1e-10 and volume_diff < 1e-10 and ratio_diff < 1e-10:
                print("✅ 数据精度验证通过")
                return True
            else:
                print("❌ 数据精度验证失败")
                return False
        else:
            print("❌ 数据读取失败")
            return False
            
    except Exception as e:
        print(f"❌ 数据准确性测试失败: {e}")
        return False


def cleanup_test_caches():
    """清理测试缓存"""
    import shutil
    
    test_dirs = [
        './test_integration_cache',
        './mock_program_cache', 
        './accuracy_test_cache'
    ]
    
    for test_dir in test_dirs:
        if os.path.exists(test_dir):
            try:
                shutil.rmtree(test_dir)
                print(f"✅ 清理测试缓存目录: {test_dir}")
            except Exception as e:
                print(f"⚠️  清理缓存目录失败 {test_dir}: {e}")


def main():
    """主测试函数"""
    print("🚀 MythQuant 缓存模块完整集成测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    success = True
    
    # 运行各项测试
    tests = [
        ("缓存集成测试", test_cache_integration_with_main_program),
        ("模拟主程序测试", test_cache_with_mock_main_program),
        ("数据准确性测试", test_cache_data_accuracy)
    ]
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行 {test_name}...")
        if not test_func():
            success = False
            print(f"❌ {test_name} 失败")
        else:
            print(f"✅ {test_name} 通过")
    
    # 清理测试缓存
    cleanup_test_caches()
    
    print("=" * 60)
    if success:
        print("🎉 所有缓存集成测试通过！")
        print("✨ 缓存模块集成成功，数据准确性得到保证")
        print("📝 建议：可以进行真实数据的完整测试")
    else:
        print("❌ 部分缓存集成测试失败")
        print("🔧 请检查缓存模块的实现和集成")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
