#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
性能分析模块

提供代码性能分析功能
"""

import time
import functools
from enum import Enum
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


class ProfilerType(Enum):
    """分析器类型"""
    TIME = "time"
    MEMORY = "memory"
    CPU = "cpu"
    CUSTOM = "custom"


@dataclass
class ProfileResult:
    """分析结果"""
    name: str
    profiler_type: ProfilerType
    start_time: float
    end_time: float
    duration: float
    metadata: Dict[str, Any] = None
    
    def __post_init__(self):
        if self.metadata is None:
            self.metadata = {}
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'profiler_type': self.profiler_type.value,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'metadata': self.metadata
        }


class Profiler:
    """性能分析器"""
    
    def __init__(self):
        self._results: List[ProfileResult] = []
        self._active_profiles: Dict[str, float] = {}
        self._max_results = 1000
    
    def start_profile(self, name: str, profiler_type: ProfilerType = ProfilerType.TIME) -> str:
        """开始性能分析"""
        profile_id = f"{name}_{int(time.time() * 1000)}"
        self._active_profiles[profile_id] = time.time()
        
        logger.debug(f"开始性能分析: {name} ({profile_id})")
        return profile_id
    
    def end_profile(self, profile_id: str, metadata: Dict[str, Any] = None) -> Optional[ProfileResult]:
        """结束性能分析"""
        if profile_id not in self._active_profiles:
            logger.warning(f"性能分析不存在: {profile_id}")
            return None
        
        start_time = self._active_profiles[profile_id]
        end_time = time.time()
        duration = end_time - start_time
        
        # 从profile_id中提取名称
        name = profile_id.split('_')[0] if '_' in profile_id else profile_id
        
        result = ProfileResult(
            name=name,
            profiler_type=ProfilerType.TIME,
            start_time=start_time,
            end_time=end_time,
            duration=duration,
            metadata=metadata or {}
        )
        
        self._results.append(result)
        del self._active_profiles[profile_id]
        
        # 限制结果数量
        if len(self._results) > self._max_results:
            self._results = self._results[-self._max_results:]
        
        logger.debug(f"结束性能分析: {name}, 耗时: {duration:.3f}s")
        return result
    
    def profile_function(self, func: Callable, *args, **kwargs) -> ProfileResult:
        """分析函数执行"""
        name = func.__name__
        profile_id = self.start_profile(name)
        
        try:
            start_time = time.time()
            result = func(*args, **kwargs)
            end_time = time.time()
            
            metadata = {
                'function': name,
                'args_count': len(args),
                'kwargs_count': len(kwargs),
                'success': True
            }
            
            profile_result = self.end_profile(profile_id, metadata)
            return profile_result
            
        except Exception as e:
            end_time = time.time()
            metadata = {
                'function': name,
                'args_count': len(args),
                'kwargs_count': len(kwargs),
                'success': False,
                'error': str(e)
            }
            
            profile_result = self.end_profile(profile_id, metadata)
            raise
    
    def get_results(self, name: Optional[str] = None, limit: int = 100) -> List[ProfileResult]:
        """获取分析结果"""
        results = self._results
        
        if name:
            results = [r for r in results if r.name == name]
        
        return results[-limit:]
    
    def get_stats(self) -> Dict[str, Any]:
        """获取统计信息"""
        if not self._results:
            return {
                'total_profiles': 0,
                'average_duration': 0,
                'max_duration': 0,
                'min_duration': 0
            }
        
        durations = [r.duration for r in self._results]
        
        return {
            'total_profiles': len(self._results),
            'active_profiles': len(self._active_profiles),
            'average_duration': sum(durations) / len(durations),
            'max_duration': max(durations),
            'min_duration': min(durations),
            'function_stats': self._get_function_stats()
        }
    
    def _get_function_stats(self) -> Dict[str, Any]:
        """获取函数统计"""
        function_stats = {}
        
        for result in self._results:
            name = result.name
            if name not in function_stats:
                function_stats[name] = {
                    'count': 0,
                    'total_duration': 0,
                    'avg_duration': 0,
                    'max_duration': 0,
                    'min_duration': float('inf')
                }
            
            stats = function_stats[name]
            stats['count'] += 1
            stats['total_duration'] += result.duration
            stats['max_duration'] = max(stats['max_duration'], result.duration)
            stats['min_duration'] = min(stats['min_duration'], result.duration)
            stats['avg_duration'] = stats['total_duration'] / stats['count']
        
        return function_stats
    
    def clear_results(self):
        """清空结果"""
        self._results.clear()
        logger.info("已清空性能分析结果")


def profile_time(name: Optional[str] = None):
    """时间分析装饰器"""
    def decorator(func: Callable) -> Callable:
        profile_name = name or func.__name__
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            profiler = get_profiler()
            return profiler.profile_function(func, *args, **kwargs).metadata.get('result')
        
        return wrapper
    return decorator


def profile_memory(name: Optional[str] = None):
    """内存分析装饰器（简化版本）"""
    def decorator(func: Callable) -> Callable:
        profile_name = name or func.__name__
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            # 简化的内存分析
            start_time = time.time()
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            logger.debug(f"内存分析: {profile_name}, 耗时: {duration:.3f}s")
            return result
        
        return wrapper
    return decorator


# 全局分析器
_global_profiler: Optional[Profiler] = None


def get_profiler() -> Profiler:
    """获取全局分析器"""
    global _global_profiler
    if _global_profiler is None:
        _global_profiler = Profiler()
    return _global_profiler
