#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件时间戳管理器
确保知识库文件遵循时间戳命名规则
"""

import os
import shutil
from datetime import datetime
from typing import Optional, Tuple

class FileTimestampManager:
    """文件时间戳管理器"""
    
    def __init__(self):
        self.timestamp_format = '%Y%m%d%H%M'
    
    def add_timestamp_to_file(self, file_path: str, backup_original: bool = True) -> str:
        """
        为文件添加时间戳后缀
        
        Args:
            file_path: 原文件路径
            backup_original: 是否备份原文件
            
        Returns:
            带时间戳的新文件路径
        """
        try:
            if not os.path.exists(file_path):
                print(f"⚠️ 文件不存在: {file_path}")
                return file_path
            
            # 生成时间戳
            timestamp = datetime.now().strftime(self.timestamp_format)
            
            # 解析文件路径
            base_name, extension = self._split_filename(file_path)
            
            # 检查是否已有时间戳
            if self._has_timestamp(base_name):
                print(f"📋 文件已有时间戳: {file_path}")
                return file_path
            
            # 生成新文件名
            new_file_path = f"{base_name}（{timestamp}）{extension}"
            
            # 备份原文件（如果需要）
            if backup_original and file_path != new_file_path:
                backup_path = f"{base_name}.backup{extension}"
                if not os.path.exists(backup_path):
                    shutil.copy2(file_path, backup_path)
                    print(f"💾 原文件已备份: {backup_path}")
            
            # 重命名文件
            if file_path != new_file_path:
                shutil.move(file_path, new_file_path)
                print(f"📝 文件已重命名: {os.path.basename(new_file_path)}")
            
            return new_file_path
            
        except Exception as e:
            print(f"❌ 添加时间戳失败: {e}")
            return file_path
    
    def save_with_timestamp(self, content: str, file_path: str, 
                          backup_original: bool = True) -> str:
        """
        保存内容到带时间戳的文件
        
        Args:
            content: 文件内容
            file_path: 目标文件路径
            backup_original: 是否备份原文件
            
        Returns:
            实际保存的文件路径
        """
        try:
            # 如果原文件存在且需要备份
            if os.path.exists(file_path) and backup_original:
                base_name, extension = self._split_filename(file_path)
                backup_path = f"{base_name}.backup{extension}"
                if not os.path.exists(backup_path):
                    shutil.copy2(file_path, backup_path)
                    print(f"💾 原文件已备份: {backup_path}")
            
            # 生成带时间戳的文件路径
            timestamp = datetime.now().strftime(self.timestamp_format)
            base_name, extension = self._split_filename(file_path)
            
            # 检查是否已有时间戳
            if not self._has_timestamp(base_name):
                timestamped_path = f"{base_name}（{timestamp}）{extension}"
            else:
                timestamped_path = file_path
            
            # 保存文件
            with open(timestamped_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"📁 文件已保存: {os.path.basename(timestamped_path)}")
            return timestamped_path
            
        except Exception as e:
            print(f"❌ 保存文件失败: {e}")
            return file_path
    
    def update_file_with_timestamp(self, file_path: str, content: str) -> str:
        """
        更新文件内容并添加新时间戳
        
        Args:
            file_path: 文件路径
            content: 新内容
            
        Returns:
            新文件路径
        """
        try:
            # 备份原文件
            if os.path.exists(file_path):
                base_name, extension = self._split_filename(file_path)
                # 移除旧时间戳（如果有）
                clean_base_name = self._remove_timestamp(base_name)
                backup_path = f"{clean_base_name}.backup{extension}"
                shutil.copy2(file_path, backup_path)
                print(f"💾 原文件已备份: {backup_path}")
            
            # 生成新的时间戳文件
            return self.save_with_timestamp(content, file_path, backup_original=False)
            
        except Exception as e:
            print(f"❌ 更新文件失败: {e}")
            return file_path
    
    def batch_add_timestamps(self, directory: str, file_pattern: str = "*.md") -> int:
        """
        批量为目录中的文件添加时间戳
        
        Args:
            directory: 目录路径
            file_pattern: 文件模式
            
        Returns:
            处理的文件数量
        """
        try:
            import glob
            
            pattern_path = os.path.join(directory, file_pattern)
            files = glob.glob(pattern_path)
            
            processed_count = 0
            for file_path in files:
                if os.path.isfile(file_path):
                    new_path = self.add_timestamp_to_file(file_path)
                    if new_path != file_path:
                        processed_count += 1
            
            print(f"📊 批量处理完成: {processed_count}/{len(files)} 个文件")
            return processed_count
            
        except Exception as e:
            print(f"❌ 批量处理失败: {e}")
            return 0
    
    def _split_filename(self, file_path: str) -> Tuple[str, str]:
        """分离文件名和扩展名"""
        if '.' in os.path.basename(file_path):
            base_name = file_path.rsplit('.', 1)[0]
            extension = '.' + file_path.rsplit('.', 1)[1]
        else:
            base_name = file_path
            extension = ''
        return base_name, extension
    
    def _has_timestamp(self, base_name: str) -> bool:
        """检查文件名是否已有时间戳"""
        import re
        timestamp_pattern = r'（\d{12}）$'
        return bool(re.search(timestamp_pattern, base_name))
    
    def _remove_timestamp(self, base_name: str) -> str:
        """移除文件名中的时间戳"""
        import re
        timestamp_pattern = r'（\d{12}）$'
        return re.sub(timestamp_pattern, '', base_name)
    
    def get_file_timestamp(self, file_path: str) -> Optional[datetime]:
        """从文件名中提取时间戳"""
        try:
            import re
            base_name = os.path.basename(file_path)
            timestamp_pattern = r'（(\d{12})）'
            match = re.search(timestamp_pattern, base_name)
            
            if match:
                timestamp_str = match.group(1)
                return datetime.strptime(timestamp_str, self.timestamp_format)
            
            return None
            
        except Exception as e:
            print(f"❌ 提取时间戳失败: {e}")
            return None

def fix_knowledge_base_timestamps():
    """修复知识库文件的时间戳问题"""
    print("🔧 修复知识库文件时间戳问题")
    print("=" * 60)
    
    timestamp_manager = FileTimestampManager()
    
    # 需要检查的知识库文件
    knowledge_base_files = [
        "knowledge_base/faq_database.md",
        "knowledge_base/faq_manager.py",
        "knowledge_base/session_summarizer.py",
        "knowledge_base/problem_classifier.py",
        "knowledge_base/solution_templates.py",
        "knowledge_base/knowledge_graph.py"
    ]
    
    processed_count = 0
    for file_path in knowledge_base_files:
        if os.path.exists(file_path):
            print(f"\n📋 检查文件: {file_path}")
            
            # 检查是否已有时间戳
            base_name, _ = timestamp_manager._split_filename(file_path)
            if timestamp_manager._has_timestamp(base_name):
                print(f"  ✅ 已有时间戳，跳过")
                continue
            
            # 添加时间戳
            new_path = timestamp_manager.add_timestamp_to_file(file_path)
            if new_path != file_path:
                processed_count += 1
                print(f"  ✅ 已添加时间戳")
            else:
                print(f"  ⚠️ 未处理")
        else:
            print(f"  ❌ 文件不存在: {file_path}")
    
    print(f"\n📊 处理结果: {processed_count} 个文件已添加时间戳")
    
    # 显示当前知识库文件状态
    print(f"\n📁 当前知识库文件:")
    if os.path.exists("knowledge_base"):
        for file_name in os.listdir("knowledge_base"):
            if file_name.endswith(('.py', '.md')) and not file_name.startswith('__'):
                file_path = os.path.join("knowledge_base", file_name)
                timestamp = timestamp_manager.get_file_timestamp(file_path)
                if timestamp:
                    print(f"  ✅ {file_name} (时间戳: {timestamp.strftime('%Y-%m-%d %H:%M')})")
                else:
                    print(f"  ⚠️ {file_name} (无时间戳)")

def demonstrate_timestamp_manager():
    """演示时间戳管理器"""
    print("🚀 文件时间戳管理器演示")
    print("=" * 60)
    
    timestamp_manager = FileTimestampManager()
    
    # 演示功能
    print("📋 主要功能:")
    print("1. add_timestamp_to_file() - 为现有文件添加时间戳")
    print("2. save_with_timestamp() - 保存内容到带时间戳的文件")
    print("3. update_file_with_timestamp() - 更新文件并添加新时间戳")
    print("4. batch_add_timestamps() - 批量处理目录中的文件")
    
    print(f"\n🎯 时间戳格式: {timestamp_manager.timestamp_format}")
    print(f"📅 当前时间戳: {datetime.now().strftime(timestamp_manager.timestamp_format)}")
    
    print("\n✅ 时间戳管理器初始化完成")

if __name__ == '__main__':
    # 演示时间戳管理器
    demonstrate_timestamp_manager()
    
    # 修复知识库时间戳问题
    fix_knowledge_base_timestamps()
