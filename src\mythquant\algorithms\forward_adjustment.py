#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
前复权计算模块

提供高精度的前复权价格计算功能，支持除权除息数据处理
"""

import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Dict, List, Tuple
import logging

# 导入新架构的配置系统
from src.mythquant.config.manager import ConfigManager

logger = logging.getLogger(__name__)


class ForwardAdjustmentCalculator:
    """前复权计算器 - 高精度前复权价格计算"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化前复权计算器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.precision = Decimal('0.001')  # 价格精度：3位小数
        self.ratio_precision = Decimal('0.000001')  # 比率精度：6位小数
    
    def calculate_forward_adjustment(self, price_data: pd.DataFrame, 
                                   dividend_data: Optional[pd.DataFrame] = None) -> pd.DataFrame:
        """
        计算前复权价格
        
        Args:
            price_data: 价格数据DataFrame，包含date, open, high, low, close, volume等列
            dividend_data: 除权除息数据DataFrame，包含date, dividend, split_ratio等列
            
        Returns:
            包含前复权价格的DataFrame
        """
        if price_data is None or price_data.empty:
            logger.warning("价格数据为空，无法计算前复权")
            return pd.DataFrame()
        
        try:
            # 复制数据以避免修改原始数据
            result_df = price_data.copy()
            
            # 确保日期列为datetime类型
            if 'date' in result_df.columns:
                result_df['date'] = pd.to_datetime(result_df['date'])
                result_df = result_df.sort_values('date')
            
            # 如果没有除权除息数据，直接返回原价格作为前复权价格
            if dividend_data is None or dividend_data.empty:
                logger.info("无除权除息数据，前复权价格等于原价格")
                result_df['adj_open'] = result_df['open']
                result_df['adj_high'] = result_df['high']
                result_df['adj_low'] = result_df['low']
                result_df['adj_close'] = result_df['close']
                return result_df
            
            # 处理除权除息数据
            dividend_df = dividend_data.copy()
            dividend_df['date'] = pd.to_datetime(dividend_df['date'])
            dividend_df = dividend_df.sort_values('date')
            
            # 计算复权因子
            adjustment_factors = self._calculate_adjustment_factors(result_df, dividend_df)
            
            # 应用复权因子计算前复权价格
            result_df = self._apply_adjustment_factors(result_df, adjustment_factors)
            
            logger.info(f"前复权计算完成，处理了{len(result_df)}条价格记录")
            return result_df
            
        except Exception as e:
            logger.error(f"前复权计算失败: {e}")
            return pd.DataFrame()
    
    def _calculate_adjustment_factors(self, price_data: pd.DataFrame, 
                                    dividend_data: pd.DataFrame) -> Dict[str, Decimal]:
        """
        计算复权因子
        
        Args:
            price_data: 价格数据
            dividend_data: 除权除息数据
            
        Returns:
            日期到复权因子的映射字典
        """
        adjustment_factors = {}
        cumulative_factor = Decimal('1.0')
        
        try:
            # 从最新日期开始向前计算
            for _, dividend_row in dividend_data.iterrows():
                ex_date = dividend_row['date']
                dividend = Decimal(str(dividend_row.get('dividend', 0)))
                split_ratio = Decimal(str(dividend_row.get('split_ratio', 1)))
                
                # 获取除权日的收盘价
                ex_date_price = self._get_price_on_date(price_data, ex_date)
                if ex_date_price is None:
                    continue
                
                # 计算复权因子
                # 前复权公式：复权价格 = (原价格 - 股息) / 拆股比例
                if ex_date_price > 0:
                    factor = (ex_date_price - dividend) / (ex_date_price * split_ratio)
                    cumulative_factor *= factor
                    adjustment_factors[ex_date.strftime('%Y-%m-%d')] = cumulative_factor
                
                logger.debug(f"除权日{ex_date}: 股息={dividend}, 拆股比例={split_ratio}, 累积因子={cumulative_factor}")
            
            return adjustment_factors
            
        except Exception as e:
            logger.error(f"计算复权因子失败: {e}")
            return {}
    
    def _get_price_on_date(self, price_data: pd.DataFrame, target_date: pd.Timestamp) -> Optional[Decimal]:
        """
        获取指定日期的收盘价
        
        Args:
            price_data: 价格数据
            target_date: 目标日期
            
        Returns:
            收盘价或None
        """
        try:
            matching_rows = price_data[price_data['date'] == target_date]
            if not matching_rows.empty:
                close_price = matching_rows.iloc[0]['close']
                return Decimal(str(close_price))
            return None
        except Exception as e:
            logger.error(f"获取{target_date}价格失败: {e}")
            return None
    
    def _apply_adjustment_factors(self, price_data: pd.DataFrame, 
                                adjustment_factors: Dict[str, Decimal]) -> pd.DataFrame:
        """
        应用复权因子计算前复权价格
        
        Args:
            price_data: 价格数据
            adjustment_factors: 复权因子字典
            
        Returns:
            包含前复权价格的DataFrame
        """
        result_df = price_data.copy()
        
        try:
            # 初始化前复权价格列
            result_df['adj_open'] = result_df['open']
            result_df['adj_high'] = result_df['high']
            result_df['adj_low'] = result_df['low']
            result_df['adj_close'] = result_df['close']
            
            # 如果没有复权因子，直接返回
            if not adjustment_factors:
                return result_df
            
            # 按日期应用复权因子
            for idx, row in result_df.iterrows():
                date_str = row['date'].strftime('%Y-%m-%d')
                
                # 找到适用的复权因子
                applicable_factor = Decimal('1.0')
                for factor_date, factor in adjustment_factors.items():
                    if date_str <= factor_date:
                        applicable_factor = factor
                        break
                
                # 应用复权因子
                if applicable_factor != Decimal('1.0'):
                    result_df.at[idx, 'adj_open'] = self._apply_precision(
                        Decimal(str(row['open'])) * applicable_factor
                    )
                    result_df.at[idx, 'adj_high'] = self._apply_precision(
                        Decimal(str(row['high'])) * applicable_factor
                    )
                    result_df.at[idx, 'adj_low'] = self._apply_precision(
                        Decimal(str(row['low'])) * applicable_factor
                    )
                    result_df.at[idx, 'adj_close'] = self._apply_precision(
                        Decimal(str(row['close'])) * applicable_factor
                    )
            
            return result_df
            
        except Exception as e:
            logger.error(f"应用复权因子失败: {e}")
            return result_df
    
    def _apply_precision(self, value: Decimal) -> float:
        """
        应用精度控制
        
        Args:
            value: Decimal值
            
        Returns:
            精度控制后的float值
        """
        try:
            return float(value.quantize(self.precision, rounding=ROUND_HALF_UP))
        except Exception:
            return float(value)
    
    def calculate_adjustment_ratio(self, original_price: float, adjusted_price: float) -> float:
        """
        计算复权比率
        
        Args:
            original_price: 原始价格
            adjusted_price: 复权价格
            
        Returns:
            复权比率
        """
        try:
            if original_price == 0:
                return 1.0
            
            ratio = Decimal(str(adjusted_price)) / Decimal(str(original_price))
            return float(ratio.quantize(self.ratio_precision, rounding=ROUND_HALF_UP))
            
        except Exception as e:
            logger.error(f"计算复权比率失败: {e}")
            return 1.0
    
    def validate_adjustment_data(self, price_data: pd.DataFrame, 
                               dividend_data: Optional[pd.DataFrame] = None) -> Tuple[bool, List[str]]:
        """
        验证复权数据的有效性
        
        Args:
            price_data: 价格数据
            dividend_data: 除权除息数据
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            # 验证价格数据
            if price_data is None or price_data.empty:
                errors.append("价格数据为空")
                return False, errors
            
            required_columns = ['date', 'open', 'high', 'low', 'close']
            missing_columns = [col for col in required_columns if col not in price_data.columns]
            if missing_columns:
                errors.append(f"价格数据缺少必要列: {missing_columns}")
            
            # 检查价格数据的有效性
            numeric_columns = ['open', 'high', 'low', 'close']
            for col in numeric_columns:
                if col in price_data.columns:
                    if price_data[col].isnull().any():
                        errors.append(f"价格数据{col}列包含空值")
                    if (price_data[col] <= 0).any():
                        errors.append(f"价格数据{col}列包含非正值")
            
            # 验证除权除息数据
            if dividend_data is not None and not dividend_data.empty:
                dividend_required = ['date', 'dividend', 'split_ratio']
                missing_dividend_columns = [col for col in dividend_required if col not in dividend_data.columns]
                if missing_dividend_columns:
                    errors.append(f"除权除息数据缺少必要列: {missing_dividend_columns}")
            
            is_valid = len(errors) == 0
            return is_valid, errors
            
        except Exception as e:
            errors.append(f"数据验证异常: {e}")
            return False, errors


# 导出
__all__ = ['ForwardAdjustmentCalculator']
