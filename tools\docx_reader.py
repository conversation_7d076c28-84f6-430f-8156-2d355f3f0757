#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DOCX文档读取工具

提供读取和转换.docx文件的功能
"""

import sys
from pathlib import Path
import argparse

try:
    from docx import Document
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False
    print("⚠️ python-docx库未安装，请运行: pip install python-docx")


class DocxReader:
    """DOCX文档读取器"""
    
    def __init__(self):
        if not DOCX_AVAILABLE:
            raise ImportError("python-docx库未安装")
    
    def read_docx(self, file_path: str) -> dict:
        """读取DOCX文件内容"""
        try:
            doc = Document(file_path)
            
            content = {
                'title': self._extract_title(doc),
                'paragraphs': [],
                'tables': [],
                'headers': [],
                'footers': [],
                'metadata': self._extract_metadata(doc)
            }
            
            # 提取段落
            for para in doc.paragraphs:
                if para.text.strip():
                    content['paragraphs'].append({
                        'text': para.text,
                        'style': para.style.name if para.style else 'Normal'
                    })
            
            # 提取表格
            for table in doc.tables:
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        row_data.append(cell.text.strip())
                    table_data.append(row_data)
                content['tables'].append(table_data)
            
            # 提取页眉页脚
            for section in doc.sections:
                if section.header:
                    for para in section.header.paragraphs:
                        if para.text.strip():
                            content['headers'].append(para.text)
                
                if section.footer:
                    for para in section.footer.paragraphs:
                        if para.text.strip():
                            content['footers'].append(para.text)
            
            return content
            
        except Exception as e:
            raise Exception(f"读取DOCX文件失败: {e}")
    
    def _extract_title(self, doc) -> str:
        """提取文档标题"""
        # 尝试从第一个段落提取标题
        for para in doc.paragraphs:
            if para.text.strip():
                return para.text.strip()
        return "未知标题"
    
    def _extract_metadata(self, doc) -> dict:
        """提取文档元数据"""
        core_props = doc.core_properties
        return {
            'author': core_props.author or '',
            'title': core_props.title or '',
            'subject': core_props.subject or '',
            'created': str(core_props.created) if core_props.created else '',
            'modified': str(core_props.modified) if core_props.modified else '',
            'last_modified_by': core_props.last_modified_by or ''
        }
    
    def convert_to_markdown(self, file_path: str, output_path: str = None) -> str:
        """将DOCX转换为Markdown格式"""
        content = self.read_docx(file_path)
        
        markdown = f"# {content['title']}\n\n"
        
        # 添加元数据
        if content['metadata']['author']:
            markdown += f"**作者**: {content['metadata']['author']}\n"
        if content['metadata']['created']:
            markdown += f"**创建时间**: {content['metadata']['created']}\n"
        if content['metadata']['modified']:
            markdown += f"**修改时间**: {content['metadata']['modified']}\n"
        markdown += "\n---\n\n"
        
        # 添加段落内容
        for para in content['paragraphs']:
            text = para['text']
            style = para['style']
            
            # 根据样式格式化
            if 'Heading' in style:
                level = self._get_heading_level(style)
                markdown += f"{'#' * level} {text}\n\n"
            else:
                markdown += f"{text}\n\n"
        
        # 添加表格
        for i, table in enumerate(content['tables']):
            markdown += f"## 表格 {i+1}\n\n"
            if table:
                # 表头
                markdown += "| " + " | ".join(table[0]) + " |\n"
                markdown += "| " + " | ".join(['---'] * len(table[0])) + " |\n"
                
                # 表格内容
                for row in table[1:]:
                    markdown += "| " + " | ".join(row) + " |\n"
                markdown += "\n"
        
        # 保存到文件
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(markdown)
            print(f"✅ Markdown文件已保存: {output_path}")
        
        return markdown
    
    def _get_heading_level(self, style_name: str) -> int:
        """获取标题级别"""
        if 'Heading 1' in style_name:
            return 1
        elif 'Heading 2' in style_name:
            return 2
        elif 'Heading 3' in style_name:
            return 3
        elif 'Heading 4' in style_name:
            return 4
        elif 'Heading 5' in style_name:
            return 5
        elif 'Heading 6' in style_name:
            return 6
        else:
            return 2  # 默认二级标题
    
    def print_content(self, file_path: str):
        """打印DOCX文件内容"""
        content = self.read_docx(file_path)
        
        print(f"📄 文档标题: {content['title']}")
        print(f"👤 作者: {content['metadata']['author']}")
        print(f"📅 创建时间: {content['metadata']['created']}")
        print(f"🔄 修改时间: {content['metadata']['modified']}")
        print("=" * 60)
        
        print("\n📝 文档内容:")
        for i, para in enumerate(content['paragraphs'], 1):
            print(f"{i:3d}. [{para['style']}] {para['text']}")
        
        if content['tables']:
            print(f"\n📊 表格数量: {len(content['tables'])}")
            for i, table in enumerate(content['tables'], 1):
                print(f"\n表格 {i}:")
                for row in table:
                    print("  | " + " | ".join(row))
        
        if content['headers']:
            print(f"\n📋 页眉:")
            for header in content['headers']:
                print(f"  {header}")
        
        if content['footers']:
            print(f"\n📋 页脚:")
            for footer in content['footers']:
                print(f"  {footer}")


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='DOCX文档读取工具')
    parser.add_argument('file_path', help='DOCX文件路径')
    parser.add_argument('--convert', '-c', help='转换为Markdown文件路径')
    parser.add_argument('--print', '-p', action='store_true', help='打印文档内容')
    
    args = parser.parse_args()
    
    if not DOCX_AVAILABLE:
        print("❌ 请先安装python-docx库: pip install python-docx")
        sys.exit(1)
    
    file_path = Path(args.file_path)
    if not file_path.exists():
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    if not file_path.suffix.lower() == '.docx':
        print(f"❌ 不是DOCX文件: {file_path}")
        sys.exit(1)
    
    try:
        reader = DocxReader()
        
        if args.convert:
            markdown = reader.convert_to_markdown(str(file_path), args.convert)
            print(f"✅ 转换完成: {file_path} → {args.convert}")
        
        if args.print:
            reader.print_content(str(file_path))
        
        if not args.convert and not args.print:
            # 默认转换为同名的.md文件
            output_path = file_path.with_suffix('.md')
            reader.convert_to_markdown(str(file_path), str(output_path))
    
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
