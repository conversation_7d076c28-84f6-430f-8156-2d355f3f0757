#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场领域事件

市场相关的业务事件定义
"""

from dataclasses import dataclass
from datetime import datetime
from typing import Optional
from ..value_objects.trading import TradingSession
from ...shared.patterns.architectural_patterns import DomainEvent


@dataclass
class MarketOpened(DomainEvent):
    """市场开市事件"""
    market_id: str
    market_code: str
    session: TradingSession
    opened_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"market_opened_{self.market_id}_{int(self.opened_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="MarketOpened",
            occurred_at=self.opened_at,
            version=1,
            data={
                'market_code': self.market_code,
                'session': self.session.value,
                'session_name': self.session.display_name
            }
        )


@dataclass
class MarketClosed(DomainEvent):
    """市场收市事件"""
    market_id: str
    market_code: str
    session: Optional[TradingSession]
    closed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"market_closed_{self.market_id}_{int(self.closed_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="MarketClosed",
            occurred_at=self.closed_at,
            version=1,
            data={
                'market_code': self.market_code,
                'session': self.session.value if self.session else None,
                'session_name': self.session.display_name if self.session else None
            }
        )


@dataclass
class MarketHalted(DomainEvent):
    """市场暂停交易事件"""
    market_id: str
    market_code: str
    reason: str
    halted_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"market_halted_{self.market_id}_{int(self.halted_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="MarketHalted",
            occurred_at=self.halted_at,
            version=1,
            data={
                'market_code': self.market_code,
                'reason': self.reason
            }
        )


@dataclass
class TradingSessionStarted(DomainEvent):
    """交易时段开始事件"""
    market_id: str
    market_code: str
    session: TradingSession
    started_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"session_started_{self.market_id}_{self.session.value}_{int(self.started_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="TradingSessionStarted",
            occurred_at=self.started_at,
            version=1,
            data={
                'market_code': self.market_code,
                'session': self.session.value,
                'session_name': self.session.display_name
            }
        )


@dataclass
class TradingSessionEnded(DomainEvent):
    """交易时段结束事件"""
    market_id: str
    market_code: str
    session: TradingSession
    ended_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"session_ended_{self.market_id}_{self.session.value}_{int(self.ended_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="TradingSessionEnded",
            occurred_at=self.ended_at,
            version=1,
            data={
                'market_code': self.market_code,
                'session': self.session.value,
                'session_name': self.session.display_name
            }
        )


@dataclass
class TradingResumed(DomainEvent):
    """交易恢复事件"""
    market_id: str
    market_code: str
    resumed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"trading_resumed_{self.market_id}_{int(self.resumed_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="TradingResumed",
            occurred_at=self.resumed_at,
            version=1,
            data={
                'market_code': self.market_code
            }
        )


@dataclass
class MarketHolidayAdded(DomainEvent):
    """市场假期添加事件"""
    market_id: str
    market_code: str
    holiday_date: str  # ISO格式日期
    holiday_name: str
    is_full_day: bool
    added_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"holiday_added_{self.market_id}_{self.holiday_date}_{int(self.added_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="MarketHolidayAdded",
            occurred_at=self.added_at,
            version=1,
            data={
                'market_code': self.market_code,
                'holiday_date': self.holiday_date,
                'holiday_name': self.holiday_name,
                'is_full_day': self.is_full_day
            }
        )


@dataclass
class MarketHolidayRemoved(DomainEvent):
    """市场假期移除事件"""
    market_id: str
    market_code: str
    holiday_date: str  # ISO格式日期
    removed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"holiday_removed_{self.market_id}_{self.holiday_date}_{int(self.removed_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="MarketHolidayRemoved",
            occurred_at=self.removed_at,
            version=1,
            data={
                'market_code': self.market_code,
                'holiday_date': self.holiday_date
            }
        )


@dataclass
class StockListed(DomainEvent):
    """股票上市事件"""
    market_id: str
    market_code: str
    stock_code: str
    stock_name: str
    listed_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"stock_listed_{self.market_id}_{self.stock_code}_{int(self.listed_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="StockListed",
            occurred_at=self.listed_at,
            version=1,
            data={
                'market_code': self.market_code,
                'stock_code': self.stock_code,
                'stock_name': self.stock_name
            }
        )


@dataclass
class StockDelisted(DomainEvent):
    """股票退市事件"""
    market_id: str
    market_code: str
    stock_code: str
    reason: str
    delisted_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"stock_delisted_{self.market_id}_{self.stock_code}_{int(self.delisted_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="StockDelisted",
            occurred_at=self.delisted_at,
            version=1,
            data={
                'market_code': self.market_code,
                'stock_code': self.stock_code,
                'reason': self.reason
            }
        )


@dataclass
class MarketDataUpdated(DomainEvent):
    """市场数据更新事件"""
    market_id: str
    market_code: str
    data_type: str  # PRICE, VOLUME, INDEX等
    update_count: int
    updated_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"market_data_updated_{self.market_id}_{self.data_type}_{int(self.updated_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="MarketDataUpdated",
            occurred_at=self.updated_at,
            version=1,
            data={
                'market_code': self.market_code,
                'data_type': self.data_type,
                'update_count': self.update_count
            }
        )


@dataclass
class MarketIndexUpdated(DomainEvent):
    """市场指数更新事件"""
    market_id: str
    market_code: str
    index_name: str
    old_value: Optional[float]
    new_value: float
    updated_at: datetime
    
    def __post_init__(self):
        super().__init__(
            event_id=f"index_updated_{self.market_id}_{self.index_name}_{int(self.updated_at.timestamp())}",
            aggregate_id=self.market_id,
            event_type="MarketIndexUpdated",
            occurred_at=self.updated_at,
            version=1,
            data={
                'market_code': self.market_code,
                'index_name': self.index_name,
                'old_value': self.old_value,
                'new_value': self.new_value,
                'change': self.new_value - self.old_value if self.old_value else None,
                'change_percentage': (
                    (self.new_value - self.old_value) / self.old_value * 100
                    if self.old_value and self.old_value != 0 else None
                )
            }
        )


# 事件工厂函数
def create_market_opened_event(market_id: str, market_code: str, 
                              session: TradingSession) -> MarketOpened:
    """创建市场开市事件"""
    return MarketOpened(
        market_id=market_id,
        market_code=market_code,
        session=session,
        opened_at=datetime.now()
    )


def create_market_closed_event(market_id: str, market_code: str,
                              session: Optional[TradingSession] = None) -> MarketClosed:
    """创建市场收市事件"""
    return MarketClosed(
        market_id=market_id,
        market_code=market_code,
        session=session,
        closed_at=datetime.now()
    )


def create_market_halted_event(market_id: str, market_code: str,
                              reason: str) -> MarketHalted:
    """创建市场暂停事件"""
    return MarketHalted(
        market_id=market_id,
        market_code=market_code,
        reason=reason,
        halted_at=datetime.now()
    )


def create_trading_session_started_event(market_id: str, market_code: str,
                                        session: TradingSession) -> TradingSessionStarted:
    """创建交易时段开始事件"""
    return TradingSessionStarted(
        market_id=market_id,
        market_code=market_code,
        session=session,
        started_at=datetime.now()
    )


def create_trading_session_ended_event(market_id: str, market_code: str,
                                      session: TradingSession) -> TradingSessionEnded:
    """创建交易时段结束事件"""
    return TradingSessionEnded(
        market_id=market_id,
        market_code=market_code,
        session=session,
        ended_at=datetime.now()
    )
