# MythQuant 用户指南

## 欢迎使用 MythQuant

MythQuant 是一个现代化的Python量化交易系统，提供高精度的股票数据处理、算法计算和数据分析功能。

## 快速入门

### 系统要求

- Python 3.7+
- pandas >= 1.0.0
- numpy >= 1.18.0
- 可选: openpyxl (Excel支持)

### 安装配置

1. **下载项目**
   ```bash
   git clone https://github.com/your-repo/mythquant.git
   cd mythquant
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置系统**
   编辑 `user_config.py` 文件：
   ```python
   # TDX安装路径
   tdx_path = r"C:\通达信"
   
   # 输出路径
   output_path = r"D:\MythQuant\Output"
   
   # 调试模式
   DEBUG = True
   ```

### 第一个程序

创建 `hello_mythquant.py`：

```python
import config_compatibility as config
import data_access_compatibility as data_access
import algorithm_compatibility as algo
import io_compatibility as io_compat

def main():
    print("🚀 欢迎使用 MythQuant!")
    
    # 1. 检查配置
    print(f"TDX路径: {config.get_tdx_path()}")
    print(f"输出路径: {config.get_output_path()}")
    
    # 2. 测试数据源连通性
    connectivity = data_access.test_data_sources_connectivity()
    print("数据源状态:")
    for source, status in connectivity.items():
        status_icon = "✅" if status else "❌"
        print(f"  {status_icon} {source}")
    
    # 3. 获取股票列表
    stock_list = data_access.get_stock_list()
    print(f"可用股票: {len(stock_list)} 只")
    
    print("✅ MythQuant 系统正常运行!")

if __name__ == "__main__":
    main()
```

运行程序：
```bash
python hello_mythquant.py
```

## 核心功能使用

### 1. 股票数据获取

#### 获取日线数据

```python
import data_access_compatibility as data_access

# 获取单只股票日线数据
stock_code = "000001"
day_data = data_access.read_stock_day_data(stock_code)

if day_data is not None:
    print(f"获取到 {len(day_data)} 条日线数据")
    print(day_data.head())
else:
    print("数据获取失败")
```

#### 获取分钟数据

```python
# 获取1分钟数据
minute_data = data_access.read_stock_minute_data("000001", frequency=1)

# 获取5分钟数据
minute_5_data = data_access.read_stock_minute_data("000001", frequency=5)
```

#### 批量获取数据

```python
# 获取多只股票数据
stock_codes = ["000001", "000002", "000858"]
all_data = {}

for code in stock_codes:
    data = data_access.read_stock_day_data(code)
    if data is not None:
        all_data[code] = data
        print(f"✅ {code}: {len(data)} 条记录")
    else:
        print(f"❌ {code}: 获取失败")
```

### 2. 算法计算

#### L2指标计算

```python
import algorithm_compatibility as algo

# 计算L2指标
l2_data = algo.calculate_l2_metrics(day_data)

if l2_data is not None:
    print("L2指标计算完成")
    print("包含列:", list(l2_data.columns))
    
    # 查看主买主卖数据
    print("\n主买主卖统计:")
    print(f"平均主买: {l2_data['main_buy'].mean():.3f}")
    print(f"平均主卖: {l2_data['main_sell'].mean():.3f}")
    print(f"平均路径总长: {l2_data['path_length'].mean():.3f}")
```

#### 前复权计算

```python
# 前复权计算（需要除权除息数据）
adj_data = algo.calculate_forward_adjustment(day_data)

if adj_data is not None:
    print("前复权计算完成")
    
    # 比较原价格和前复权价格
    comparison = adj_data[['date', 'close', 'adj_close']].head()
    print(comparison)
```

#### 技术指标计算

```python
# 计算技术指标
tech_data = algo.calculate_technical_indicators(day_data)

if tech_data is not None:
    print("技术指标计算完成")
    
    # 查看移动平均线
    ma_columns = [col for col in tech_data.columns if col.startswith('ma_')]
    print("移动平均线:", ma_columns)
    
    # 查看最新的技术指标
    latest = tech_data.iloc[-1]
    print(f"最新收盘价: {latest['close']:.3f}")
    print(f"5日均线: {latest.get('ma_5', 'N/A')}")
    print(f"20日均线: {latest.get('ma_20', 'N/A')}")
```

### 3. 数据输出

#### 标准格式输出

```python
import io_compatibility as io_compat

# 写入标准格式文件
output_path = io_compat.write_stock_data_file(
    l2_data, 
    stock_code="000001", 
    data_type="day",
    start_date="20230101",
    end_date="20231231"
)

if output_path:
    print(f"✅ 数据已写入: {output_path}")
else:
    print("❌ 数据写入失败")
```

#### CSV格式输出

```python
# 写入CSV文件
csv_path = io_compat.write_csv_file(
    l2_data, 
    filename="stock_analysis.csv",
    subdirectory="analysis"
)

if csv_path:
    print(f"✅ CSV文件已保存: {csv_path}")
```

#### 批量输出

```python
# 批量写入多只股票数据
results = io_compat.batch_write_stock_data(all_data, data_type="day")

print("批量写入结果:")
for stock_code, output_path in results.items():
    if output_path:
        print(f"✅ {stock_code}: {output_path}")
    else:
        print(f"❌ {stock_code}: 写入失败")
```

## 完整工作流程示例

### 单只股票分析流程

```python
def analyze_single_stock(stock_code):
    """分析单只股票的完整流程"""
    print(f"🔍 开始分析股票: {stock_code}")
    
    # 1. 获取数据
    print("📊 获取股票数据...")
    day_data = data_access.read_stock_day_data(stock_code)
    
    if day_data is None or day_data.empty:
        print(f"❌ 无法获取 {stock_code} 的数据")
        return None
    
    print(f"✅ 获取到 {len(day_data)} 条日线数据")
    
    # 2. 计算指标
    print("🧮 计算L2指标...")
    l2_data = algo.calculate_l2_metrics(day_data)
    
    if l2_data is None or l2_data.empty:
        print("❌ L2指标计算失败")
        return None
    
    print("✅ L2指标计算完成")
    
    # 3. 计算技术指标
    print("📈 计算技术指标...")
    tech_data = algo.calculate_technical_indicators(l2_data)
    
    # 4. 输出结果
    print("💾 保存分析结果...")
    output_path = io_compat.write_stock_data_file(
        tech_data, stock_code, "day"
    )
    
    if output_path:
        print(f"✅ 分析完成，结果保存至: {output_path}")
        return output_path
    else:
        print("❌ 结果保存失败")
        return None

# 使用示例
result = analyze_single_stock("000001")
```

### 多股票批量分析

```python
def batch_analyze_stocks(stock_codes):
    """批量分析多只股票"""
    print(f"🚀 开始批量分析 {len(stock_codes)} 只股票")
    
    results = {}
    
    for i, stock_code in enumerate(stock_codes, 1):
        print(f"\n[{i}/{len(stock_codes)}] 处理 {stock_code}")
        
        try:
            result = analyze_single_stock(stock_code)
            results[stock_code] = result
        except Exception as e:
            print(f"❌ {stock_code} 分析失败: {e}")
            results[stock_code] = None
    
    # 统计结果
    success_count = sum(1 for r in results.values() if r is not None)
    print(f"\n📊 批量分析完成:")
    print(f"  成功: {success_count}/{len(stock_codes)}")
    print(f"  成功率: {success_count/len(stock_codes)*100:.1f}%")
    
    return results

# 使用示例
stock_list = ["000001", "000002", "000858", "600000", "600036"]
batch_results = batch_analyze_stocks(stock_list)
```

## 高级功能

### 1. 自定义数据源优先级

```python
from mythquant.data.sources import DataSourceManager, DataSourceType
from mythquant.config import config_manager

# 创建数据源管理器
data_manager = DataSourceManager(config_manager)

# 自定义数据源优先级
data = data_manager.get_stock_data(
    stock_code="000001",
    data_type="day",
    source_priority=[
        DataSourceType.TDX,      # 优先使用TDX
        DataSourceType.PYTDX,    # 其次使用PyTDX
        DataSourceType.INTERNET  # 最后使用互联网数据源
    ]
)
```

### 2. 高精度计算配置

```python
from mythquant.algorithms import L2MetricsCalculator
from mythquant.config import config_manager

# 创建高精度L2计算器
l2_calculator = L2MetricsCalculator(config_manager)

# 计算L2指标
l2_data = l2_calculator.calculate_l2_metrics(stock_data)

# 获取计算统计
summary = l2_calculator.get_metrics_summary(l2_data)
print("计算统计:", summary)
```

### 3. 自定义输出格式

```python
from mythquant.io import DataFormatter, OutputWriter
from mythquant.config import config_manager

# 创建格式化器和写入器
formatter = DataFormatter(config_manager)
writer = OutputWriter(config_manager)

# 自定义格式化
formatted_content = formatter.format_stock_data_for_output(
    l2_data, "000001", "day"
)

# 自定义文件名
filename = formatter.format_filename(
    "000001", "day", "20230101", "20231231", "custom_source"
)

print(f"自定义文件名: {filename}")
```

## 故障排除

### 常见问题

#### 1. TDX路径配置错误

**问题**: `TDX路径不存在或无法访问`

**解决方案**:
```python
import config_compatibility as config

# 检查当前配置
current_path = config.get_tdx_path()
print(f"当前TDX路径: {current_path}")

# 修改user_config.py中的tdx_path配置
# tdx_path = r"C:\通达信"  # 修改为正确路径
```

#### 2. 数据获取失败

**问题**: `无法获取股票数据`

**解决方案**:
```python
# 测试数据源连通性
connectivity = data_access.test_data_sources_connectivity()
print("数据源状态:", connectivity)

# 尝试不同的股票代码
test_codes = ["000001", "000002", "600000"]
for code in test_codes:
    data = data_access.read_stock_day_data(code)
    print(f"{code}: {'成功' if data is not None else '失败'}")
```

#### 3. 输出文件权限错误

**问题**: `文件写入权限不足`

**解决方案**:
```python
# 检查输出路径权限
import os
output_path = config.get_output_path()
print(f"输出路径: {output_path}")
print(f"路径存在: {os.path.exists(output_path)}")
print(f"可写权限: {os.access(output_path, os.W_OK)}")

# 创建输出目录
os.makedirs(output_path, exist_ok=True)
```

### 调试模式

启用调试模式获取详细信息：

```python
# 在user_config.py中设置
DEBUG = True

# 或者临时启用
import logging
logging.basicConfig(level=logging.DEBUG)
```

### 性能优化建议

1. **批量处理**: 使用批量函数处理多只股票
2. **数据缓存**: 避免重复获取相同数据
3. **内存管理**: 处理大量数据时及时释放内存
4. **并行处理**: 对于大量股票可考虑并行处理

```python
# 内存优化示例
import gc

for stock_code in large_stock_list:
    data = data_access.read_stock_day_data(stock_code)
    result = algo.calculate_l2_metrics(data)
    io_compat.write_stock_data_file(result, stock_code, "day")
    
    # 释放内存
    del data, result
    gc.collect()
```

## 下一步

- 📖 查看 [API文档](api/README.md) 了解详细接口
- 🏗️ 查看 [架构文档](architecture.md) 了解系统设计
- 🧪 运行 `python run_tests.py` 验证系统功能
- 💡 查看 `examples/` 目录获取更多示例

## 获取帮助

- 📧 技术支持: <EMAIL>
- 📖 在线文档: https://docs.mythquant.com
- 🐛 问题报告: GitHub Issues
- 💬 社区讨论: GitHub Discussions
