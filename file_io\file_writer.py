#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文件写入器兼容性模块
为DDD架构提供向后兼容的文件写入功能
"""

import os
import sys
from typing import List, Dict
from decimal import Decimal

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from mythquant.shared.logging import get_smart_logger

logger = get_smart_logger("FileWriter")


def write_minute_txt_file(output_path: str, stock_data: List[Dict], stock_code: str, time_range: str) -> bool:
    """
    写入分钟级别买卖差txt文件 - 包含L2详细指标
    
    参数:
    output_path: str - 输出文件路径
    stock_data: List[Dict] - 股票数据列表
    stock_code: str - 股票代码
    time_range: str - 时间范围描述
    
    返回:
    bool - 写入是否成功
    """
    try:
        # 打印前10行数据内容
        print(f"\n📋 {stock_code} 分钟级别txt数据预览 (前10行):")
        print("=" * 160)
        print(f"{'序号':<4} {'股票编码':<8} {'时间':<12} {'买卖差':<12} {'当日收盘价C':<13} {'前复权收盘价C':<15} {'路径总长':<12} {'主买':<12} {'主卖':<12}")
        print("-" * 160)
        
        with open(output_path, 'w', encoding='utf-8-sig') as f:  # 使用utf-8-sig添加BOM
            # 写入表头 - 使用管道符分隔
            f.write("股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖\n")
            
            # 写入数据行
            for i, record in enumerate(stock_data):
                try:
                    # 确保股票代码格式正确（6位数字，前导零不丢失）
                    formatted_stock_code = str(stock_code).zfill(6)
                    
                    # 格式化时间
                    datetime_int = record.get('datetime_int', 0)
                    
                    # 获取各项数据，使用安全的默认值
                    buy_sell_diff = record.get('buy_sell_diff', 0)
                    close_price = record.get('close', 0)  # 当日收盘价
                    close_qfq = record.get('close_qfq', close_price)  # 前复权收盘价，默认使用当日收盘价
                    path_length = record.get('path_length', 0)
                    main_buy = record.get('main_buy', 0)
                    main_sell = record.get('main_sell', 0)
                    
                    # 格式化数值（保留3位小数）
                    buy_sell_diff_str = f"{float(buy_sell_diff):.3f}" if buy_sell_diff != 0 else "0.000"
                    close_price_str = f"{float(close_price):.3f}" if close_price != 0 else "0.000"
                    close_qfq_str = f"{float(close_qfq):.3f}" if close_qfq != 0 else "0.000"
                    path_length_str = f"{float(path_length):.3f}" if path_length != 0 else "0.000"
                    main_buy_str = f"{float(main_buy):.3f}" if main_buy != 0 else "0.000"
                    main_sell_str = f"{float(main_sell):.3f}" if main_sell != 0 else "0.000"
                    
                    # 写入数据行
                    line = f"{formatted_stock_code}|{datetime_int}|{buy_sell_diff_str}|{close_price_str}|{close_qfq_str}|{path_length_str}|{main_buy_str}|{main_sell_str}\n"
                    f.write(line)
                    
                    # 打印前10行预览
                    if i < 10:
                        print(f"{i+1:<4} {formatted_stock_code:<8} {datetime_int:<12} {buy_sell_diff_str:<12} {close_price_str:<13} {close_qfq_str:<15} {path_length_str:<12} {main_buy_str:<12} {main_sell_str:<12}")
                        
                except Exception as e:
                    logger.warning(f"处理第{i+1}行数据时出错: {e}")
                    continue
        
        print("-" * 160)
        print(f"✅ 成功写入 {len(stock_data)} 行数据到文件: {output_path}")
        logger.info(f"成功写入分钟级别txt文件: {output_path}, 数据行数: {len(stock_data)}")
        return True
        
    except Exception as e:
        logger.error(f"写入txt文件 {output_path} 时出错: {e}")
        if len(stock_data) > 0:
            sample_record = stock_data[0]
            logger.error(f"样本数据: {sample_record}")
        return False


def write_daily_txt_file(output_path: str, stock_data: List[Dict], stock_code: str, time_range: str) -> bool:
    """
    写入日线级别买卖差txt文件 - 与分钟级别格式保持一致
    
    参数:
    output_path: str - 输出文件路径
    stock_data: List[Dict] - 股票数据列表
    stock_code: str - 股票代码
    time_range: str - 时间范围描述
    
    返回:
    bool - 写入是否成功
    """
    try:
        # 打印前10行数据内容
        print(f"\n📋 {stock_code} 日线数据预览 (前10行):")
        print("=" * 160)
        print(f"{'序号':<4} {'股票编码':<8} {'时间':<12} {'买卖差':<12} {'当日收盘价C':<13} {'前复权收盘价C':<15} {'路径总长':<12} {'主买':<12} {'主卖':<12}")
        print("-" * 160)
        
        with open(output_path, 'w', encoding='utf-8-sig') as f:  # 使用utf-8-sig添加BOM
            # 写入表头 - 使用管道符分隔，与分钟级别格式一致
            f.write("股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖\n")
            
            # 写入数据行
            for i, record in enumerate(stock_data):
                try:
                    # 确保股票代码格式正确（6位数字，前导零不丢失）
                    formatted_stock_code = str(stock_code).zfill(6)
                    
                    # 格式化时间
                    datetime_int = record.get('datetime_int', 0)
                    
                    # 获取各项数据，使用安全的默认值
                    buy_sell_diff = record.get('buy_sell_diff', 0)
                    close_price = record.get('close', 0)  # 当日收盘价
                    close_qfq = record.get('close_qfq', close_price)  # 前复权收盘价，默认使用当日收盘价
                    path_length = record.get('path_length', 0)
                    main_buy = record.get('main_buy', 0)
                    main_sell = record.get('main_sell', 0)
                    
                    # 格式化数值（保留3位小数）
                    buy_sell_diff_str = f"{float(buy_sell_diff):.3f}" if buy_sell_diff != 0 else "0.000"
                    close_price_str = f"{float(close_price):.3f}" if close_price != 0 else "0.000"
                    close_qfq_str = f"{float(close_qfq):.3f}" if close_qfq != 0 else "0.000"
                    path_length_str = f"{float(path_length):.3f}" if path_length != 0 else "0.000"
                    main_buy_str = f"{float(main_buy):.3f}" if main_buy != 0 else "0.000"
                    main_sell_str = f"{float(main_sell):.3f}" if main_sell != 0 else "0.000"
                    
                    # 写入数据行
                    line = f"{formatted_stock_code}|{datetime_int}|{buy_sell_diff_str}|{close_price_str}|{close_qfq_str}|{path_length_str}|{main_buy_str}|{main_sell_str}\n"
                    f.write(line)
                    
                    # 打印前10行预览
                    if i < 10:
                        print(f"{i+1:<4} {formatted_stock_code:<8} {datetime_int:<12} {buy_sell_diff_str:<12} {close_price_str:<13} {close_qfq_str:<15} {path_length_str:<12} {main_buy_str:<12} {main_sell_str:<12}")
                        
                except Exception as e:
                    logger.warning(f"处理第{i+1}行数据时出错: {e}")
                    continue
        
        print("-" * 160)
        print(f"✅ 成功写入 {len(stock_data)} 行数据到文件: {output_path}")
        logger.info(f"成功写入日线级别txt文件: {output_path}, 数据行数: {len(stock_data)}")
        return True
        
    except Exception as e:
        logger.error(f"写入txt文件 {output_path} 时出错: {e}")
        if len(stock_data) > 0:
            sample_record = stock_data[0]
            logger.error(f"样本数据: {sample_record}")
        return False
