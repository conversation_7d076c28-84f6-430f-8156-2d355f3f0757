2025-07-29 00:36:59,507 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250729_003659.log
2025-07-29 00:36:59,507 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-29 00:36:59,508 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-29 00:36:59,508 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-29 00:36:59,508 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-29 00:36:59,508 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-29 00:36:59,508 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-29 00:36:59,509 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-29 00:36:59,509 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-29 00:36:59,957 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-29 00:36:59,957 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 00:36:59,957 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 00:36:59,957 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-29 00:37:00,146 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 3天, 频率: 1min)
2025-07-29 00:37:00,146 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-29 00:37:00,146 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 00:37:00,146 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 00:37:00,201 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-29 00:37:00,420 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-29 00:37:00,420 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-29 00:37:00,420 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-29 00:37:00,425 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-29 00:37:00,433 - Main - INFO - info:307 - ℹ️ ✅ 数据保存完成: 1min_0_000617_20250725-20250725_来源互联网（202507290037）.txt (11313 字节)
2025-07-29 00:37:00,440 - Main - INFO - info:307 - ℹ️ ✅ 增量下载前提条件验证通过
2025-07-29 00:37:00,441 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-29 00:37:00,441 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-29 00:37:00,442 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-29 00:37:00,480 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数21360, 覆盖89个交易日
2025-07-29 00:37:00,480 - Main - INFO - info:307 - ℹ️ ✅ 数据完整，无缺失
2025-07-29 00:37:00,481 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-29 00:37:00,484 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-29 00:37:00,484 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-29 00:37:00,486 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250318-20250725_来源互联网（202507290032）.txt
2025-07-29 00:37:00,486 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250318-20250725_来源互联网（202507290032）.txt
2025-07-29 00:37:00,486 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-29 00:37:00,504 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 00:37:00,504 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 00:37:00,504 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-29 00:37:00,686 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 3天, 频率: 1min)
2025-07-29 00:37:00,686 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-29 00:37:00,686 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 00:37:00,686 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 00:37:00,739 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-29 00:37:00,965 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-29 00:37:00,965 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-29 00:37:00,966 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-29 00:37:00,969 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-29 00:37:00,978 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-29 00:37:00,982 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-29 00:37:00,983 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250318 - 20250725
2025-07-29 00:37:00,983 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250317
2025-07-29 00:37:00,983 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250726 - 20250727
2025-07-29 00:37:00,996 - Main - INFO - info:307 - ℹ️ 读取现有数据: 21360 条记录
2025-07-29 00:37:00,996 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250317
2025-07-29 00:37:00,996 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 00:37:00,996 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 00:37:00,997 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250317
2025-07-29 00:37:01,172 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32880条 (目标日期: 20250101, 交易日: 137天, 频率: 1min)
2025-07-29 00:37:01,173 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32880条 -> 实际请求800条 (配置限制:800)
2025-07-29 00:37:01,173 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 00:37:01,173 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 00:37:01,223 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32080条
2025-07-29 00:37:01,453 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-29 00:37:01,681 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-29 00:37:01,915 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-29 00:37:02,139 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-29 00:37:02,367 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-29 00:37:02,598 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-29 00:37:02,829 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-29 00:37:03,058 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-29 00:37:03,284 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-29 00:37:03,515 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-29 00:37:03,738 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-29 00:37:03,969 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-29 00:37:04,193 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-29 00:37:04,433 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-29 00:37:04,656 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-29 00:37:04,882 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-29 00:37:05,105 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-29 00:37:05,340 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-29 00:37:05,563 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-29 00:37:05,794 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-29 00:37:06,027 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-29 00:37:06,257 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-29 00:37:06,476 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-29 00:37:06,703 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-29 00:37:06,936 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-29 00:37:07,157 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-29 00:37:07,384 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21600条数据
2025-07-29 00:37:07,390 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32880条，实际获取21600条
2025-07-29 00:37:07,390 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11280条数据（约47个交易日）
2025-07-29 00:37:07,391 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-29 00:37:07,391 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-29 00:37:07,391 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-29 00:37:07,466 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250101 - 20250317
2025-07-29 00:37:07,479 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-29 00:37:07,480 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-29 00:37:07,480 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250726 - 20250727
2025-07-29 00:37:07,480 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 00:37:07,480 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 00:37:07,480 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250726 - 20250727
2025-07-29 00:37:07,657 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250726, 交易日: 2天, 频率: 1min)
2025-07-29 00:37:07,657 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-29 00:37:07,657 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 00:37:07,658 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 00:37:07,708 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-29 00:37:07,942 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-29 00:37:07,942 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-29 00:37:07,943 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-29 00:37:07,946 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250726 - 20250727
2025-07-29 00:37:07,947 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-29 00:37:07,947 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-29 00:37:07,947 - Main - INFO - info:307 - ℹ️ ✅ 现有数据已覆盖目标范围，无需下载新数据
2025-07-29 00:37:07,948 - Main - INFO - info:307 - ℹ️ ✅ 添加时间戳标识: 1min_0_000617_20250318-20250725_来源互联网（202507290037）.txt
2025-07-29 00:37:07,949 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-29 00:37:07,949 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-29 00:37:07,949 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-29 00:37:07,949 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-29 00:37:07,950 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-29 00:37:07,951 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-29 00:37:07,951 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-29 00:37:07,951 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-29 00:37:07,951 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-29 00:37:07,951 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-29 00:37:07,952 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250729_003707.txt
2025-07-29 00:37:07,952 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-29 00:37:07,952 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-29 00:37:07,953 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-29 00:37:07,954 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-29 00:37:07,954 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-29 00:37:07,955 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-29 00:37:07,955 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-29 00:37:07,955 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-29 00:37:07,955 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-29 00:37:07,955 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-29 00:37:07,955 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
