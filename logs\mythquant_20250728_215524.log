2025-07-28 21:55:24,615 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_215524.log
2025-07-28 21:55:24,616 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-28 21:55:24,636 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-28 21:55:24,639 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-28 21:55:24,639 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-28 21:55:24,640 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-28 21:55:24,640 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-28 21:55:24,902 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.170s
2025-07-28 21:55:24,974 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.191.117.167 (60.191.117.167:7709) - 0.218s
2025-07-28 21:55:24,977 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.219s
2025-07-28 21:55:24,987 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.232s
2025-07-28 21:55:25,002 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.233s
2025-07-28 21:55:25,016 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************ (************:7709) - 0.245s
2025-07-28 21:55:25,022 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-28 21:55:25,022 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-28 21:55:25,023 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-28 21:55:25,023 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-28 21:55:25,023 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-28 21:55:25,023 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-28 21:55:25,023 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-28 21:55:25,526 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-28 21:55:25,526 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-28 21:55:25,533 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-28 21:55:25,533 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-28 21:55:25,534 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-28 21:55:25,534 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-28 21:55:25,534 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-28 21:55:25,534 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-28 21:55:25,535 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-28 21:55:25,539 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-28 21:55:25,539 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-28 21:55:26,012 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-28 21:55:26,012 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 21:55:26,013 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 21:55:26,013 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 21:55:26,013 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 21:55:26,013 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 21:55:26,013 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-28 21:55:26,013 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-28 21:55:26,014 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-28 21:55:26,014 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-28 21:55:26,014 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-28 21:55:26,156 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-28 21:55:26,156 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-28 21:55:26,164 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-28 21:55:26,164 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-28 21:55:26,164 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-28 21:55:26,164 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-28 21:55:26,164 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-28 21:55:26,164 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-28 21:55:26,165 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-28 21:55:26,168 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-28 21:55:26,168 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-28 21:56:05,044 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-28 21:56:11,678 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-28 21:56:11,847 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-28 21:56:12,413 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-28 21:56:12,586 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-28 21:56:12,586 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-28 21:56:12,655 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-28 21:56:12,655 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-28 21:56:12,655 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-28 21:56:12,655 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-28 21:56:12,656 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-28 21:56:12,656 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-28 21:56:12,656 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-28 21:56:12,672 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 46.66 秒
2025-07-28 21:56:12,672 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-28 21:56:12,672 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 21:56:12,672 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-28 21:56:12,673 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-28 21:56:12,675 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-28 21:56:13,058 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-28 21:56:13,072 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-28 21:56:13,072 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-28 21:56:13,072 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-28 21:56:13,072 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-28 21:56:13,072 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-28 21:56:13,073 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-28 21:56:13,073 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-28 21:56:13,073 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-28 21:56:13,076 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-28 21:56:13,076 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-28 21:56:13,077 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_202503030937-202507251500_来源互联网（202507282142）.txt
2025-07-28 21:56:13,077 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_202503030937-202507251500_来源互联网（202507282142）.txt
2025-07-28 21:56:13,077 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-28 21:56:13,104 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-07-28 21:56:13,108 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:13,289 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:13,340 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-07-28 21:56:13,344 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 21:56:13,344 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 21:56:13,344 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-28 21:56:13,345 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:13,518 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:13,519 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 2天, 频率: 1min)
2025-07-28 21:56:13,519 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 21:56:13,519 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 21:56:13,519 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 21:56:13,570 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 21:56:13,571 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:13,748 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:13,796 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 21:56:13,796 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 21:56:13,797 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 21:56:13,797 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 21:56:13,797 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250725
2025-07-28 21:56:13,800 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 21:56:13,802 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 21:56:13,802 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 21:56:13,804 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-28 21:56:13,805 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 21:56:13,805 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-28 21:56:13,805 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-28 21:56:13,805 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-28 21:56:13,805 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 21:56:13,805 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 21:56:13,805 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 21:56:13,810 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-28 21:56:13,810 - Main - WARNING - warning:311 - ⚠️ ⚠️ 无法解析文件名格式
2025-07-28 21:56:13,810 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-28 21:56:13,810 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 21:56:13,810 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 21:56:13,810 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-28 21:56:13,811 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:13,990 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:13,993 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32640条 (目标日期: 20250101, 交易日: 136天, 频率: 1min)
2025-07-28 21:56:13,993 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32640条 -> 实际请求800条 (配置限制:800)
2025-07-28 21:56:13,993 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 21:56:13,993 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 21:56:14,045 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需31840条
2025-07-28 21:56:14,046 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:14,225 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:14,277 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 21:56:14,277 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:14,441 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:14,490 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 21:56:14,490 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:14,666 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:14,719 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 21:56:14,719 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:14,900 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:14,953 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 21:56:14,953 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:15,127 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:15,183 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-28 21:56:15,183 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:15,364 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:15,419 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-28 21:56:15,419 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:15,595 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:15,648 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-28 21:56:15,648 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:15,831 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:15,884 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-28 21:56:15,884 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:16,066 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:16,120 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-28 21:56:16,120 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:16,288 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:16,339 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-28 21:56:16,339 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:16,510 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:16,561 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-28 21:56:16,561 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:16,737 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:16,787 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-28 21:56:16,787 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:16,961 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:17,013 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-28 21:56:17,014 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:17,189 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:17,241 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-28 21:56:17,241 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:17,415 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:17,465 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-28 21:56:17,465 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:17,635 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:17,687 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-28 21:56:17,687 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:17,872 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:17,923 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-28 21:56:17,923 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:18,095 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:18,146 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-28 21:56:18,146 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:18,316 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:18,366 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-28 21:56:18,367 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:18,541 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:18,591 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-28 21:56:18,592 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:18,761 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:18,812 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-28 21:56:18,813 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:18,990 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:19,042 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-28 21:56:19,042 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:19,213 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:19,263 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-28 21:56:19,263 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:19,445 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:19,499 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-28 21:56:19,500 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:19,680 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:19,735 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-28 21:56:19,735 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:19,907 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:19,960 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-28 21:56:19,960 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-28 21:56:20,146 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-28 21:56:20,191 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21600条数据
2025-07-28 21:56:20,197 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32640条，实际获取21600条
2025-07-28 21:56:20,197 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11040条数据（约46个交易日）
2025-07-28 21:56:20,197 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-28 21:56:20,197 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-28 21:56:20,197 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-28 21:56:20,253 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-07-28 21:56:20,260 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共21360条记录
2025-07-28 21:56:20,260 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 21:56:20,337 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.600, 前复权=6.600
2025-07-28 21:56:20,342 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 21:56:20,343 - Main - INFO - info:307 - ℹ️   日期: 202503180931
2025-07-28 21:56:20,343 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.6
2025-07-28 21:56:20,343 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.6
2025-07-28 21:56:20,343 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 21360 >= 5
2025-07-28 21:56:20,343 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 21:56:20,344 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 21:56:20,397 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250318-20250725_来源互联网（202507282156）.txt (999617 字节)
2025-07-28 21:56:20,398 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-28 21:56:20,398 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-28 21:56:20,399 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-28 21:56:20,399 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-28 21:56:20,399 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 21:56:20,400 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-28 21:56:20,401 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-28 21:56:20,401 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-28 21:56:20,401 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-28 21:56:20,401 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-28 21:56:20,401 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-28 21:56:20,401 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: True, 互联网: False
2025-07-28 21:56:20,403 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250728_215620.txt
2025-07-28 21:56:20,403 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-28 21:56:20,403 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-28 21:56:20,403 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 66.7%
2025-07-28 21:56:20,405 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 21:56:20,405 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 21:56:20,405 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-28 21:56:20,405 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-28 21:56:20,405 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 21:56:20,405 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 21:56:20,405 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
