#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
黑名单管理工具
用于手动管理服务器黑名单
"""

import sys
import os
import json
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.smart_server_manager import SmartServerManager

def show_blacklist():
    """显示当前黑名单"""
    print("🚫 当前服务器黑名单")
    print("=" * 80)
    
    manager = SmartServerManager()
    summary = manager.get_blacklist_summary()
    
    if summary['active_count'] == 0:
        print("✅ 当前无活跃黑名单服务器")
        return
    
    print(f"📊 活跃黑名单: {summary['active_count']} 个")
    print(f"📊 过期黑名单: {summary['expired_count']} 个")
    print()
    
    print("📋 活跃黑名单详情:")
    print("-" * 80)
    for ip, info in summary['active_list'].items():
        print(f"  🚫 {ip}")
        print(f"     原因: {info['reason']}")
        print(f"     时间: {info['datetime']} ({info['age_minutes']}分钟前)")
        print()

def add_to_blacklist():
    """手动添加IP到黑名单"""
    print("➕ 手动添加IP到黑名单")
    print("=" * 60)
    
    ip = input("请输入要加入黑名单的IP地址: ").strip()
    if not ip:
        print("❌ IP地址不能为空")
        return
    
    reason = input("请输入加入黑名单的原因 (可选): ").strip()
    if not reason:
        reason = "手动添加"
    
    manager = SmartServerManager()
    manager.add_to_blacklist(ip, reason)
    
    print(f"✅ 已将 {ip} 加入黑名单")
    print(f"📝 原因: {reason}")

def remove_from_blacklist():
    """手动从黑名单移除IP"""
    print("➖ 手动从黑名单移除IP")
    print("=" * 60)
    
    # 先显示当前黑名单
    manager = SmartServerManager()
    summary = manager.get_blacklist_summary()
    
    if summary['active_count'] == 0:
        print("✅ 当前无活跃黑名单服务器")
        return
    
    print("📋 当前黑名单:")
    ips = list(summary['active_list'].keys())
    for i, ip in enumerate(ips, 1):
        print(f"  {i}. {ip}")
    
    print()
    choice = input("请输入要移除的IP地址或序号: ").strip()
    
    # 判断是IP地址还是序号
    if choice.isdigit():
        index = int(choice) - 1
        if 0 <= index < len(ips):
            ip_to_remove = ips[index]
        else:
            print("❌ 序号无效")
            return
    else:
        ip_to_remove = choice
    
    if ip_to_remove in summary['active_list']:
        manager.remove_from_blacklist(ip_to_remove)
        print(f"✅ 已将 {ip_to_remove} 从黑名单移除")
    else:
        print(f"❌ {ip_to_remove} 不在黑名单中")

def clear_blacklist():
    """清空黑名单"""
    print("🗑️ 清空黑名单")
    print("=" * 60)
    
    confirm = input("⚠️ 确定要清空所有黑名单吗？(y/N): ").strip().lower()
    if confirm != 'y':
        print("❌ 操作已取消")
        return
    
    manager = SmartServerManager()
    
    # 获取所有黑名单IP
    summary = manager.get_blacklist_summary()
    if summary['active_count'] == 0:
        print("✅ 黑名单已经是空的")
        return
    
    # 清空黑名单
    for ip in summary['active_list'].keys():
        manager.remove_from_blacklist(ip)
    
    print(f"✅ 已清空 {summary['active_count']} 个黑名单条目")

def export_blacklist():
    """导出黑名单到文件"""
    print("📤 导出黑名单")
    print("=" * 60)
    
    manager = SmartServerManager()
    summary = manager.get_blacklist_summary()
    
    if summary['active_count'] == 0:
        print("✅ 当前无黑名单可导出")
        return
    
    # 生成导出文件名
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    export_file = f"blacklist_export_{timestamp}.txt"
    
    try:
        with open(export_file, 'w', encoding='utf-8') as f:
            f.write(f"# 服务器黑名单导出\n")
            f.write(f"# 导出时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"# 总计: {summary['active_count']} 个IP\n\n")
            
            for ip, info in summary['active_list'].items():
                f.write(f"{ip}\t{info['reason']}\t{info['datetime']}\n")
        
        print(f"✅ 黑名单已导出到: {export_file}")
        
    except Exception as e:
        print(f"❌ 导出失败: {e}")

def show_menu():
    """显示菜单"""
    print("\n🛠️ 黑名单管理工具")
    print("=" * 50)
    print("1. 📋 显示黑名单")
    print("2. ➕ 添加IP到黑名单")
    print("3. ➖ 从黑名单移除IP")
    print("4. 🗑️ 清空黑名单")
    print("5. 📤 导出黑名单")
    print("0. 🚪 退出")
    print("-" * 50)

def main():
    """主函数"""
    print("🚀 服务器黑名单管理工具")
    print("=" * 80)
    
    while True:
        show_menu()
        choice = input("请选择操作 (0-5): ").strip()
        
        if choice == '1':
            show_blacklist()
        elif choice == '2':
            add_to_blacklist()
        elif choice == '3':
            remove_from_blacklist()
        elif choice == '4':
            clear_blacklist()
        elif choice == '5':
            export_blacklist()
        elif choice == '0':
            print("👋 再见！")
            break
        else:
            print("❌ 无效选择，请重新输入")
        
        input("\n按回车键继续...")

if __name__ == '__main__':
    try:
        main()
    except KeyboardInterrupt:
        print("\n\n👋 用户中断，再见！")
    except Exception as e:
        print(f"\n❌ 程序错误: {e}")
        import traceback
        traceback.print_exc()
