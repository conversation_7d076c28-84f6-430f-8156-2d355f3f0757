#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试运行器

统一的测试执行入口
"""

import sys
import os
from pathlib import Path
import subprocess
import time

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def run_test_file(test_file_path, description=""):
    """运行单个测试文件"""
    print(f"\n🔬 运行测试: {test_file_path.name}")
    if description:
        print(f"   📝 描述: {description}")
    print("-" * 50)
    
    try:
        # 使用subprocess运行测试，避免被main.py拦截
        result = subprocess.run(
            [sys.executable, str(test_file_path)],
            cwd=str(project_root),
            capture_output=True,
            text=True,
            timeout=120
        )
        
        if result.returncode == 0:
            print("   ✅ 测试通过")
            if result.stdout:
                print("   📄 输出:")
                for line in result.stdout.strip().split('\n'):
                    print(f"      {line}")
            return True
        else:
            print("   ❌ 测试失败")
            if result.stderr:
                print("   🚨 错误:")
                for line in result.stderr.strip().split('\n'):
                    print(f"      {line}")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⏰ 测试超时")
        return False
    except Exception as e:
        print(f"   💥 测试异常: {e}")
        return False

def main():
    """主测试函数"""
    print("🧪 MythQuant 测试套件")
    print("=" * 50)
    
    # 定义测试文件
    test_files = [
        (project_root / "tests" / "final_validation_test.py", "最终验证测试"),
        (project_root / "tests" / "test_config_system.py", "配置系统测试"),
        (project_root / "tests" / "test_integration.py", "集成测试"),
    ]
    
    # 过滤存在的测试文件
    available_tests = []
    for test_file, description in test_files:
        if test_file.exists():
            available_tests.append((test_file, description))
        else:
            print(f"⚠️ 测试文件不存在: {test_file.name}")
    
    if not available_tests:
        print("❌ 没有找到可用的测试文件")
        return False
    
    print(f"\n📋 找到 {len(available_tests)} 个测试文件")
    
    # 运行测试
    passed = 0
    total = len(available_tests)
    start_time = time.time()
    
    for test_file, description in available_tests:
        result = run_test_file(test_file, description)
        if result:
            passed += 1
    
    end_time = time.time()
    duration = end_time - start_time
    
    # 显示结果
    print(f"\n📊 测试结果统计:")
    print("=" * 50)
    print(f"   总测试数: {total}")
    print(f"   通过测试: {passed}")
    print(f"   失败测试: {total - passed}")
    print(f"   成功率: {passed/total*100:.1f}%")
    print(f"   执行时间: {duration:.2f} 秒")
    
    if passed == total:
        print("\n🎉 所有测试通过！")
        return True
    else:
        print(f"\n⚠️ {total-passed} 个测试失败")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 测试套件执行完成 - 所有测试通过")
        else:
            print("\n❌ 测试套件执行完成 - 部分测试失败")
    except KeyboardInterrupt:
        print("\n⏹️ 测试被用户中断")
    except Exception as e:
        print(f"\n💥 测试套件异常: {e}")
    
    input("\n按回车键退出...")
