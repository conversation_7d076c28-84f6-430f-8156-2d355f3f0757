#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构清理归档方案

分析新旧架构关系，提供清理归档建议
"""

import os
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Tuple
import json


class ArchitectureCleanupPlan:
    """架构清理归档计划"""
    
    def __init__(self, project_root: str = "."):
        self.project_root = Path(project_root)
        self.timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 清理分类
        self.cleanup_categories = {
            "legacy_code": {
                "description": "遗留代码文件",
                "action": "archive",
                "items": []
            },
            "duplicate_modules": {
                "description": "重复的模块文件",
                "action": "archive", 
                "items": []
            },
            "old_configs": {
                "description": "旧配置文件",
                "action": "archive",
                "items": []
            },
            "temp_files": {
                "description": "临时文件和缓存",
                "action": "delete",
                "items": []
            },
            "old_docs": {
                "description": "过时的文档",
                "action": "archive",
                "items": []
            },
            "test_artifacts": {
                "description": "测试产物和临时测试文件",
                "action": "delete",
                "items": []
            }
        }
    
    def analyze_cleanup_items(self):
        """分析需要清理的项目"""
        
        # 1. 遗留代码文件
        legacy_items = [
            "legacy/",                              # 整个legacy目录
            "algorithms/",                          # 根目录下的旧算法模块
            "core/",                               # 根目录下的旧核心模块
            "cache/",                              # 根目录下的旧缓存模块
            "file_io/",                            # 根目录下的旧IO模块
            "ui/",                                 # 根目录下的旧UI模块
            "utils/",                              # 根目录下的旧工具模块
            "algorithm_compatibility.py",          # 兼容性文件
            "config_compatibility.py",
            "data_access_compatibility.py", 
            "io_compatibility.py",
        ]
        
        # 2. 重复模块（已迁移到src/mythquant）
        duplicate_items = [
            # 这些模块已经在src/mythquant中重新实现
        ]
        
        # 3. 旧配置文件
        old_config_items = [
            "config/",                             # 旧配置目录
            "custom_datacfg/",                     # 自定义数据配置
        ]
        
        # 4. 临时文件和缓存
        temp_items = [
            "__pycache__/",                        # Python缓存
            "*.pyc",                               # 编译文件
            "cache/temp/",                         # 临时缓存
            "cache/files/",                        # 缓存文件
            "logs/*.log",                          # 旧日志文件
            "signal/",                             # 信号文件
        ]
        
        # 5. 过时文档
        old_doc_items = [
            "docs/legacy/",                        # 遗留文档
            "docs_rename_backup/",                 # 文档重命名备份
            "archived_files/",                     # 已归档文件
            "archive/",                            # 旧归档
        ]
        
        # 6. 测试产物
        test_artifact_items = [
            "benchmark_results/",                  # 基准测试结果
            "validation_results/",                # 验证结果
            "reports/",                           # 旧报告
            "migration_backups/",                 # 迁移备份
            "manual_cleanup_backup/",             # 手动清理备份
            "final_cleanup_backup/",              # 最终清理备份
        ]
        
        # 填充清理项目
        self.cleanup_categories["legacy_code"]["items"] = legacy_items
        self.cleanup_categories["duplicate_modules"]["items"] = duplicate_items
        self.cleanup_categories["old_configs"]["items"] = old_config_items
        self.cleanup_categories["temp_files"]["items"] = temp_items
        self.cleanup_categories["old_docs"]["items"] = old_doc_items
        self.cleanup_categories["test_artifacts"]["items"] = test_artifact_items
    
    def check_item_exists(self, item_path: str) -> bool:
        """检查项目是否存在"""
        full_path = self.project_root / item_path
        return full_path.exists()
    
    def generate_cleanup_report(self) -> Dict:
        """生成清理报告"""
        self.analyze_cleanup_items()
        
        report = {
            "timestamp": self.timestamp,
            "project_root": str(self.project_root),
            "architecture_status": "DDD架构已完成",
            "cleanup_summary": {},
            "categories": {}
        }
        
        total_items = 0
        total_size = 0
        
        for category, info in self.cleanup_categories.items():
            existing_items = []
            category_size = 0
            
            for item in info["items"]:
                if self.check_item_exists(item):
                    item_path = self.project_root / item
                    existing_items.append({
                        "path": item,
                        "exists": True,
                        "size": self._get_size(item_path),
                        "type": "directory" if item_path.is_dir() else "file"
                    })
                    category_size += self._get_size(item_path)
                else:
                    existing_items.append({
                        "path": item,
                        "exists": False,
                        "size": 0,
                        "type": "unknown"
                    })
            
            report["categories"][category] = {
                "description": info["description"],
                "action": info["action"],
                "total_items": len(info["items"]),
                "existing_items": len([i for i in existing_items if i["exists"]]),
                "total_size": category_size,
                "items": existing_items
            }
            
            total_items += len([i for i in existing_items if i["exists"]])
            total_size += category_size
        
        report["cleanup_summary"] = {
            "total_categories": len(self.cleanup_categories),
            "total_items": total_items,
            "total_size": total_size,
            "estimated_cleanup_time": "30-60分钟"
        }
        
        return report
    
    def _get_size(self, path: Path) -> int:
        """获取文件或目录大小"""
        try:
            if path.is_file():
                return path.stat().st_size
            elif path.is_dir():
                total_size = 0
                for item in path.rglob("*"):
                    if item.is_file():
                        total_size += item.stat().st_size
                return total_size
            return 0
        except:
            return 0
    
    def create_archive_structure(self):
        """创建归档目录结构"""
        archive_root = self.project_root / f"architecture_migration_archive_{self.timestamp}"
        
        # 创建归档目录结构
        archive_dirs = [
            "legacy_code",
            "duplicate_modules", 
            "old_configs",
            "old_docs",
            "metadata"
        ]
        
        for dir_name in archive_dirs:
            (archive_root / dir_name).mkdir(parents=True, exist_ok=True)
        
        return archive_root
    
    def execute_cleanup(self, dry_run: bool = True):
        """执行清理操作"""
        report = self.generate_cleanup_report()
        
        if dry_run:
            print("🔍 DRY RUN - 不会实际执行清理操作")
            print("=" * 60)
        else:
            print("🗂️ 执行架构清理归档")
            print("=" * 60)
            archive_root = self.create_archive_structure()
        
        for category, info in report["categories"].items():
            print(f"\n📂 {info['description']} ({category})")
            print(f"   动作: {info['action']}")
            print(f"   项目数: {info['existing_items']}/{info['total_items']}")
            print(f"   大小: {self._format_size(info['total_size'])}")
            
            for item in info["items"]:
                if item["exists"]:
                    status = "✅" if item["exists"] else "❌"
                    size_str = self._format_size(item["size"])
                    print(f"   {status} {item['path']} ({size_str})")
                    
                    if not dry_run:
                        self._execute_item_action(
                            item["path"], 
                            info["action"], 
                            archive_root / category
                        )
        
        # 保存清理报告
        report_path = self.project_root / f"cleanup_report_{self.timestamp}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"\n📊 清理报告已保存: {report_path}")
        
        if not dry_run:
            print(f"📁 归档目录: {archive_root}")
    
    def _format_size(self, size_bytes: int) -> str:
        """格式化文件大小"""
        for unit in ['B', 'KB', 'MB', 'GB']:
            if size_bytes < 1024:
                return f"{size_bytes:.1f}{unit}"
            size_bytes /= 1024
        return f"{size_bytes:.1f}TB"
    
    def _execute_item_action(self, item_path: str, action: str, target_dir: Path):
        """执行单个项目的清理动作"""
        source_path = self.project_root / item_path
        
        if not source_path.exists():
            return
        
        try:
            if action == "archive":
                # 归档到指定目录
                target_path = target_dir / source_path.name
                if source_path.is_dir():
                    shutil.copytree(source_path, target_path, dirs_exist_ok=True)
                    shutil.rmtree(source_path)
                else:
                    shutil.copy2(source_path, target_path)
                    source_path.unlink()
                print(f"   📦 已归档: {item_path}")
                
            elif action == "delete":
                # 直接删除
                if source_path.is_dir():
                    shutil.rmtree(source_path)
                else:
                    source_path.unlink()
                print(f"   🗑️ 已删除: {item_path}")
                
        except Exception as e:
            print(f"   ❌ 处理失败: {item_path}, 错误: {e}")


def main():
    """主函数"""
    cleanup_plan = ArchitectureCleanupPlan()
    
    print("🏗️ MythQuant 架构清理归档方案")
    print("=" * 60)
    print("📋 当前状态: DDD架构已完成，Clean Architecture已实施")
    print("🎯 目标: 清理归档遗留代码，保持项目结构清洁")
    print()
    
    # 生成清理报告
    print("🔍 分析项目结构...")
    report = cleanup_plan.generate_cleanup_report()
    
    print(f"📊 清理摘要:")
    print(f"   总分类: {report['cleanup_summary']['total_categories']}")
    print(f"   总项目: {report['cleanup_summary']['total_items']}")
    print(f"   总大小: {cleanup_plan._format_size(report['cleanup_summary']['total_size'])}")
    print(f"   预计时间: {report['cleanup_summary']['estimated_cleanup_time']}")
    print()
    
    # 询问用户操作
    print("🤔 请选择操作:")
    print("1. 查看详细清理计划 (dry-run)")
    print("2. 执行清理归档")
    print("3. 仅生成报告")
    print("4. 退出")
    
    choice = input("\n请输入选择 (1-4): ").strip()
    
    if choice == "1":
        cleanup_plan.execute_cleanup(dry_run=True)
    elif choice == "2":
        confirm = input("⚠️ 确认执行清理归档? (y/N): ").strip().lower()
        if confirm == 'y':
            cleanup_plan.execute_cleanup(dry_run=False)
        else:
            print("❌ 操作已取消")
    elif choice == "3":
        report_path = f"cleanup_report_{cleanup_plan.timestamp}.json"
        with open(report_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"📊 报告已生成: {report_path}")
    else:
        print("👋 退出")


if __name__ == "__main__":
    main()
