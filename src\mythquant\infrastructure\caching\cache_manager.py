#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存管理器实现

提供统一的缓存管理接口和多级缓存管理
"""

import logging
import threading
from typing import Any, Optional, Dict, List, Callable, Union
from datetime import datetime, timedelta
import time
import json
import hashlib

from .cache_providers import CacheProvider, CompositeCacheProvider, create_multi_level_cache
from ...domain.exceptions import DomainException

logger = logging.getLogger(__name__)


class CacheException(DomainException):
    """缓存异常"""
    pass


class CacheKey:
    """缓存键生成器"""
    
    @staticmethod
    def generate(prefix: str, *args, **kwargs) -> str:
        """生成缓存键"""
        key_parts = [prefix]
        
        # 添加位置参数
        for arg in args:
            if isinstance(arg, (str, int, float)):
                key_parts.append(str(arg))
            else:
                # 对复杂对象进行哈希
                key_parts.append(CacheKey._hash_object(arg))
        
        # 添加关键字参数
        if kwargs:
            sorted_kwargs = sorted(kwargs.items())
            for key, value in sorted_kwargs:
                if isinstance(value, (str, int, float)):
                    key_parts.append(f"{key}={value}")
                else:
                    key_parts.append(f"{key}={CacheKey._hash_object(value)}")
        
        return ":".join(key_parts)
    
    @staticmethod
    def _hash_object(obj: Any) -> str:
        """对对象进行哈希"""
        try:
            if hasattr(obj, '__dict__'):
                # 对象属性哈希
                obj_str = json.dumps(obj.__dict__, sort_keys=True, default=str)
            else:
                obj_str = str(obj)
            
            return hashlib.md5(obj_str.encode()).hexdigest()[:8]
        except:
            return hashlib.md5(str(obj).encode()).hexdigest()[:8]


class CacheManager:
    """缓存管理器"""
    
    def __init__(self, provider: CacheProvider, default_ttl: int = 3600):
        self._provider = provider
        self._default_ttl = default_ttl
        self._lock = threading.RLock()
        self._stats = {
            'operations': 0,
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        with self._lock:
            self._stats['operations'] += 1
            
            try:
                value = self._provider.get(key)
                if value is not None:
                    self._stats['hits'] += 1
                    logger.debug(f"缓存命中: {key}")
                    return value
                else:
                    self._stats['misses'] += 1
                    logger.debug(f"缓存未命中: {key}")
                    return default
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"缓存获取失败: {key}, 错误: {e}")
                return default
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            self._stats['operations'] += 1
            
            try:
                ttl = ttl or self._default_ttl
                success = self._provider.set(key, value, ttl)
                if success:
                    self._stats['sets'] += 1
                    logger.debug(f"缓存设置成功: {key}, TTL: {ttl}")
                else:
                    self._stats['errors'] += 1
                    logger.warning(f"缓存设置失败: {key}")
                return success
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"缓存设置异常: {key}, 错误: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self._lock:
            self._stats['operations'] += 1
            
            try:
                success = self._provider.delete(key)
                if success:
                    self._stats['deletes'] += 1
                    logger.debug(f"缓存删除成功: {key}")
                return success
            except Exception as e:
                self._stats['errors'] += 1
                logger.error(f"缓存删除失败: {key}, 错误: {e}")
                return False
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        try:
            return self._provider.exists(key)
        except Exception as e:
            logger.error(f"缓存存在性检查失败: {key}, 错误: {e}")
            return False
    
    def clear(self) -> bool:
        """清空所有缓存"""
        try:
            success = self._provider.clear()
            if success:
                logger.info("缓存清空成功")
            return success
        except Exception as e:
            logger.error(f"缓存清空失败: {e}")
            return False
    
    def get_or_set(self, key: str, factory: Callable[[], Any], 
                   ttl: Optional[int] = None) -> Any:
        """获取缓存值，如果不存在则通过工厂函数创建"""
        value = self.get(key)
        if value is not None:
            return value
        
        try:
            # 使用工厂函数创建值
            new_value = factory()
            self.set(key, new_value, ttl)
            return new_value
        except Exception as e:
            logger.error(f"缓存工厂函数执行失败: {key}, 错误: {e}")
            raise CacheException(f"缓存工厂函数执行失败: {e}") from e
    
    def mget(self, keys: List[str]) -> Dict[str, Any]:
        """批量获取缓存值"""
        result = {}
        for key in keys:
            value = self.get(key)
            if value is not None:
                result[key] = value
        return result
    
    def mset(self, mapping: Dict[str, Any], ttl: Optional[int] = None) -> bool:
        """批量设置缓存值"""
        success = True
        for key, value in mapping.items():
            if not self.set(key, value, ttl):
                success = False
        return success
    
    def mdelete(self, keys: List[str]) -> int:
        """批量删除缓存"""
        deleted_count = 0
        for key in keys:
            if self.delete(key):
                deleted_count += 1
        return deleted_count
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            provider_stats = self._provider.get_stats()
            
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                'manager_stats': {
                    **self._stats,
                    'hit_rate': hit_rate
                },
                'provider_stats': provider_stats,
                'timestamp': datetime.now().isoformat()
            }
    
    def invalidate_pattern(self, pattern: str) -> int:
        """根据模式失效缓存（需要提供者支持）"""
        # 这是一个简化实现，实际需要根据提供者类型实现
        logger.warning(f"模式失效功能需要提供者支持: {pattern}")
        return 0


class MultiLevelCacheManager(CacheManager):
    """多级缓存管理器"""
    
    def __init__(self, providers: List[CacheProvider], default_ttl: int = 3600):
        composite_provider = CompositeCacheProvider(providers)
        super().__init__(composite_provider, default_ttl)
        self._level_count = len(providers)
    
    def get_level_stats(self) -> List[Dict[str, Any]]:
        """获取各级缓存统计"""
        if isinstance(self._provider, CompositeCacheProvider):
            return self._provider.get_stats().get('provider_stats', [])
        return []
    
    def warm_up(self, data: Dict[str, Any], ttl: Optional[int] = None):
        """预热缓存"""
        logger.info(f"开始缓存预热: {len(data)} 个条目")
        
        success_count = 0
        for key, value in data.items():
            if self.set(key, value, ttl):
                success_count += 1
        
        logger.info(f"缓存预热完成: {success_count}/{len(data)} 成功")
    
    def get_cache_hierarchy(self) -> Dict[str, Any]:
        """获取缓存层次结构信息"""
        level_stats = self.get_level_stats()
        
        hierarchy = {
            'total_levels': self._level_count,
            'levels': []
        }
        
        for i, stats in enumerate(level_stats):
            level_info = {
                'level': i + 1,
                'type': stats.get('provider_type', 'unknown'),
                'hit_rate': stats.get('hit_rate', 0),
                'size': stats.get('size', 0),
                'level_hits': stats.get('level_hits', 0)
            }
            hierarchy['levels'].append(level_info)
        
        return hierarchy


class CacheNamespace:
    """缓存命名空间"""
    
    def __init__(self, manager: CacheManager, namespace: str):
        self._manager = manager
        self._namespace = namespace
    
    def _get_namespaced_key(self, key: str) -> str:
        """获取带命名空间的键"""
        return f"{self._namespace}:{key}"
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取缓存值"""
        return self._manager.get(self._get_namespaced_key(key), default)
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        return self._manager.set(self._get_namespaced_key(key), value, ttl)
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        return self._manager.delete(self._get_namespaced_key(key))
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        return self._manager.exists(self._get_namespaced_key(key))
    
    def clear_namespace(self) -> bool:
        """清空命名空间下的所有缓存"""
        # 这需要提供者支持模式删除
        logger.warning(f"命名空间清空功能需要提供者支持: {self._namespace}")
        return False
    
    def get_or_set(self, key: str, factory: Callable[[], Any], 
                   ttl: Optional[int] = None) -> Any:
        """获取缓存值，如果不存在则通过工厂函数创建"""
        return self._manager.get_or_set(self._get_namespaced_key(key), factory, ttl)


class CacheRegistry:
    """缓存注册表"""
    
    def __init__(self):
        self._managers: Dict[str, CacheManager] = {}
        self._namespaces: Dict[str, CacheNamespace] = {}
        self._lock = threading.RLock()
    
    def register_manager(self, name: str, manager: CacheManager):
        """注册缓存管理器"""
        with self._lock:
            self._managers[name] = manager
            logger.info(f"注册缓存管理器: {name}")
    
    def get_manager(self, name: str) -> Optional[CacheManager]:
        """获取缓存管理器"""
        return self._managers.get(name)
    
    def create_namespace(self, manager_name: str, namespace: str) -> CacheNamespace:
        """创建缓存命名空间"""
        with self._lock:
            manager = self.get_manager(manager_name)
            if not manager:
                raise CacheException(f"缓存管理器不存在: {manager_name}")
            
            full_name = f"{manager_name}:{namespace}"
            if full_name not in self._namespaces:
                self._namespaces[full_name] = CacheNamespace(manager, namespace)
                logger.info(f"创建缓存命名空间: {full_name}")
            
            return self._namespaces[full_name]
    
    def get_namespace(self, manager_name: str, namespace: str) -> Optional[CacheNamespace]:
        """获取缓存命名空间"""
        full_name = f"{manager_name}:{namespace}"
        return self._namespaces.get(full_name)
    
    def get_all_stats(self) -> Dict[str, Any]:
        """获取所有缓存统计"""
        stats = {}
        for name, manager in self._managers.items():
            stats[name] = manager.get_stats()
        return stats
    
    def clear_all(self) -> Dict[str, bool]:
        """清空所有缓存"""
        results = {}
        for name, manager in self._managers.items():
            results[name] = manager.clear()
        return results


# 全局缓存注册表
_global_cache_registry = CacheRegistry()


def get_cache_registry() -> CacheRegistry:
    """获取全局缓存注册表"""
    return _global_cache_registry


def setup_default_caches():
    """设置默认缓存"""
    registry = get_cache_registry()
    
    # 创建多级缓存管理器
    multi_level_cache = create_multi_level_cache()
    multi_level_manager = MultiLevelCacheManager(multi_level_cache._providers)
    
    # 注册默认缓存管理器
    registry.register_manager('default', multi_level_manager)
    registry.register_manager('multi_level', multi_level_manager)
    
    logger.info("默认缓存设置完成")


def get_default_cache() -> CacheManager:
    """获取默认缓存管理器"""
    registry = get_cache_registry()
    manager = registry.get_manager('default')
    
    if not manager:
        setup_default_caches()
        manager = registry.get_manager('default')
    
    return manager


# 便捷函数
def cache_get(key: str, default: Any = None, manager_name: str = 'default') -> Any:
    """便捷的缓存获取函数"""
    manager = get_cache_registry().get_manager(manager_name)
    if manager:
        return manager.get(key, default)
    return default


def cache_set(key: str, value: Any, ttl: Optional[int] = None, 
              manager_name: str = 'default') -> bool:
    """便捷的缓存设置函数"""
    manager = get_cache_registry().get_manager(manager_name)
    if manager:
        return manager.set(key, value, ttl)
    return False


def cache_delete(key: str, manager_name: str = 'default') -> bool:
    """便捷的缓存删除函数"""
    manager = get_cache_registry().get_manager(manager_name)
    if manager:
        return manager.delete(key)
    return False
