#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境验证器
用于验证测试环境配置的完整性和一致性
"""

import os
import logging
from typing import Dict, List, Any, Tuple
from dataclasses import dataclass
from enum import Enum

logger = logging.getLogger(__name__)


class ValidationResult(Enum):
    """验证结果枚举"""
    PASS = "PASS"
    WARN = "WARN"
    FAIL = "FAIL"


@dataclass
class ValidationReport:
    """验证报告数据类"""
    check_name: str
    result: ValidationResult
    message: str
    details: Dict[str, Any] = None
    suggestions: List[str] = None


class TestEnvironmentValidator:
    """测试环境验证器"""
    
    def __init__(self):
        self.logger = logger
        self.validation_reports: List[ValidationReport] = []
    
    def validate_test_environment(self, task_name: str = 'minute_data_download') -> Dict[str, Any]:
        """
        验证测试环境配置
        
        Args:
            task_name: 任务名称
            
        Returns:
            验证结果汇总
        """
        try:
            from user_config import get_environment_config, detect_environment
            
            current_env = detect_environment()
            if current_env != 'test':
                return {
                    'overall_result': ValidationResult.FAIL,
                    'message': f'当前环境不是测试环境: {current_env}',
                    'reports': []
                }
            
            # 获取测试环境配置
            test_config = get_environment_config(task_name)
            validation_rules = test_config.get('validation_rules', {})
            
            # 执行各项验证
            self._validate_file_existence(test_config, validation_rules)
            self._validate_path_consistency(test_config, validation_rules)
            self._validate_configuration_integrity(test_config, validation_rules)
            self._validate_environment_isolation(test_config, validation_rules)
            
            # 生成验证汇总
            return self._generate_validation_summary()
            
        except Exception as e:
            self.logger.error(f"测试环境验证失败: {e}")
            return {
                'overall_result': ValidationResult.FAIL,
                'message': f'验证过程异常: {str(e)}',
                'reports': []
            }
    
    def _validate_file_existence(self, test_config: Dict, validation_rules: Dict):
        """验证文件存在性"""
        file_check = validation_rules.get('file_existence_check', {})
        if not file_check.get('enabled', True):
            return
        
        test_files = test_config.get('test_files', {})
        input_data_path = test_config.get('input_data_path', '')
        required_files = file_check.get('required_files', [])
        
        missing_files = []
        existing_files = []
        
        for stock_code, file_config in test_files.items():
            for file_type in required_files:
                file_name = file_config.get(file_type)
                if file_name:
                    file_path = os.path.join(input_data_path, file_name)
                    if os.path.exists(file_path):
                        existing_files.append(f"{stock_code}:{file_type}")
                    else:
                        missing_files.append(f"{stock_code}:{file_type} ({file_path})")
        
        if missing_files:
            result = ValidationResult.WARN if file_check.get('failure_action') == 'warn_and_continue' else ValidationResult.FAIL
            self.validation_reports.append(ValidationReport(
                check_name="文件存在性检查",
                result=result,
                message=f"发现 {len(missing_files)} 个缺失的测试文件",
                details={
                    'missing_files': missing_files,
                    'existing_files': existing_files
                },
                suggestions=[
                    "检查测试文件是否已正确创建",
                    "验证文件路径配置是否正确",
                    "运行测试环境初始化脚本"
                ]
            ))
        else:
            self.validation_reports.append(ValidationReport(
                check_name="文件存在性检查",
                result=ValidationResult.PASS,
                message=f"所有 {len(existing_files)} 个测试文件都存在"
            ))
    
    def _validate_path_consistency(self, test_config: Dict, validation_rules: Dict):
        """验证路径一致性"""
        path_check = validation_rules.get('path_consistency_check', {})
        if not path_check.get('enabled', True):
            return
        
        check_points = path_check.get('check_points', [])
        path_issues = []
        path_ok = []
        
        # 检查输入数据路径
        if 'input_data_path_exists' in check_points:
            input_path = test_config.get('input_data_path', '')
            if os.path.exists(input_path):
                path_ok.append(f"输入数据路径存在: {input_path}")
            else:
                path_issues.append(f"输入数据路径不存在: {input_path}")
        
        # 检查输出数据路径
        if 'output_data_path_writable' in check_points:
            output_path = test_config.get('output_data_path', '')
            if output_path:
                try:
                    os.makedirs(output_path, exist_ok=True)
                    test_file = os.path.join(output_path, '.write_test')
                    with open(test_file, 'w') as f:
                        f.write('test')
                    os.remove(test_file)
                    path_ok.append(f"输出数据路径可写: {output_path}")
                except Exception as e:
                    path_issues.append(f"输出数据路径不可写: {output_path} ({e})")
        
        # 检查备份数据路径
        if 'backup_data_path_accessible' in check_points:
            backup_path = test_config.get('backup_data_path', '')
            if backup_path:
                try:
                    os.makedirs(backup_path, exist_ok=True)
                    path_ok.append(f"备份数据路径可访问: {backup_path}")
                except Exception as e:
                    path_issues.append(f"备份数据路径不可访问: {backup_path} ({e})")
        
        if path_issues:
            result = ValidationResult.FAIL if path_check.get('failure_action') == 'error_and_stop' else ValidationResult.WARN
            self.validation_reports.append(ValidationReport(
                check_name="路径一致性检查",
                result=result,
                message=f"发现 {len(path_issues)} 个路径问题",
                details={
                    'path_issues': path_issues,
                    'path_ok': path_ok
                },
                suggestions=[
                    "检查目录权限设置",
                    "确保测试环境目录结构正确",
                    "运行测试环境初始化脚本"
                ]
            ))
        else:
            self.validation_reports.append(ValidationReport(
                check_name="路径一致性检查",
                result=ValidationResult.PASS,
                message=f"所有 {len(path_ok)} 个路径检查通过"
            ))
    
    def _validate_configuration_integrity(self, test_config: Dict, validation_rules: Dict):
        """验证配置完整性"""
        config_check = validation_rules.get('configuration_integrity_check', {})
        if not config_check.get('enabled', True):
            return
        
        required_configs = config_check.get('required_configs', [])
        missing_configs = []
        existing_configs = []
        
        for config_name in required_configs:
            if config_name in test_config and test_config[config_name]:
                existing_configs.append(config_name)
            else:
                missing_configs.append(config_name)
        
        if missing_configs:
            result = ValidationResult.WARN if config_check.get('failure_action') == 'warn_and_use_defaults' else ValidationResult.FAIL
            self.validation_reports.append(ValidationReport(
                check_name="配置完整性检查",
                result=result,
                message=f"发现 {len(missing_configs)} 个缺失的配置项",
                details={
                    'missing_configs': missing_configs,
                    'existing_configs': existing_configs
                },
                suggestions=[
                    "检查user_config.py中的测试环境配置",
                    "补充缺失的配置项",
                    "使用默认配置作为模板"
                ]
            ))
        else:
            self.validation_reports.append(ValidationReport(
                check_name="配置完整性检查",
                result=ValidationResult.PASS,
                message=f"所有 {len(existing_configs)} 个配置项都存在"
            ))
    
    def _validate_environment_isolation(self, test_config: Dict, validation_rules: Dict):
        """验证环境隔离"""
        isolation_check = validation_rules.get('environment_isolation_check', {})
        if not isolation_check.get('enabled', True):
            return
        
        isolation_points = isolation_check.get('isolation_points', [])
        isolation_issues = []
        isolation_ok = []
        
        # 检查文件路径分离
        if 'file_path_separation' in isolation_points:
            input_path = test_config.get('input_data_path', '')
            if 'test_environments' in input_path:
                isolation_ok.append("文件路径正确分离到测试环境")
            else:
                isolation_issues.append(f"文件路径未分离到测试环境: {input_path}")
        
        # 检查数据保护
        if 'data_protection' in isolation_points:
            test_management = test_config.get('test_management', {})
            if test_management.get('auto_backup') and test_management.get('auto_restore'):
                isolation_ok.append("数据保护机制已启用")
            else:
                isolation_issues.append("数据保护机制未完全启用")
        
        if isolation_issues:
            result = ValidationResult.FAIL if isolation_check.get('failure_action') == 'error_and_stop' else ValidationResult.WARN
            self.validation_reports.append(ValidationReport(
                check_name="环境隔离检查",
                result=result,
                message=f"发现 {len(isolation_issues)} 个隔离问题",
                details={
                    'isolation_issues': isolation_issues,
                    'isolation_ok': isolation_ok
                },
                suggestions=[
                    "检查测试环境路径配置",
                    "启用数据保护机制",
                    "验证环境隔离设置"
                ]
            ))
        else:
            self.validation_reports.append(ValidationReport(
                check_name="环境隔离检查",
                result=ValidationResult.PASS,
                message=f"所有 {len(isolation_ok)} 个隔离检查通过"
            ))
    
    def _generate_validation_summary(self) -> Dict[str, Any]:
        """生成验证汇总"""
        pass_count = sum(1 for r in self.validation_reports if r.result == ValidationResult.PASS)
        warn_count = sum(1 for r in self.validation_reports if r.result == ValidationResult.WARN)
        fail_count = sum(1 for r in self.validation_reports if r.result == ValidationResult.FAIL)
        
        overall_result = ValidationResult.PASS
        if fail_count > 0:
            overall_result = ValidationResult.FAIL
        elif warn_count > 0:
            overall_result = ValidationResult.WARN
        
        return {
            'overall_result': overall_result,
            'summary': {
                'total_checks': len(self.validation_reports),
                'pass_count': pass_count,
                'warn_count': warn_count,
                'fail_count': fail_count
            },
            'reports': self.validation_reports,
            'recommendations': self._generate_recommendations()
        }
    
    def _generate_recommendations(self) -> List[str]:
        """生成改进建议"""
        recommendations = []
        
        fail_reports = [r for r in self.validation_reports if r.result == ValidationResult.FAIL]
        warn_reports = [r for r in self.validation_reports if r.result == ValidationResult.WARN]
        
        if fail_reports:
            recommendations.append("立即修复失败的验证项，确保测试环境正常工作")
            for report in fail_reports:
                if report.suggestions:
                    recommendations.extend(report.suggestions)
        
        if warn_reports:
            recommendations.append("关注警告项，考虑进行优化改进")
            for report in warn_reports:
                if report.suggestions:
                    recommendations.extend(report.suggestions)
        
        if not fail_reports and not warn_reports:
            recommendations.append("测试环境配置优秀，建议定期进行验证检查")
        
        return list(set(recommendations))  # 去重


if __name__ == "__main__":
    # 测试验证器
    print("🧪 测试环境验证器")
    print("=" * 50)
    
    validator = TestEnvironmentValidator()
    result = validator.validate_test_environment()
    
    print(f"验证结果: {result['overall_result'].value}")
    print(f"总检查项: {result['summary']['total_checks']}")
    print(f"通过: {result['summary']['pass_count']}")
    print(f"警告: {result['summary']['warn_count']}")
    print(f"失败: {result['summary']['fail_count']}")
    
    if result['recommendations']:
        print("\n建议:")
        for rec in result['recommendations']:
            print(f"  • {rec}")
