#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增强的错误处理和日志系统
提供统一的异常处理、日志记录和监控能力
"""

import logging
import traceback
import functools
import time
from typing import Any, Callable, Optional, Dict, List
from enum import Enum
import threading
from collections import defaultdict, deque
import os
import json
from datetime import datetime

from utils.process_flow_optimizer import ProcessFlowOptimizer


class ErrorLevel(Enum):
    """错误级别枚举"""
    DEBUG = "DEBUG"
    INFO = "INFO"
    WARNING = "WARNING"
    ERROR = "ERROR"
    CRITICAL = "CRITICAL"


class ErrorCategory(Enum):
    """错误类别枚举"""
    DATA_ACCESS = "DATA_ACCESS"      # 数据访问错误
    CALCULATION = "CALCULATION"      # 计算错误
    FILE_IO = "FILE_IO"             # 文件IO错误
    NETWORK = "NETWORK"             # 网络错误
    VALIDATION = "VALIDATION"       # 数据验证错误
    SYSTEM = "SYSTEM"               # 系统错误
    BUSINESS = "BUSINESS"           # 业务逻辑错误


class EnhancedErrorHandler:
    """增强的错误处理器"""
    
    def __init__(self, 
                 logger_name: str = __name__,
                 enable_performance_logging: bool = True,
                 enable_error_statistics: bool = True,
                 max_error_history: int = 1000):
        """
        初始化错误处理器
        
        Args:
            logger_name: 日志器名称
            enable_performance_logging: 是否启用性能日志
            enable_error_statistics: 是否启用错误统计
            max_error_history: 最大错误历史记录数
        """
        self.logger = logging.getLogger(logger_name)
        self.enable_performance_logging = enable_performance_logging
        self.enable_error_statistics = enable_error_statistics
        self.max_error_history = max_error_history
        
        # 错误统计
        self._error_counts = defaultdict(int)
        self._error_history = deque(maxlen=max_error_history)
        self._performance_stats = defaultdict(list)
        self._lock = threading.Lock()

        # 流程优化器
        self.flow_optimizer = ProcessFlowOptimizer()

        # 配置日志格式
        self._setup_logging()
    
    def _setup_logging(self):
        """设置日志配置"""
        # 如果logger还没有handler，添加一个
        if not self.logger.handlers:
            handler = logging.StreamHandler()
            formatter = logging.Formatter(
                '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            )
            handler.setFormatter(formatter)
            self.logger.addHandler(handler)
            self.logger.setLevel(logging.INFO)
    
    def log_error(self, 
                  error: Exception,
                  category: ErrorCategory,
                  context: Optional[Dict[str, Any]] = None,
                  stock_code: Optional[str] = None,
                  operation: Optional[str] = None) -> str:
        """
        记录错误信息
        
        Args:
            error: 异常对象
            category: 错误类别
            context: 上下文信息
            stock_code: 股票代码
            operation: 操作名称
            
        Returns:
            错误ID
        """
        error_id = f"{category.value}_{int(time.time() * 1000)}"
        
        # 构建错误信息
        error_info = {
            'error_id': error_id,
            'timestamp': datetime.now().isoformat(),
            'category': category.value,
            'error_type': type(error).__name__,
            'error_message': str(error),
            'stock_code': stock_code,
            'operation': operation,
            'context': context or {},
            'traceback': traceback.format_exc()
        }
        
        # 记录到历史
        if self.enable_error_statistics:
            with self._lock:
                self._error_counts[category.value] += 1
                self._error_history.append(error_info)
        
        # 智能错误输出：根据上下文优化显示
        log_message = self._format_error_message(error_info)
        optimized_message = self.flow_optimizer.optimize_error_display(
            error_info['error_message'],
            error_info['traceback'] if not self.flow_optimizer.should_suppress("技术堆栈信息") else None
        )

        if category == ErrorCategory.SYSTEM:
            self.logger.error(optimized_message)
        elif category in [ErrorCategory.DATA_ACCESS, ErrorCategory.FILE_IO, ErrorCategory.NETWORK]:
            self.logger.warning(optimized_message)
        else:
            self.logger.error(optimized_message)
        
        return error_id
    
    def _format_error_message(self, error_info: Dict[str, Any]) -> str:
        """格式化错误消息"""
        parts = [
            f"🚨 [{error_info['category']}] {error_info['error_type']}: {error_info['error_message']}"
        ]
        
        if error_info.get('stock_code'):
            parts.append(f"股票: {error_info['stock_code']}")
        
        if error_info.get('operation'):
            parts.append(f"操作: {error_info['operation']}")
        
        if error_info.get('context'):
            context_str = ', '.join([f"{k}={v}" for k, v in error_info['context'].items()])
            parts.append(f"上下文: {context_str}")
        
        return ' | '.join(parts)
    
    def with_error_handling(self, 
                           category: ErrorCategory,
                           operation: str,
                           fallback_value: Any = None,
                           stock_code: Optional[str] = None,
                           raise_on_error: bool = False):
        """
        错误处理装饰器
        
        Args:
            category: 错误类别
            operation: 操作名称
            fallback_value: 失败时的返回值
            stock_code: 股票代码
            raise_on_error: 是否重新抛出异常
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                try:
                    return func(*args, **kwargs)
                except Exception as e:
                    # 记录错误
                    context = {
                        'function': func.__name__,
                        'args_count': len(args),
                        'kwargs_keys': list(kwargs.keys())
                    }
                    
                    error_id = self.log_error(
                        error=e,
                        category=category,
                        context=context,
                        stock_code=stock_code,
                        operation=operation
                    )
                    
                    if raise_on_error:
                        raise
                    
                    return fallback_value
            
            return wrapper
        return decorator
    
    def with_performance_logging(self, operation: str, threshold_seconds: float = 1.0):
        """
        性能监控装饰器
        
        Args:
            operation: 操作名称
            threshold_seconds: 性能警告阈值（秒）
        """
        def decorator(func: Callable) -> Callable:
            @functools.wraps(func)
            def wrapper(*args, **kwargs):
                if not self.enable_performance_logging:
                    return func(*args, **kwargs)
                
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    execution_time = time.time() - start_time
                    
                    # 记录性能统计
                    with self._lock:
                        self._performance_stats[operation].append(execution_time)
                    
                    # 性能警告
                    if execution_time > threshold_seconds:
                        self.logger.warning(
                            f"⚠️ 性能警告: {operation} 执行时间 {execution_time:.2f}秒 "
                            f"(阈值: {threshold_seconds:.2f}秒)"
                        )
                    
                    return result
                    
                except Exception as e:
                    execution_time = time.time() - start_time
                    self.logger.error(
                        f"❌ {operation} 执行失败，耗时 {execution_time:.2f}秒: {e}"
                    )
                    raise
            
            return wrapper
        return decorator
    
    def get_error_statistics(self) -> Dict[str, Any]:
        """获取错误统计信息"""
        with self._lock:
            return {
                'error_counts': dict(self._error_counts),
                'total_errors': sum(self._error_counts.values()),
                'recent_errors': list(self._error_history)[-10:],  # 最近10个错误
                'error_history_size': len(self._error_history)
            }
    
    def get_performance_statistics(self) -> Dict[str, Any]:
        """获取性能统计信息"""
        with self._lock:
            stats = {}
            for operation, times in self._performance_stats.items():
                if times:
                    stats[operation] = {
                        'count': len(times),
                        'avg_time': sum(times) / len(times),
                        'min_time': min(times),
                        'max_time': max(times),
                        'total_time': sum(times)
                    }
            return stats
    
    def export_error_report(self, file_path: str):
        """导出错误报告"""
        report = {
            'timestamp': datetime.now().isoformat(),
            'error_statistics': self.get_error_statistics(),
            'performance_statistics': self.get_performance_statistics()
        }
        
        try:
            os.makedirs(os.path.dirname(file_path), exist_ok=True)
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(report, f, indent=2, ensure_ascii=False)
            
            self.logger.info(f"📊 错误报告已导出: {file_path}")
        except Exception as e:
            self.logger.error(f"❌ 导出错误报告失败: {e}")
    
    def clear_statistics(self):
        """清空统计信息"""
        with self._lock:
            self._error_counts.clear()
            self._error_history.clear()
            self._performance_stats.clear()
        
        self.logger.info("📊 错误统计信息已清空")


class SmartLogger:
    """智能日志器"""
    
    def __init__(self, name: str, error_handler: EnhancedErrorHandler):
        self.name = name
        self.error_handler = error_handler
        self.logger = logging.getLogger(name)
    
    def debug(self, message: str, **kwargs):
        """调试日志"""
        self.logger.debug(f"🔍 {message}", **kwargs)
    
    def info(self, message: str, **kwargs):
        """信息日志"""
        self.logger.info(f"ℹ️ {message}", **kwargs)
    
    def warning(self, message: str, **kwargs):
        """警告日志"""
        self.logger.warning(f"⚠️ {message}", **kwargs)
    
    def error(self, message: str, **kwargs):
        """错误日志"""
        self.logger.error(f"❌ {message}", **kwargs)
    
    def critical(self, message: str, **kwargs):
        """严重错误日志"""
        self.logger.critical(f"🚨 {message}", **kwargs)
    
    def step(self, step_name: str, status: str = "开始", details: str = ""):
        """步骤日志"""
        if status == "开始":
            self.info(f"步骤开始: {step_name} {details}")
        elif status == "完成":
            self.info(f"步骤完成: {step_name} {details}")
        elif status == "失败":
            self.error(f"步骤失败: {step_name} {details}")
    
    def performance_warning(self, message: str):
        """性能警告"""
        self.warning(f"性能提醒: {message}")
    
    def business_info(self, message: str, stock_code: Optional[str] = None):
        """业务信息日志"""
        if stock_code:
            self.info(f"[{stock_code}] {message}")
        else:
            self.info(message)


# 全局错误处理器实例
_global_error_handler = None
_global_smart_logger = None


def get_error_handler() -> EnhancedErrorHandler:
    """获取全局错误处理器"""
    global _global_error_handler
    if _global_error_handler is None:
        _global_error_handler = EnhancedErrorHandler("MythQuant")
    return _global_error_handler


def get_smart_logger(name: str = "MythQuant") -> SmartLogger:
    """获取智能日志器"""
    global _global_smart_logger
    if _global_smart_logger is None:
        _global_smart_logger = SmartLogger(name, get_error_handler())
    return _global_smart_logger


# 便捷装饰器
def handle_errors(category: ErrorCategory, operation: str, **kwargs):
    """便捷的错误处理装饰器"""
    return get_error_handler().with_error_handling(category, operation, **kwargs)


def monitor_performance(operation: str, threshold: float = 1.0):
    """便捷的性能监控装饰器"""
    return get_error_handler().with_performance_logging(operation, threshold)


if __name__ == '__main__':
    # 测试错误处理器
    handler = get_error_handler()
    logger = get_smart_logger()
    
    # 测试错误记录
    try:
        raise ValueError("测试错误")
    except Exception as e:
        handler.log_error(e, ErrorCategory.VALIDATION, {'test': True})
    
    # 测试装饰器
    @handle_errors(ErrorCategory.CALCULATION, "测试计算", fallback_value=0)
    @monitor_performance("测试操作", 0.1)
    def test_function():
        time.sleep(0.2)
        return 42
    
    result = test_function()
    print(f"结果: {result}")
    
    # 输出统计信息
    print("错误统计:", handler.get_error_statistics())
    print("性能统计:", handler.get_performance_statistics())


class LoggingConfig:
    """日志配置管理器"""

    @staticmethod
    def setup_enhanced_logging(log_level: str = "INFO",
                             log_file: Optional[str] = None,
                             enable_console: bool = True,
                             enable_performance: bool = True):
        """
        设置增强的日志配置

        Args:
            log_level: 日志级别
            log_file: 日志文件路径
            enable_console: 是否启用控制台输出
            enable_performance: 是否启用性能日志
        """
        # 获取根日志器
        root_logger = logging.getLogger()
        root_logger.setLevel(getattr(logging, log_level.upper()))

        # 清除现有的处理器
        for handler in root_logger.handlers[:]:
            root_logger.removeHandler(handler)

        # 创建格式器
        detailed_formatter = logging.Formatter(
            '%(asctime)s - %(name)s - %(levelname)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        simple_formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(message)s'
        )

        # 控制台处理器
        if enable_console:
            console_handler = logging.StreamHandler()
            console_handler.setFormatter(simple_formatter)
            console_handler.setLevel(getattr(logging, log_level.upper()))
            root_logger.addHandler(console_handler)

        # 文件处理器
        if log_file:
            try:
                os.makedirs(os.path.dirname(log_file), exist_ok=True)
                file_handler = logging.FileHandler(log_file, encoding='utf-8')
                file_handler.setFormatter(detailed_formatter)
                file_handler.setLevel(logging.DEBUG)  # 文件记录更详细的日志
                root_logger.addHandler(file_handler)
            except Exception as e:
                print(f"⚠️ 无法创建日志文件 {log_file}: {e}")

        # 设置第三方库的日志级别
        logging.getLogger('mootdx').setLevel(logging.WARNING)
        logging.getLogger('pandas').setLevel(logging.WARNING)
        logging.getLogger('numpy').setLevel(logging.WARNING)

        # 日志配置完成，不在此处输出，由调用方决定是否显示


def setup_project_logging(log_dir: str = "logs"):
    """设置项目日志"""
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    log_file = os.path.join(log_dir, f"mythquant_{timestamp}.log")

    LoggingConfig.setup_enhanced_logging(
        log_level="INFO",
        log_file=log_file,
        enable_console=False,  # 禁用console输出，避免与结构化输出重复
        enable_performance=True
    )

    return log_file
