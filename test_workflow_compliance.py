#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
1分钟数据工作流程合规性测试（根目录版本）
验证当前生产环境是否完全实现了知识库中的改进工作流程
"""

import os
import sys
from pathlib import Path
from typing import Dict, Any, List

try:
    from test_config import enable_test_mode, MinuteDataTestConfig
    from utils.smart_file_selector import SmartFileSelector
    from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
    from utils.missing_data_processor import MissingDataProcessor
    from utils.unified_interfaces import check_incremental_download_prerequisite
    from test_environments.shared.utilities.specific_minute_data_fetcher import get_specific_minute_data
except ImportError as e:
    print(f"❌ 导入失败: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)


def test_workflow_compliance():
    """测试工作流程合规性"""
    print("🎯 1分钟数据工作流程合规性测试")
    print("=" * 60)
    
    # 启用测试模式
    enable_test_mode()
    print("✅ 测试模式已启用")
    
    test_results = {}
    passed_count = 0
    
    # 第1步：智能文件选择和分析
    print("\n🔍 [1/7] 测试智能文件选择和分析...")
    try:
        # 检查智能文件选择器是否存在
        selector = SmartFileSelector("./signals")  # 使用生产路径进行测试
        
        # 检查关键方法
        methods = ['find_best_file', 'analyze_file', 'scan_candidate_files']
        for method in methods:
            if hasattr(selector, method):
                print(f"   ✅ 方法存在: {method}")
            else:
                print(f"   ❌ 方法缺失: {method}")
                test_results['智能文件选择'] = False
                break
        else:
            test_results['智能文件选择'] = True
            passed_count += 1
            print(f"   ✅ 智能文件选择器功能完整")
            
    except Exception as e:
        print(f"   ❌ 智能文件选择测试失败: {e}")
        test_results['智能文件选择'] = False
    
    # 第2步：增量下载前提条件判断
    print("\n🔍 [2/7] 测试增量下载前提条件判断...")
    try:
        # 检查统一接口是否可用
        from utils.unified_interfaces import get_unified_data_downloader
        
        downloader = get_unified_data_downloader()
        print(f"   ✅ 统一数据下载器初始化成功")
        
        # 检查关键方法
        if hasattr(downloader, 'check_incremental_download_prerequisite'):
            print(f"   ✅ 增量下载前提条件检查方法存在")
            test_results['增量下载前提条件'] = True
            passed_count += 1
        else:
            print(f"   ❌ 增量下载前提条件检查方法缺失")
            test_results['增量下载前提条件'] = False
            
    except Exception as e:
        print(f"   ❌ 增量下载前提条件测试失败: {e}")
        test_results['增量下载前提条件'] = False
    
    # 第3步：数据质量检查与修复
    print("\n🔍 [3/7] 测试数据质量检查与修复...")
    try:
        processor = MissingDataProcessor()
        
        # 检查关键方法
        methods = ['detect_missing_minute_data', 'repair_missing_data', 'process_missing_data_for_file']
        for method in methods:
            if hasattr(processor, method):
                print(f"   ✅ 方法存在: {method}")
            else:
                print(f"   ❌ 方法缺失: {method}")
                test_results['数据质量检查'] = False
                break
        else:
            test_results['数据质量检查'] = True
            passed_count += 1
            print(f"   ✅ 缺失数据处理器功能完整")
            
    except Exception as e:
        print(f"   ❌ 数据质量检查测试失败: {e}")
        test_results['数据质量检查'] = False
    
    # 第4步：特定分钟数据获取
    print("\n🔍 [4/7] 测试特定分钟数据获取...")
    try:
        # 检查特定数据获取接口
        data = get_specific_minute_data('000617', '202507041447')
        print(f"   ✅ 特定分钟数据获取接口可用")
        print(f"   📊 数据获取结果: {'有数据' if data else '无数据（正常，可能超出范围）'}")
        test_results['特定数据获取'] = True
        passed_count += 1
        
    except Exception as e:
        print(f"   ❌ 特定分钟数据获取测试失败: {e}")
        test_results['特定数据获取'] = False
    
    # 第5步：结构化下载器
    print("\n🔍 [5/7] 测试结构化下载器...")
    try:
        downloader = StructuredInternetMinuteDownloader()
        
        # 检查四步处理流程方法
        step_methods = [
            '_step1_smart_file_selection',
            '_step2_incremental_feasibility_check', 
            '_step3_missing_data_audit_and_repair',
            '_step4_incremental_data_download'
        ]
        
        for method in step_methods:
            if hasattr(downloader, method):
                print(f"   ✅ 步骤方法存在: {method}")
            else:
                print(f"   ❌ 步骤方法缺失: {method}")
                test_results['结构化下载器'] = False
                break
        else:
            test_results['结构化下载器'] = True
            passed_count += 1
            print(f"   ✅ 结构化下载器四步流程完整")
            
    except Exception as e:
        print(f"   ❌ 结构化下载器测试失败: {e}")
        test_results['结构化下载器'] = False
    
    # 第6步：输出格式化
    print("\n🔍 [6/7] 测试输出格式化...")
    try:
        from utils.structured_output_formatter import StructuredOutputFormatter

        formatter = StructuredOutputFormatter()
        print(f"   ✅ 结构化输出格式器可用")

        # 检查关键方法
        if hasattr(formatter, 'format_step_output'):
            print(f"   ✅ 步骤输出格式化方法存在")
            test_results['输出格式化'] = True
            passed_count += 1
        else:
            print(f"   ❌ 步骤输出格式化方法缺失")
            test_results['输出格式化'] = False

    except Exception as e:
        print(f"   ❌ 输出格式化测试失败: {e}")
        test_results['输出格式化'] = False

    # 第7步：最终验证
    print("\n🔍 [7/7] 测试最终验证...")
    try:
        # 检查数据质量稽核器是否存在
        auditor_path = Path("src/mythquant/core/data_quality_auditor.py")
        if auditor_path.exists():
            print(f"   ✅ 数据质量稽核器文件存在")

            # 检查关键方法（通过文件内容检查）
            with open(auditor_path, 'r', encoding='utf-8') as f:
                content = f.read()
                if 'audit_after_task_execution' in content:
                    print(f"   ✅ 任务执行后稽核方法存在")
                    test_results['最终验证'] = True
                    passed_count += 1
                else:
                    print(f"   ❌ 任务执行后稽核方法缺失")
                    test_results['最终验证'] = False
        else:
            print(f"   ❌ 数据质量稽核器文件不存在")
            test_results['最终验证'] = False

    except Exception as e:
        print(f"   ❌ 最终验证测试失败: {e}")
        test_results['最终验证'] = False
    
    # 生成总结
    total_steps = 7
    success_rate = (passed_count / total_steps) * 100
    
    print(f"\n📊 工作流程合规性测试总结:")
    print(f"=" * 60)
    print(f"   总测试步骤: {total_steps}")
    print(f"   通过步骤: {passed_count}")
    print(f"   成功率: {success_rate:.1f}%")
    print(f"   整体评估: {'✅ 合规' if success_rate >= 85 else '❌ 不合规'}")
    
    print(f"\n📋 详细结果:")
    for step_name, result in test_results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {step_name}: {status}")
    
    if success_rate < 85:
        print(f"\n💡 改进建议:")
        for step_name, result in test_results.items():
            if not result:
                print(f"   - 修复 {step_name} 功能")
    
    return success_rate >= 85


if __name__ == '__main__':
    success = test_workflow_compliance()
    sys.exit(0 if success else 1)
