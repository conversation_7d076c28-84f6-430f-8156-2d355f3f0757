#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存提供者实现

实现不同类型的缓存提供者：内存、<PERSON><PERSON>、文件等
"""

import json
import pickle
import threading
import time
from abc import ABC, abstractmethod
from typing import Any, Optional, Dict, List, Union
from datetime import datetime, timedelta
from pathlib import Path
import logging

logger = logging.getLogger(__name__)


class CacheProvider(ABC):
    """缓存提供者抽象基类"""
    
    @abstractmethod
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        pass
    
    @abstractmethod
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        pass
    
    @abstractmethod
    def delete(self, key: str) -> bool:
        """删除缓存"""
        pass
    
    @abstractmethod
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        pass
    
    @abstractmethod
    def clear(self) -> bool:
        """清空所有缓存"""
        pass
    
    @abstractmethod
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        pass


class MemoryCacheProvider(CacheProvider):
    """内存缓存提供者"""
    
    def __init__(self, max_size: int = 1000, default_ttl: int = 3600):
        self._cache: Dict[str, Dict[str, Any]] = {}
        self._max_size = max_size
        self._default_ttl = default_ttl
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0
        }
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            if key not in self._cache:
                self._stats['misses'] += 1
                return None
            
            entry = self._cache[key]
            
            # 检查是否过期
            if self._is_expired(entry):
                del self._cache[key]
                self._stats['misses'] += 1
                return None
            
            # 更新访问时间（用于LRU）
            entry['last_accessed'] = time.time()
            self._stats['hits'] += 1
            
            return entry['value']
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            try:
                # 检查是否需要清理空间
                if len(self._cache) >= self._max_size and key not in self._cache:
                    self._evict_lru()
                
                ttl = ttl or self._default_ttl
                expires_at = time.time() + ttl if ttl > 0 else None
                
                self._cache[key] = {
                    'value': value,
                    'created_at': time.time(),
                    'last_accessed': time.time(),
                    'expires_at': expires_at,
                    'ttl': ttl
                }
                
                self._stats['sets'] += 1
                return True
                
            except Exception as e:
                logger.error(f"内存缓存设置失败: {key}, 错误: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self._lock:
            if key in self._cache:
                del self._cache[key]
                self._stats['deletes'] += 1
                return True
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        with self._lock:
            if key not in self._cache:
                return False
            
            entry = self._cache[key]
            if self._is_expired(entry):
                del self._cache[key]
                return False
            
            return True
    
    def clear(self) -> bool:
        """清空所有缓存"""
        with self._lock:
            self._cache.clear()
            return True
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            return {
                **self._stats,
                'size': len(self._cache),
                'max_size': self._max_size,
                'hit_rate': hit_rate,
                'memory_usage': self._estimate_memory_usage()
            }
    
    def _is_expired(self, entry: Dict[str, Any]) -> bool:
        """检查条目是否过期"""
        expires_at = entry.get('expires_at')
        return expires_at is not None and time.time() > expires_at
    
    def _evict_lru(self):
        """清理最近最少使用的条目"""
        if not self._cache:
            return
        
        # 找到最久未访问的条目
        lru_key = min(self._cache.keys(), 
                     key=lambda k: self._cache[k]['last_accessed'])
        
        del self._cache[lru_key]
        self._stats['evictions'] += 1
        
        logger.debug(f"LRU清理: {lru_key}")
    
    def _estimate_memory_usage(self) -> int:
        """估算内存使用量（字节）"""
        try:
            import sys
            total_size = 0
            for key, entry in self._cache.items():
                total_size += sys.getsizeof(key)
                total_size += sys.getsizeof(entry['value'])
                total_size += sys.getsizeof(entry)
            return total_size
        except:
            return 0


class RedisCacheProvider(CacheProvider):
    """Redis缓存提供者"""
    
    def __init__(self, host: str = 'localhost', port: int = 6379, 
                 db: int = 0, password: Optional[str] = None,
                 key_prefix: str = 'mythquant:'):
        self._host = host
        self._port = port
        self._db = db
        self._password = password
        self._key_prefix = key_prefix
        self._redis = None
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'errors': 0
        }
        
        self._connect()
    
    def _connect(self):
        """连接Redis"""
        try:
            import redis
            self._redis = redis.Redis(
                host=self._host,
                port=self._port,
                db=self._db,
                password=self._password,
                decode_responses=False  # 保持二进制数据
            )
            # 测试连接
            self._redis.ping()
            logger.info(f"Redis连接成功: {self._host}:{self._port}")
        except ImportError:
            logger.warning("Redis模块未安装，Redis缓存不可用")
            self._redis = None
        except Exception as e:
            logger.error(f"Redis连接失败: {e}")
            self._redis = None
    
    def _get_key(self, key: str) -> str:
        """获取完整的键名"""
        return f"{self._key_prefix}{key}"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        if not self._redis:
            return None
        
        try:
            full_key = self._get_key(key)
            data = self._redis.get(full_key)
            
            if data is None:
                self._stats['misses'] += 1
                return None
            
            value = pickle.loads(data)
            self._stats['hits'] += 1
            return value
            
        except Exception as e:
            logger.error(f"Redis获取失败: {key}, 错误: {e}")
            self._stats['errors'] += 1
            return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        if not self._redis:
            return False
        
        try:
            full_key = self._get_key(key)
            data = pickle.dumps(value)
            
            if ttl:
                result = self._redis.setex(full_key, ttl, data)
            else:
                result = self._redis.set(full_key, data)
            
            if result:
                self._stats['sets'] += 1
                return True
            return False
            
        except Exception as e:
            logger.error(f"Redis设置失败: {key}, 错误: {e}")
            self._stats['errors'] += 1
            return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        if not self._redis:
            return False
        
        try:
            full_key = self._get_key(key)
            result = self._redis.delete(full_key)
            
            if result > 0:
                self._stats['deletes'] += 1
                return True
            return False
            
        except Exception as e:
            logger.error(f"Redis删除失败: {key}, 错误: {e}")
            self._stats['errors'] += 1
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        if not self._redis:
            return False
        
        try:
            full_key = self._get_key(key)
            return bool(self._redis.exists(full_key))
        except Exception as e:
            logger.error(f"Redis存在性检查失败: {key}, 错误: {e}")
            return False
    
    def clear(self) -> bool:
        """清空所有缓存"""
        if not self._redis:
            return False
        
        try:
            pattern = f"{self._key_prefix}*"
            keys = self._redis.keys(pattern)
            if keys:
                self._redis.delete(*keys)
            return True
        except Exception as e:
            logger.error(f"Redis清空失败: {e}")
            return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self._stats['hits'] + self._stats['misses']
        hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
        
        stats = {
            **self._stats,
            'hit_rate': hit_rate,
            'connected': self._redis is not None
        }
        
        # 获取Redis服务器信息
        if self._redis:
            try:
                info = self._redis.info()
                stats.update({
                    'redis_memory': info.get('used_memory_human', 'N/A'),
                    'redis_keys': info.get('db0', {}).get('keys', 0) if 'db0' in info else 0
                })
            except:
                pass
        
        return stats


class FileCacheProvider(CacheProvider):
    """文件缓存提供者"""
    
    def __init__(self, cache_dir: str = ".cache", max_files: int = 1000):
        self._cache_dir = Path(cache_dir)
        self._cache_dir.mkdir(exist_ok=True)
        self._max_files = max_files
        self._lock = threading.RLock()
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'evictions': 0
        }
    
    def _get_file_path(self, key: str) -> Path:
        """获取缓存文件路径"""
        # 使用哈希避免文件名冲突
        import hashlib
        hash_key = hashlib.md5(key.encode()).hexdigest()
        return self._cache_dir / f"{hash_key}.cache"
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值"""
        with self._lock:
            file_path = self._get_file_path(key)
            
            if not file_path.exists():
                self._stats['misses'] += 1
                return None
            
            try:
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                # 检查是否过期
                if self._is_expired(data):
                    file_path.unlink()
                    self._stats['misses'] += 1
                    return None
                
                # 更新访问时间
                data['last_accessed'] = time.time()
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)
                
                self._stats['hits'] += 1
                return data['value']
                
            except Exception as e:
                logger.error(f"文件缓存读取失败: {key}, 错误: {e}")
                self._stats['misses'] += 1
                return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值"""
        with self._lock:
            try:
                # 检查是否需要清理空间
                self._cleanup_if_needed()
                
                file_path = self._get_file_path(key)
                expires_at = time.time() + ttl if ttl else None
                
                data = {
                    'value': value,
                    'created_at': time.time(),
                    'last_accessed': time.time(),
                    'expires_at': expires_at,
                    'ttl': ttl
                }
                
                with open(file_path, 'wb') as f:
                    pickle.dump(data, f)
                
                self._stats['sets'] += 1
                return True
                
            except Exception as e:
                logger.error(f"文件缓存写入失败: {key}, 错误: {e}")
                return False
    
    def delete(self, key: str) -> bool:
        """删除缓存"""
        with self._lock:
            file_path = self._get_file_path(key)
            
            if file_path.exists():
                try:
                    file_path.unlink()
                    self._stats['deletes'] += 1
                    return True
                except Exception as e:
                    logger.error(f"文件缓存删除失败: {key}, 错误: {e}")
            
            return False
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        with self._lock:
            file_path = self._get_file_path(key)
            
            if not file_path.exists():
                return False
            
            try:
                with open(file_path, 'rb') as f:
                    data = pickle.load(f)
                
                if self._is_expired(data):
                    file_path.unlink()
                    return False
                
                return True
                
            except:
                return False
    
    def clear(self) -> bool:
        """清空所有缓存"""
        with self._lock:
            try:
                for file_path in self._cache_dir.glob("*.cache"):
                    file_path.unlink()
                return True
            except Exception as e:
                logger.error(f"文件缓存清空失败: {e}")
                return False
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        with self._lock:
            total_requests = self._stats['hits'] + self._stats['misses']
            hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
            
            # 统计文件数量和大小
            file_count = len(list(self._cache_dir.glob("*.cache")))
            total_size = sum(f.stat().st_size for f in self._cache_dir.glob("*.cache"))
            
            return {
                **self._stats,
                'hit_rate': hit_rate,
                'file_count': file_count,
                'total_size': total_size,
                'cache_dir': str(self._cache_dir)
            }
    
    def _is_expired(self, data: Dict[str, Any]) -> bool:
        """检查数据是否过期"""
        expires_at = data.get('expires_at')
        return expires_at is not None and time.time() > expires_at
    
    def _cleanup_if_needed(self):
        """如果需要则清理文件"""
        cache_files = list(self._cache_dir.glob("*.cache"))
        
        if len(cache_files) >= self._max_files:
            # 按最后访问时间排序，删除最旧的文件
            files_with_time = []
            
            for file_path in cache_files:
                try:
                    with open(file_path, 'rb') as f:
                        data = pickle.load(f)
                    files_with_time.append((file_path, data.get('last_accessed', 0)))
                except:
                    # 损坏的文件直接删除
                    file_path.unlink()
            
            # 排序并删除最旧的文件
            files_with_time.sort(key=lambda x: x[1])
            files_to_delete = files_with_time[:len(files_with_time) - self._max_files + 1]
            
            for file_path, _ in files_to_delete:
                try:
                    file_path.unlink()
                    self._stats['evictions'] += 1
                except:
                    pass


class CompositeCacheProvider(CacheProvider):
    """组合缓存提供者（多级缓存）"""
    
    def __init__(self, providers: List[CacheProvider]):
        self._providers = providers
        self._stats = {
            'hits': 0,
            'misses': 0,
            'sets': 0,
            'deletes': 0,
            'level_hits': [0] * len(providers)
        }
    
    def get(self, key: str) -> Optional[Any]:
        """获取缓存值（从最快的缓存开始查找）"""
        for i, provider in enumerate(self._providers):
            value = provider.get(key)
            if value is not None:
                self._stats['hits'] += 1
                self._stats['level_hits'][i] += 1
                
                # 回填到更快的缓存层
                self._backfill(key, value, i)
                return value
        
        self._stats['misses'] += 1
        return None
    
    def set(self, key: str, value: Any, ttl: Optional[int] = None) -> bool:
        """设置缓存值（写入所有缓存层）"""
        success = True
        for provider in self._providers:
            if not provider.set(key, value, ttl):
                success = False
        
        if success:
            self._stats['sets'] += 1
        
        return success
    
    def delete(self, key: str) -> bool:
        """删除缓存（从所有缓存层删除）"""
        success = True
        for provider in self._providers:
            if not provider.delete(key):
                success = False
        
        if success:
            self._stats['deletes'] += 1
        
        return success
    
    def exists(self, key: str) -> bool:
        """检查缓存是否存在"""
        for provider in self._providers:
            if provider.exists(key):
                return True
        return False
    
    def clear(self) -> bool:
        """清空所有缓存"""
        success = True
        for provider in self._providers:
            if not provider.clear():
                success = False
        return success
    
    def get_stats(self) -> Dict[str, Any]:
        """获取缓存统计"""
        total_requests = self._stats['hits'] + self._stats['misses']
        hit_rate = self._stats['hits'] / total_requests if total_requests > 0 else 0
        
        provider_stats = []
        for i, provider in enumerate(self._providers):
            stats = provider.get_stats()
            stats['level'] = i
            stats['level_hits'] = self._stats['level_hits'][i]
            provider_stats.append(stats)
        
        return {
            **self._stats,
            'hit_rate': hit_rate,
            'provider_count': len(self._providers),
            'provider_stats': provider_stats
        }
    
    def _backfill(self, key: str, value: Any, found_level: int):
        """回填到更快的缓存层"""
        for i in range(found_level):
            try:
                self._providers[i].set(key, value)
            except Exception as e:
                logger.warning(f"缓存回填失败: level {i}, key {key}, 错误: {e}")


# 工厂函数
def create_cache_provider(provider_type: str, **kwargs) -> CacheProvider:
    """创建缓存提供者"""
    if provider_type == "memory":
        return MemoryCacheProvider(**kwargs)
    elif provider_type == "redis":
        return RedisCacheProvider(**kwargs)
    elif provider_type == "file":
        return FileCacheProvider(**kwargs)
    elif provider_type == "composite":
        providers = kwargs.get('providers', [])
        return CompositeCacheProvider(providers)
    else:
        raise ValueError(f"不支持的缓存提供者类型: {provider_type}")


def create_multi_level_cache(**kwargs) -> CompositeCacheProvider:
    """创建多级缓存"""
    providers = [
        MemoryCacheProvider(max_size=500, default_ttl=300),  # L1: 内存缓存
        RedisCacheProvider(**kwargs.get('redis', {})),       # L2: Redis缓存
        FileCacheProvider(**kwargs.get('file', {}))          # L3: 文件缓存
    ]
    
    # 过滤掉不可用的提供者
    available_providers = []
    for provider in providers:
        if isinstance(provider, RedisCacheProvider) and not provider._redis:
            continue
        available_providers.append(provider)
    
    return CompositeCacheProvider(available_providers)
