#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目根目录清理分析脚本
分析根目录中与项目无关或可以精简归档的文件夹
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class RootDirectoryAnalyzer:
    """根目录分析器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.analysis_result = {}
        
    def analyze_root_directories(self) -> Dict[str, Any]:
        """分析根目录结构"""
        print("🔍 分析项目根目录结构...")
        
        analysis = {
            'core_directories': {
                'description': '核心项目目录（必须保留）',
                'directories': [
                    'src',              # DDD架构源码
                    'tests',            # 测试目录
                    'docs',             # 文档目录
                    'scripts',          # 脚本目录
                    'test_environments' # 测试环境
                ],
                'reason': '这些是当前DDD架构的核心组成部分'
            },
            
            'utility_directories': {
                'description': '工具和配置目录（保留但可优化）',
                'directories': [
                    'tools',            # 工具脚本
                    'utils',            # 工具模块（可能需要迁移到src）
                    'file_io',          # 文件IO模块（可能需要迁移到src）
                    'prompt_templates', # AI提示模板
                    'logs',             # 日志目录（可清理旧日志）
                    'data'              # 数据目录（可清理临时数据）
                ],
                'reason': '这些目录有用但可能需要整理或部分迁移'
            },
            
            'legacy_directories': {
                'description': '遗留目录（可以归档）',
                'directories': [
                    'environments',     # 旧的环境配置（与test_environments重复）
                    'lib',              # 库文件（可能已过时）
                    'assets'            # 资源文件（只有一个img.png）
                ],
                'reason': '这些目录与当前架构关联度低，可以归档'
            },
            
            'temporary_directories': {
                'description': '临时目录（可以删除）',
                'directories': [
                    '__pycache__',      # Python缓存
                ],
                'reason': '这些是临时生成的目录，可以安全删除'
            },
            
            'backup_directories': {
                'description': '备份目录（可以归档或删除）',
                'directories': [
                    'architecture_migration_final_archive_20250802_233447',
                    'backup_test_integration_20250803_232702'
                ],
                'reason': '这些是历史备份，可以归档到专门的备份目录'
            },
            
            'core_files': {
                'description': '核心文件（必须保留）',
                'files': [
                    'main.py',
                    'user_config.py',
                    'test_config.py',
                    'requirements.txt',
                    'pyproject.toml',
                    'pytest.ini'
                ],
                'reason': '这些是项目运行的核心文件'
            },
            
            'temporary_files': {
                'description': '临时文件（可以删除或归档）',
                'files': [
                    'comprehensive_1min_test.py',
                    'comprehensive_test_with_proper_config.py',
                    'comprehensive_test_report.md',
                    'architecture_cleanup_report_20241220.json',
                    'cleanup_report_20250802_233731.txt',
                    'test_integration_report_20250803_232702.json'
                ],
                'reason': '这些是临时测试文件或报告，可以移动到适当位置'
            }
        }
        
        return analysis
    
    def generate_cleanup_recommendations(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成清理建议"""
        print("💡 生成清理建议...")
        
        recommendations = {
            'immediate_actions': {
                'delete_safely': {
                    'directories': analysis['temporary_directories']['directories'],
                    'description': '可以安全删除的临时目录'
                },
                'archive_backups': {
                    'directories': analysis['backup_directories']['directories'],
                    'target': 'archive/backups/',
                    'description': '将备份目录归档到专门位置'
                }
            },
            
            'optimization_actions': {
                'consolidate_utilities': {
                    'action': '将utils和file_io模块迁移到src/mythquant/',
                    'directories': ['utils', 'file_io'],
                    'description': '统一工具模块到DDD架构中'
                },
                'clean_logs': {
                    'action': '清理30天前的日志文件',
                    'directory': 'logs',
                    'description': '保留最近日志，归档旧日志'
                },
                'organize_data': {
                    'action': '清理临时数据文件',
                    'directory': 'data',
                    'description': '保留配置文件，清理临时数据'
                }
            },
            
            'archival_actions': {
                'archive_legacy': {
                    'directories': analysis['legacy_directories']['directories'],
                    'target': 'archive/legacy/',
                    'description': '归档遗留目录'
                },
                'archive_temp_files': {
                    'files': analysis['temporary_files']['files'],
                    'target': 'archive/temp_files/',
                    'description': '归档临时文件和报告'
                }
            },
            
            'final_structure': {
                'description': '清理后的理想根目录结构',
                'structure': [
                    'src/',              # DDD架构源码
                    'tests/',            # 测试目录
                    'docs/',             # 文档目录
                    'scripts/',          # 脚本目录
                    'test_environments/', # 测试环境
                    'tools/',            # 工具脚本
                    'prompt_templates/', # AI提示模板
                    'logs/',             # 日志目录（清理后）
                    'data/',             # 数据目录（清理后）
                    'archive/',          # 归档目录
                    'main.py',           # 主程序
                    'user_config.py',    # 用户配置
                    'test_config.py',    # 测试配置
                    'requirements.txt',  # 依赖文件
                    'pyproject.toml',    # 项目配置
                    'pytest.ini'         # 测试配置
                ]
            }
        }
        
        return recommendations
    
    def estimate_cleanup_impact(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """评估清理影响"""
        print("📊 评估清理影响...")
        
        # 统计目录和文件数量
        total_dirs = 0
        total_files = 0
        
        for category in ['core_directories', 'utility_directories', 'legacy_directories', 
                        'temporary_directories', 'backup_directories']:
            if category in analysis:
                total_dirs += len(analysis[category]['directories'])
        
        for category in ['core_files', 'temporary_files']:
            if category in analysis:
                total_files += len(analysis[category]['files'])
        
        # 计算清理统计
        delete_dirs = len(analysis['temporary_directories']['directories'])
        archive_dirs = (len(analysis['legacy_directories']['directories']) + 
                       len(analysis['backup_directories']['directories']))
        keep_dirs = (len(analysis['core_directories']['directories']) + 
                    len(analysis['utility_directories']['directories']))
        
        impact = {
            'statistics': {
                'total_directories_analyzed': total_dirs,
                'total_files_analyzed': total_files,
                'directories_to_delete': delete_dirs,
                'directories_to_archive': archive_dirs,
                'directories_to_keep': keep_dirs,
                'files_to_archive': len(analysis['temporary_files']['files'])
            },
            'space_savings': {
                'estimated_cleanup_percentage': round((delete_dirs + archive_dirs) / total_dirs * 100, 1),
                'root_directory_simplification': f"从{total_dirs}个目录减少到{keep_dirs}个核心目录"
            },
            'benefits': [
                '根目录结构更清晰',
                '符合DDD架构标准',
                '减少维护复杂度',
                '提高开发效率',
                '便于新开发者理解项目结构'
            ],
            'risks': [
                '需要验证归档目录中是否有重要文件',
                '可能需要更新一些脚本的路径引用',
                '建议先创建完整备份'
            ]
        }
        
        return impact
    
    def generate_cleanup_script_template(self, recommendations: Dict[str, Any]) -> str:
        """生成清理脚本模板"""
        script_template = '''#!/bin/bash
# 项目根目录清理脚本
# 生成时间: {timestamp}

echo "🧹 开始项目根目录清理..."

# 1. 创建归档目录结构
echo "📁 创建归档目录结构..."
mkdir -p archive/{{backups,legacy,temp_files}}

# 2. 删除临时目录
echo "🗑️ 删除临时目录..."
{delete_commands}

# 3. 归档备份目录
echo "📦 归档备份目录..."
{archive_backup_commands}

# 4. 归档遗留目录
echo "📚 归档遗留目录..."
{archive_legacy_commands}

# 5. 归档临时文件
echo "📄 归档临时文件..."
{archive_files_commands}

# 6. 清理日志文件（保留最近30天）
echo "🧹 清理旧日志文件..."
find logs/ -name "*.log" -mtime +30 -exec mv {{}} archive/temp_files/ \\; 2>/dev/null || true
find logs/ -name "*.json" -mtime +30 -exec mv {{}} archive/temp_files/ \\; 2>/dev/null || true

echo "✅ 根目录清理完成！"
echo "📋 清理后的目录结构："
ls -la | grep "^d"
'''.format(
            timestamp=datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
            delete_commands=self._generate_delete_commands(recommendations),
            archive_backup_commands=self._generate_archive_commands(
                recommendations['immediate_actions']['archive_backups']
            ),
            archive_legacy_commands=self._generate_archive_commands(
                recommendations['archival_actions']['archive_legacy']
            ),
            archive_files_commands=self._generate_file_archive_commands(
                recommendations['archival_actions']['archive_temp_files']
            )
        )
        
        return script_template
    
    def _generate_delete_commands(self, recommendations: Dict[str, Any]) -> str:
        """生成删除命令"""
        commands = []
        for directory in recommendations['immediate_actions']['delete_safely']['directories']:
            commands.append(f'rm -rf {directory}')
        return '\n'.join(commands)
    
    def _generate_archive_commands(self, archive_config: Dict[str, Any]) -> str:
        """生成归档命令"""
        commands = []
        target = archive_config['target']
        for directory in archive_config['directories']:
            commands.append(f'[ -d {directory} ] && mv {directory} {target}')
        return '\n'.join(commands)
    
    def _generate_file_archive_commands(self, archive_config: Dict[str, Any]) -> str:
        """生成文件归档命令"""
        commands = []
        target = archive_config['target']
        for file in archive_config['files']:
            commands.append(f'[ -f {file} ] && mv {file} {target}')
        return '\n'.join(commands)
    
    def run_analysis(self) -> Dict[str, Any]:
        """运行完整分析"""
        print("🎯 项目根目录清理分析开始")
        print("=" * 60)
        
        # 1. 分析目录结构
        analysis = self.analyze_root_directories()
        
        # 2. 生成清理建议
        recommendations = self.generate_cleanup_recommendations(analysis)
        
        # 3. 评估清理影响
        impact = self.estimate_cleanup_impact(analysis)
        
        # 4. 生成清理脚本
        cleanup_script = self.generate_cleanup_script_template(recommendations)
        
        # 5. 汇总结果
        result = {
            'analysis_time': datetime.now().isoformat(),
            'analysis': analysis,
            'recommendations': recommendations,
            'impact_assessment': impact,
            'cleanup_script': cleanup_script
        }
        
        # 6. 保存分析报告
        report_file = self.project_root / f'root_cleanup_analysis_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(result, f, indent=2, ensure_ascii=False)
        
        # 7. 保存清理脚本
        script_file = self.project_root / f'cleanup_root_directory.sh'
        with open(script_file, 'w', encoding='utf-8') as f:
            f.write(cleanup_script)
        os.chmod(script_file, 0o755)  # 添加执行权限
        
        print(f"\n📊 分析结果:")
        print(f"   总目录数: {impact['statistics']['total_directories_analyzed']}")
        print(f"   可删除目录: {impact['statistics']['directories_to_delete']}")
        print(f"   可归档目录: {impact['statistics']['directories_to_archive']}")
        print(f"   保留目录: {impact['statistics']['directories_to_keep']}")
        print(f"   清理比例: {impact['space_savings']['estimated_cleanup_percentage']}%")
        
        print(f"\n📋 生成文件:")
        print(f"   分析报告: {report_file}")
        print(f"   清理脚本: {script_file}")
        
        return result


def main():
    """主函数"""
    analyzer = RootDirectoryAnalyzer()
    
    print("📋 项目根目录清理分析")
    print("=" * 40)
    print("此工具将分析项目根目录结构，识别可以清理或归档的目录")
    print()
    
    result = analyzer.run_analysis()
    
    print("\n🎯 清理建议总结:")
    print("=" * 40)
    
    # 显示主要建议
    recommendations = result['recommendations']
    
    print("🗑️ 可以安全删除:")
    for directory in recommendations['immediate_actions']['delete_safely']['directories']:
        print(f"   - {directory}")
    
    print("\n📦 建议归档:")
    for directory in recommendations['immediate_actions']['archive_backups']['directories']:
        print(f"   - {directory} (备份目录)")
    for directory in recommendations['archival_actions']['archive_legacy']['directories']:
        print(f"   - {directory} (遗留目录)")
    
    print("\n🔧 建议优化:")
    print("   - 将utils和file_io模块迁移到src/mythquant/")
    print("   - 清理30天前的日志文件")
    print("   - 整理data目录中的临时文件")
    
    print(f"\n✨ 清理后效果:")
    print(f"   - 根目录将从复杂结构简化为{len(recommendations['final_structure']['structure'])}个核心项目")
    print(f"   - 清理比例: {result['impact_assessment']['space_savings']['estimated_cleanup_percentage']}%")
    print(f"   - 符合DDD架构标准")
    
    print(f"\n📋 下一步:")
    print("1. 查看生成的分析报告了解详细信息")
    print("2. 检查cleanup_root_directory.sh脚本")
    print("3. 创建完整备份后执行清理脚本")
    print("4. 验证清理后的项目功能正常")
    
    return True


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
