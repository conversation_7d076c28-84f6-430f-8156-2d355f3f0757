#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确定位workflow违规的代码位置

找出在TaskManager第273行日志输出后、第313行四步流程开始前
是哪里调用了MissingDataProcessor.detect_missing_minute_data
"""

import os
import sys
import builtins

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def install_comprehensive_patch():
    """安装全面的追踪补丁"""
    try:
        # 1. 追踪MissingDataProcessor的调用
        from utils.missing_data_processor import MissingDataProcessor
        
        original_detect = MissingDataProcessor.detect_missing_minute_data
        original_init = MissingDataProcessor.__init__
        
        def patched_init(self):
            """追踪MissingDataProcessor的初始化"""
            import traceback
            print(f"\nMissingDataProcessor.__init__被调用")
            print("初始化调用堆栈:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-5:], 1):
                print(f"   {i}. {frame.strip()}")
            print("=" * 80)
            
            return original_init(self)
        
        def patched_detect(self, file_path, stock_code, silent=True):
            """追踪detect_missing_minute_data的调用"""
            import traceback
            
            print(f"\nMissingDataProcessor.detect_missing_minute_data被调用！")
            print(f"   文件路径: {file_path}")
            print(f"   股票代码: {stock_code}")
            print(f"   静默模式: {silent}")

            print(f"\n详细调用堆栈:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack, 1):
                if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'execute']):
                    print(f"   >>> {i:2d}. {frame.strip()}")
                else:
                    print(f"      {i:2d}. {frame.strip()}")
            
            print(f"\n" + "="*120)
            
            return original_detect(self, file_path, stock_code, silent)
        
        # 替换方法
        MissingDataProcessor.__init__ = patched_init
        MissingDataProcessor.detect_missing_minute_data = patched_detect
        
        print("✅ MissingDataProcessor追踪补丁安装成功")
        
        # 2. 追踪TaskManager的关键方法调用
        from src.mythquant.core.task_manager import TaskManager
        
        original_execute_minute = TaskManager._execute_minute_task
        
        def patched_execute_minute(self, task, target_stocks):
            """追踪_execute_minute_task的执行"""
            print(f"\nTaskManager._execute_minute_task开始执行")
            print(f"   任务: {task.name}")
            print(f"   目标股票: {target_stocks}")

            result = original_execute_minute(self, task, target_stocks)

            print(f"TaskManager._execute_minute_task执行完成")
            return result
        
        TaskManager._execute_minute_task = patched_execute_minute
        
        print("✅ TaskManager追踪补丁安装成功")
        
        return True
        
    except Exception as e:
        print(f"❌ 安装追踪补丁失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_main_with_tracking():
    """运行main.py并追踪调用"""
    print("\n开始运行main.py并追踪workflow违规")
    print("=" * 80)

    try:
        # 直接导入并运行main.py的逻辑
        from main import main

        print("开始执行main函数...")
        result = main()

        print(f"main函数执行完成，结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 执行失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("精确定位workflow违规代码")
    print("=" * 120)
    
    # 安装追踪补丁
    if not install_comprehensive_patch():
        return 1
    
    # 运行main.py
    success = run_main_with_tracking()
    
    print(f"\n追踪结果")
    print("=" * 80)

    if success:
        print("追踪执行完成")
        print("重点关注MissingDataProcessor调用的堆栈信息")
        print("查找TaskManager相关的调用路径")
    else:
        print("追踪执行失败")

    print("\n分析重点:")
    print("   1. MissingDataProcessor何时被初始化？")
    print("   2. detect_missing_minute_data何时被调用？")
    print("   3. 调用路径中是否有TaskManager._execute_minute_task？")
    print("   4. 是否在第273行日志后、第313行print前被调用？")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
