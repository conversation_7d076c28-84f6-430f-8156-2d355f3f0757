#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
缓存性能测试脚本
测试统一缓存管理器的性能，对比优化前后的数据访问速度
"""

import sys
import os
import time
import pandas as pd
import numpy as np
from datetime import datetime
import unittest

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from cache import CacheManager, CacheConfig, CacheStrategy


class CachePerformanceTest(unittest.TestCase):
    """缓存性能测试类"""
    
    def setUp(self):
        """测试前准备"""
        # 创建测试用的缓存管理器
        cache_config = {
            'memory_max_size': 1000,
            'memory_ttl': 3600,
            'file_max_size': 5000,
            'file_ttl': 7200,
            'cache_dir': './test_cache'
        }
        self.cache_manager = CacheManager(cache_config)
        
        # 创建测试数据
        self.test_data = self._create_test_datasets()
    
    def _create_test_datasets(self):
        """创建不同大小的测试数据集"""
        datasets = {}
        
        # 小数据集（模拟单只股票的除权除息数据）
        small_data = pd.DataFrame({
            'date': pd.date_range('2020-01-01', periods=10, freq='D'),
            'fenhong': np.random.uniform(0, 1, 10),
            'songzhuangu': np.random.uniform(0, 0.5, 10),
            'peigu': np.random.uniform(0, 0.2, 10),
            'peigujia': np.random.uniform(5, 15, 10)
        })
        datasets['small'] = small_data
        
        # 中等数据集（模拟多只股票的数据）
        medium_data = pd.DataFrame({
            'date': pd.date_range('2015-01-01', periods=1000, freq='D'),
            'stock_code': np.random.choice(['000001', '000002', '600000', '600036'], 1000),
            'close': np.random.uniform(5, 50, 1000),
            'volume': np.random.randint(1000, 100000, 1000),
            'amount': np.random.uniform(10000, 1000000, 1000)
        })
        datasets['medium'] = medium_data
        
        # 大数据集（模拟分钟级数据）
        large_data = pd.DataFrame({
            'datetime': pd.date_range('2024-01-01 09:30:00', periods=10000, freq='1min'),
            'open': np.random.uniform(10, 20, 10000),
            'high': np.random.uniform(10, 20, 10000),
            'low': np.random.uniform(10, 20, 10000),
            'close': np.random.uniform(10, 20, 10000),
            'volume': np.random.randint(100, 10000, 10000)
        })
        datasets['large'] = large_data
        
        return datasets
    
    def test_memory_cache_performance(self):
        """测试内存缓存性能"""
        print("\n🧪 测试内存缓存性能...")
        
        # 写入性能测试
        write_times = []
        for size, data in self.test_data.items():
            start_time = time.time()
            key = f"test_memory_{size}"
            success = self.cache_manager.put_dataframe(key, data)
            write_time = time.time() - start_time
            write_times.append(write_time)
            
            self.assertTrue(success, f"内存缓存写入失败: {size}")
            print(f"   {size}数据集写入耗时: {write_time*1000:.2f}ms")
        
        # 读取性能测试
        read_times = []
        for size, _ in self.test_data.items():
            key = f"test_memory_{size}"
            start_time = time.time()
            result = self.cache_manager.get_dataframe(key)
            read_time = time.time() - start_time
            read_times.append(read_time)
            
            self.assertIsNotNone(result, f"内存缓存读取失败: {size}")
            print(f"   {size}数据集读取耗时: {read_time*1000:.2f}ms")
        
        avg_write_time = np.mean(write_times) * 1000
        avg_read_time = np.mean(read_times) * 1000
        
        print(f"✅ 内存缓存平均写入时间: {avg_write_time:.2f}ms")
        print(f"✅ 内存缓存平均读取时间: {avg_read_time:.2f}ms")
        
        # 性能断言
        self.assertLess(avg_write_time, 100, "内存缓存写入时间应小于100ms")
        self.assertLess(avg_read_time, 10, "内存缓存读取时间应小于10ms")
    
    def test_file_cache_performance(self):
        """测试文件缓存性能"""
        print("\n🧪 测试文件缓存性能...")
        
        # 清空内存缓存，强制使用文件缓存
        self.cache_manager.memory_cache.clear()
        
        # 写入性能测试
        write_times = []
        for size, data in self.test_data.items():
            start_time = time.time()
            key = f"test_file_{size}"
            success = self.cache_manager.file_cache.put_dataframe(key, data)
            write_time = time.time() - start_time
            write_times.append(write_time)
            
            self.assertTrue(success, f"文件缓存写入失败: {size}")
            print(f"   {size}数据集写入耗时: {write_time*1000:.2f}ms")
        
        # 读取性能测试
        read_times = []
        for size, _ in self.test_data.items():
            key = f"test_file_{size}"
            start_time = time.time()
            result = self.cache_manager.file_cache.get_dataframe(key)
            read_time = time.time() - start_time
            read_times.append(read_time)
            
            self.assertIsNotNone(result, f"文件缓存读取失败: {size}")
            print(f"   {size}数据集读取耗时: {read_time*1000:.2f}ms")
        
        avg_write_time = np.mean(write_times) * 1000
        avg_read_time = np.mean(read_times) * 1000
        
        print(f"✅ 文件缓存平均写入时间: {avg_write_time:.2f}ms")
        print(f"✅ 文件缓存平均读取时间: {avg_read_time:.2f}ms")
        
        # 性能断言（文件缓存相对较慢）
        self.assertLess(avg_write_time, 1000, "文件缓存写入时间应小于1000ms")
        self.assertLess(avg_read_time, 500, "文件缓存读取时间应小于500ms")
    
    def test_cache_hit_rate(self):
        """测试缓存命中率"""
        print("\n🧪 测试缓存命中率...")
        
        # 存储测试数据
        test_keys = []
        for i, (size, data) in enumerate(self.test_data.items()):
            key = f"hit_rate_test_{i}_{size}"
            test_keys.append(key)
            self.cache_manager.put_dataframe(key, data)
        
        # 多次访问相同数据
        access_count = 100
        for _ in range(access_count):
            for key in test_keys:
                result = self.cache_manager.get_dataframe(key)
                self.assertIsNotNone(result, f"缓存命中失败: {key}")
        
        # 获取统计信息
        stats = self.cache_manager.get_cache_stats()
        global_stats = stats['global_stats']
        
        hit_rate = global_stats['hit_rate']
        total_requests = global_stats['total_requests']
        
        print(f"   总请求数: {total_requests}")
        print(f"   命中次数: {global_stats['hits']}")
        print(f"   未命中次数: {global_stats['misses']}")
        print(f"✅ 缓存命中率: {hit_rate:.2%}")
        
        # 命中率应该很高（除了第一次访问）
        expected_hit_rate = (access_count * len(test_keys) - len(test_keys)) / (access_count * len(test_keys))
        self.assertGreaterEqual(hit_rate, expected_hit_rate * 0.9, "缓存命中率应该足够高")
    
    def test_multi_level_cache_performance(self):
        """测试多级缓存性能"""
        print("\n🧪 测试多级缓存性能...")
        
        # 测试缓存提升机制
        test_data = self.test_data['medium']
        key = "multi_level_test"
        
        # 1. 直接存储到文件缓存
        self.cache_manager.file_cache.put_dataframe(key, test_data)
        
        # 2. 第一次访问（从文件缓存读取，应该提升到内存缓存）
        start_time = time.time()
        result1 = self.cache_manager.get_dataframe(key)
        first_access_time = time.time() - start_time
        
        self.assertIsNotNone(result1, "第一次访问失败")
        
        # 3. 第二次访问（应该从内存缓存读取，更快）
        start_time = time.time()
        result2 = self.cache_manager.get_dataframe(key)
        second_access_time = time.time() - start_time
        
        self.assertIsNotNone(result2, "第二次访问失败")
        
        print(f"   第一次访问耗时: {first_access_time*1000:.2f}ms（文件缓存）")
        print(f"   第二次访问耗时: {second_access_time*1000:.2f}ms（内存缓存）")
        
        # 第二次访问应该明显更快
        self.assertLess(second_access_time, first_access_time, "缓存提升机制应该提高访问速度")

        # 避免除零错误
        if second_access_time > 0:
            speedup = first_access_time / second_access_time
            print(f"✅ 缓存提升效果: {speedup:.1f}x 加速")
        else:
            print(f"✅ 缓存提升效果: 极快（第二次访问时间 < 1ms）")
    
    def test_cache_health_monitoring(self):
        """测试缓存健康监控"""
        print("\n🧪 测试缓存健康监控...")
        
        # 执行一些缓存操作
        for i, (size, data) in enumerate(self.test_data.items()):
            key = f"health_test_{i}"
            self.cache_manager.put_dataframe(key, data)
            # 多次访问以提高命中率
            for _ in range(5):
                self.cache_manager.get_dataframe(key)
        
        # 获取健康状态
        health = self.cache_manager.get_cache_health()
        
        print(f"   健康评分: {health['health_score']}/100")
        print(f"   健康状态: {health['health_status']}")
        print(f"   命中率: {health['hit_rate']:.2%}")
        print(f"   平均响应时间: {health['avg_response_time_ms']:.2f}ms")
        
        if health['recommendations']:
            print("   优化建议:")
            for rec in health['recommendations']:
                print(f"     - {rec}")
        
        # 健康评分应该合理
        self.assertGreaterEqual(health['health_score'], 40, "缓存健康评分应该合理")
        self.assertIn(health['health_status'], ['优秀', '良好', '一般', '需要优化'], "健康状态应该有效")
    
    def tearDown(self):
        """测试后清理"""
        # 清理测试缓存
        self.cache_manager.clear()
        
        # 删除测试缓存目录
        import shutil
        test_cache_dir = './test_cache'
        if os.path.exists(test_cache_dir):
            shutil.rmtree(test_cache_dir)


def run_cache_performance_tests():
    """运行缓存性能测试"""
    print("🚀 开始缓存性能测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(CachePerformanceTest)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    if result.wasSuccessful():
        print("🎉 所有缓存性能测试通过！")
        print("✨ 统一缓存管理器性能良好")
        return True
    else:
        print("❌ 部分性能测试失败")
        print("🔧 请检查缓存实现和配置")
        return False


if __name__ == '__main__':
    success = run_cache_performance_tests()
    sys.exit(0 if success else 1)
