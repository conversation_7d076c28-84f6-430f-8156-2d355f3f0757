{"timestamp": "2025-08-05T21:29:50.874361", "error_statistics": {"error_counts": {"BUSINESS": 1}, "total_errors": 1, "recent_errors": [{"error_id": "BUSINESS_1754400590873", "timestamp": "2025-08-05T21:29:50.873400", "category": "BUSINESS", "error_type": "TypeError", "error_message": "log_error() got an unexpected keyword argument 'context'", "stock_code": null, "operation": "任务执行", "context": {"task_name": "分钟级数据生成"}, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\src\\mythquant\\core\\task_manager.py\", line 241, in execute_task\n    result = self._execute_internet_minute_task(task, target_stocks)\nAttributeError: 'TaskManager' object has no attribute '_execute_internet_minute_task'\n\nDuring handling of the above exception, another exception occurred:\n\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\src\\mythquant\\core\\application.py\", line 196, in run_all_tasks\n    success = self.task_manager.execute_task(task)\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\src\\mythquant\\core\\task_manager.py\", line 255, in execute_task\n    error_id = self.error_handler.log_error(\nTypeError: log_error() got an unexpected keyword argument 'context'\n"}], "error_history_size": 1}, "performance_statistics": {}}