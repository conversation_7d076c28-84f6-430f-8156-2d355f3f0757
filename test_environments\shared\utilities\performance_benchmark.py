#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 性能监控和基准测试系统
建立性能基准，为后续优化提供数据支持，实现系统性能的持续监控
"""

import sys
import os
import time
import psutil
import json
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Any, Optional, Tuple
import logging
import subprocess
import threading
from collections import defaultdict
# 可选依赖：matplotlib和seaborn用于图表生成
try:
    import matplotlib.pyplot as plt
    import seaborn as sns
    PLOTTING_AVAILABLE = True
except ImportError:
    PLOTTING_AVAILABLE = False

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class SystemMonitor:
    """系统资源监控器"""
    
    def __init__(self, interval: float = 1.0):
        self.interval = interval
        self.monitoring = False
        self.data = []
        self.thread = None
    
    def start_monitoring(self):
        """开始监控"""
        self.monitoring = True
        self.data = []
        self.thread = threading.Thread(target=self._monitor_loop)
        self.thread.daemon = True
        self.thread.start()
        logger.info("🔍 系统监控已启动")
    
    def stop_monitoring(self):
        """停止监控"""
        self.monitoring = False
        if self.thread:
            self.thread.join()
        logger.info("🔍 系统监控已停止")
    
    def _monitor_loop(self):
        """监控循环"""
        while self.monitoring:
            try:
                # 获取系统资源信息
                cpu_percent = psutil.cpu_percent()
                memory = psutil.virtual_memory()
                disk_io = psutil.disk_io_counters()
                
                # 获取当前进程信息
                process = psutil.Process()
                process_memory = process.memory_info()
                process_cpu = process.cpu_percent()
                
                data_point = {
                    'timestamp': datetime.now(),
                    'system_cpu_percent': cpu_percent,
                    'system_memory_percent': memory.percent,
                    'system_memory_available_gb': memory.available / (1024**3),
                    'process_memory_rss_mb': process_memory.rss / (1024**2),
                    'process_memory_vms_mb': process_memory.vms / (1024**2),
                    'process_cpu_percent': process_cpu,
                    'disk_read_bytes': disk_io.read_bytes if disk_io else 0,
                    'disk_write_bytes': disk_io.write_bytes if disk_io else 0
                }
                
                self.data.append(data_point)
                
            except Exception as e:
                logger.error(f"监控数据收集失败: {e}")
            
            time.sleep(self.interval)
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取监控统计信息"""
        if not self.data:
            return {}
        
        df = pd.DataFrame(self.data)
        
        stats = {
            'duration_seconds': (df['timestamp'].iloc[-1] - df['timestamp'].iloc[0]).total_seconds(),
            'data_points': len(df),
            'system_cpu': {
                'avg': df['system_cpu_percent'].mean(),
                'max': df['system_cpu_percent'].max(),
                'min': df['system_cpu_percent'].min()
            },
            'system_memory': {
                'avg_percent': df['system_memory_percent'].mean(),
                'max_percent': df['system_memory_percent'].max(),
                'min_available_gb': df['system_memory_available_gb'].min()
            },
            'process_memory': {
                'avg_rss_mb': df['process_memory_rss_mb'].mean(),
                'max_rss_mb': df['process_memory_rss_mb'].max(),
                'peak_vms_mb': df['process_memory_vms_mb'].max()
            },
            'process_cpu': {
                'avg': df['process_cpu_percent'].mean(),
                'max': df['process_cpu_percent'].max()
            }
        }
        
        return stats


class PerformanceBenchmark:
    """性能基准测试器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.main_script = os.path.join(project_root, "main_v20230219_optimized.py")
        self.results_dir = os.path.join(project_root, "benchmark_results")
        self.monitor = SystemMonitor(interval=0.5)
        
        # 确保结果目录存在
        os.makedirs(self.results_dir, exist_ok=True)
    
    def run_single_benchmark(self, test_name: str, description: str = "") -> Dict[str, Any]:
        """运行单次基准测试"""
        logger.info(f"🚀 开始基准测试: {test_name}")
        
        # 开始系统监控
        self.monitor.start_monitoring()
        
        start_time = time.time()
        start_datetime = datetime.now()
        
        try:
            # 运行主程序
            result = subprocess.run(
                [sys.executable, self.main_script],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300  # 5分钟超时
            )
            
            end_time = time.time()
            end_datetime = datetime.now()
            execution_time = end_time - start_time
            
            # 停止监控
            self.monitor.stop_monitoring()
            
            # 获取监控统计
            monitor_stats = self.monitor.get_statistics()
            
            # 验证输出文件
            output_verification = self._verify_output_files()
            
            benchmark_result = {
                'test_name': test_name,
                'description': description,
                'timestamp': start_datetime.isoformat(),
                'execution_time': execution_time,
                'return_code': result.returncode,
                'success': result.returncode == 0,
                'stdout_lines': len(result.stdout.split('\n')) if result.stdout else 0,
                'stderr_lines': len(result.stderr.split('\n')) if result.stderr else 0,
                'output_verification': output_verification,
                'system_monitoring': monitor_stats,
                'environment': self._get_environment_info()
            }
            
            if result.returncode == 0:
                logger.info(f"✅ 基准测试完成: {test_name} - {execution_time:.2f}秒")
            else:
                logger.error(f"❌ 基准测试失败: {test_name} - 返回码: {result.returncode}")
                if result.stderr:
                    logger.error(f"错误信息: {result.stderr[:500]}...")
            
            return benchmark_result
            
        except subprocess.TimeoutExpired:
            self.monitor.stop_monitoring()
            logger.error(f"❌ 基准测试超时: {test_name}")
            return {
                'test_name': test_name,
                'description': description,
                'timestamp': start_datetime.isoformat(),
                'execution_time': 300,
                'success': False,
                'timeout': True,
                'system_monitoring': self.monitor.get_statistics(),
                'environment': self._get_environment_info()
            }
        
        except Exception as e:
            self.monitor.stop_monitoring()
            logger.error(f"❌ 基准测试异常: {test_name} - {e}")
            return {
                'test_name': test_name,
                'description': description,
                'timestamp': start_datetime.isoformat(),
                'execution_time': 0,
                'success': False,
                'error': str(e),
                'environment': self._get_environment_info()
            }
    
    def run_multiple_benchmarks(self, count: int = 3, interval: int = 30) -> List[Dict[str, Any]]:
        """运行多次基准测试"""
        logger.info(f"🔄 开始运行 {count} 次基准测试，间隔 {interval} 秒")
        
        results = []
        
        for i in range(count):
            test_name = f"基准测试_{i+1}"
            description = f"第{i+1}次基准测试，共{count}次"
            
            result = self.run_single_benchmark(test_name, description)
            results.append(result)
            
            # 如果不是最后一次测试，等待间隔时间
            if i < count - 1:
                logger.info(f"⏳ 等待 {interval} 秒后进行下一次测试...")
                time.sleep(interval)
        
        return results
    
    def _verify_output_files(self) -> Dict[str, Any]:
        """验证输出文件"""
        output_dir = "H:/MPV1.17/T0002/signals/"
        expected_files = ["day_0_000617_20150101-20250731.txt"]
        
        verification = {
            'files_checked': len(expected_files),
            'files_found': 0,
            'files_valid': 0,
            'file_details': {}
        }
        
        for file_name in expected_files:
            file_path = os.path.join(output_dir, file_name)
            
            if os.path.exists(file_path):
                verification['files_found'] += 1
                
                try:
                    file_stats = os.stat(file_path)
                    file_size = file_stats.st_size
                    
                    # 简单验证文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    line_count = len(lines)
                    header_correct = len(lines) > 0 and "股票编码|时间|买卖差" in lines[0]
                    
                    if header_correct and line_count > 1:
                        verification['files_valid'] += 1
                    
                    verification['file_details'][file_name] = {
                        'exists': True,
                        'size_bytes': file_size,
                        'line_count': line_count,
                        'header_correct': header_correct,
                        'valid': header_correct and line_count > 1
                    }
                    
                except Exception as e:
                    verification['file_details'][file_name] = {
                        'exists': True,
                        'error': str(e),
                        'valid': False
                    }
            else:
                verification['file_details'][file_name] = {
                    'exists': False,
                    'valid': False
                }
        
        return verification
    
    def _get_environment_info(self) -> Dict[str, Any]:
        """获取环境信息"""
        try:
            return {
                'python_version': sys.version,
                'platform': sys.platform,
                'cpu_count': psutil.cpu_count(),
                'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                'disk_usage': {
                    'total_gb': psutil.disk_usage('/').total / (1024**3),
                    'free_gb': psutil.disk_usage('/').free / (1024**3)
                }
            }
        except Exception as e:
            return {'error': str(e)}
    
    def analyze_results(self, results: List[Dict[str, Any]]) -> Dict[str, Any]:
        """分析基准测试结果"""
        if not results:
            return {}
        
        successful_results = [r for r in results if r.get('success', False)]
        
        if not successful_results:
            return {
                'total_tests': len(results),
                'successful_tests': 0,
                'success_rate': 0.0,
                'analysis': '所有测试都失败了'
            }
        
        execution_times = [r['execution_time'] for r in successful_results]
        
        analysis = {
            'total_tests': len(results),
            'successful_tests': len(successful_results),
            'success_rate': len(successful_results) / len(results),
            'execution_time': {
                'avg': np.mean(execution_times),
                'min': np.min(execution_times),
                'max': np.max(execution_times),
                'std': np.std(execution_times),
                'median': np.median(execution_times)
            },
            'performance_stability': {
                'coefficient_of_variation': np.std(execution_times) / np.mean(execution_times),
                'performance_consistent': np.std(execution_times) / np.mean(execution_times) < 0.1
            }
        }
        
        # 分析系统资源使用
        if successful_results[0].get('system_monitoring'):
            memory_usage = []
            cpu_usage = []
            
            for result in successful_results:
                monitor_stats = result.get('system_monitoring', {})
                if monitor_stats.get('process_memory'):
                    memory_usage.append(monitor_stats['process_memory']['max_rss_mb'])
                if monitor_stats.get('process_cpu'):
                    cpu_usage.append(monitor_stats['process_cpu']['avg'])
            
            if memory_usage:
                analysis['resource_usage'] = {
                    'memory_mb': {
                        'avg': np.mean(memory_usage),
                        'max': np.max(memory_usage),
                        'min': np.min(memory_usage)
                    }
                }
            
            if cpu_usage:
                analysis['resource_usage'] = analysis.get('resource_usage', {})
                analysis['resource_usage']['cpu_percent'] = {
                    'avg': np.mean(cpu_usage),
                    'max': np.max(cpu_usage)
                }
        
        return analysis
    
    def save_results(self, results: List[Dict[str, Any]], analysis: Dict[str, Any]):
        """保存基准测试结果"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # 保存详细结果
        results_file = os.path.join(self.results_dir, f"benchmark_results_{timestamp}.json")
        with open(results_file, 'w', encoding='utf-8') as f:
            json.dump({
                'timestamp': timestamp,
                'results': results,
                'analysis': analysis
            }, f, indent=2, ensure_ascii=False, default=str)
        
        # 保存简化报告
        report_file = os.path.join(self.results_dir, f"benchmark_report_{timestamp}.txt")
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write(f"MythQuant 性能基准测试报告\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write("=" * 60 + "\n\n")
            
            f.write(f"测试概况:\n")
            f.write(f"  总测试次数: {analysis.get('total_tests', 0)}\n")
            f.write(f"  成功次数: {analysis.get('successful_tests', 0)}\n")
            f.write(f"  成功率: {analysis.get('success_rate', 0):.1%}\n\n")
            
            if 'execution_time' in analysis:
                et = analysis['execution_time']
                f.write(f"执行时间统计:\n")
                f.write(f"  平均时间: {et['avg']:.2f} 秒\n")
                f.write(f"  最短时间: {et['min']:.2f} 秒\n")
                f.write(f"  最长时间: {et['max']:.2f} 秒\n")
                f.write(f"  标准差: {et['std']:.2f} 秒\n")
                f.write(f"  中位数: {et['median']:.2f} 秒\n\n")
            
            if 'performance_stability' in analysis:
                ps = analysis['performance_stability']
                f.write(f"性能稳定性:\n")
                f.write(f"  变异系数: {ps['coefficient_of_variation']:.3f}\n")
                f.write(f"  性能稳定: {'是' if ps['performance_consistent'] else '否'}\n\n")
            
            if 'resource_usage' in analysis:
                ru = analysis['resource_usage']
                if 'memory_mb' in ru:
                    f.write(f"内存使用:\n")
                    f.write(f"  平均: {ru['memory_mb']['avg']:.1f} MB\n")
                    f.write(f"  峰值: {ru['memory_mb']['max']:.1f} MB\n\n")
        
        logger.info(f"📊 基准测试结果已保存:")
        logger.info(f"   详细结果: {results_file}")
        logger.info(f"   测试报告: {report_file}")
        
        return results_file, report_file


def main():
    """主函数"""
    print("🚀 MythQuant 性能监控和基准测试系统")
    print("=" * 60)
    
    # 初始化基准测试器
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    benchmark = PerformanceBenchmark(project_root)
    
    # 运行基准测试
    print("📊 开始运行基准测试...")
    results = benchmark.run_multiple_benchmarks(count=3, interval=10)
    
    # 分析结果
    print("📈 分析测试结果...")
    analysis = benchmark.analyze_results(results)
    
    # 保存结果
    print("💾 保存测试结果...")
    results_file, report_file = benchmark.save_results(results, analysis)
    
    # 输出摘要
    print("\n📋 基准测试摘要:")
    print(f"   总测试次数: {analysis.get('total_tests', 0)}")
    print(f"   成功次数: {analysis.get('successful_tests', 0)}")
    print(f"   成功率: {analysis.get('success_rate', 0):.1%}")
    
    if 'execution_time' in analysis:
        et = analysis['execution_time']
        print(f"   平均执行时间: {et['avg']:.2f} 秒")
        print(f"   性能稳定性: {'良好' if analysis.get('performance_stability', {}).get('performance_consistent', False) else '需要关注'}")
    
    print(f"\n📊 详细报告: {report_file}")
    
    return analysis.get('success_rate', 0) > 0.8


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        sys.exit(1)
