# 知识库检索使用指南

## 🎯 指南目标

本指南旨在帮助AI助手高效、准确地检索和使用项目知识库，提升问题解决效率和知识利用率。

## 📚 知识库分类与检索策略

### **1. 技术问题知识库**

#### **主要知识库**
- `docs/knowledge/troubleshooting/debugging_knowledge_base.md` - 调试经验和技术问题解决方案
- `docs/knowledge/faq_knowledge_base.md` - 常见问题和快速解答
- `docs/troubleshooting/*.md` - 专项技术问题分析文档

#### **检索触发条件**
- **错误关键词**: KeyError, AttributeError, ImportError, 异常, 错误, bug
- **技术术语**: pandas, DataFrame, 多线程, 工作流, 6步法
- **问题描述**: 数据处理失败, 函数调用错误, 模块导入问题

#### **检索策略**
1. **精确匹配**: 使用具体错误信息作为搜索关键词
2. **模式匹配**: 识别错误类型和问题模式
3. **历史经验**: 优先检索相似问题的历史解决方案

### **2. 工作流设计知识库**

#### **主要知识库**
- `docs/troubleshooting/workflow_atomicity_issue_analysis.md` - 工作流原子性问题
- `docs/implementation/*_report.md` - 工作流实施经验
- `docs/workflow/*.md` - 工作流设计规范

#### **检索触发条件**
- **工作流关键词**: 6步法, 工作流, 流程, 步骤, 原子性
- **设计问题**: 职责混乱, 嵌套调用, 多线程问题
- **用户体验**: 输出混乱, 时序问题, 错误处理

#### **检索策略**
1. **问题分类**: 识别是设计问题还是实施问题
2. **经验匹配**: 查找类似的工作流问题和解决方案
3. **最佳实践**: 参考成功的工作流实施经验

### **3. 架构设计知识库**

#### **主要知识库**
- `docs/implementation/rules_*_report.md` - 架构实施报告
- `docs/architecture/*.md` - 架构设计文档
- `docs/knowledge/ddd_*.md` - DDD架构相关知识

#### **检索触发条件**
- **架构关键词**: DDD, 架构, 设计, 模块, 分层, 依赖
- **设计模式**: 门面模式, 适配器模式, 依赖倒置
- **架构问题**: 耦合度, 职责分离, 向后兼容

#### **检索策略**
1. **架构层次**: 根据问题所在的架构层次检索相关知识
2. **设计原则**: 参考DDD设计原则和最佳实践
3. **实施经验**: 查找类似架构问题的实施经验

### **4. 质量规范知识库**

#### **主要知识库**
- `docs/knowledge/testing_quality_rules.md` - 测试质量规范
- `docs/quality/*.md` - 代码质量标准
- `docs/standards/*.md` - 项目规范文档

#### **检索触发条件**
- **质量关键词**: 测试, 验证, 质量, 规范, 标准
- **代码问题**: 代码重复, 命名规范, 性能问题
- **流程问题**: 测试流程, 质量保证, 代码审查

## 🔍 检索最佳实践

### **多策略检索方法**

#### **1. 关键词检索**
```
步骤1: 提取问题中的关键技术术语
步骤2: 使用关键词在相关知识库中搜索
步骤3: 根据匹配度排序检索结果
```

#### **2. 问题模式检索**
```
步骤1: 识别问题的类型和模式
步骤2: 在FAQ和历史问题中查找相似模式
步骤3: 参考相似问题的解决方案
```

#### **3. 交叉引用检索**
```
步骤1: 从一个知识点开始检索
步骤2: 查找相关的交叉引用和关联知识
步骤3: 构建完整的知识网络
```

### **检索结果应用指南**

#### **1. 结果评估**
- **相关性评估**: 检索结果与当前问题的匹配度
- **时效性评估**: 知识库内容的更新时间和有效性
- **完整性评估**: 是否需要检索更多相关知识

#### **2. 知识整合**
- **多源整合**: 整合多个知识库的相关信息
- **经验提取**: 从历史经验中提取可应用的解决方案
- **最佳实践**: 结合最佳实践形成完整的解决方案

#### **3. 应用反馈**
- **效果评估**: 评估知识库内容的实际应用效果
- **更新建议**: 基于应用结果提出知识库更新建议
- **经验沉淀**: 将新的经验和解决方案沉淀到知识库

## 📊 检索效果优化

### **检索质量指标**
- **命中率**: 检索到相关知识的比例
- **准确率**: 检索结果的准确性和适用性
- **完整性**: 检索结果的完整性和全面性
- **时效性**: 检索到的知识的时效性和有效性

### **持续优化策略**
- **使用统计**: 跟踪知识库的使用频率和效果
- **反馈收集**: 收集知识库使用的反馈和建议
- **内容更新**: 基于使用反馈持续更新知识库内容
- **结构优化**: 优化知识库的组织结构和检索路径

## 🔄 知识库维护流程

### **定期维护任务**
1. **内容审查**: 定期审查知识库内容的准确性
2. **时效性检查**: 检查知识库内容是否过时
3. **交叉引用更新**: 维护知识点之间的交叉引用关系
4. **使用统计分析**: 分析知识库的使用情况和效果

### **更新触发条件**
- 解决新类型问题时
- 发现现有知识库内容不准确时
- 积累足够的新经验时
- 用户反馈知识库需要改进时

---

**文档创建时间**: 2025-08-12  
**适用范围**: AI助手知识库检索和使用  
**维护责任**: 项目团队和AI助手协作维护
