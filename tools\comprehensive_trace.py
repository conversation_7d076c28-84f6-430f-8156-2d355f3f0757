#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Comprehensive trace to find ALL MissingDataProcessor calls
Install patches at the earliest possible moment
"""

import os
import sys
import importlib.util

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# Global counter for calls
call_counter = 0

def install_early_patch():
    """Install patch as early as possible"""
    global call_counter
    
    try:
        # Import the module directly
        spec = importlib.util.spec_from_file_location(
            "missing_data_processor", 
            os.path.join(project_root, "utils", "missing_data_processor.py")
        )
        missing_data_module = importlib.util.module_from_spec(spec)
        spec.loader.exec_module(missing_data_module)
        
        # Get the class
        MissingDataProcessor = missing_data_module.MissingDataProcessor
        
        # Save original methods
        original_init = MissingDataProcessor.__init__
        original_detect = MissingDataProcessor.detect_missing_minute_data
        
        def patched_init(self):
            """Patched __init__ method"""
            import traceback
            print(f"\n=== MissingDataProcessor.__init__ CALLED ===")
            print("Init call stack:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-8:], 1):
                if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'main', 'application']):
                    print(f">>> {i:2d}. {frame.strip()}")
                else:
                    print(f"    {i:2d}. {frame.strip()}")
            print("=" * 80)
            
            return original_init(self)
        
        def patched_detect(self, file_path, stock_code, silent=True):
            """Patched detect method"""
            global call_counter
            call_counter += 1
            
            import traceback
            
            print(f"\n=== MissingDataProcessor.detect_missing_minute_data CALLED #{call_counter} ===")
            print(f"File: {file_path}")
            print(f"Stock: {stock_code}")
            print(f"Silent: {silent}")
            
            print(f"\nCall stack:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack, 1):
                if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'main', 'application', 'structured_internet']):
                    print(f">>> {i:2d}. {frame.strip()}")
                else:
                    print(f"    {i:2d}. {frame.strip()}")
            
            print("=" * 100)
            
            return original_detect(self, file_path, stock_code, silent)
        
        # Replace methods
        MissingDataProcessor.__init__ = patched_init
        MissingDataProcessor.detect_missing_minute_data = patched_detect
        
        # Also patch the module in sys.modules if it exists
        if 'utils.missing_data_processor' in sys.modules:
            sys.modules['utils.missing_data_processor'].MissingDataProcessor = MissingDataProcessor
        
        print("Early MissingDataProcessor patch installed successfully")
        return True
        
    except Exception as e:
        print(f"Early patch installation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def install_import_hook():
    """Install import hook to catch module imports"""
    original_import = __builtins__.__import__
    
    def patched_import(name, globals=None, locals=None, fromlist=(), level=0):
        """Patched import function"""
        if 'missing_data_processor' in name:
            print(f"\n!!! IMPORTING missing_data_processor: {name} !!!")
            import traceback
            print("Import call stack:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-6:], 1):
                print(f"  {i:2d}. {frame.strip()}")
            print("=" * 60)
        
        return original_import(name, globals, locals, fromlist, level)
    
    __builtins__.__import__ = patched_import
    print("Import hook installed")

def run_main_with_comprehensive_trace():
    """Run main with comprehensive tracing"""
    print("\nRunning main.py with comprehensive tracing...")
    
    try:
        # Import and run main
        import main
        result = main.main()
        
        print(f"\nMain completed with result: {result}")
        print(f"Total MissingDataProcessor calls detected: {call_counter}")
        
        return True
        
    except Exception as e:
        print(f"Main execution failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Comprehensive MissingDataProcessor Tracer")
    print("=" * 80)
    
    # Install import hook first
    install_import_hook()
    
    # Install early patch
    if not install_early_patch():
        return 1
    
    # Run main
    success = run_main_with_comprehensive_trace()
    
    print(f"\nComprehensive trace completed: {'SUCCESS' if success else 'FAILED'}")
    print(f"Total calls detected: {call_counter}")
    
    if call_counter > 1:
        print(f"WARNING: Multiple calls detected! This indicates workflow violation.")
    elif call_counter == 1:
        print(f"INFO: Single call detected, likely normal workflow.")
    else:
        print(f"INFO: No calls detected, patch may not be working.")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
