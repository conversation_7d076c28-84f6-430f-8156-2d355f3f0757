#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置门面

为外部提供简化的配置访问接口，隐藏DDD架构的复杂性
"""

from typing import List, Dict, Any, Optional
from src.mythquant.application.config.services.config_application_service import ConfigApplicationService
from src.mythquant.application.config.dto.config_dto import TradingConfigDto, DataSourceConfigDto, ProcessingConfigDto, TaskConfigDto
from src.mythquant.infrastructure.config.repositories.file_config_repository import FileConfigRepository
from src.mythquant.infrastructure.config.adapters.user_config_adapter import UserConfigAdapter
from src.mythquant.domain.config.services.config_validation_service import ValidationResult


class ConfigFacade:
    """配置门面 - 为外部提供简化的配置访问接口"""
    
    def __init__(self):
        """初始化配置门面"""
        # 依赖注入 - 构建完整的DDD架构
        self._user_config_adapter = UserConfigAdapter()
        self._config_repository = FileConfigRepository(self._user_config_adapter)
        self._config_service = ConfigApplicationService(self._config_repository)
    
    # ==================== 便捷访问方法（向后兼容） ====================
    
    def get_tdx_path(self) -> str:
        """获取TDX路径"""
        return self._config_service.get_tdx_path()
    
    def get_output_path(self) -> str:
        """获取输出路径"""
        return self._config_service.get_output_path()
    
    def is_smart_file_selector_enabled(self) -> bool:
        """检查智能文件选择器是否启用"""
        return self._config_service.is_smart_file_selector_enabled()
    
    def is_debug_enabled(self) -> bool:
        """检查调试模式是否启用"""
        return self._config_service.is_debug_enabled()
    
    def is_verbose_mode_enabled(self) -> bool:
        """检查详细模式是否启用"""
        return self._config_service.is_verbose_mode_enabled()
    
    def get_pytdx_connection_info(self) -> Dict[str, Any]:
        """获取pytdx连接信息"""
        return self._config_service.get_pytdx_connection_info()

    def get_internet_data_config(self) -> Dict[str, Any]:
        """获取互联网数据配置（向后兼容）"""
        try:
            # 从user_config.py获取互联网数据配置
            import user_config
            if hasattr(user_config, 'internet_data_config'):
                return user_config.internet_data_config
            else:
                # 返回默认配置
                return {
                    'enabled': True,
                    'data_sources': {
                        'pytdx': {'enabled': True, 'priority': 1, 'auto_server_discovery': True},
                        'akshare': {'enabled': True, 'priority': 2},
                        'baostock': {'enabled': True, 'priority': 3},
                        'yfinance': {'enabled': False, 'priority': 4}
                    },
                    'download_params': {
                        'start_date': '20150101',
                        'end_date': 'current',
                        'request_delay': 0.5,
                        'max_retries': 3,
                        'timeout': 30
                    },
                    'output': {
                        'directory': './output',
                        'filename_suffix': '_来源互联网',
                        'encoding': 'utf-8'
                    }
                }
        except Exception as e:
            # 返回默认配置
            return {
                'enabled': True,
                'data_sources': {
                    'pytdx': {'enabled': True, 'priority': 1},
                    'akshare': {'enabled': True, 'priority': 2}
                },
                'download_params': {
                    'start_date': '20150101',
                    'end_date': 'current'
                }
            }
    
    # ==================== 任务配置相关 ====================
    
    def get_task_configs(self) -> List[Dict[str, Any]]:
        """获取任务配置列表"""
        task_dtos = self._config_service.get_task_configs()
        return [dto.to_dict() for dto in task_dtos]
    
    def get_minute_task_configs(self) -> List[Dict[str, Any]]:
        """获取分钟级任务配置"""
        task_dtos = self._config_service.get_minute_task_configs()
        return [dto.to_dict() for dto in task_dtos]
    
    def get_daily_task_configs(self) -> List[Dict[str, Any]]:
        """获取日级任务配置"""
        task_dtos = self._config_service.get_daily_task_configs()
        return [dto.to_dict() for dto in task_dtos]
    
    # ==================== 详细配置访问 ====================
    
    def get_trading_config(self) -> Dict[str, Any]:
        """获取交易配置"""
        dto = self._config_service.get_trading_config()
        return {
            'tdx_path': dto.tdx_path,
            'tdx_ip': dto.tdx_ip,
            'tdx_port': dto.tdx_port,
            'output_path': dto.output_path,
            'target_stocks_file': dto.target_stocks_file,
            'default_stocks': dto.default_stocks,
            'gbbq_path': dto.gbbq_path,
            'pytdx_auto_detect': dto.pytdx_auto_detect,
            'pytdx_show_top5': dto.pytdx_show_top5,
            'pytdx_smart_detect': dto.pytdx_smart_detect,
            'pytdx_blacklist_enabled': dto.pytdx_blacklist_enabled,
            'pytdx_blacklist_timeout': dto.pytdx_blacklist_timeout,
            'kline_limits': dto.kline_limits,
            'data_buffer_factor': dto.data_buffer_factor
        }
    
    def get_data_source_config(self) -> Dict[str, Any]:
        """获取数据源配置"""
        dto = self._config_service.get_data_source_config()
        return {
            'tdx_path': dto.tdx_path,
            'tdx_ip': dto.tdx_ip,
            'tdx_port': dto.tdx_port,
            'connection_timeout': dto.connection_timeout,
            'read_timeout': dto.read_timeout,
            'max_connections': dto.max_connections,
            'keep_alive': dto.keep_alive,
            'user_agent': dto.user_agent,
            'log_all_errors': dto.log_all_errors,
            'raise_on_critical': dto.raise_on_critical,
            'continue_on_partial_failure': dto.continue_on_partial_failure,
            'error_notification': dto.error_notification,
            'track_api_usage': dto.track_api_usage,
            'log_request_details': dto.log_request_details,
            'performance_metrics': dto.performance_metrics,
            'success_rate_threshold': dto.success_rate_threshold,
            'internet_sources_enabled': dto.internet_sources_enabled
        }
    
    def get_processing_config(self) -> Dict[str, Any]:
        """获取数据处理配置"""
        dto = self._config_service.get_processing_config()
        return {
            'smart_file_selector_enabled': dto.smart_file_selector_enabled,
            'file_selector_strategy': dto.file_selector_strategy,
            'freshness_weight': dto.freshness_weight,
            'coverage_weight': dto.coverage_weight,
            'match_weight': dto.match_weight,
            'auto_resolve_conflicts': dto.auto_resolve_conflicts,
            'prefer_newer_files': dto.prefer_newer_files,
            'min_score_threshold': dto.min_score_threshold,
            'transparent_processing_enabled': dto.transparent_processing_enabled,
            'auto_backup_enabled': dto.auto_backup_enabled,
            'validation_enabled': dto.validation_enabled,
            'quality_verification_enabled': dto.quality_verification_enabled,
            'strict_mode': dto.strict_mode,
            'auto_fix_enabled': dto.auto_fix_enabled,
            'incremental_download_enabled': dto.incremental_download_enabled,
            'auto_detect_incremental': dto.auto_detect_incremental,
            'incremental_validation_enabled': dto.incremental_validation_enabled,
            'missing_data_processor_enabled': dto.missing_data_processor_enabled,
            'auto_fill_missing_data': dto.auto_fill_missing_data,
            'missing_data_notification_enabled': dto.missing_data_notification_enabled,
            'display_level': dto.display_level,
            'progress_bar_enabled': dto.progress_bar_enabled,
            'color_output_enabled': dto.color_output_enabled,
            'emoji_enabled': dto.emoji_enabled,
            'verbose_mode_enabled': dto.verbose_mode_enabled,
            'show_forward_adj_details': dto.show_forward_adj_details,
            'show_performance_warnings': dto.show_performance_warnings,
            'show_data_processing_steps': dto.show_data_processing_steps,
            'show_cache_status': dto.show_cache_status,
            'highlight_critical_info': dto.highlight_critical_info,
            'show_detailed_calculations': dto.show_detailed_calculations,
            'debug_enabled': dto.debug_enabled
        }
    
    # ==================== 配置验证和管理 ====================
    
    def validate_all_configs(self) -> Dict[str, Any]:
        """验证所有配置"""
        result = self._config_service.validate_all_configs()
        return {
            'is_valid': result.is_valid,
            'errors': result.errors,
            'warnings': result.warnings,
            'suggestions': result.suggestions
        }
    
    def validate_config_integrity(self) -> bool:
        """验证配置完整性"""
        return self._config_service.validate_config_integrity()
    
    def backup_config(self) -> str:
        """备份配置"""
        return self._config_service.backup_config()
    
    def restore_config(self, backup_path: str) -> bool:
        """恢复配置"""
        return self._config_service.restore_config(backup_path)
    
    def get_config_file_info(self) -> Dict[str, Any]:
        """获取配置文件信息"""
        return self._config_repository.get_config_file_info()
    
    def reload_config(self) -> None:
        """重新加载配置"""
        self._user_config_adapter.reload_config()
    
    # ==================== 向后兼容的方法 ====================
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值（向后兼容）"""
        try:
            # 解析键路径
            keys = key.split('.')
            
            if keys[0] == 'tdx':
                config = self.get_trading_config()
                if len(keys) == 2:
                    if keys[1] == 'tdx_path':
                        return config['tdx_path']
                    elif keys[1] == 'pytdx_ip':
                        return config['tdx_ip']
                    elif keys[1] == 'pytdx_port':
                        return config['tdx_port']
            
            elif keys[0] == 'output_config':
                config = self.get_trading_config()
                if len(keys) == 2 and keys[1] == 'base_output_path':
                    return config['output_path']
            
            elif keys[0] == 'smart_file_selector':
                config = self.get_processing_config()
                if len(keys) == 2 and keys[1] == 'enabled':
                    return config['smart_file_selector_enabled']
            
            elif keys[0] == 'debug':
                return self.is_debug_enabled()
            
            elif keys[0] == 'verbose_mode':
                config = self.get_processing_config()
                if len(keys) == 2 and keys[1] == 'enabled':
                    return config['verbose_mode_enabled']
            
            return default
            
        except Exception:
            return default
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值（向后兼容，但不实际保存）"""
        # 由于配置来源于user_config.py，这里不实现实际的设置功能
        # 只是为了向后兼容而提供接口
        raise NotImplementedError("配置设置功能未实现，请直接编辑user_config.py文件")
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置（向后兼容）"""
        return {
            'trading': self.get_trading_config(),
            'data_source': self.get_data_source_config(),
            'processing': self.get_processing_config(),
            'tasks': self.get_task_configs()
        }
