#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理模块 - DDD架构统一接口

提供符合DDD原则的配置管理功能，同时保持向后兼容性
"""

from typing import Dict, Any, List, Optional
from .manager import Confi<PERSON><PERSON><PERSON><PERSON> as LegacyConfigManager, config_manager as legacy_config_manager
from .validators import ConfigValidator, config_validator
from .user_settings import (
    DEBUG, VERBOSE_MODE, SMART_FILE_SELECTOR, TDX_CONFIG,
    OUTPUT_CONFIG, DATA_PROCESSING, INTELLIGENT_FEATURES,
    ERROR_HANDLING, USER_INTERFACE
)

# 延迟导入DDD架构组件，避免循环依赖
_config_facade = None

def get_config_facade():
    """获取配置门面（新的DDD架构）"""
    global _config_facade
    if _config_facade is None:
        from src.mythquant.interfaces.config.config_facade import ConfigFacade
        _config_facade = ConfigFacade()
    return _config_facade

def get_config_manager():
    """获取配置管理器（向后兼容接口）"""
    return LegacyConfigAdapter()

class LegacyConfigAdapter:
    """遗留配置管理器适配器 - 提供向后兼容性"""

    def __init__(self):
        self._facade = None

    def _get_facade(self):
        """延迟获取门面实例"""
        if self._facade is None:
            self._facade = get_config_facade()
        return self._facade

    def get_tdx_path(self) -> str:
        """获取TDX路径"""
        return self._get_facade().get_tdx_path()

    def get_output_path(self) -> str:
        """获取输出路径"""
        return self._get_facade().get_output_path()

    def is_smart_file_selector_enabled(self) -> bool:
        """检查智能文件选择器是否启用"""
        return self._get_facade().is_smart_file_selector_enabled()

    def is_debug_enabled(self) -> bool:
        """检查调试模式是否启用"""
        return self._get_facade().is_debug_enabled()

    def is_verbose_enabled(self) -> bool:
        """检查详细模式是否启用"""
        return self._get_facade().is_verbose_mode_enabled()

    def get_task_configs(self) -> List[Dict[str, Any]]:
        """获取任务配置列表"""
        return self._get_facade().get_task_configs()

    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        return self._get_facade().get(key, default)

    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        return self._get_facade().set(key, value)

    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._get_facade().get_config()

    def validate_config(self) -> bool:
        """验证配置"""
        return self._get_facade().validate_config_integrity()

    def get_internet_data_config(self) -> Dict[str, Any]:
        """获取互联网数据配置（向后兼容）"""
        return self._get_facade().get_internet_data_config()

# 创建全局实例（向后兼容）
config_manager = get_config_manager()

# 新的DDD架构便捷函数
def get_trading_config() -> Dict[str, Any]:
    """获取交易配置"""
    return get_config_facade().get_trading_config()

def get_data_source_config() -> Dict[str, Any]:
    """获取数据源配置"""
    return get_config_facade().get_data_source_config()

def get_processing_config() -> Dict[str, Any]:
    """获取数据处理配置"""
    return get_config_facade().get_processing_config()

def get_task_configs() -> List[Dict[str, Any]]:
    """获取任务配置"""
    return get_config_facade().get_task_configs()

def validate_all_configs() -> Dict[str, Any]:
    """验证所有配置"""
    return get_config_facade().validate_all_configs()

def backup_config() -> str:
    """备份配置"""
    return get_config_facade().backup_config()

def restore_config(backup_path: str) -> bool:
    """恢复配置"""
    return get_config_facade().restore_config(backup_path)

__all__ = [
    # 向后兼容接口
    'LegacyConfigManager',
    'ConfigValidator',
    'config_manager',
    'config_validator',
    'get_config_manager',

    # 新的DDD架构接口
    'get_config_facade',
    'get_trading_config',
    'get_data_source_config',
    'get_processing_config',
    'get_task_configs',
    'validate_all_configs',
    'backup_config',
    'restore_config',

    # 配置常量（向后兼容）
    'DEBUG',
    'VERBOSE_MODE',
    'SMART_FILE_SELECTOR',
    'TDX_CONFIG',
    'OUTPUT_CONFIG',
    'DATA_PROCESSING',
    'INTELLIGENT_FEATURES',
    'ERROR_HANDLING',
    'USER_INTERFACE',
]
