#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
文档版本管理工具

提供文档版本控制、状态管理和更新历史记录功能
"""

import os
import re
import json
import yaml
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict


@dataclass
class DocumentVersion:
    """文档版本信息"""
    version: str
    date: str
    author: str
    changes: str
    status: str = "发布"


@dataclass
class DocumentMetadata:
    """文档元数据"""
    title: str
    version: str
    date: str
    author: str
    status: str
    category: str
    tags: List[str]
    last_updated: str
    versions: List[DocumentVersion] = None
    
    def __post_init__(self):
        if self.versions is None:
            self.versions = []


class DocumentVersionManager:
    """文档版本管理器"""
    
    def __init__(self, docs_root: str = "docs"):
        self.docs_root = Path(docs_root)
        self.version_db_path = self.docs_root / ".document_versions.json"
        self.version_db = self._load_version_db()
    
    def _load_version_db(self) -> Dict:
        """加载版本数据库"""
        if self.version_db_path.exists():
            try:
                with open(self.version_db_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 加载版本数据库失败: {e}")
        
        return {}
    
    def _save_version_db(self):
        """保存版本数据库"""
        try:
            with open(self.version_db_path, 'w', encoding='utf-8') as f:
                json.dump(self.version_db, f, ensure_ascii=False, indent=2)
        except Exception as e:
            print(f"❌ 保存版本数据库失败: {e}")
    
    def extract_metadata(self, file_path: Path) -> Optional[DocumentMetadata]:
        """从文档中提取元数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 检查是否有YAML前置元数据
            if not content.startswith('---'):
                return None
            
            # 提取YAML元数据
            yaml_end = content.find('---', 3)
            if yaml_end == -1:
                return None
            
            yaml_content = content[3:yaml_end].strip()
            metadata_dict = yaml.safe_load(yaml_content)
            
            # 转换为DocumentMetadata对象
            metadata = DocumentMetadata(
                title=metadata_dict.get('title', ''),
                version=metadata_dict.get('version', 'v1.0'),
                date=metadata_dict.get('date', ''),
                author=metadata_dict.get('author', ''),
                status=metadata_dict.get('status', '草稿'),
                category=metadata_dict.get('category', ''),
                tags=metadata_dict.get('tags', []),
                last_updated=metadata_dict.get('last_updated', '')
            )
            
            return metadata
            
        except Exception as e:
            print(f"❌ 提取元数据失败 {file_path}: {e}")
            return None
    
    def update_metadata(self, file_path: Path, metadata: DocumentMetadata):
        """更新文档元数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 构建新的YAML元数据
            metadata_dict = {
                'title': metadata.title,
                'version': metadata.version,
                'date': metadata.date,
                'author': metadata.author,
                'status': metadata.status,
                'category': metadata.category,
                'tags': metadata.tags,
                'last_updated': metadata.last_updated
            }
            
            yaml_content = yaml.dump(metadata_dict, allow_unicode=True, default_flow_style=False)
            new_yaml_block = f"---\n{yaml_content}---"
            
            # 替换或添加元数据
            if content.startswith('---'):
                yaml_end = content.find('---', 3)
                if yaml_end != -1:
                    # 替换现有元数据
                    new_content = new_yaml_block + content[yaml_end + 3:]
                else:
                    # 添加结束标记
                    new_content = new_yaml_block + '\n\n' + content[3:]
            else:
                # 添加新元数据
                new_content = new_yaml_block + '\n\n' + content
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return True
            
        except Exception as e:
            print(f"❌ 更新元数据失败 {file_path}: {e}")
            return False
    
    def increment_version(self, version: str) -> str:
        """递增版本号"""
        # 解析版本号 (如 v1.2.3)
        match = re.match(r'v?(\d+)\.(\d+)(?:\.(\d+))?', version)
        if match:
            major, minor, patch = match.groups()
            patch = patch or '0'
            
            # 递增补丁版本
            new_patch = int(patch) + 1
            return f"v{major}.{minor}.{new_patch}"
        else:
            # 如果无法解析，默认递增
            return "v1.1"
    
    def create_version_entry(self, file_path: Path, changes: str, author: str = None) -> DocumentVersion:
        """创建版本记录"""
        metadata = self.extract_metadata(file_path)
        if not metadata:
            return None
        
        # 递增版本号
        new_version = self.increment_version(metadata.version)
        
        # 创建版本记录
        version_entry = DocumentVersion(
            version=new_version,
            date=datetime.now().strftime('%Y-%m-%d'),
            author=author or metadata.author,
            changes=changes,
            status="发布"
        )
        
        return version_entry
    
    def update_document_version(self, file_path: Path, changes: str, author: str = None) -> bool:
        """更新文档版本"""
        try:
            # 提取当前元数据
            metadata = self.extract_metadata(file_path)
            if not metadata:
                print(f"⚠️ 无法提取元数据: {file_path}")
                return False
            
            # 创建新版本记录
            version_entry = self.create_version_entry(file_path, changes, author)
            if not version_entry:
                return False
            
            # 更新元数据
            metadata.version = version_entry.version
            metadata.last_updated = version_entry.date
            if author:
                metadata.author = author
            
            # 更新文档
            success = self.update_metadata(file_path, metadata)
            if success:
                # 记录到版本数据库
                file_key = str(file_path.relative_to(self.docs_root))
                if file_key not in self.version_db:
                    self.version_db[file_key] = []
                
                self.version_db[file_key].append(asdict(version_entry))
                self._save_version_db()
                
                print(f"✅ 版本更新成功: {file_path} → {version_entry.version}")
                return True
            
            return False
            
        except Exception as e:
            print(f"❌ 版本更新失败 {file_path}: {e}")
            return False
    
    def get_version_history(self, file_path: Path) -> List[DocumentVersion]:
        """获取文档版本历史"""
        file_key = str(file_path.relative_to(self.docs_root))
        
        if file_key in self.version_db:
            versions = []
            for version_data in self.version_db[file_key]:
                versions.append(DocumentVersion(**version_data))
            return versions
        
        return []
    
    def generate_changelog(self, file_path: Path) -> str:
        """生成变更日志"""
        versions = self.get_version_history(file_path)
        
        if not versions:
            return "## 更新历史\n\n暂无版本历史记录。\n"
        
        changelog = "## 更新历史\n\n"
        changelog += "| 版本 | 日期 | 作者 | 变更内容 |\n"
        changelog += "|------|------|------|----------|\n"
        
        # 按版本倒序排列
        versions.sort(key=lambda x: x.date, reverse=True)
        
        for version in versions:
            changelog += f"| {version.version} | {version.date} | {version.author} | {version.changes} |\n"
        
        return changelog
    
    def add_changelog_to_document(self, file_path: Path) -> bool:
        """将变更日志添加到文档末尾"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 生成变更日志
            changelog = self.generate_changelog(file_path)
            
            # 检查是否已有更新历史部分
            if "## 更新历史" in content:
                # 替换现有的更新历史
                pattern = r'## 更新历史.*?(?=\n##|\n---|\Z)'
                new_content = re.sub(pattern, changelog.rstrip(), content, flags=re.DOTALL)
            else:
                # 添加到文档末尾
                new_content = content.rstrip() + '\n\n' + changelog
            
            # 写入文件
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(new_content)
            
            return True
            
        except Exception as e:
            print(f"❌ 添加变更日志失败 {file_path}: {e}")
            return False
    
    def batch_update_status(self, status: str, pattern: str = "*.md") -> int:
        """批量更新文档状态"""
        updated_count = 0
        
        for file_path in self.docs_root.rglob(pattern):
            if file_path.is_file():
                metadata = self.extract_metadata(file_path)
                if metadata:
                    metadata.status = status
                    metadata.last_updated = datetime.now().strftime('%Y-%m-%d')
                    
                    if self.update_metadata(file_path, metadata):
                        updated_count += 1
                        print(f"✅ 状态更新: {file_path} → {status}")
        
        return updated_count
    
    def generate_version_report(self) -> str:
        """生成版本管理报告"""
        report = f"""# 文档版本管理报告

## 生成时间
{datetime.now().strftime('%Y-%m-%d %H:%M:%S')}

## 版本统计
"""
        
        total_docs = 0
        versioned_docs = 0
        status_count = {}
        
        for file_path in self.docs_root.rglob('*.md'):
            if file_path.is_file():
                total_docs += 1
                metadata = self.extract_metadata(file_path)
                
                if metadata:
                    # 统计状态
                    status = metadata.status
                    status_count[status] = status_count.get(status, 0) + 1
                    
                    # 检查是否有版本历史
                    if self.get_version_history(file_path):
                        versioned_docs += 1
        
        report += f"- 总文档数: {total_docs}\n"
        report += f"- 有版本历史的文档: {versioned_docs}\n"
        report += f"- 版本化率: {versioned_docs/total_docs*100:.1f}%\n\n"
        
        report += "## 文档状态分布\n"
        for status, count in status_count.items():
            report += f"- {status}: {count} 个文档\n"
        
        report += "\n## 最近更新的文档\n"
        
        # 获取最近更新的文档
        recent_docs = []
        for file_path in self.docs_root.rglob('*.md'):
            if file_path.is_file():
                metadata = self.extract_metadata(file_path)
                if metadata and metadata.last_updated:
                    recent_docs.append((file_path, metadata.last_updated))
        
        # 按更新时间排序
        recent_docs.sort(key=lambda x: x[1], reverse=True)
        
        for file_path, update_date in recent_docs[:10]:
            report += f"- {file_path.name}: {update_date}\n"
        
        return report


def main():
    """主函数"""
    print("📚 MythQuant 文档版本管理工具")
    print("=" * 50)
    
    manager = DocumentVersionManager()
    
    # 显示菜单
    while True:
        print("\n🔧 可用操作:")
        print("1. 更新文档版本")
        print("2. 批量更新状态")
        print("3. 生成版本报告")
        print("4. 为文档添加变更日志")
        print("5. 退出")
        
        choice = input("\n请选择操作 (1-5): ").strip()
        
        if choice == '1':
            file_path = input("请输入文档路径: ").strip()
            changes = input("请输入变更说明: ").strip()
            author = input("请输入作者姓名 (可选): ").strip() or None
            
            path = Path(file_path)
            if path.exists():
                manager.update_document_version(path, changes, author)
            else:
                print("❌ 文件不存在")
        
        elif choice == '2':
            status = input("请输入新状态 (草稿/审核/发布/归档): ").strip()
            count = manager.batch_update_status(status)
            print(f"✅ 已更新 {count} 个文档的状态")
        
        elif choice == '3':
            report = manager.generate_version_report()
            report_path = Path("docs/reports/document_version_report.md")
            report_path.parent.mkdir(parents=True, exist_ok=True)
            
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(report)
            
            print(f"📄 版本报告已生成: {report_path}")
        
        elif choice == '4':
            file_path = input("请输入文档路径: ").strip()
            path = Path(file_path)
            
            if path.exists():
                if manager.add_changelog_to_document(path):
                    print("✅ 变更日志已添加")
                else:
                    print("❌ 添加变更日志失败")
            else:
                print("❌ 文件不存在")
        
        elif choice == '5':
            print("👋 再见！")
            break
        
        else:
            print("❌ 无效选择，请重试")


if __name__ == "__main__":
    main()
