# 1分钟数据下载功能测试期望和验证标准

## 📋 测试目标概述

### 🎯 总体目标
验证DDD架构改造后的1分钟数据下载和缺失数据修复功能是否**完好如初**，确保所有核心功能都能正常工作，数据质量达到生产标准。

### 🔍 测试范围
- **功能完整性**: 所有原有功能都能正常工作
- **数据质量**: 输出数据格式和内容正确
- **性能表现**: 处理速度和成功率达标
- **配置遵循**: 严格按照user_config.py配置执行
- **错误处理**: 异常情况下的优雅处理

## ✅ 详细测试期望

### 1. 配置读取期望

#### ✅ **时间范围配置**
- **期望**: 程序必须读取`user_config.py`中的`time_ranges['internet_minute']`配置
- **具体要求**:
  ```
  ✅ 开始时间: 2025-01-01 (来自start_date: '20250101')
  ✅ 结束时间: 2025-07-27 (来自end_date: '20250727')
  ✅ 数据频率: 1min (来自frequency: '1min')
  ❌ 不应该使用: 2024-01-01至2024-12-31 (硬编码)
  ```

#### ✅ **股票代码配置**
- **期望**: 读取`user_config.py`中的目标股票代码
- **具体要求**:
  ```
  ✅ 股票文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
  ✅ 测试股票: 000617 (中钢天源)
  ✅ 市场识别: 深证市场 (前缀: 0_)
  ```

### 2. 数据获取期望

#### ✅ **pytdx数据获取**
- **期望**: 成功从pytdx服务器获取实时分钟数据
- **具体要求**:
  ```
  ✅ 连接成功: 能够连接到通达信服务器
  ✅ 数据获取: 获取到240条/天的分钟数据 (9:30-11:30 + 13:00-15:00)
  ✅ 数据范围: 最近1-3个交易日的数据 (符合pytdx限制)
  ✅ 数据格式: OHLCV格式，包含时间戳
  ```

#### ✅ **数据质量验证**
- **期望**: 获取的原始数据质量符合标准
- **具体要求**:
  ```
  ✅ 时间连续性: 分钟数据时间连续，无大幅跳跃
  ✅ 价格合理性: OHLC价格关系正确 (H≥O,C,L≤O,C)
  ✅ 成交量非负: 成交量≥0
  ✅ 数据完整性: 无空值或异常值
  ```

### 3. 数据处理期望

#### ✅ **数据预处理**
- **期望**: 原始数据经过清洗和标准化处理
- **具体要求**:
  ```
  ✅ 列名标准化: open, high, low, close, volume
  ✅ 数据类型转换: 价格为float，成交量为int
  ✅ 时间格式化: datetime_int格式 (YYYYMMDDHHMM)
  ✅ 异常数据清理: 删除不合理的数据行
  ```

#### ✅ **前复权计算**
- **期望**: 正确计算前复权价格
- **具体要求**:
  ```
  ✅ 除权数据获取: 尝试获取除权除息信息
  ✅ 复权计算: 如有除权事件，正确计算前复权价格
  ✅ 默认处理: 无除权事件时，前复权价格=当日收盘价
  ✅ 精度控制: 价格保留3位小数
  ```

#### ✅ **L2指标计算**
- **期望**: 计算买卖差、路径总长、主买主卖等指标
- **具体要求**:
  ```
  ✅ 买卖差计算: buy_sell_diff = main_buy - main_sell
  ✅ 路径总长: path_length = (high - low) / close
  ✅ 主买计算: 基于价格上涨和成交量
  ✅ 主卖计算: 基于价格下跌和成交量
  ✅ 数值精度: 所有指标保留3位小数
  ```

### 4. 文件输出期望

#### ✅ **文件命名规范**
- **期望**: 生成的文件名符合标准格式
- **具体要求**:
  ```
  ✅ 命名格式: 1min_0_000617_20250101-20250727.txt
  ✅ 频率标识: 1min (不是1_)
  ✅ 市场标识: 0_ (深证市场)
  ✅ 股票代码: 000617 (6位数字)
  ✅ 时间范围: 实际数据的起止时间
  ```

#### ✅ **文件内容格式**
- **期望**: 文件内容格式完全正确
- **具体要求**:
  ```
  ✅ 表头格式: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
  ✅ 字段分隔: 使用管道符"|"分隔
  ✅ 编码格式: UTF-8-BOM编码
  ✅ 数据行数: 240行/天 × 交易日数
  ```

#### ✅ **数据内容验证**
- **期望**: 文件中的数据内容准确无误
- **具体要求**:
  ```
  ✅ 股票代码: 每行都是000617
  ✅ 时间格式: YYYYMMDDHHMM (如202508010931)
  ✅ 价格数据: 当日收盘价和前复权收盘价合理
  ✅ 指标数据: 买卖差、路径总长、主买主卖数值合理
  ```

### 5. 性能期望

#### ✅ **处理速度**
- **期望**: 数据处理速度达到生产标准
- **具体要求**:
  ```
  ✅ 数据获取: ≤5秒获取240条分钟数据
  ✅ 数据处理: ≤2秒完成预处理和指标计算
  ✅ 文件写入: ≤1秒完成文件生成
  ✅ 总体耗时: ≤10秒完成单只股票处理
  ```

#### ✅ **成功率**
- **期望**: 功能执行成功率达标
- **具体要求**:
  ```
  ✅ 数据获取成功率: ≥90%
  ✅ 数据处理成功率: ≥95%
  ✅ 文件生成成功率: ≥98%
  ✅ 整体成功率: ≥85%
  ```

### 6. 错误处理期望

#### ✅ **配置错误处理**
- **期望**: 配置问题时的优雅处理
- **具体要求**:
  ```
  ✅ 配置缺失: 使用合理的默认值
  ✅ 时间格式错误: 自动修正或提示
  ✅ 路径不存在: 自动创建或提示
  ✅ 权限问题: 明确的错误提示
  ```

#### ✅ **数据获取错误处理**
- **期望**: 数据源问题时的回退机制
- **具体要求**:
  ```
  ✅ 网络连接失败: 重试机制或本地数据回退
  ✅ 服务器无响应: 切换备用服务器
  ✅ 数据格式异常: 数据清洗和修复
  ✅ 无数据返回: 明确的提示信息
  ```

## 🚫 不可接受的情况

### ❌ **严重错误**
- 程序崩溃或无法启动
- 使用硬编码时间而不读取配置
- 生成空文件或格式错误的文件
- 数据质量严重问题（如价格异常）

### ⚠️ **警告情况**
- 配置读取失败但有合理回退
- 部分数据获取失败但整体可用
- 性能略低于期望但在可接受范围
- 非关键功能异常但核心功能正常

## 📊 测试验证方法

### 1. **自动化验证**
- 运行完整的测试套件
- 检查文件生成和格式
- 验证数据质量和完整性
- 性能基准测试

### 2. **手动验证**
- 检查配置读取是否正确
- 验证时间范围是否符合配置
- 抽样检查数据内容
- 确认错误处理机制

### 3. **回归测试**
- 与DDD改造前的结果对比
- 确保功能完整性不降低
- 验证性能不退化
- 检查兼容性问题

## 🎯 成功标准

### ✅ **完全成功** (≥95%)
- 所有核心功能正常工作
- 数据质量100%达标
- 性能达到或超过期望
- 配置完全按照user_config.py执行

### ✅ **基本成功** (≥85%)
- 主要功能正常工作
- 数据质量基本达标
- 性能在可接受范围
- 配置基本正确，有少量回退

### ⚠️ **需要改进** (<85%)
- 部分功能存在问题
- 数据质量有待提升
- 性能低于期望
- 配置读取有问题

---

**总结**: 这个文档定义了1分钟数据下载功能的完整测试期望和验证标准，确保测试的充分性和准确性。所有测试都应该按照这些标准进行验证，任何偏差都需要明确记录和解释。
