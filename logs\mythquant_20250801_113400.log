2025-08-01 11:34:00,354 - Main - INFO - info:316 - ℹ️ 程序启动，日志文件: logs\mythquant_20250801_113400.log
2025-08-01 11:34:00,355 - Main - INFO - info:316 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-08-01 11:34:00,355 - Main - INFO - info:316 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-08-01 11:34:00,355 - Main - INFO - info:316 - ℹ️ 正在加载任务配置...
2025-08-01 11:34:00,355 - Main - INFO - info:316 - ℹ️ 加载了 7 个任务配置
2025-08-01 11:34:00,355 - Main - INFO - info:316 - ℹ️ 核心组件初始化完成
2025-08-01 11:34:00,355 - Main - INFO - info:316 - ℹ️ 应用程序初始化完成
2025-08-01 11:34:00,356 - Main - INFO - info:316 - ℹ️ 开始执行所有任务
2025-08-01 11:34:00,357 - Main - INFO - info:316 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-08-01 11:34:11,940 - Main - INFO - info:316 - ℹ️ 🎯 获取特定分钟数据: 000617 @ 202507041447
2025-08-01 11:34:11,940 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-08-01 11:34:12,131 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 5040条 (目标日期: 20250704, 交易日: 21天, 频率: 1min)
2025-08-01 11:34:12,131 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计5040条 -> 实际请求800条 (配置限制:800)
2025-08-01 11:34:12,131 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 11:34:12,131 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 11:34:12,184 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4240条
2025-08-01 11:34:12,431 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 11:34:12,654 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 11:34:12,878 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 11:34:13,102 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 11:34:13,316 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 11:34:13,540 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +240条，总计5040条
2025-08-01 11:34:13,540 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计5040条数据
2025-08-01 11:34:13,541 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要5040条，实际获取5040条
2025-08-01 11:34:13,555 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-01 11:34:13,558 - Main - INFO - info:316 - ℹ️ ℹ️ pytdx提供未复权原始价格，正在计算前复权价格以完善数据字段...
2025-08-01 11:34:13,558 - Main - INFO - info:316 - ℹ️ 🔄 pytdx前复权计算: 000617
2025-08-01 11:34:13,559 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-08-01 11:34:13,559 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-08-01 11:34:14,027 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-08-01 11:34:14,027 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-08-01 11:34:14,034 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-08-01 11:34:14,034 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17/T0002
2025-08-01 11:34:14,034 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-08-01 11:34:14,034 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-08-01 11:34:14,034 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 未找到任何市场的分钟数据文件
2025-08-01 11:34:14,034 - main_v20230219_optimized - WARNING - _get_optimal_tdx_path:255 - ⚠️  主路径下未找到分钟数据文件，但将继续使用配置路径
2025-08-01 11:34:14,034 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17/T0002，缓存方法: memory
2025-08-01 11:34:25,367 - cache.gbbq_cache - ERROR - _load_all_data:288 - 加载GBBQ数据失败: '类别'
2025-08-01 11:34:36,325 - main_v20230219_optimized - INFO - _ensure_pickle_cache:441 - 🔄 更新pickle缓存...
2025-08-01 11:34:36,325 - main_v20230219_optimized - ERROR - _ensure_pickle_cache:495 - ❌ pickle缓存更新失败: 'StockDataProcessor' object has no attribute 'tdx_path'
2025-08-01 11:34:36,855 - main_v20230219_optimized - WARNING - _extract_stock_code_from_data:2514 - 无法从数据中提取股票代码，使用target_stocks中的第一个
2025-08-01 11:34:36,866 - main_v20230219_optimized - INFO - apply_forward_adjustment:2202 - 保留交易时间数据后: 240条
2025-08-01 11:34:36,867 - Main - INFO - info:316 - ℹ️ ✅ pytdx前复权计算成功，处理了20个除权事件
2025-08-01 11:34:36,881 - Main - INFO - info:316 - ℹ️ ✅ pytdx前复权计算成功
2025-08-01 11:34:36,881 - Main - WARNING - warning:320 - ⚠️ ⚠️ 前复权价格与原始价格相同，可能无除权事件
2025-08-01 11:34:36,882 - Main - INFO - info:316 - ℹ️ 📊 获取到 240 条数据，查找目标时间 202507041447
2025-08-01 11:34:36,886 - Main - INFO - info:316 - ℹ️ ✅ 找到目标数据: 收盘价=7.550, 前复权=nan
2025-08-01 11:34:36,886 - Main - INFO - info:316 - ℹ️ ✅ 增量下载前提条件验证通过
2025-08-01 11:34:36,887 - Main - INFO - info:316 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-08-01 11:34:36,887 - Main - INFO - info:316 - ℹ️ 🔍 开始检测000617的缺失数据
2025-08-01 11:34:36,915 - Main - INFO - info:316 - ℹ️ 📊 数据统计: 总记录数17211, 覆盖72个交易日
2025-08-01 11:34:36,915 - Main - WARNING - warning:320 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-08-01 11:34:36,915 - Main - WARNING - warning:320 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-08-01 11:34:36,915 - Main - WARNING - warning:320 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-08-01 11:34:36,916 - Main - INFO - info:316 - ℹ️ 🔧 开始修复000617的缺失数据
2025-08-01 11:34:36,916 - Main - INFO - info:316 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-08-01 11:34:36,916 - Main - INFO - info:316 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-08-01 11:34:36,916 - Main - INFO - info:316 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-08-01 11:34:36,916 - Main - INFO - info:316 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-08-01 11:34:36,916 - Main - INFO - info:316 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-08-01 11:34:36,916 - Main - INFO - info:316 - ℹ️ ✅ 缺失数据修复完成
2025-08-01 11:34:37,288 - Main - INFO - info:316 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-08-01 11:34:37,288 - Main - INFO - info:316 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-08-01 11:34:37,288 - Main - INFO - info:316 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-01 11:34:37,288 - Main - INFO - info:316 - ℹ️ 找到现有文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-01 11:34:37,288 - Main - INFO - info:316 - ℹ️ 📊 开始智能时间范围分析
2025-08-01 11:34:37,304 - Main - INFO - info:316 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-08-01 11:34:37,528 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-08-01 11:34:37,530 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 11:34:37,530 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 11:34:37,530 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-08-01 11:34:37,712 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 5040条 (目标日期: 20250704, 交易日: 21天, 频率: 1min)
2025-08-01 11:34:37,712 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计5040条 -> 实际请求800条 (配置限制:800)
2025-08-01 11:34:37,712 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 11:34:37,712 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 11:34:37,764 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4240条
2025-08-01 11:34:37,987 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 11:34:38,217 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 11:34:38,447 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 11:34:38,675 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 11:34:38,902 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 11:34:39,118 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +240条，总计5040条
2025-08-01 11:34:39,118 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计5040条数据
2025-08-01 11:34:39,119 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要5040条，实际获取5040条
2025-08-01 11:34:39,132 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-01 11:34:39,139 - Main - INFO - info:316 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-08-01 11:34:39,141 - Main - INFO - info:316 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-08-01 11:34:39,141 - Main - INFO - info:316 - ℹ️ 从文件名解析时间范围: 20250320 - 20250704
2025-08-01 11:34:39,141 - Main - INFO - info:316 - ℹ️ 需要下载早期数据: 20250101 - 20250319
2025-08-01 11:34:39,141 - Main - INFO - info:316 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-08-01 11:34:39,150 - Main - INFO - info:316 - ℹ️ 读取现有数据: 17211 条记录
2025-08-01 11:34:39,150 - Main - INFO - info:316 - ℹ️ 下载增量数据: 20250101 - 20250319
2025-08-01 11:34:39,150 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 11:34:39,151 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 11:34:39,151 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250319
2025-08-01 11:34:39,331 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 33600条 (目标日期: 20250101, 交易日: 140天, 频率: 1min)
2025-08-01 11:34:39,331 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计33600条 -> 实际请求800条 (配置限制:800)
2025-08-01 11:34:39,331 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 11:34:39,331 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 11:34:39,381 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32800条
2025-08-01 11:34:39,603 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 11:34:39,834 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 11:34:40,061 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 11:34:40,282 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 11:34:40,508 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 11:34:40,742 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-08-01 11:34:40,968 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-08-01 11:34:41,183 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-08-01 11:34:41,409 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-08-01 11:34:41,634 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-08-01 11:34:41,864 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-08-01 11:34:42,104 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-08-01 11:34:42,338 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-08-01 11:34:42,560 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-08-01 11:34:42,781 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-08-01 11:34:43,000 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-08-01 11:34:43,220 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-08-01 11:34:43,454 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-08-01 11:34:43,684 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-08-01 11:34:43,901 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-08-01 11:34:44,131 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-08-01 11:34:44,366 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-08-01 11:34:44,585 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-08-01 11:34:44,804 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-08-01 11:34:45,035 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-08-01 11:34:45,256 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-08-01 11:34:45,495 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-08-01 11:34:45,715 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +40条，总计22440条
2025-08-01 11:34:45,715 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计22440条数据
2025-08-01 11:34:45,720 - Main - WARNING - warning:320 - ⚠️ ⚠️ 数据覆盖不足: 需要33600条，实际获取22440条
2025-08-01 11:34:45,720 - Main - WARNING - warning:320 - ⚠️ ⚠️ 缺少约11160条数据（约46个交易日）
2025-08-01 11:34:45,720 - Main - WARNING - warning:320 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-08-
2025-08-01 11:34:45,720 - Main - WARNING - warning:320 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-08-01 11:34:45,720 - Main - WARNING - warning:320 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-08-01 11:34:45,771 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 480 条 1min 数据
2025-08-01 11:34:45,780 - Main - INFO - info:316 - ℹ️ 获得新数据: 480 条记录
2025-08-01 11:34:45,780 - Main - INFO - info:316 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-08-01 11:34:45,780 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 11:34:45,780 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 11:34:45,780 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-08-01 11:34:45,947 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 4800条 (目标日期: 20250705, 交易日: 20天, 频率: 1min)
2025-08-01 11:34:45,948 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计4800条 -> 实际请求800条 (配置限制:800)
2025-08-01 11:34:45,948 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 11:34:45,948 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 11:34:45,997 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4000条
2025-08-01 11:34:46,218 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 11:34:46,452 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 11:34:46,685 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 11:34:46,905 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 11:34:47,124 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 11:34:47,124 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计4800条数据
2025-08-01 11:34:47,126 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要4800条，实际获取4800条
2025-08-01 11:34:47,137 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-08-01 11:34:47,155 - Main - INFO - info:316 - ℹ️ 获得新数据: 3600 条记录
2025-08-01 11:34:47,158 - Main - INFO - info:316 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-08-01 11:34:47,167 - Main - INFO - info:316 - ℹ️ 合并后数据: 21291 条记录
2025-08-01 11:34:47,251 - Main - INFO - info:316 - ℹ️ 删除旧文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-01 11:34:47,251 - Main - INFO - info:316 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503180931-202507251500_来源互联网（202508011134）.txt
2025-08-01 11:34:47,251 - Main - INFO - info:316 - ℹ️ ✅ 智能增量下载完成
2025-08-01 11:34:47,252 - Main - INFO - info:316 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-08-01 11:34:47,252 - Main - INFO - info:316 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-08-01 11:34:47,252 - Main - INFO - info:316 - ℹ️ 开始执行前复权数据比较分析任务
2025-08-01 11:34:47,253 - Main - INFO - info:316 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-08-01 11:34:47,253 - Main - INFO - info:316 - ℹ️ 正在分析股票: 000617
2025-08-01 11:34:47,253 - Main - INFO - info:316 - ℹ️ 开始比较股票 000617 的前复权数据
2025-08-01 11:34:47,253 - Main - INFO - info:316 - ℹ️ 找到文件 - TDX: 未找到
2025-08-01 11:34:47,253 - Main - INFO - info:316 - ℹ️ 找到文件 - 互联网: 未找到
2025-08-01 11:34:47,253 - Main - WARNING - warning:320 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-08-01 11:34:47,254 - Main - INFO - info:316 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250801_113447.txt
2025-08-01 11:34:47,254 - Main - INFO - info:316 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-08-01 11:34:47,254 - Main - ERROR - error:324 - ❌ 任务执行失败: 前复权数据比较分析
2025-08-01 11:34:47,254 - Main - INFO - info:316 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-08-01 11:34:47,255 - Main - INFO - info:316 - ℹ️ 开始清理应用程序资源
2025-08-01 11:34:47,255 - Main - INFO - info:316 - ℹ️ 开始清理股票处理器资源
2025-08-01 11:34:47,255 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-08-01 11:34:47,255 - Main - INFO - info:316 - ℹ️ 异步IO处理器资源清理完成
2025-08-01 11:34:47,255 - Main - INFO - info:316 - ℹ️ 股票处理器资源清理完成
2025-08-01 11:34:47,255 - Main - INFO - info:316 - ℹ️ 开始清理任务管理器资源
2025-08-01 11:34:47,255 - Main - INFO - info:316 - ℹ️ 任务管理器资源清理完成
2025-08-01 11:34:47,255 - Main - INFO - info:316 - ℹ️ 应用程序资源清理完成
