#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出格式迁移验证测试脚本

验证各模块是否正确迁移到新的结构化输出格式

位置: test_environments/integration_tests/configs/
作者: AI Assistant
创建时间: 2025-07-30
修正时间: 2025-07-31
"""

import sys
import os
import importlib
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_stats_table,
    print_completion
)


def check_module_imports(module_name: str, expected_imports: list) -> bool:
    """检查模块是否正确导入了结构化输出格式器"""
    try:
        # 构建正确的模块文件路径
        module_path = os.path.join(project_root, module_name.replace('.', os.sep) + '.py')
        
        if not os.path.exists(module_path):
            print_error(f"模块文件不存在: {module_path}")
            return False
        
        with open(module_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查是否导入了结构化输出格式器
        if 'structured_output_formatter' not in content:
            print_warning(f"模块 {module_name} 未导入结构化输出格式器")
            return False
        
        # 检查具体的导入函数
        missing_imports = []
        for import_func in expected_imports:
            if import_func not in content:
                missing_imports.append(import_func)
        
        if missing_imports:
            print_warning(f"模块 {module_name} 缺少导入: {', '.join(missing_imports)}")
            return False
        
        print_result(f"模块 {module_name} 导入检查通过", True)
        return True
        
    except Exception as e:
        print_error(f"检查模块 {module_name} 时出错: {e}")
        return False


def check_old_print_patterns(module_name: str) -> bool:
    """检查模块是否还有旧的print格式"""
    try:
        module_path = os.path.join(project_root, module_name.replace('.', os.sep) + '.py')
        
        if not os.path.exists(module_path):
            return True
        
        with open(module_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 检查旧的print模式
        old_patterns = [
            'print("="',  # 旧的分隔符
            'print(f"🔍',  # 旧的步骤格式
            'print(f"📊',  # 旧的数据格式（如果不是通过格式器）
            'print("\\n"',  # 手动换行
        ]
        
        found_patterns = []
        for pattern in old_patterns:
            if pattern in content:
                found_patterns.append(pattern)
        
        if found_patterns:
            print_warning(f"模块 {module_name} 仍有旧格式: {', '.join(found_patterns)}")
            return False
        
        print_result(f"模块 {module_name} 旧格式检查通过", True)
        return True
        
    except Exception as e:
        print_error(f"检查模块 {module_name} 旧格式时出错: {e}")
        return False


def test_module_functionality(module_name: str) -> bool:
    """测试模块功能是否正常"""
    try:
        # 临时切换到项目根目录以确保导入正确
        original_cwd = os.getcwd()
        os.chdir(project_root)
        
        try:
            # 尝试导入模块
            module = importlib.import_module(module_name)
            print_result(f"模块 {module_name} 导入成功", True)
            
            # 对特定模块进行深度测试
            if module_name == 'utils.structured_internet_minute_downloader':
                # 测试创建实例
                downloader_class = getattr(module, 'StructuredInternetMinuteDownloader')
                downloader = downloader_class()
                print_result(f"模块 {module_name} 实例创建成功", True)
            
            return True
            
        finally:
            # 恢复原始工作目录
            os.chdir(original_cwd)
        
    except Exception as e:
        print_error(f"模块 {module_name} 功能测试失败: {e}")
        return False


def main():
    """主验证函数"""
    print_banner("输出格式迁移验证测试", "检查各模块是否正确迁移到新的结构化输出格式")
    
    print_info(f"项目根目录: {project_root}")
    print_info(f"当前工作目录: {os.getcwd()}")
    
    # 定义需要检查的模块和预期导入
    modules_to_check = {
        'main': {
            'expected_imports': ['print_banner', 'print_main_process', 'print_completion'],
            'priority': 'high'
        },
        'utils.structured_internet_minute_downloader': {
            'expected_imports': ['print_main_process', 'print_sub_process', 'print_step', 'print_result'],
            'priority': 'high'
        },
        'core.application': {
            'expected_imports': ['print_sub_process', 'print_result', 'print_stats_table'],
            'priority': 'medium'
        },
        'tools.data_integrity_auditor': {
            'expected_imports': ['print_banner', 'print_main_process', 'print_result'],
            'priority': 'medium'
        },
        'utils.smart_file_selector': {
            'expected_imports': ['print_step', 'print_result', 'print_info'],
            'priority': 'medium'
        },
        'utils.missing_data_processor': {
            'expected_imports': ['print_step', 'print_result', 'print_warning'],
            'priority': 'medium'
        }
    }
    
    print_main_process("模块迁移状态检查")
    
    results = {}
    
    for module_name, config in modules_to_check.items():
        print_step(f"检查模块: {module_name}")
        
        # 检查导入
        import_ok = check_module_imports(module_name, config['expected_imports'])
        
        # 检查旧格式
        old_format_ok = check_old_print_patterns(module_name)
        
        # 检查功能
        functionality_ok = test_module_functionality(module_name)
        
        # 综合评估
        overall_ok = import_ok and old_format_ok and functionality_ok
        results[module_name] = {
            'status': 'passed' if overall_ok else 'failed',
            'priority': config['priority'],
            'import_ok': import_ok,
            'old_format_ok': old_format_ok,
            'functionality_ok': functionality_ok
        }
        
        if overall_ok:
            print_result(f"模块 {module_name} 迁移验证通过", True)
        else:
            print_result(f"模块 {module_name} 迁移验证失败", False)
    
    # 统计结果
    print_main_process("迁移验证结果统计")
    
    total_modules = len(results)
    passed_modules = sum(1 for r in results.values() if r['status'] == 'passed')
    failed_modules = total_modules - passed_modules
    
    # 按优先级统计
    high_priority = [name for name, config in modules_to_check.items() if config['priority'] == 'high']
    high_passed = sum(1 for name in high_priority if results[name]['status'] == 'passed')
    
    medium_priority = [name for name, config in modules_to_check.items() if config['priority'] == 'medium']
    medium_passed = sum(1 for name in medium_priority if results[name]['status'] == 'passed')
    
    # 显示统计信息
    stats = {
        "总模块数": total_modules,
        "通过模块数": passed_modules,
        "失败模块数": failed_modules,
        "总体通过率": f"{passed_modules/total_modules*100:.1f}%",
        "高优先级通过率": f"{high_passed/len(high_priority)*100:.1f}%" if high_priority else "N/A",
        "中优先级通过率": f"{medium_passed/len(medium_priority)*100:.1f}%" if medium_priority else "N/A"
    }
    
    print_stats_table("迁移验证统计", stats)
    
    # 显示详细结果
    if failed_modules > 0:
        print_main_process("失败模块详情")
        for module_name, result in results.items():
            if result['status'] == 'failed':
                print_error(f"模块 {module_name} 失败原因:")
                if not result['import_ok']:
                    print_info("- 导入检查失败", level=2)
                if not result['old_format_ok']:
                    print_info("- 仍有旧格式", level=2)
                if not result['functionality_ok']:
                    print_info("- 功能测试失败", level=2)
    
    # 完成信息
    completion_stats = {
        "检查模块数": total_modules,
        "验证通过率": f"{passed_modules/total_modules*100:.1f}%",
        "测试环境": "integration_tests"
    }
    
    success = passed_modules == total_modules
    completion_message = "所有模块迁移验证通过！" if success else f"{failed_modules}个模块需要进一步处理"
    
    print_completion(completion_message, success, completion_stats)
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
