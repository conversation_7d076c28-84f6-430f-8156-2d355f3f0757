#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置迁移测试脚本

测试新旧配置系统的兼容性和一致性
"""

import sys
import os
from pathlib import Path

def test_old_config():
    """测试旧配置系统"""
    print("🔍 测试旧配置系统...")
    
    try:
        import user_config
        print("   ✅ 旧配置系统可用")
        
        # 测试关键配置项
        configs = {
            'TDX路径': getattr(user_config, 'tdx_path', 'Not found'),
            '调试模式': getattr(user_config, 'DEBUG', 'Not found'),
            '输出路径': getattr(user_config, 'output_storage', {}).get('base_directory', 'Not found') if hasattr(user_config, 'output_storage') else 'Not found'
        }
        
        for key, value in configs.items():
            print(f"   📋 {key}: {value}")
        
        return True, configs
        
    except ImportError:
        print("   ⚠️ 旧配置系统不可用")
        return False, {}
    except Exception as e:
        print(f"   ❌ 旧配置系统测试失败: {e}")
        return False, {}

def test_new_config():
    """测试新配置系统"""
    print("\n🔍 测试新配置系统...")
    
    try:
        # 添加src目录到路径
        project_root = Path(__file__).parent
        src_path = project_root / "src"
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        from mythquant.config import config_manager
        print("   ✅ 新配置系统可用")
        
        # 测试关键配置项
        configs = {
            'TDX路径': config_manager.get_tdx_path(),
            '调试模式': config_manager.is_debug_enabled(),
            '输出路径': config_manager.get_output_path(),
            '智能文件选择器': config_manager.is_smart_file_selector_enabled(),
            '增量下载': config_manager.is_incremental_download_enabled()
        }
        
        for key, value in configs.items():
            print(f"   📋 {key}: {value}")
        
        return True, configs
        
    except ImportError as e:
        print(f"   ❌ 新配置系统导入失败: {e}")
        return False, {}
    except Exception as e:
        print(f"   ❌ 新配置系统测试失败: {e}")
        return False, {}

def test_compatibility_module():
    """测试兼容性模块"""
    print("\n🔍 测试配置兼容性模块...")
    
    try:
        import config_compatibility as compat
        print("   ✅ 兼容性模块可用")
        
        # 测试关键配置项
        configs = {
            'TDX路径': compat.tdx_path,
            '调试模式': compat.DEBUG,
            '输出路径': compat.output_path,
            '智能文件选择器': compat.smart_file_selector_enabled,
            '控制台输出': compat.enable_console,
            '文件编码': compat.file_encoding
        }
        
        for key, value in configs.items():
            print(f"   📋 {key}: {value}")
        
        # 测试配置函数
        print("\n   🔧 测试配置函数:")
        print(f"   📋 get_tdx_path(): {compat.get_tdx_path()}")
        print(f"   📋 is_debug_enabled(): {compat.is_debug_enabled()}")
        print(f"   📋 get_output_path(): {compat.get_output_path()}")
        
        return True, configs
        
    except ImportError as e:
        print(f"   ❌ 兼容性模块导入失败: {e}")
        return False, {}
    except Exception as e:
        print(f"   ❌ 兼容性模块测试失败: {e}")
        return False, {}

def compare_configs(old_configs, new_configs, compat_configs):
    """比较配置一致性"""
    print("\n📊 配置一致性比较:")
    print("-" * 60)
    
    # 定义配置映射关系
    config_mappings = [
        ('TDX路径', 'TDX路径', 'TDX路径'),
        ('调试模式', '调试模式', '调试模式'),
        ('输出路径', '输出路径', '输出路径')
    ]
    
    consistent = True
    
    for old_key, new_key, compat_key in config_mappings:
        old_val = old_configs.get(old_key, 'N/A')
        new_val = new_configs.get(new_key, 'N/A')
        compat_val = compat_configs.get(compat_key, 'N/A')
        
        print(f"📋 {old_key}:")
        print(f"   旧配置: {old_val}")
        print(f"   新配置: {new_val}")
        print(f"   兼容性: {compat_val}")
        
        # 检查一致性
        if old_val != 'N/A' and new_val != 'N/A':
            if str(old_val) != str(new_val):
                print(f"   ⚠️ 配置不一致！")
                consistent = False
            else:
                print(f"   ✅ 配置一致")
        else:
            print(f"   ℹ️ 部分配置不可用")
        
        print()
    
    return consistent

def test_config_access_patterns():
    """测试不同的配置访问模式"""
    print("🔧 测试配置访问模式...")
    
    try:
        import config_compatibility as config
        
        # 测试模块级别访问
        print(f"   📋 模块级别访问 - TDX路径: {config.tdx_path}")
        
        # 测试函数访问
        print(f"   📋 函数访问 - TDX路径: {config.get_tdx_path()}")
        
        # 测试配置对象访问
        print(f"   📋 对象访问 - TDX路径: {config.compat_config.tdx_path}")
        
        # 测试动态配置访问
        print(f"   📋 动态访问 - 调试模式: {config.get_config('debug', False)}")
        
        print("   ✅ 所有访问模式正常")
        return True
        
    except Exception as e:
        print(f"   ❌ 配置访问测试失败: {e}")
        return False

def generate_migration_report(old_available, new_available, compat_available, consistent):
    """生成迁移报告"""
    print("\n📄 生成配置迁移报告...")
    
    report = f"""# 配置迁移测试报告

## 测试概况
- 测试时间: {os.popen('date /t & time /t').read().strip()}
- 旧配置系统: {'✅ 可用' if old_available else '❌ 不可用'}
- 新配置系统: {'✅ 可用' if new_available else '❌ 不可用'}
- 兼容性模块: {'✅ 可用' if compat_available else '❌ 不可用'}
- 配置一致性: {'✅ 一致' if consistent else '⚠️ 不一致'}

## 迁移状态
{'🎉 配置迁移准备就绪！' if compat_available else '🔧 需要修复配置问题'}

## 下一步建议
{'1. 开始将现有代码中的 import user_config 替换为 import config_compatibility' if compat_available else '1. 修复配置兼容性问题'}
{'2. 逐步测试各个功能模块' if compat_available else '2. 重新测试配置系统'}
{'3. 验证输出结果的一致性' if compat_available else '3. 检查配置映射关系'}

## 风险评估
风险等级: {'🟢 低风险' if consistent and compat_available else '🟡 中风险' if compat_available else '🔴 高风险'}

## 技术细节
- Python路径: {sys.path[:3]}...
- 项目根目录: {Path(__file__).parent}
- src目录: {Path(__file__).parent / 'src'}
"""
    
    with open("config_migration_test_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("   ✅ 报告已生成: config_migration_test_report.md")

def main():
    """主测试函数"""
    print("🧪 MythQuant 配置迁移测试")
    print("=" * 60)
    
    # 测试各个配置系统
    old_available, old_configs = test_old_config()
    new_available, new_configs = test_new_config()
    compat_available, compat_configs = test_compatibility_module()
    
    # 比较配置一致性
    consistent = True
    if old_available and new_available and compat_available:
        consistent = compare_configs(old_configs, new_configs, compat_configs)
    
    # 测试配置访问模式
    if compat_available:
        access_test = test_config_access_patterns()
    else:
        access_test = False
    
    # 生成报告
    generate_migration_report(old_available, new_available, compat_available, consistent)
    
    # 总结
    print("\n" + "=" * 60)
    if compat_available and consistent:
        print("🎉 配置迁移测试通过！")
        print("\n✅ 迁移准备就绪:")
        print("   • 兼容性模块工作正常")
        print("   • 配置值一致性良好")
        print("   • 所有访问模式可用")
        print("\n🚀 可以开始迁移现有代码！")
        return True
    else:
        print("⚠️ 配置迁移测试发现问题！")
        print("\n🔧 需要解决的问题:")
        if not compat_available:
            print("   • 兼容性模块不可用")
        if not consistent:
            print("   • 配置值不一致")
        print("\n💡 请检查配置映射和模块导入")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
