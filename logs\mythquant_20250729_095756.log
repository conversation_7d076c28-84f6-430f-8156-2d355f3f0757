2025-07-29 09:57:56,985 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250729_095756.log
2025-07-29 09:57:56,985 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-29 09:57:56,986 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-29 09:57:56,986 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-29 09:57:56,986 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-29 09:57:56,986 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-29 09:57:56,986 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-29 09:57:56,987 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-29 09:57:56,988 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-29 09:57:57,421 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-29 09:57:57,421 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 09:57:57,421 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 09:57:57,422 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-29 09:57:57,614 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4320条 (目标日期: 20250704, 交易日: 18天, 频率: 1min)
2025-07-29 09:57:57,614 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4320条 -> 实际请求800条 (配置限制:800)
2025-07-29 09:57:57,614 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 09:57:57,615 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 09:57:57,666 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3520条
2025-07-29 09:57:57,899 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-29 09:57:58,129 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-29 09:57:58,375 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-29 09:57:58,608 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-29 09:57:58,833 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +320条，总计4320条
2025-07-29 09:57:58,834 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4320条数据
2025-07-29 09:57:58,835 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要4320条，实际获取4320条
2025-07-29 09:57:58,847 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-29 09:57:58,863 - Main - INFO - info:307 - ℹ️ ✅ 数据保存完成: 1min_0_000617_20250704-20250704_来源互联网（202507290957）.txt (11331 字节)
2025-07-29 09:57:58,869 - Main - INFO - info:307 - ℹ️ ✅ 增量下载前提条件验证通过
2025-07-29 09:57:58,870 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-29 09:57:58,870 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-29 09:57:58,870 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-29 09:57:58,901 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数17211, 覆盖72个交易日
2025-07-29 09:57:58,901 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-07-29 09:57:58,901 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-07-29 09:57:58,901 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-07-29 09:57:58,901 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-07-29 09:57:58,901 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-07-29 09:57:58,902 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-07-29 09:57:58,902 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-07-29 09:57:58,902 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-07-29 09:57:58,902 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-07-29 09:57:58,902 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-07-29 09:57:58,902 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-29 09:57:58,904 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-29 09:57:58,904 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-29 09:57:58,906 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-07-29 09:57:58,906 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-07-29 09:57:58,906 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-29 09:57:58,930 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-07-29 09:57:59,162 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-07-29 09:57:59,166 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 09:57:59,166 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 09:57:59,166 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-29 09:57:59,343 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4320条 (目标日期: 20250704, 交易日: 18天, 频率: 1min)
2025-07-29 09:57:59,343 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4320条 -> 实际请求800条 (配置限制:800)
2025-07-29 09:57:59,343 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 09:57:59,343 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 09:57:59,394 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3520条
2025-07-29 09:57:59,622 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-29 09:57:59,850 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-29 09:58:00,077 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-29 09:58:00,309 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-29 09:58:00,531 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +320条，总计4320条
2025-07-29 09:58:00,531 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4320条数据
2025-07-29 09:58:00,532 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要4320条，实际获取4320条
2025-07-29 09:58:00,543 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-29 09:58:00,553 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-29 09:58:00,555 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-29 09:58:00,555 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250320 - 20250704
2025-07-29 09:58:00,556 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250319
2025-07-29 09:58:00,556 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-07-29 09:58:00,566 - Main - INFO - info:307 - ℹ️ 读取现有数据: 17211 条记录
2025-07-29 09:58:00,566 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250319
2025-07-29 09:58:00,566 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 09:58:00,566 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 09:58:00,566 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250319
2025-07-29 09:58:00,748 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32880条 (目标日期: 20250101, 交易日: 137天, 频率: 1min)
2025-07-29 09:58:00,748 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32880条 -> 实际请求800条 (配置限制:800)
2025-07-29 09:58:00,748 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 09:58:00,748 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 09:58:00,801 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32080条
2025-07-29 09:58:01,031 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-29 09:58:01,261 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-29 09:58:01,489 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-29 09:58:01,719 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-29 09:58:01,941 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-29 09:58:02,168 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-29 09:58:02,394 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-29 09:58:02,628 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-29 09:58:02,863 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-29 09:58:03,086 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-29 09:58:03,323 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-29 09:58:03,544 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-29 09:58:03,772 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-29 09:58:04,002 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-29 09:58:04,228 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-29 09:58:04,459 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-29 09:58:04,691 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-29 09:58:04,916 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-29 09:58:05,145 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-29 09:58:05,365 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-29 09:58:05,598 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-29 09:58:05,824 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-29 09:58:06,062 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-29 09:58:06,290 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-29 09:58:06,523 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-29 09:58:06,742 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-29 09:58:06,970 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +29条，总计21629条
2025-07-29 09:58:06,970 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21629条数据
2025-07-29 09:58:06,978 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32880条，实际获取21629条
2025-07-29 09:58:06,978 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11251条数据（约46个交易日）
2025-07-29 09:58:06,978 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-29 09:58:06,978 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-29 09:58:06,979 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-29 09:58:07,063 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 480 条 1min 数据
2025-07-29 09:58:07,078 - Main - INFO - info:307 - ℹ️ 获得新数据: 480 条记录
2025-07-29 09:58:07,078 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-07-29 09:58:07,078 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 09:58:07,078 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 09:58:07,078 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-07-29 09:58:07,251 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4080条 (目标日期: 20250705, 交易日: 17天, 频率: 1min)
2025-07-29 09:58:07,251 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4080条 -> 实际请求800条 (配置限制:800)
2025-07-29 09:58:07,251 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 09:58:07,251 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 09:58:07,301 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3280条
2025-07-29 09:58:07,531 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-29 09:58:07,761 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-29 09:58:07,990 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-29 09:58:08,223 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-29 09:58:08,452 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +80条，总计4080条
2025-07-29 09:58:08,452 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4080条数据
2025-07-29 09:58:08,453 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要4080条，实际获取4080条
2025-07-29 09:58:08,478 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-07-29 09:58:08,495 - Main - INFO - info:307 - ℹ️ 获得新数据: 3600 条记录
2025-07-29 09:58:08,498 - Main - INFO - info:307 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-07-29 09:58:08,514 - Main - INFO - info:307 - ℹ️ 合并后数据: 21291 条记录
2025-07-29 09:58:08,569 - Main - INFO - info:307 - ℹ️ 删除旧文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-07-29 09:58:08,569 - Main - INFO - info:307 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503180931-202507251500_来源互联网（202507290958）.txt
2025-07-29 09:58:08,570 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-29 09:58:08,570 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-29 09:58:08,571 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-29 09:58:08,571 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-29 09:58:08,572 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-29 09:58:08,572 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-29 09:58:08,572 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-29 09:58:08,573 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-29 09:58:08,573 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-29 09:58:08,573 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-29 09:58:08,575 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250729_095808.txt
2025-07-29 09:58:08,575 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-29 09:58:08,575 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-29 09:58:08,575 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-29 09:58:08,577 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-29 09:58:08,577 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-29 09:58:08,577 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-29 09:58:08,577 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-29 09:58:08,577 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-29 09:58:08,577 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-29 09:58:08,577 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-29 09:58:08,577 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
