#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置数据传输对象

用于在应用层和接口层之间传输配置数据
"""

from dataclasses import dataclass
from typing import List, Dict, Any, Optional
from src.mythquant.domain.config.entities.trading_config import TradingConfig
from src.mythquant.domain.config.entities.data_source_config import DataSourceConfig
from src.mythquant.domain.config.entities.processing_config import ProcessingConfig
from src.mythquant.domain.config.value_objects.tdx_connection import TdxConnection
from src.mythquant.domain.config.value_objects.file_path import FilePath


@dataclass
class TradingConfigDto:
    """交易配置数据传输对象"""
    
    # TDX连接信息
    tdx_path: str
    tdx_ip: str
    tdx_port: int
    
    # 输出路径
    output_path: str
    
    # 目标股票
    target_stocks_file: Optional[str] = None
    default_stocks: List[str] = None
    
    # GBBQ路径
    gbbq_path: Optional[str] = None
    
    # pytdx配置
    pytdx_auto_detect: bool = False
    pytdx_show_top5: bool = True
    pytdx_smart_detect: bool = True
    pytdx_blacklist_enabled: bool = True
    pytdx_blacklist_timeout: int = 7200
    kline_limits: Dict[str, int] = None
    data_buffer_factor: float = 1.0
    
    def __post_init__(self):
        if self.default_stocks is None:
            self.default_stocks = ["000617"]
        
        if self.kline_limits is None:
            self.kline_limits = {
                '1min': 800,
                '5min': 800,
                '15min': 800,
                '30min': 800,
                '60min': 800,
                'daily': 800,
            }
    
    @classmethod
    def from_entity(cls, entity: TradingConfig) -> 'TradingConfigDto':
        """从实体创建DTO"""
        return cls(
            tdx_path=entity.tdx_connection.path,
            tdx_ip=entity.tdx_connection.ip,
            tdx_port=entity.tdx_connection.port,
            output_path=entity.output_path.path,
            target_stocks_file=entity.target_stocks_file.path if entity.target_stocks_file else None,
            default_stocks=entity.default_stocks,
            gbbq_path=entity.gbbq_path,
            pytdx_auto_detect=entity.pytdx_config.get('auto_detect', False) if entity.pytdx_config else False,
            pytdx_show_top5=entity.pytdx_config.get('show_top5', True) if entity.pytdx_config else True,
            pytdx_smart_detect=entity.pytdx_config.get('smart_detect', True) if entity.pytdx_config else True,
            pytdx_blacklist_enabled=entity.pytdx_config.get('blacklist_enabled', True) if entity.pytdx_config else True,
            pytdx_blacklist_timeout=entity.pytdx_config.get('blacklist_timeout', 7200) if entity.pytdx_config else 7200,
            kline_limits=entity.pytdx_config.get('kline_limits', {}) if entity.pytdx_config else {},
            data_buffer_factor=entity.pytdx_config.get('data_buffer_factor', 1.0) if entity.pytdx_config else 1.0
        )
    
    def to_entity(self) -> TradingConfig:
        """转换为实体"""
        tdx_connection = TdxConnection(
            path=self.tdx_path,
            ip=self.tdx_ip,
            port=self.tdx_port
        )
        
        output_path = FilePath(self.output_path)
        
        target_stocks_file = FilePath(self.target_stocks_file) if self.target_stocks_file else None
        
        pytdx_config = {
            'auto_detect': self.pytdx_auto_detect,
            'show_top5': self.pytdx_show_top5,
            'smart_detect': self.pytdx_smart_detect,
            'blacklist_enabled': self.pytdx_blacklist_enabled,
            'blacklist_timeout': self.pytdx_blacklist_timeout,
            'kline_limits': self.kline_limits,
            'data_buffer_factor': self.data_buffer_factor
        }
        
        return TradingConfig(
            tdx_connection=tdx_connection,
            output_path=output_path,
            target_stocks_file=target_stocks_file,
            default_stocks=self.default_stocks,
            gbbq_path=self.gbbq_path,
            pytdx_config=pytdx_config
        )


@dataclass
class DataSourceConfigDto:
    """数据源配置数据传输对象"""
    
    # TDX连接信息
    tdx_path: str
    tdx_ip: str
    tdx_port: int
    
    # 网络配置
    connection_timeout: int = 30
    read_timeout: int = 60
    max_connections: int = 10
    keep_alive: bool = True
    user_agent: str = 'MythQuant/1.0'
    
    # 错误处理配置
    log_all_errors: bool = True
    raise_on_critical: bool = True
    continue_on_partial_failure: bool = True
    error_notification: bool = False
    
    # 监控配置
    track_api_usage: bool = True
    log_request_details: bool = False
    performance_metrics: bool = True
    success_rate_threshold: float = 0.8
    
    # 互联网数据源配置
    internet_sources_enabled: bool = True
    
    @classmethod
    def from_entity(cls, entity: DataSourceConfig) -> 'DataSourceConfigDto':
        """从实体创建DTO"""
        return cls(
            tdx_path=entity.tdx_connection.path,
            tdx_ip=entity.tdx_connection.ip,
            tdx_port=entity.tdx_connection.port,
            connection_timeout=entity.get_connection_timeout(),
            read_timeout=entity.get_read_timeout(),
            max_connections=entity.get_max_connections(),
            keep_alive=entity.network_config.get('keep_alive', True),
            user_agent=entity.network_config.get('user_agent', 'MythQuant/1.0'),
            log_all_errors=entity.is_error_logging_enabled(),
            raise_on_critical=entity.should_raise_on_critical(),
            continue_on_partial_failure=entity.should_continue_on_partial_failure(),
            error_notification=entity.error_handling.get('error_notification', False),
            track_api_usage=entity.monitoring.get('track_api_usage', True),
            log_request_details=entity.monitoring.get('log_request_details', False),
            performance_metrics=entity.is_performance_monitoring_enabled(),
            success_rate_threshold=entity.get_success_rate_threshold(),
            internet_sources_enabled=entity.is_internet_source_enabled()
        )
    
    def to_entity(self) -> DataSourceConfig:
        """转换为实体"""
        tdx_connection = TdxConnection(
            path=self.tdx_path,
            ip=self.tdx_ip,
            port=self.tdx_port
        )
        
        network_config = {
            'connection_timeout': self.connection_timeout,
            'read_timeout': self.read_timeout,
            'max_connections': self.max_connections,
            'keep_alive': self.keep_alive,
            'user_agent': self.user_agent
        }
        
        error_handling = {
            'log_all_errors': self.log_all_errors,
            'raise_on_critical': self.raise_on_critical,
            'continue_on_partial_failure': self.continue_on_partial_failure,
            'error_notification': self.error_notification
        }
        
        monitoring = {
            'track_api_usage': self.track_api_usage,
            'log_request_details': self.log_request_details,
            'performance_metrics': self.performance_metrics,
            'success_rate_threshold': self.success_rate_threshold
        }
        
        internet_sources = {
            'enabled': self.internet_sources_enabled
        }
        
        return DataSourceConfig(
            tdx_connection=tdx_connection,
            network_config=network_config,
            error_handling=error_handling,
            monitoring=monitoring,
            internet_sources=internet_sources
        )


@dataclass
class ProcessingConfigDto:
    """数据处理配置数据传输对象"""

    # 智能文件选择器配置
    smart_file_selector_enabled: bool = True
    file_selector_strategy: str = 'smart_comprehensive'
    freshness_weight: float = 0.3
    coverage_weight: float = 0.4
    match_weight: float = 0.3
    auto_resolve_conflicts: bool = True
    prefer_newer_files: bool = True
    min_score_threshold: float = 0.6

    # 数据处理配置
    transparent_processing_enabled: bool = True
    auto_backup_enabled: bool = True
    validation_enabled: bool = True
    quality_verification_enabled: bool = True
    strict_mode: bool = False
    auto_fix_enabled: bool = True

    # 智能功能配置
    incremental_download_enabled: bool = True
    auto_detect_incremental: bool = True
    incremental_validation_enabled: bool = True
    missing_data_processor_enabled: bool = True
    auto_fill_missing_data: bool = False
    missing_data_notification_enabled: bool = True

    # 用户界面配置
    display_level: str = 'normal'
    progress_bar_enabled: bool = True
    color_output_enabled: bool = True
    emoji_enabled: bool = True

    # 详细模式配置
    verbose_mode_enabled: bool = False
    show_forward_adj_details: bool = False
    show_performance_warnings: bool = False
    show_data_processing_steps: bool = False
    show_cache_status: bool = False
    highlight_critical_info: bool = False
    show_detailed_calculations: bool = False

    # 调试配置
    debug_enabled: bool = False

    @classmethod
    def from_entity(cls, entity: ProcessingConfig) -> 'ProcessingConfigDto':
        """从实体创建DTO"""
        scoring_weights = entity.get_scoring_weights()

        return cls(
            smart_file_selector_enabled=entity.is_smart_file_selector_enabled(),
            file_selector_strategy=entity.get_file_selector_strategy(),
            freshness_weight=scoring_weights.get('freshness_weight', 0.3),
            coverage_weight=scoring_weights.get('coverage_weight', 0.4),
            match_weight=scoring_weights.get('match_weight', 0.3),
            auto_resolve_conflicts=entity.smart_file_selector.get('conflict_resolution', {}).get('auto_resolve', True),
            prefer_newer_files=entity.smart_file_selector.get('conflict_resolution', {}).get('prefer_newer', True),
            min_score_threshold=entity.smart_file_selector.get('conflict_resolution', {}).get('min_score_threshold', 0.6),
            transparent_processing_enabled=entity.is_transparent_processing_enabled(),
            auto_backup_enabled=entity.is_auto_backup_enabled(),
            validation_enabled=entity.data_processing.get('transparent_processing', {}).get('validation_enabled', True),
            quality_verification_enabled=entity.is_quality_verification_enabled(),
            strict_mode=entity.data_processing.get('quality_verification', {}).get('strict_mode', False),
            auto_fix_enabled=entity.data_processing.get('quality_verification', {}).get('auto_fix', True),
            incremental_download_enabled=entity.is_incremental_download_enabled(),
            auto_detect_incremental=entity.intelligent_features.get('incremental_download', {}).get('auto_detect', True),
            incremental_validation_enabled=entity.intelligent_features.get('incremental_download', {}).get('validation_enabled', True),
            missing_data_processor_enabled=entity.is_missing_data_processor_enabled(),
            auto_fill_missing_data=entity.intelligent_features.get('missing_data_processor', {}).get('auto_fill', False),
            missing_data_notification_enabled=entity.intelligent_features.get('missing_data_processor', {}).get('notification_enabled', True),
            display_level=entity.get_display_level(),
            progress_bar_enabled=entity.is_progress_bar_enabled(),
            color_output_enabled=entity.user_interface.get('color_output', True),
            emoji_enabled=entity.is_emoji_enabled(),
            verbose_mode_enabled=entity.is_verbose_mode_enabled(),
            show_forward_adj_details=entity.should_show_forward_adj_details(),
            show_performance_warnings=entity.verbose_mode.get('show_performance_warnings', False) if entity.verbose_mode else False,
            show_data_processing_steps=entity.verbose_mode.get('show_data_processing_steps', False) if entity.verbose_mode else False,
            show_cache_status=entity.verbose_mode.get('show_cache_status', False) if entity.verbose_mode else False,
            highlight_critical_info=entity.verbose_mode.get('highlight_critical_info', False) if entity.verbose_mode else False,
            show_detailed_calculations=entity.verbose_mode.get('show_detailed_calculations', False) if entity.verbose_mode else False,
            debug_enabled=entity.debug_enabled
        )

    def to_entity(self) -> ProcessingConfig:
        """转换为实体"""
        smart_file_selector = {
            'enabled': self.smart_file_selector_enabled,
            'default_strategy': self.file_selector_strategy,
            'scoring_weights': {
                'freshness_weight': self.freshness_weight,
                'coverage_weight': self.coverage_weight,
                'match_weight': self.match_weight,
            },
            'conflict_resolution': {
                'auto_resolve': self.auto_resolve_conflicts,
                'prefer_newer': self.prefer_newer_files,
                'min_score_threshold': self.min_score_threshold,
            }
        }

        data_processing = {
            'transparent_processing': {
                'enabled': self.transparent_processing_enabled,
                'auto_backup': self.auto_backup_enabled,
                'validation_enabled': self.validation_enabled,
            },
            'quality_verification': {
                'enabled': self.quality_verification_enabled,
                'strict_mode': self.strict_mode,
                'auto_fix': self.auto_fix_enabled,
            }
        }

        intelligent_features = {
            'incremental_download': {
                'enabled': self.incremental_download_enabled,
                'auto_detect': self.auto_detect_incremental,
                'validation_enabled': self.incremental_validation_enabled,
            },
            'missing_data_processor': {
                'enabled': self.missing_data_processor_enabled,
                'auto_fill': self.auto_fill_missing_data,
                'notification_enabled': self.missing_data_notification_enabled,
            }
        }

        user_interface = {
            'display_level': self.display_level,
            'progress_bar': self.progress_bar_enabled,
            'color_output': self.color_output_enabled,
            'emoji_enabled': self.emoji_enabled,
        }

        verbose_mode = {
            'enabled': self.verbose_mode_enabled,
            'show_forward_adj_details': self.show_forward_adj_details,
            'show_performance_warnings': self.show_performance_warnings,
            'show_data_processing_steps': self.show_data_processing_steps,
            'show_cache_status': self.show_cache_status,
            'highlight_critical_info': self.highlight_critical_info,
            'show_detailed_calculations': self.show_detailed_calculations,
        }

        return ProcessingConfig(
            smart_file_selector=smart_file_selector,
            data_processing=data_processing,
            intelligent_features=intelligent_features,
            user_interface=user_interface,
            verbose_mode=verbose_mode,
            debug_enabled=self.debug_enabled
        )


@dataclass
class TaskConfigDto:
    """任务配置数据传输对象"""

    name: str
    data_type: str
    enabled: bool
    start_time: str
    end_time: str
    use_multithread: bool = True
    max_workers: int = 4
    frequency: str = '1min'
    use_structured_flow: bool = False

    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'name': self.name,
            'data_type': self.data_type,
            'enabled': self.enabled,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'use_multithread': self.use_multithread,
            'max_workers': self.max_workers,
            'frequency': self.frequency,
            'use_structured_flow': self.use_structured_flow
        }

    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'TaskConfigDto':
        """从字典创建DTO"""
        return cls(
            name=data['name'],
            data_type=data['data_type'],
            enabled=data['enabled'],
            start_time=data['start_time'],
            end_time=data['end_time'],
            use_multithread=data.get('use_multithread', True),
            max_workers=data.get('max_workers', 4),
            frequency=data.get('frequency', '1min'),
            use_structured_flow=data.get('use_structured_flow', False)
        )
