# MythQuant 架构文档

## 架构概述

MythQuant 采用现代化的分层架构设计，实现了从扁平化结构到企业级架构的全面升级。

## 整体架构

```
┌─────────────────────────────────────────────────────────────┐
│                    用户接口层 (User Interface)                │
├─────────────────────────────────────────────────────────────┤
│                   兼容性层 (Compatibility)                   │
├─────────────────────────────────────────────────────────────┤
│                    应用层 (Application)                     │
├─────────────────────────────────────────────────────────────┤
│     算法层        │    IO层     │    配置层    │    核心层    │
│  (Algorithms)    │    (IO)     │  (Config)   │   (Core)    │
├─────────────────────────────────────────────────────────────┤
│                    数据层 (Data Sources)                    │
├─────────────────────────────────────────────────────────────┤
│                   基础设施层 (Infrastructure)                │
└─────────────────────────────────────────────────────────────┘
```

## 分层详解

### 1. 用户接口层 (User Interface)

**职责**: 提供用户交互接口和API

**组件**:
- 命令行接口 (CLI)
- Python API
- 配置文件接口

**特点**:
- 简洁易用的API设计
- 完整的错误处理和用户反馈
- 支持批量操作和自动化

### 2. 兼容性层 (Compatibility)

**职责**: 确保向后兼容性，桥接新旧架构

**核心模块**:
```
compatibility/
├── config_compatibility.py      # 配置兼容性
├── data_access_compatibility.py # 数据访问兼容性
├── algorithm_compatibility.py   # 算法兼容性
└── io_compatibility.py         # IO兼容性
```

**设计原则**:
- 100% 向后兼容
- 自动回退机制
- 透明的新旧架构切换

### 3. 应用层 (Application)

**职责**: 业务逻辑协调和流程控制

**核心组件**:
```python
from mythquant.core import MythQuantApplication

app = MythQuantApplication(config_manager)
result = app.process_stock_data("000001", "day")
```

**功能**:
- 统一的业务流程管理
- 组件间协调
- 错误处理和日志记录

### 4. 算法层 (Algorithms)

**职责**: 核心算法实现和计算逻辑

**模块结构**:
```
mythquant/algorithms/
├── __init__.py
├── forward_adjustment.py    # 前复权计算
├── l2_metrics.py           # L2指标计算
├── buy_sell_calculator.py  # 主买主卖计算
└── technical_indicators.py # 技术指标计算
```

**设计特点**:
- 高精度 Decimal 计算
- 模块化算法组件
- 可扩展的算法框架
- 完整的数据验证

### 5. IO层 (Input/Output)

**职责**: 数据输入输出和格式化

**模块结构**:
```
mythquant/io/
├── __init__.py
├── file_manager.py     # 文件管理
├── data_formatter.py   # 数据格式化
├── output_writer.py    # 输出写入
└── excel_handler.py    # Excel处理
```

**功能特性**:
- 统一的文件操作接口
- 标准化的数据格式
- 多种输出格式支持
- 自动备份和恢复

### 6. 配置层 (Configuration)

**职责**: 统一配置管理和验证

**模块结构**:
```
mythquant/config/
├── __init__.py
├── manager.py      # 配置管理器
└── validators.py   # 配置验证器
```

**特性**:
- 类型安全的配置管理
- 环境变量支持
- 配置验证和默认值
- 热重载支持

### 7. 数据层 (Data Sources)

**职责**: 多数据源管理和数据访问

**模块结构**:
```
mythquant/data/sources/
├── __init__.py
├── manager.py          # 数据源管理器
├── tdx_source.py      # TDX数据源
├── pytdx_source.py    # PyTDX数据源
└── internet_source.py # 互联网数据源
```

**核心特性**:
- 多数据源统一接口
- 自动回退机制
- 连通性检测
- 数据质量验证

### 8. 核心层 (Core)

**职责**: 系统核心功能和基础服务

**组件**:
- 应用程序主控制器
- 任务管理器
- 异常处理器
- 日志管理器

## 设计模式

### 1. 策略模式 (Strategy Pattern)

**应用**: 数据源选择和算法切换

```python
class DataSourceManager:
    def __init__(self):
        self.sources = {
            DataSourceType.TDX: TdxDataSource(),
            DataSourceType.PYTDX: PyTdxDataSource(),
            DataSourceType.INTERNET: InternetDataSource()
        }
    
    def get_data(self, source_type):
        return self.sources[source_type].fetch_data()
```

### 2. 工厂模式 (Factory Pattern)

**应用**: 算法计算器创建

```python
class AlgorithmFactory:
    @staticmethod
    def create_calculator(algorithm_type, config_manager):
        if algorithm_type == "l2_metrics":
            return L2MetricsCalculator(config_manager)
        elif algorithm_type == "forward_adjustment":
            return ForwardAdjustmentCalculator(config_manager)
```

### 3. 观察者模式 (Observer Pattern)

**应用**: 配置变更通知

```python
class ConfigManager:
    def __init__(self):
        self.observers = []
    
    def notify_config_change(self, key, value):
        for observer in self.observers:
            observer.on_config_changed(key, value)
```

### 4. 装饰器模式 (Decorator Pattern)

**应用**: 性能监控和日志记录

```python
def performance_monitor(func):
    def wrapper(*args, **kwargs):
        start_time = time.time()
        result = func(*args, **kwargs)
        end_time = time.time()
        logger.info(f"{func.__name__} took {end_time - start_time:.3f}s")
        return result
    return wrapper
```

## 数据流架构

### 典型数据处理流程

```
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   用户请求   │───▶│  配置加载   │───▶│  数据获取   │
└─────────────┘    └─────────────┘    └─────────────┘
                                              │
┌─────────────┐    ┌─────────────┐    ┌─────────────┐
│   结果输出   │◀───│  算法计算   │◀───│  数据验证   │
└─────────────┘    └─────────────┘    └─────────────┘
```

### 数据流详解

1. **用户请求** → 接收用户输入和参数
2. **配置加载** → 加载系统配置和用户设置
3. **数据获取** → 从多数据源获取股票数据
4. **数据验证** → 验证数据完整性和格式
5. **算法计算** → 执行L2指标、前复权等计算
6. **结果输出** → 格式化并输出结果文件

## 错误处理架构

### 分层错误处理

```python
class ErrorHandler:
    def __init__(self):
        self.handlers = {
            ConfigError: self.handle_config_error,
            DataSourceError: self.handle_data_source_error,
            AlgorithmError: self.handle_algorithm_error,
            IOError: self.handle_io_error
        }
    
    def handle_error(self, error):
        handler = self.handlers.get(type(error))
        if handler:
            return handler(error)
        else:
            return self.handle_unknown_error(error)
```

### 错误分类

1. **系统错误** - 配置错误、环境问题
2. **业务错误** - 数据格式错误、计算异常
3. **数据访问错误** - 数据源连接失败
4. **IO错误** - 文件读写权限问题

## 性能架构

### 性能优化策略

1. **计算优化**
   - 使用 Decimal 进行高精度计算
   - 向量化操作替代循环
   - 智能缓存机制

2. **内存优化**
   - 分块处理大数据集
   - 及时释放不需要的对象
   - 使用生成器减少内存占用

3. **IO优化**
   - 异步文件操作
   - 批量写入减少IO次数
   - 压缩存储节省空间

### 性能监控

```python
class PerformanceMonitor:
    def __init__(self):
        self.metrics = {}
    
    def record_execution_time(self, function_name, duration):
        if function_name not in self.metrics:
            self.metrics[function_name] = []
        self.metrics[function_name].append(duration)
    
    def get_performance_report(self):
        report = {}
        for func, times in self.metrics.items():
            report[func] = {
                'avg_time': sum(times) / len(times),
                'max_time': max(times),
                'min_time': min(times),
                'call_count': len(times)
            }
        return report
```

## 扩展性架构

### 插件化设计

```python
class PluginManager:
    def __init__(self):
        self.plugins = {}
    
    def register_plugin(self, name, plugin):
        self.plugins[name] = plugin
    
    def execute_plugin(self, name, *args, **kwargs):
        if name in self.plugins:
            return self.plugins[name].execute(*args, **kwargs)
```

### 新算法集成

添加新算法只需：

1. 继承基础算法类
2. 实现必要的接口方法
3. 注册到算法工厂
4. 更新兼容性模块

```python
class CustomAlgorithm(BaseAlgorithm):
    def calculate(self, data):
        # 自定义算法实现
        pass
    
    def validate_input(self, data):
        # 输入验证
        pass
```

## 测试架构

### 测试分层

```
tests/
├── unit/           # 单元测试
├── integration/    # 集成测试
├── performance/    # 性能测试
├── fixtures/       # 测试夹具
└── utils/          # 测试工具
```

### 测试策略

1. **单元测试** - 测试单个模块功能
2. **集成测试** - 测试模块间协作
3. **性能测试** - 验证性能指标
4. **兼容性测试** - 确保向后兼容

## 部署架构

### 环境配置

```
environments/
├── development/    # 开发环境
├── testing/       # 测试环境
├── staging/       # 预发布环境
└── production/    # 生产环境
```

### 配置管理

- 环境变量支持
- 配置文件分层
- 敏感信息加密
- 配置验证机制

## 安全架构

### 安全措施

1. **输入验证** - 严格的参数验证
2. **权限控制** - 文件访问权限检查
3. **错误处理** - 安全的错误信息显示
4. **日志安全** - 敏感信息脱敏

### 数据保护

```python
class DataProtector:
    def sanitize_log_data(self, data):
        # 移除敏感信息
        sanitized = data.copy()
        sensitive_fields = ['password', 'token', 'key']
        for field in sensitive_fields:
            if field in sanitized:
                sanitized[field] = '***'
        return sanitized
```

## 监控架构

### 系统监控

1. **性能监控** - 执行时间、内存使用
2. **错误监控** - 异常统计和分析
3. **业务监控** - 数据处理成功率
4. **资源监控** - 磁盘空间、网络状态

### 监控指标

```python
class SystemMonitor:
    def collect_metrics(self):
        return {
            'cpu_usage': self.get_cpu_usage(),
            'memory_usage': self.get_memory_usage(),
            'disk_usage': self.get_disk_usage(),
            'active_connections': self.get_connection_count(),
            'error_rate': self.get_error_rate(),
            'throughput': self.get_throughput()
        }
```

## 未来架构演进

### 短期规划

1. **微服务化** - 将核心模块拆分为独立服务
2. **容器化** - Docker 容器化部署
3. **API网关** - 统一API管理和路由
4. **服务发现** - 自动化服务注册和发现

### 长期愿景

1. **云原生** - 支持云平台部署
2. **分布式计算** - 支持大规模并行计算
3. **实时处理** - 流式数据处理能力
4. **AI集成** - 机器学习算法集成

## 总结

MythQuant 的新架构具有以下优势：

- ✅ **模块化设计** - 高内聚低耦合
- ✅ **可扩展性** - 易于添加新功能
- ✅ **可维护性** - 清晰的代码结构
- ✅ **高性能** - 优化的计算和IO
- ✅ **高可靠性** - 完善的错误处理
- ✅ **向后兼容** - 保护现有投资

这个架构为 MythQuant 的长期发展奠定了坚实的基础。
