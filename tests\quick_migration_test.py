#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速迁移测试

验证配置迁移和新架构的基本功能
"""

import sys
import os
from pathlib import Path

def test_config_access():
    """测试配置访问"""
    print("🔍 测试配置访问...")
    
    try:
        import config_compatibility as config
        
        # 基本配置测试
        configs = {
            'TDX路径': config.tdx_path,
            '调试模式': config.DEBUG,
            '输出路径': config.output_path,
            '智能选择器': config.smart_file_selector_enabled
        }
        
        print("   📋 配置值:")
        for name, value in configs.items():
            print(f"      {name}: {value}")
        
        # 函数访问测试
        tdx_func = config.get_tdx_path()
        debug_func = config.is_debug_enabled()
        
        print("   🔧 函数访问:")
        print(f"      get_tdx_path(): {tdx_func}")
        print(f"      is_debug_enabled(): {debug_func}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 配置访问失败: {e}")
        return False

def test_new_architecture():
    """测试新架构"""
    print("\n🔍 测试新架构...")
    
    try:
        # 添加src路径
        project_root = Path(__file__).parent
        src_path = project_root / "src"
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        # 测试新配置系统
        from mythquant.config import config_manager
        print("   ✅ 新配置系统导入成功")
        
        # 测试配置访问
        tdx_path = config_manager.get_tdx_path()
        debug_mode = config_manager.is_debug_enabled()
        print(f"   📋 新配置TDX路径: {tdx_path}")
        print(f"   📋 新配置调试模式: {debug_mode}")
        
        # 测试数据源
        from mythquant.data.sources import DataSourceManager
        data_manager = DataSourceManager(config_manager)
        print("   ✅ 数据源管理器创建成功")
        
        # 测试核心模块
        from mythquant.core import MythQuantApplication
        app = MythQuantApplication(config_manager)
        print("   ✅ 应用程序创建成功")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 新架构测试失败: {e}")
        return False

def test_file_migration_status():
    """测试文件迁移状态"""
    print("\n🔍 检查文件迁移状态...")
    
    files_to_check = [
        "main.py",
        "func_Tdx.py",
        "func_Util.py"
    ]
    
    migration_status = {}
    
    for file_name in files_to_check:
        file_path = Path(file_name)
        if not file_path.exists():
            migration_status[file_name] = "不存在"
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            if 'config_compatibility' in content:
                migration_status[file_name] = "已迁移"
            elif 'mythquant.config' in content:
                migration_status[file_name] = "新架构"
            elif 'user_config' in content:
                migration_status[file_name] = "需迁移"
            else:
                migration_status[file_name] = "无配置"
            
        except Exception as e:
            migration_status[file_name] = f"读取错误: {e}"
    
    print("   📊 文件迁移状态:")
    for file_name, status in migration_status.items():
        if status == "已迁移" or status == "新架构":
            print(f"      ✅ {file_name}: {status}")
        elif status == "需迁移":
            print(f"      ⚠️ {file_name}: {status}")
        else:
            print(f"      ℹ️ {file_name}: {status}")
    
    migrated_count = sum(1 for status in migration_status.values() 
                        if status in ["已迁移", "新架构"])
    total_count = len(migration_status)
    
    return migrated_count, total_count

def main():
    """主测试函数"""
    print("🧪 MythQuant 快速迁移测试")
    print("=" * 40)
    
    # 执行测试
    config_ok = test_config_access()
    arch_ok = test_new_architecture()
    migrated_count, total_count = test_file_migration_status()
    
    # 汇总结果
    print("\n" + "=" * 40)
    print("📊 测试结果:")
    
    print(f"   配置访问: {'✅ 正常' if config_ok else '❌ 异常'}")
    print(f"   新架构: {'✅ 正常' if arch_ok else '❌ 异常'}")
    print(f"   文件迁移: {migrated_count}/{total_count} 已迁移")
    
    # 判断整体状态
    overall_success = config_ok and arch_ok and (migrated_count >= total_count * 0.8)
    
    print(f"\n🎯 整体状态: {'✅ 良好' if overall_success else '⚠️ 需要关注'}")
    
    if overall_success:
        print("\n🚀 迁移状态良好，可以继续数据访问层迁移！")
        print("\n📋 下一步:")
        print("   1. 开始数据访问层迁移")
        print("   2. 测试数据读取功能")
        print("   3. 验证数据一致性")
    else:
        print("\n🔧 需要先解决以下问题:")
        if not config_ok:
            print("   • 配置访问异常")
        if not arch_ok:
            print("   • 新架构异常")
        if migrated_count < total_count * 0.8:
            print("   • 文件迁移不完整")
    
    return overall_success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
