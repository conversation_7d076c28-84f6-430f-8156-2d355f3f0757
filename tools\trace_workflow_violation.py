#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trace workflow violation: find pytdx calls before step 1
"""

import os
import sys

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# Global state tracking
workflow_started = False
step1_started = False
violation_calls = []

def install_comprehensive_patches():
    """Install comprehensive patches to track workflow violation"""
    global workflow_started, step1_started, violation_calls
    
    try:
        # 1. Patch TaskManager to track workflow start
        from src.mythquant.core.task_manager import TaskManager
        
        original_execute_minute = TaskManager._execute_minute_task
        
        def patched_execute_minute(self, task, target_stocks):
            """Track when workflow starts"""
            global workflow_started
            print(f"\n=== WORKFLOW START MARKER ===")
            print(f"TaskManager._execute_minute_task called")
            workflow_started = True
            
            result = original_execute_minute(self, task, target_stocks)
            
            print(f"=== WORKFLOW END MARKER ===")
            workflow_started = False
            step1_started = False
            
            return result
        
        TaskManager._execute_minute_task = patched_execute_minute
        
        # 2. Patch structured downloader to track step 1 start
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        original_step1 = StructuredInternetMinuteDownloader._step1_smart_file_selection
        
        def patched_step1(self, stock_code, start_date, end_date):
            """Track when step 1 starts"""
            global step1_started
            print(f"\n=== STEP 1 START MARKER ===")
            print(f"_step1_smart_file_selection called for {stock_code}")
            step1_started = True
            
            return original_step1(self, stock_code, start_date, end_date)
        
        StructuredInternetMinuteDownloader._step1_smart_file_selection = patched_step1
        
        # 3. Patch pytdx downloader to catch violations
        from utils.pytdx_downloader import PytdxDownloader
        
        original_download = PytdxDownloader.download_minute_data
        
        def patched_download(self, stock_code, start_date, end_date, frequency='1min'):
            """Catch pytdx calls before step 1"""
            global workflow_started, step1_started, violation_calls
            
            import traceback
            
            # Check if this is a workflow violation
            is_violation = workflow_started and not step1_started
            
            if is_violation:
                print(f"\n*** WORKFLOW VIOLATION DETECTED ***")
                print(f"PytdxDownloader called BEFORE step 1!")
                print(f"Stock: {stock_code}")
                print(f"Date Range: {start_date} - {end_date}")
                print(f"Frequency: {frequency}")
                print(f"Workflow started: {workflow_started}")
                print(f"Step 1 started: {step1_started}")
                
                print(f"\nVIOLATION Call stack:")
                stack = traceback.format_stack()
                for i, frame in enumerate(stack, 1):
                    if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'structured_internet', 'stock_data_downloader']):
                        print(f">>> {i:2d}. {frame.strip()}")
                    else:
                        print(f"    {i:2d}. {frame.strip()}")
                
                violation_calls.append({
                    'stock_code': stock_code,
                    'date_range': f"{start_date}-{end_date}",
                    'frequency': frequency,
                    'stack': stack
                })
                
                print("=" * 120)
            else:
                print(f"\n--- Normal pytdx call ---")
                print(f"Stock: {stock_code}, Range: {start_date}-{end_date}")
                print(f"Workflow: {workflow_started}, Step1: {step1_started}")
            
            return original_download(self, stock_code, start_date, end_date, frequency)
        
        PytdxDownloader.download_minute_data = patched_download
        
        print("Comprehensive workflow violation patches installed")
        return True
        
    except Exception as e:
        print(f"Patch installation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_test():
    """Run the test"""
    global violation_calls
    
    print("\nRunning main.py with workflow violation detection...")
    
    try:
        from main import main
        result = main()
        
        print(f"\nMain completed with result: {result}")
        print(f"Total workflow violations detected: {len(violation_calls)}")
        
        if violation_calls:
            print(f"\n*** WORKFLOW VIOLATIONS SUMMARY ***")
            for i, violation in enumerate(violation_calls, 1):
                print(f"Violation #{i}:")
                print(f"  Stock: {violation['stock_code']}")
                print(f"  Range: {violation['date_range']}")
                print(f"  Frequency: {violation['frequency']}")
                print(f"  Stack depth: {len(violation['stack'])}")
                print()
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Workflow Violation Detector")
    print("=" * 80)
    print("Detecting pytdx calls BEFORE step 1 (智能文件选择和分析)")
    print("=" * 80)
    
    if not install_comprehensive_patches():
        return 1
    
    success = run_test()
    
    print(f"\nDetection completed: {'SUCCESS' if success else 'FAILED'}")
    
    if violation_calls:
        print(f"\n🚨 WORKFLOW VIOLATIONS FOUND: {len(violation_calls)}")
        print("These calls violate the strict 4-step workflow!")
    else:
        print(f"\n✅ NO WORKFLOW VIOLATIONS DETECTED")
        print("All pytdx calls occur after step 1 starts")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
