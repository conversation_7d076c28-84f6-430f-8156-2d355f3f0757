#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结构化输出格式演示

展示新的结构化输出格式器的效果

作者: AI Assistant
创建时间: 2025-07-30
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from utils.structured_output_formatter import StructuredOutputFormatter


def demo_structured_output():
    """演示结构化输出格式"""
    formatter = StructuredOutputFormatter()
    
    # 1. 程序横幅
    formatter.print_banner(
        "MythQuant 量化交易数据处理系统",
        "前复权数据生成 & L2指标计算 | 结构化输出演示"
    )
    
    # 2. 主要流程
    formatter.print_main_process("应用程序初始化")
    formatter.print_info("加载配置文件", level=1)
    formatter.print_info("初始化日志系统", level=1)
    formatter.print_result("初始化完成", True, level=1)
    
    # 3. 子流程
    formatter.print_sub_process("1分钟数据处理工作流程", 1, 2)
    
    # 4. 四步流程演示
    formatter.print_step("智能文件选择和分析", 1, 4)
    formatter.print_action("加载智能文件选择器配置")
    formatter.print_result("找到候选文件: 1min_0_000617_20250320-20250704.txt", True)
    formatter.print_data_info("时间范围", "20250320 ~ 20250704", level=3)
    formatter.print_data_info("覆盖天数", "106天", level=3)
    formatter.print_data_info("质量评分", "85.6", level=3)
    
    formatter.print_step("增量下载前提条件判断", 2, 4)
    formatter.print_action("执行价格比较验证")
    formatter.print_info("文件最后时间: 202507041447", level=2)
    formatter.print_info("文件收盘价: 7.550", level=2)
    formatter.print_warning("API数据获取失败（网络问题）")
    formatter.print_result("增量下载条件: 不满足", False)
    
    formatter.print_step("数据质量检查与修复", 3, 4)
    formatter.print_action("检测缺失数据")
    formatter.print_data_info("总记录数", "17,212", level=2)
    formatter.print_data_info("预期记录数", "18,000", level=2)
    formatter.print_warning("数据完整性: 95.6%")
    formatter.print_result("质量检查完成", True)
    
    formatter.print_step("增量数据下载", 4, 4)
    formatter.print_action("执行pytdx数据下载")
    formatter.print_progress("正在下载数据...")
    formatter.print_info("分批获取成功: +800条，总计21600条", level=2)
    formatter.print_result("数据下载完成", True)
    
    # 5. 统计表格
    stats = {
        "执行任务数": 2,
        "成功任务数": 1,
        "成功率": "50.0%",
        "总执行时间": "7.21秒"
    }
    formatter.print_stats_table("执行统计", stats)
    
    # 6. 汇总信息
    summary_items = [
        {"name": "互联网分钟级数据下载", "status": "success"},
        {"name": "前复权数据比较分析", "status": "failed"}
    ]
    formatter.print_summary("任务执行结果", summary_items)
    
    # 7. 完成信息
    completion_stats = {
        "总执行时间": "7.21 秒",
        "执行时间段": "22:35:17 - 22:35:24"
    }
    formatter.print_completion("程序执行完成，但部分任务失败", False, completion_stats)


def show_format_comparison():
    """显示格式对比"""
    print("\n" + "=" * 100)
    print("📊 输出格式改进对比")
    print("=" * 100)
    
    print("\n❌ 旧格式问题:")
    print("   🔍 符号混乱: 🔍📊✅❌⚖️🔧📥等随意使用")
    print("   📐 缩进不一: 有些3空格，有些4空格，有些无缩进")
    print("   🌀 层级混乱: 主流程、子流程、详细信息混在一起")
    print("   🔄 信息冗余: 重复显示相似信息")
    print("   🔀 日志混合: logger和print信息交织")
    
    print("\n✅ 新格式优势:")
    print("   🎯 统一符号: 🚀主流程 📊子流程 🔍步骤 ⚡操作 ✅成功 ❌错误")
    print("   📏 规范缩进: level_0(0) level_1(2) level_2(4) level_3(6) level_4(8)")
    print("   🏗️ 清晰层级: 横幅→主流程→子流程→步骤→操作→结果")
    print("   🎪 信息精简: 每条信息都有明确价值和目的")
    print("   🔧 关注分离: print专注界面，logger专注调试")
    
    print("\n🚀 用户体验提升:")
    print("   👁️ 可读性: 层次分明，信息结构清晰")
    print("   💼 专业性: 统一格式，提升专业形象")
    print("   🎯 信息密度: 关键信息突出，次要信息简化")
    print("   😌 视觉舒适: 减少符号混乱和信息疲劳")


if __name__ == '__main__':
    print("🎭 结构化输出格式演示")
    print("=" * 100)
    print(f"📅 演示时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 目标: 展示新的结构化输出格式的效果")
    print("=" * 100)
    
    # 演示结构化输出
    demo_structured_output()
    
    # 显示对比分析
    show_format_comparison()
    
    print("\n" + "=" * 100)
    print("🎉 结构化输出格式演示完成！")
    print("💡 新格式显著提升了用户体验和系统专业性")
    print("🚀 建议在所有模块中统一采用新的输出格式")
    print("=" * 100)
