#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试FileInfo错误

重现和修复'FileInfo' object has no attribute 'actual_start'错误

作者: AI Assistant
创建时间: 2025-07-30
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_fileinfo_creation():
    """测试FileInfo对象创建"""
    print("🔍 测试FileInfo对象创建")
    print("-" * 50)
    
    try:
        from utils.smart_file_selector import SmartFileSelector, FileInfo
        
        # 创建智能文件选择器
        selector = SmartFileSelector("test_environments/minute_data_tests/output_data")
        print("✅ SmartFileSelector 创建成功")
        
        # 测试文件
        test_file = "test_environments/minute_data_tests/input_data/1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
        
        if os.path.exists(test_file):
            print(f"📁 测试文件存在: {os.path.basename(test_file)}")
            
            # 分析文件信息
            print("🔍 开始分析文件...")
            file_info = selector.analyze_file(test_file, "20250320", "20250704")
            
            if file_info:
                print("✅ 文件分析成功")
                print(f"   类型: {type(file_info)}")
                print(f"   属性: {dir(file_info)}")
                
                # 检查关键属性
                attrs_to_check = ['start_date', 'end_date', 'coverage_days', 'total_score']
                for attr in attrs_to_check:
                    if hasattr(file_info, attr):
                        value = getattr(file_info, attr)
                        print(f"   ✅ {attr}: {value}")
                    else:
                        print(f"   ❌ 缺少属性: {attr}")
                
                # 检查是否有actual_start属性
                if hasattr(file_info, 'actual_start'):
                    print(f"   ⚠️ 意外发现actual_start属性: {file_info.actual_start}")
                else:
                    print(f"   ✅ 确认没有actual_start属性")
                
                return True
            else:
                print("❌ 文件分析失败")
                return False
        else:
            print(f"⚠️ 测试文件不存在: {test_file}")
            return True
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_structured_downloader():
    """测试结构化下载器"""
    print("\n🚀 测试结构化下载器")
    print("-" * 50)
    
    try:
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        # 创建下载器
        downloader = StructuredInternetMinuteDownloader()
        print("✅ StructuredInternetMinuteDownloader 创建成功")
        
        # 测试第一步
        print("🔍 测试第一步：智能文件选择")
        
        existing_file, file_info = downloader._step1_smart_file_selection(
            stock_code="000617",
            start_date="20250320",
            end_date="20250704"
        )
        
        if existing_file:
            print(f"✅ 找到现有文件: {os.path.basename(existing_file)}")
            
            if file_info:
                print("✅ 文件信息获取成功")
                print(f"   类型: {type(file_info)}")
                
                # 尝试访问属性
                try:
                    print(f"   时间范围: {file_info.start_date} ~ {file_info.end_date}")
                    print(f"   覆盖天数: {file_info.coverage_days}天")
                    print(f"   质量评分: {file_info.total_score}")
                except AttributeError as e:
                    print(f"   ❌ 属性访问错误: {e}")
                    return False
            else:
                print("⚠️ 文件信息获取失败")
        else:
            print("ℹ️ 未找到现有文件")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def main():
    """主函数"""
    print("🧪 FileInfo错误调试")
    print("=" * 80)
    
    # 测试1: FileInfo对象创建
    result1 = test_fileinfo_creation()
    
    # 测试2: 结构化下载器
    result2 = test_structured_downloader()
    
    print(f"\n📊 测试结果:")
    print(f"   FileInfo对象创建: {'✅ 通过' if result1 else '❌ 失败'}")
    print(f"   结构化下载器: {'✅ 通过' if result2 else '❌ 失败'}")
    
    if result1 and result2:
        print("🎉 所有测试通过，FileInfo错误已修复！")
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
    
    return result1 and result2


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
