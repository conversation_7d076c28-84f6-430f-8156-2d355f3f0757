# DDD架构测试目录

## 目录结构

### 测试分层
- `unit/` - 单元测试（测试单个模块）
- `integration/` - 集成测试（测试模块间协作）
- `performance/` - 性能测试（验证性能指标）
- `domain/` - 领域层测试
- `fixtures/` - 测试夹具和数据

## 测试原则

1. **单元测试** - 快速、独立、可重复
2. **集成测试** - 验证模块间协作
3. **性能测试** - 确保性能指标达标
4. **领域测试** - 验证业务逻辑正确性

## 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行单元测试
python -m pytest tests/unit/

# 运行集成测试
python -m pytest tests/integration/
```

## 测试配置

- 使用 `pytest.ini` 进行pytest配置
- 使用 `tests/test_config.py` 进行测试专用配置
- 测试数据存放在 `test_environments/` 目录
