#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户配置设置模块

从原user_config.py迁移而来，提供用户可配置的设置项。
"""

# 基础调试配置
DEBUG = False  # 是否开启调试日志输出

# 详细模式配置 (2025-07-31: 禁用以避免与结构化输出重复)
VERBOSE_MODE = {
    'enabled': False,  # 生产环境禁用详细模式，使用结构化输出格式器
    'show_forward_adj_details': False,  # 显示前复权处理详情
    'show_performance_warnings': False,  # 显示性能警告
    'show_data_processing_steps': False,  # 显示数据处理步骤
    'show_cache_status': False,  # 显示缓存状态详情
    'highlight_critical_info': False,  # 高亮关键信息
    'show_detailed_calculations': False,  # 显示详细的计算步骤
}

# 智能文件选择器配置
SMART_FILE_SELECTOR = {
    'enabled': True,  # 是否启用智能文件选择器
    'default_strategy': 'smart_comprehensive',  # 默认文件选择策略
    'scoring_weights': {
        'freshness_weight': 0.3,    # 新鲜度权重
        'coverage_weight': 0.4,     # 覆盖度权重
        'match_weight': 0.3,        # 匹配度权重
    },
    'conflict_resolution': {
        'auto_resolve': True,       # 自动解决冲突
        'prefer_newer': True,       # 优先选择较新的文件
        'min_score_threshold': 0.6, # 最低评分阈值
    },
}

# TDX配置
TDX_CONFIG = {
    'tdx_path': 'H:/MPV1.17/T0002',  # TDX主路径
    'tdx_test': '',  # TDX测试路径
    'tdx_min_path': '',  # 分钟线路径
    'tdx_day_path': '',  # 日线路径
    '目标股票代码': None,  # 目标股票文件路径
    'csv_gbbq': 'T0002/hq_cache/gbbq',  # GBBQ文件路径
}

# 输出配置
OUTPUT_CONFIG = {
    'base_output_path': 'H:/MPV1.17/T0002/signals',  # 基础输出路径
    'enable_console': False,  # 禁用控制台日志输出
    'file_encoding': 'utf-8',  # 文件编码
    'line_separator': '\n',  # 行分隔符
}

# 数据处理配置
DATA_PROCESSING = {
    'transparent_processing': {
        'enabled': True,  # 启用透明数据处理
        'auto_backup': True,  # 自动备份
        'validation_enabled': True,  # 启用数据验证
    },
    'quality_verification': {
        'enabled': True,  # 启用数据质量验证
        'strict_mode': False,  # 严格模式
        'auto_fix': True,  # 自动修复
    },
}

# 智能功能配置
INTELLIGENT_FEATURES = {
    'incremental_download': {
        'enabled': True,  # 启用增量下载
        'auto_detect': True,  # 自动检测
        'validation_enabled': True,  # 启用验证
    },
    'missing_data_processor': {
        'enabled': True,  # 启用缺失数据处理
        'auto_fill': False,  # 自动填充
        'notification_enabled': True,  # 启用通知
    },
}

# 错误处理配置
ERROR_HANDLING = {
    'auto_recovery': True,  # 自动恢复
    'detailed_logging': True,  # 详细日志
    'user_notification': True,  # 用户通知
    'fallback_strategies': True,  # 回退策略
}

# 用户界面配置
USER_INTERFACE = {
    'display_level': 'normal',  # 显示级别: minimal, normal, detailed
    'progress_bar': True,  # 显示进度条
    'color_output': True,  # 彩色输出
    'emoji_enabled': True,  # 启用表情符号
}

# 向后兼容性映射
LEGACY_MAPPING = {
    'debug': DEBUG,
    'verbose_mode': VERBOSE_MODE,
    'smart_file_selector': SMART_FILE_SELECTOR,
    'tdx': TDX_CONFIG,
    'output_config': OUTPUT_CONFIG,
}

# 导出所有配置
__all__ = [
    'DEBUG',
    'VERBOSE_MODE',
    'SMART_FILE_SELECTOR',
    'TDX_CONFIG',
    'OUTPUT_CONFIG',
    'DATA_PROCESSING',
    'INTELLIGENT_FEATURES',
    'ERROR_HANDLING',
    'USER_INTERFACE',
    'LEGACY_MAPPING',
]
