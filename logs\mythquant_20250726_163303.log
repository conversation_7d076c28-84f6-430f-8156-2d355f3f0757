2025-07-26 16:33:03,329 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_163303.log
2025-07-26 16:33:03,330 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 16:33:03,348 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 16:33:03,350 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 16:33:03,350 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 16:33:03,350 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-26 16:33:03,350 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-26 16:33:03,612 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.170s
2025-07-26 16:33:03,666 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.90.165 (115.238.90.165:7709) - 0.211s
2025-07-26 16:33:03,693 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.222s
2025-07-26 16:33:03,724 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************ (************:7709) - 0.245s
2025-07-26 16:33:03,741 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.253s
2025-07-26 16:33:03,987 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.515s
2025-07-26 16:33:03,993 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-26 16:33:03,993 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-26 16:33:03,993 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-26 16:33:03,993 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 16:33:03,993 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 16:33:03,994 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 16:33:03,994 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 16:33:04,429 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 16:33:04,429 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 16:33:04,435 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 16:33:04,436 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 16:33:04,436 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 16:33:04,436 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 16:33:04,436 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 16:33:04,436 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 16:33:04,437 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 16:33:04,441 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 16:33:04,441 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 16:33:04,441 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 16:33:04,441 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 16:33:04,447 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 16:33:04,841 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.39秒
2025-07-26 16:33:04,842 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.39秒
2025-07-26 16:33:04,842 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 16:33:04,842 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 16:33:04,842 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 16:33:04,842 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 16:33:04,842 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 16:33:04,842 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 16:33:04,842 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 16:33:04,842 - main_v20230219_optimized - INFO - generate_daily_data_task:3275 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 16:33:04,842 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:3814 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 16:33:04,843 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 16:33:04,844 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 16:33:04,985 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 16:33:04,986 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 16:33:04,992 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 16:33:04,992 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 16:33:04,992 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 16:33:04,992 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 16:33:04,992 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 16:33:04,992 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 16:33:04,993 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 16:33:04,996 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 16:33:04,996 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 16:33:04,996 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 16:33:04,996 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 16:33:05,003 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 16:33:05,631 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.63秒
2025-07-26 16:33:05,631 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.63秒
2025-07-26 16:33:15,641 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 16:33:38,815 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 16:33:38,815 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 16:33:38,815 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 16:33:38,822 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 16:33:39,258 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 16:33:39,258 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 16:33:39,258 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 16:33:39,259 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 16:33:39,260 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 16:33:39,260 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 16:33:39,260 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 16:33:39,260 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 16:33:39,261 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 16:33:39,297 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 16:33:39,738 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 16:33:39,756 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 16:33:39,774 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 16:33:39,788 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 16:33:39,805 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 16:33:39,822 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 16:33:39,840 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 16:33:39,859 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 16:33:39,876 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 16:33:39,894 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 16:33:39,910 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 16:33:39,927 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 16:33:39,943 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 16:33:39,958 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 16:33:39,976 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 16:33:39,993 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 16:33:40,006 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 16:33:40,021 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 16:33:40,036 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 16:33:40,052 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 16:33:40,068 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 16:33:40,068 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 16:33:44,081 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 16:33:44,081 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 16:33:44,082 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 16:33:44,082 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 16:33:44,082 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 16:33:44,084 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 16:33:44,159 - main_v20230219_optimized - INFO - apply_forward_adjustment:1911 - 保留交易时间数据后: 320880条
2025-07-26 16:33:44,159 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 16:33:44,159 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 16:33:44,159 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 16:33:44,160 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 16:33:44,160 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 16:33:44,160 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 16:33:44,171 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 16:33:44,317 - main_v20230219_optimized - INFO - load_and_process_minute_data:2380 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 16:33:44,802 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 16:33:45,030 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 16:33:45,030 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 16:33:45,111 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 16:33:45,112 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 16:33:45,113 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3993 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 16:33:45,113 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3994 - 📊 成功生成: 1 个文件
2025-07-26 16:33:45,113 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3995 - ❌ 处理失败: 0 个股票
2025-07-26 16:33:45,113 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3996 - 📈 成功率: 100.0%
2025-07-26 16:33:45,113 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3997 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 16:33:45,144 - main_v20230219_optimized - INFO - generate_daily_data_task:3285 - ✅ 日线数据生成完成，耗时: 40.30 秒
2025-07-26 16:33:45,144 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 16:33:45,144 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网日线数据下载
2025-07-26 16:33:45,144 - Main - INFO - info:307 - ℹ️ 开始执行互联网日线数据下载任务
2025-07-26 16:33:45,146 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 16:33:45,147 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 16:33:45,471 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 16:33:45,480 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 16:33:45,480 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 16:33:45,481 - Main - INFO - info:307 - ℹ️ 互联网数据下载时间范围: 20170101 - 20250720
2025-07-26 16:33:45,481 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的互联网数据
2025-07-26 16:33:45,481 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 16:33:45,481 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 16:33:45,481 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 16:33:45,481 - Main - INFO - info:307 - ℹ️ 📊 频率转换: d -> daily
2025-07-26 16:33:45,481 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 daily 数据: 20170101 - 20250720
2025-07-26 16:33:45,481 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:45,661 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:45,661 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计749520条 -> 实际请求800条 (配置限制:800)
2025-07-26 16:33:45,661 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 daily 数据
2025-07-26 16:33:45,662 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=9, market=0, code=000617, count=800
2025-07-26 16:33:45,714 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需748720条
2025-07-26 16:33:45,714 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:45,885 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:45,937 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 16:33:45,938 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:46,112 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:46,164 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 16:33:46,164 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:46,340 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:46,393 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 16:33:46,393 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:46,566 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:46,616 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 16:33:46,617 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:46,800 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:46,853 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 16:33:46,853 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:47,026 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:47,077 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 16:33:47,077 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:47,255 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:47,307 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 16:33:47,307 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:47,482 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:47,537 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +355条，总计6755条
2025-07-26 16:33:47,537 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计6755条数据
2025-07-26 16:33:47,554 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 2073 条 daily 数据
2025-07-26 16:33:47,557 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共2073条记录
2025-07-26 16:33:47,557 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 16:33:47,563 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=19.030, 前复权=19.030
2025-07-26 16:33:47,564 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 16:33:47,564 - Main - INFO - info:307 - ℹ️   日期: 20170103
2025-07-26 16:33:47,565 - Main - INFO - info:307 - ℹ️   当日收盘价C: 19.03
2025-07-26 16:33:47,565 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 19.03
2025-07-26 16:33:47,565 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 16:33:47,565 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 2073 >= 5
2025-07-26 16:33:47,565 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 16:33:47,565 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 16:33:47,565 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 16:33:47,568 - Main - INFO - info:307 - ℹ️ 数据无变化，将添加时间戳: day_0_000617_20170101-20250720_来源互联网（202507261633）.txt
2025-07-26 16:33:47,592 - Main - INFO - info:307 - ℹ️ 成功保存000617数据到: H:/MPV1.17/T0002/signals\day_0_000617_20170101-20250720_来源互联网（202507261633）.txt
2025-07-26 16:33:47,592 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 16:33:47,592 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 16:33:47,592 - Main - INFO - info:307 - ℹ️ 互联网数据下载完成: 1/1 成功 (100.0%)
2025-07-26 16:33:47,592 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网日线数据下载
2025-07-26 16:33:47,592 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 16:33:47,592 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 16:33:47,592 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250726
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 16:33:47,593 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 16:33:47,595 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-26 16:33:47,595 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250101-20250726_来源互联网.txt
2025-07-26 16:33:47,595 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250101-20250726_来源互联网.txt
2025-07-26 16:33:47,595 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-26 16:33:47,595 - core.logging_service - INFO - verbose_log:67 - 📊 分析现有文件时间跨度: 1min_0_000617_20250101-20250726_来源互联网.txt
2025-07-26 16:33:47,598 - core.logging_service - INFO - verbose_log:67 - 📅 现有文件时间跨度: 20250303 ~ 20250725 (24000条记录)
2025-07-26 16:33:47,598 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-26 16:33:47,598 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250725
2025-07-26 16:33:47,598 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250726
2025-07-26 16:33:47,599 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-26 16:33:47,599 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-26 16:33:47,599 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-26 16:33:47,599 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-26 16:33:47,599 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-26 16:33:47,599 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-26 16:33:47,599 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250726 ~ 20250726
2025-07-26 16:33:47,600 - core.logging_service - INFO - verbose_log:67 - 🔍 验证最后一条记录的数据一致性
2025-07-26 16:33:47,602 - core.logging_service - INFO - verbose_log:67 - 📋 文件最后记录: 时间=202507250000, 前复权价=8.9
2025-07-26 16:33:47,602 - core.logging_service - INFO - verbose_log:67 - 🔄 从API获取 20250725 的数据进行比对验证
2025-07-26 16:33:47,602 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 16:33:47,602 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 16:33:47,602 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-26 16:33:47,602 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:47,787 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:47,788 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计240条 -> 实际请求240条 (配置限制:800)
2025-07-26 16:33:47,788 - Main - INFO - info:307 - ℹ️ 📊 预计获取 240 条 1min 数据
2025-07-26 16:33:47,788 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=240
2025-07-26 16:33:47,839 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-26 16:33:47,840 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-26 16:33:47,841 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 16:33:47,842 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-26 16:33:47,843 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 16:33:47,843 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-26 16:33:47,843 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-26 16:33:47,843 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-26 16:33:47,843 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 16:33:47,843 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-26 16:33:47,843 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 16:33:47,844 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 16:33:47,844 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 16:33:47,847 - Main - INFO - info:307 - ℹ️ ❌ 数据一致性验证失败: API数据中未找到匹配的时间记录: 202507250000
2025-07-26 16:33:47,847 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-26 16:33:47,847 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 16:33:47,847 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 16:33:47,848 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250726
2025-07-26 16:33:47,848 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:48,023 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:48,023 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计49680条 -> 实际请求800条 (配置限制:800)
2025-07-26 16:33:48,023 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 16:33:48,023 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 16:33:48,074 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需48880条
2025-07-26 16:33:48,074 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:48,254 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:48,306 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 16:33:48,307 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:48,485 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:48,538 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 16:33:48,538 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:48,714 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:48,767 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 16:33:48,767 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:48,941 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:48,990 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 16:33:48,990 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:49,171 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:49,225 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 16:33:49,225 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:49,405 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:49,457 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 16:33:49,457 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:49,628 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:49,678 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 16:33:49,678 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:49,864 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:49,918 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 16:33:49,918 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:50,101 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:50,165 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 16:33:50,165 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:50,337 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:50,388 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 16:33:50,388 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:50,571 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:50,623 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 16:33:50,624 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:50,796 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:50,847 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 16:33:50,848 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:51,020 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:51,071 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 16:33:51,071 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:51,256 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:51,306 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 16:33:51,307 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:51,476 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:51,527 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 16:33:51,528 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:51,698 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:51,748 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 16:33:51,748 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:51,914 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:51,963 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 16:33:51,963 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:52,142 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:52,194 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 16:33:52,194 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:52,369 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:52,422 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 16:33:52,422 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:52,593 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:52,642 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 16:33:52,642 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:52,820 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:52,874 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 16:33:52,874 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:53,052 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:53,103 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 16:33:53,103 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:53,273 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:53,323 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 16:33:53,323 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:53,501 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:53,553 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 16:33:53,553 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:53,721 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:53,772 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 16:33:53,772 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:53,946 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:53,997 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 16:33:53,997 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:54,174 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:54,227 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 16:33:54,227 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:54,396 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:54,447 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 16:33:54,447 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:54,625 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:54,681 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 16:33:54,681 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 16:33:54,863 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 16:33:54,909 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 16:33:54,964 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 24000 条 1min 数据
2025-07-26 16:33:54,972 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共24000条记录
2025-07-26 16:33:54,973 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 16:33:55,044 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.290, 前复权=6.290
2025-07-26 16:33:55,048 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 16:33:55,048 - Main - INFO - info:307 - ℹ️   日期: 202503030931
2025-07-26 16:33:55,048 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.29
2025-07-26 16:33:55,048 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.29
2025-07-26 16:33:55,048 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 16:33:55,049 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 24000 >= 5
2025-07-26 16:33:55,049 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 16:33:55,049 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 16:33:55,049 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 16:33:55,132 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250101-20250726_来源互联网.txt (1123291 字节)
2025-07-26 16:33:55,133 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 16:33:55,133 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 16:33:55,133 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-26 16:33:55,133 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-26 16:33:55,133 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 16:33:55,133 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 16:33:55,134 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 16:33:55,134 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 16:33:55,134 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 16:33:55,135 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 16:33:55,135 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: day_0_000617_20240701-20240731_来源互联网.txt
2025-07-26 16:33:55,151 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20200101-20250724.txt, 数据行数: 1337
2025-07-26 16:33:55,152 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20240701-20240731_来源互联网.txt, 数据行数: 23
2025-07-26 16:33:55,154 - Main - INFO - info:307 - ℹ️ 数据对齐完成，共同日期数: 23
2025-07-26 16:33:55,156 - Main - INFO - info:307 - ℹ️ 价格差异计算完成，数据点数: 23
2025-07-26 16:33:55,159 - Main - INFO - info:307 - ℹ️ 股票 000617 数据比较完成
2025-07-26 16:33:55,159 - Main - INFO - info:307 - ℹ️ ✅ 000617 分析完成
2025-07-26 16:33:55,174 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_163355.txt
2025-07-26 16:33:55,174 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 1/1 成功 (100.0%)
2025-07-26 16:33:55,174 - Main - INFO - info:307 - ℹ️ 任务执行成功: 前复权数据比较分析
2025-07-26 16:33:55,175 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 100.0%
2025-07-26 16:33:55,175 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 16:33:55,175 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 16:33:55,175 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 16:33:55,176 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 16:33:55,176 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 16:33:55,176 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 16:33:55,176 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
