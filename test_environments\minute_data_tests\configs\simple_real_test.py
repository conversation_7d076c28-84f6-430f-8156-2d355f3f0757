#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的实际数据测试脚本

测试改进后的1分钟数据处理工作流程的核心功能

作者: AI Assistant
创建时间: 2025-07-30
"""

import sys
import os
import glob
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))


def test_interface_imports():
    """测试接口导入"""
    print("🔍 测试1：接口导入验证")
    print("-" * 50)
    
    try:
        # 测试新价格比较接口
        from test_environments.shared.utilities.test_file_api_comparator import TestFileApiComparator
        print("   ✅ TestFileApiComparator 导入成功")
        
        from test_environments.shared.utilities.specific_minute_data_fetcher import SpecificMinuteDataFetcher
        print("   ✅ SpecificMinuteDataFetcher 导入成功")
        
        # 测试主工作流程
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        print("   ✅ StructuredInternetMinuteDownloader 导入成功")
        
        # 测试240行标准模块
        from utils.missing_data_processor import MissingDataProcessor
        processor = MissingDataProcessor()
        if hasattr(processor, 'STANDARD_MINUTES_PER_DAY') and processor.STANDARD_MINUTES_PER_DAY == 240:
            print("   ✅ 240行标准验证通过")
        else:
            print("   ⚠️ 240行标准验证失败")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 接口导入失败: {e}")
        return False


def test_file_analysis():
    """测试文件分析功能"""
    print("\n📊 测试2：文件分析功能")
    print("-" * 50)
    
    try:
        # 查找现有测试文件
        test_files = glob.glob("test_environments/minute_data_tests/input_data/*.txt")
        
        if not test_files:
            print("   ⚠️ 未找到测试文件")
            # 查找项目根目录下的1min文件
            root_files = glob.glob("1min_0_000617_*.txt")
            if root_files:
                test_files = root_files[:1]  # 只取第一个
                print(f"   📁 使用根目录文件: {os.path.basename(test_files[0])}")
            else:
                print("   ❌ 未找到任何1min数据文件")
                return False
        
        test_file = test_files[0]
        print(f"   📁 分析文件: {os.path.basename(test_file)}")
        print(f"   📊 文件大小: {os.path.getsize(test_file):,} bytes")
        
        # 分析文件内容
        with open(test_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
            
        print(f"   📋 总行数: {len(lines):,}")
        
        if len(lines) > 1:
            header = lines[0].strip()
            print(f"   📊 表头: {header}")
            
            # 检查表头格式
            expected_fields = ['股票编码', '时间', '当日收盘价C', '前复权收盘价C']
            header_correct = all(field in header for field in expected_fields)
            print(f"   ✅ 表头格式: {'正确' if header_correct else '错误'}")
            
            # 分析数据行
            data_lines = [line for line in lines[1:] if line.strip()]
            print(f"   📊 数据行数: {len(data_lines):,}")
            
            if data_lines:
                # 分析第一行和最后一行
                first_line = data_lines[0].strip().split('|')
                last_line = data_lines[-1].strip().split('|')
                
                if len(first_line) >= 2 and len(last_line) >= 2:
                    first_time = first_line[1]
                    last_time = last_line[1]
                    print(f"   📅 时间范围: {first_time} ~ {last_time}")
                    
                    # 检查股票代码格式
                    stock_code = first_line[0]
                    print(f"   🏷️ 股票代码: {stock_code} ({'✅ 格式正确' if len(stock_code) == 6 else '❌ 格式错误'})")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 文件分析失败: {e}")
        return False


def test_price_comparison():
    """测试价格比较功能"""
    print("\n💰 测试3：价格比较功能")
    print("-" * 50)
    
    try:
        from test_environments.shared.utilities.test_file_api_comparator import TestFileApiComparator
        
        # 查找测试文件
        test_files = glob.glob("test_environments/minute_data_tests/input_data/*.txt")
        if not test_files:
            test_files = glob.glob("1min_0_000617_*.txt")
        
        if not test_files:
            print("   ⚠️ 未找到测试文件，跳过价格比较测试")
            return True
        
        test_file = test_files[0]
        stock_code = "000617"
        
        print(f"   📁 测试文件: {os.path.basename(test_file)}")
        
        # 创建比较器
        comparator = TestFileApiComparator()
        
        # 执行比较（设置较短的超时时间）
        print("   🔍 执行价格比较...")
        
        result = comparator.compare_last_record_close_price(
            test_file_path=test_file,
            stock_code=stock_code,
            tolerance=0.001
        )
        
        if result['success']:
            file_info = result['file_info']
            print(f"   ✅ 文件分析成功:")
            print(f"      📅 最后时间: {file_info['last_time']}")
            print(f"      💰 最后价格: {file_info['last_close_price']:.3f}")
            
            api_info = result['api_info']
            if api_info.get('found', False):
                print(f"   ✅ API数据获取成功:")
                print(f"      💰 API价格: {api_info['close_price']:.3f}")
                print(f"      📊 价格差异: {result['comparison']['price_diff']:.6f}")
                print(f"      🎯 比较结果: {'一致' if result['is_equal'] else '不一致'}")
            else:
                print("   ⚠️ API数据获取失败（可能是网络问题或时间超出范围）")
            
            return True
        else:
            print(f"   ❌ 价格比较失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"   ❌ 价格比较测试失败: {e}")
        return False


def test_workflow_structure():
    """测试工作流程结构"""
    print("\n🚀 测试4：工作流程结构")
    print("-" * 50)
    
    try:
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        # 创建下载器实例
        downloader = StructuredInternetMinuteDownloader()
        print("   ✅ 下载器实例创建成功")
        
        # 检查四步流程方法是否存在
        methods_to_check = [
            '_step1_smart_file_selection',
            '_step2_incremental_feasibility_check',
            '_step3_missing_data_audit_and_repair',
            '_step4_incremental_data_download'
        ]
        
        for method_name in methods_to_check:
            if hasattr(downloader, method_name):
                print(f"   ✅ {method_name} 方法存在")
            else:
                print(f"   ❌ {method_name} 方法缺失")
                return False
        
        # 检查主执行方法
        if hasattr(downloader, '_execute_four_step_process'):
            print("   ✅ _execute_four_step_process 主方法存在")
        else:
            print("   ❌ _execute_four_step_process 主方法缺失")
            return False
        
        print("   ✅ 工作流程结构验证通过")
        return True
        
    except Exception as e:
        print(f"   ❌ 工作流程结构测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 简化实际数据测试")
    print("=" * 80)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 80)
    
    test_results = []
    
    # 测试1: 接口导入验证
    result1 = test_interface_imports()
    test_results.append(("接口导入验证", result1))
    
    # 测试2: 文件分析功能
    result2 = test_file_analysis()
    test_results.append(("文件分析功能", result2))
    
    # 测试3: 价格比较功能
    result3 = test_price_comparison()
    test_results.append(("价格比较功能", result3))
    
    # 测试4: 工作流程结构
    result4 = test_workflow_structure()
    test_results.append(("工作流程结构", result4))
    
    # 汇总测试结果
    print("\n📊 简化实际数据测试结果汇总")
    print("=" * 80)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    total_count = len(test_results)
    pass_rate = passed_count / total_count
    
    print(f"\n📈 总体通过率: {passed_count}/{total_count} ({pass_rate:.1%})")
    
    if pass_rate >= 0.75:
        print("🎉 简化实际数据测试通过！")
        print("💡 核心功能在真实环境中工作正常")
        
        if pass_rate == 1.0:
            print("🌟 所有测试项目都通过，改进效果优秀！")
        else:
            print("⚠️ 部分功能可能受网络或数据源限制影响")
        
        print("🚀 建议进行完整的端到端测试")
    else:
        print("⚠️ 部分核心功能存在问题")
        print("🔧 建议检查失败的测试项并进行修复")
    
    print("=" * 80)
    
    return pass_rate >= 0.75


if __name__ == '__main__':
    success = main()
    print(f"\n🏁 测试完成，退出码: {0 if success else 1}")
    sys.exit(0 if success else 1)
