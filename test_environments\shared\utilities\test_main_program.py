#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主程序测试脚本
验证主程序是否能正常运行并生成前复权txt文档
"""

import sys
import os
import pandas as pd
import logging
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def test_main_program_with_mock_data():
    """使用模拟数据测试主程序"""
    print("🧪 使用模拟数据测试主程序...")
    print("=" * 60)
    
    try:
        # 检查配置文件
        import user_config
        print(f"✅ 配置文件加载成功")
        print(f"   TDX路径: {user_config.tdx['tdx_path']}")
        print(f"   输出目录: {user_config.output_config['base_output_path']}")
        
        # 检查算法模块导入
        from algorithms import L2MetricsCalculator, TimeFrameResampler
        print("✅ 算法模块导入成功")
        
        # 创建算法实例
        l2_calc = L2MetricsCalculator()
        resampler = TimeFrameResampler()
        
        # 创建模拟数据
        mock_data = create_realistic_stock_data()
        print(f"✅ 创建模拟数据成功 - {len(mock_data)}条记录")
        
        # 执行L2指标计算
        result_data = l2_calc.calculate_l2_metrics(mock_data)
        print(f"✅ L2指标计算完成")
        print(f"   平均买卖差: {result_data['买卖差'].mean():.4f}")
        print(f"   平均涨跌幅: {result_data['涨跌幅'].mean():.2f}%")
        
        # 执行重采样
        df_daily, df_weekly = resampler.resample_to_timeframes(result_data, 0)
        
        if df_daily is not None and len(df_daily) > 0:
            print(f"✅ 日线重采样成功 - {len(df_daily)}条记录")
        
        if df_weekly is not None and len(df_weekly) > 0:
            print(f"✅ 周线重采样成功 - {len(df_weekly)}条记录")
        
        # 测试文件写入功能
        test_file_writing(result_data, df_daily)
        
        print("=" * 60)
        print("🎉 主程序模拟测试全部通过！")
        print("✨ 算法模块迁移成功，核心功能正常")
        return True
        
    except Exception as e:
        print(f"❌ 主程序测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def create_realistic_stock_data():
    """创建更真实的股票数据"""
    import numpy as np
    from datetime import datetime, timedelta
    
    # 生成一天的分钟数据
    start_time = datetime(2024, 1, 15, 9, 30)  # 2024年1月15日 9:30
    data = []
    base_price = 12.50  # 基准价格
    
    # 生成240分钟的交易数据（一个交易日）
    record_count = 0
    for i in range(240):
        current_time = start_time + timedelta(minutes=i)

        # 跳过中午休市时间
        if current_time.hour == 11 and current_time.minute >= 30:
            continue
        if current_time.hour == 12:
            continue
        if current_time.hour == 13 and current_time.minute < 0:
            continue
        
        # 模拟价格波动（带趋势）
        trend = 0.001 * i  # 轻微上涨趋势
        volatility = np.random.normal(0, 0.02)  # 2%的波动率
        
        open_price = base_price + trend + volatility
        high_price = open_price + abs(np.random.normal(0, 0.01))
        low_price = open_price - abs(np.random.normal(0, 0.01))
        close_price = open_price + np.random.normal(0, 0.005)
        
        # 确保价格关系合理
        high_price = max(high_price, open_price, close_price)
        low_price = min(low_price, open_price, close_price)
        
        # 模拟成交量（开盘和收盘时较大）
        if record_count < 30 or record_count > 210:  # 开盘和收盘时段
            volume = np.random.randint(5000, 15000)
        else:
            volume = np.random.randint(1000, 8000)

        data.append({
            '日期': current_time.strftime('%Y%m%d %H:%M:%S'),
            '股票代码': '000617',
            'open': round(open_price, 3),
            'high': round(high_price, 3),
            'low': round(low_price, 3),
            'close': round(close_price, 3),
            'volume': volume,
            'amount': round(volume * close_price, 2),
            '上周期C': round(base_price if record_count == 0 else data[record_count-1]['close'], 3)
        })

        base_price = close_price  # 更新基准价格
        record_count += 1
    
    return pd.DataFrame(data)


def test_file_writing(minute_data, daily_data):
    """测试文件写入功能"""
    print("\n📁 测试文件写入功能...")
    
    try:
        from file_io.file_writer import write_minute_txt_file, write_daily_txt_file
        from utils.helpers import get_output_directory
        
        # 获取输出目录
        import user_config
        output_dir = user_config.output_config['base_output_path']
        os.makedirs(output_dir, exist_ok=True)
        
        # 测试分钟数据写入
        if minute_data is not None and len(minute_data) > 0:
            minute_file = os.path.join(output_dir, "test_min_1_000617_20240115-20240115.txt")
            write_minute_txt_file(minute_data, minute_file, '000617')
            
            if os.path.exists(minute_file):
                file_size = os.path.getsize(minute_file)
                print(f"✅ 分钟数据文件写入成功: {minute_file} ({file_size} bytes)")
            else:
                print("❌ 分钟数据文件写入失败")
        
        # 测试日线数据写入
        if daily_data is not None and len(daily_data) > 0:
            daily_file = os.path.join(output_dir, "test_day_0_000617_20240115-20240115.txt")
            write_daily_txt_file(daily_data, daily_file, '000617')
            
            if os.path.exists(daily_file):
                file_size = os.path.getsize(daily_file)
                print(f"✅ 日线数据文件写入成功: {daily_file} ({file_size} bytes)")
            else:
                print("❌ 日线数据文件写入失败")
        
        return True
        
    except Exception as e:
        print(f"❌ 文件写入测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🚀 MythQuant 主程序测试套件")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    success = test_main_program_with_mock_data()
    
    print("=" * 60)
    if success:
        print("🎉 主程序测试通过！")
        print("✨ 算法模块迁移成功，系统核心功能正常")
        print("📝 建议：可以尝试使用真实数据进行完整测试")
    else:
        print("❌ 主程序测试失败")
        print("🔧 请检查算法模块的实现和集成")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
