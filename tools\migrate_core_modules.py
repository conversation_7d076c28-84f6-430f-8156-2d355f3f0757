#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
核心模块迁移脚本
将现有的core目录文件迁移到新的src/mythquant/core/目录
"""

import os
import shutil
from pathlib import Path

def migrate_core_modules():
    """迁移核心模块"""
    
    # 定义源目录和目标目录
    source_dir = Path("core")
    target_dir = Path("src/mythquant/core")
    
    # 需要迁移的文件列表
    files_to_migrate = [
        "stock_processor.py",
        "task_manager.py", 
        "data_quality_auditor.py",
        "environment_manager.py",
        "logging_service.py"
    ]
    
    print("🔄 开始迁移核心模块...")
    
    migrated_count = 0
    skipped_count = 0
    
    for filename in files_to_migrate:
        source_file = source_dir / filename
        target_file = target_dir / filename
        
        if source_file.exists():
            if not target_file.exists():
                try:
                    # 复制文件
                    shutil.copy2(source_file, target_file)
                    print(f"✅ 迁移: {filename}")
                    migrated_count += 1
                    
                    # 更新导入路径
                    update_imports(target_file)
                    
                except Exception as e:
                    print(f"❌ 迁移失败 {filename}: {e}")
            else:
                print(f"⏭️ 已存在: {filename}")
                skipped_count += 1
        else:
            print(f"⚠️ 源文件不存在: {filename}")
    
    print(f"\n📊 迁移统计:")
    print(f"   迁移成功: {migrated_count} 个文件")
    print(f"   跳过文件: {skipped_count} 个文件")
    
    return migrated_count > 0

def update_imports(file_path):
    """更新文件中的导入路径"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 更新导入路径
        import_mappings = {
            'from core.config_manager import': 'from mythquant.config.manager import',
            'from core.': 'from mythquant.core.',
            'import user_config': '# import user_config  # 将使用新的配置系统',
            'user_config.': '# user_config.  # 将使用新的配置系统',
        }
        
        updated = False
        for old_import, new_import in import_mappings.items():
            if old_import in content:
                content = content.replace(old_import, new_import)
                updated = True
        
        if updated:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"    📝 更新导入路径: {file_path.name}")
    
    except Exception as e:
        print(f"    ⚠️ 更新导入路径失败 {file_path.name}: {e}")

def create_stub_modules():
    """为尚未迁移的模块创建存根"""
    
    stub_modules = {
        "src/mythquant/core/stock_processor.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据处理器模块 (存根)
"""

class StockDataProcessor:
    """股票数据处理器"""
    
    def __init__(self, tdx_path: str):
        self.tdx_path = tdx_path
    
    def get_status_info(self):
        return {'cache_status': 'Unknown', 'optimization_modules': 'Unknown'}
    
    def cleanup(self):
        pass
''',
        
        "src/mythquant/core/task_manager.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
任务管理器模块 (存根)
"""

class TaskManager:
    """任务管理器"""
    
    def __init__(self, stock_processor):
        self.stock_processor = stock_processor
    
    def get_enabled_tasks(self):
        return []
    
    def get_task_overview(self):
        return {'total_tasks': 0, 'enabled_tasks': 0}
    
    def execute_task(self, task):
        return True
    
    def cleanup(self):
        pass
''',
        
        "src/mythquant/core/logging_service.py": '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务模块 (存根)
"""

import logging

class LoggingService:
    """日志服务"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
    
    def info(self, msg):
        self.logger.info(msg)
    
    def warning(self, msg):
        self.logger.warning(msg)
    
    def error(self, msg):
        self.logger.error(msg)

# 全局实例
logging_service = LoggingService()
'''
    }
    
    print("\n🔧 创建存根模块...")
    
    for file_path, content in stub_modules.items():
        if not os.path.exists(file_path):
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                print(f"✅ 创建存根: {os.path.basename(file_path)}")
            except Exception as e:
                print(f"❌ 创建存根失败 {file_path}: {e}")
        else:
            print(f"⏭️ 存根已存在: {os.path.basename(file_path)}")

if __name__ == "__main__":
    print("🏗️ MythQuant 核心模块迁移工具")
    print("=" * 50)
    
    # 迁移现有模块
    migrate_success = migrate_core_modules()
    
    # 创建存根模块
    create_stub_modules()
    
    print("\n" + "=" * 50)
    if migrate_success:
        print("🎉 核心模块迁移完成！")
    else:
        print("⚠️ 核心模块迁移完成（部分使用存根）")
    
    print("\n💡 下一步:")
    print("1. 检查迁移后的文件")
    print("2. 更新导入路径")
    print("3. 运行测试验证")
