#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试工具模块

提供测试断言、比较工具和测试辅助函数
"""

import pandas as pd
import numpy as np
from decimal import Decimal
from typing import Any, Optional, List, Dict
import logging
from pathlib import Path


class TestUtils:
    """测试工具类 - 提供各种测试辅助功能"""
    
    @staticmethod
    def assert_dataframe_equal(df1: pd.DataFrame, df2: pd.DataFrame, 
                              check_dtype: bool = True, 
                              check_index: bool = True,
                              tolerance: float = 1e-6) -> bool:
        """
        比较两个DataFrame是否相等
        
        Args:
            df1: 第一个DataFrame
            df2: 第二个DataFrame
            check_dtype: 是否检查数据类型
            check_index: 是否检查索引
            tolerance: 数值比较容差
            
        Returns:
            是否相等
            
        Raises:
            AssertionError: 如果不相等
        """
        try:
            # 基本形状检查
            assert df1.shape == df2.shape, f"Shape mismatch: {df1.shape} vs {df2.shape}"
            
            # 列名检查
            assert list(df1.columns) == list(df2.columns), f"Columns mismatch: {df1.columns} vs {df2.columns}"
            
            # 索引检查
            if check_index:
                assert df1.index.equals(df2.index), "Index mismatch"
            
            # 数据比较
            for col in df1.columns:
                if pd.api.types.is_numeric_dtype(df1[col]):
                    # 数值列使用容差比较
                    diff = np.abs(df1[col] - df2[col])
                    max_diff = diff.max()
                    assert max_diff <= tolerance, f"Column {col} differs by {max_diff} (tolerance: {tolerance})"
                else:
                    # 非数值列直接比较
                    assert df1[col].equals(df2[col]), f"Column {col} values differ"
                
                # 数据类型检查
                if check_dtype:
                    assert df1[col].dtype == df2[col].dtype, f"Column {col} dtype mismatch: {df1[col].dtype} vs {df2[col].dtype}"
            
            return True
            
        except AssertionError as e:
            logging.error(f"DataFrame comparison failed: {e}")
            raise
    
    @staticmethod
    def assert_price_precision(value: float, expected_precision: int = 3) -> bool:
        """
        验证价格精度
        
        Args:
            value: 价格值
            expected_precision: 期望精度
            
        Returns:
            是否符合精度要求
        """
        try:
            # 转换为字符串检查小数位数
            str_value = str(value)
            if '.' in str_value:
                decimal_places = len(str_value.split('.')[1])
                assert decimal_places <= expected_precision, f"Price precision {decimal_places} exceeds expected {expected_precision}"
            
            # 使用Decimal验证精度
            decimal_value = Decimal(str(value))
            precision_check = decimal_value.quantize(Decimal('0.' + '0' * expected_precision))
            assert abs(float(precision_check) - value) < 1e-10, "Decimal precision check failed"
            
            return True
            
        except AssertionError as e:
            logging.error(f"Price precision check failed: {e}")
            raise
    
    @staticmethod
    def assert_file_exists(file_path: Path, min_size: int = 0) -> bool:
        """
        验证文件存在且符合要求
        
        Args:
            file_path: 文件路径
            min_size: 最小文件大小（字节）
            
        Returns:
            是否符合要求
        """
        try:
            assert file_path.exists(), f"File does not exist: {file_path}"
            assert file_path.is_file(), f"Path is not a file: {file_path}"
            
            if min_size > 0:
                file_size = file_path.stat().st_size
                assert file_size >= min_size, f"File size {file_size} is less than minimum {min_size}"
            
            return True
            
        except AssertionError as e:
            logging.error(f"File existence check failed: {e}")
            raise
    
    @staticmethod
    def assert_output_format(content: str, expected_header: str = None) -> bool:
        """
        验证输出格式
        
        Args:
            content: 文件内容
            expected_header: 期望的表头
            
        Returns:
            是否符合格式要求
        """
        try:
            lines = content.strip().split('\n')
            assert len(lines) >= 1, "Content is empty"
            
            # 检查表头
            if expected_header:
                actual_header = lines[0]
                assert actual_header == expected_header, f"Header mismatch: expected '{expected_header}', got '{actual_header}'"
            
            # 检查数据行格式
            if len(lines) > 1:
                header_fields = lines[0].split('|')
                for i, line in enumerate(lines[1:], 1):
                    fields = line.split('|')
                    assert len(fields) == len(header_fields), f"Line {i} field count mismatch: expected {len(header_fields)}, got {len(fields)}"
            
            return True
            
        except AssertionError as e:
            logging.error(f"Output format check failed: {e}")
            raise
    
    @staticmethod
    def assert_config_valid(config: Dict[str, Any], required_keys: List[str] = None) -> bool:
        """
        验证配置有效性
        
        Args:
            config: 配置字典
            required_keys: 必需的键列表
            
        Returns:
            是否有效
        """
        try:
            assert isinstance(config, dict), "Config must be a dictionary"
            
            if required_keys:
                for key in required_keys:
                    assert key in config, f"Required config key missing: {key}"
            
            # 检查路径配置
            if 'tdx_path' in config:
                assert isinstance(config['tdx_path'], str), "tdx_path must be a string"
            
            if 'output_path' in config:
                assert isinstance(config['output_path'], str), "output_path must be a string"
            
            # 检查布尔配置
            if 'debug' in config:
                assert isinstance(config['debug'], bool), "debug must be a boolean"
            
            return True
            
        except AssertionError as e:
            logging.error(f"Config validation failed: {e}")
            raise
    
    @staticmethod
    def compare_performance(func1, func2, *args, iterations: int = 10, **kwargs) -> Dict[str, float]:
        """
        比较两个函数的性能
        
        Args:
            func1: 第一个函数
            func2: 第二个函数
            *args: 函数参数
            iterations: 迭代次数
            **kwargs: 函数关键字参数
            
        Returns:
            性能比较结果
        """
        import time
        
        # 测试函数1
        times1 = []
        for _ in range(iterations):
            start_time = time.time()
            result1 = func1(*args, **kwargs)
            end_time = time.time()
            times1.append(end_time - start_time)
        
        # 测试函数2
        times2 = []
        for _ in range(iterations):
            start_time = time.time()
            result2 = func2(*args, **kwargs)
            end_time = time.time()
            times2.append(end_time - start_time)
        
        avg_time1 = sum(times1) / len(times1)
        avg_time2 = sum(times2) / len(times2)
        
        return {
            'func1_avg_time': avg_time1,
            'func2_avg_time': avg_time2,
            'speedup': avg_time1 / avg_time2 if avg_time2 > 0 else float('inf'),
            'func1_times': times1,
            'func2_times': times2
        }
    
    @staticmethod
    def create_test_report(test_results: Dict[str, bool], 
                          test_name: str = "Test Suite") -> str:
        """
        创建测试报告
        
        Args:
            test_results: 测试结果字典
            test_name: 测试套件名称
            
        Returns:
            测试报告字符串
        """
        total_tests = len(test_results)
        passed_tests = sum(test_results.values())
        failed_tests = total_tests - passed_tests
        success_rate = (passed_tests / total_tests * 100) if total_tests > 0 else 0
        
        report = f"""
{test_name} 测试报告
{'=' * 50}

总测试数: {total_tests}
通过: {passed_tests}
失败: {failed_tests}
成功率: {success_rate:.1f}%

详细结果:
"""
        
        for test_name, result in test_results.items():
            status = "✅ PASS" if result else "❌ FAIL"
            report += f"  {status} {test_name}\n"
        
        return report


# 全局测试工具实例
test_utils = TestUtils()

# 便捷函数
def assert_dataframe_equal(df1: pd.DataFrame, df2: pd.DataFrame, **kwargs) -> bool:
    """DataFrame相等断言的便捷函数"""
    return test_utils.assert_dataframe_equal(df1, df2, **kwargs)

def assert_price_precision(value: float, precision: int = 3) -> bool:
    """价格精度断言的便捷函数"""
    return test_utils.assert_price_precision(value, precision)

def assert_file_exists(file_path: Path, min_size: int = 0) -> bool:
    """文件存在断言的便捷函数"""
    return test_utils.assert_file_exists(file_path, min_size)

def assert_output_format(content: str, expected_header: str = None) -> bool:
    """输出格式断言的便捷函数"""
    return test_utils.assert_output_format(content, expected_header)


# 导出
__all__ = [
    'TestUtils',
    'test_utils',
    'assert_dataframe_equal',
    'assert_price_precision',
    'assert_file_exists',
    'assert_output_format'
]
