{"analysis_time": "2025-08-04T23:57:29.178285", "analysis": {"core_directories": {"description": "核心项目目录（必须保留）", "directories": ["src", "tests", "docs", "scripts", "test_environments"], "reason": "这些是当前DDD架构的核心组成部分"}, "utility_directories": {"description": "工具和配置目录（保留但可优化）", "directories": ["tools", "utils", "file_io", "prompt_templates", "logs", "data"], "reason": "这些目录有用但可能需要整理或部分迁移"}, "legacy_directories": {"description": "遗留目录（可以归档）", "directories": ["environments", "lib", "assets"], "reason": "这些目录与当前架构关联度低，可以归档"}, "temporary_directories": {"description": "临时目录（可以删除）", "directories": ["__pycache__"], "reason": "这些是临时生成的目录，可以安全删除"}, "backup_directories": {"description": "备份目录（可以归档或删除）", "directories": ["architecture_migration_final_archive_20250802_233447", "backup_test_integration_20250803_232702"], "reason": "这些是历史备份，可以归档到专门的备份目录"}, "core_files": {"description": "核心文件（必须保留）", "files": ["main.py", "user_config.py", "test_config.py", "requirements.txt", "pyproject.toml", "pytest.ini"], "reason": "这些是项目运行的核心文件"}, "temporary_files": {"description": "临时文件（可以删除或归档）", "files": ["comprehensive_1min_test.py", "comprehensive_test_with_proper_config.py", "comprehensive_test_report.md", "architecture_cleanup_report_20241220.json", "cleanup_report_20250802_233731.txt", "test_integration_report_20250803_232702.json"], "reason": "这些是临时测试文件或报告，可以移动到适当位置"}}, "recommendations": {"immediate_actions": {"delete_safely": {"directories": ["__pycache__"], "description": "可以安全删除的临时目录"}, "archive_backups": {"directories": ["architecture_migration_final_archive_20250802_233447", "backup_test_integration_20250803_232702"], "target": "archive/backups/", "description": "将备份目录归档到专门位置"}}, "optimization_actions": {"consolidate_utilities": {"action": "将utils和file_io模块迁移到src/mythquant/", "directories": ["utils", "file_io"], "description": "统一工具模块到DDD架构中"}, "clean_logs": {"action": "清理30天前的日志文件", "directory": "logs", "description": "保留最近日志，归档旧日志"}, "organize_data": {"action": "清理临时数据文件", "directory": "data", "description": "保留配置文件，清理临时数据"}}, "archival_actions": {"archive_legacy": {"directories": ["environments", "lib", "assets"], "target": "archive/legacy/", "description": "归档遗留目录"}, "archive_temp_files": {"files": ["comprehensive_1min_test.py", "comprehensive_test_with_proper_config.py", "comprehensive_test_report.md", "architecture_cleanup_report_20241220.json", "cleanup_report_20250802_233731.txt", "test_integration_report_20250803_232702.json"], "target": "archive/temp_files/", "description": "归档临时文件和报告"}}, "final_structure": {"description": "清理后的理想根目录结构", "structure": ["src/", "tests/", "docs/", "scripts/", "test_environments/", "tools/", "prompt_templates/", "logs/", "data/", "archive/", "main.py", "user_config.py", "test_config.py", "requirements.txt", "pyproject.toml", "pytest.ini"]}}, "impact_assessment": {"statistics": {"total_directories_analyzed": 17, "total_files_analyzed": 12, "directories_to_delete": 1, "directories_to_archive": 5, "directories_to_keep": 11, "files_to_archive": 6}, "space_savings": {"estimated_cleanup_percentage": 35.3, "root_directory_simplification": "从17个目录减少到11个核心目录"}, "benefits": ["根目录结构更清晰", "符合DDD架构标准", "减少维护复杂度", "提高开发效率", "便于新开发者理解项目结构"], "risks": ["需要验证归档目录中是否有重要文件", "可能需要更新一些脚本的路径引用", "建议先创建完整备份"]}, "cleanup_script": "#!/bin/bash\n# 项目根目录清理脚本\n# 生成时间: 2025-08-04 23:57:29\n\necho \"🧹 开始项目根目录清理...\"\n\n# 1. 创建归档目录结构\necho \"📁 创建归档目录结构...\"\nmkdir -p archive/{backups,legacy,temp_files}\n\n# 2. 删除临时目录\necho \"🗑️ 删除临时目录...\"\nrm -rf __pycache__\n\n# 3. 归档备份目录\necho \"📦 归档备份目录...\"\n[ -d architecture_migration_final_archive_20250802_233447 ] && mv architecture_migration_final_archive_20250802_233447 archive/backups/\n[ -d backup_test_integration_20250803_232702 ] && mv backup_test_integration_20250803_232702 archive/backups/\n\n# 4. 归档遗留目录\necho \"📚 归档遗留目录...\"\n[ -d environments ] && mv environments archive/legacy/\n[ -d lib ] && mv lib archive/legacy/\n[ -d assets ] && mv assets archive/legacy/\n\n# 5. 归档临时文件\necho \"📄 归档临时文件...\"\n[ -f comprehensive_1min_test.py ] && mv comprehensive_1min_test.py archive/temp_files/\n[ -f comprehensive_test_with_proper_config.py ] && mv comprehensive_test_with_proper_config.py archive/temp_files/\n[ -f comprehensive_test_report.md ] && mv comprehensive_test_report.md archive/temp_files/\n[ -f architecture_cleanup_report_20241220.json ] && mv architecture_cleanup_report_20241220.json archive/temp_files/\n[ -f cleanup_report_20250802_233731.txt ] && mv cleanup_report_20250802_233731.txt archive/temp_files/\n[ -f test_integration_report_20250803_232702.json ] && mv test_integration_report_20250803_232702.json archive/temp_files/\n\n# 6. 清理日志文件（保留最近30天）\necho \"🧹 清理旧日志文件...\"\nfind logs/ -name \"*.log\" -mtime +30 -exec mv {} archive/temp_files/ \\; 2>/dev/null || true\nfind logs/ -name \"*.json\" -mtime +30 -exec mv {} archive/temp_files/ \\; 2>/dev/null || true\n\necho \"✅ 根目录清理完成！\"\necho \"📋 清理后的目录结构：\"\nls -la | grep \"^d\"\n"}