#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数学算法模块

纯函数数学算法实现，无副作用，高度可测试
"""

import math
from typing import List, Optional, Tuple, Union
from decimal import Decimal
import numpy as np
from ..exceptions import InsufficientDataError, InvalidParameterError


def linear_interpolation(x: float, x1: float, y1: float, x2: float, y2: float) -> float:
    """
    线性插值
    
    Args:
        x: 插值点
        x1, y1: 第一个已知点
        x2, y2: 第二个已知点
    
    Returns:
        插值结果
    
    Raises:
        InvalidParameterError: 当x1 == x2时
    """
    if x1 == x2:
        raise InvalidParameterError("线性插值", "x1", x1, "x1不能等于x2")
    
    return y1 + (y2 - y1) * (x - x1) / (x2 - x1)


def exponential_smoothing(data: List[float], alpha: float) -> List[float]:
    """
    指数平滑
    
    Args:
        data: 数据序列
        alpha: 平滑系数 (0 < alpha <= 1)
    
    Returns:
        平滑后的数据序列
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: alpha参数无效
    """
    if not data:
        raise InsufficientDataError("指数平滑", 1, 0)
    
    if not (0 < alpha <= 1):
        raise InvalidParameterError("指数平滑", "alpha", alpha, "alpha必须在(0,1]范围内")
    
    result = [data[0]]  # 第一个值保持不变
    
    for i in range(1, len(data)):
        smoothed = alpha * data[i] + (1 - alpha) * result[i-1]
        result.append(smoothed)
    
    return result


def moving_average(data: List[float], window: int) -> List[float]:
    """
    移动平均
    
    Args:
        data: 数据序列
        window: 窗口大小
    
    Returns:
        移动平均序列
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 窗口大小无效
    """
    if len(data) < window:
        raise InsufficientDataError("移动平均", window, len(data))
    
    if window <= 0:
        raise InvalidParameterError("移动平均", "window", window, "窗口大小必须大于0")
    
    result = []
    for i in range(window - 1, len(data)):
        avg = sum(data[i - window + 1:i + 1]) / window
        result.append(avg)
    
    return result


def weighted_average(data: List[float], weights: List[float]) -> float:
    """
    加权平均
    
    Args:
        data: 数据序列
        weights: 权重序列
    
    Returns:
        加权平均值
    
    Raises:
        InvalidParameterError: 数据和权重长度不匹配或权重和为0
    """
    if len(data) != len(weights):
        raise InvalidParameterError(
            "加权平均", "weights", len(weights), 
            f"权重长度({len(weights)})必须等于数据长度({len(data)})"
        )
    
    weight_sum = sum(weights)
    if weight_sum == 0:
        raise InvalidParameterError("加权平均", "weights", weights, "权重和不能为0")
    
    weighted_sum = sum(d * w for d, w in zip(data, weights))
    return weighted_sum / weight_sum


def compound_growth_rate(start_value: float, end_value: float, periods: int) -> float:
    """
    复合增长率 (CAGR)
    
    Args:
        start_value: 起始值
        end_value: 结束值
        periods: 期间数
    
    Returns:
        复合增长率
    
    Raises:
        InvalidParameterError: 参数无效
    """
    if start_value <= 0:
        raise InvalidParameterError("复合增长率", "start_value", start_value, "起始值必须大于0")
    
    if end_value <= 0:
        raise InvalidParameterError("复合增长率", "end_value", end_value, "结束值必须大于0")
    
    if periods <= 0:
        raise InvalidParameterError("复合增长率", "periods", periods, "期间数必须大于0")
    
    return (end_value / start_value) ** (1 / periods) - 1


def standard_deviation(data: List[float], ddof: int = 0) -> float:
    """
    标准差
    
    Args:
        data: 数据序列
        ddof: 自由度修正 (0=总体标准差, 1=样本标准差)
    
    Returns:
        标准差
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) <= ddof:
        raise InsufficientDataError("标准差", ddof + 1, len(data))
    
    mean_val = sum(data) / len(data)
    variance_val = sum((x - mean_val) ** 2 for x in data) / (len(data) - ddof)
    
    return math.sqrt(variance_val)


def variance(data: List[float], ddof: int = 0) -> float:
    """
    方差
    
    Args:
        data: 数据序列
        ddof: 自由度修正 (0=总体方差, 1=样本方差)
    
    Returns:
        方差
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) <= ddof:
        raise InsufficientDataError("方差", ddof + 1, len(data))
    
    mean_val = sum(data) / len(data)
    return sum((x - mean_val) ** 2 for x in data) / (len(data) - ddof)


def correlation(x: List[float], y: List[float]) -> float:
    """
    皮尔逊相关系数
    
    Args:
        x: 第一个数据序列
        y: 第二个数据序列
    
    Returns:
        相关系数 (-1 到 1)
    
    Raises:
        InvalidParameterError: 数据长度不匹配
        InsufficientDataError: 数据不足
    """
    if len(x) != len(y):
        raise InvalidParameterError(
            "相关系数", "y", len(y),
            f"y的长度({len(y)})必须等于x的长度({len(x)})"
        )
    
    if len(x) < 2:
        raise InsufficientDataError("相关系数", 2, len(x))
    
    n = len(x)
    mean_x = sum(x) / n
    mean_y = sum(y) / n
    
    numerator = sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n))
    
    sum_sq_x = sum((x[i] - mean_x) ** 2 for i in range(n))
    sum_sq_y = sum((y[i] - mean_y) ** 2 for i in range(n))
    
    denominator = math.sqrt(sum_sq_x * sum_sq_y)
    
    if denominator == 0:
        return 0.0  # 如果其中一个序列是常数
    
    return numerator / denominator


def covariance(x: List[float], y: List[float], ddof: int = 0) -> float:
    """
    协方差
    
    Args:
        x: 第一个数据序列
        y: 第二个数据序列
        ddof: 自由度修正
    
    Returns:
        协方差
    
    Raises:
        InvalidParameterError: 数据长度不匹配
        InsufficientDataError: 数据不足
    """
    if len(x) != len(y):
        raise InvalidParameterError(
            "协方差", "y", len(y),
            f"y的长度({len(y)})必须等于x的长度({len(x)})"
        )
    
    if len(x) <= ddof:
        raise InsufficientDataError("协方差", ddof + 1, len(x))
    
    n = len(x)
    mean_x = sum(x) / n
    mean_y = sum(y) / n
    
    return sum((x[i] - mean_x) * (y[i] - mean_y) for i in range(n)) / (n - ddof)


def percentile(data: List[float], p: float) -> float:
    """
    百分位数
    
    Args:
        data: 数据序列
        p: 百分位 (0-100)
    
    Returns:
        百分位数值
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 百分位参数无效
    """
    if not data:
        raise InsufficientDataError("百分位数", 1, 0)
    
    if not (0 <= p <= 100):
        raise InvalidParameterError("百分位数", "p", p, "百分位必须在[0,100]范围内")
    
    sorted_data = sorted(data)
    n = len(sorted_data)
    
    if p == 0:
        return sorted_data[0]
    if p == 100:
        return sorted_data[-1]
    
    # 使用线性插值方法
    index = (p / 100) * (n - 1)
    lower_index = int(index)
    upper_index = min(lower_index + 1, n - 1)
    
    if lower_index == upper_index:
        return sorted_data[lower_index]
    
    weight = index - lower_index
    return sorted_data[lower_index] * (1 - weight) + sorted_data[upper_index] * weight


def quantile(data: List[float], q: float) -> float:
    """
    分位数 (百分位数的别名，使用0-1范围)
    
    Args:
        data: 数据序列
        q: 分位数 (0-1)
    
    Returns:
        分位数值
    """
    return percentile(data, q * 100)


def z_score(data: List[float]) -> List[float]:
    """
    Z分数标准化
    
    Args:
        data: 数据序列
    
    Returns:
        标准化后的数据序列
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 2:
        raise InsufficientDataError("Z分数", 2, len(data))
    
    mean_val = sum(data) / len(data)
    std_val = standard_deviation(data, ddof=1)
    
    if std_val == 0:
        return [0.0] * len(data)  # 如果标准差为0，返回全0序列
    
    return [(x - mean_val) / std_val for x in data]


def min_max_normalize(data: List[float], 
                     feature_range: Tuple[float, float] = (0.0, 1.0)) -> List[float]:
    """
    最小-最大标准化
    
    Args:
        data: 数据序列
        feature_range: 目标范围
    
    Returns:
        标准化后的数据序列
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if not data:
        raise InsufficientDataError("最小-最大标准化", 1, 0)
    
    min_val = min(data)
    max_val = max(data)
    
    if min_val == max_val:
        # 如果所有值相同，返回目标范围的中点
        mid_point = (feature_range[0] + feature_range[1]) / 2
        return [mid_point] * len(data)
    
    range_val = max_val - min_val
    target_min, target_max = feature_range
    target_range = target_max - target_min
    
    return [target_min + (x - min_val) / range_val * target_range for x in data]


def robust_scale(data: List[float]) -> List[float]:
    """
    鲁棒标准化 (使用中位数和四分位距)
    
    Args:
        data: 数据序列
    
    Returns:
        标准化后的数据序列
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 4:
        raise InsufficientDataError("鲁棒标准化", 4, len(data))
    
    median_val = percentile(data, 50)
    q1 = percentile(data, 25)
    q3 = percentile(data, 75)
    iqr = q3 - q1
    
    if iqr == 0:
        return [0.0] * len(data)  # 如果四分位距为0，返回全0序列
    
    return [(x - median_val) / iqr for x in data]


def rolling_window(data: List[float], window: int, 
                  func: callable) -> List[float]:
    """
    滚动窗口函数应用
    
    Args:
        data: 数据序列
        window: 窗口大小
        func: 应用的函数
    
    Returns:
        滚动计算结果
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 窗口大小无效
    """
    if len(data) < window:
        raise InsufficientDataError("滚动窗口", window, len(data))
    
    if window <= 0:
        raise InvalidParameterError("滚动窗口", "window", window, "窗口大小必须大于0")
    
    result = []
    for i in range(window - 1, len(data)):
        window_data = data[i - window + 1:i + 1]
        result.append(func(window_data))
    
    return result


def cumulative_sum(data: List[float]) -> List[float]:
    """
    累积和
    
    Args:
        data: 数据序列
    
    Returns:
        累积和序列
    """
    if not data:
        return []
    
    result = [data[0]]
    for i in range(1, len(data)):
        result.append(result[-1] + data[i])
    
    return result


def cumulative_product(data: List[float]) -> List[float]:
    """
    累积乘积
    
    Args:
        data: 数据序列
    
    Returns:
        累积乘积序列
    """
    if not data:
        return []
    
    result = [data[0]]
    for i in range(1, len(data)):
        result.append(result[-1] * data[i])
    
    return result


def diff(data: List[float], periods: int = 1) -> List[float]:
    """
    差分
    
    Args:
        data: 数据序列
        periods: 差分周期
    
    Returns:
        差分序列
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 周期参数无效
    """
    if len(data) <= periods:
        raise InsufficientDataError("差分", periods + 1, len(data))
    
    if periods <= 0:
        raise InvalidParameterError("差分", "periods", periods, "差分周期必须大于0")
    
    return [data[i] - data[i - periods] for i in range(periods, len(data))]


def pct_change(data: List[float], periods: int = 1) -> List[float]:
    """
    百分比变化
    
    Args:
        data: 数据序列
        periods: 变化周期
    
    Returns:
        百分比变化序列
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 周期参数无效
    """
    if len(data) <= periods:
        raise InsufficientDataError("百分比变化", periods + 1, len(data))
    
    if periods <= 0:
        raise InvalidParameterError("百分比变化", "periods", periods, "变化周期必须大于0")
    
    result = []
    for i in range(periods, len(data)):
        if data[i - periods] == 0:
            result.append(float('inf') if data[i] > 0 else float('-inf') if data[i] < 0 else 0)
        else:
            result.append((data[i] - data[i - periods]) / data[i - periods])
    
    return result
