#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
PyTDX数据源模块

提供基于pytdx库的在线数据获取功能
"""

import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging

# 导入新架构的配置系统
from src.mythquant.config.manager import ConfigManager

logger = logging.getLogger(__name__)


class PyTDXDataSource:
    """PyTDX数据源类 - 在线数据获取接口"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化PyTDX数据源
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.api = None
        self.connected = False
        
        # 初始化连接
        self._initialize_connection()
    
    def _initialize_connection(self):
        """初始化PyTDX连接"""
        try:
            from pytdx.hq import TdxHq_API
            self.api = TdxHq_API()
            logger.info("PyTDX API初始化成功")
        except ImportError:
            logger.error("pytdx库未安装，请安装: pip install pytdx")
            self.api = None
        except Exception as e:
            logger.error(f"PyTDX API初始化失败: {e}")
            self.api = None
    
    def connect(self, host: str = '**************', port: int = 7709) -> bool:
        """
        连接到TDX服务器
        
        Args:
            host: 服务器地址
            port: 服务器端口
            
        Returns:
            是否连接成功
        """
        if not self.api:
            logger.error("PyTDX API未初始化")
            return False
        
        try:
            result = self.api.connect(host, port)
            if result:
                self.connected = True
                logger.info(f"成功连接到TDX服务器 {host}:{port}")
            else:
                logger.error(f"连接TDX服务器失败 {host}:{port}")
            return result
        except Exception as e:
            logger.error(f"连接TDX服务器异常: {e}")
            return False
    
    def disconnect(self):
        """断开连接"""
        if self.api and self.connected:
            try:
                self.api.disconnect()
                self.connected = False
                logger.info("已断开TDX服务器连接")
            except Exception as e:
                logger.error(f"断开连接异常: {e}")
    
    def get_security_bars(self, stock_code: str, frequency: int = 9, 
                         count: int = 800) -> Optional[pd.DataFrame]:
        """
        获取K线数据
        
        Args:
            stock_code: 股票代码
            frequency: K线类型 (4=1分钟, 5=5分钟, 6=15分钟, 7=30分钟, 8=小时, 9=日线)
            count: 获取数量
            
        Returns:
            K线数据DataFrame或None
        """
        if not self.connected:
            logger.error("未连接到TDX服务器")
            return None
        
        try:
            # 确定市场代码
            if stock_code.startswith(('60', '68')):
                market = 1  # 上海
            elif stock_code.startswith(('00', '30')):
                market = 0  # 深圳
            else:
                logger.warning(f"不支持的股票代码: {stock_code}")
                return None
            
            # 获取数据
            data = self.api.get_security_bars(frequency, market, stock_code, 0, count)
            
            if data:
                df = pd.DataFrame(data)
                # 重命名列
                df = df.rename(columns={
                    'datetime': 'datetime',
                    'open': 'open',
                    'close': 'close',
                    'high': 'high',
                    'low': 'low',
                    'vol': 'volume',
                    'amount': 'amount'
                })
                
                # 转换数据类型
                df['datetime'] = pd.to_datetime(df['datetime'])
                
                logger.debug(f"成功获取{stock_code} K线数据，共{len(df)}条记录")
                return df
            else:
                logger.warning(f"未获取到{stock_code}的K线数据")
                return None
                
        except Exception as e:
            logger.error(f"获取{stock_code} K线数据失败: {e}")
            return None
    
    def get_minute_data(self, stock_code: str, count: int = 240) -> Optional[pd.DataFrame]:
        """
        获取1分钟数据
        
        Args:
            stock_code: 股票代码
            count: 获取数量
            
        Returns:
            1分钟数据DataFrame或None
        """
        return self.get_security_bars(stock_code, frequency=4, count=count)
    
    def get_day_data(self, stock_code: str, count: int = 800) -> Optional[pd.DataFrame]:
        """
        获取日线数据
        
        Args:
            stock_code: 股票代码
            count: 获取数量
            
        Returns:
            日线数据DataFrame或None
        """
        return self.get_security_bars(stock_code, frequency=9, count=count)
    
    def get_xdxr_info(self, stock_code: str) -> Optional[pd.DataFrame]:
        """
        获取除权除息信息
        
        Args:
            stock_code: 股票代码
            
        Returns:
            除权除息信息DataFrame或None
        """
        if not self.connected:
            logger.error("未连接到TDX服务器")
            return None
        
        try:
            # 确定市场代码
            if stock_code.startswith(('60', '68')):
                market = 1  # 上海
            elif stock_code.startswith(('00', '30')):
                market = 0  # 深圳
            else:
                logger.warning(f"不支持的股票代码: {stock_code}")
                return None
            
            # 获取除权除息信息
            data = self.api.get_xdxr_info(market, stock_code)
            
            if data:
                df = pd.DataFrame(data)
                logger.debug(f"成功获取{stock_code}除权除息信息，共{len(df)}条记录")
                return df
            else:
                logger.warning(f"未获取到{stock_code}的除权除息信息")
                return None
                
        except Exception as e:
            logger.error(f"获取{stock_code}除权除息信息失败: {e}")
            return None
    
    def get_stock_list(self, market: str = "all") -> List[str]:
        """
        获取股票列表
        
        Args:
            market: 市场类型 ("sh", "sz", "all")
            
        Returns:
            股票代码列表
        """
        if not self.connected:
            logger.error("未连接到TDX服务器")
            return []
        
        stock_list = []
        
        try:
            if market in ["sh", "all"]:
                # 获取上海A股
                sh_stocks = self.api.get_security_list(1, 0)  # 上海A股
                for stock in sh_stocks:
                    if stock['code'].startswith(('60', '68')):
                        stock_list.append(stock['code'])
            
            if market in ["sz", "all"]:
                # 获取深圳A股
                sz_stocks = self.api.get_security_list(0, 0)  # 深圳A股
                for stock in sz_stocks:
                    if stock['code'].startswith(('00', '30')):
                        stock_list.append(stock['code'])
            
            logger.info(f"获取到{len(stock_list)}只股票")
            return sorted(stock_list)
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def is_trading_day(self, date: datetime) -> bool:
        """
        判断是否为交易日
        
        Args:
            date: 日期
            
        Returns:
            是否为交易日
        """
        # 简单实现：排除周末
        # 实际应该考虑节假日
        return date.weekday() < 5
    
    def get_latest_trading_day(self) -> datetime:
        """
        获取最新交易日
        
        Returns:
            最新交易日
        """
        today = datetime.now().date()
        current_date = datetime.combine(today, datetime.min.time())
        
        # 向前查找最近的交易日
        while not self.is_trading_day(current_date):
            current_date -= timedelta(days=1)
        
        return current_date
    
    def cleanup(self):
        """清理资源"""
        self.disconnect()
        logger.debug("PyTDX数据源资源已清理")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()


# 导出
__all__ = ['PyTDXDataSource']
