#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置迁移示例

演示如何将现有代码从旧配置系统迁移到新配置系统
"""

def example_old_way():
    """旧的配置使用方式"""
    print("📋 旧的配置使用方式:")
    
    try:
        # 旧方式：直接导入user_config
        import user_config
        
        print(f"   TDX路径: {getattr(user_config, 'tdx_path', 'Not found')}")
        print(f"   调试模式: {getattr(user_config, 'DEBUG', 'Not found')}")
        
        # 旧方式：访问复杂配置
        output_config = getattr(user_config, 'output_storage', {})
        if isinstance(output_config, dict):
            print(f"   输出路径: {output_config.get('base_directory', 'Not found')}")
        else:
            print(f"   输出路径: Not found")
        
        return True
        
    except ImportError:
        print("   ⚠️ user_config 不可用")
        return False
    except Exception as e:
        print(f"   ❌ 旧配置访问失败: {e}")
        return False

def example_compatibility_way():
    """兼容性配置使用方式"""
    print("\n📋 兼容性配置使用方式:")
    
    try:
        # 兼容性方式：使用config_compatibility
        import config_compatibility as config
        
        print(f"   TDX路径: {config.tdx_path}")
        print(f"   调试模式: {config.DEBUG}")
        print(f"   输出路径: {config.output_path}")
        print(f"   智能文件选择器: {config.smart_file_selector_enabled}")
        print(f"   文件编码: {config.file_encoding}")
        
        # 兼容性方式：使用函数访问
        print(f"   函数访问TDX路径: {config.get_tdx_path()}")
        print(f"   函数访问调试模式: {config.is_debug_enabled()}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ config_compatibility 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 兼容性配置访问失败: {e}")
        return False

def example_new_way():
    """新的配置使用方式"""
    print("\n📋 新的配置使用方式:")
    
    try:
        import sys
        from pathlib import Path
        
        # 添加src目录到路径
        project_root = Path(__file__).parent
        src_path = project_root / "src"
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        # 新方式：使用新配置系统
        from mythquant.config import config_manager
        
        print(f"   TDX路径: {config_manager.get_tdx_path()}")
        print(f"   调试模式: {config_manager.is_debug_enabled()}")
        print(f"   输出路径: {config_manager.get_output_path()}")
        print(f"   智能文件选择器: {config_manager.is_smart_file_selector_enabled()}")
        print(f"   增量下载: {config_manager.is_incremental_download_enabled()}")
        print(f"   文件编码: {config_manager.get_file_encoding()}")
        
        # 新方式：使用通用配置访问
        print(f"   通用访问调试模式: {config_manager.get('debug', False)}")
        print(f"   通用访问TDX路径: {config_manager.get('tdx.tdx_path', 'Unknown')}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 新配置系统导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 新配置访问失败: {e}")
        return False

def demonstrate_migration_steps():
    """演示迁移步骤"""
    print("\n🔄 配置迁移步骤演示:")
    print("=" * 50)
    
    print("\n步骤1: 保持现有代码不变，测试兼容性")
    print("```python")
    print("# 原代码")
    print("import user_config")
    print("tdx_path = user_config.tdx_path")
    print("```")
    
    print("\n步骤2: 替换导入，保持访问方式不变")
    print("```python")
    print("# 迁移后代码（第一阶段）")
    print("import config_compatibility as user_config")
    print("tdx_path = user_config.tdx_path")
    print("```")
    
    print("\n步骤3: 逐步采用新的配置API")
    print("```python")
    print("# 迁移后代码（最终目标）")
    print("from mythquant.config import config_manager")
    print("tdx_path = config_manager.get_tdx_path()")
    print("```")

def create_migration_checklist():
    """创建迁移检查清单"""
    print("\n📝 创建迁移检查清单...")
    
    checklist = """# 配置迁移检查清单

## 迁移前准备
- [ ] 备份现有的user_config.py文件
- [ ] 确认config_compatibility.py文件存在
- [ ] 测试新配置系统可用性

## 迁移执行
- [ ] 找到所有使用 `import user_config` 的文件
- [ ] 逐个文件替换为 `import config_compatibility as user_config`
- [ ] 测试每个修改后的文件功能正常

## 迁移验证
- [ ] 运行主程序，确保正常启动
- [ ] 验证所有配置项都能正确读取
- [ ] 检查输出结果与之前一致
- [ ] 测试错误处理和边界情况

## 迁移完成
- [ ] 所有功能测试通过
- [ ] 性能无明显下降
- [ ] 错误日志无配置相关问题
- [ ] 团队成员确认迁移成功

## 后续优化（可选）
- [ ] 逐步将配置访问改为新API
- [ ] 移除不再使用的旧配置文件
- [ ] 更新文档和注释
"""
    
    with open("config_migration_checklist.md", "w", encoding="utf-8") as f:
        f.write(checklist)
    
    print("   ✅ 检查清单已创建: config_migration_checklist.md")

def main():
    """主演示函数"""
    print("🎯 MythQuant 配置迁移示例")
    print("=" * 60)
    
    # 演示不同的配置使用方式
    old_success = example_old_way()
    compat_success = example_compatibility_way()
    new_success = example_new_way()
    
    # 演示迁移步骤
    demonstrate_migration_steps()
    
    # 创建迁移检查清单
    create_migration_checklist()
    
    # 总结
    print("\n" + "=" * 60)
    print("📊 配置系统可用性总结:")
    print(f"   旧配置系统: {'✅ 可用' if old_success else '❌ 不可用'}")
    print(f"   兼容性模块: {'✅ 可用' if compat_success else '❌ 不可用'}")
    print(f"   新配置系统: {'✅ 可用' if new_success else '❌ 不可用'}")
    
    if compat_success:
        print("\n🎉 配置迁移准备就绪！")
        print("\n💡 建议的迁移策略:")
        print("1. 🔄 使用兼容性模块进行无缝迁移")
        print("2. 🧪 逐个文件测试和验证")
        print("3. 🚀 逐步采用新配置API")
        print("4. 🧹 清理旧配置文件")
        
        print("\n📋 立即行动:")
        print("• 查看 config_migration_checklist.md 获取详细步骤")
        print("• 开始替换第一个使用user_config的文件")
        print("• 测试功能是否正常工作")
        
        return True
    else:
        print("\n⚠️ 配置迁移需要先解决问题！")
        print("\n🔧 需要检查:")
        if not compat_success:
            print("• config_compatibility.py 模块问题")
        if not new_success:
            print("• 新配置系统导入问题")
        
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*60}")
    if success:
        print("✅ 配置迁移示例运行成功 - 可以开始迁移！")
    else:
        print("❌ 配置迁移示例发现问题 - 需要先修复！")
    exit(0 if success else 1)
