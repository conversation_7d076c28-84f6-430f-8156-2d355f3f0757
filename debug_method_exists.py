#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试方法是否存在
"""

import importlib
import sys

def debug_method_exists():
    """调试方法是否存在"""
    print("🔍 调试TaskManager方法是否存在")
    print("=" * 50)
    
    try:
        # 强制重新加载模块
        if 'src.mythquant.core.task_manager' in sys.modules:
            importlib.reload(sys.modules['src.mythquant.core.task_manager'])
        
        from src.mythquant.core.task_manager import TaskManager
        from src.mythquant.core.stock_processor import StockDataProcessor
        
        # 创建实例
        stock_processor = StockDataProcessor("")
        task_manager = TaskManager(stock_processor)
        
        print(f"✅ TaskManager实例创建成功")
        
        # 获取所有方法
        methods = [method for method in dir(task_manager) if not method.startswith('__')]
        print(f"📊 TaskManager所有方法 ({len(methods)}个):")
        
        for method in sorted(methods):
            if method.startswith('_execute'):
                print(f"   ✅ {method}")
        
        # 特别检查internet_minute_task方法
        if hasattr(task_manager, '_execute_internet_minute_task'):
            print(f"\n🎯 _execute_internet_minute_task方法:")
            print(f"   ✅ 方法存在")
            method = getattr(task_manager, '_execute_internet_minute_task')
            print(f"   📊 方法类型: {type(method)}")
            print(f"   📊 可调用: {callable(method)}")
            
            # 检查方法的源代码位置
            import inspect
            try:
                source_lines = inspect.getsourcelines(method)
                print(f"   📊 源代码行数: {len(source_lines[0])}")
                print(f"   📊 起始行号: {source_lines[1]}")
            except Exception as e:
                print(f"   ⚠️ 无法获取源代码信息: {e}")
            
            return True
        else:
            print(f"\n❌ _execute_internet_minute_task方法不存在")
            return False
            
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


if __name__ == '__main__':
    success = debug_method_exists()
    if success:
        print(f"\n🎉 方法存在，问题可能在其他地方")
    else:
        print(f"\n⚠️ 方法确实不存在")
