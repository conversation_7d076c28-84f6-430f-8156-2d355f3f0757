#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细测试任务配置是否正确
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_user_config():
    """测试user_config.py中的配置"""
    print("🔍 测试user_config.py中的配置")
    print("=" * 50)
    
    try:
        import user_config
        
        time_ranges = getattr(user_config, 'time_ranges', {})
        internet_minute = time_ranges.get('internet_minute', {})
        
        print(f"📊 internet_minute配置:")
        print(f"   enabled: {internet_minute.get('enabled')}")
        print(f"   start_date: {internet_minute.get('start_date')}")
        print(f"   end_date: {internet_minute.get('end_date')}")
        print(f"   frequency: {internet_minute.get('frequency')}")
        
        return internet_minute
        
    except Exception as e:
        print(f"❌ 读取user_config.py失败: {e}")
        return None

def test_ddd_config_adapter():
    """测试DDD配置适配器"""
    print("\n🔍 测试DDD配置适配器")
    print("=" * 50)
    
    try:
        from src.mythquant.infrastructure.config.adapters.user_config_adapter import UserConfigAdapter
        
        adapter = UserConfigAdapter()
        task_configs = adapter.get_task_configs_data()
        
        print(f"📊 DDD适配器任务配置:")
        print(f"   任务数量: {len(task_configs)}")
        
        for i, task in enumerate(task_configs, 1):
            print(f"\n   📋 任务 {i}:")
            print(f"      名称: {task.get('name', 'Unknown')}")
            print(f"      类型: {task.get('data_type', 'Unknown')}")
            print(f"      启用: {'✅' if task.get('enabled') else '❌'}")
            print(f"      时间: {task.get('start_time')} ~ {task.get('end_time')}")
            print(f"      频率: {task.get('frequency', 'Unknown')}")
        
        return task_configs
        
    except Exception as e:
        print(f"❌ 测试DDD配置适配器失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_legacy_config_manager():
    """测试传统配置管理器"""
    print("\n🔍 测试传统配置管理器")
    print("=" * 50)
    
    try:
        from src.mythquant.core.config_manager import ConfigManager
        
        config_manager = ConfigManager()
        task_configs = config_manager.get_task_configs()
        
        print(f"📊 传统配置管理器任务配置:")
        print(f"   任务数量: {len(task_configs)}")
        
        for i, task in enumerate(task_configs, 1):
            print(f"\n   📋 任务 {i}:")
            print(f"      名称: {task.get('name', 'Unknown')}")
            print(f"      类型: {task.get('data_type', 'Unknown')}")
            print(f"      启用: {'✅' if task.get('enabled') else '❌'}")
            print(f"      时间: {task.get('start_time')} ~ {task.get('end_time')}")
            print(f"      频率: {task.get('frequency', 'Unknown')}")
        
        return task_configs
        
    except Exception as e:
        print(f"❌ 测试传统配置管理器失败: {e}")
        import traceback
        traceback.print_exc()
        return None

def main():
    """主函数"""
    print("🚀 任务配置详细测试")
    print("=" * 60)
    
    # 测试user_config.py
    user_config_data = test_user_config()
    
    # 测试DDD配置适配器
    ddd_config_data = test_ddd_config_adapter()
    
    # 测试传统配置管理器
    legacy_config_data = test_legacy_config_manager()
    
    # 对比结果
    print("\n" + "=" * 60)
    print("🔍 配置对比结果")
    print("=" * 60)
    
    if user_config_data:
        expected_start = user_config_data.get('start_date')
        expected_end = user_config_data.get('end_date')
        print(f"📋 user_config.py期望: {expected_start} ~ {expected_end}")
        
        configs_to_check = [
            ("DDD适配器", ddd_config_data),
            ("传统配置管理器", legacy_config_data)
        ]
        
        for name, config_data in configs_to_check:
            if config_data and len(config_data) > 0:
                actual_start = config_data[0].get('start_time')
                actual_end = config_data[0].get('end_time')
                
                start_match = actual_start == expected_start
                end_match = actual_end == expected_end
                
                print(f"📊 {name}: {actual_start} ~ {actual_end}")
                print(f"   开始时间: {'✅' if start_match else '❌'}")
                print(f"   结束时间: {'✅' if end_match else '❌'}")
                
                if not start_match or not end_match:
                    print(f"   ⚠️ 配置不匹配！")
            else:
                print(f"❌ {name}: 无配置数据")

if __name__ == '__main__':
    main()
