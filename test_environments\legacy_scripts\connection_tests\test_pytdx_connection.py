#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pytdx连接功能
调试和修复pytdx服务器连接问题
"""

import sys
import os

# 添加项目根目录到路径
project_root = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, project_root)
sys.path.insert(0, os.path.join(project_root, 'src'))

def test_pytdx_basic_connection():
    """测试pytdx基本连接功能"""
    print("🧪 [1/4] 测试pytdx基本连接功能")
    print("=" * 60)
    
    try:
        from pytdx.hq import TdxHq_API
        print("   ✅ pytdx模块导入成功")
        
        api = TdxHq_API()
        print("   ✅ TdxHq_API实例创建成功")
        
        # 测试连接到通达信服务器
        servers = [
            ('**************', 7709),  # 通达信主服务器
            ('************', 7709),    # 备用服务器1
            ('*************', 7709),   # 备用服务器2
        ]
        
        connected = False
        for ip, port in servers:
            try:
                print(f"   🔄 尝试连接服务器: {ip}:{port}")
                if api.connect(ip, port):
                    print(f"   ✅ 连接成功: {ip}:{port}")
                    
                    # 测试获取数据
                    try:
                        test_data = api.get_security_list(0, 0)  # 获取深圳市场股票列表
                        if test_data:
                            print(f"   ✅ 数据获取测试成功，获取到{len(test_data)}条记录")
                            connected = True
                            break
                        else:
                            print(f"   ⚠️ 连接成功但无法获取数据")
                    except Exception as e:
                        print(f"   ❌ 数据获取测试失败: {e}")
                    
                    api.disconnect()
                else:
                    print(f"   ❌ 连接失败: {ip}:{port}")
            except Exception as e:
                print(f"   ❌ 连接异常: {ip}:{port} - {e}")
        
        if connected:
            print("   🎉 pytdx基本连接功能正常")
            return True
        else:
            print("   ❌ 所有服务器连接失败")
            return False
            
    except ImportError as e:
        print(f"   ❌ pytdx模块导入失败: {e}")
        print("   💡 请安装pytdx: pip install pytdx")
        return False
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False


def test_pytdx_downloader():
    """测试pytdx下载器"""
    print("\n🧪 [2/4] 测试pytdx下载器")
    print("=" * 60)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        print("   ✅ PytdxDownloader导入成功")
        
        downloader = PytdxDownloader()
        print("   ✅ PytdxDownloader实例创建成功")
        
        # 测试连接
        print("   🔄 测试服务器连接...")
        api = downloader.connect_to_server()
        
        if api:
            print("   ✅ 服务器连接成功")
            
            # 测试获取分钟数据
            print("   🔄 测试获取分钟数据...")
            try:
                df = downloader.download_minute_data('000617', '20250801', '20250803', '1min')
                if df is not None and not df.empty:
                    print(f"   ✅ 分钟数据获取成功: {len(df)} 条记录")
                    print(f"   📊 数据列: {list(df.columns)}")
                    if len(df) > 0:
                        print(f"   📋 样本数据: {df.iloc[0].to_dict()}")
                else:
                    print("   ⚠️ 未获取到分钟数据（可能是时间范围问题）")
                
                api.disconnect()
                return True
                
            except Exception as e:
                print(f"   ❌ 分钟数据获取失败: {e}")
                api.disconnect()
                return False
        else:
            print("   ❌ 服务器连接失败")
            return False
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_stock_processor_integration():
    """测试StockProcessor集成"""
    print("\n🧪 [3/4] 测试StockProcessor集成")
    print("=" * 60)
    
    try:
        from mythquant.core.stock_processor import StockDataProcessor
        print("   ✅ StockDataProcessor导入成功")
        
        processor = StockDataProcessor("./test_tdx")
        print("   ✅ StockDataProcessor实例创建成功")
        
        # 测试数据处理方法
        print("   🔄 测试load_and_process_minute_data方法...")
        result = processor.load_and_process_minute_data(
            'sz000617', 
            '2025-08-01 09:30:00', 
            '2025-08-03 15:00:00'
        )
        
        if result is not None and not result.empty:
            print(f"   ✅ 数据处理成功: {len(result)} 条记录")
            print(f"   📊 输出列: {list(result.columns)}")
            if len(result) > 0:
                print(f"   📋 样本数据: {result.iloc[0].to_dict()}")
            return True
        else:
            print("   ⚠️ 数据处理返回空结果")
            return True  # 这是预期的，因为可能没有数据
            
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_main_integration():
    """测试main.py集成"""
    print("\n🧪 [4/4] 测试main.py集成")
    print("=" * 60)
    
    try:
        print("   🔄 运行main.py...")
        
        # 运行main.py并捕获输出
        import subprocess
        result = subprocess.run(
            [sys.executable, 'main.py'],
            capture_output=True,
            text=True,
            timeout=60
        )
        
        if result.returncode == 0:
            print("   ✅ main.py执行成功")
            
            # 检查输出中是否包含成功信息
            output = result.stdout
            if "分钟数据生成完成" in output or "数据处理完成" in output:
                print("   ✅ 包含数据处理成功信息")
            else:
                print("   ⚠️ 未包含数据处理成功信息（可能是预期的）")
            
            return True
        else:
            print(f"   ❌ main.py执行失败，返回码: {result.returncode}")
            if result.stderr:
                print(f"   ❌ 错误信息: {result.stderr}")
            return False
            
    except subprocess.TimeoutExpired:
        print("   ⚠️ main.py执行超时（60秒）")
        return False
    except Exception as e:
        print(f"   ❌ 测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🎯 pytdx连接问题诊断和修复测试")
    print("=" * 80)
    print("📋 测试目标: 诊断和修复pytdx服务器连接问题")
    print("=" * 80)
    
    test_results = []
    
    # 执行各项测试
    test_results.append(test_pytdx_basic_connection())
    test_results.append(test_pytdx_downloader())
    test_results.append(test_stock_processor_integration())
    test_results.append(test_main_integration())
    
    # 汇总结果
    print("\n" + "=" * 80)
    print("📊 pytdx连接测试结果汇总")
    print("=" * 80)
    
    passed_tests = sum(test_results)
    total_tests = len(test_results)
    success_rate = (passed_tests / total_tests) * 100
    
    print(f"✅ 通过测试: {passed_tests}/{total_tests}")
    print(f"📈 成功率: {success_rate:.1f}%")
    
    if success_rate >= 75:
        print("\n🎉 结论: pytdx连接功能基本正常！")
        print("💡 可以进行实际数据下载测试")
    elif success_rate >= 50:
        print("\n⚠️ 结论: pytdx连接功能部分正常")
        print("💡 需要进一步调试网络连接问题")
    else:
        print("\n❌ 结论: pytdx连接功能存在严重问题")
        print("💡 需要检查网络环境和pytdx安装")
    
    return success_rate >= 75


if __name__ == '__main__':
    success = main()
    if success:
        print("\n🎯 下一步: 可以进行完整的1分钟数据下载测试")
    else:
        print("\n🔧 下一步: 需要修复pytdx连接问题")
