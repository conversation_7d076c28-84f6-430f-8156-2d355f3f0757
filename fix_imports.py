#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统性修复导入问题
"""

import os
import re
from pathlib import Path

def fix_imports_in_file(file_path):
    """修复单个文件中的导入问题"""
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # 修复导入路径
        import_fixes = [
            (r'from mythquant\.shared\.logging import', 'from src.mythquant.shared.logging import'),
            (r'from mythquant\.utils\.chunk_processor import', 'from src.mythquant.utils.chunk_processor import'),
            (r'from mythquant\.utils\.async_io_processor import', 'from src.mythquant.utils.async_io_processor import'),
            (r'from mythquant\.core\.', 'from src.mythquant.core.'),
            (r'from mythquant\.config\.', 'from src.mythquant.config.'),
            (r'from mythquant\.domain\.', 'from src.mythquant.domain.'),
            (r'from mythquant\.infrastructure\.', 'from src.mythquant.infrastructure.'),
            (r'from mythquant\.application\.', 'from src.mythquant.application.'),
        ]
        
        for pattern, replacement in import_fixes:
            content = re.sub(pattern, replacement, content)
        
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            print(f"✅ 修复导入: {file_path}")
            return True
        else:
            return False
            
    except Exception as e:
        print(f"❌ 修复失败 {file_path}: {e}")
        return False

def find_python_files(directory):
    """查找所有Python文件"""
    python_files = []
    for root, dirs, files in os.walk(directory):
        # 跳过一些不需要的目录
        dirs[:] = [d for d in dirs if not d.startswith('.') and d not in ['__pycache__', 'node_modules']]
        
        for file in files:
            if file.endswith('.py'):
                python_files.append(Path(root) / file)
    
    return python_files

def main():
    """主函数"""
    print("🔧 系统性修复导入问题")
    print("=" * 50)
    
    # 查找src目录下的所有Python文件
    src_dir = Path('src')
    if not src_dir.exists():
        print("❌ src目录不存在")
        return
    
    python_files = find_python_files(src_dir)
    print(f"📊 找到 {len(python_files)} 个Python文件")
    
    fixed_count = 0
    for file_path in python_files:
        if fix_imports_in_file(file_path):
            fixed_count += 1
    
    print(f"\n📈 修复完成: {fixed_count}/{len(python_files)} 个文件")
    
    if fixed_count > 0:
        print("✅ 导入问题修复完成，请重新测试")
    else:
        print("ℹ️ 没有发现需要修复的导入问题")

if __name__ == '__main__':
    main()
