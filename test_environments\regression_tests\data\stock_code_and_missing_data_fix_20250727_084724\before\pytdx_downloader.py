#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基于pytdx的1分钟数据下载器
无需MongoDB，直接从通达信服务器获取真正的1分钟数据
"""

import os
import sys
import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from typing import Dict, List, Tuple, Optional, Any
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.enhanced_error_handler import get_smart_logger, get_error_handler, ErrorCategory


class PytdxDownloader:
    """基于pytdx的数据下载器"""
    
    def __init__(self, output_dir: str = "H:/MPV1.17/T0002/signals/"):
        """
        初始化pytdx下载器
        
        Args:
            output_dir: 数据文件输出目录
        """
        self.output_dir = output_dir
        self.smart_logger = get_smart_logger("PytdxDownloader")
        self.error_handler = get_error_handler()

        # 缓存的最佳服务器连接
        self._cached_api = None
        self._cached_server_info = None
        self._cache_time = 0
        self._cache_timeout = 300  # 5分钟缓存

        # 通达信服务器列表（备用）
        self.tdx_servers = [
            ('**************', 7709),  # 通达信主服务器
            ('************', 7709),    # 备用服务器1
            ('*************', 7709),   # 备用服务器2
        ]
        
        # 频率映射
        self.frequency_map = {
            '1min': 8,   # 1分钟
            '5min': 0,   # 5分钟
            '15min': 1,  # 15分钟
            '30min': 2,  # 30分钟
            '60min': 3,  # 60分钟
            'daily': 9   # 日线
        }
    
    def get_market_code(self, stock_code: str) -> int:
        """
        获取市场代码
        
        Args:
            stock_code: 股票代码（如000617）
            
        Returns:
            市场代码（0=深圳，1=上海）
        """
        if stock_code.startswith(('000', '002', '003', '300')):
            return 0  # 深圳市场
        elif stock_code.startswith(('600', '601', '603', '605', '688')):
            return 1  # 上海市场
        else:
            # 默认深圳
            return 0
    
    def connect_to_server(self):
        """
        连接到通达信服务器（使用缓存避免重复检测）

        Returns:
            连接成功的API对象或None
        """
        try:
            import time
            from pytdx.hq import TdxHq_API

            # 检查缓存的连接是否仍然有效
            current_time = time.time()
            if (self._cached_api and
                self._cached_server_info and
                current_time - self._cache_time < self._cache_timeout):

                # 测试缓存的连接是否仍然可用
                try:
                    # 简单测试：获取股票列表
                    test_result = self._cached_api.get_security_list(0, 0)
                    if test_result:
                        self.smart_logger.info(f"⚡ 使用缓存连接: {self._cached_server_info['ip']}:{self._cached_server_info['port']}")
                        return self._cached_api
                except:
                    # 缓存连接失效，清除缓存
                    self._cached_api = None
                    self._cached_server_info = None

            api = TdxHq_API()

            # 首先尝试使用配置文件中的最佳服务器
            try:
                import user_config
                tdx_config = getattr(user_config, 'tdx', {})
                best_ip = tdx_config.get('pytdx_ip', '')
                best_port = tdx_config.get('pytdx_port', 7709)

                if best_ip and self._is_valid_ip(best_ip):
                    self.smart_logger.info(f"🔄 尝试连接配置的最佳服务器: {best_ip}:{best_port}")
                    if api.connect(best_ip, best_port):
                        self.smart_logger.info(f"✅ 连接配置的最佳服务器成功: {best_ip}:{best_port}")

                        # 缓存成功的连接
                        self._cached_api = api
                        self._cached_server_info = {'ip': best_ip, 'port': best_port}
                        self._cache_time = current_time

                        return api
                    else:
                        self.smart_logger.warning(f"⚠️ 配置的最佳服务器连接失败: {best_ip}:{best_port}")
            except Exception as e:
                self.smart_logger.warning(f"读取配置服务器失败: {e}")

            # 尝试使用智能服务器管理器获取缓存的良好服务器
            try:
                from utils.smart_server_manager import SmartServerManager
                smart_manager = SmartServerManager()

                # 获取缓存的良好服务器
                good_servers = smart_manager.get_good_servers(max_age=1800)  # 30分钟缓存

                if good_servers:
                    # 尝试连接缓存的良好服务器
                    for server_info in good_servers[:3]:  # 只尝试前3个最好的
                        try:
                            server_ip = server_info['ip']
                            server_port = server_info['port']

                            self.smart_logger.info(f"🔄 尝试连接缓存的良好服务器: {server_ip}:{server_port}")
                            if api.connect(server_ip, server_port):
                                self.smart_logger.info(f"✅ 连接缓存的良好服务器成功: {server_ip}:{server_port}")

                                # 缓存成功的连接
                                self._cached_api = api
                                self._cached_server_info = {'ip': server_ip, 'port': server_port}
                                self._cache_time = current_time

                                return api
                            else:
                                # 连接失败，更新服务器状态
                                smart_manager.update_server_status(server_ip, server_port, False, 0.0, server_info.get('name', ''))
                        except Exception as e:
                            self.smart_logger.warning(f"尝试缓存服务器失败: {e}")
                else:
                    self.smart_logger.info("未找到缓存的良好服务器")

            except Exception as e:
                self.smart_logger.warning(f"智能服务器管理器失败: {e}")

            # 最后尝试备用服务器列表
            backup_servers = [
                ('**************', 7709),
                ('************', 7709),
                ('*************', 7709),
            ]

            for server_ip, server_port in backup_servers:
                try:
                    self.smart_logger.info(f"🔄 尝试连接备用服务器: {server_ip}:{server_port}")
                    if api.connect(server_ip, server_port):
                        self.smart_logger.info(f"✅ 连接备用服务器成功: {server_ip}:{server_port}")
                        return api
                except Exception as e:
                    self.smart_logger.warning(f"备用服务器连接失败 {server_ip}:{server_port}: {e}")
                    continue

            self.smart_logger.error("❌ 所有通达信服务器连接失败")
            return None

        except ImportError:
            self.smart_logger.error("❌ pytdx未安装，请运行: pip install pytdx")
            return None
        except Exception as e:
            self.smart_logger.error(f"连接服务器异常: {e}")
            return None

    def _is_valid_ip(self, ip: str) -> bool:
        """验证是否为有效的IP地址"""
        try:
            import ipaddress
            ipaddress.ip_address(ip)
            return True
        except ValueError:
            return False
    
    def download_minute_data(self, stock_code: str, start_date: str, end_date: str, 
                           frequency: str = '1min') -> Optional[pd.DataFrame]:
        """
        下载分钟级数据
        
        Args:
            stock_code: 股票代码（如000617）
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率（1min, 5min, 15min, 30min, 60min）
            
        Returns:
            DataFrame或None
        """
        try:
            self.smart_logger.info(f"🔄 开始下载 {stock_code} {frequency} 数据: {start_date} - {end_date}")
            
            # 连接服务器
            api = self.connect_to_server()
            if api is None:
                return None
            
            # 获取市场代码和频率代码
            market = self.get_market_code(stock_code)
            category = self.frequency_map.get(frequency, 8)  # 默认1分钟
            
            # 智能计算需要获取的数据量（基于实际交易日）
            estimated_count = self._calculate_smart_data_count(start_date, frequency)
            
            # 根据配置获取K线数量限制
            try:
                import user_config
                tdx_config = getattr(user_config, 'tdx', {})
                kline_limits = tdx_config.get('pytdx_kline_limits', {})

                # 获取对应频率的限制
                limit_key = frequency if frequency in kline_limits else 'daily'
                max_limit = kline_limits.get(limit_key, 800)

                max_count = min(estimated_count, max_limit)

                self.smart_logger.info(f"📊 数据量限制: 预计{estimated_count}条 -> 实际请求{max_count}条 (配置限制:{max_limit})")

            except Exception as e:
                # 如果配置读取失败，使用默认值
                max_count = min(estimated_count, 800)
                self.smart_logger.warning(f"读取K线限制配置失败，使用默认值800: {e}")
                self.smart_logger.info(f"📊 数据量限制: 预计{estimated_count}条 -> 实际请求{max_count}条 (默认限制:800)")
            
            self.smart_logger.info(f"📊 预计获取 {max_count} 条 {frequency} 数据")
            
            # 确保股票代码是字符串格式
            stock_code_str = str(stock_code).zfill(6)  # 补齐到6位

            # 获取数据
            self.smart_logger.info(f"📊 调用get_security_bars: category={category}, market={market}, code={stock_code}, count={max_count}")

            # 尝试多次获取数据（有时服务器响应不稳定）
            data = None
            for attempt in range(3):  # 最多尝试3次
                try:
                    data = api.get_security_bars(category, market, stock_code_str, 0, max_count)
                    if data:
                        break
                    else:
                        self.smart_logger.warning(f"⚠️ 第{attempt+1}次尝试未获得数据")
                        if attempt < 2:  # 不是最后一次尝试
                            import time
                            time.sleep(1)  # 等待1秒后重试
                except Exception as e:
                    self.smart_logger.warning(f"⚠️ 第{attempt+1}次尝试出错: {e}")
                    if attempt < 2:
                        import time
                        time.sleep(1)

            # 断开连接
            api.disconnect()

            if not data:
                self.smart_logger.warning(f"⚠️ 未获得 {stock_code} 的数据")
                # 尝试其他市场代码
                if market == 0:
                    self.smart_logger.info(f"🔄 尝试上海市场获取 {stock_code} 数据")
                    api2 = self.connect_to_server()
                    if api2:
                        data = api2.get_security_bars(category, 1, stock_code_str, 0, max_count)
                        api2.disconnect()
                        if data:
                            self.smart_logger.info(f"✅ 在上海市场找到 {stock_code} 数据")
                        else:
                            self.smart_logger.warning(f"⚠️ 上海市场也未找到 {stock_code} 数据")

                if not data:
                    return None

            # 如果需要更多数据且单次获取达到上限，尝试分批获取
            if estimated_count > 800 and len(data) == max_count:
                self.smart_logger.info(f"🔄 需要分批获取更多数据: 已获取{len(data)}条，还需{estimated_count - len(data)}条")

                # 分批获取剩余数据
                all_data = list(data)  # 转换为列表
                start_pos = len(data)

                while start_pos < estimated_count and len(all_data) < estimated_count:
                    remaining = min(800, estimated_count - len(all_data))

                    try:
                        api2 = self.connect_to_server()
                        if api2:
                            batch_data = api2.get_security_bars(category, market, stock_code_str, start_pos, remaining)
                            api2.disconnect()

                            if batch_data and len(batch_data) > 0:
                                all_data.extend(batch_data)
                                start_pos += len(batch_data)
                                self.smart_logger.info(f"📊 分批获取成功: +{len(batch_data)}条，总计{len(all_data)}条")

                                # 如果获取的数据少于请求量，说明没有更多数据了
                                if len(batch_data) < remaining:
                                    break
                            else:
                                break
                        else:
                            break
                    except Exception as e:
                        self.smart_logger.warning(f"分批获取失败: {e}")
                        break

                data = all_data
                self.smart_logger.info(f"✅ 分批获取完成: 总计{len(data)}条数据")

                # 检查数据覆盖范围是否充足
                if len(data) < estimated_count:
                    shortage = estimated_count - len(data)
                    shortage_days = shortage // 240  # 转换为交易日

                    self.smart_logger.warning(f"⚠️ 数据覆盖不足: 需要{estimated_count}条，实际获取{len(data)}条")
                    self.smart_logger.warning(f"⚠️ 缺少约{shortage}条数据（约{shortage_days}个交易日）")
                    self.smart_logger.warning(f"⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据")

                    if shortage_days > 30:  # 缺少超过30个交易日
                        self.smart_logger.warning(f"⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据")

                    print(f"⚠️ 注意：pytdx数据覆盖不足，缺少约{shortage_days}个交易日的数据")
                    print(f"   如需完整的{start_date}数据，建议使用其他数据源")

            # 转换为DataFrame
            df = pd.DataFrame(data)
            
            # 处理时间字段
            df['datetime'] = pd.to_datetime(df['datetime'])
            
            # 过滤时间范围
            start_dt = pd.to_datetime(start_date, format='%Y%m%d')
            end_dt = pd.to_datetime(end_date, format='%Y%m%d') + pd.Timedelta(days=1)
            
            df = df[(df['datetime'] >= start_dt) & (df['datetime'] < end_dt)]
            
            if len(df) == 0:
                self.smart_logger.warning(f"⚠️ 时间范围内无数据: {start_date} - {end_date}")
                return None
            
            # 按时间排序
            df = df.sort_values('datetime').reset_index(drop=True)
            
            self.smart_logger.info(f"✅ 成功获取 {len(df)} 条 {frequency} 数据")
            
            return df
            
        except Exception as e:
            self.smart_logger.error(f"下载分钟数据失败: {e}")
            return None

    def _calculate_smart_data_count(self, start_date: str, frequency: str) -> int:
        """
        根据目标日期智能计算需要获取的数据条数
        基于实际交易日数量动态计算，确保覆盖目标日期

        Args:
            start_date: 目标开始日期 YYYYMMDD
            frequency: 频率 ('1min', '5min', '15min', '30min', '60min')

        Returns:
            int: 智能计算的数据条数
        """
        try:
            from datetime import datetime, timedelta

            # 计算从目标日期到今天的交易日数量
            start_dt = datetime.strptime(start_date, '%Y%m%d')
            end_dt = datetime.now()

            # 确保开始日期不晚于结束日期
            if start_dt > end_dt:
                start_dt, end_dt = end_dt, start_dt

            trading_days = 0
            current_dt = start_dt

            while current_dt <= end_dt:
                # 排除周末（周六=5, 周日=6）
                if current_dt.weekday() < 5:
                    trading_days += 1
                current_dt += timedelta(days=1)

            # 根据频率计算每日数据条数
            # A股交易时间：上午9:30-11:30，下午13:00-15:00，共4小时=240分钟
            if frequency == '1min':
                bars_per_day = 240  # 1分钟数据
            elif frequency == '5min':
                bars_per_day = 48   # 5分钟数据
            elif frequency == '15min':
                bars_per_day = 16   # 15分钟数据
            elif frequency == '30min':
                bars_per_day = 8    # 30分钟数据
            elif frequency == '60min':
                bars_per_day = 4    # 60分钟数据
            else:
                bars_per_day = 240  # 默认1分钟数据

            # 计算总需求量
            required_count = trading_days * bars_per_day

            # 添加20%缓冲区，确保数据充足
            buffered_count = int(required_count * 1.2)

            # 设置合理的上下限
            min_count = 1000   # 最少获取1000条，确保基本覆盖
            max_count = 50000  # 最多获取50000条，确保能覆盖长期历史数据

            final_count = max(min_count, min(buffered_count, max_count))

            self.smart_logger.info(f"📊 智能计算数据量: {final_count}条 (目标日期: {start_date}, 交易日: {trading_days}天, 频率: {frequency})")

            return final_count

        except Exception as e:
            self.smart_logger.warning(f"智能计算数据量失败，使用默认值: {e}")
            return 3000  # 默认值
    
    def convert_to_target_format(self, df: pd.DataFrame, stock_code: str, 
                                data_type: str = "minute") -> pd.DataFrame:
        """
        转换为目标格式
        
        Args:
            df: 原始数据DataFrame
            stock_code: 股票代码
            data_type: 数据类型
            
        Returns:
            转换后的DataFrame
        """
        try:
            if df.empty:
                return pd.DataFrame()
            
            # 创建目标格式的DataFrame
            result_df = pd.DataFrame()
            
            # 确保股票代码格式正确（6位数字）
            formatted_stock_code = str(stock_code).zfill(6)
            result_df['股票编码'] = [formatted_stock_code] * len(df)
            
            # 根据数据类型设置时间格式
            if data_type == "minute":
                # 分钟级数据：12位数字格式 YYYYMMDDHHMM（如202501020931）
                result_df['时间'] = df['datetime'].dt.strftime('%Y%m%d%H%M')
            else:
                # 日线数据只保留到日
                result_df['时间'] = df['datetime'].dt.strftime('%Y%m%d')
            
            result_df['买卖差'] = [0.0] * len(df)  # pytdx数据无买卖差信息
            result_df['当日收盘价C'] = df['close'].round(3)
            
            # pytdx获取的是原始价格，需要计算前复权价格
            self.smart_logger.warning("⚠️ pytdx只提供原始价格，正在尝试计算前复权价格...")

            try:
                # 尝试使用本地前复权计算
                adjusted_prices = self._calculate_forward_adjustment_pytdx(df, stock_code)
                if adjusted_prices is not None:
                    result_df['前复权收盘价C'] = adjusted_prices.round(3)
                    self.smart_logger.info("✅ pytdx前复权计算成功")

                    # 验证前复权价格是否与原始价格不同
                    original_sample = df['close'].iloc[0] if len(df) > 0 else 0
                    adjusted_sample = adjusted_prices.iloc[0] if len(adjusted_prices) > 0 else 0
                    if abs(original_sample - adjusted_sample) > 0.001:
                        self.smart_logger.info(f"✅ 前复权价格验证通过: 原始={original_sample:.3f}, 前复权={adjusted_sample:.3f}")
                    else:
                        self.smart_logger.warning(f"⚠️ 前复权价格与原始价格相同，可能无除权事件")
                else:
                    # 备用方案：使用原始价格但记录警告
                    result_df['前复权收盘价C'] = df['close'].round(3)
                    self.smart_logger.error("❌ pytdx前复权计算失败，使用原始价格（数据质量不合格）")
            except Exception as e:
                result_df['前复权收盘价C'] = df['close'].round(3)
                self.smart_logger.error(f"❌ pytdx前复权计算异常: {e}")
                self.smart_logger.warning("⚠️ 使用原始收盘价（数据质量不合格）")
            
            result_df['路径总长'] = [0.0] * len(df)
            result_df['主买'] = [0.0] * len(df)
            result_df['主卖'] = [0.0] * len(df)
            
            return result_df
            
        except Exception as e:
            self.smart_logger.error(f"数据格式转换失败: {e}")
            return pd.DataFrame()

    def _calculate_forward_adjustment_pytdx(self, df: pd.DataFrame, stock_code: str) -> Optional[pd.Series]:
        """
        pytdx数据的前复权计算

        Args:
            df: pytdx原始数据DataFrame
            stock_code: 股票代码

        Returns:
            前复权价格Series或None
        """
        try:
            self.smart_logger.info(f"🔄 pytdx前复权计算: {stock_code}")

            # 尝试使用现有的前复权处理器
            try:
                from main_v20230219_optimized import StockDataProcessor
                # 使用默认的tdx路径初始化
                processor = StockDataProcessor(tdx_path="H:/MPV1.17/T0002")

                # 准备数据格式（pytdx数据已经有datetime索引）
                data_for_adjustment = df.copy()
                if 'datetime' in df.columns:
                    data_for_adjustment.index = pd.to_datetime(df['datetime'])

                # 获取除权除息数据
                xdxr_data = processor.load_dividend_data(stock_code)

                if xdxr_data is not None and len(xdxr_data) > 0:
                    # 应用前复权处理
                    adjusted_data = processor.apply_forward_adjustment(data_for_adjustment, xdxr_data)

                    if adjusted_data is not None and 'close' in adjusted_data.columns:
                        self.smart_logger.info(f"✅ pytdx前复权计算成功，处理了{len(xdxr_data)}个除权事件")

                        # 验证数据长度匹配
                        if len(adjusted_data) == len(df):
                            return adjusted_data['close']
                        else:
                            self.smart_logger.warning(f"前复权数据长度不匹配: 原始{len(df)}, 调整后{len(adjusted_data)}")
                            return None
                    else:
                        self.smart_logger.warning("pytdx前复权处理返回空数据")
                        return None
                else:
                    self.smart_logger.info(f"股票{stock_code}无除权除息数据，前复权价格等于原始价格")
                    return df['close']  # 无除权事件时，前复权价格等于原始价格

            except ImportError:
                self.smart_logger.warning("无法导入前复权处理器，跳过pytdx前复权计算")
                return None
            except Exception as e:
                self.smart_logger.warning(f"pytdx前复权计算失败: {e}")
                return None

        except Exception as e:
            self.smart_logger.error(f"pytdx前复权计算异常: {e}")
            return None

    def get_xdxr_info(self, stock_code: str) -> Optional[pd.DataFrame]:
        """
        获取股票的除权除息信息（pytdx原生支持）

        Args:
            stock_code: 股票代码

        Returns:
            除权除息信息DataFrame或None
        """
        try:
            self.smart_logger.info(f"🔄 获取{stock_code}的除权除息信息...")

            # 获取市场代码
            market = self.get_market_code(stock_code)

            # 连接服务器
            api = self.connect_to_server()
            if not api:
                self.smart_logger.error("无法连接到pytdx服务器")
                return None

            try:
                # 使用pytdx原生API获取除权除息信息
                xdxr_data = api.get_xdxr_info(market, stock_code)
                api.disconnect()

                if not xdxr_data:
                    self.smart_logger.info(f"股票{stock_code}无除权除息数据")
                    return None

                # 转换为DataFrame
                df = pd.DataFrame(xdxr_data)

                self.smart_logger.info(f"✅ 成功获取{stock_code}的除权除息信息: {len(df)}条记录")

                # 显示数据样例
                if len(df) > 0:
                    self.smart_logger.info(f"📊 除权除息数据样例:")
                    for i in range(min(3, len(df))):
                        record = df.iloc[i]
                        self.smart_logger.info(f"  {i+1}. {record}")

                return df

            except Exception as e:
                api.disconnect()
                self.smart_logger.error(f"获取除权除息信息失败: {e}")
                return None

        except Exception as e:
            self.smart_logger.error(f"除权除息信息获取异常: {e}")
            return None
    
    def save_minute_data(self, stock_code: str, start_date: str, end_date: str, 
                        frequency: str = '1min') -> bool:
        """
        下载并保存分钟级数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率
            
        Returns:
            是否成功
        """
        try:
            # 下载数据
            df = self.download_minute_data(stock_code, start_date, end_date, frequency)
            
            if df is None or df.empty:
                return False
            
            # 转换格式
            formatted_df = self.convert_to_target_format(df, stock_code, "minute")
            
            if formatted_df.empty:
                return False
            
            # 确保输出目录存在
            os.makedirs(self.output_dir, exist_ok=True)
            
            # 获取实际数据的时间范围（而不是请求范围）
            actual_start_date = formatted_df['时间'].min()
            actual_end_date = formatted_df['时间'].max()

            # 转换为YYYYMMDD格式
            if len(str(actual_start_date)) == 12:  # YYYYMMDDHHMM格式
                actual_start_str = str(actual_start_date)[:8]
                actual_end_str = str(actual_end_date)[:8]
            else:  # 其他格式，尝试解析
                try:
                    actual_start_str = pd.to_datetime(str(actual_start_date)).strftime('%Y%m%d')
                    actual_end_str = pd.to_datetime(str(actual_end_date)).strftime('%Y%m%d')
                except:
                    # 如果解析失败，回退到请求范围
                    actual_start_str = start_date
                    actual_end_str = end_date

            # 生成文件名（使用实际数据的时间范围，添加时间戳）
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d%H%M')
            filename = f"{frequency}min_0_{stock_code}_{actual_start_str}-{actual_end_str}_来源互联网（{timestamp}）.txt"
            filepath = os.path.join(self.output_dir, filename)
            
            # 保存文件
            formatted_df.to_csv(filepath, sep='|', index=False, encoding='utf-8')
            
            file_size = os.path.getsize(filepath)
            self.smart_logger.info(f"✅ pytdx数据保存成功: {filename} ({file_size} 字节)")
            
            return True
            
        except Exception as e:
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.FILE_IO,
                context={'stock_code': stock_code, 'frequency': frequency},
                operation="pytdx数据保存"
            )
            self.smart_logger.error(f"pytdx数据保存失败 [错误ID: {error_id}]: {e}")
            return False
    
    def batch_download(self, stock_codes: List[str], start_date: str, end_date: str, 
                      frequency: str = '1min') -> Dict[str, bool]:
        """
        批量下载数据
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期
            end_date: 结束日期
            frequency: 数据频率
            
        Returns:
            下载结果字典
        """
        try:
            results = {}
            
            self.smart_logger.info(f"🚀 开始批量下载 {len(stock_codes)} 只股票的 {frequency} 数据")
            
            for i, stock_code in enumerate(stock_codes, 1):
                self.smart_logger.info(f"📊 [{i}/{len(stock_codes)}] 下载 {stock_code}")
                
                success = self.save_minute_data(stock_code, start_date, end_date, frequency)
                results[stock_code] = success
                
                if success:
                    self.smart_logger.info(f"✅ {stock_code} 下载成功")
                else:
                    self.smart_logger.warning(f"❌ {stock_code} 下载失败")
            
            # 统计结果
            success_count = sum(results.values())
            total_count = len(results)
            success_rate = success_count / total_count * 100 if total_count > 0 else 0
            
            self.smart_logger.info(f"📊 批量下载完成: {success_count}/{total_count} 成功 ({success_rate:.1f}%)")
            
            return results
            
        except Exception as e:
            self.smart_logger.error(f"批量下载失败: {e}")
            return {}
