2025-08-02 00:16:34,694 - Main - INFO - info:316 - ℹ️ 程序启动，日志文件: logs\mythquant_20250802_001634.log
2025-08-02 00:16:34,695 - Main - INFO - info:316 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-08-02 00:16:34,695 - Main - INFO - info:316 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-08-02 00:16:34,695 - Main - INFO - info:316 - ℹ️ 正在加载任务配置...
2025-08-02 00:16:34,695 - Main - INFO - info:316 - ℹ️ 加载了 7 个任务配置
2025-08-02 00:16:34,695 - Main - INFO - info:316 - ℹ️ 核心组件初始化完成
2025-08-02 00:16:34,695 - Main - INFO - info:316 - ℹ️ 应用程序初始化完成
2025-08-02 00:16:34,696 - Main - INFO - info:316 - ℹ️ 开始执行所有任务
2025-08-02 00:16:34,697 - Main - INFO - info:316 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-08-02 00:16:35,102 - Main - INFO - info:316 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-08-02 00:16:35,102 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-02 00:16:35,102 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-02 00:16:35,102 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-08-02 00:16:35,299 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 33600条 (目标日期: 20250101, 交易日: 140天, 频率: 1min)
2025-08-02 00:16:35,299 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计33600条 -> 实际请求800条 (配置限制:800)
2025-08-02 00:16:35,299 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-02 00:16:35,299 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-02 00:16:35,350 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32800条
2025-08-02 00:16:35,568 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-02 00:16:35,787 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-02 00:16:36,020 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-02 00:16:36,238 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-02 00:16:36,460 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-02 00:16:36,686 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-08-02 00:16:36,909 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-08-02 00:16:37,136 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-08-02 00:16:37,359 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-08-02 00:16:37,574 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-08-02 00:16:37,802 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-08-02 00:16:38,026 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-08-02 00:16:38,258 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-08-02 00:16:38,485 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-08-02 00:16:38,709 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-08-02 00:16:38,928 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-08-02 00:16:39,152 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-08-02 00:16:39,371 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-08-02 00:16:39,613 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-08-02 00:16:39,852 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-08-02 00:16:40,082 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-08-02 00:16:40,308 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-08-02 00:16:40,535 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-08-02 00:16:40,762 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-08-02 00:16:41,000 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-08-02 00:16:41,231 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-08-02 00:16:41,451 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-08-02 00:16:41,679 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +160条，总计22560条
2025-08-02 00:16:41,679 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计22560条数据
2025-08-02 00:16:41,685 - Main - WARNING - warning:320 - ⚠️ ⚠️ 数据覆盖不足: 需要33600条，实际获取22560条
2025-08-02 00:16:41,685 - Main - WARNING - warning:320 - ⚠️ ⚠️ 缺少约11040条数据（约46个交易日）
2025-08-02 00:16:41,685 - Main - WARNING - warning:320 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-08-
2025-08-02 00:16:41,685 - Main - WARNING - warning:320 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-08-02 00:16:41,685 - Main - WARNING - warning:320 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-08-02 00:16:41,738 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-08-02 00:16:41,872 - Main - INFO - info:316 - ℹ️ ✅ 数据保存完成: 1min_0_000617_20250318-20250725_来源互联网（202508020016）.txt (999617 字节)
2025-08-02 00:16:41,872 - Main - INFO - info:316 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-08-02 00:16:41,873 - Main - INFO - info:316 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-08-02 00:16:41,873 - Main - INFO - info:316 - ℹ️ 开始执行前复权数据比较分析任务
2025-08-02 00:16:41,874 - Main - INFO - info:316 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-08-02 00:16:41,874 - Main - INFO - info:316 - ℹ️ 正在分析股票: 000617
2025-08-02 00:16:41,874 - Main - INFO - info:316 - ℹ️ 开始比较股票 000617 的前复权数据
2025-08-02 00:16:41,874 - Main - INFO - info:316 - ℹ️ 找到文件 - TDX: 未找到
2025-08-02 00:16:41,874 - Main - INFO - info:316 - ℹ️ 找到文件 - 互联网: 未找到
2025-08-02 00:16:41,874 - Main - WARNING - warning:320 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-08-02 00:16:41,875 - Main - INFO - info:316 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250802_001641.txt
2025-08-02 00:16:41,875 - Main - INFO - info:316 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-08-02 00:16:41,875 - Main - ERROR - error:324 - ❌ 任务执行失败: 前复权数据比较分析
2025-08-02 00:16:41,875 - Main - INFO - info:316 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-08-02 00:16:41,876 - Main - INFO - info:316 - ℹ️ 开始清理应用程序资源
2025-08-02 00:16:41,876 - Main - INFO - info:316 - ℹ️ 开始清理股票处理器资源
2025-08-02 00:16:41,876 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-08-02 00:16:41,876 - Main - INFO - info:316 - ℹ️ 异步IO处理器资源清理完成
2025-08-02 00:16:41,876 - Main - INFO - info:316 - ℹ️ 股票处理器资源清理完成
2025-08-02 00:16:41,876 - Main - INFO - info:316 - ℹ️ 开始清理任务管理器资源
2025-08-02 00:16:41,876 - Main - INFO - info:316 - ℹ️ 任务管理器资源清理完成
2025-08-02 00:16:41,876 - Main - INFO - info:316 - ℹ️ 应用程序资源清理完成
