#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票聚合根

管理股票的基本信息、价格历史和相关业务逻辑
"""

from typing import List, Optional, Dict, Any
from datetime import datetime, date
from dataclasses import dataclass, field
from decimal import Decimal

from ..value_objects.identifiers import StockCode, MarketCode
from ..value_objects.measurements import Price, Volume, Percentage
from ..events.stock_events import (
    StockCreated, StockPriceChanged, StockSuspended, 
    StockResumed, StockDelisted, DividendAnnounced
)
from ..exceptions import (
    InvalidPriceError, BusinessRuleViolationError,
    InvariantViolationError
)
from ...shared.patterns.architectural_patterns import AggregateRoot


@dataclass
class StockInfo:
    """股票基本信息"""
    name: str
    industry: str
    sector: str
    listing_date: date
    total_shares: int
    float_shares: int
    market_cap: Optional[Decimal] = None
    
    def __post_init__(self):
        if self.total_shares <= 0:
            raise ValueError("总股本必须大于0")
        if self.float_shares <= 0:
            raise ValueError("流通股本必须大于0")
        if self.float_shares > self.total_shares:
            raise ValueError("流通股本不能大于总股本")


@dataclass
class PriceData:
    """价格数据"""
    date: date
    open_price: Price
    high_price: Price
    low_price: Price
    close_price: Price
    volume: Volume
    turnover: Optional[Decimal] = None
    
    def __post_init__(self):
        # 验证价格关系
        if not (self.low_price <= self.open_price <= self.high_price):
            raise InvalidPriceError(
                self.open_price.value, 
                f"开盘价{self.open_price}不在最高价{self.high_price}和最低价{self.low_price}之间"
            )
        if not (self.low_price <= self.close_price <= self.high_price):
            raise InvalidPriceError(
                self.close_price.value,
                f"收盘价{self.close_price}不在最高价{self.high_price}和最低价{self.low_price}之间"
            )
    
    @property
    def price_change(self) -> Price:
        """价格变化"""
        return Price(self.close_price.value - self.open_price.value, self.close_price.currency)
    
    @property
    def price_change_percentage(self) -> Percentage:
        """价格变化百分比"""
        if self.open_price.value == 0:
            return Percentage.zero()
        change = (self.close_price.value - self.open_price.value) / self.open_price.value * 100
        return Percentage(change)
    
    @property
    def amplitude(self) -> Percentage:
        """振幅"""
        if self.open_price.value == 0:
            return Percentage.zero()
        amp = (self.high_price.value - self.low_price.value) / self.open_price.value * 100
        return Percentage(amp)


@dataclass
class DividendInfo:
    """分红信息"""
    announcement_date: date
    ex_dividend_date: date
    record_date: date
    payment_date: date
    dividend_per_share: Decimal
    dividend_type: str  # CASH, STOCK, SPECIAL
    
    def __post_init__(self):
        if self.dividend_per_share < 0:
            raise ValueError("每股分红不能为负数")
        
        # 验证日期顺序
        if not (self.announcement_date <= self.ex_dividend_date <= 
                self.record_date <= self.payment_date):
            raise ValueError("分红日期顺序不正确")


class Stock(AggregateRoot):
    """股票聚合根"""
    
    def __init__(self, stock_code: StockCode, market_code: MarketCode, 
                 stock_info: StockInfo):
        super().__init__(str(stock_code))
        self._stock_code = stock_code
        self._market_code = market_code
        self._stock_info = stock_info
        self._price_history: List[PriceData] = []
        self._dividend_history: List[DividendInfo] = []
        self._is_suspended = False
        self._is_delisted = False
        self._created_at = datetime.now()
        self._updated_at = datetime.now()
        
        # 发布股票创建事件
        self.add_domain_event(StockCreated(
            stock_id=self.id,
            stock_code=str(self._stock_code),
            market_code=str(self._market_code),
            stock_name=self._stock_info.name,
            created_at=self._created_at
        ))
    
    @property
    def stock_code(self) -> StockCode:
        """股票代码"""
        return self._stock_code
    
    @property
    def market_code(self) -> MarketCode:
        """市场代码"""
        return self._market_code
    
    @property
    def stock_info(self) -> StockInfo:
        """股票信息"""
        return self._stock_info
    
    @property
    def is_suspended(self) -> bool:
        """是否停牌"""
        return self._is_suspended
    
    @property
    def is_delisted(self) -> bool:
        """是否退市"""
        return self._is_delisted
    
    @property
    def is_tradable(self) -> bool:
        """是否可交易"""
        return not self._is_suspended and not self._is_delisted
    
    @property
    def latest_price(self) -> Optional[Price]:
        """最新价格"""
        if not self._price_history:
            return None
        return self._price_history[-1].close_price
    
    @property
    def price_history(self) -> List[PriceData]:
        """价格历史（只读）"""
        return self._price_history.copy()
    
    @property
    def dividend_history(self) -> List[DividendInfo]:
        """分红历史（只读）"""
        return self._dividend_history.copy()
    
    def update_stock_info(self, stock_info: StockInfo):
        """更新股票信息"""
        self._ensure_not_delisted()
        
        old_info = self._stock_info
        self._stock_info = stock_info
        self._updated_at = datetime.now()
        
        # 可以添加股票信息更新事件
        # self.add_domain_event(StockInfoUpdated(...))
    
    def add_price_data(self, price_data: PriceData):
        """添加价格数据"""
        self._ensure_not_delisted()
        
        # 验证日期顺序
        if self._price_history and price_data.date <= self._price_history[-1].date:
            raise BusinessRuleViolationError(
                "价格数据日期顺序",
                f"新价格数据日期{price_data.date}必须晚于最后一条数据日期{self._price_history[-1].date}"
            )
        
        old_price = self.latest_price
        self._price_history.append(price_data)
        self._updated_at = datetime.now()
        
        # 发布价格变化事件
        self.add_domain_event(StockPriceChanged(
            stock_id=self.id,
            stock_code=str(self._stock_code),
            old_price=old_price.value if old_price else None,
            new_price=price_data.close_price.value,
            price_date=price_data.date,
            volume=price_data.volume.value,
            changed_at=datetime.now()
        ))
    
    def suspend_trading(self, reason: str):
        """停牌"""
        self._ensure_not_delisted()
        
        if self._is_suspended:
            raise BusinessRuleViolationError(
                "股票停牌",
                f"股票{self._stock_code}已经停牌"
            )
        
        self._is_suspended = True
        self._updated_at = datetime.now()
        
        # 发布停牌事件
        self.add_domain_event(StockSuspended(
            stock_id=self.id,
            stock_code=str(self._stock_code),
            reason=reason,
            suspended_at=datetime.now()
        ))
    
    def resume_trading(self):
        """复牌"""
        self._ensure_not_delisted()
        
        if not self._is_suspended:
            raise BusinessRuleViolationError(
                "股票复牌",
                f"股票{self._stock_code}未停牌，无需复牌"
            )
        
        self._is_suspended = False
        self._updated_at = datetime.now()
        
        # 发布复牌事件
        self.add_domain_event(StockResumed(
            stock_id=self.id,
            stock_code=str(self._stock_code),
            resumed_at=datetime.now()
        ))
    
    def delist(self, reason: str):
        """退市"""
        if self._is_delisted:
            raise BusinessRuleViolationError(
                "股票退市",
                f"股票{self._stock_code}已经退市"
            )
        
        self._is_delisted = True
        self._is_suspended = False  # 退市后不再是停牌状态
        self._updated_at = datetime.now()
        
        # 发布退市事件
        self.add_domain_event(StockDelisted(
            stock_id=self.id,
            stock_code=str(self._stock_code),
            reason=reason,
            delisted_at=datetime.now()
        ))
    
    def announce_dividend(self, dividend_info: DividendInfo):
        """公告分红"""
        self._ensure_not_delisted()
        
        # 验证分红日期不能早于上市日期
        if dividend_info.announcement_date < self._stock_info.listing_date:
            raise BusinessRuleViolationError(
                "分红公告",
                f"分红公告日期{dividend_info.announcement_date}不能早于上市日期{self._stock_info.listing_date}"
            )
        
        self._dividend_history.append(dividend_info)
        self._updated_at = datetime.now()
        
        # 发布分红公告事件
        self.add_domain_event(DividendAnnounced(
            stock_id=self.id,
            stock_code=str(self._stock_code),
            dividend_per_share=dividend_info.dividend_per_share,
            ex_dividend_date=dividend_info.ex_dividend_date,
            announced_at=datetime.now()
        ))
    
    def calculate_return(self, start_date: date, end_date: date) -> Optional[Percentage]:
        """计算指定期间的收益率"""
        start_price = self._get_price_on_date(start_date)
        end_price = self._get_price_on_date(end_date)
        
        if not start_price or not end_price:
            return None
        
        if start_price.value == 0:
            return None
        
        return_rate = (end_price.value - start_price.value) / start_price.value * 100
        return Percentage(return_rate)
    
    def calculate_volatility(self, days: int = 30) -> Optional[Percentage]:
        """计算波动率"""
        if len(self._price_history) < days:
            return None
        
        recent_prices = self._price_history[-days:]
        returns = []
        
        for i in range(1, len(recent_prices)):
            prev_price = recent_prices[i-1].close_price.value
            curr_price = recent_prices[i].close_price.value
            if prev_price > 0:
                daily_return = (curr_price - prev_price) / prev_price
                returns.append(daily_return)
        
        if not returns:
            return None
        
        # 计算标准差
        mean_return = sum(returns) / len(returns)
        variance = sum((r - mean_return) ** 2 for r in returns) / len(returns)
        volatility = (variance ** 0.5) * 100  # 转换为百分比
        
        return Percentage(Decimal(str(volatility)))
    
    def get_price_range(self, start_date: date, end_date: date) -> List[PriceData]:
        """获取指定日期范围的价格数据"""
        return [
            price_data for price_data in self._price_history
            if start_date <= price_data.date <= end_date
        ]
    
    def _get_price_on_date(self, target_date: date) -> Optional[Price]:
        """获取指定日期的收盘价"""
        for price_data in self._price_history:
            if price_data.date == target_date:
                return price_data.close_price
        return None
    
    def _ensure_not_delisted(self):
        """确保股票未退市"""
        if self._is_delisted:
            raise InvariantViolationError(
                "股票状态",
                f"股票{self._stock_code}已退市，不能执行此操作"
            )
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'stock_code': str(self._stock_code),
            'market_code': str(self._market_code),
            'stock_info': {
                'name': self._stock_info.name,
                'industry': self._stock_info.industry,
                'sector': self._stock_info.sector,
                'listing_date': self._stock_info.listing_date.isoformat(),
                'total_shares': self._stock_info.total_shares,
                'float_shares': self._stock_info.float_shares,
                'market_cap': str(self._stock_info.market_cap) if self._stock_info.market_cap else None
            },
            'is_suspended': self._is_suspended,
            'is_delisted': self._is_delisted,
            'latest_price': str(self.latest_price.value) if self.latest_price else None,
            'price_history_count': len(self._price_history),
            'dividend_history_count': len(self._dividend_history),
            'created_at': self._created_at.isoformat(),
            'updated_at': self._updated_at.isoformat()
        }


# 类型别名
StockAggregate = Stock


# 工厂函数
def create_stock(stock_code: str, market_code: str, name: str, 
                industry: str, sector: str, listing_date: date,
                total_shares: int, float_shares: int) -> Stock:
    """创建股票聚合根的便捷函数"""
    stock_code_vo = StockCode.from_string(stock_code)
    market_code_vo = MarketCode.from_string(market_code)
    stock_info = StockInfo(
        name=name,
        industry=industry,
        sector=sector,
        listing_date=listing_date,
        total_shares=total_shares,
        float_shares=float_shares
    )
    
    return Stock(stock_code_vo, market_code_vo, stock_info)


def create_price_data(date_str: str, open_price: float, high_price: float,
                     low_price: float, close_price: float, volume: int,
                     currency: str = "CNY") -> PriceData:
    """创建价格数据的便捷函数"""
    from datetime import datetime
    
    price_date = datetime.strptime(date_str, "%Y-%m-%d").date()
    
    return PriceData(
        date=price_date,
        open_price=Price.from_float(open_price, currency),
        high_price=Price.from_float(high_price, currency),
        low_price=Price.from_float(low_price, currency),
        close_price=Price.from_float(close_price, currency),
        volume=Volume(volume)
    )
