#!/usr/bin/env python3
"""
活跃代码质量检查工具

专门检查当前活跃的代码文件（排除archive、backup等历史文件），
重点关注可能影响当前运行的代码质量问题。
"""

import os
import re
import sys
from typing import Dict, List, Any
from collections import defaultdict

class ActiveCodeQualityChecker:
    """活跃代码质量检查器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.excluded_dirs = {
            'archive', 'backup', 'backups', '.git', '__pycache__', 
            '.venv', 'node_modules', 'legacy_code', 'old_code'
        }
    
    def is_active_file(self, file_path: str) -> bool:
        """判断是否为活跃的代码文件"""
        # 排除archive、backup等目录
        path_parts = file_path.split(os.sep)
        for part in path_parts:
            if any(excluded in part.lower() for excluded in self.excluded_dirs):
                return False
        
        # 只检查Python文件
        return file_path.endswith('.py')
    
    def check_critical_files(self) -> Dict[str, Any]:
        """检查关键的活跃文件"""
        critical_files = [
            'main.py',
            'src/mythquant/core/task_manager.py',
            'utils/missing_data_processor.py',
            'utils/pytdx_data_repairer.py'
        ]
        
        results = {}
        
        for file_rel_path in critical_files:
            file_path = os.path.join(self.project_root, file_rel_path)
            if os.path.exists(file_path):
                print(f"🔍 检查关键文件: {file_rel_path}")
                
                # 检查重复函数定义
                duplicate_issues = self.check_duplicate_functions(file_path)
                
                # 检查导入问题
                import_issues = self.check_import_conflicts(file_path)
                
                results[file_rel_path] = {
                    'duplicate_functions': duplicate_issues,
                    'import_conflicts': import_issues,
                    'total_issues': len(duplicate_issues) + len(import_issues)
                }
                
                if results[file_rel_path]['total_issues'] > 0:
                    print(f"   ❌ 发现 {results[file_rel_path]['total_issues']} 个问题")
                else:
                    print(f"   ✅ 无问题")
            else:
                print(f"   ⚠️ 文件不存在: {file_rel_path}")
        
        return results
    
    def check_duplicate_functions(self, file_path: str) -> List[Dict[str, Any]]:
        """检查重复函数定义"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找所有函数定义
            function_pattern = r'^\s*def\s+(\w+)\s*\('
            matches = re.finditer(function_pattern, content, re.MULTILINE)
            
            function_counts = defaultdict(list)
            for match in matches:
                func_name = match.group(1)
                line_num = content[:match.start()].count('\n') + 1
                function_counts[func_name].append(line_num)
            
            # 找出重复定义
            for func_name, line_numbers in function_counts.items():
                if len(line_numbers) > 1:
                    issues.append({
                        'type': 'duplicate_function',
                        'function_name': func_name,
                        'line_numbers': line_numbers,
                        'severity': 'high' if func_name != '__init__' else 'medium'
                    })
        
        except Exception as e:
            issues.append({
                'type': 'analysis_error',
                'error': str(e),
                'severity': 'medium'
            })
        
        return issues
    
    def check_import_conflicts(self, file_path: str) -> List[Dict[str, Any]]:
        """检查导入冲突"""
        issues = []
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 查找导入语句
            import_pattern = r'^\s*from\s+([^\s]+)\s+import\s+([^\n]+)'
            imports = re.findall(import_pattern, content, re.MULTILINE)
            
            imported_functions = defaultdict(list)
            for module, functions in imports:
                for func in functions.split(','):
                    func = func.strip()
                    if func and not func.startswith('*'):
                        imported_functions[func].append(module)
            
            # 检查冲突导入
            for func_name, modules in imported_functions.items():
                if len(set(modules)) > 1:
                    issues.append({
                        'type': 'import_conflict',
                        'function_name': func_name,
                        'modules': list(set(modules)),
                        'severity': 'medium'
                    })
        
        except Exception as e:
            issues.append({
                'type': 'import_analysis_error',
                'error': str(e),
                'severity': 'low'
            })
        
        return issues
    
    def generate_focused_report(self, results: Dict[str, Any]) -> str:
        """生成专注的报告"""
        report = []
        report.append("🔍 活跃代码质量检查报告")
        report.append("=" * 50)
        
        total_issues = sum(r['total_issues'] for r in results.values())
        report.append(f"\n📊 关键文件扫描结果:")
        report.append(f"   检查文件数: {len(results)}")
        report.append(f"   发现问题数: {total_issues}")
        
        if total_issues > 0:
            report.append(f"\n🚨 发现的问题:")
            
            for file_path, file_results in results.items():
                if file_results['total_issues'] > 0:
                    report.append(f"\n   📄 {file_path}:")
                    
                    # 重复函数问题
                    for issue in file_results['duplicate_functions']:
                        if issue['severity'] == 'high':
                            func_name = issue['function_name']
                            lines = issue['line_numbers']
                            report.append(f"      ❌ 重复函数定义: {func_name} (行号: {lines})")
                    
                    # 导入冲突问题
                    for issue in file_results['import_conflicts']:
                        func_name = issue['function_name']
                        modules = issue['modules']
                        report.append(f"      ⚠️ 导入冲突: {func_name} 从 {modules} 导入")
        else:
            report.append(f"\n✅ 所有关键文件无质量问题")
        
        return '\n'.join(report)

def main():
    """主函数"""
    print("🚀 活跃代码质量检查")
    print("=" * 60)
    
    # 获取项目根目录
    current_dir = os.path.dirname(os.path.abspath(__file__))
    project_root = os.path.join(current_dir, '..')
    
    # 创建检查器
    checker = ActiveCodeQualityChecker(project_root)
    
    # 检查关键文件
    results = checker.check_critical_files()
    
    # 生成报告
    report = checker.generate_focused_report(results)
    print(report)
    
    # 分析问题影响
    total_issues = sum(r['total_issues'] for r in results.values())
    
    if total_issues > 0:
        print(f"\n💡 问题影响分析:")
        print("   1. 重复函数定义可能导致Python名称解析错误")
        print("   2. 变量作用域问题可能在运行时出现")
        print("   3. 导入冲突可能导致调用错误的函数版本")
        print("   4. 这些问题通常在集成测试中才会暴露")
        
        print(f"\n🔧 建议修复步骤:")
        print("   1. 删除重复的函数定义")
        print("   2. 统一导入路径")
        print("   3. 运行集成测试验证修复")
        print("   4. 建立定期的代码质量检查")
    
    return 1 if total_issues > 0 else 0

if __name__ == '__main__':
    sys.exit(main())
