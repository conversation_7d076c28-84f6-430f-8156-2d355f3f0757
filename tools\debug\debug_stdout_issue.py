#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试stdout问题的最小测试
"""

import sys
import os

print("🔍 调试stdout问题")
print(f"Python版本: {sys.version}")
print(f"平台: {sys.platform}")
print(f"stdout类型: {type(sys.stdout)}")
print(f"stdout编码: {getattr(sys.stdout, 'encoding', 'unknown')}")
print(f"stdout是否关闭: {sys.stdout.closed if hasattr(sys.stdout, 'closed') else 'unknown'}")

# 测试基本print
try:
    print("✅ 基本print测试成功")
except Exception as e:
    print(f"❌ 基本print测试失败: {e}")

# 测试导入structured_output_formatter
try:
    print("🔄 导入structured_output_formatter...")
    from utils.structured_output_formatter import StructuredOutputFormatter
    print("✅ 导入成功")
    
    # 测试创建实例
    formatter = StructuredOutputFormatter()
    print("✅ 创建实例成功")
    
    # 测试print_banner
    print("🔄 测试print_banner...")
    formatter.print_banner("测试标题", "测试副标题")
    print("✅ print_banner测试成功")
    
except Exception as e:
    print(f"❌ structured_output_formatter测试失败: {e}")
    import traceback
    traceback.print_exc()

print("🏁 调试完成")
