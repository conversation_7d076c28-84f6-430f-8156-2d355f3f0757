#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
部署准备脚本

准备MythQuant系统的部署，包括环境检查、文件整理、配置验证等
"""

import os
import sys
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any


class DeploymentPreparator:
    """部署准备器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.deployment_dir = self.project_root / "deployment"
        self.backup_dir = self.project_root / "backup"
        
    def prepare_deployment(self) -> bool:
        """准备部署"""
        print("🚀 MythQuant 部署准备")
        print("=" * 50)
        
        try:
            # 1. 环境检查
            if not self.check_environment():
                return False
            
            # 2. 创建部署目录
            self.create_deployment_structure()
            
            # 3. 复制核心文件
            self.copy_core_files()
            
            # 4. 生成配置文件
            self.generate_deployment_configs()
            
            # 5. 创建启动脚本
            self.create_startup_scripts()
            
            # 6. 生成部署文档
            self.generate_deployment_docs()
            
            # 7. 创建备份
            self.create_backup()
            
            print("\n✅ 部署准备完成！")
            print(f"📁 部署文件位置: {self.deployment_dir}")
            print(f"💾 备份文件位置: {self.backup_dir}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 部署准备失败: {e}")
            return False
    
    def check_environment(self) -> bool:
        """检查环境"""
        print("🔍 检查部署环境...")
        
        # 检查Python版本
        python_version = sys.version_info
        if python_version.major < 3 or (python_version.major == 3 and python_version.minor < 7):
            print("   ❌ Python版本过低，需要3.7+")
            return False
        
        print(f"   ✅ Python版本: {python_version.major}.{python_version.minor}.{python_version.micro}")
        
        # 检查必要包
        required_packages = ['pandas', 'numpy']
        missing_packages = []
        
        for package in required_packages:
            try:
                __import__(package)
                print(f"   ✅ {package}: 已安装")
            except ImportError:
                missing_packages.append(package)
                print(f"   ❌ {package}: 未安装")
        
        if missing_packages:
            print(f"   ⚠️ 请安装缺少的包: pip install {' '.join(missing_packages)}")
            return False
        
        # 检查核心文件
        core_files = [
            'config_compatibility.py',
            'data_access_compatibility.py',
            'algorithm_compatibility.py',
            'io_compatibility.py'
        ]
        
        missing_files = []
        for file_name in core_files:
            if (self.project_root / file_name).exists():
                print(f"   ✅ {file_name}: 存在")
            else:
                missing_files.append(file_name)
                print(f"   ❌ {file_name}: 缺失")
        
        if missing_files:
            print("   ❌ 缺少核心文件，无法部署")
            return False
        
        return True
    
    def create_deployment_structure(self):
        """创建部署目录结构"""
        print("\n📁 创建部署目录结构...")
        
        # 创建主部署目录
        self.deployment_dir.mkdir(exist_ok=True)
        
        # 创建子目录
        subdirs = [
            'core',           # 核心文件
            'config',         # 配置文件
            'docs',           # 文档
            'scripts',        # 脚本
            'tests',          # 测试
            'examples',       # 示例
            'logs',           # 日志目录
            'output',         # 输出目录
            'backup'          # 备份目录
        ]
        
        for subdir in subdirs:
            (self.deployment_dir / subdir).mkdir(exist_ok=True)
            print(f"   ✅ 创建目录: {subdir}")
    
    def copy_core_files(self):
        """复制核心文件"""
        print("\n📋 复制核心文件...")
        
        # 核心兼容性文件
        core_files = [
            'config_compatibility.py',
            'data_access_compatibility.py',
            'algorithm_compatibility.py',
            'io_compatibility.py'
        ]
        
        for file_name in core_files:
            src = self.project_root / file_name
            dst = self.deployment_dir / 'core' / file_name
            if src.exists():
                shutil.copy2(src, dst)
                print(f"   ✅ 复制: {file_name}")
        
        # 复制src目录（如果存在）
        src_dir = self.project_root / 'src'
        if src_dir.exists():
            dst_src = self.deployment_dir / 'src'
            shutil.copytree(src_dir, dst_src, dirs_exist_ok=True)
            print("   ✅ 复制: src/ 目录")
        
        # 复制测试文件
        test_files = [
            'run_tests.py',
            'final_validation_test.py'
        ]
        
        for file_name in test_files:
            src = self.project_root / file_name
            dst = self.deployment_dir / 'tests' / file_name
            if src.exists():
                shutil.copy2(src, dst)
                print(f"   ✅ 复制: {file_name}")
        
        # 复制tests目录
        tests_dir = self.project_root / 'tests'
        if tests_dir.exists():
            for test_file in tests_dir.glob('*.py'):
                dst = self.deployment_dir / 'tests' / test_file.name
                shutil.copy2(test_file, dst)
                print(f"   ✅ 复制: tests/{test_file.name}")
        
        # 复制文档
        docs_dir = self.project_root / 'docs'
        if docs_dir.exists():
            dst_docs = self.deployment_dir / 'docs'
            shutil.copytree(docs_dir, dst_docs, dirs_exist_ok=True)
            print("   ✅ 复制: docs/ 目录")
    
    def generate_deployment_configs(self):
        """生成部署配置文件"""
        print("\n⚙️ 生成部署配置...")
        
        # 生成requirements.txt
        requirements = [
            "pandas>=1.0.0",
            "numpy>=1.18.0",
            "openpyxl>=3.0.0  # Excel支持（可选）"
        ]
        
        req_file = self.deployment_dir / 'requirements.txt'
        with open(req_file, 'w', encoding='utf-8') as f:
            f.write('\n'.join(requirements))
        print("   ✅ 生成: requirements.txt")
        
        # 生成部署配置
        deployment_config = {
            "version": "1.0.0",
            "deployment_date": datetime.now().isoformat(),
            "python_version_required": "3.7+",
            "core_modules": [
                "config_compatibility",
                "data_access_compatibility", 
                "algorithm_compatibility",
                "io_compatibility"
            ],
            "optional_modules": [
                "mythquant.config",
                "mythquant.algorithms",
                "mythquant.io"
            ],
            "directories": {
                "core": "核心兼容性模块",
                "config": "配置文件",
                "docs": "文档",
                "scripts": "启动脚本",
                "tests": "测试文件",
                "examples": "使用示例",
                "logs": "日志输出",
                "output": "数据输出",
                "backup": "备份文件"
            }
        }
        
        config_file = self.deployment_dir / 'config' / 'deployment.json'
        with open(config_file, 'w', encoding='utf-8') as f:
            json.dump(deployment_config, f, indent=2, ensure_ascii=False)
        print("   ✅ 生成: deployment.json")
        
        # 生成示例配置
        sample_config = {
            "# MythQuant 配置文件": "请根据实际环境修改以下配置",
            "tdx_path": "C:\\通达信",
            "output_path": "./output",
            "debug": True,
            "file_encoding": "utf-8",
            "data_sources": {
                "tdx": {"enabled": True, "priority": 1},
                "pytdx": {"enabled": True, "priority": 2},
                "internet": {"enabled": True, "priority": 3}
            }
        }
        
        sample_file = self.deployment_dir / 'config' / 'user_config_sample.py'
        with open(sample_file, 'w', encoding='utf-8') as f:
            f.write("# -*- coding: utf-8 -*-\n")
            f.write('"""\nMythQuant 用户配置文件示例\n\n请复制此文件为 user_config.py 并根据实际环境修改配置\n"""\n\n')
            f.write(f"# TDX安装路径\ntdx_path = r'{sample_config['tdx_path']}'\n\n")
            f.write(f"# 输出路径\noutput_path = r'{sample_config['output_path']}'\n\n")
            f.write(f"# 调试模式\nDEBUG = {sample_config['debug']}\n\n")
            f.write(f"# 文件编码\nfile_encoding = '{sample_config['file_encoding']}'\n")
        print("   ✅ 生成: user_config_sample.py")
    
    def create_startup_scripts(self):
        """创建启动脚本"""
        print("\n📜 创建启动脚本...")
        
        # Windows批处理脚本
        bat_script = """@echo off
echo MythQuant 启动脚本
echo ==================

REM 检查Python
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请确保Python已安装并添加到PATH
    pause
    exit /b 1
)

REM 检查配置文件
if not exist "config\\user_config.py" (
    echo 警告: 未找到配置文件，请复制 user_config_sample.py 为 user_config.py 并修改配置
    pause
)

REM 运行验证测试
echo 运行系统验证...
python tests\\final_validation_test.py

echo.
echo 启动完成！
pause
"""
        
        bat_file = self.deployment_dir / 'scripts' / 'start.bat'
        with open(bat_file, 'w', encoding='gbk') as f:
            f.write(bat_script)
        print("   ✅ 生成: start.bat (Windows)")
        
        # Linux/Mac shell脚本
        sh_script = """#!/bin/bash
echo "MythQuant 启动脚本"
echo "=================="

# 检查Python
if ! command -v python3 &> /dev/null; then
    echo "错误: 未找到Python3，请确保Python3已安装"
    exit 1
fi

# 检查配置文件
if [ ! -f "config/user_config.py" ]; then
    echo "警告: 未找到配置文件，请复制 user_config_sample.py 为 user_config.py 并修改配置"
fi

# 运行验证测试
echo "运行系统验证..."
python3 tests/final_validation_test.py

echo ""
echo "启动完成！"
"""
        
        sh_file = self.deployment_dir / 'scripts' / 'start.sh'
        with open(sh_file, 'w', encoding='utf-8') as f:
            f.write(sh_script)
        
        # 设置执行权限
        try:
            os.chmod(sh_file, 0o755)
        except:
            pass
        
        print("   ✅ 生成: start.sh (Linux/Mac)")
        
        # Python启动脚本
        py_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant Python启动脚本
"""

import sys
import os
from pathlib import Path

def main():
    print("🚀 MythQuant 启动")
    print("=" * 30)
    
    # 添加路径
    project_root = Path(__file__).parent.parent
    sys.path.insert(0, str(project_root / "core"))
    
    try:
        # 导入核心模块
        import config_compatibility as config
        import data_access_compatibility as data_access
        import algorithm_compatibility as algo
        import io_compatibility as io_compat
        
        print("✅ 核心模块加载成功")
        
        # 检查配置
        print(f"TDX路径: {config.get_tdx_path()}")
        print(f"输出路径: {config.get_output_path()}")
        print(f"调试模式: {config.is_debug_enabled()}")
        
        # 测试数据源
        connectivity = data_access.test_data_sources_connectivity()
        print(f"数据源状态: {len(connectivity)} 个数据源可用")
        
        print("\\n🎉 MythQuant 启动成功！")
        print("\\n📖 使用指南:")
        print("  - 查看 docs/ 目录获取详细文档")
        print("  - 运行 python tests/final_validation_test.py 进行系统验证")
        print("  - 查看 examples/ 目录获取使用示例")
        
    except Exception as e:
        print(f"❌ 启动失败: {e}")
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
'''
        
        py_file = self.deployment_dir / 'scripts' / 'start.py'
        with open(py_file, 'w', encoding='utf-8') as f:
            f.write(py_script)
        print("   ✅ 生成: start.py (Python)")
    
    def generate_deployment_docs(self):
        """生成部署文档"""
        print("\n📖 生成部署文档...")
        
        readme_content = """# MythQuant 部署包

## 快速开始

### 1. 环境要求
- Python 3.7+
- pandas >= 1.0.0
- numpy >= 1.18.0

### 2. 安装依赖
```bash
pip install -r requirements.txt
```

### 3. 配置系统
1. 复制 `config/user_config_sample.py` 为 `config/user_config.py`
2. 根据实际环境修改配置文件中的路径设置

### 4. 启动系统

#### Windows
```cmd
scripts\\start.bat
```

#### Linux/Mac
```bash
chmod +x scripts/start.sh
./scripts/start.sh
```

#### Python
```bash
python scripts/start.py
```

### 5. 验证系统
```bash
python tests/final_validation_test.py
```

## 目录结构

```
deployment/
├── core/           # 核心兼容性模块
├── src/            # 新架构源码（可选）
├── config/         # 配置文件
├── docs/           # 文档
├── scripts/        # 启动脚本
├── tests/          # 测试文件
├── examples/       # 使用示例
├── logs/           # 日志目录
├── output/         # 输出目录
└── backup/         # 备份目录
```

## 基本使用

```python
# 导入核心模块
import sys
sys.path.append('core')

import config_compatibility as config
import data_access_compatibility as data_access
import algorithm_compatibility as algo
import io_compatibility as io_compat

# 获取股票数据
stock_data = data_access.read_stock_day_data("000001")

# 计算L2指标
l2_data = algo.calculate_l2_metrics(stock_data)

# 输出结果
output_path = io_compat.write_stock_data_file(l2_data, "000001", "day")
```

## 故障排除

### 常见问题

1. **导入错误**: 确保将 `core` 目录添加到 Python 路径
2. **配置错误**: 检查 `config/user_config.py` 中的路径设置
3. **权限错误**: 确保输出目录有写入权限

### 获取帮助

- 查看 `docs/` 目录获取详细文档
- 运行测试验证系统状态
- 查看日志文件排查问题

## 版本信息

- 版本: 1.0.0
- 部署日期: {deployment_date}
- 兼容性: 100% 向后兼容

## 许可证

请遵循项目许可证使用本软件。
""".format(deployment_date=datetime.now().strftime('%Y-%m-%d'))
        
        readme_file = self.deployment_dir / 'README.md'
        with open(readme_file, 'w', encoding='utf-8') as f:
            f.write(readme_content)
        print("   ✅ 生成: README.md")
    
    def create_backup(self):
        """创建备份"""
        print("\n💾 创建备份...")
        
        # 创建备份目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"mythquant_backup_{timestamp}"
        backup_path = self.backup_dir / backup_name
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 备份核心文件
        core_files = [
            'config_compatibility.py',
            'data_access_compatibility.py',
            'algorithm_compatibility.py',
            'io_compatibility.py',
            'user_config.py'
        ]
        
        for file_name in core_files:
            src = self.project_root / file_name
            if src.exists():
                dst = backup_path / file_name
                shutil.copy2(src, dst)
                print(f"   ✅ 备份: {file_name}")
        
        # 备份src目录
        src_dir = self.project_root / 'src'
        if src_dir.exists():
            dst_src = backup_path / 'src'
            shutil.copytree(src_dir, dst_src, dirs_exist_ok=True)
            print("   ✅ 备份: src/ 目录")
        
        print(f"   📁 备份位置: {backup_path}")


def main():
    """主函数"""
    preparator = DeploymentPreparator()
    success = preparator.prepare_deployment()
    
    if success:
        print("\n🎉 部署准备成功完成！")
        print("\n📋 下一步:")
        print("1. 查看 deployment/ 目录")
        print("2. 复制部署包到目标环境")
        print("3. 按照 README.md 说明进行部署")
        print("4. 运行验证测试确保系统正常")
        return True
    else:
        print("\n❌ 部署准备失败！")
        print("请检查错误信息并修复问题后重试")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
