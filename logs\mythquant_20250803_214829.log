2025-08-03 21:48:29,540 - TaskManager - INFO - 股票数据处理器初始化完成: ./test_tdx
2025-08-03 21:48:29,540 - TaskManager - INFO - 🔄 开始加载和处理分钟数据: sz000617
2025-08-03 21:48:29,540 - TaskManager - INFO - 📅 时间范围: 2025-08-01 09:30:00 至 2025-08-03 15:00:00
2025-08-03 21:48:29,541 - TaskManager - INFO - 🌐 尝试从pytdx获取000617的分钟数据
2025-08-03 21:48:30,492 - TaskManager - INFO - ✅ pytdx数据获取成功: 240 条记录
2025-08-03 21:48:30,492 - TaskManager - INFO - 📊 原始数据加载成功: 240 条记录
2025-08-03 21:48:30,493 - TaskManager - INFO - 🔧 开始预处理分钟数据
2025-08-03 21:48:30,496 - TaskManager - INFO - ✅ 数据预处理完成: 240 条有效记录
2025-08-03 21:48:30,497 - TaskManager - INFO - 📊 开始计算前复权价格
2025-08-03 21:48:30,499 - TaskManager - INFO - ✅ 前复权价格计算完成（简化方法）
2025-08-03 21:48:30,499 - TaskManager - INFO - 🧮 开始计算L2指标
2025-08-03 21:48:30,520 - TaskManager - INFO - ✅ 新架构L2指标计算成功
2025-08-03 21:48:30,520 - TaskManager - INFO - 📋 格式化输出数据
2025-08-03 21:48:30,522 - TaskManager - INFO - ✅ 数据格式化完成: 240 条记录
2025-08-03 21:48:30,522 - TaskManager - INFO - ✅ 分钟数据处理完成: 240 条记录
2025-08-03 21:48:30,522 - TaskManager - INFO - 股票数据处理器初始化完成: ./test_tdx
2025-08-03 21:48:30,523 - TaskManager - INFO - 执行分钟级数据生成任务（DDD架构）
2025-08-03 21:48:30,524 - TaskManager - INFO - 🚀 开始生成分钟数据 (输出到: H:/MPV1.17/T0002/signals)
2025-08-03 21:48:30,524 - TaskManager - INFO - 🔄 开始加载和处理分钟数据: sz000617
2025-08-03 21:48:30,525 - TaskManager - INFO - 📅 时间范围: 2025-08-01 09:30:00 至 2025-08-03 15:00:00
2025-08-03 21:48:30,525 - TaskManager - INFO - 🌐 尝试从pytdx获取000617的分钟数据
2025-08-03 21:48:31,436 - TaskManager - INFO - ✅ pytdx数据获取成功: 240 条记录
2025-08-03 21:48:31,436 - TaskManager - INFO - 📊 原始数据加载成功: 240 条记录
2025-08-03 21:48:31,436 - TaskManager - INFO - 🔧 开始预处理分钟数据
2025-08-03 21:48:31,439 - TaskManager - INFO - ✅ 数据预处理完成: 240 条有效记录
2025-08-03 21:48:31,439 - TaskManager - INFO - 📊 开始计算前复权价格
2025-08-03 21:48:31,440 - TaskManager - INFO - ✅ 前复权价格计算完成（简化方法）
2025-08-03 21:48:31,440 - TaskManager - INFO - 🧮 开始计算L2指标
2025-08-03 21:48:31,461 - TaskManager - INFO - ✅ 新架构L2指标计算成功
2025-08-03 21:48:31,461 - TaskManager - INFO - 📋 格式化输出数据
2025-08-03 21:48:31,463 - TaskManager - INFO - ✅ 数据格式化完成: 240 条记录
2025-08-03 21:48:31,463 - TaskManager - INFO - ✅ 分钟数据处理完成: 240 条记录
2025-08-03 21:48:31,497 - TaskManager - INFO - 成功写入分钟级别txt文件: H:/MPV1.17/T0002/signals\1min_0_000617_20250801-20250803.txt, 数据行数: 240
2025-08-03 21:48:31,497 - TaskManager - INFO - 
分钟级别txt文件生成完成汇总:
2025-08-03 21:48:31,497 - TaskManager - INFO - 📊 成功生成: 1 个文件
2025-08-03 21:48:31,497 - TaskManager - INFO - ❌ 处理失败: 0 个股票
2025-08-03 21:48:31,497 - TaskManager - INFO - 📈 成功率: 100.0%
2025-08-03 21:48:31,498 - TaskManager - INFO - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-08-03 21:48:31,498 - TaskManager - INFO - ✅ 分钟数据生成完成，耗时: 0.97 秒
2025-08-03 21:48:31,499 - TaskManager - INFO - 成功写入分钟级别txt文件: test_1min_output.txt, 数据行数: 1
2025-08-03 21:48:31,842 - TaskManager - INFO - 股票数据处理器初始化完成: ./test_tdx
2025-08-03 21:48:31,842 - TaskManager - INFO - 正在加载任务配置...
2025-08-03 21:48:31,842 - TaskManager - INFO - 加载了 2 个任务配置
2025-08-03 21:48:31,843 - TaskManager - INFO - 执行分钟级数据生成任务（DDD架构）
2025-08-03 21:48:31,843 - TaskManager - INFO - 🚀 开始生成分钟数据 (输出到: H:/MPV1.17/T0002/signals)
2025-08-03 21:48:31,843 - TaskManager - INFO - 🔄 开始加载和处理分钟数据: sz000617
2025-08-03 21:48:31,843 - TaskManager - INFO - 📅 时间范围: 2025-08-01 09:30:00 至 2025-08-03 15:00:00
2025-08-03 21:48:31,843 - TaskManager - INFO - 🌐 尝试从pytdx获取000617的分钟数据
2025-08-03 21:48:32,777 - TaskManager - INFO - ✅ pytdx数据获取成功: 240 条记录
2025-08-03 21:48:32,778 - TaskManager - INFO - 📊 原始数据加载成功: 240 条记录
2025-08-03 21:48:32,778 - TaskManager - INFO - 🔧 开始预处理分钟数据
2025-08-03 21:48:32,781 - TaskManager - INFO - ✅ 数据预处理完成: 240 条有效记录
2025-08-03 21:48:32,781 - TaskManager - INFO - 📊 开始计算前复权价格
2025-08-03 21:48:32,782 - TaskManager - INFO - ✅ 前复权价格计算完成（简化方法）
2025-08-03 21:48:32,782 - TaskManager - INFO - 🧮 开始计算L2指标
2025-08-03 21:48:32,802 - TaskManager - INFO - ✅ 新架构L2指标计算成功
2025-08-03 21:48:32,802 - TaskManager - INFO - 📋 格式化输出数据
2025-08-03 21:48:32,804 - TaskManager - INFO - ✅ 数据格式化完成: 240 条记录
2025-08-03 21:48:32,804 - TaskManager - INFO - ✅ 分钟数据处理完成: 240 条记录
2025-08-03 21:48:32,825 - TaskManager - INFO - 成功写入分钟级别txt文件: H:/MPV1.17/T0002/signals\1min_0_000617_20250801-20250803.txt, 数据行数: 240
2025-08-03 21:48:32,825 - TaskManager - INFO - 
分钟级别txt文件生成完成汇总:
2025-08-03 21:48:32,825 - TaskManager - INFO - 📊 成功生成: 1 个文件
2025-08-03 21:48:32,825 - TaskManager - INFO - ❌ 处理失败: 0 个股票
2025-08-03 21:48:32,825 - TaskManager - INFO - 📈 成功率: 100.0%
2025-08-03 21:48:32,826 - TaskManager - INFO - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-08-03 21:48:32,826 - TaskManager - INFO - ✅ 分钟数据生成完成，耗时: 0.98 秒
