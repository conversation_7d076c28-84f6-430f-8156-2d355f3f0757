---
type: "auto"
description: "AI助手智能行为规则，根据上下文自动应用"
---

# MythQuant项目AI智能规则

## 智能文件定位
当用户提及功能时，自动定位到对应文件：
- **配置修改** → `user_config.py`
- **核心任务** → `src/mythquant/core/` 或 `main.py`
- **数据处理** → `utils/` 目录对应模块
- **测试相关** → `test_environments/` 目录
- **数据质量** → `utils/missing_data_processor.py`
- **数据修复** → `utils/pytdx_data_repairer.py`

## 知识库智能索引和检索
### **配置驱动的智能检索**
基于`.augment/knowledge_index.yaml`配置进行智能检索：
- **分类检索**: 根据问题类型自动选择对应的知识库分类
- **多策略检索**: 结合精确匹配、语义匹配、模式匹配进行检索
- **优先级排序**: 基于相关性、时效性、完整性、使用频率排序结果
- **交叉引用**: 自动发现和利用知识库之间的交叉引用关系

### **知识库分类索引映射**
- **技术错误问题** → `debugging_knowledge_base.md` + `faq_knowledge_base.md`
- **工作流设计问题** → `workflow_atomicity_issue_analysis.md` + 实施报告
- **架构设计问题** → `implementation/`目录 + DDD相关文档
- **质量规范问题** → `testing_quality_rules.md` + 相关规范文档
- **问题排查经验** → `troubleshooting/`目录下的专项分析文档
- **最佳实践总结** → `implementation/`目录下的实施总结

### **多策略检索优化**
- **精确匹配**: 错误代码、异常类型、具体技术术语
- **语义匹配**: 问题描述、解决方案、技术概念
- **模式匹配**: 问题类型、解决模式、架构模式
- **时间相关性**: 优先检索最近更新的相关知识和经验
- **交叉引用**: 通过一个知识点发现相关的其他知识资产

### **检索结果应用策略**
1. **问题分类识别** → 确定问题类型和严重程度
2. **多源知识检索** → 同时检索多个相关知识库
3. **结果综合分析** → 整合多个知识源的信息
4. **解决方案生成** → 基于知识库内容生成解决方案
5. **引用来源说明** → 明确说明引用的知识库和具体内容
6. **知识更新评估** → 评估是否需要更新现有知识库

## 测试驱动质量保证
### **警醒测试原则**
- **主动测试**：用户要求测试时，在专门测试环境中全面测试
- **多层验证**：静态检查 + 功能测试 + 实际执行验证
- **问题导向**：测试目标是发现问题，保持警醒态度
- **实际运行**：不能仅依赖静态分析，必须实际运行验证

### **测试环境管理**
- **环境隔离**：测试在test_environments/目录中进行
- **路径规范**：使用正确的项目根目录和绝对路径
- **编码处理**：正确处理Windows下的Unicode编码
- **报告生成**：重要测试生成详细报告

## 代码质量自动检查
### **DataFrame布尔判断检查**
- 自动检测`if df`用法，建议使用`df is None or df.empty`
- 检查pandas操作的空值处理

### **函数名称冲突检查**
- 检测同一模块中的重复函数名
- 建议使用不同的命名空间或重构

### **工作流原子性检查**
- 检测工作流步骤中的复杂嵌套调用
- 确保每个步骤职责单一，避免副作用
- 识别多线程环境下的输出时序问题

### **依赖完整性验证**
- 确保导入路径在不同环境中正确
- 检查模块依赖的完整性
- 优先使用绝对导入

## 流程优化自动应用
- **智能识别**：自动识别需要流程优化的场景
- **用户体验导向**：隐藏技术细节，突出关键信息
- **系统性思维**：建立系统性的流程管理机制
- **质量标准**：确保输出质量95%以上

## 知识沉淀自动化
- **问题模式识别**：自动识别新问题模式并沉淀
- **FAQ自动更新**：重要问题解决后评估是否需要添加到FAQ
- **规则体系完善**：发现规则缺失时主动提出更新建议
- **经验教训提取**：重要工作完成后自动提取最佳实践
- **举一反三**：解决问题后分析类似问题的预防措施

## 用户交互智能优化
- **错误识别**：主动识别用户可能的文件定位错误
- **具体建议**：提供具体修改建议而非抽象指导
- **决策解释**：解释技术决策的原因
- **主动确认**：不确定时主动询问确认

---

**文件类型**: Auto规则 - AI自动检测应用
**最后更新**: 2025-08-10 14:30 (重组版)
**适用范围**: AI助手智能行为指导
**重组说明**: 从96行优化到78行，增强智能化程度
