#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
指标收集系统实现

提供指标定义、收集、聚合、导出等功能
"""

import time
import threading
from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Union, Callable
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from enum import Enum
from collections import defaultdict, deque
import statistics
import logging

logger = logging.getLogger(__name__)


class MetricType(Enum):
    """指标类型"""
    COUNTER = "counter"          # 计数器（只增不减）
    GAUGE = "gauge"              # 仪表（可增可减）
    HISTOGRAM = "histogram"      # 直方图
    SUMMARY = "summary"          # 摘要
    TIMER = "timer"              # 计时器


@dataclass
class MetricSample:
    """指标样本"""
    timestamp: float
    value: Union[int, float]
    labels: Dict[str, str] = field(default_factory=dict)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'timestamp': self.timestamp,
            'value': self.value,
            'labels': self.labels
        }


class Metric(ABC):
    """指标抽象基类"""
    
    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None):
        self.name = name
        self.description = description
        self.labels = labels or {}
        self._lock = threading.RLock()
    
    @abstractmethod
    def collect(self) -> List[MetricSample]:
        """收集指标样本"""
        pass
    
    @abstractmethod
    def reset(self):
        """重置指标"""
        pass


class Counter(Metric):
    """计数器指标"""
    
    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None):
        super().__init__(name, description, labels)
        self._value = 0
    
    def inc(self, amount: Union[int, float] = 1):
        """增加计数"""
        if amount < 0:
            raise ValueError("计数器只能增加正数")
        
        with self._lock:
            self._value += amount
    
    def get_value(self) -> Union[int, float]:
        """获取当前值"""
        with self._lock:
            return self._value
    
    def collect(self) -> List[MetricSample]:
        """收集样本"""
        with self._lock:
            return [MetricSample(
                timestamp=time.time(),
                value=self._value,
                labels=self.labels
            )]
    
    def reset(self):
        """重置计数器"""
        with self._lock:
            self._value = 0


class Gauge(Metric):
    """仪表指标"""
    
    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None):
        super().__init__(name, description, labels)
        self._value = 0
    
    def set(self, value: Union[int, float]):
        """设置值"""
        with self._lock:
            self._value = value
    
    def inc(self, amount: Union[int, float] = 1):
        """增加值"""
        with self._lock:
            self._value += amount
    
    def dec(self, amount: Union[int, float] = 1):
        """减少值"""
        with self._lock:
            self._value -= amount
    
    def get_value(self) -> Union[int, float]:
        """获取当前值"""
        with self._lock:
            return self._value
    
    def collect(self) -> List[MetricSample]:
        """收集样本"""
        with self._lock:
            return [MetricSample(
                timestamp=time.time(),
                value=self._value,
                labels=self.labels
            )]
    
    def reset(self):
        """重置仪表"""
        with self._lock:
            self._value = 0


class Histogram(Metric):
    """直方图指标"""
    
    def __init__(self, name: str, description: str = "", 
                 buckets: Optional[List[float]] = None,
                 labels: Optional[Dict[str, str]] = None):
        super().__init__(name, description, labels)
        self._buckets = buckets or [0.005, 0.01, 0.025, 0.05, 0.1, 0.25, 0.5, 1.0, 2.5, 5.0, 10.0]
        self._bucket_counts = {bucket: 0 for bucket in self._buckets}
        self._bucket_counts[float('inf')] = 0  # +Inf bucket
        self._count = 0
        self._sum = 0
    
    def observe(self, value: Union[int, float]):
        """观察值"""
        with self._lock:
            self._count += 1
            self._sum += value
            
            # 更新桶计数
            for bucket in self._buckets:
                if value <= bucket:
                    self._bucket_counts[bucket] += 1
            self._bucket_counts[float('inf')] += 1
    
    def get_count(self) -> int:
        """获取观察次数"""
        with self._lock:
            return self._count
    
    def get_sum(self) -> Union[int, float]:
        """获取观察值总和"""
        with self._lock:
            return self._sum
    
    def get_buckets(self) -> Dict[float, int]:
        """获取桶计数"""
        with self._lock:
            return self._bucket_counts.copy()
    
    def collect(self) -> List[MetricSample]:
        """收集样本"""
        with self._lock:
            samples = []
            
            # 桶样本
            for bucket, count in self._bucket_counts.items():
                bucket_labels = {**self.labels, 'le': str(bucket)}
                samples.append(MetricSample(
                    timestamp=time.time(),
                    value=count,
                    labels=bucket_labels
                ))
            
            # 计数样本
            count_labels = {**self.labels}
            samples.append(MetricSample(
                timestamp=time.time(),
                value=self._count,
                labels=count_labels
            ))
            
            # 总和样本
            sum_labels = {**self.labels}
            samples.append(MetricSample(
                timestamp=time.time(),
                value=self._sum,
                labels=sum_labels
            ))
            
            return samples
    
    def reset(self):
        """重置直方图"""
        with self._lock:
            self._bucket_counts = {bucket: 0 for bucket in self._buckets}
            self._bucket_counts[float('inf')] = 0
            self._count = 0
            self._sum = 0


class Summary(Metric):
    """摘要指标"""
    
    def __init__(self, name: str, description: str = "",
                 quantiles: Optional[List[float]] = None,
                 max_age: float = 600,  # 10分钟
                 labels: Optional[Dict[str, str]] = None):
        super().__init__(name, description, labels)
        self._quantiles = quantiles or [0.5, 0.9, 0.95, 0.99]
        self._max_age = max_age
        self._observations = deque()
        self._count = 0
        self._sum = 0
    
    def observe(self, value: Union[int, float]):
        """观察值"""
        with self._lock:
            current_time = time.time()
            self._observations.append((current_time, value))
            self._count += 1
            self._sum += value
            
            # 清理过期观察值
            self._cleanup_old_observations(current_time)
    
    def get_count(self) -> int:
        """获取观察次数"""
        with self._lock:
            return self._count
    
    def get_sum(self) -> Union[int, float]:
        """获取观察值总和"""
        with self._lock:
            return self._sum
    
    def get_quantiles(self) -> Dict[float, float]:
        """获取分位数"""
        with self._lock:
            current_time = time.time()
            self._cleanup_old_observations(current_time)
            
            if not self._observations:
                return {q: 0 for q in self._quantiles}
            
            values = [obs[1] for obs in self._observations]
            values.sort()
            
            quantiles = {}
            for q in self._quantiles:
                if q < 0 or q > 1:
                    continue
                
                if q == 0:
                    quantiles[q] = values[0]
                elif q == 1:
                    quantiles[q] = values[-1]
                else:
                    index = q * (len(values) - 1)
                    if index.is_integer():
                        quantiles[q] = values[int(index)]
                    else:
                        lower = values[int(index)]
                        upper = values[int(index) + 1]
                        quantiles[q] = lower + (upper - lower) * (index - int(index))
            
            return quantiles
    
    def collect(self) -> List[MetricSample]:
        """收集样本"""
        with self._lock:
            samples = []
            
            # 分位数样本
            quantiles = self.get_quantiles()
            for q, value in quantiles.items():
                quantile_labels = {**self.labels, 'quantile': str(q)}
                samples.append(MetricSample(
                    timestamp=time.time(),
                    value=value,
                    labels=quantile_labels
                ))
            
            # 计数样本
            samples.append(MetricSample(
                timestamp=time.time(),
                value=self._count,
                labels=self.labels
            ))
            
            # 总和样本
            samples.append(MetricSample(
                timestamp=time.time(),
                value=self._sum,
                labels=self.labels
            ))
            
            return samples
    
    def reset(self):
        """重置摘要"""
        with self._lock:
            self._observations.clear()
            self._count = 0
            self._sum = 0
    
    def _cleanup_old_observations(self, current_time: float):
        """清理过期观察值"""
        cutoff_time = current_time - self._max_age
        while self._observations and self._observations[0][0] < cutoff_time:
            self._observations.popleft()


class Timer(Metric):
    """计时器指标"""
    
    def __init__(self, name: str, description: str = "", labels: Optional[Dict[str, str]] = None):
        super().__init__(name, description, labels)
        self._histogram = Histogram(f"{name}_duration", f"{description} duration")
        self._counter = Counter(f"{name}_total", f"{description} total")
    
    def time(self, func: Optional[Callable] = None):
        """计时装饰器或上下文管理器"""
        if func is not None:
            # 作为装饰器使用
            def wrapper(*args, **kwargs):
                start_time = time.time()
                try:
                    result = func(*args, **kwargs)
                    duration = time.time() - start_time
                    self.observe(duration)
                    return result
                except Exception as e:
                    duration = time.time() - start_time
                    self.observe(duration)
                    raise
            return wrapper
        else:
            # 作为上下文管理器使用
            return TimerContext(self)
    
    def observe(self, duration: float):
        """观察执行时间"""
        self._histogram.observe(duration)
        self._counter.inc()
    
    def collect(self) -> List[MetricSample]:
        """收集样本"""
        samples = []
        samples.extend(self._histogram.collect())
        samples.extend(self._counter.collect())
        return samples
    
    def reset(self):
        """重置计时器"""
        self._histogram.reset()
        self._counter.reset()


class TimerContext:
    """计时器上下文管理器"""
    
    def __init__(self, timer: Timer):
        self._timer = timer
        self._start_time = None
    
    def __enter__(self):
        self._start_time = time.time()
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if self._start_time is not None:
            duration = time.time() - self._start_time
            self._timer.observe(duration)


class MetricsRegistry:
    """指标注册表"""
    
    def __init__(self):
        self._metrics: Dict[str, Metric] = {}
        self._lock = threading.RLock()
    
    def register(self, metric: Metric) -> Metric:
        """注册指标"""
        with self._lock:
            if metric.name in self._metrics:
                raise ValueError(f"指标已存在: {metric.name}")
            
            self._metrics[metric.name] = metric
            logger.debug(f"注册指标: {metric.name}")
            return metric
    
    def unregister(self, name: str) -> bool:
        """取消注册指标"""
        with self._lock:
            if name in self._metrics:
                del self._metrics[name]
                logger.debug(f"取消注册指标: {name}")
                return True
            return False
    
    def get_metric(self, name: str) -> Optional[Metric]:
        """获取指标"""
        return self._metrics.get(name)
    
    def get_all_metrics(self) -> Dict[str, Metric]:
        """获取所有指标"""
        with self._lock:
            return self._metrics.copy()
    
    def collect_all(self) -> List[MetricSample]:
        """收集所有指标样本"""
        samples = []
        with self._lock:
            for metric in self._metrics.values():
                try:
                    samples.extend(metric.collect())
                except Exception as e:
                    logger.error(f"收集指标失败: {metric.name}, 错误: {e}")
        return samples
    
    def reset_all(self):
        """重置所有指标"""
        with self._lock:
            for metric in self._metrics.values():
                try:
                    metric.reset()
                except Exception as e:
                    logger.error(f"重置指标失败: {metric.name}, 错误: {e}")
    
    def get_metrics_info(self) -> List[Dict[str, Any]]:
        """获取指标信息"""
        with self._lock:
            return [
                {
                    'name': metric.name,
                    'description': metric.description,
                    'type': type(metric).__name__.lower(),
                    'labels': metric.labels
                }
                for metric in self._metrics.values()
            ]


class MetricsCollector:
    """指标收集器"""
    
    def __init__(self, registry: Optional[MetricsRegistry] = None):
        self._registry = registry or MetricsRegistry()
        self._collection_interval = 60  # 1分钟
        self._running = False
        self._thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
        self._collectors: List[Callable[[], List[MetricSample]]] = []
    
    @property
    def registry(self) -> MetricsRegistry:
        """指标注册表"""
        return self._registry
    
    def add_collector(self, collector: Callable[[], List[MetricSample]]):
        """添加自定义收集器"""
        with self._lock:
            self._collectors.append(collector)
    
    def start_collection(self, interval: float = 60):
        """启动指标收集"""
        with self._lock:
            if self._running:
                return
            
            self._collection_interval = interval
            self._running = True
            self._thread = threading.Thread(target=self._collection_worker, daemon=True)
            self._thread.start()
            logger.info(f"指标收集已启动，间隔: {interval}秒")
    
    def stop_collection(self):
        """停止指标收集"""
        with self._lock:
            self._running = False
            if self._thread:
                self._thread.join(timeout=5)
            logger.info("指标收集已停止")
    
    def collect_once(self) -> List[MetricSample]:
        """执行一次收集"""
        samples = []
        
        # 收集注册表中的指标
        samples.extend(self._registry.collect_all())
        
        # 收集自定义收集器的指标
        with self._lock:
            for collector in self._collectors:
                try:
                    samples.extend(collector())
                except Exception as e:
                    logger.error(f"自定义收集器执行失败: {e}")
        
        return samples
    
    def _collection_worker(self):
        """收集工作线程"""
        while self._running:
            try:
                samples = self.collect_once()
                logger.debug(f"收集了 {len(samples)} 个指标样本")
                
                # 这里可以将样本发送到监控系统
                # 例如：Prometheus、InfluxDB、CloudWatch等
                
            except Exception as e:
                logger.error(f"指标收集失败: {e}")
            
            time.sleep(self._collection_interval)


# 全局指标注册表和收集器
_global_registry = MetricsRegistry()
_global_collector = MetricsCollector(_global_registry)


def get_metrics_registry() -> MetricsRegistry:
    """获取全局指标注册表"""
    return _global_registry


def get_metrics_collector() -> MetricsCollector:
    """获取全局指标收集器"""
    return _global_collector


# 便捷函数
def counter(name: str, description: str = "", labels: Optional[Dict[str, str]] = None) -> Counter:
    """创建并注册计数器"""
    metric = Counter(name, description, labels)
    return _global_registry.register(metric)


def gauge(name: str, description: str = "", labels: Optional[Dict[str, str]] = None) -> Gauge:
    """创建并注册仪表"""
    metric = Gauge(name, description, labels)
    return _global_registry.register(metric)


def histogram(name: str, description: str = "", 
              buckets: Optional[List[float]] = None,
              labels: Optional[Dict[str, str]] = None) -> Histogram:
    """创建并注册直方图"""
    metric = Histogram(name, description, buckets, labels)
    return _global_registry.register(metric)


def summary(name: str, description: str = "",
            quantiles: Optional[List[float]] = None,
            labels: Optional[Dict[str, str]] = None) -> Summary:
    """创建并注册摘要"""
    metric = Summary(name, description, quantiles, labels=labels)
    return _global_registry.register(metric)


def timer(name: str, description: str = "", labels: Optional[Dict[str, str]] = None) -> Timer:
    """创建并注册计时器"""
    metric = Timer(name, description, labels)
    return _global_registry.register(metric)
