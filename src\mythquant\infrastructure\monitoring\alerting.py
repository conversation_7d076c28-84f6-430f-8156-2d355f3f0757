#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
告警模块

提供系统告警功能
"""

import time
from enum import Enum
from typing import Dict, List, Any, Optional, Callable
from dataclasses import dataclass
import logging

logger = logging.getLogger(__name__)


class AlertLevel(Enum):
    """告警级别"""
    INFO = "info"
    WARNING = "warning"
    ERROR = "error"
    CRITICAL = "critical"


@dataclass
class Alert:
    """告警信息"""
    id: str
    level: AlertLevel
    title: str
    message: str
    source: str = ""
    timestamp: float = None
    metadata: Dict[str, Any] = None
    resolved: bool = False
    resolved_at: Optional[float] = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = time.time()
        if self.metadata is None:
            self.metadata = {}
    
    def resolve(self):
        """解决告警"""
        self.resolved = True
        self.resolved_at = time.time()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'level': self.level.value,
            'title': self.title,
            'message': self.message,
            'source': self.source,
            'timestamp': self.timestamp,
            'metadata': self.metadata,
            'resolved': self.resolved,
            'resolved_at': self.resolved_at
        }


@dataclass
class AlertRule:
    """告警规则"""
    name: str
    condition: Callable[[Dict[str, Any]], bool]
    level: AlertLevel
    title: str
    message_template: str
    enabled: bool = True
    cooldown: float = 300  # 5分钟冷却期
    last_triggered: Optional[float] = None
    
    def should_trigger(self, data: Dict[str, Any]) -> bool:
        """检查是否应该触发告警"""
        if not self.enabled:
            return False
        
        # 检查冷却期
        if self.last_triggered and (time.time() - self.last_triggered) < self.cooldown:
            return False
        
        return self.condition(data)
    
    def trigger(self, data: Dict[str, Any]) -> Alert:
        """触发告警"""
        self.last_triggered = time.time()
        
        alert_id = f"{self.name}_{int(time.time() * 1000)}"
        message = self.message_template.format(**data)
        
        return Alert(
            id=alert_id,
            level=self.level,
            title=self.title,
            message=message,
            source=self.name,
            metadata=data
        )


class AlertManager:
    """告警管理器"""
    
    def __init__(self):
        self._rules: Dict[str, AlertRule] = {}
        self._active_alerts: Dict[str, Alert] = {}
        self._alert_history: List[Alert] = []
        self._max_history = 1000
    
    def add_rule(self, rule: AlertRule):
        """添加告警规则"""
        self._rules[rule.name] = rule
        logger.info(f"添加告警规则: {rule.name}")
    
    def remove_rule(self, name: str):
        """移除告警规则"""
        if name in self._rules:
            del self._rules[name]
            logger.info(f"移除告警规则: {name}")
    
    def enable_rule(self, name: str):
        """启用告警规则"""
        if name in self._rules:
            self._rules[name].enabled = True
            logger.info(f"启用告警规则: {name}")
    
    def disable_rule(self, name: str):
        """禁用告警规则"""
        if name in self._rules:
            self._rules[name].enabled = False
            logger.info(f"禁用告警规则: {name}")
    
    def check_rules(self, data: Dict[str, Any]) -> List[Alert]:
        """检查所有规则"""
        triggered_alerts = []
        
        for rule in self._rules.values():
            if rule.should_trigger(data):
                alert = rule.trigger(data)
                self._active_alerts[alert.id] = alert
                self._alert_history.append(alert)
                triggered_alerts.append(alert)
                
                logger.warning(f"触发告警: {alert.title} - {alert.message}")
        
        # 限制历史记录大小
        if len(self._alert_history) > self._max_history:
            self._alert_history = self._alert_history[-self._max_history:]
        
        return triggered_alerts
    
    def resolve_alert(self, alert_id: str):
        """解决告警"""
        if alert_id in self._active_alerts:
            alert = self._active_alerts[alert_id]
            alert.resolve()
            del self._active_alerts[alert_id]
            logger.info(f"解决告警: {alert.title}")
    
    def get_active_alerts(self) -> List[Alert]:
        """获取活跃告警"""
        return list(self._active_alerts.values())
    
    def get_alert_history(self, limit: int = 100) -> List[Alert]:
        """获取告警历史"""
        return self._alert_history[-limit:]
    
    def get_alert_stats(self) -> Dict[str, Any]:
        """获取告警统计"""
        active_by_level = {}
        history_by_level = {}
        
        for alert in self._active_alerts.values():
            level = alert.level.value
            active_by_level[level] = active_by_level.get(level, 0) + 1
        
        for alert in self._alert_history:
            level = alert.level.value
            history_by_level[level] = history_by_level.get(level, 0) + 1
        
        return {
            'active_alerts': len(self._active_alerts),
            'total_rules': len(self._rules),
            'enabled_rules': len([r for r in self._rules.values() if r.enabled]),
            'active_by_level': active_by_level,
            'history_by_level': history_by_level,
            'total_history': len(self._alert_history)
        }
    
    def send_alert(self, level: AlertLevel, title: str, message: str, 
                   source: str = "", metadata: Dict[str, Any] = None):
        """手动发送告警"""
        alert_id = f"manual_{int(time.time() * 1000)}"
        
        alert = Alert(
            id=alert_id,
            level=level,
            title=title,
            message=message,
            source=source,
            metadata=metadata or {}
        )
        
        self._active_alerts[alert.id] = alert
        self._alert_history.append(alert)
        
        logger.warning(f"手动告警: {title} - {message}")
        return alert


# 预定义的告警规则
def create_cpu_usage_rule() -> AlertRule:
    """CPU使用率告警规则"""
    def condition(data: Dict[str, Any]) -> bool:
        cpu_percent = data.get('cpu', {}).get('percent', 0)
        return cpu_percent > 80
    
    return AlertRule(
        name="high_cpu_usage",
        condition=condition,
        level=AlertLevel.WARNING,
        title="CPU使用率过高",
        message_template="CPU使用率达到 {cpu[percent]:.1f}%",
        cooldown=300
    )


def create_memory_usage_rule() -> AlertRule:
    """内存使用率告警规则"""
    def condition(data: Dict[str, Any]) -> bool:
        memory_percent = data.get('memory', {}).get('percent', 0)
        return memory_percent > 85
    
    return AlertRule(
        name="high_memory_usage",
        condition=condition,
        level=AlertLevel.WARNING,
        title="内存使用率过高",
        message_template="内存使用率达到 {memory[percent]:.1f}%",
        cooldown=300
    )


def create_error_rate_rule() -> AlertRule:
    """错误率告警规则"""
    def condition(data: Dict[str, Any]) -> bool:
        error_rate = data.get('error_rate', 0)
        return error_rate > 0.05  # 5%
    
    return AlertRule(
        name="high_error_rate",
        condition=condition,
        level=AlertLevel.ERROR,
        title="错误率过高",
        message_template="错误率达到 {error_rate:.2%}",
        cooldown=600
    )


# 全局告警管理器
_global_alert_manager: Optional[AlertManager] = None


def get_alert_manager() -> AlertManager:
    """获取全局告警管理器"""
    global _global_alert_manager
    if _global_alert_manager is None:
        _global_alert_manager = AlertManager()
        
        # 添加默认告警规则
        _global_alert_manager.add_rule(create_cpu_usage_rule())
        _global_alert_manager.add_rule(create_memory_usage_rule())
        _global_alert_manager.add_rule(create_error_rate_rule())
    
    return _global_alert_manager
