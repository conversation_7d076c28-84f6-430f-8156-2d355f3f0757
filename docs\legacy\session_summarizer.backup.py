#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
会话摘要生成器
自动生成会话摘要并添加到FAQ知识库
"""

import os
import re
from datetime import datetime
from typing import List, Dict, Optional
from knowledge_base.faq_manager import FAQManager

class SessionSummarizer:
    """会话摘要生成器"""
    
    def __init__(self):
        self.faq_manager = FAQManager()
        self.session_keywords = [
            # 问题类型关键词
            '问题', '错误', '失败', '无法', '不能', '异常', 'bug', 'error', 'warning',
            # 解决方案关键词
            '解决', '修复', '优化', '改进', '实现', '方案', '建议',
            # 技术关键词
            '正则表达式', 'pytdx', '数据下载', '文件处理', '配置', '架构',
            # 结果关键词
            '成功', '完成', '验证', '测试', '效果'
        ]
    
    def generate_session_summary(self, session_content: str, session_title: str = "") -> Dict:
        """
        生成会话摘要
        
        Args:
            session_content: 会话内容
            session_title: 会话标题
            
        Returns:
            会话摘要字典
        """
        try:
            summary = {
                'title': session_title or self._extract_main_topic(session_content),
                'timestamp': datetime.now().strftime('%Y-%m-%d %H:%M:%S'),
                'problems_identified': self._extract_problems(session_content),
                'solutions_provided': self._extract_solutions(session_content),
                'technical_points': self._extract_technical_points(session_content),
                'files_modified': self._extract_modified_files(session_content),
                'key_achievements': self._extract_achievements(session_content),
                'follow_up_items': self._extract_follow_ups(session_content),
                'complexity_level': self._assess_complexity(session_content),
                'domain_tags': self._extract_domain_tags(session_content),
                'word_count': len(session_content.split())
            }
            
            return summary
            
        except Exception as e:
            print(f"❌ 生成会话摘要失败: {e}")
            return {}
    
    def auto_add_to_faq(self, session_summary: Dict) -> bool:
        """
        自动将会话摘要添加到FAQ
        
        Args:
            session_summary: 会话摘要
            
        Returns:
            是否成功添加
        """
        try:
            if not session_summary.get('problems_identified'):
                return False
            
            # 为每个主要问题创建FAQ条目
            success_count = 0
            for i, problem in enumerate(session_summary['problems_identified'][:3], 1):  # 最多3个主要问题
                question = f"{session_summary['title']} - 问题{i}: {problem['description']}"
                
                # 构建答案
                answer_parts = []
                if problem.get('solution'):
                    answer_parts.append(f"**解决方案**: {problem['solution']}")
                
                if session_summary.get('technical_points'):
                    tech_points = '\n'.join([f"- {point}" for point in session_summary['technical_points'][:5]])
                    answer_parts.append(f"**技术要点**:\n{tech_points}")
                
                if session_summary.get('files_modified'):
                    files = '\n'.join([f"- {file}" for file in session_summary['files_modified'][:5]])
                    answer_parts.append(f"**修改文件**:\n{files}")
                
                answer = '\n\n'.join(answer_parts)
                
                # 确定标签
                domain = session_summary.get('domain_tags', ['通用'])[0]
                module = problem.get('module', '未分类')
                tech_stack = problem.get('tech_stack', '通用')
                
                success = self.faq_manager.add_faq_entry(
                    question=question,
                    answer=answer,
                    tags=[],
                    domain=domain,
                    module=module,
                    tech_stack=tech_stack
                )
                
                if success:
                    success_count += 1
            
            return success_count > 0
            
        except Exception as e:
            print(f"❌ 自动添加FAQ失败: {e}")
            return False
    
    def _extract_main_topic(self, content: str) -> str:
        """提取主要话题"""
        # 简单的话题提取逻辑
        lines = content.split('\n')
        for line in lines[:10]:  # 检查前10行
            if any(keyword in line for keyword in ['问题', '修复', '优化', '实现']):
                return line.strip()[:50]
        return "技术讨论会话"
    
    def _extract_problems(self, content: str) -> List[Dict]:
        """提取问题"""
        problems = []
        problem_patterns = [
            r'问题[：:](.+)',
            r'错误[：:](.+)',
            r'无法(.+)',
            r'失败(.+)',
            r'❌(.+)'
        ]
        
        for pattern in problem_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches[:3]:  # 最多3个问题
                problems.append({
                    'description': match.strip(),
                    'type': 'technical_issue',
                    'severity': 'medium'
                })
        
        return problems
    
    def _extract_solutions(self, content: str) -> List[str]:
        """提取解决方案"""
        solutions = []
        solution_patterns = [
            r'解决方案[：:](.+)',
            r'修复[：:](.+)',
            r'✅(.+)',
            r'方案[：:](.+)'
        ]
        
        for pattern in solution_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            solutions.extend([match.strip() for match in matches[:5]])
        
        return solutions
    
    def _extract_technical_points(self, content: str) -> List[str]:
        """提取技术要点"""
        tech_points = []
        
        # 查找代码块
        code_blocks = re.findall(r'```[\w]*\n(.*?)\n```', content, re.DOTALL)
        for block in code_blocks[:3]:
            tech_points.append(f"代码实现: {block[:100]}...")
        
        # 查找技术关键词
        for keyword in self.session_keywords:
            if keyword in content.lower():
                context = self._extract_context(content, keyword)
                if context:
                    tech_points.append(f"{keyword}: {context}")
        
        return tech_points[:10]
    
    def _extract_modified_files(self, content: str) -> List[str]:
        """提取修改的文件"""
        file_patterns = [
            r'utils/[\w_]+\.py',
            r'core/[\w_]+\.py',
            r'knowledge_base/[\w_]+\.py',
            r'[\w_]+\.py',
            r'[\w_]+\.md'
        ]
        
        files = set()
        for pattern in file_patterns:
            matches = re.findall(pattern, content)
            files.update(matches)
        
        return list(files)[:10]
    
    def _extract_achievements(self, content: str) -> List[str]:
        """提取关键成就"""
        achievements = []
        achievement_patterns = [
            r'✅(.+)',
            r'成功(.+)',
            r'完成(.+)',
            r'解决(.+)'
        ]
        
        for pattern in achievement_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            achievements.extend([match.strip() for match in matches[:5]])
        
        return achievements
    
    def _extract_follow_ups(self, content: str) -> List[str]:
        """提取后续事项"""
        follow_ups = []
        followup_patterns = [
            r'需要(.+)',
            r'建议(.+)',
            r'下一步(.+)',
            r'TODO(.+)'
        ]
        
        for pattern in followup_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            follow_ups.extend([match.strip() for match in matches[:3]])
        
        return follow_ups
    
    def _assess_complexity(self, content: str) -> str:
        """评估复杂度"""
        word_count = len(content.split())
        code_blocks = len(re.findall(r'```', content))
        
        if word_count > 5000 or code_blocks > 10:
            return "高"
        elif word_count > 2000 or code_blocks > 5:
            return "中"
        else:
            return "低"
    
    def _extract_domain_tags(self, content: str) -> List[str]:
        """提取领域标签"""
        domain_keywords = {
            '数据处理': ['数据', '下载', 'pytdx', 'akshare'],
            '文件处理': ['文件', '正则', '匹配', '选择器'],
            '架构设计': ['架构', '模块', '重构', '设计'],
            '性能优化': ['性能', '优化', '缓存', '速度'],
            '配置管理': ['配置', 'config', '设置', '参数'],
            '错误处理': ['错误', '异常', '调试', 'debug']
        }
        
        domains = []
        for domain, keywords in domain_keywords.items():
            if any(keyword in content.lower() for keyword in keywords):
                domains.append(domain)
        
        return domains or ['通用']
    
    def _extract_context(self, content: str, keyword: str, context_length: int = 100) -> str:
        """提取关键词上下文"""
        try:
            index = content.lower().find(keyword.lower())
            if index != -1:
                start = max(0, index - context_length // 2)
                end = min(len(content), index + context_length // 2)
                return content[start:end].strip()
        except:
            pass
        return ""

def demonstrate_session_summarizer():
    """演示会话摘要功能"""
    print("🚀 会话摘要生成器演示")
    print("=" * 50)
    
    summarizer = SessionSummarizer()
    
    # 模拟会话内容
    sample_session = """
    用户提出了正则表达式无法匹配带时间戳文件名的问题。
    
    问题：智能文件选择器无法识别1min_0_000617_20250401-20250726_来源互联网（202507261741）.txt格式的文件。
    
    解决方案：更新正则表达式支持时间戳后缀(?:（\\d{12}）)?
    
    修改文件：
    - utils/smart_file_selector.py
    - utils/incremental_download_validator.py
    
    ✅ 正则表达式修复成功
    ✅ 文件匹配功能正常
    
    ```python
    self.filename_pattern = r'(\\d+min)_0_(\\d{6})_(\\d{8})-(\\d{8})_来源互联网(?:（\\d{12}）)?\\.txt'
    ```
    """
    
    # 生成摘要
    summary = summarizer.generate_session_summary(sample_session, "正则表达式文件名匹配修复")
    
    print("📋 生成的会话摘要:")
    for key, value in summary.items():
        if isinstance(value, list):
            print(f"  {key}: {len(value)} 项")
        else:
            print(f"  {key}: {value}")
    
    print("\n✅ 会话摘要生成器初始化完成")

if __name__ == '__main__':
    demonstrate_session_summarizer()
