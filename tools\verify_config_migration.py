#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证配置迁移效果

检查配置迁移是否成功，配置访问是否正常
"""

import sys
import os
from pathlib import Path

def test_config_compatibility():
    """测试配置兼容性模块"""
    print("🔍 测试配置兼容性模块...")
    
    try:
        import config_compatibility as config
        print("   ✅ config_compatibility 导入成功")
        
        # 测试基本配置访问
        configs_to_test = [
            ('TDX路径', 'tdx_path'),
            ('输出路径', 'output_path'),
            ('调试模式', 'DEBUG'),
            ('智能文件选择器', 'smart_file_selector_enabled'),
            ('文件编码', 'file_encoding')
        ]
        
        print("   📋 配置值测试:")
        for desc, attr in configs_to_test:
            try:
                value = getattr(config, attr, 'Not found')
                print(f"      {desc}: {value}")
            except Exception as e:
                print(f"      {desc}: ❌ 访问失败 - {e}")
        
        # 测试配置函数
        print("   🔧 配置函数测试:")
        try:
            tdx_path = config.get_tdx_path()
            print(f"      get_tdx_path(): {tdx_path}")
        except Exception as e:
            print(f"      get_tdx_path(): ❌ 失败 - {e}")
        
        try:
            debug_mode = config.is_debug_enabled()
            print(f"      is_debug_enabled(): {debug_mode}")
        except Exception as e:
            print(f"      is_debug_enabled(): ❌ 失败 - {e}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ config_compatibility 导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 配置兼容性测试失败: {e}")
        return False

def test_new_config_system():
    """测试新配置系统"""
    print("\n🔍 测试新配置系统...")
    
    try:
        # 添加src目录到路径
        project_root = Path(__file__).parent
        src_path = project_root / "src"
        if str(src_path) not in sys.path:
            sys.path.insert(0, str(src_path))
        
        from mythquant.config import config_manager
        print("   ✅ 新配置系统导入成功")
        
        # 测试配置访问
        configs_to_test = [
            ('TDX路径', 'get_tdx_path'),
            ('输出路径', 'get_output_path'),
            ('调试模式', 'is_debug_enabled'),
            ('智能文件选择器', 'is_smart_file_selector_enabled'),
            ('文件编码', 'get_file_encoding')
        ]
        
        print("   📋 新配置系统测试:")
        for desc, method in configs_to_test:
            try:
                value = getattr(config_manager, method)()
                print(f"      {desc}: {value}")
            except Exception as e:
                print(f"      {desc}: ❌ 访问失败 - {e}")
        
        return True
        
    except ImportError as e:
        print(f"   ❌ 新配置系统导入失败: {e}")
        return False
    except Exception as e:
        print(f"   ❌ 新配置系统测试失败: {e}")
        return False

def check_migrated_files():
    """检查已迁移的文件"""
    print("\n🔍 检查已迁移的文件...")
    
    files_to_check = [
        "main.py",
        "func_Tdx.py", 
        "func_Util.py",
        "gbbq_cache_solution.py"
    ]
    
    migration_status = {}
    
    for file_name in files_to_check:
        file_path = Path(file_name)
        if not file_path.exists():
            print(f"   ⚠️ 文件不存在: {file_name}")
            migration_status[file_name] = 'not_found'
            continue
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except Exception as e:
                print(f"   ❌ 无法读取文件 {file_name}: {e}")
                migration_status[file_name] = 'read_error'
                continue
        
        # 检查迁移状态
        has_old_import = 'import user_config' in content and 'config_compatibility' not in content
        has_new_import = 'config_compatibility' in content
        
        if has_new_import and not has_old_import:
            print(f"   ✅ {file_name}: 已迁移到config_compatibility")
            migration_status[file_name] = 'migrated'
        elif has_old_import and not has_new_import:
            print(f"   ⚠️ {file_name}: 仍使用旧的user_config")
            migration_status[file_name] = 'needs_migration'
        elif has_new_import and has_old_import:
            print(f"   🔄 {file_name}: 混合使用新旧配置")
            migration_status[file_name] = 'mixed'
        else:
            print(f"   ℹ️ {file_name}: 无配置导入")
            migration_status[file_name] = 'no_config'
    
    return migration_status

def generate_migration_status_report(compat_success, new_config_success, migration_status):
    """生成迁移状态报告"""
    print("\n📄 生成迁移状态报告...")
    
    migrated_count = sum(1 for status in migration_status.values() if status == 'migrated')
    total_files = len(migration_status)
    
    report = f"""# 配置迁移状态报告

## 迁移概况
- 检查时间: {os.popen('date /t & time /t').read().strip()}
- 兼容性模块: {'✅ 正常' if compat_success else '❌ 异常'}
- 新配置系统: {'✅ 正常' if new_config_success else '❌ 异常'}
- 已迁移文件: {migrated_count}/{total_files}

## 文件迁移状态
| 文件名 | 状态 | 说明 |
|--------|------|------|
"""
    
    status_descriptions = {
        'migrated': '✅ 已迁移',
        'needs_migration': '⚠️ 需要迁移',
        'mixed': '🔄 混合状态',
        'no_config': 'ℹ️ 无配置',
        'not_found': '❌ 文件不存在',
        'read_error': '❌ 读取错误'
    }
    
    for file_name, status in migration_status.items():
        description = status_descriptions.get(status, '❓ 未知状态')
        report += f"| {file_name} | {description} | - |\n"
    
    report += f"""
## 系统状态评估
{'🎉 配置迁移成功！系统已准备就绪。' if compat_success and migrated_count == total_files else '⚠️ 配置迁移需要进一步处理。'}

## 下一步建议
"""
    
    if compat_success and migrated_count == total_files:
        report += """1. ✅ 配置迁移已完成，可以开始功能测试
2. 🧪 运行主程序验证功能正常
3. 📊 检查输出结果与之前的一致性
4. 🚀 准备进行下一阶段迁移（数据访问层）"""
    else:
        report += """1. 🔧 修复配置兼容性问题
2. ⚠️ 完成剩余文件的迁移
3. 🧪 重新测试配置系统
4. 📋 检查错误日志"""
    
    with open("config_migration_status_report.md", "w", encoding="utf-8") as f:
        f.write(report)
    
    print("   ✅ 状态报告已生成: config_migration_status_report.md")

def main():
    """主验证函数"""
    print("🧪 MythQuant 配置迁移验证")
    print("=" * 60)
    
    # 测试配置兼容性模块
    compat_success = test_config_compatibility()
    
    # 测试新配置系统
    new_config_success = test_new_config_system()
    
    # 检查已迁移的文件
    migration_status = check_migrated_files()
    
    # 生成状态报告
    generate_migration_status_report(compat_success, new_config_success, migration_status)
    
    # 总结
    print("\n" + "=" * 60)
    migrated_count = sum(1 for status in migration_status.values() if status == 'migrated')
    total_files = len(migration_status)
    
    if compat_success and new_config_success and migrated_count == total_files:
        print("🎉 配置迁移验证通过！")
        print(f"\n✅ 迁移成果:")
        print(f"   • 兼容性模块工作正常")
        print(f"   • 新配置系统可用")
        print(f"   • {migrated_count}/{total_files} 文件已迁移")
        print(f"\n🚀 可以开始下一阶段迁移！")
        return True
    else:
        print("⚠️ 配置迁移验证发现问题！")
        print(f"\n🔧 问题总结:")
        if not compat_success:
            print("   • 兼容性模块异常")
        if not new_config_success:
            print("   • 新配置系统异常")
        if migrated_count < total_files:
            print(f"   • 仍有 {total_files - migrated_count} 个文件需要迁移")
        print(f"\n💡 请查看状态报告了解详情")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
