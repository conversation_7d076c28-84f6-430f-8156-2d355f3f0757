# 提问模板库

## 📖 概述

本文档提供了各种场景下的标准化提问模板，帮助快速构建高质量的技术问题描述。

**最后更新**: 2025-08-08  
**配套文档**: `effective_questioning_guide.md`

---

## 🔧 技术问题模板

### T1: 代码错误诊断
```
【代码错误诊断】
错误场景: 在执行[具体功能]时
错误现象: [具体错误信息/异常行为]
预期行为: [应该发生的正确行为]
环境信息: [操作系统/版本/依赖等]
重现步骤: [详细的重现步骤]
已尝试方案: [已经尝试过的解决方法]
请求帮助: [具体需要什么帮助]
```

### T2: 性能问题分析
```
【性能问题分析】
性能现状: [当前性能指标，如响应时间、吞吐量]
性能目标: [期望达到的性能指标]
测试环境: [硬件配置、数据量、并发数]
瓶颈现象: [观察到的性能瓶颈表现]
监控数据: [CPU、内存、IO等监控数据]
业务影响: [对用户/业务的具体影响]
请求分析: [需要分析的具体方面]
```

### T3: 架构设计问题
```
【架构设计问题】
当前架构: [现有架构的主要组件和关系]
设计问题: [发现的架构问题或不合理之处]
业务需求: [相关的业务需求和约束]
技术约束: [技术栈、资源、时间等约束]
影响评估: [问题对系统的影响程度]
设计原则: [需要遵循的设计原则，如SOLID、DDD等]
请求建议: [需要什么样的架构建议]
```

---

## 🐛 调试问题模板

### D1: 系统异常调试
```
【系统异常调试】
异常时间: [异常发生的具体时间]
异常频率: [偶发/频繁/持续发生]
异常环境: [生产/测试/开发环境]
异常表现: [用户看到的异常现象]
日志信息: [相关的错误日志]
系统状态: [异常时的系统资源状态]
影响范围: [受影响的功能/用户范围]
紧急程度: [高/中/低]
请求支持: [需要什么调试支持]
```

### D2: 数据问题调试
```
【数据问题调试】
数据源: [数据来源和类型]
问题表现: [数据的异常表现]
数据范围: [受影响的数据范围]
时间窗口: [问题出现的时间范围]
数据样本: [异常数据的具体示例]
预期结果: [数据应该是什么样的]
业务影响: [对业务流程的影响]
数据依赖: [相关的数据依赖关系]
请求协助: [需要什么数据分析协助]
```

---

## 🏗️ 架构设计模板

### A1: 系统重构规划
```
【系统重构规划】
重构目标: [为什么要重构，要达到什么目标]
当前痛点: [现有系统的主要问题]
业务需求: [重构需要支持的业务需求]
技术债务: [需要解决的技术债务清单]
资源约束: [时间、人力、预算约束]
风险评估: [重构可能带来的风险]
兼容性要求: [向后兼容的要求]
请求规划: [需要什么样的重构规划]
```

### A2: 技术选型决策
```
【技术选型决策】
选型场景: [需要选型的具体场景]
候选方案: 
  - 方案A: [技术方案] - 优势: [具体优势] - 劣势: [具体劣势]
  - 方案B: [技术方案] - 优势: [具体优势] - 劣势: [具体劣势]
  - 方案C: [技术方案] - 优势: [具体优势] - 劣势: [具体劣势]
评估维度: [性能、可维护性、学习成本、社区支持等]
业务约束: [业务对技术选型的特殊要求]
团队能力: [团队的技术能力和经验]
请求建议: [需要什么样的选型建议]
```

---

## 📊 业务问题模板

### B1: 需求分析问题
```
【需求分析问题】
业务背景: [业务场景和背景信息]
用户角色: [涉及的用户角色和权限]
功能需求: [具体的功能需求描述]
非功能需求: [性能、安全、可用性等要求]
业务规则: [相关的业务规则和约束]
数据流向: [数据的流转和处理过程]
集成需求: [与其他系统的集成要求]
请求分析: [需要什么样的需求分析]
```

### B2: 用户体验问题
```
【用户体验问题】
用户场景: [用户使用的具体场景]
当前体验: [用户当前的使用体验]
问题反馈: [用户反馈的具体问题]
期望体验: [用户期望的理想体验]
使用数据: [相关的用户行为数据]
竞品对比: [竞品的相关功能对比]
技术限制: [当前技术实现的限制]
请求优化: [需要什么样的体验优化建议]
```

---

## 🔄 流程优化模板

### P1: 工作流程问题
```
【工作流程问题】
当前流程: [现有工作流程的详细步骤]
流程问题: [流程中存在的具体问题]
效率影响: [对工作效率的具体影响]
质量影响: [对工作质量的影响]
资源消耗: [流程消耗的时间和人力资源]
改进目标: [希望达到的流程改进目标]
约束条件: [流程改进需要考虑的约束]
请求优化: [需要什么样的流程优化建议]
```

### P2: 开发流程问题
```
【开发流程问题】
开发阶段: [问题出现在哪个开发阶段]
流程现状: [当前开发流程的具体情况]
痛点分析: [开发过程中的主要痛点]
质量问题: [代码质量、测试覆盖等问题]
协作问题: [团队协作中的问题]
工具链: [当前使用的开发工具链]
改进期望: [希望改进的具体方面]
请求建议: [需要什么样的流程改进建议]
```

---

## 🎯 问题定位模板

### L1: 问题根因分析
```
【问题根因分析】
问题现象: [观察到的具体问题现象]
发生频率: [问题发生的频率和规律]
影响范围: [问题影响的系统/用户范围]
时间线: [问题发生的时间线和关键节点]
环境因素: [可能相关的环境变化]
假设原因: [初步分析的可能原因]
验证方法: [如何验证假设原因]
请求分析: [需要什么样的根因分析]
```

### L2: 系统故障定位
```
【系统故障定位】
故障时间: [故障发生的准确时间]
故障现象: [系统故障的具体表现]
影响评估: [故障对业务的影响程度]
系统状态: [故障时的系统状态信息]
监控告警: [相关的监控告警信息]
操作记录: [故障前的系统操作记录]
恢复措施: [已采取的恢复措施]
请求定位: [需要什么样的故障定位支持]
```

---

## 📈 优化改进模板

### O1: 代码优化请求
```
【代码优化请求】
代码模块: [需要优化的具体代码模块]
当前问题: [代码存在的具体问题]
性能指标: [当前的性能表现]
优化目标: [希望达到的优化目标]
约束条件: [优化需要考虑的约束]
代码示例: [相关的代码片段]
测试场景: [需要验证的测试场景]
请求建议: [需要什么样的优化建议]
```

### O2: 系统扩展规划
```
【系统扩展规划】
扩展需求: [系统需要扩展的具体需求]
当前容量: [系统当前的处理能力]
预期增长: [业务增长的预期情况]
瓶颈分析: [当前系统的主要瓶颈]
扩展方案: [考虑的扩展方案选项]
资源预算: [可用的资源预算]
时间要求: [扩展完成的时间要求]
请求规划: [需要什么样的扩展规划]
```

---

## 🤝 协作沟通模板

### C1: 跨团队协作
```
【跨团队协作】
协作背景: [需要跨团队协作的背景]
涉及团队: [参与协作的各个团队]
协作目标: [协作要达成的目标]
各方职责: [各团队的职责分工]
协作难点: [协作过程中的难点]
沟通机制: [当前的沟通协调机制]
时间安排: [协作的时间安排]
请求支持: [需要什么样的协作支持]
```

### C2: 技术方案评审
```
【技术方案评审】
方案背景: [技术方案的背景和目标]
方案概述: [技术方案的核心内容]
技术细节: [关键的技术实现细节]
风险评估: [方案实施的潜在风险]
资源需求: [方案实施需要的资源]
时间计划: [方案实施的时间计划]
评审重点: [希望评审关注的重点]
请求反馈: [需要什么样的评审反馈]
```

---

## 📝 使用指南

### 模板选择原则
1. **问题类型匹配**: 根据问题的性质选择对应的模板
2. **详细程度适中**: 提供足够信息但避免冗余
3. **上下文完整**: 确保AI能够理解问题的完整背景
4. **目标明确**: 清楚地表达期望得到什么帮助

### 模板定制建议
1. **根据项目特点调整**: 结合具体项目的特点定制模板
2. **团队标准化**: 在团队内部统一使用相同的模板
3. **持续优化**: 根据使用效果不断优化模板内容
4. **版本管理**: 对模板进行版本管理和更新维护

### 质量检查要点
- [ ] 问题描述是否具体明确
- [ ] 背景信息是否充分
- [ ] 期望结果是否清晰
- [ ] 约束条件是否明确
- [ ] 请求行动是否具体

---

## 🔄 更新维护

### 模板更新原则
- 基于实际使用效果进行优化
- 根据新的问题类型扩展模板
- 结合团队反馈改进模板结构
- 保持模板的简洁性和实用性

### 贡献方式
- 提交新的模板建议
- 分享成功的使用案例
- 报告模板使用中的问题
- 参与模板的评审和优化

---

**💡 提示**: 这些模板是起点，不是终点。根据具体情况灵活调整，关键是要清晰、具体、结构化地表达问题。
