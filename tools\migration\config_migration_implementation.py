#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置迁移实施工具

自动化执行配置迁移，将现有代码从user_config迁移到config_compatibility
"""

import os
import re
import shutil
from pathlib import Path
from datetime import datetime

class ConfigMigrationImplementer:
    """配置迁移实施器"""
    
    def __init__(self):
        """初始化迁移实施器"""
        self.project_root = Path(__file__).parent
        self.backup_dir = self.project_root / "migration_backups"
        self.migration_log = []
        
        # 需要迁移的文件列表
        self.target_files = [
            "main.py",
            "func_Tdx.py",
            "func_Tdx1.py", 
            "func_Util.py",
            "gbbq_cache_solution.py"
        ]
        
        # 迁移模式
        self.migration_patterns = [
            # 导入语句迁移
            (r'import\s+user_config\s+as\s+ucfg', 'import config_compatibility as ucfg'),
            (r'import\s+user_config', 'import config_compatibility as user_config'),
            (r'from\s+user_config\s+import', 'from config_compatibility import'),
            
            # 配置访问迁移（保持原有访问方式）
            # 这些模式保持不变，因为config_compatibility提供了相同的接口
        ]
    
    def create_backup(self):
        """创建备份"""
        print("📦 创建迁移备份...")
        
        # 创建备份目录
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_path = self.backup_dir / f"config_migration_{timestamp}"
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 备份目标文件
        backed_up_files = []
        for file_name in self.target_files:
            source_file = self.project_root / file_name
            if source_file.exists():
                backup_file = backup_path / file_name
                shutil.copy2(source_file, backup_file)
                backed_up_files.append(file_name)
                print(f"   ✅ 备份: {file_name}")
            else:
                print(f"   ⚠️ 文件不存在: {file_name}")
        
        # 创建备份信息文件
        backup_info = f"""# 配置迁移备份信息

## 备份时间
{datetime.now().strftime("%Y-%m-%d %H:%M:%S")}

## 备份文件
{chr(10).join(f"- {f}" for f in backed_up_files)}

## 恢复方法
如需恢复，将此目录中的文件复制回项目根目录即可。

## 备份路径
{backup_path}
"""
        
        with open(backup_path / "backup_info.md", "w", encoding="utf-8") as f:
            f.write(backup_info)
        
        self.migration_log.append(f"✅ 创建备份: {backup_path}")
        return backup_path
    
    def analyze_file(self, file_path: Path):
        """分析文件中的配置使用情况"""
        if not file_path.exists():
            return None
        
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            try:
                with open(file_path, 'r', encoding='gbk') as f:
                    content = f.read()
            except:
                print(f"   ❌ 无法读取文件: {file_path}")
                return None
        
        analysis = {
            'file': file_path.name,
            'has_user_config_import': False,
            'import_patterns': [],
            'usage_patterns': [],
            'needs_migration': False
        }
        
        # 检查导入模式
        import_patterns = [
            r'import\s+user_config\s+as\s+\w+',
            r'import\s+user_config',
            r'from\s+user_config\s+import'
        ]
        
        for pattern in import_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                analysis['has_user_config_import'] = True
                analysis['import_patterns'].extend(matches)
                analysis['needs_migration'] = True
        
        # 检查使用模式
        usage_patterns = [
            r'user_config\.\w+',
            r'ucfg\.\w+'
        ]
        
        for pattern in usage_patterns:
            matches = re.findall(pattern, content)
            if matches:
                analysis['usage_patterns'].extend(matches)
        
        return analysis
    
    def migrate_file(self, file_path: Path):
        """迁移单个文件"""
        print(f"🔄 迁移文件: {file_path.name}")
        
        # 分析文件
        analysis = self.analyze_file(file_path)
        if not analysis or not analysis['needs_migration']:
            print(f"   ℹ️ 文件无需迁移: {file_path.name}")
            return True
        
        try:
            # 读取文件内容
            with open(file_path, 'r', encoding='utf-8') as f:
                content = f.read()
        except UnicodeDecodeError:
            with open(file_path, 'r', encoding='gbk') as f:
                content = f.read()
        
        # 应用迁移模式
        original_content = content
        changes_made = []
        
        for old_pattern, new_pattern in self.migration_patterns:
            matches = re.findall(old_pattern, content)
            if matches:
                content = re.sub(old_pattern, new_pattern, content)
                changes_made.append(f"{old_pattern} -> {new_pattern}")
        
        # 如果有变更，写入文件
        if content != original_content:
            try:
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"   ✅ 迁移完成: {file_path.name}")
                for change in changes_made:
                    print(f"      📝 {change}")
                
                self.migration_log.append(f"✅ 迁移文件: {file_path.name}")
                return True
                
            except Exception as e:
                print(f"   ❌ 写入文件失败: {e}")
                return False
        else:
            print(f"   ℹ️ 文件无需修改: {file_path.name}")
            return True
    
    def verify_migration(self):
        """验证迁移结果"""
        print("\n🧪 验证迁移结果...")
        
        verification_results = []
        
        for file_name in self.target_files:
            file_path = self.project_root / file_name
            if not file_path.exists():
                continue
            
            analysis = self.analyze_file(file_path)
            if analysis:
                result = {
                    'file': file_name,
                    'has_old_imports': analysis['has_user_config_import'],
                    'import_patterns': analysis['import_patterns'],
                    'status': 'needs_check' if analysis['has_user_config_import'] else 'ok'
                }
                verification_results.append(result)
                
                if result['status'] == 'ok':
                    print(f"   ✅ {file_name}: 迁移成功")
                else:
                    print(f"   ⚠️ {file_name}: 仍有旧导入语句")
                    for pattern in result['import_patterns']:
                        print(f"      📋 {pattern}")
        
        return verification_results
    
    def generate_migration_report(self, backup_path, verification_results):
        """生成迁移报告"""
        print("\n📄 生成迁移报告...")
        
        successful_migrations = sum(1 for r in verification_results if r['status'] == 'ok')
        total_files = len(verification_results)
        
        report = f"""# 配置迁移实施报告

## 迁移概况
- 迁移时间: {datetime.now().strftime("%Y-%m-%d %H:%M:%S")}
- 目标文件数: {len(self.target_files)}
- 实际处理文件数: {total_files}
- 成功迁移文件数: {successful_migrations}
- 迁移成功率: {successful_migrations/total_files*100:.1f}%

## 备份信息
- 备份路径: {backup_path}
- 备份文件数: {len(self.target_files)}

## 迁移详情
| 文件名 | 状态 | 说明 |
|--------|------|------|
"""
        
        for result in verification_results:
            status_icon = "✅" if result['status'] == 'ok' else "⚠️"
            description = "迁移成功" if result['status'] == 'ok' else f"仍有旧导入: {', '.join(result['import_patterns'])}"
            report += f"| {result['file']} | {status_icon} | {description} |\n"
        
        report += f"""
## 迁移日志
{chr(10).join(f"- {log}" for log in self.migration_log)}

## 下一步建议
{'✅ 迁移完成，可以开始测试功能' if successful_migrations == total_files else '⚠️ 需要手动检查未完全迁移的文件'}

## 测试建议
1. 运行主程序，检查是否正常启动
2. 测试核心功能，验证配置读取正确
3. 检查日志，确认无配置相关错误
4. 如有问题，可从备份目录恢复文件

## 回退方法
如需回退，执行以下命令：
```bash
# 从备份目录恢复文件
copy "{backup_path}\\*.py" .
```
"""
        
        report_file = self.project_root / "config_migration_implementation_report.md"
        with open(report_file, "w", encoding="utf-8") as f:
            f.write(report)
        
        print(f"   ✅ 报告已生成: {report_file}")
        return report_file
    
    def run_migration(self):
        """执行完整的迁移流程"""
        print("🚀 开始配置迁移实施")
        print("=" * 60)
        
        # 步骤1：创建备份
        backup_path = self.create_backup()
        
        # 步骤2：分析目标文件
        print(f"\n🔍 分析目标文件...")
        files_to_migrate = []
        for file_name in self.target_files:
            file_path = self.project_root / file_name
            analysis = self.analyze_file(file_path)
            if analysis and analysis['needs_migration']:
                files_to_migrate.append(file_path)
                print(f"   📋 需要迁移: {file_name}")
                for pattern in analysis['import_patterns']:
                    print(f"      🔍 发现: {pattern}")
            elif analysis:
                print(f"   ✅ 无需迁移: {file_name}")
        
        if not files_to_migrate:
            print("\n🎉 所有文件都已是最新状态，无需迁移！")
            return True
        
        # 步骤3：执行迁移
        print(f"\n🔄 执行迁移 ({len(files_to_migrate)} 个文件)...")
        migration_success = True
        for file_path in files_to_migrate:
            if not self.migrate_file(file_path):
                migration_success = False
        
        # 步骤4：验证迁移
        verification_results = self.verify_migration()
        
        # 步骤5：生成报告
        report_file = self.generate_migration_report(backup_path, verification_results)
        
        # 总结
        print("\n" + "=" * 60)
        if migration_success:
            print("🎉 配置迁移实施完成！")
            print(f"\n📊 迁移统计:")
            print(f"   处理文件: {len(verification_results)} 个")
            print(f"   成功迁移: {sum(1 for r in verification_results if r['status'] == 'ok')} 个")
            print(f"   备份路径: {backup_path}")
            print(f"   迁移报告: {report_file}")
            
            print(f"\n🧪 下一步测试:")
            print("1. 运行主程序测试基本功能")
            print("2. 检查配置读取是否正常")
            print("3. 验证输出结果一致性")
            
            return True
        else:
            print("⚠️ 配置迁移实施遇到问题！")
            print(f"\n🔧 问题排查:")
            print("1. 检查迁移报告了解详情")
            print("2. 手动检查未成功迁移的文件")
            print("3. 如需回退，从备份目录恢复")
            
            return False

def main():
    """主函数"""
    print("🎯 MythQuant 配置迁移实施工具")
    print("=" * 60)
    
    # 创建迁移实施器
    migrator = ConfigMigrationImplementer()
    
    # 执行迁移
    success = migrator.run_migration()
    
    return success

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
