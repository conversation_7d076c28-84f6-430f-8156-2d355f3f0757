#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际执行测试

在测试环境中实际运行主程序的核心功能，验证是否有运行时错误

位置: test_environments/integration_tests/configs/
作者: AI Assistant
创建时间: 2025-07-31
"""

import sys
import os
import subprocess
import time
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_completion
)


def test_main_program_execution():
    """测试主程序执行"""
    print_step("主程序执行测试", 1, 3)
    
    try:
        # 切换到项目根目录
        original_cwd = os.getcwd()
        os.chdir(project_root)
        
        try:
            print_info("启动主程序...", level=2)
            
            # 运行主程序，设置较短的超时时间，指定UTF-8编码
            process = subprocess.Popen(
                [sys.executable, 'main.py'],
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                encoding='utf-8',
                errors='replace',  # 遇到编码错误时替换为?
                cwd=project_root
            )
            
            # 等待程序执行，最多等待60秒
            try:
                stdout, stderr = process.communicate(timeout=60)
                return_code = process.returncode
                
                print_info(f"程序执行完成，返回码: {return_code}", level=2)
                
                # 检查是否有严重错误
                if "Traceback" in stderr or "Exception" in stderr:
                    print_error("发现运行时异常:", level=2)
                    print_error(stderr[:500], level=2)  # 只显示前500字符
                    return False
                
                # 检查是否有关键错误信息
                error_keywords = ["❌", "ERROR", "Failed", "失败"]
                has_errors = any(keyword in stdout for keyword in error_keywords)
                
                if has_errors:
                    print_warning("程序输出中包含错误信息", level=2)
                    # 但这不一定是致命错误，可能是业务逻辑的正常错误处理
                
                # 检查是否有成功的输出格式
                success_indicators = ["🚀", "✅", "📊", "🎉"]
                has_success_output = any(indicator in stdout for indicator in success_indicators)
                
                if has_success_output:
                    print_result("程序输出格式正常", True, level=2)
                else:
                    print_warning("程序输出格式可能异常", level=2)
                
                return True
                
            except subprocess.TimeoutExpired:
                print_warning("程序执行超时，终止进程", level=2)
                process.kill()
                return True  # 超时不算错误，可能是正常的长时间运行
                
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        print_error(f"主程序执行测试失败: {e}", level=2)
        return False


def test_structured_downloader_execution():
    """测试结构化下载器执行"""
    print_step("结构化下载器执行测试", 2, 3)
    
    try:
        original_cwd = os.getcwd()
        os.chdir(project_root)
        
        try:
            from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
            
            # 创建下载器实例
            downloader = StructuredInternetMinuteDownloader()
            print_result("下载器实例创建成功", True, level=2)
            
            # 测试四步流程方法是否存在
            required_methods = [
                '_step1_smart_file_selection',
                '_step2_incremental_feasibility_check', 
                '_step3_missing_data_audit_and_repair',
                '_step4_incremental_data_download',
                '_execute_four_step_process'
            ]
            
            for method_name in required_methods:
                if hasattr(downloader, method_name):
                    print_result(f"方法 {method_name} 存在", True, level=2)
                else:
                    print_error(f"方法 {method_name} 缺失", level=2)
                    return False
            
            return True
            
        finally:
            os.chdir(original_cwd)
            
    except Exception as e:
        print_error(f"结构化下载器执行测试失败: {e}", level=2)
        return False


def test_output_formatter_execution():
    """测试输出格式器执行"""
    print_step("输出格式器执行测试", 3, 3)
    
    try:
        from utils.structured_output_formatter import StructuredOutputFormatter
        
        # 创建格式器实例
        formatter = StructuredOutputFormatter()
        print_result("格式器实例创建成功", True, level=2)
        
        # 测试各种输出方法
        test_methods = [
            ('print_banner', lambda: formatter.print_banner("测试标题", "测试副标题")),
            ('print_main_process', lambda: formatter.print_main_process("测试主流程")),
            ('print_sub_process', lambda: formatter.print_sub_process("测试子流程", 1, 2)),
            ('print_step', lambda: formatter.print_step("测试步骤", 1, 4)),
            ('print_result', lambda: formatter.print_result("测试结果", True)),
            ('print_info', lambda: formatter.print_info("测试信息")),
            ('print_warning', lambda: formatter.print_warning("测试警告")),
            ('print_error', lambda: formatter.print_error("测试错误")),
        ]
        
        for method_name, test_func in test_methods:
            try:
                test_func()
                print_result(f"方法 {method_name} 执行成功", True, level=2)
            except Exception as e:
                print_error(f"方法 {method_name} 执行失败: {e}", level=2)
                return False
        
        return True
        
    except Exception as e:
        print_error(f"输出格式器执行测试失败: {e}", level=2)
        return False


def main():
    """主函数"""
    print_banner("实际执行测试", "在测试环境中验证核心功能的实际运行")
    
    print_info(f"项目根目录: {project_root}")
    print_info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print_main_process("执行实际运行测试")
    
    # 执行测试
    tests = [
        ("主程序执行", test_main_program_execution),
        ("结构化下载器执行", test_structured_downloader_execution),
        ("输出格式器执行", test_output_formatter_execution)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print_error(f"测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    total_tests = len(tests)
    pass_rate = passed_tests / total_tests
    
    print_main_process("实际执行测试结果")
    
    # 显示详细结果
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print_result(f"{test_name}: {status}", result, level=1)
    
    # 显示统计信息
    stats = {
        "总测试数": total_tests,
        "通过测试数": passed_tests,
        "失败测试数": total_tests - passed_tests,
        "通过率": f"{pass_rate:.1%}"
    }
    
    from utils.structured_output_formatter import print_stats_table
    print_stats_table("执行测试统计", stats)
    
    # 判断整体结果
    overall_success = pass_rate >= 0.8
    
    completion_message = "实际执行测试通过！" if overall_success else "实际执行测试发现问题"
    print_completion(completion_message, overall_success, stats)
    
    return 0 if overall_success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
