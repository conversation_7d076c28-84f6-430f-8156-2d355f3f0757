# MythQuant项目AI行为规则

## 智能文件定位规则
当用户提及以下功能时，自动定位到对应文件：
1. **显示相关功能** → `ui/display.py` 或 `ui/progress.py`
2. **文件IO操作** → `file_io/` 目录下对应模块
3. **配置修改** → `user_config.py`
4. **核心算法** → `main_v20230219_optimized.py` (第2120-3154行待拆分)
5. **日志功能** → `core/logging_service.py`
6. **工具函数** → `utils/helpers.py`
7. **数据读取** → `file_io/excel_reader.py` 或 `file_io/data_formatter.py`
8. **结果输出** → `file_io/file_writer.py`

## 代码修改标准工作流
1. **理解需求**：分析用户描述，识别涉及的功能模块
2. **依赖检查**：确认修改不会破坏现有功能
3. **创建测试**：编写测试脚本验证修改效果
4. **备份建议**：如修改核心文件，建议先备份

## 项目结构理解
- 项目已完成3个阶段的模块化拆分
- 当前主文件4228行，原始4833行
- 根目录已从71个Python文件清理至19个核心文件
- 52个文件按功能分类存档在backup_files/目录

## 自主文档查找策略
- 遇到技术问题时主动搜索prompt_templates中的相关文档
- 优先查找：pitfall_guide.md (避坑指南)、best_practices.md (最佳实践)
- 引用文档时提供具体文件名和章节

## 复制检测和防护
进行代码修改前，检查：
1. 是否已有类似功能实现
2. 新功能是否会与现有功能冲突
3. 是否可以复用现有组件
4. 修改是否符合项目架构设计

## 用户交互优化
- 主动识别用户可能的文件定位错误
- 提供具体的修改建议而非抽象指导
- 解释技术决策的原因
- 在不确定时主动询问确认 