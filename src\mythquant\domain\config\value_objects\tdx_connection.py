#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDX连接值对象

表示TDX数据源的连接配置
"""

from dataclasses import dataclass
from typing import Optional
import re
import os


@dataclass(frozen=True)
class TdxConnection:
    """TDX连接值对象"""
    
    path: str
    ip: str
    port: int
    test_path: Optional[str] = None
    min_path: Optional[str] = None
    day_path: Optional[str] = None
    
    def __post_init__(self):
        """初始化后验证"""
        if not self.is_valid():
            raise ValueError(f"无效的TDX连接配置: path={self.path}, ip={self.ip}, port={self.port}")
    
    def is_valid(self) -> bool:
        """验证连接配置的有效性"""
        return (
            self._is_valid_path(self.path) and
            self._is_valid_ip(self.ip) and
            self._is_valid_port(self.port)
        )
    
    def _is_valid_path(self, path: str) -> bool:
        """验证路径有效性"""
        if not path:
            return False
        
        # 检查路径格式（支持Windows和Unix路径）
        if not re.match(r'^[A-Za-z]:[/\\]|^[/\\]|^[A-Za-z]:', path):
            return False
        
        # 检查路径是否存在（可选，因为路径可能在其他机器上）
        return True
    
    def _is_valid_ip(self, ip: str) -> bool:
        """验证IP地址有效性"""
        if not ip:
            return False
        
        # 简单的IP地址格式验证
        pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        if not re.match(pattern, ip):
            return False
        
        # 验证每个数字段在0-255范围内
        parts = ip.split('.')
        return all(0 <= int(part) <= 255 for part in parts)
    
    def _is_valid_port(self, port: int) -> bool:
        """验证端口号有效性"""
        return 1024 <= port <= 65535
    
    def get_full_path(self, sub_path: str = "") -> str:
        """获取完整路径"""
        if not sub_path:
            return self.path

        # 处理路径分隔符
        separator = '\\' if '\\' in self.path else '/'
        # 修复f-string中的反斜杠问题
        path_separators = '/\\'
        clean_path = self.path.rstrip(path_separators)
        clean_sub_path = sub_path.lstrip(path_separators)
        return f"{clean_path}{separator}{clean_sub_path}"
    
    def get_connection_string(self) -> str:
        """获取连接字符串"""
        return f"{self.ip}:{self.port}"
    
    def __str__(self) -> str:
        return f"TdxConnection(path='{self.path}', ip='{self.ip}', port={self.port})"
