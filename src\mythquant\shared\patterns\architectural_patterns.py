#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
架构模式实现

融合Clean Architecture、DDD、六边形架构等先进模式的具体实现
"""

from abc import ABC, abstractmethod
from typing import TypeVar, Generic, List, Dict, Any, Optional
from dataclasses import dataclass
from datetime import datetime
from enum import Enum
import asyncio
from functools import wraps


# =============================================================================
# Clean Architecture 基础组件
# =============================================================================

class Entity(ABC):
    """实体基类 - Clean Architecture"""
    
    def __init__(self, entity_id: str):
        self._id = entity_id
        self._domain_events: List['DomainEvent'] = []
    
    @property
    def id(self) -> str:
        return self._id
    
    @property
    def domain_events(self) -> List['DomainEvent']:
        return self._domain_events.copy()
    
    def add_domain_event(self, event: 'DomainEvent'):
        """添加领域事件"""
        self._domain_events.append(event)
    
    def clear_domain_events(self):
        """清除领域事件"""
        self._domain_events.clear()


class ValueObject(ABC):
    """值对象基类 - DDD"""
    
    def __eq__(self, other):
        if not isinstance(other, self.__class__):
            return False
        return self.__dict__ == other.__dict__
    
    def __hash__(self):
        return hash(tuple(sorted(self.__dict__.items())))


class AggregateRoot(Entity):
    """聚合根基类 - DDD"""
    
    def __init__(self, entity_id: str):
        super().__init__(entity_id)
        self._version = 0
    
    @property
    def version(self) -> int:
        return self._version
    
    def increment_version(self):
        """递增版本号"""
        self._version += 1


# =============================================================================
# 领域事件系统
# =============================================================================

@dataclass
class DomainEvent:
    """领域事件基类"""
    event_id: str
    aggregate_id: str
    event_type: str
    occurred_at: datetime
    version: int
    data: Dict[str, Any]


class DomainEventPublisher:
    """领域事件发布器"""
    
    def __init__(self):
        self._handlers: Dict[str, List[callable]] = {}
    
    def subscribe(self, event_type: str, handler: callable):
        """订阅事件"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)
    
    async def publish(self, event: DomainEvent):
        """发布事件"""
        handlers = self._handlers.get(event.event_type, [])
        tasks = [handler(event) for handler in handlers]
        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)


# =============================================================================
# 六边形架构 - 端口和适配器
# =============================================================================

T = TypeVar('T')
R = TypeVar('R')

class Port(ABC, Generic[T, R]):
    """端口接口 - 六边形架构"""
    
    @abstractmethod
    async def execute(self, request: T) -> R:
        """执行操作"""
        pass


class InboundPort(Port[T, R]):
    """入站端口 - 应用程序接收外部请求"""
    pass


class OutboundPort(Port[T, R]):
    """出站端口 - 应用程序调用外部服务"""
    pass


class Adapter(ABC):
    """适配器基类"""
    
    def __init__(self, port: Port):
        self._port = port
    
    @abstractmethod
    async def adapt(self, external_request: Any) -> Any:
        """适配外部请求到内部格式"""
        pass


# =============================================================================
# CQRS 模式实现
# =============================================================================

class Command:
    """命令基类 - CQRS"""
    pass


class Query:
    """查询基类 - CQRS"""
    pass


class CommandHandler(ABC, Generic[T]):
    """命令处理器"""
    
    @abstractmethod
    async def handle(self, command: T) -> None:
        """处理命令"""
        pass


class QueryHandler(ABC, Generic[T, R]):
    """查询处理器"""
    
    @abstractmethod
    async def handle(self, query: T) -> R:
        """处理查询"""
        pass


class CommandBus:
    """命令总线"""
    
    def __init__(self):
        self._handlers: Dict[type, CommandHandler] = {}
    
    def register(self, command_type: type, handler: CommandHandler):
        """注册命令处理器"""
        self._handlers[command_type] = handler
    
    async def dispatch(self, command: Command):
        """分发命令"""
        handler = self._handlers.get(type(command))
        if not handler:
            raise ValueError(f"No handler registered for {type(command)}")
        await handler.handle(command)


class QueryBus:
    """查询总线"""
    
    def __init__(self):
        self._handlers: Dict[type, QueryHandler] = {}
    
    def register(self, query_type: type, handler: QueryHandler):
        """注册查询处理器"""
        self._handlers[query_type] = handler
    
    async def dispatch(self, query: Query) -> Any:
        """分发查询"""
        handler = self._handlers.get(type(query))
        if not handler:
            raise ValueError(f"No handler registered for {type(query)}")
        return await handler.handle(query)


# =============================================================================
# 仓储模式
# =============================================================================

class Repository(ABC, Generic[T]):
    """仓储接口"""
    
    @abstractmethod
    async def save(self, entity: T) -> T:
        """保存实体"""
        pass
    
    @abstractmethod
    async def find_by_id(self, entity_id: str) -> Optional[T]:
        """根据ID查找实体"""
        pass
    
    @abstractmethod
    async def find_all(self) -> List[T]:
        """查找所有实体"""
        pass
    
    @abstractmethod
    async def delete(self, entity_id: str) -> bool:
        """删除实体"""
        pass


# =============================================================================
# 规约模式
# =============================================================================

class Specification(ABC, Generic[T]):
    """规约模式基类"""
    
    @abstractmethod
    def is_satisfied_by(self, candidate: T) -> bool:
        """检查候选对象是否满足规约"""
        pass
    
    def and_specification(self, other: 'Specification[T]') -> 'Specification[T]':
        """AND组合规约"""
        return AndSpecification(self, other)
    
    def or_specification(self, other: 'Specification[T]') -> 'Specification[T]':
        """OR组合规约"""
        return OrSpecification(self, other)
    
    def not_specification(self) -> 'Specification[T]':
        """NOT规约"""
        return NotSpecification(self)


class AndSpecification(Specification[T]):
    """AND组合规约"""
    
    def __init__(self, left: Specification[T], right: Specification[T]):
        self.left = left
        self.right = right
    
    def is_satisfied_by(self, candidate: T) -> bool:
        return (self.left.is_satisfied_by(candidate) and 
                self.right.is_satisfied_by(candidate))


class OrSpecification(Specification[T]):
    """OR组合规约"""
    
    def __init__(self, left: Specification[T], right: Specification[T]):
        self.left = left
        self.right = right
    
    def is_satisfied_by(self, candidate: T) -> bool:
        return (self.left.is_satisfied_by(candidate) or 
                self.right.is_satisfied_by(candidate))


class NotSpecification(Specification[T]):
    """NOT规约"""
    
    def __init__(self, specification: Specification[T]):
        self.specification = specification
    
    def is_satisfied_by(self, candidate: T) -> bool:
        return not self.specification.is_satisfied_by(candidate)


# =============================================================================
# 装饰器模式 - 横切关注点
# =============================================================================

def transactional(func):
    """事务装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 事务开始
        try:
            result = await func(*args, **kwargs)
            # 提交事务
            return result
        except Exception as e:
            # 回滚事务
            raise e
    return wrapper


def logged(func):
    """日志装饰器"""
    @wraps(func)
    async def wrapper(*args, **kwargs):
        # 记录开始日志
        try:
            result = await func(*args, **kwargs)
            # 记录成功日志
            return result
        except Exception as e:
            # 记录错误日志
            raise e
    return wrapper


def cached(ttl: int = 300):
    """缓存装饰器"""
    def decorator(func):
        @wraps(func)
        async def wrapper(*args, **kwargs):
            # 检查缓存
            # 如果缓存命中，返回缓存结果
            # 否则执行函数并缓存结果
            return await func(*args, **kwargs)
        return wrapper
    return decorator


# =============================================================================
# 工厂模式
# =============================================================================

class Factory(ABC, Generic[T]):
    """工厂接口"""
    
    @abstractmethod
    def create(self, **kwargs) -> T:
        """创建对象"""
        pass


class AbstractFactory(ABC):
    """抽象工厂"""
    
    @abstractmethod
    def create_repository(self) -> Repository:
        """创建仓储"""
        pass
    
    @abstractmethod
    def create_service(self) -> Any:
        """创建服务"""
        pass


# =============================================================================
# 策略模式
# =============================================================================

class Strategy(ABC, Generic[T, R]):
    """策略接口"""
    
    @abstractmethod
    async def execute(self, context: T) -> R:
        """执行策略"""
        pass


class Context(Generic[T, R]):
    """策略上下文"""
    
    def __init__(self, strategy: Strategy[T, R]):
        self._strategy = strategy
    
    def set_strategy(self, strategy: Strategy[T, R]):
        """设置策略"""
        self._strategy = strategy
    
    async def execute_strategy(self, data: T) -> R:
        """执行策略"""
        return await self._strategy.execute(data)


# =============================================================================
# 观察者模式
# =============================================================================

class Observer(ABC):
    """观察者接口"""
    
    @abstractmethod
    async def update(self, event: Any):
        """更新通知"""
        pass


class Subject:
    """主题/被观察者"""
    
    def __init__(self):
        self._observers: List[Observer] = []
    
    def attach(self, observer: Observer):
        """添加观察者"""
        self._observers.append(observer)
    
    def detach(self, observer: Observer):
        """移除观察者"""
        self._observers.remove(observer)
    
    async def notify(self, event: Any):
        """通知所有观察者"""
        tasks = [observer.update(event) for observer in self._observers]
        await asyncio.gather(*tasks, return_exceptions=True)


# =============================================================================
# 建造者模式
# =============================================================================

class Builder(ABC, Generic[T]):
    """建造者接口"""

    @abstractmethod
    def reset(self) -> 'Builder[T]':
        """重置建造者"""
        pass

    @abstractmethod
    def build(self) -> T:
        """构建对象"""
        pass


class Director(Generic[T]):
    """指挥者"""

    def __init__(self, builder: Builder[T]):
        self._builder = builder

    def construct(self) -> T:
        """构建对象"""
        return (self._builder
                .reset()
                .build())


# =============================================================================
# 企业级设计模式扩展
# =============================================================================

class Singleton(type):
    """单例元类"""
    _instances = {}

    def __call__(cls, *args, **kwargs):
        if cls not in cls._instances:
            cls._instances[cls] = super().__call__(*args, **kwargs)
        return cls._instances[cls]


class ServiceLocator:
    """服务定位器模式"""

    def __init__(self):
        self._services: Dict[type, Any] = {}
        self._factories: Dict[type, callable] = {}

    def register_service(self, service_type: type, service_instance: Any):
        """注册服务实例"""
        self._services[service_type] = service_instance

    def register_factory(self, service_type: type, factory: callable):
        """注册服务工厂"""
        self._factories[service_type] = factory

    def get_service(self, service_type: type) -> Any:
        """获取服务"""
        if service_type in self._services:
            return self._services[service_type]

        if service_type in self._factories:
            service = self._factories[service_type]()
            self._services[service_type] = service
            return service

        raise ValueError(f"Service {service_type} not registered")


class DependencyInjector:
    """依赖注入器"""

    def __init__(self):
        self._bindings: Dict[type, type] = {}
        self._instances: Dict[type, Any] = {}
        self._singletons: set = set()

    def bind(self, interface: type, implementation: type, singleton: bool = False):
        """绑定接口到实现"""
        self._bindings[interface] = implementation
        if singleton:
            self._singletons.add(interface)

    def bind_instance(self, interface: type, instance: Any):
        """绑定接口到实例"""
        self._instances[interface] = instance

    def get(self, interface: type) -> Any:
        """获取实例"""
        # 如果有直接绑定的实例
        if interface in self._instances:
            return self._instances[interface]

        # 如果有绑定的实现类
        if interface in self._bindings:
            implementation = self._bindings[interface]

            # 检查是否为单例
            if interface in self._singletons:
                if interface not in self._instances:
                    self._instances[interface] = self._create_instance(implementation)
                return self._instances[interface]
            else:
                return self._create_instance(implementation)

        raise ValueError(f"No binding found for {interface}")

    def _create_instance(self, cls: type) -> Any:
        """创建实例（支持构造函数注入）"""
        import inspect

        sig = inspect.signature(cls.__init__)
        kwargs = {}

        for param_name, param in sig.parameters.items():
            if param_name == 'self':
                continue

            if param.annotation and param.annotation != inspect.Parameter.empty:
                kwargs[param_name] = self.get(param.annotation)

        return cls(**kwargs)


class EventBus:
    """事件总线"""

    def __init__(self):
        self._handlers: Dict[type, List[callable]] = {}
        self._middleware: List[callable] = []

    def subscribe(self, event_type: type, handler: callable):
        """订阅事件"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        self._handlers[event_type].append(handler)

    def unsubscribe(self, event_type: type, handler: callable):
        """取消订阅"""
        if event_type in self._handlers:
            self._handlers[event_type].remove(handler)

    def add_middleware(self, middleware: callable):
        """添加中间件"""
        self._middleware.append(middleware)

    async def publish(self, event: Any):
        """发布事件"""
        event_type = type(event)

        # 应用中间件
        for middleware in self._middleware:
            event = await middleware(event) if asyncio.iscoroutinefunction(middleware) else middleware(event)
            if event is None:
                return

        # 调用处理器
        handlers = self._handlers.get(event_type, [])
        tasks = []

        for handler in handlers:
            if asyncio.iscoroutinefunction(handler):
                tasks.append(handler(event))
            else:
                tasks.append(asyncio.create_task(asyncio.coroutine(lambda: handler(event))()))

        if tasks:
            await asyncio.gather(*tasks, return_exceptions=True)


class Pipeline(Generic[T]):
    """管道模式"""

    def __init__(self):
        self._stages: List[callable] = []

    def add_stage(self, stage: callable) -> 'Pipeline[T]':
        """添加管道阶段"""
        self._stages.append(stage)
        return self

    async def execute(self, input_data: T) -> T:
        """执行管道"""
        result = input_data

        for stage in self._stages:
            if asyncio.iscoroutinefunction(stage):
                result = await stage(result)
            else:
                result = stage(result)

        return result


class ChainOfResponsibility(ABC, Generic[T]):
    """责任链模式"""

    def __init__(self):
        self._next_handler: Optional['ChainOfResponsibility[T]'] = None

    def set_next(self, handler: 'ChainOfResponsibility[T]') -> 'ChainOfResponsibility[T]':
        """设置下一个处理器"""
        self._next_handler = handler
        return handler

    async def handle(self, request: T) -> Optional[Any]:
        """处理请求"""
        result = await self._handle(request)

        if result is None and self._next_handler:
            return await self._next_handler.handle(request)

        return result

    @abstractmethod
    async def _handle(self, request: T) -> Optional[Any]:
        """具体处理逻辑"""
        pass


class Mediator(ABC):
    """中介者模式"""

    @abstractmethod
    async def notify(self, sender: Any, event: str, data: Any = None):
        """通知中介者"""
        pass


class Component(ABC):
    """组件基类"""

    def __init__(self, mediator: Mediator):
        self._mediator = mediator

    async def notify_mediator(self, event: str, data: Any = None):
        """通知中介者"""
        await self._mediator.notify(self, event, data)


class State(ABC, Generic[T]):
    """状态模式"""

    @abstractmethod
    async def handle(self, context: T) -> Optional['State[T]']:
        """处理状态"""
        pass


class StateMachine(Generic[T]):
    """状态机"""

    def __init__(self, initial_state: State[T]):
        self._current_state = initial_state
        self._context: Optional[T] = None

    def set_context(self, context: T):
        """设置上下文"""
        self._context = context

    async def transition(self) -> bool:
        """状态转换"""
        if self._context is None:
            return False

        next_state = await self._current_state.handle(self._context)

        if next_state:
            self._current_state = next_state
            return True

        return False

    @property
    def current_state(self) -> State[T]:
        """当前状态"""
        return self._current_state


class Memento(Generic[T]):
    """备忘录模式"""

    def __init__(self, state: T):
        self._state = state
        self._timestamp = datetime.now()

    @property
    def state(self) -> T:
        return self._state

    @property
    def timestamp(self) -> datetime:
        return self._timestamp


class Originator(Generic[T]):
    """发起人"""

    def __init__(self, initial_state: T):
        self._state = initial_state

    @property
    def state(self) -> T:
        return self._state

    @state.setter
    def state(self, state: T):
        self._state = state

    def create_memento(self) -> Memento[T]:
        """创建备忘录"""
        return Memento(self._state)

    def restore_from_memento(self, memento: Memento[T]):
        """从备忘录恢复"""
        self._state = memento.state


class Caretaker(Generic[T]):
    """管理者"""

    def __init__(self):
        self._mementos: List[Memento[T]] = []

    def save_memento(self, memento: Memento[T]):
        """保存备忘录"""
        self._mementos.append(memento)

    def get_memento(self, index: int) -> Optional[Memento[T]]:
        """获取备忘录"""
        if 0 <= index < len(self._mementos):
            return self._mementos[index]
        return None

    def get_latest_memento(self) -> Optional[Memento[T]]:
        """获取最新备忘录"""
        return self._mementos[-1] if self._mementos else None
