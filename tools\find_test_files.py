#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
查找测试文件
"""

import os
import glob

def find_test_files():
    """查找测试文件"""
    print("🔍 查找测试文件")
    print("=" * 50)
    
    # 可能的测试目录路径
    test_paths = [
        "H:/MPV1.17/T0002/signals/TestCase/01",
        "H:\\MPV1.17\\T0002\\signals\\TestCase\\01",
        "TestCase/01",
        "signals/TestCase/01",
        "."
    ]
    
    found_files = []
    
    for test_path in test_paths:
        if os.path.exists(test_path):
            print(f"✅ 找到目录: {test_path}")
            
            # 查找test_开头的文件
            pattern = os.path.join(test_path, "test_*.txt")
            files = glob.glob(pattern)
            
            if files:
                print(f"📁 找到 {len(files)} 个测试文件:")
                for file in files:
                    print(f"   - {os.path.basename(file)}")
                    found_files.append(file)
            else:
                print(f"   ❌ 该目录下无test_开头的文件")
        else:
            print(f"❌ 目录不存在: {test_path}")
    
    # 全局搜索test_开头的1min文件
    print(f"\n🔍 全局搜索test_1min文件...")
    global_patterns = [
        "**/test_1min_*.txt",
        "test_1min_*.txt",
        "H:/MPV1.17/T0002/**/test_1min_*.txt"
    ]
    
    for pattern in global_patterns:
        try:
            files = glob.glob(pattern, recursive=True)
            if files:
                print(f"📁 模式 {pattern} 找到 {len(files)} 个文件:")
                for file in files:
                    print(f"   - {file}")
                    found_files.append(file)
        except Exception as e:
            print(f"   ❌ 搜索失败: {e}")
    
    return list(set(found_files))  # 去重

if __name__ == "__main__":
    files = find_test_files()
    
    if files:
        print(f"\n📊 总共找到 {len(files)} 个测试文件")
        
        # 分析第一个文件
        if files:
            test_file = files[0]
            print(f"\n🔍 分析测试文件: {os.path.basename(test_file)}")
            
            try:
                with open(test_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                print(f"📊 文件行数: {len(lines)}")
                
                # 显示前几行
                print(f"📋 文件内容预览:")
                for i, line in enumerate(lines[:5]):
                    print(f"   {i+1}: {line.strip()}")
                
                # 显示最后几行
                if len(lines) > 10:
                    print(f"   ...")
                    for i, line in enumerate(lines[-3:], len(lines)-2):
                        print(f"   {i}: {line.strip()}")
                
                # 分析时间范围
                data_lines = [line for line in lines if not line.startswith('股票编码') and line.strip()]
                if data_lines:
                    first_line = data_lines[0].split('|')
                    last_line = data_lines[-1].split('|')
                    
                    if len(first_line) >= 2 and len(last_line) >= 2:
                        first_time = first_line[1]
                        last_time = last_line[1]
                        print(f"📅 时间范围: {first_time} ~ {last_time}")
                        
                        # 计算从7月4日到现在的交易日
                        from datetime import datetime
                        
                        # 提取日期部分
                        if len(last_time) >= 8:
                            last_date = last_time[:8]  # YYYYMMDD
                            print(f"📅 文件最后日期: {last_date}")
                            
                            # 计算从20250704到last_date的交易日
                            start_date = "20250704"
                            if last_date >= start_date:
                                # 简单计算（实际应该考虑节假日）
                                start_dt = datetime.strptime(start_date, '%Y%m%d')
                                end_dt = datetime.strptime(last_date, '%Y%m%d')
                                days_diff = (end_dt - start_dt).days + 1
                                
                                # 估算交易日（排除周末，约70%）
                                estimated_trading_days = int(days_diff * 0.7)
                                estimated_minutes = estimated_trading_days * 240
                                
                                print(f"📊 从{start_date}到{last_date}:")
                                print(f"   总天数: {days_diff}")
                                print(f"   估算交易日: {estimated_trading_days}")
                                print(f"   估算分钟数: {estimated_minutes}")
                                print(f"   实际数据行数: {len(data_lines)}")
                
            except Exception as e:
                print(f"❌ 文件分析失败: {e}")
    else:
        print(f"\n❌ 未找到任何测试文件")
        print(f"💡 请确认测试环境是否正确设置")
