#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速测试上限修复效果
"""

import sys
sys.path.append('.')

def test_fix():
    print("🔧 测试上限修复效果")
    print("=" * 50)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        downloader = PytdxDownloader()
        
        # 测试20250301
        count_0301 = downloader._calculate_smart_data_count('20250301', '1min')
        print(f"📋 20250301 (4个月前): {count_0301}条")
        
        # 测试20250701  
        count_0701 = downloader._calculate_smart_data_count('20250701', '1min')
        print(f"📋 20250701 (本月): {count_0701}条")
        
        print(f"\n🎯 修复验证:")
        if count_0301 >= 30000:
            print(f"✅ 20250301数据量充足 ({count_0301}条 >= 30000条)")
            print(f"✅ 可以覆盖105个交易日的数据")
        else:
            print(f"❌ 20250301数据量不足 ({count_0301}条 < 30000条)")
            
        print(f"\n💡 您的观察完全正确！")
        print(f"  问题: 10000条上限不足以覆盖20250301到今天的数据")
        print(f"  修复: 将上限提升到50000条")
        print(f"  效果: 现在可以覆盖约8-9个月的历史数据")
        
        return count_0301 >= 30000
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        return False

if __name__ == '__main__':
    success = test_fix()
    if success:
        print(f"\n🎉 修复成功！20250301问题已解决！")
    else:
        print(f"\n⚠️ 仍需进一步调试")
