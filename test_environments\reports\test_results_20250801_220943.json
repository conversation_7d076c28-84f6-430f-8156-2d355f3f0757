{"success": true, "test_level": "standard", "start_time": "2025-08-01T22:09:36.558402", "end_time": "2025-08-01T22:09:43.867556", "suites": [{"suite_name": "test_price_consistency", "success": true, "return_code": 0, "stdout": "✅ 生产环境路径映射已更新\n🧪 测试环境已初始化\n🧪 价格一致性检查测试套件\n📅 测试时间: 2025-08-01 22:09:36\n🌍 环境模式: 测试\n================================================================================\n🧪 测试基本价格一致性检查\n------------------------------------------------------------\n📋 测试参数:\n   文件: 1min_0_000617_20250320-20250704_来源互联网.txt\n   股票: 000617\n📋 测试素材已复制: 1min_0_000617_20250320-20250704_来源互联网.txt -> test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n   ✅ 测试数据已准备: test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n🔍 比较测试文件与API的未复权收盘价\n   文件: 1min_0_000617_20250320-20250704_来源互联网.txt\n   股票: 000617\n   📋 文件最后记录: 时间=202507041447, 未复权收盘价=7.55\n\n📊 检查结果:\n   成功: True\n   一致: True\n   环境: test\n   ✅ 正确使用测试环境\n\n🧪 测试多个股票的价格一致性\n------------------------------------------------------------\n\n📋 测试股票: 000617\n📋 测试素材已复制: 1min_0_000617_20250320-20250704_来源互联网.txt -> test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n🔍 比较测试文件与API的未复权收盘价\n   文件: 1min_0_000617_20250320-20250704_来源互联网.txt\n   股票: 000617\n   📋 文件最后记录: 时间=202507041447, 未复权收盘价=7.55\n   结果: ✅ 成功\n\n📋 测试股票: 000001\n📋 测试素材已复制: 1min_0_000001_20250101-20250731_来源互联网.txt -> test_environments/minute_data_tests/input_data\\1min_0_000001_20250101-20250731_来源互联网.txt\n🔍 比较测试文件与API的未复权收盘价\n   文件: 1min_0_000001_20250101-20250731_来源互联网.txt\n   股票: 000001\n   📋 文件最后记录: 时间=202507311447, 未复权收盘价=3.51\n   结果: ✅ 成功\n\n📊 多股票测试结果: 2/2 成功\n\n🧪 测试增量下载前提条件检查\n------------------------------------------------------------\n📋 测试素材已复制: 1min_0_000617_20250320-20250704_来源互联网.txt -> test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n🔍 比较测试文件与API的未复权收盘价\n   文件: 1min_0_000617_20250320-20250704_来源互联网.txt\n   股票: 000617\n   📋 文件最后记录: 时间=202507041447, 未复权收盘价=7.55\n📊 检查结果:\n   具备前提条件: True\n   结论: 价格一致，无分红配股影响\n   建议: 可以进行增量下载\n   ✅ 正确使用测试环境\n\n🧪 测试结构化下载器集成\n------------------------------------------------------------\n📋 测试素材已复制: 1min_0_000617_20250320-20250704_来源互联网.txt -> test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n🔍 比较测试文件与API的未复权收盘价\n   文件: 1min_0_000617_20250320-20250704_来源互联网.txt\n   股票: 000617\n   📋 文件最后记录: 时间=202507041447, 未复权收盘价=7.55\n📊 检查结果:\n   具备前提条件: True\n   结论: 价格一致，无分红配股影响\n   ✅ 使用了统一接口\n   环境模式: test\n\n🧪 测试数据保鲜机制\n------------------------------------------------------------\n📋 测试素材已复制: 1min_0_000617_20250320-20250704_来源互联网.txt -> test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n   ✅ 第一次数据准备成功: test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n📋 测试素材已复制: 1min_0_000617_20250320-20250704_来源互联网.txt -> test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n   ✅ 第二次数据准备成功: test_environments/minute_data_tests/input_data\\1min_0_000617_20250320-20250704_来源互联网.txt\n   ✅ 数据保鲜机制工作正常\n\n📊 测试结果汇总\n================================================================================\n   基本价格一致性检查: ✅ 通过\n   多股票价格一致性: ✅ 通过\n   增量下载前提条件检查: ✅ 通过\n   结构化下载器集成: ✅ 通过\n   数据保鲜机制: ✅ 通过\n\n📈 统计信息:\n   总测试数: 5\n   通过测试数: 5\n   失败测试数: 0\n   通过率: 100.0%\n\n🎉 价格一致性检查测试套件通过！\n", "stderr": "", "duration": 7.309154, "timestamp": "2025-08-01T22:09:43.867556"}], "summary": {"total": 1, "passed": 1, "failed": 0, "pass_rate": 1.0, "duration": 7.309154}}