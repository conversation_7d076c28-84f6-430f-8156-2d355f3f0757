#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Find early pytdx call before 4-step workflow
"""

import os
import sys

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# Global state tracking
taskmanager_log_printed = False
four_step_started = False
early_calls = []

def install_precise_patches():
    """Install precise patches to catch early pytdx calls"""
    global taskmanager_log_printed, four_step_started, early_calls
    
    try:
        # 1. Patch TaskManager log to mark the critical point
        from src.mythquant.core.task_manager import TaskManager
        
        original_execute_minute = TaskManager._execute_minute_task
        
        def patched_execute_minute(self, task, target_stocks):
            """Track TaskManager log point"""
            global taskmanager_log_printed, four_step_started
            
            print(f"\n=== TASKMANAGER LOG POINT ===")
            print(f"About to log: 🚀 使用结构化四步流程执行分钟级数据下载")
            taskmanager_log_printed = True
            four_step_started = False  # Reset
            
            result = original_execute_minute(self, task, target_stocks)
            
            print(f"=== TASKMANAGER EXECUTION COMPLETE ===")
            taskmanager_log_printed = False
            four_step_started = False
            
            return result
        
        TaskManager._execute_minute_task = patched_execute_minute
        
        # 2. Patch structured downloader to mark 4-step start
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        original_execute = StructuredInternetMinuteDownloader.execute_structured_download
        
        def patched_execute(self, stock_codes, start_date, end_date, frequency, original_frequency):
            """Track when 4-step actually starts"""
            global four_step_started

            print(f"\n=== FOUR-STEP WORKFLOW STARTS ===")
            print(f"execute_structured_download called for {stock_codes}")
            four_step_started = True

            return original_execute(self, stock_codes, start_date, end_date, frequency, original_frequency)
        
        StructuredInternetMinuteDownloader.execute_structured_download = patched_execute
        
        # 3. Patch ALL pytdx-related calls to catch early usage
        from utils.pytdx_downloader import PytdxDownloader

        original_download = PytdxDownloader.download_minute_data
        
        def patched_download(self, stock_code, start_date, end_date, frequency='1min', suppress_warnings=False):
            """Catch early download calls"""
            global taskmanager_log_printed, four_step_started, early_calls
            
            import traceback
            
            # Check if this is an early call
            is_early_call = taskmanager_log_printed and not four_step_started
            
            if is_early_call:
                print(f"\n*** EARLY PYTDX CALL DETECTED ***")
                print(f"download_minute_data called BEFORE 4-step workflow!")
                print(f"Stock: {stock_code}")
                print(f"Range: {start_date} - {end_date}")
                print(f"TaskManager logged: {taskmanager_log_printed}")
                print(f"4-step started: {four_step_started}")
                
                print(f"\nEARLY CALL Stack:")
                stack = traceback.format_stack()
                for i, frame in enumerate(stack, 1):
                    if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'structured_internet', 'execute_structured_download']):
                        print(f">>> {i:2d}. {frame.strip()}")
                    else:
                        print(f"    {i:2d}. {frame.strip()}")
                
                early_calls.append({
                    'method': 'download_minute_data',
                    'stock_code': stock_code,
                    'date_range': f"{start_date}-{end_date}",
                    'stack': stack
                })
                
                print("=" * 120)
            
            return original_download(self, stock_code, start_date, end_date, frequency, suppress_warnings)
        
        # Apply patches
        PytdxDownloader.download_minute_data = patched_download
        
        print("Precise early pytdx call detection patches installed")
        return True
        
    except Exception as e:
        print(f"Patch installation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def run_test():
    """Run the test"""
    global early_calls
    
    print("\nRunning main.py with early pytdx call detection...")
    
    try:
        from main import main
        result = main()
        
        print(f"\nMain completed with result: {result}")
        print(f"Total early pytdx calls detected: {len(early_calls)}")
        
        if early_calls:
            print(f"\n*** EARLY PYTDX CALLS SUMMARY ***")
            for i, call in enumerate(early_calls, 1):
                print(f"Early Call #{i}:")
                print(f"  Method: {call['method']}")
                if 'stock_code' in call:
                    print(f"  Stock: {call['stock_code']}")
                    print(f"  Range: {call['date_range']}")
                print(f"  Stack depth: {len(call['stack'])}")
                print()
        
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("Early PytdxDownloader Call Detector")
    print("=" * 80)
    print("Finding pytdx calls BEFORE 4-step workflow starts")
    print("=" * 80)
    
    if not install_precise_patches():
        return 1
    
    success = run_test()
    
    print(f"\nDetection completed: {'SUCCESS' if success else 'FAILED'}")
    
    if early_calls:
        print(f"\n🚨 EARLY PYTDX CALLS FOUND: {len(early_calls)}")
        print("These calls violate the strict 4-step workflow!")
        print("pytdx should ONLY be called within the 4-step process!")
    else:
        print(f"\n✅ NO EARLY PYTDX CALLS DETECTED")
        print("All pytdx calls occur within the 4-step workflow")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
