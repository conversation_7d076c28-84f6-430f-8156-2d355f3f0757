#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复功能专项测试脚本
用于测试强制修复策略和PytdxDataRepairer的bug修复
"""

import os
import sys
import shutil

# 设置强制测试环境标识
os.environ['FORCE_TEST_ENV'] = '1'
os.environ['AUGMENT_MODE'] = '1'

def setup_repair_test_environment():
    """设置修复功能测试环境"""
    print("🔧 设置修复功能测试环境")

    # 1. 验证测试环境配置
    print("📋 验证测试环境配置...")
    try:
        from utils.test_environment_validator import TestEnvironmentValidator
        validator = TestEnvironmentValidator()
        validation_result = validator.validate_test_environment()

        print(f"   验证结果: {validation_result['overall_result'].value}")
        print(f"   检查项: {validation_result['summary']['total_checks']}")
        print(f"   通过: {validation_result['summary']['pass_count']}")
        print(f"   警告: {validation_result['summary']['warn_count']}")
        print(f"   失败: {validation_result['summary']['fail_count']}")

        if validation_result['overall_result'].value == 'FAIL':
            print("❌ 测试环境验证失败，但继续执行测试")
            for report in validation_result['reports']:
                if report.result.value == 'FAIL':
                    print(f"   ❌ {report.check_name}: {report.message}")
        else:
            print("✅ 测试环境验证通过")

    except Exception as e:
        print(f"⚠️ 测试环境验证异常: {e}")

    # 2. 执行测试场景预检查
    print("📋 执行测试场景预检查...")
    try:
        from utils.test_scenario_executor import TestScenarioExecutor
        executor = TestScenarioExecutor()
        scenario_result = executor.execute_all_scenarios()

        print(f"   场景结果: {scenario_result['overall_result'].value}")
        print(f"   总场景数: {scenario_result['summary']['total_scenarios']}")
        print(f"   成功: {scenario_result['summary']['success_count']}")
        print(f"   部分成功: {scenario_result['summary']['partial_count']}")
        print(f"   失败: {scenario_result['summary']['failure_count']}")

    except Exception as e:
        print(f"⚠️ 测试场景预检查异常: {e}")

    # 3. 确保测试环境目录存在
    test_env_path = 'test_environments/minute_data_tests'
    input_data_path = os.path.join(test_env_path, 'input_data')

    if not os.path.exists(input_data_path):
        os.makedirs(input_data_path, exist_ok=True)
        print(f"✅ 创建测试环境目录: {input_data_path}")

    # 4. 检查有缺失数据的测试文件
    incomplete_file = os.path.join(input_data_path, 'test_incomplete_1min_0_000617_20250320-20250704.txt')

    if os.path.exists(incomplete_file):
        print(f"✅ 找到有缺失数据的测试文件: {incomplete_file}")

        # 统计文件行数
        with open(incomplete_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()

        print(f"📊 测试文件行数: {len(lines)} (包含表头)")
        print(f"📊 数据行数: {len(lines) - 1}")
        print(f"📊 预期缺失: 大量数据 (只包含2025-03-20的部分数据)")

        return True
    else:
        print(f"❌ 未找到有缺失数据的测试文件: {incomplete_file}")
        return False

def create_working_copy_for_repair_test():
    """为修复测试创建工作副本"""
    print("🔄 创建修复测试工作副本")

    input_data_path = 'test_environments/minute_data_tests/input_data'
    incomplete_file = os.path.join(input_data_path, 'test_incomplete_1min_0_000617_20250320-20250704.txt')
    working_file = os.path.join(input_data_path, 'working_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt')

    # 检查源文件
    if not os.path.exists(incomplete_file):
        print(f"❌ 源文件不存在: {incomplete_file}")

        # 尝试使用原始测试文件作为源
        original_file = os.path.join(input_data_path, 'test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt')
        if os.path.exists(original_file):
            print(f"📋 使用原始测试文件作为源: {original_file}")
            # 创建有缺失数据的版本（取前1200行，模拟缺失数据）
            try:
                with open(original_file, 'r', encoding='utf-8') as f:
                    lines = f.readlines()

                # 创建有缺失数据的文件（保留表头+前1200行数据）
                with open(incomplete_file, 'w', encoding='utf-8') as f:
                    f.writelines(lines[:1201])  # 表头 + 1200行数据

                print(f"✅ 创建有缺失数据的测试文件: {len(lines[:1201])} 行")

            except Exception as e:
                print(f"❌ 创建有缺失数据文件失败: {e}")
                return False
        else:
            print(f"❌ 原始测试文件也不存在: {original_file}")
            return False

    # 创建工作副本
    try:
        shutil.copy2(incomplete_file, working_file)
        print(f"✅ 创建工作副本成功")
        print(f"   源文件: {os.path.basename(incomplete_file)}")
        print(f"   工作副本: {os.path.basename(working_file)}")

        # 验证工作副本
        if os.path.exists(working_file):
            with open(working_file, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            print(f"   工作副本行数: {len(lines)} (包含表头)")
            return True
        else:
            print(f"❌ 工作副本创建失败")
            return False

    except Exception as e:
        print(f"❌ 创建工作副本失败: {e}")
        return False

def run_repair_test():
    """运行修复功能测试"""
    print("🚀 开始修复功能测试")
    print("=" * 60)
    
    try:
        # 验证环境检测
        import user_config
        print(f"🔍 环境检测结果: {user_config.CURRENT_ENVIRONMENT}")
        
        if user_config.CURRENT_ENVIRONMENT != 'test':
            print("❌ 环境检测失败，仍为生产环境")
            return False
        else:
            print("✅ 成功切换到测试环境")
        
        # 运行主程序（应该触发修复流程）
        from main import main
        main()
        
        return True
        
    except Exception as e:
        print(f"❌ 修复测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_repair_test():
    """清理修复测试环境"""
    print("\n🔄 清理修复测试环境")
    
    input_data_path = 'test_environments/minute_data_tests/input_data'
    working_file = os.path.join(input_data_path, 'working_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt')
    
    if os.path.exists(working_file):
        os.remove(working_file)
        print(f"✅ 删除工作副本: {working_file}")
    
    print("✅ 修复测试环境清理完成")

if __name__ == "__main__":
    print("🧪 修复功能专项测试")
    print("目标: 测试强制修复策略和PytdxDataRepairer的bug修复")
    print("=" * 60)
    
    try:
        # 1. 设置测试环境
        if not setup_repair_test_environment():
            print("❌ 测试环境设置失败")
            sys.exit(1)
        
        # 2. 创建工作副本
        if not create_working_copy_for_repair_test():
            print("❌ 工作副本创建失败")
            sys.exit(1)
        
        # 3. 运行修复测试
        success = run_repair_test()
        
        if success:
            print("\n✅ 修复功能测试完成")
        else:
            print("\n❌ 修复功能测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试脚本异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 4. 清理测试环境
        cleanup_repair_test()
