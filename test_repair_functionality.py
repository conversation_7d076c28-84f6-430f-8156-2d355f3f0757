#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修复功能专项测试脚本
用于测试强制修复策略和PytdxDataRepairer的bug修复
"""

import os
import sys
import shutil

# 设置强制测试环境标识
os.environ['FORCE_TEST_ENV'] = '1'
os.environ['AUGMENT_MODE'] = '1'

def setup_repair_test_environment():
    """设置修复功能测试环境"""
    print("🔧 设置修复功能测试环境")
    
    # 确保测试环境目录存在
    test_env_path = 'test_environments/minute_data_tests'
    input_data_path = os.path.join(test_env_path, 'input_data')
    
    if not os.path.exists(input_data_path):
        os.makedirs(input_data_path, exist_ok=True)
        print(f"✅ 创建测试环境目录: {input_data_path}")
    
    # 检查有缺失数据的测试文件
    incomplete_file = os.path.join(input_data_path, 'test_incomplete_1min_0_000617_20250320-20250704.txt')
    
    if os.path.exists(incomplete_file):
        print(f"✅ 找到有缺失数据的测试文件: {incomplete_file}")
        
        # 统计文件行数
        with open(incomplete_file, 'r', encoding='utf-8') as f:
            lines = f.readlines()
        
        print(f"📊 测试文件行数: {len(lines)} (包含表头)")
        print(f"📊 数据行数: {len(lines) - 1}")
        print(f"📊 预期缺失: 大量数据 (只包含2025-03-20的部分数据)")
        
        return True
    else:
        print(f"❌ 未找到有缺失数据的测试文件: {incomplete_file}")
        return False

def create_working_copy_for_repair_test():
    """为修复测试创建工作副本"""
    print("🔄 创建修复测试工作副本")
    
    input_data_path = 'test_environments/minute_data_tests/input_data'
    incomplete_file = os.path.join(input_data_path, 'test_incomplete_1min_0_000617_20250320-20250704.txt')
    working_file = os.path.join(input_data_path, 'working_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt')
    
    if os.path.exists(incomplete_file):
        # 复制有缺失数据的文件作为工作副本
        shutil.copy2(incomplete_file, working_file)
        print(f"✅ 创建工作副本: {working_file}")
        return True
    else:
        print(f"❌ 源文件不存在: {incomplete_file}")
        return False

def run_repair_test():
    """运行修复功能测试"""
    print("🚀 开始修复功能测试")
    print("=" * 60)
    
    try:
        # 验证环境检测
        import user_config
        print(f"🔍 环境检测结果: {user_config.CURRENT_ENVIRONMENT}")
        
        if user_config.CURRENT_ENVIRONMENT != 'test':
            print("❌ 环境检测失败，仍为生产环境")
            return False
        else:
            print("✅ 成功切换到测试环境")
        
        # 运行主程序（应该触发修复流程）
        from main import main
        main()
        
        return True
        
    except Exception as e:
        print(f"❌ 修复测试异常: {e}")
        import traceback
        traceback.print_exc()
        return False

def cleanup_repair_test():
    """清理修复测试环境"""
    print("\n🔄 清理修复测试环境")
    
    input_data_path = 'test_environments/minute_data_tests/input_data'
    working_file = os.path.join(input_data_path, 'working_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt')
    
    if os.path.exists(working_file):
        os.remove(working_file)
        print(f"✅ 删除工作副本: {working_file}")
    
    print("✅ 修复测试环境清理完成")

if __name__ == "__main__":
    print("🧪 修复功能专项测试")
    print("目标: 测试强制修复策略和PytdxDataRepairer的bug修复")
    print("=" * 60)
    
    try:
        # 1. 设置测试环境
        if not setup_repair_test_environment():
            print("❌ 测试环境设置失败")
            sys.exit(1)
        
        # 2. 创建工作副本
        if not create_working_copy_for_repair_test():
            print("❌ 工作副本创建失败")
            sys.exit(1)
        
        # 3. 运行修复测试
        success = run_repair_test()
        
        if success:
            print("\n✅ 修复功能测试完成")
        else:
            print("\n❌ 修复功能测试失败")
            
    except Exception as e:
        print(f"\n❌ 测试脚本异常: {e}")
        import traceback
        traceback.print_exc()
        
    finally:
        # 4. 清理测试环境
        cleanup_repair_test()
