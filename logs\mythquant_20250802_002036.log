2025-08-02 00:20:36,277 - Main - INFO - info:316 - ℹ️ 程序启动，日志文件: logs\mythquant_20250802_002036.log
2025-08-02 00:20:36,277 - Main - INFO - info:316 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-08-02 00:20:36,278 - Main - INFO - info:316 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-08-02 00:20:36,278 - Main - INFO - info:316 - ℹ️ 正在加载任务配置...
2025-08-02 00:20:36,278 - Main - INFO - info:316 - ℹ️ 加载了 7 个任务配置
2025-08-02 00:20:36,278 - Main - INFO - info:316 - ℹ️ 核心组件初始化完成
2025-08-02 00:20:36,278 - Main - INFO - info:316 - ℹ️ 应用程序初始化完成
2025-08-02 00:20:36,279 - Main - INFO - info:316 - ℹ️ 开始执行所有任务
2025-08-02 00:20:36,279 - Main - INFO - info:316 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-08-02 00:20:36,304 - Main - INFO - info:316 - ℹ️ 加载生产环境配置
2025-08-02 00:20:36,304 - Main - INFO - info:316 - ℹ️ 生产环境初始化完成
2025-08-02 00:20:36,306 - Main - INFO - info:316 - ℹ️ 🎯 获取特定分钟数据: 000617 @ 202507041447
2025-08-02 00:20:36,306 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-08-02 00:20:36,499 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 5040条 (目标日期: 20250704, 交易日: 21天, 频率: 1min)
2025-08-02 00:20:36,499 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计5040条 -> 实际请求800条 (配置限制:800)
2025-08-02 00:20:36,499 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-02 00:20:36,499 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-02 00:20:36,551 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4240条
2025-08-02 00:20:36,773 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-02 00:20:37,007 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-02 00:20:37,237 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-02 00:20:37,461 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-02 00:20:37,685 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-02 00:20:37,912 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +240条，总计5040条
2025-08-02 00:20:37,912 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计5040条数据
2025-08-02 00:20:37,913 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要5040条，实际获取5040条
2025-08-02 00:20:37,927 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-02 00:20:37,928 - Main - INFO - info:316 - ℹ️ 📊 获取到 240 条原始数据，查找目标时间 202507041447
2025-08-02 00:20:37,932 - Main - INFO - info:316 - ℹ️ ✅ 找到目标数据: 未复权收盘价=7.550
2025-08-02 00:20:37,932 - Main - INFO - info:316 - ℹ️ ✅ 增量下载前提条件验证通过
2025-08-02 00:20:37,933 - Main - INFO - info:316 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-08-02 00:20:37,933 - Main - INFO - info:316 - ℹ️ 🔍 开始检测000617的缺失数据
2025-08-02 00:20:37,961 - Main - INFO - info:316 - ℹ️ 📊 数据统计: 总记录数17211, 覆盖72个交易日
2025-08-02 00:20:37,961 - Main - WARNING - warning:320 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-08-02 00:20:37,961 - Main - WARNING - warning:320 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-08-02 00:20:37,961 - Main - WARNING - warning:320 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-08-02 00:20:37,962 - Main - INFO - info:316 - ℹ️ 🔧 开始修复000617的缺失数据
2025-08-02 00:20:37,962 - Main - INFO - info:316 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-08-02 00:20:37,962 - Main - INFO - info:316 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-08-02 00:20:37,962 - Main - INFO - info:316 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-08-02 00:20:37,962 - Main - INFO - info:316 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-08-02 00:20:37,962 - Main - INFO - info:316 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-08-02 00:20:37,962 - Main - INFO - info:316 - ℹ️ ✅ 缺失数据修复完成
2025-08-02 00:20:38,315 - Main - INFO - info:316 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-08-02 00:20:38,315 - Main - INFO - info:316 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-08-02 00:20:38,316 - Main - INFO - info:316 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-02 00:20:38,316 - Main - INFO - info:316 - ℹ️ 找到现有文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-02 00:20:38,316 - Main - INFO - info:316 - ℹ️ 📊 开始智能时间范围分析
2025-08-02 00:20:38,334 - Main - INFO - info:316 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-08-02 00:20:38,554 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-08-02 00:20:38,555 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-02 00:20:38,555 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-02 00:20:38,555 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-08-02 00:20:38,734 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 5040条 (目标日期: 20250704, 交易日: 21天, 频率: 1min)
2025-08-02 00:20:38,734 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计5040条 -> 实际请求800条 (配置限制:800)
2025-08-02 00:20:38,734 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-02 00:20:38,734 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-02 00:20:38,787 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4240条
2025-08-02 00:20:39,014 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-02 00:20:39,249 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-02 00:20:39,483 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-02 00:20:39,698 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-02 00:20:39,927 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-02 00:20:40,157 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +240条，总计5040条
2025-08-02 00:20:40,157 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计5040条数据
2025-08-02 00:20:40,158 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要5040条，实际获取5040条
2025-08-02 00:20:40,170 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-02 00:20:40,178 - Main - INFO - info:316 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-08-02 00:20:40,180 - Main - INFO - info:316 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-08-02 00:20:40,180 - Main - INFO - info:316 - ℹ️ 从文件名解析时间范围: 20250320 - 20250704
2025-08-02 00:20:40,180 - Main - INFO - info:316 - ℹ️ 需要下载早期数据: 20250101 - 20250319
2025-08-02 00:20:40,180 - Main - INFO - info:316 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-08-02 00:20:40,189 - Main - INFO - info:316 - ℹ️ 读取现有数据: 17211 条记录
2025-08-02 00:20:40,189 - Main - INFO - info:316 - ℹ️ 下载增量数据: 20250101 - 20250319
2025-08-02 00:20:40,189 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-02 00:20:40,189 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-02 00:20:40,189 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250319
2025-08-02 00:20:40,371 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 33600条 (目标日期: 20250101, 交易日: 140天, 频率: 1min)
2025-08-02 00:20:40,371 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计33600条 -> 实际请求800条 (配置限制:800)
2025-08-02 00:20:40,371 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-02 00:20:40,371 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-02 00:20:40,423 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32800条
2025-08-02 00:20:40,652 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-02 00:20:40,876 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-02 00:20:41,097 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-02 00:20:41,329 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-02 00:20:41,554 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-02 00:20:41,780 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-08-02 00:20:42,003 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-08-02 00:20:42,228 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-08-02 00:20:42,458 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-08-02 00:20:42,687 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-08-02 00:20:42,898 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-08-02 00:20:43,126 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-08-02 00:20:43,350 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-08-02 00:20:43,579 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-08-02 00:20:43,798 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-08-02 00:20:44,024 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-08-02 00:20:44,242 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-08-02 00:20:44,463 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-08-02 00:20:44,684 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-08-02 00:20:44,914 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-08-02 00:20:45,132 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-08-02 00:20:45,355 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-08-02 00:20:45,579 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-08-02 00:20:45,814 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-08-02 00:20:46,056 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-08-02 00:20:46,276 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-08-02 00:20:46,498 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-08-02 00:20:46,718 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +160条，总计22560条
2025-08-02 00:20:46,718 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计22560条数据
2025-08-02 00:20:46,724 - Main - WARNING - warning:320 - ⚠️ ⚠️ 数据覆盖不足: 需要33600条，实际获取22560条
2025-08-02 00:20:46,724 - Main - WARNING - warning:320 - ⚠️ ⚠️ 缺少约11040条数据（约46个交易日）
2025-08-02 00:20:46,724 - Main - WARNING - warning:320 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-08-
2025-08-02 00:20:46,724 - Main - WARNING - warning:320 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-08-02 00:20:46,724 - Main - WARNING - warning:320 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-08-02 00:20:46,793 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 480 条 1min 数据
2025-08-02 00:20:46,816 - Main - INFO - info:316 - ℹ️ 获得新数据: 480 条记录
2025-08-02 00:20:46,817 - Main - INFO - info:316 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-08-02 00:20:46,817 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-02 00:20:46,817 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-02 00:20:46,817 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-08-02 00:20:46,988 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 4800条 (目标日期: 20250705, 交易日: 20天, 频率: 1min)
2025-08-02 00:20:46,988 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计4800条 -> 实际请求800条 (配置限制:800)
2025-08-02 00:20:46,988 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-02 00:20:46,988 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-02 00:20:47,039 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4000条
2025-08-02 00:20:47,266 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-02 00:20:47,492 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-02 00:20:47,725 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-02 00:20:47,954 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-02 00:20:48,183 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-02 00:20:48,184 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计4800条数据
2025-08-02 00:20:48,185 - Main - INFO - info:316 - ℹ️ 📊 数据量充足: 需要4800条，实际获取4800条
2025-08-02 00:20:48,196 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-08-02 00:20:48,212 - Main - INFO - info:316 - ℹ️ 获得新数据: 3600 条记录
2025-08-02 00:20:48,215 - Main - INFO - info:316 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-08-02 00:20:48,224 - Main - INFO - info:316 - ℹ️ 合并后数据: 21291 条记录
2025-08-02 00:20:48,291 - Main - INFO - info:316 - ℹ️ 删除旧文件: 1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt
2025-08-02 00:20:48,291 - Main - INFO - info:316 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503180931-202507251500_来源互联网（202508020020）.txt
2025-08-02 00:20:48,291 - Main - INFO - info:316 - ℹ️ ✅ 智能增量下载完成
2025-08-02 00:20:48,292 - Main - INFO - info:316 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-08-02 00:20:48,292 - Main - INFO - info:316 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-08-02 00:20:48,292 - Main - INFO - info:316 - ℹ️ 开始执行前复权数据比较分析任务
2025-08-02 00:20:48,292 - Main - INFO - info:316 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-08-02 00:20:48,292 - Main - INFO - info:316 - ℹ️ 正在分析股票: 000617
2025-08-02 00:20:48,292 - Main - INFO - info:316 - ℹ️ 开始比较股票 000617 的前复权数据
2025-08-02 00:20:48,293 - Main - INFO - info:316 - ℹ️ 找到文件 - TDX: 未找到
2025-08-02 00:20:48,293 - Main - INFO - info:316 - ℹ️ 找到文件 - 互联网: 未找到
2025-08-02 00:20:48,293 - Main - WARNING - warning:320 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250802_002048.txt
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-08-02 00:20:48,294 - Main - ERROR - error:324 - ❌ 任务执行失败: 前复权数据比较分析
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 开始清理应用程序资源
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 开始清理股票处理器资源
2025-08-02 00:20:48,294 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 异步IO处理器资源清理完成
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 股票处理器资源清理完成
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 开始清理任务管理器资源
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 任务管理器资源清理完成
2025-08-02 00:20:48,294 - Main - INFO - info:316 - ℹ️ 应用程序资源清理完成
