# FAQ知识库

## 概述
本文档记录了MythQuant项目开发过程中遇到的常见问题和解决方案，按问题类型分类管理，便于快速查找和解决类似问题。

## 问题分类统计
- **架构设计**: 2个问题
- **输出优化**: 1个问题
- **配置管理**: 1个问题
- **workflow规范**: 2个问题
- **流程设计**: 1个问题
- **沟通协作**: 1个问题

---

## 架构设计类问题

### Q1: 增量下载过程中违反workflow规范，重复调用智能文件选择器
**时间**: 2025-08-07  
**复杂度**: 高  
**状态**: 已解决  
**领域**: 架构设计, workflow规范

#### 问题描述
在增量下载过程中，IncrementalDownloader在初始化时创建SmartFileSelector实例，导致违反1min_workflow_improved.md第310行"增量更新过程不能再进行智能文件选择"的规定。

#### 问题表现
```
🌐 [5.2] pytdx下载策略
📥 执行增量数据下载
📋 ✅ 加载智能文件选择器配置: 策略=smart_comprehensive  # 违规！
📋 🔍 发现9个候选文件，开始智能选择  # 不应该出现！
```

#### 根本原因
1. IncrementalDownloader依赖SmartFileSelector，违反workflow规范
2. 架构设计时没有考虑业务流程约束
3. 依赖关系设计不合理，导致在错误的阶段调用智能组件

#### 解决方案
```python
# 修复前（违规）
class IncrementalDownloader:
    def __init__(self, output_dir: str):
        self.file_selector = SmartFileSelector(output_dir)  # ❌

# 修复后（合规）
class IncrementalDownloader:
    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        # 移除智能文件选择器依赖，遵循workflow规范

    def find_existing_file(self, ...):
        # 废弃此方法，直接传递已选择的文件
        self.smart_logger.warning("⚠️ 违反workflow规范")
        return None
```

#### 技术要点
- 移除违规依赖关系
- 使用参数传递代替重新选择
- 废弃违规方法并提供警告信息
- 简化重命名逻辑，不依赖智能组件

#### 修改文件
- `utils/incremental_downloader.py`
- `utils/stock_data_downloader.py`
- `utils/structured_internet_minute_downloader.py`

#### 预防措施
- 建立workflow规范检查机制
- 在架构设计时优先考虑业务流程约束
- 建立自动化的规范违反检测工具

---

### Q2: Terminal输出重复冗余，影响用户体验
**时间**: 2025-08-07  
**复杂度**: 中  
**状态**: 已解决  
**领域**: 输出优化, 用户体验

#### 问题描述
Terminal中出现大量重复输出，包括重复的"文件最后记录"、"价格差异"、"API获取数据"等信息，严重影响用户体验。

#### 问题表现
```
❌ 修复前的重复输出：
📋 文件最后记录: 时间=202508061500, 未复权收盘价=8.46
📋 文件最后记录: 2025-08-06 15:00, 收盘价=8.46  # 重复！
🌐 API获取数据: 2025-08-06 15:00, 收盘价=8.46
📋 API对应记录: 时间=202508061500, 未复权收盘价=8.460  # 重复！
📊 价格差异: 0.000 (容差: 0.001)
📊 价格差异: 0.000000 (容差: 0.001)  # 重复！
```

#### 根本原因
1. 多个组件重复输出相同信息
2. 缺乏静默模式参数控制
3. 调用链中存在重复的分析和选择过程
4. 没有建立输出去重机制

#### 解决方案
```python
# 1. 添加静默模式参数
def compare_last_record_close_price(
    self, test_file_path: str, stock_code: str, 
    tolerance: float = 0.001, verbose: bool = True
):
    if verbose:
        print(f"📋 详细信息...")

# 2. 调用时指定静默模式
comparison_result = comparator.compare_last_record_close_price(
    test_file_path=existing_file, stock_code=stock_code,
    tolerance=0.001, verbose=False  # 避免重复输出
)

# 3. 移除重复的显示逻辑
# 只保留最终的判断结果，移除技术细节
```

#### 技术要点
- 静默模式参数设计
- 输出职责分离（用户界面 vs 调试日志）
- 调用链优化，避免重复处理
- 技术细节隐藏，只显示关键业务信息

#### 修改文件
- `utils/structured_internet_minute_downloader.py`
- `test_environments/shared/utilities/test_file_api_comparator.py`
- `utils/result_notifier.py`

#### 效果对比
```
✅ 修复后的简洁输出：
   🌐 [2.2] API数据获取与比较
📋 数据量计算: 20250807到现在 = 1个交易日 × 240条/日 = 240条
   ⚖️ [2.3] 一致性判断
   ✅ 价格一致，具备增量下载条件
```

---

## Workflow规范类问题

### Q3: 如何确保代码修改符合workflow规范要求？
**时间**: 2025-08-07  
**复杂度**: 中  
**状态**: 已解决  
**领域**: workflow规范, 开发流程

#### 问题描述
开发过程中容易违反workflow文档中的明确规定，如何建立有效的检查和预防机制？

#### 解决方案
1. **强制文档检查**：修改核心流程代码前，必须先检查相关workflow文档
2. **自动化检测**：建立代码中的workflow规范违反检测机制
3. **依赖关系审查**：新建或修改类时，审查依赖关系是否符合规范
4. **文档驱动开发**：将workflow文档作为开发的重要参考

#### 技术实现
```python
# 自动化检测示例
def detect_workflow_violation():
    import traceback
    stack = traceback.format_stack()
    for frame in stack:
        if '_save_minute_data_incremental' in frame and 'SmartFileSelector' in frame:
            raise WorkflowViolationError("违反增量更新规范")
```

#### 预防措施
- 建立workflow规范的快速查询机制
- 在代码审查中增加规范检查
- 定期审查代码是否符合最新workflow要求
- 建立规范违反的自动化检测工具

---

## 输出优化类问题

### Q4: 如何设计用户友好的Terminal输出界面？
**时间**: 2025-08-07  
**复杂度**: 中  
**状态**: 已解决  
**领域**: 用户体验, 输出优化

#### 问题描述
Terminal输出需要既专业又用户友好，如何平衡技术细节和用户体验？

#### 解决方案
1. **职责分离**：用户界面输出 vs 调试日志输出
2. **格式标准化**：统一的符号系统和层级结构
3. **静默模式支持**：为可能重复调用的方法添加verbose参数
4. **技术细节隐藏**：将详细信息记录到日志文件

#### 最佳实践
```python
# 符号和格式规范
✅ 成功：✅  ❌ 失败：❌  ⚠️ 警告：⚠️
📊 数据：📊  📋 记录：📋  🔍 分析：🔍

# 层级缩进规范
主流程：不缩进
   子步骤：3个空格缩进
      详细信息：6个空格缩进

# 进度显示格式
📊 [1/4] 主要步骤
   🔍 [1.1] 子步骤
   ✅ [1.2] 完成状态
```

#### 技术要点
- 结构化输出格式器的使用
- 信息密度控制和层级管理
- 用户反馈驱动的持续优化
- 自动化的输出质量检测

---

## 流程设计类问题

### Q5: 在文件选择前执行数据质量稽核是否合理？
**时间**: 2025-08-08
**复杂度**: 中
**状态**: 已解决
**领域**: 流程设计, workflow规范

#### 问题描述
TaskManager在执行任务时，在智能文件选择前就调用了数据质量稽核，这违反了workflow规定的标准化四步流程顺序。

#### 问题表现
```
📊 【1/1】 执行任务: 分钟级数据生成
----------------------------------------
      ⚡ 执行数据质量稽核    ← 不合理！在文件选择前就稽核
2025-08-08 00:18:46,539 - TaskManager - INFO - 开始执行任务: 分钟级数据生成
```

#### 根本原因
1. 流程设计不合理：在没有选择具体文件的情况下就进行数据质量稽核
2. 违反workflow规范：不符合1min_workflow_improved.md规定的流程顺序
3. 注释代码未生效：第191行的代码实际上没有被注释

#### 解决方案
```python
# 修复前（违规）
self.flow_optimizer.suppress_technical_details("数据质量稽核")  # ❌

# 修复后（合规）
# self.flow_optimizer.suppress_technical_details("数据质量稽核")  # 违反workflow规范，已屏蔽
```

#### 技术要点
- 流程顺序必须严格遵循workflow文档规定
- 操作必须在正确的时机和上下文中执行
- 注释代码要确保真正被注释
- 建立流程合理性检查机制

#### 修改文件
- `src/mythquant/core/application.py`

#### 预防措施
- 建立流程合理性审查机制
- 严格按照workflow文档执行
- 定期检查注释代码的有效性
- 重视用户对流程合理性的观察

---

## 沟通协作类问题

### Q6: 如何更高效地向AI提问以获得精准的技术解决方案？
**时间**: 2025-08-08
**复杂度**: 中
**状态**: 已解决
**领域**: 沟通协作, 提问技巧

#### 问题描述
用户希望学习更高效的提问方式，替代直接粘贴terminal输出的方法，以便更好地与AI协作解决技术问题。

#### 核心挑战
1. **信息过载**: 直接粘贴大量terminal输出效率低下
2. **问题不明确**: 模糊的问题描述导致AI理解偏差
3. **缺乏结构**: 随意的问题表达影响沟通效率
4. **上下文缺失**: 缺少必要的背景信息和约束条件

#### 解决方案
建立了完整的高效提问体系：

**1. 核心提问原则**
- 从现象到本质：分析问题根本原因而非表面症状
- 从局部到全局：考虑问题的系统性影响
- 从技术到业务：结合业务价值和用户体验

**2. 五种高效提问方式**
- 问题描述式：直接描述问题本质和期望
- 关键信息提取式：提取核心信息而非完整输出
- 对比式：明确期望vs实际的差异
- 具体定位式：精确定位问题发生的位置和时机
- 架构层面式：从设计原则角度质疑问题

**3. 标准化提问模板**
```
问题定位模板:
在[具体场景]中，出现了[具体现象]，
但按照[标准/规范]应该是[期望行为]，
请帮我[具体要求]。

架构设计模板:
[组件A]和[组件B]都在做[相同的事情]，
这是否符合[架构原则]？
如何优化[具体方面]？
```

#### 技术要点
- **结构化表达**: 使用清晰的逻辑结构组织问题
- **上下文管理**: 提供必要的背景信息和约束条件
- **目标明确**: 清楚表达期望得到什么帮助
- **多角度分析**: 从用户体验、架构设计、性能等多角度考虑

#### 实际效果
- **沟通效率提升**: 减少澄清轮次，快速获得精准解答
- **问题理解准确**: AI能够准确理解问题本质和用户意图
- **解决方案质量**: 获得更有针对性和实用性的解决方案
- **知识传递**: 通过结构化提问获得可复用的知识

#### 创建文档
- `docs/knowledge/communication/effective_questioning_guide.md` - 完整的提问指南
- `docs/knowledge/communication/questioning_templates.md` - 标准化提问模板库

#### 应用场景
- 技术问题诊断和调试
- 架构设计讨论和评审
- 代码优化和性能改进
- 系统重构和技术选型
- 团队协作和知识分享

#### 预防措施
- 建立团队统一的提问规范
- 定期回顾和优化提问模板
- 培养结构化思维和表达能力
- 建立问题-解决方案知识库

---

## 知识库维护

### 更新记录
- **2025-08-07**: 创建FAQ知识库，添加架构设计和输出优化相关问题
- **2025-08-07**: 添加workflow规范遵循相关问题和解决方案

### 使用指南
1. **问题查找**: 使用Ctrl+F搜索关键词快速定位相关问题
2. **分类浏览**: 按问题类型浏览相关的解决方案
3. **经验复用**: 参考类似问题的解决思路和技术要点
4. **持续更新**: 遇到新问题时及时添加到知识库

### 贡献指南
- 问题描述要清晰具体，包含错误现象和影响
- 解决方案要完整可操作，包含代码示例
- 技术要点要突出关键信息，便于快速理解
- 修改文件要列出具体路径，便于定位

## 总结

FAQ知识库是项目重要的知识资产，通过系统化地记录和管理常见问题，可以显著提高问题解决效率，避免重复踩坑，促进团队知识共享和技能提升。

**使用建议**：
1. 遇到问题时先查询FAQ知识库
2. 解决新问题后及时更新知识库
3. 定期审查和优化知识库内容
4. 建立知识库的搜索和导航机制
