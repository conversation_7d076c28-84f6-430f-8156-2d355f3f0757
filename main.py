#!/usr/bin/env python3
# -*- coding: utf-8 -*-

# 设置Windows下的编码支持
import sys
import os
if sys.platform.startswith('win'):
    import io
    sys.stdout = io.TextIOWrapper(sys.stdout.buffer, encoding='utf-8')
    sys.stderr = io.TextIOWrapper(sys.stderr.buffer, encoding='utf-8')
"""
MythQuant 主程序入口 (轻量级版本)
专注于程序启动和流程控制，核心逻辑委托给专门的模块处理
"""

import sys
import os
from datetime import datetime

# Windows编码处理将在structured_output_formatter中统一处理

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 添加src目录到Python路径以支持新架构
from pathlib import Path
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 导入新架构的核心模块
try:
    from mythquant.core import MythQuantApplication
    from mythquant.config import config_manager
    NEW_ARCHITECTURE_AVAILABLE = True
    print("✅ 使用新架构")
except ImportError:
    # 回退到旧架构
    from core.application import MythQuantApplication
    from core.config_manager import ConfigManager
    config_manager = ConfigManager()
    NEW_ARCHITECTURE_AVAILABLE = False
    print("⚠️ 回退到旧架构")

# 导入DDD架构的工具模块
from mythquant.shared.logging import get_smart_logger
from mythquant.infrastructure.monitoring.apm import get_apm_manager

# 临时简化的输出函数
def print_banner(title, subtitle=""):
    print("=" * 60)
    print(f"🎯 {title}")
    if subtitle:
        print(f"📋 {subtitle}")
    print("=" * 60)

def print_main_process(message):
    print(f"🔄 {message}")

def print_info(message):
    print(f"ℹ️ {message}")

def print_completion(message, success=True, stats=None):
    """打印完成信息"""
    symbol = "✅" if success else "❌"
    print(f"{symbol} {message}")

    if stats:
        print("\n📊 执行统计:")
        for key, value in stats.items():
            print(f"  {key}: {value}")


def display_banner():
    """显示程序启动横幅"""
    print_banner(
        "MythQuant 量化交易数据处理系统",
        "前复权数据生成 & L2指标计算 | 高性能优化版本"
    )


def check_and_update_pytdx_servers():
    """检查并更新pytdx服务器配置"""
    try:
        smart_logger = get_smart_logger("PytdxServerCheck")

        # 导入配置
        import user_config

        # 检查是否启用自动检测
        tdx_config = getattr(user_config, 'tdx', {})
        pytdx_auto_detect = tdx_config.get('pytdx_auto_detect', False)
        pytdx_show_top5 = tdx_config.get('pytdx_show_top5', True)

        if not pytdx_auto_detect:
            smart_logger.info("pytdx自动检测已禁用，跳过服务器检测")
            return

        # 检查pytdx是否可用
        try:
            import pytdx  # noqa: F401
            smart_logger.info("pytdx库可用，开始服务器检测")
        except ImportError:
            smart_logger.info("pytdx库未安装，跳过服务器检测")
            return

        # 执行服务器检测 - 暂时跳过，因为模块已归档
        # from utils.tdx_server_finder import TdxServerFinder
        smart_logger.info("TDX服务器检测功能暂时禁用（模块已归档）")
        return

    except Exception as e:
        smart_logger = get_smart_logger("PytdxServerCheck")
        smart_logger.error(f"pytdx服务器检测异常: {e}")
        print(f"⚠️ pytdx服务器检测失败: {e}")


def initialize_application():
    """初始化应用程序"""
    try:
        # 设置日志系统
        smart_logger = get_smart_logger("Main")
        smart_logger.info("程序启动")

        # 检查pytdx服务器状态（暂时跳过）
        # check_pytdx_server_status()

        # 初始化应用程序
        from mythquant.core import MythQuantApplication
        from src.mythquant.core.config_manager import ConfigManager
        config_manager = ConfigManager()
        app = MythQuantApplication(config_manager)

        smart_logger.info("应用程序初始化完成")
        return app

    except Exception as e:
        smart_logger = get_smart_logger("Main")
        smart_logger.error(f"应用程序初始化失败: {e}")
        print(f"❌ 应用程序初始化失败: {e}")
        raise


def main():
    """主函数 - 轻量级入口"""
    program_start_time = datetime.now()

    try:
        # 显示启动横幅
        display_banner()

        # 简化的流程显示
        print_main_process("初始化应用程序...")

        # 初始化应用程序
        print_main_process("应用程序初始化")
        app = initialize_application()

        # 显示系统信息
        print_main_process("系统信息加载")
        app.display_system_overview()

        # 执行主要任务
        print_main_process("数据处理任务执行")
        success = app.run_all_tasks()
        
        # 程序结束
        program_end_time = datetime.now()
        total_duration = (program_end_time - program_start_time).total_seconds()

        # 构建统计信息
        stats = {
            "总执行时间": f"{total_duration:.2f} 秒",
            "执行时间段": f"{program_start_time.strftime('%H:%M:%S')} - {program_end_time.strftime('%H:%M:%S')}"
        }

        # 显示统计信息
        app.display_final_statistics()

        # 显示完成信息
        completion_message = "程序执行完成！所有任务成功" if success else "程序执行完成，但部分任务失败"
        print_completion(completion_message, success, stats)
        
        return 0 if success else 1
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断程序执行")
        return 130
        
    except Exception as e:
        smart_logger = get_smart_logger("Main")
        smart_logger.error(f"主程序执行失败: {e}")
        print(f"\n❌ 程序执行失败: {e}")
        return 1
        
    finally:
        # 清理资源
        try:
            if 'app' in locals():
                app.cleanup()
        except Exception as e:
            print(f"⚠️ 资源清理警告: {e}")


if __name__ == '__main__':
    sys.exit(main())
