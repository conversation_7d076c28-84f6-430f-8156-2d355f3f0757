#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出协调测试

验证结构化输出格式器与verbose_log系统的协调工作

位置: test_environments/integration_tests/configs/
作者: AI Assistant
创建时间: 2025-07-31
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_completion
)


def test_output_coordination():
    """测试输出协调"""
    print_step("输出协调测试", 1, 3)
    
    try:
        # 测试结构化输出格式器
        print_info("测试结构化输出格式器", level=2)
        from utils.structured_output_formatter import StructuredOutputFormatter
        formatter = StructuredOutputFormatter()
        formatter.print_info("这是结构化输出格式器的信息")
        print_result("结构化输出格式器正常", True, level=2)
        
        # 测试verbose_log系统
        print_info("测试verbose_log系统", level=2)
        from core.logging_service import verbose_log
        verbose_log('info', '这是verbose_log的信息', 'GENERAL')
        print_result("verbose_log系统正常", True, level=2)
        
        # 测试配置管理器
        print_info("检查详细模式配置", level=2)
        from core.config_manager import ConfigManager
        config_manager = ConfigManager()
        verbose_enabled = config_manager.is_verbose_enabled()
        print_info(f"详细模式状态: {'启用' if verbose_enabled else '禁用'}", level=2)
        
        if verbose_enabled:
            print_warning("详细模式已启用，可能与结构化输出产生重复", level=2)
            return False
        else:
            print_result("详细模式已禁用，输出协调良好", True, level=2)
            return True
        
    except Exception as e:
        print_error(f"输出协调测试失败: {e}", level=2)
        return False


def test_verbose_mode_impact():
    """测试详细模式对输出的影响"""
    print_step("详细模式影响测试", 2, 3)
    
    try:
        # 检查用户配置
        print_info("检查用户配置文件", level=2)
        
        # 读取用户配置
        config_path = os.path.join(project_root, 'user_config.py')
        if os.path.exists(config_path):
            with open(config_path, 'r', encoding='utf-8') as f:
                config_content = f.read()
            
            # 检查verbose_mode配置
            if "'enabled': True" in config_content:
                print_warning("用户配置中详细模式已启用", level=2)
                print_info("这可能导致verbose_log与结构化输出重复", level=2)
                return False
            else:
                print_result("用户配置中详细模式已禁用", True, level=2)
                return True
        else:
            print_warning("用户配置文件不存在", level=2)
            return True
            
    except Exception as e:
        print_error(f"详细模式影响测试失败: {e}", level=2)
        return False


def suggest_coordination_solution():
    """建议输出协调解决方案"""
    print_step("输出协调解决方案", 3, 3)
    
    print_info("建议的输出协调策略:", level=2)
    print_info("1. 在生产环境中禁用verbose_mode", level=3)
    print_info("2. 使用结构化输出格式器作为主要用户界面", level=3)
    print_info("3. verbose_log仅用于开发调试", level=3)
    print_info("4. 通过配置文件控制输出模式", level=3)
    
    # 生成建议的配置
    suggested_config = """
# 建议的生产环境配置
verbose_mode = {
    'enabled': False,  # 生产环境禁用详细模式
    'show_forward_adj_details': False,
    'show_performance_warnings': False,
    'show_data_processing_steps': False,
    'show_cache_status': False,
    'highlight_critical_info': False,
    'show_detailed_calculations': False,
}
"""
    
    print_info("建议的配置修改:", level=2)
    print_info(suggested_config.strip(), level=3)
    
    return True


def main():
    """主函数"""
    print_banner("输出协调测试", "验证结构化输出格式器与verbose_log系统的协调")
    
    print_info(f"项目根目录: {project_root}")
    print_info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    print_main_process("执行输出协调测试")
    
    # 执行测试
    tests = [
        ("输出协调", test_output_coordination),
        ("详细模式影响", test_verbose_mode_impact),
        ("解决方案建议", suggest_coordination_solution)
    ]
    
    results = {}
    passed_tests = 0
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results[test_name] = result
            if result:
                passed_tests += 1
        except Exception as e:
            print_error(f"测试 {test_name} 执行异常: {e}")
            results[test_name] = False
    
    # 生成测试报告
    total_tests = len(tests)
    pass_rate = passed_tests / total_tests
    
    print_main_process("输出协调测试结果")
    
    # 显示详细结果
    for test_name, result in results.items():
        status = "✅ 通过" if result else "❌ 失败"
        print_result(f"{test_name}: {status}", result, level=1)
    
    # 显示统计信息
    stats = {
        "总测试数": total_tests,
        "通过测试数": passed_tests,
        "失败测试数": total_tests - passed_tests,
        "通过率": f"{pass_rate:.1%}"
    }
    
    from utils.structured_output_formatter import print_stats_table
    print_stats_table("协调测试统计", stats)
    
    # 判断整体结果
    overall_success = pass_rate >= 0.6  # 允许部分失败，因为这是诊断性测试
    
    completion_message = "输出协调测试完成" if overall_success else "发现输出协调问题"
    print_completion(completion_message, overall_success, stats)
    
    return 0 if overall_success else 1


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
