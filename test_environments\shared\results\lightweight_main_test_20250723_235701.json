{"test_timestamp": "2025-07-23T23:57:01.239595", "new_main_result": {"test_name": "新轻量级主程序", "execution_time": 52.90667271614075, "result_code": 0, "success": true, "output_verification": {"files_checked": 1, "files_found": 1, "files_valid": 1, "total_size_bytes": 132499, "total_lines": 2448, "file_details": {"day_0_000617_20150101-20250731.txt": {"exists": true, "size_bytes": 132499, "size_mb": 0.12636089324951172, "line_count": 2448, "header_correct": true, "has_data": true, "data_complete": true, "forward_adj_correct": true, "valid": true}}}, "timestamp": "2025-07-23T23:55:18.323990"}, "original_main_result": {"test_name": "原始主程序", "execution_time": 97.89416742324829, "success": true, "output_verification": {"files_checked": 1, "files_found": 1, "files_valid": 1, "total_size_bytes": 132499, "total_lines": 2448, "file_details": {"day_0_000617_20150101-20250731.txt": {"exists": true, "size_bytes": 132499, "size_mb": 0.12636089324951172, "line_count": 2448, "header_correct": true, "has_data": true, "data_complete": true, "forward_adj_correct": true, "valid": true}}}, "timestamp": "2025-07-23T23:57:01.238602"}, "comparison": {"both_successful": true, "performance_comparison": {"new_time": 52.90667271614075, "original_time": 97.89416742324829, "time_difference": -44.987494707107544, "performance_change_percent": -45.955235016814434, "new_is_faster": true}, "output_comparison": {"files_match": true, "size_match": true, "lines_match": true}, "functionality_equivalent": true}, "backup_mapping": {"day_0_000617_20150101-20250731.txt": "day_0_000617_20150101-20250731.txt.backup_20250723_235425"}}