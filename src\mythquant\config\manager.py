#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置管理器模块

提供统一的配置管理、验证和访问接口。
"""

import os
from typing import Dict, Any, Optional, Union
from pathlib import Path

from .user_settings import (
    DEBUG, VERBOSE_MODE, SMART_FILE_SELECTOR, TDX_CONFIG, 
    OUTPUT_CONFIG, DATA_PROCESSING, INTELLIGENT_FEATURES,
    ERROR_HANDLING, USER_INTERFACE, LEGACY_MAPPING
)


class ConfigManager:
    """配置管理器 - 统一配置访问接口"""
    
    def __init__(self):
        """初始化配置管理器"""
        self._config_cache = {}
        self._load_configurations()
    
    def _load_configurations(self):
        """加载所有配置"""
        self._config_cache = {
            'debug': DEBUG,
            'verbose_mode': VERBOSE_MODE,
            'smart_file_selector': SMART_FILE_SELECTOR,
            'tdx': TDX_CONFIG,
            'output_config': OUTPUT_CONFIG,
            'data_processing': DATA_PROCESSING,
            'intelligent_features': INTELLIGENT_FEATURES,
            'error_handling': ERROR_HANDLING,
            'user_interface': USER_INTERFACE,
        }
    
    # ==================== 通用配置访问 ====================
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config_cache
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        config = self._config_cache
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    # ==================== TDX配置 ====================
    
    def get_tdx_path(self) -> str:
        """获取TDX主路径"""
        return self.get('tdx.tdx_path', '')
    
    def get_tdx_test_path(self) -> str:
        """获取TDX测试路径"""
        return self.get('tdx.tdx_test', '')
    
    def get_minute_paths(self) -> str:
        """获取分钟线路径配置"""
        return self.get('tdx.tdx_min_path', '')
    
    def get_day_paths(self) -> str:
        """获取日线路径配置"""
        return self.get('tdx.tdx_day_path', '')
    
    def get_target_stocks_file(self) -> Optional[str]:
        """获取目标股票文件路径"""
        return self.get('tdx.目标股票代码')
    
    def get_gbbq_path(self) -> str:
        """获取GBBQ文件路径"""
        return self.get('tdx.csv_gbbq', 'T0002/hq_cache/gbbq')
    
    # ==================== 输出配置 ====================
    
    def get_output_path(self) -> str:
        """获取输出路径"""
        return self.get('output_config.base_output_path', '')
    
    def is_console_enabled(self) -> bool:
        """检查是否启用控制台输出"""
        return self.get('output_config.enable_console', False)
    
    def get_file_encoding(self) -> str:
        """获取文件编码"""
        return self.get('output_config.file_encoding', 'utf-8')
    
    # ==================== 调试和日志配置 ====================
    
    def is_debug_enabled(self) -> bool:
        """检查是否启用调试模式"""
        return self.get('debug', False)
    
    def is_verbose_enabled(self) -> bool:
        """检查是否启用详细日志"""
        return self.get('verbose_mode.enabled', False)
    
    def should_show_forward_adj_details(self) -> bool:
        """是否显示前复权处理详情"""
        return self.get('verbose_mode.show_forward_adj_details', False)
    
    def should_show_performance_warnings(self) -> bool:
        """是否显示性能警告"""
        return self.get('verbose_mode.show_performance_warnings', False)
    
    # ==================== 智能功能配置 ====================
    
    def is_smart_file_selector_enabled(self) -> bool:
        """检查是否启用智能文件选择器"""
        return self.get('smart_file_selector.enabled', True)
    
    def get_file_selector_strategy(self) -> str:
        """获取文件选择策略"""
        return self.get('smart_file_selector.default_strategy', 'smart_comprehensive')
    
    def is_incremental_download_enabled(self) -> bool:
        """检查是否启用增量下载"""
        return self.get('intelligent_features.incremental_download.enabled', True)
    
    def is_transparent_processing_enabled(self) -> bool:
        """检查是否启用透明数据处理"""
        return self.get('data_processing.transparent_processing.enabled', True)
    
    # ==================== 错误处理配置 ====================
    
    def is_auto_recovery_enabled(self) -> bool:
        """检查是否启用自动恢复"""
        return self.get('error_handling.auto_recovery', True)
    
    def is_detailed_logging_enabled(self) -> bool:
        """检查是否启用详细错误日志"""
        return self.get('error_handling.detailed_logging', True)
    
    # ==================== 用户界面配置 ====================
    
    def get_display_level(self) -> str:
        """获取显示级别"""
        return self.get('user_interface.display_level', 'normal')
    
    def is_progress_bar_enabled(self) -> bool:
        """检查是否启用进度条"""
        return self.get('user_interface.progress_bar', True)
    
    def is_emoji_enabled(self) -> bool:
        """检查是否启用表情符号"""
        return self.get('user_interface.emoji_enabled', True)
    
    # ==================== 向后兼容性 ====================
    
    def get_legacy_config(self, key: str, default: Any = None) -> Any:
        """获取遗留配置（向后兼容）"""
        return LEGACY_MAPPING.get(key, default)
    
    # ==================== 配置验证 ====================
    
    def validate_config(self) -> Dict[str, Any]:
        """验证配置的有效性"""
        validation_results = {
            'valid': True,
            'errors': [],
            'warnings': []
        }
        
        # 验证TDX路径
        tdx_path = self.get_tdx_path()
        if tdx_path and not os.path.exists(tdx_path):
            validation_results['warnings'].append(f"TDX路径不存在: {tdx_path}")
        
        # 验证输出路径
        output_path = self.get_output_path()
        if output_path:
            output_dir = os.path.dirname(output_path)
            if not os.path.exists(output_dir):
                validation_results['warnings'].append(f"输出目录不存在: {output_dir}")
        
        # 验证权重配置
        weights = self.get('smart_file_selector.scoring_weights', {})
        if weights:
            total_weight = sum(weights.values())
            if abs(total_weight - 1.0) > 0.01:
                validation_results['errors'].append(f"智能文件选择器权重总和应为1.0，当前为: {total_weight}")
                validation_results['valid'] = False
        
        return validation_results

    # ==================== 兼容性方法 ====================

    def get_time_ranges(self) -> Dict[str, Any]:
        """获取时间范围配置（兼容性方法）"""
        try:
            import user_config
            return getattr(user_config, 'time_ranges', {})
        except Exception:
            return {}

    def get_task_configs(self) -> list:
        """获取任务配置（兼容性方法）"""
        try:
            import user_config
            time_ranges = getattr(user_config, 'time_ranges', {})

            task_configs = []

            # 分钟级数据任务配置
            minute_config = time_ranges.get('internet_minute', {})
            if minute_config.get('enabled', False):
                # 转换日期格式：20250101 -> 2025-01-01
                start_date = minute_config.get('start_date', '20250101')
                end_date = minute_config.get('end_date', '20250803')

                start_time = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
                end_time = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

                task_configs.append({
                    'name': '分钟级数据生成',
                    'data_type': 'minute',  # 暂时使用minute类型，但在处理中调用四步流程
                    'enabled': True,
                    'start_time': start_time,
                    'end_time': end_time,
                    'use_multithread': True,
                    'max_workers': 4,
                    'frequency': minute_config.get('frequency', '1min'),
                    'use_structured_flow': True  # 标记使用结构化四步流程
                })

            # 日级数据任务配置
            daily_config = time_ranges.get('internet_daily', {})
            if daily_config.get('enabled', False):
                # 转换日期格式
                start_date = daily_config.get('start_date', '20240101')
                end_date = daily_config.get('end_date', '20241231')

                start_time = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
                end_time = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

                task_configs.append({
                    'name': '日级数据生成',
                    'data_type': 'daily',
                    'enabled': True,
                    'start_time': start_time,
                    'end_time': end_time,
                    'use_multithread': True,
                    'max_workers': 4,
                    'frequency': 'daily'
                })

            # 如果没有启用的任务，返回默认配置（使用当前时间）
            if not task_configs:
                import datetime
                today = datetime.datetime.now()
                yesterday = today - datetime.timedelta(days=1)

                task_configs = [
                    {
                        'name': '分钟级数据生成',
                        'data_type': 'minute',  # 暂时使用minute类型，但在处理中调用四步流程
                        'enabled': True,
                        'start_time': yesterday.strftime('%Y-%m-%d'),
                        'end_time': today.strftime('%Y-%m-%d'),
                        'use_multithread': True,
                        'max_workers': 4,
                        'frequency': '1min'
                    }
                ]

            return task_configs

        except Exception as e:
            # 回退到默认配置（使用当前时间）
            import datetime
            today = datetime.datetime.now()
            yesterday = today - datetime.timedelta(days=1)

            return [
                {
                    'name': '分钟级数据生成',
                    'data_type': 'minute',
                    'enabled': True,
                    'start_time': yesterday.strftime('%Y-%m-%d'),
                    'end_time': today.strftime('%Y-%m-%d'),
                    'use_multithread': True,
                    'max_workers': 4,
                    'frequency': '1min'
                }
            ]

    def highlight_critical_info(self, message: str) -> str:
        """高亮显示关键信息（兼容性方法）"""
        return f"🔥 {message}"


# 全局配置管理器实例
config_manager = ConfigManager()

# 导出
__all__ = ['ConfigManager', 'config_manager']
