#!/usr/bin/python
# -*- coding: UTF-8 -*-
"""
功能：用户配置文件
作者：myth
创建时间：20240101
"""

# 配置部分开始
from pandas._config.config import F
import os
import traceback

# =============================================================================
# 环境检测和配置
# =============================================================================

def detect_environment():
    """
    检测当前运行环境
    Returns:
        str: 'test' 或 'production'
    """
    # 强制AI调试模式检测
    if os.environ.get('FORCE_TEST_ENV') == '1':
        return 'test'

    current_path = os.getcwd()

    # 检测是否在测试环境中
    if 'test_environments' in current_path:
        return 'test'

    # 检测是否是AI调试模式（通过调用栈检测）
    stack = traceback.format_stack()
    ai_debug_indicators = [
        'tools/', 'test_', 'debug_', 'detector', 'tracker',
        'precise_', 'simple_', 'correct_', 'find_early',
        'str-replace-editor', 'launch-process', 'augment',
        'codebase-retrieval', 'view', 'save-file'
    ]

    for frame in stack:
        if any(indicator in frame for indicator in ai_debug_indicators):
            return 'test'

    # 检测是否通过AI工具调用（检查进程名和环境变量）
    import sys
    if hasattr(sys, 'ps1') or 'augment' in str(sys.argv) or 'AUGMENT_MODE' in os.environ:
        return 'test'

    return 'production'

# 当前环境
CURRENT_ENVIRONMENT = detect_environment()

# =============================================================================
# 基于任务的环境配置
# =============================================================================

# 测试环境配置
test_environments = {
    # 1分钟数据下载任务的测试环境
    'minute_data_download': {
        'base_path': 'test_environments/minute_data_tests',
        'input_data_path': 'test_environments/minute_data_tests/input_data',
        'output_data_path': 'test_environments/minute_data_tests/output_data',
        'backup_data_path': 'test_environments/minute_data_tests/backup_data',
        'expected_data_path': 'test_environments/minute_data_tests/expected_data',
        'config_path': 'test_environments/minute_data_tests/configs',

        # 测试文件配置
        'test_files': {
            '000617': {
                'original_file': 'test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt',  # 原始测试文件（只读）
                'working_file': 'working_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt',  # 工作副本（可修改）
                'incomplete_file': 'test_incomplete_1min_0_000617_20250320-20250704.txt',  # 有缺失数据的测试文件
                'backup_file': 'backup_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt',   # 备份文件
                'date_range': {
                    'start_date': '20250320',
                    'end_date': '20250704'
                }
            }
        },

        # 测试环境管理配置
        'test_management': {
            'auto_backup': True,        # 测试前自动备份
            'auto_restore': True,       # 测试后自动还原
            'preserve_results': True,   # 保留测试结果到专门目录
            'cleanup_on_exit': False    # 退出时是否清理工作文件
        },

        # 智能文件选择器测试配置
        'smart_file_selector': {
            'force_test_file': True,  # 强制使用测试文件
            'ignore_production_files': True,  # 忽略生产环境文件
            'test_file_priority': 'highest'  # 测试文件优先级
        }
    },

    # 日线数据处理任务的测试环境
    'daily_data_processing': {
        'base_path': 'test_environments/daily_data_tests',
        'input_data_path': 'test_environments/daily_data_tests/input_data',
        'output_data_path': 'test_environments/daily_data_tests/output_data',
        'backup_data_path': 'test_environments/daily_data_tests/backup_data',
        'expected_data_path': 'test_environments/daily_data_tests/expected_data',
    }
}

# 生产环境配置
production_environments = {
    'minute_data_download': {
        'base_path': '.',
        'input_data_path': 'H:/MPV1.17/T0002/signals',
        'output_data_path': 'H:/MPV1.17/T0002/signals',
        'backup_data_path': './backup',
    },

    'daily_data_processing': {
        'base_path': '.',
        'input_data_path': 'H:/MPV1.17/T0002/signals',
        'output_data_path': 'H:/MPV1.17/T0002/signals',
        'backup_data_path': './backup',
    }
}

def get_environment_config(task_name='minute_data_download'):
    """
    获取指定任务的环境配置

    Args:
        task_name: 任务名称，如 'minute_data_download', 'daily_data_processing'

    Returns:
        dict: 环境配置
    """
    if CURRENT_ENVIRONMENT == 'test':
        return test_environments.get(task_name, test_environments['minute_data_download'])
    else:
        return production_environments.get(task_name, production_environments['minute_data_download'])

debug = False  # 是否开启调试日志输出  开=True  关=False

# 详细模式配置 (2025-07-31: 禁用以避免与结构化输出重复)
verbose_mode = {
    'enabled': False,  # 生产环境禁用详细模式，使用结构化输出格式器
    'show_forward_adj_details': False,  # 显示前复权处理详情
    'show_performance_warnings': False,  # 显示性能警告
    'show_data_processing_steps': False,  # 显示数据处理步骤
    'show_cache_status': False,  # 显示缓存状态详情
    'highlight_critical_info': False,  # 高亮关键信息
    'show_detailed_calculations': False,  # 显示详细的计算步骤（前复权除权除息计算过程）

    # 注意：如需调试，可临时启用特定类别的详细输出
    # 'enabled': True,  # 调试时取消注释
}

# 智能文件选择器配置（对所有股票生效）
smart_file_selector = {
    # 基础设置
    'enabled': True,  # 是否启用智能文件选择器（True=启用，False=使用传统的第一个匹配文件）

    # 默认选择策略
    'default_strategy': 'smart_comprehensive',  # 默认文件选择策略
    # 可选策略：
    # - 'latest_first': 最新优先策略（选择结束日期最新的文件）
    # - 'max_coverage': 最大覆盖策略（选择时间范围最大的文件）
    # - 'best_match': 最佳匹配策略（选择与目标时间范围最匹配的文件）
    # - 'smart_comprehensive': 智能综合策略（综合评分最高的文件，推荐）

    # 评分权重配置（仅在smart_comprehensive策略下生效）
    'scoring_weights': {
        'freshness_weight': 0.3,    # 新鲜度权重（0.0-1.0）：数据距离现在越近评分越高
        'coverage_weight': 0.4,     # 覆盖度权重（0.0-1.0）：时间范围越大评分越高
        'match_weight': 0.3,        # 匹配度权重（0.0-1.0）：与目标范围重叠度越高评分越高
        # 注意：三个权重之和应该等于1.0
    },

    # 文件冲突处理设置
    'conflict_resolution': {
        'show_candidates': True,     # 是否显示所有候选文件的详细信息
        'show_selection_reason': True,  # 是否显示选择原因
        'log_detailed_analysis': True,  # 是否记录详细的分析过程
        'user_confirmation_required': False,  # 是否需要用户确认选择（True=暂停询问用户）
    },

    # 文件重命名设置
    'auto_rename': {
        'enabled': True,             # 是否启用自动重命名（更新后根据实际时间范围重命名文件）
        'backup_before_rename': True,  # 重命名前是否备份原文件
        'show_rename_details': True,   # 是否显示重命名详情
    },

    # 高级设置
    'advanced': {
        'freshness_max_days': 365,   # 新鲜度计算的最大天数（超过此天数的文件新鲜度为0）
        'match_bonus_for_full_coverage': 0.2,  # 完全覆盖目标范围时的额外奖励分数
        'min_overlap_ratio': 0.1,    # 最小重叠比例（低于此比例的文件不考虑）
        'enable_file_size_factor': False,  # 是否考虑文件大小因素（大文件可能数据更完整）
    },

    # 策略特定设置
    'strategy_specific': {
        # 最新优先策略设置
        'latest_first': {
            'prefer_recent_modify_time': True,  # 当结束日期相同时，优先选择修改时间最新的文件
        },

        # 最大覆盖策略设置
        'max_coverage': {
            'prefer_earlier_start': True,      # 当覆盖天数相同时，优先选择开始日期更早的文件
        },

        # 最佳匹配策略设置
        'best_match': {
            'penalty_for_excess_data': 0.1,   # 超出目标范围的数据的惩罚系数
            'bonus_for_exact_match': 0.3,     # 精确匹配目标范围的奖励系数
        }
    },

    # 调试设置
    'debug': {
        'save_analysis_report': False,  # 是否保存详细的分析报告到文件
        'report_save_path': 'logs/file_selection_reports',  # 分析报告保存路径
        'enable_performance_timing': False,  # 是否启用性能计时
    }
}

# 输出目录配置
output_config = {
    'base_output_path': 'H:/MPV1.17/T0002/signals',  # 基础输出路径
    'use_subdirectories': False,  # 是否使用子目录（True=使用子目录，False=直接输出到基础路径）
    'custom_subdirs': {  # 自定义子目录名称（当use_subdirectories=True时使用）
        'minute': 'minute_data',
        'daily': 'daily_data',
        'weekly': 'weekly_data'
    }
}

# 目录需要事先手动建立好，不然程序会出错
tdx = {
    # 'tdx_path': 'D:/Program Files/new_mpv1.08（零碎人生2）',  # 指定通达信目录
    # 'tdx_path': 'D:/Program Files/new_mpv1.16',  # 指定通达信目录
    # 'tdx_path': 'D:/Program Files/专业研究版V7.641（财富大数据）',  # 指定通达信目录 #20230709变更到此
    'tdx_path': 'H:/MPV1.17',  # 指定通达信目录 #20250629变更到此
    'tdx_min_path': '/vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline',  # 指定通达信分钟线的相对路径（基于tdx_path，沪深京三个市场，分号分隔）
    'tdx_day_path': '/vipdoc/sz/lday;/vipdoc/sh/lday;/vipdoc/bj/lday',  # 指定通达信日线的相对路径（基于tdx_path，沪深京三个市场，分号分隔）
    'tdx_test': 'D:/TDXdata/TDX_ROOT',  # 20220320：指定通达信测试用临时目录，存放测试数据
    'csv_lday': 'D:/TDXdata/csv_day',  # 指定csv格式日线数据保存目录
    'csv_min': 'D:/TDXdata/csv_min',  # 指定csv格式日线数据保存目录
    'pickle': 'D:/TDXdata/pickle',  # 指定pickle格式日线数据保存目录
    'csv_index': 'D:/TDXdata/index',  # 指定指数保存目录
    'csv_cw': 'D:/TDXdata/cw',  # 指定专业财务保存目录
    'csv_gbbq': 'T0002/hq_cache/gbbq',  # 指定股本变迁保存的具体文件相对路径（注意：gbbq是一个不带任何后缀名的文件而不是目录）
    'pytdx_ip': '*************',  # 指定pytdx的通达信服务器IP（自动检测更新）
    'pytdx_port': 7709,  # 指定pytdx的通达信服务器端口。int类型
    'pytdx_auto_detect': True,  # 是否在程序启动时自动检测最佳服务器IP
    'pytdx_show_top5': True,  # 是否显示响应速度最快的Top5服务器信息
    'pytdx_smart_detect': True,  # 智能检测：只检测上次通过的服务器（提高效率）
    'pytdx_blacklist_enabled': True,  # 启用服务器黑名单管理
    'pytdx_blacklist_timeout': 7200,  # 黑名单超时时间（秒），超时后重新尝试

    # K线数量配置
    # 注意：pytdx服务器对1分钟数据有历史范围限制（约100个交易日）
    # 测试发现：1000条请求会被服务器拒绝，800条及以下正常
    'pytdx_kline_limits': {
        '1min': 800,    # 1分钟K线最大获取数量（测试验证：1000条会被拒绝）
        '5min': 800,    # 5分钟K线最大获取数量
        '15min': 800,   # 15分钟K线最大获取数量
        '30min': 800,   # 30分钟K线最大获取数量
        '60min': 800,   # 60分钟K线最大获取数量
        'daily': 800,   # 日线K线最大获取数量
    },

    # 数据量计算缓冲区系数
    # 用于智能计算需要下载的数据量时的缓冲区
    # 1.0 = 严格按交易日计算，1.2 = 增加20%缓冲区
    'pytdx_data_buffer_factor': 1.0,  # 默认1.0，严格计算不添加缓冲区
    '目标股票代码': 'C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx'  # 目标股票代码Excel文件路径
}

# ==================== 时间范围配置 ====================
time_ranges = {
    # TDX日线数据时间范围
    'tdx_daily': {
        'enabled': False,           # 是否启用此数据类型
        'start_date': '20200101',  # TDX日线数据开始日期
        'end_date': '20250724',    # TDX日线数据结束日期
        'description': 'TDX本地日线数据处理的时间范围'
    },

    # TDX周线数据时间范围
    'tdx_weekly': {
        'enabled': False,          # 是否启用此数据类型
        'start_date': '20150101',  # TDX周线数据开始日期
        'end_date': '20250731',    # TDX周线数据结束日期
        'description': 'TDX本地周线数据处理的时间范围'
    },

    # TDX月线数据时间范围
    'tdx_monthly': {
        'enabled': False,          # 是否启用此数据类型
        'start_date': '20150101',  # TDX月线数据开始日期
        'end_date': '20250731',    # TDX月线数据结束日期
        'description': 'TDX本地月线数据处理的时间范围'
    },

    # TDX分钟级数据时间范围（通常时间范围较短）
    'tdx_minute': {
        'enabled': False,          # 是否启用此数据类型
        'start_date': '20250101',  # TDX分钟数据开始日期
        'end_date': '20250723',    # TDX分钟数据结束日期
        'description': 'TDX本地分钟级数据处理的时间范围'
    },

    # 互联网日线数据下载时间范围
    'internet_daily': {
        'enabled': False,           # 是否启用此数据类型
        'start_date': '20170101',  # 互联网日线数据开始日期
        'end_date': '20250720',    # 互联网日线数据结束日期 ('current'表示当前日期，也可以指定具体日期如'20250731')
        'description': '互联网日线数据下载的时间范围'
    },

    # 互联网分钟级数据下载时间范围
    'internet_minute': {
        'enabled': True,         # 是否启用此数据类型（默认关闭，因为数据量大）
        'start_date': '20250101',  # 互联网分钟数据开始日期（调整为最近3-4个月，符合pytdx限制）
        'end_date': '20250807',     # 互联网分钟数据结束日期
        'frequency': '1min',       # 分钟频率：1min(AKShare支持), 5min, 15min, 30min, 60min
        'description': '互联网分钟级数据下载的时间范围（pytdx约支持最近100个交易日）'
    }
}

# ==================== 互联网数据下载配置 ====================
internet_data_config = {
    # 是否启用互联网数据下载
    'enabled': True,

    # 数据源优先级 (1=最高优先级)
    # 注意：pytdx支持真正的1分钟数据，无需数据库
    'data_sources': {
        'pytdx': {'enabled': True, 'priority': 1, 'auto_server_discovery': True},      # 优先使用pytdx（支持1分钟，自动服务器发现）
        'akshare': {'enabled': True, 'priority': 2},    # 备用AKShare（支持1分钟但不稳定）
        'baostock': {'enabled': True, 'priority': 3},   # 备用BaoStock（最小5分钟）
        'yfinance': {'enabled': False, 'priority': 4}   # 主要用于港股美股
    },

    # 分数据源配置参数
    'data_source_configs': {
        # pytdx配置
        'pytdx': {
            'request_delay': 0.2,      # pytdx请求间隔（秒）- pytdx服务器响应快，可以设置较短间隔
            'max_retries': 3,          # 最大重试次数
            'timeout': 10,             # 请求超时（秒）- pytdx响应快，设置较短超时
            'batch_size': 800,         # 每次请求的数据量 - 根据调研，800条是安全的批量大小
            'auto_server_discovery': True,  # 是否自动发现最佳服务器
            'server_test_timeout': 3,  # 服务器测试超时（秒）
        },

        # AKShare配置
        'akshare': {
            'request_delay': 0.5,      # AKShare请求间隔（秒）- 根据调研：每分钟200次请求限制
            'max_retries': 3,          # 最大重试次数
            'timeout': 30,             # 请求超时（秒）- AKShare可能较慢
            'batch_size_daily': 100,   # 日线数据批量大小（天数）
            'batch_size_minute': 5,    # 分钟数据批量大小（天数）- 分钟数据量大，小批量处理
            'enable_compression': True, # 是否启用数据压缩
            'validate_data_integrity': True, # 是否验证数据完整性
        },

        # BaoStock配置
        'baostock': {
            'request_delay': 1.0,      # BaoStock请求间隔（秒）- BaoStock相对保守
            'max_retries': 3,          # 最大重试次数
            'timeout': 30,             # 请求超时（秒）
            'batch_size': 50,          # 每次请求的数据量 - BaoStock建议较小批量
            'login_retry': 3,          # 登录重试次数
            'session_timeout': 3600,   # 会话超时（秒）
        },

        # YFinance配置（主要用于港股美股）
        'yfinance': {
            'request_delay': 1.0,      # YFinance请求间隔（秒）
            'max_retries': 3,          # 最大重试次数
            'timeout': 30,             # 请求超时（秒）
            'batch_size': 20,          # 每次请求的股票数量
            'enable_prepost': False,   # 是否包含盘前盘后数据
        }
    },

    # 输出配置
    'output': {
        'directory': 'H:/MPV1.17/T0002/signals/',  # 输出目录
        'filename_suffix': '_来源互联网',             # 文件名后缀
        'encoding': 'utf-8'                        # 文件编码
    },

    # 数据质量控制
    'quality_control': {
        'min_data_points': 5,      # 最少数据点数（降低要求）
        'price_diff_threshold': 0.01,  # 前复权价格差异阈值（更敏感）
        'validate_stock_code': True,     # 是否验证股票代码格式
        'strict_mode': False       # 是否启用严格模式（失败时拒绝保存数据）
    }
}



# 前复权算法优化配置
forward_adjustment_config = {
    # 精度策略（供main程序使用）
    'precision_strategy': 'high_precision',  # 'high_precision', 'tdx_compatible', 'standard'

    # 数据质量配置
    'data_quality': {
        'enhanced_validation': True,  # 是否启用增强数据验证
        'outlier_detection': True,   # 是否检测异常值
        'duplicate_removal': True,   # 是否移除重复数据
        'future_event_filter': True, # 是否过滤未来事件
    },

    # 性能优化配置
    'performance': {
        'cache_enabled': True,       # 是否启用缓存
        'batch_processing': True,    # 是否启用批量处理
        'parallel_processing': False, # 是否启用并行处理
        'memory_optimization': True,  # 是否启用内存优化
    },

    # 调试和监控配置
    'monitoring': {
        'quality_metrics': False,    # 是否计算质量指标
        'performance_stats': False,  # 是否统计性能数据
        'detailed_logging': False,   # 是否启用详细日志
        'benchmark_comparison': False, # 是否与基准对比
    }
}

# 配置部分结束

# ==================== 精度控制配置 ====================
# 用于控制除权参考价和复权因子计算精度
precision_config = {
    # 基础精度设置
    'calculation_precision': 20,  # 计算过程中的总精度位数（建议10-20位）
    
    # 除权参考价精度配置
    'ex_dividend_price': {
        'decimal_places': 6,     # 除权参考价小数位数（默认6位）
        'round_method': 'ROUND_HALF_UP',  # 舍入方法：ROUND_HALF_UP, ROUND_DOWN, ROUND_UP等
        'intermediate_round': False,      # 是否对中间结果进行舍入
        'display_precision': 4,           # 显示和打印时的精度（默认4位小数）
    },
    
    # 复权因子精度配置
    'adjustment_factor': {
        'single_factor_places': 12,      # 单次复权因子小数位数（默认12位）
        'cumulative_factor_places': 18,  # 累计复权因子小数位数（默认15位）
        'round_method': 'ROUND_HALF_UP',  # 舍入方法
        'intermediate_round': False,      # 是否对中间计算结果进行舍入
        'display_precision': 8,           # 显示时的精度（默认8位小数）
    },
    
    # 分红数据精度配置
    'dividend_data': {
        'per_share_places': 10,          # 每股分红金额精度（默认10位）
        'ratio_places': 12,              # 送转股比例精度（默认12位）
        'price_places': 6,               # 配股价格精度（默认6位）
        'normalize_precision': 3,        # 数据标准化时的精度（默认3位：到毫）
    },
    
    # 持久化存储精度配置
    'persistence': {
        'ex_price_storage_places': 6,    # 除权参考价存储精度
        'factor_storage_places': 12,     # 复权因子存储精度
        'csv_output_places': 4,          # CSV输出文件精度
        'database_places': 8,            # 数据库存储精度
    },
    
    # 高级精度策略
    'advanced_options': {
        'use_decimal_calculation': True,  # 是否使用Decimal进行高精度计算
        'error_tolerance': 1e-10,        # 数值误差容忍度
        'consistency_check': True,       # 是否进行一致性检查
        'precision_validation': True,    # 是否验证精度设置的合理性
    },
    
    # 特定股票精度策略（用于处理特殊情况）
    'stock_specific': {
        'enabled': False,                # 是否启用股票特定精度策略
        'stock_configs': {
            # 示例：'000617': {'factor_places': 15, 'ex_price_places': 8}
        }
    }
}

# ==================== AKShare专项配置 ====================
# 基于深度调研结果的AKShare优化配置
akshare_config = {
    # API限制和性能优化
    'api_limits': {
        'requests_per_minute': 200,    # AKShare官方限制：每分钟200次请求
        'requests_per_day': 100000,    # AKShare官方限制：每天10万次请求
        'optimal_delay': 0.5,          # 最优请求间隔（秒）：60/200 = 0.3秒，设置0.5秒更安全
        'conservative_delay': 1.0,     # 保守请求间隔（秒）：网络不稳定时使用
        'burst_delay': 2.0,            # 突发请求后的延迟（秒）：避免触发限制
    },

    # 重试策略配置
    'retry_strategy': {
        'max_retries': 3,              # 最大重试次数
        'backoff_factor': 2.0,         # 指数退避因子：每次重试间隔翻倍
        'initial_backoff': 1.0,        # 初始退避时间（秒）
        'max_backoff': 30.0,           # 最大退避时间（秒）
        'retry_on_errors': [           # 需要重试的错误类型
            'ConnectionError',
            'Timeout',
            'HTTPError',
            'RequestException'
        ]
    },

    # 数据获取优化
    'data_optimization': {
        'batch_size_daily': 100,       # 日线数据批量大小（天数）
        'batch_size_minute': 5,        # 分钟数据批量大小（天数）- 分钟数据量大，小批量处理
        'enable_compression': True,    # 是否启用数据压缩
        'cache_responses': True,       # 是否缓存API响应
        'validate_data_integrity': True, # 是否验证数据完整性
    },

    # 前复权数据配置
    'forward_adjustment': {
        'always_request_qfq': True,    # 是否总是请求前复权数据
        'fallback_to_original': True,  # 前复权失败时是否回退到原始数据
        'validate_qfq_difference': True, # 是否验证前复权价格差异
        'min_price_difference': 0.001, # 最小价格差异阈值（元）
        'log_qfq_issues': True,        # 是否记录前复权问题
    },

    # 网络和连接配置
    'network': {
        'connection_timeout': 30,      # 连接超时（秒）
        'read_timeout': 60,            # 读取超时（秒）
        'max_connections': 10,         # 最大并发连接数
        'keep_alive': True,            # 是否保持连接
        'user_agent': 'MythQuant/1.0', # 用户代理字符串
    },

    # 错误处理配置
    'error_handling': {
        'log_all_errors': True,        # 是否记录所有错误
        'raise_on_critical': True,     # 关键错误时是否抛出异常
        'continue_on_partial_failure': True, # 部分失败时是否继续
        'error_notification': False,   # 是否发送错误通知
    },

    # 调试和监控
    'monitoring': {
        'track_api_usage': True,       # 是否跟踪API使用情况
        'log_request_details': False,  # 是否记录请求详情（调试用）
        'performance_metrics': True,   # 是否收集性能指标
        'success_rate_threshold': 0.8, # 成功率阈值（低于此值时告警）
    }
}

# ==================== 测试环境配置 (2025-07-28) ====================
test_environment_config = {
    # 测试模式开关
    'enabled': False,  # 是否启用测试模式
    'auto_setup': True,  # 是否自动设置测试环境

    # 测试目录配置
    'base_directory': r'H:\MPV1.17\T0002\signals\TestCase\01',
    'input_directory': r'H:\MPV1.17\T0002\signals\TestCase\01\input',
    'output_directory': r'H:\MPV1.17\T0002\signals\TestCase\01\output',
    'expected_directory': r'H:\MPV1.17\T0002\signals\TestCase\01\expected',
    'backup_directory': r'H:\MPV1.17\T0002\signals\TestCase\01\backup',

    # 测试数据配置
    'copy_from_production': True,  # 是否从生产环境复制测试数据
    'test_file_prefix': 'test_',   # 测试文件前缀
    'min_file_size': 1024,         # 最小文件大小（字节）
    'production_source': r'H:\MPV1.17\T0002\signals',  # 生产环境数据源

    # 测试验证配置
    'compare_with_expected': True,  # 是否与预期结果比较
    'size_tolerance_percent': 5.0,  # 文件大小容差百分比
    'generate_report': True,        # 是否生成测试报告
    'validate_data_quality': True,  # 是否验证数据质量

    # 测试清理配置
    'cleanup_before_test': True,   # 测试前是否清理
    'backup_results': True,        # 是否备份测试结果
    'keep_backup_days': 7,         # 保留备份天数

    # 轻量级验证配置
    'validation_data_limit': 1200,  # 验证阶段最大数据量
    'validation_timeout': 5,        # 验证超时时间（秒）
    'quick_validation': True,       # 是否启用快速验证模式
}

# 配置部分结束
