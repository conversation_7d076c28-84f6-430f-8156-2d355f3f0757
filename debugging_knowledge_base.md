# MythQuant调试知识库

## 测试与生产环境不一致问题

### 问题模式：测试通过但生产报错 (2025-08-06)

**典型症状**：
- 单元测试或组件测试全部通过
- 主程序运行时出现 `ModuleNotFoundError`、`AttributeError` 等错误
- 配置在测试环境可用但生产环境失败

**根本原因分析**：
1. **导入路径差异**：测试环境和生产环境的Python路径配置不同
2. **多重实现问题**：项目中存在多个同名类，测试时调用了不同的实现
3. **依赖模块缺失**：某些模块在测试时从归档文件加载，但生产时不存在
4. **上下文差异**：配置加载在不同上下文中的行为不一致

**诊断方法**：
```python
# 1. 检查导入路径
import sys
print("Python路径:", sys.path)

# 2. 检查模块位置
import inspect
print("模块位置:", inspect.getfile(ModuleName))

# 3. 检查类的所有实现
import pkgutil
for importer, modname, ispkg in pkgutil.walk_packages():
    if 'ConfigManager' in modname:
        print(f"发现ConfigManager: {modname}")
```

**解决方案**：
1. **统一导入路径**：使用绝对导入，确保项目根目录在Python路径中
2. **多重实现统一**：识别所有同名类，确保接口一致
3. **依赖完整性检查**：从归档中恢复缺失的模块
4. **端到端测试**：在生产环境中进行完整测试

### 具体案例：ConfigManager方法缺失 (2025-08-06)

**错误信息**：
```
'ConfigManager' object has no attribute 'get_internet_data_config'
```

**问题分析**：
- 项目中存在多个ConfigManager类：
  - `src.mythquant.config.manager.ConfigManager`
  - `src.mythquant.core.config_manager.ConfigManager`
- 新方法只在部分实现中添加
- 不同模块调用了不同的ConfigManager

**解决步骤**：
1. 识别所有ConfigManager实现
2. 在每个实现中添加缺失的方法
3. 逐步统一到DDD架构的配置管理器
4. 使用接口或抽象基类确保一致性

## 依赖模块管理问题

### 问题模式：运行时模块缺失 (2025-08-06)

**典型症状**：
```
No module named 'utils.incremental_downloader'
ModuleNotFoundError: No module named 'core'
```

**根本原因**：
1. 模块被误删或移动到归档目录
2. 依赖关系没有正确维护
3. 模块重构时遗漏了某些文件

**解决方案**：
1. **从归档恢复**：
   ```bash
   cp "archive/path/to/module.py" utils/
   ```
2. **依赖关系检查**：建立完整的依赖关系图
3. **自动化检测**：使用工具检测依赖完整性

### 具体案例：incremental_downloader缺失 (2025-08-06)

**问题**：主程序运行时提示 `No module named 'utils.incremental_downloader'`

**解决**：
```bash
# 从归档中恢复缺失的模块
cp "archive/backups/.../utils/incremental_downloader.py" utils/
cp "archive/backups/.../utils/incremental_download_validator.py" utils/
cp "archive/backups/.../utils/smart_incremental_analyzer.py" utils/
```

## 导入和路径问题

### 问题模式：导入路径错误 (2025-08-06)

**典型症状**：
```python
from core.config_manager import ConfigManager  # 错误
# 应该是：
from src.mythquant.core.config_manager import ConfigManager
```

**解决原则**：
1. **统一使用绝对导入**
2. **避免动态修改sys.path**
3. **使用标准的包结构**

### 问题模式：Path未定义 (2025-08-06)

**典型症状**：
```
NameError: name 'Path' is not defined
```

**解决方案**：
```python
# 添加导入
from pathlib import Path
```

**预防措施**：
- 在文件开头统一导入所有需要的模块
- 使用IDE的导入检查功能
- 建立导入规范和检查清单

## DDD架构实施经验

### 成功模式：渐进式DDD架构实施 (2025-08-06)

**关键成功要素**：
1. **向后兼容优先**：确保现有代码无需修改
2. **门面模式应用**：隐藏架构复杂性
3. **适配器模式使用**：桥接新旧接口
4. **用户体验保护**：保持配置接口简单

**实施步骤**：
1. 创建完整的DDD分层架构
2. 实现适配器保持向后兼容
3. 逐步迁移功能到新架构
4. 进行全面的测试验证

**测试策略**：
1. **组件测试**：测试各个DDD组件
2. **集成测试**：测试组件间的协作
3. **端到端测试**：测试完整的用户场景
4. **向后兼容测试**：确保旧接口仍然可用

## 配置管理最佳实践

### 统一配置管理架构 (2025-08-06)

**设计原则**：
1. **用户友好性**：保持user_config.py的简单性
2. **内部复杂性隐藏**：使用门面模式
3. **统一接口**：所有ConfigManager提供一致接口
4. **验证机制**：完整的配置验证

**实现模式**：
```python
# 用户层：简单的配置文件
# user_config.py
tdx = {
    'tdx_path': 'path/to/tdx',
    'pytdx_ip': '127.0.0.1'
}

# 架构层：DDD配置管理
class ConfigFacade:
    def get_trading_config(self):
        # 复杂的配置处理逻辑
        pass

# 兼容层：适配器
class LegacyConfigAdapter:
    def get_tdx_path(self):
        return self.facade.get_trading_config()['tdx_path']
```

## 问题预防检查清单

### 架构升级前检查 (2025-08-06)
- [ ] 识别所有相关的类和接口
- [ ] 确保新架构完全向后兼容
- [ ] 准备完整的测试套件
- [ ] 建立回滚机制

### 代码修改前检查 (2025-08-06)
- [ ] 检查是否存在多个同名类
- [ ] 确认所有依赖模块存在
- [ ] 验证导入路径正确性
- [ ] 准备测试和生产环境验证

### 发布前检查 (2025-08-06)
- [ ] 端到端测试通过
- [ ] 生产环境验证通过
- [ ] 向后兼容性确认
- [ ] 文档同步更新

## 调试工具和技巧

### 快速诊断脚本 (2025-08-06)
```python
#!/usr/bin/env python3
"""快速诊断脚本"""

def diagnose_import_issues():
    """诊断导入问题"""
    import sys
    print("Python路径:")
    for path in sys.path:
        print(f"  {path}")
    
    # 检查关键模块
    modules_to_check = [
        'utils.incremental_downloader',
        'src.mythquant.core.config_manager',
        'src.mythquant.config.manager'
    ]
    
    for module in modules_to_check:
        try:
            __import__(module)
            print(f"✅ {module}")
        except ImportError as e:
            print(f"❌ {module}: {e}")

def diagnose_config_managers():
    """诊断ConfigManager问题"""
    managers = []
    
    try:
        from src.mythquant.core.config_manager import ConfigManager as CoreConfigManager
        managers.append(('Core', CoreConfigManager))
    except ImportError:
        pass
    
    try:
        from src.mythquant.config.manager import ConfigManager as MainConfigManager
        managers.append(('Main', MainConfigManager))
    except ImportError:
        pass
    
    for name, manager_class in managers:
        print(f"\n{name} ConfigManager:")
        methods = [m for m in dir(manager_class) if not m.startswith('_')]
        for method in methods:
            print(f"  {method}")

if __name__ == '__main__':
    diagnose_import_issues()
    diagnose_config_managers()
```

### 环境一致性检查 (2025-08-06)
```python
def check_environment_consistency():
    """检查测试和生产环境一致性"""
    import os
    import sys
    
    print("环境信息:")
    print(f"  Python版本: {sys.version}")
    print(f"  工作目录: {os.getcwd()}")
    print(f"  项目根目录: {os.path.dirname(os.path.abspath(__file__))}")
    
    # 检查关键文件存在性
    critical_files = [
        'user_config.py',
        'main.py',
        'utils/incremental_downloader.py',
        'src/mythquant/core/config_manager.py'
    ]
    
    for file_path in critical_files:
        if os.path.exists(file_path):
            print(f"  ✅ {file_path}")
        else:
            print(f"  ❌ {file_path}")
```

这个调试知识库总结了我们在DDD架构实施过程中遇到的所有问题和解决方案，为将来遇到类似问题提供了完整的参考。
