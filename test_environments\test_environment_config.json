{"project_name": "MythQuant", "test_environment_version": "2.0.0", "created_time": "2025-07-30T00:28:06.724098", "structure": {"test_environments": {"description": "统一测试环境根目录", "subdirs": {"unit_tests": {"description": "单元测试环境", "subdirs": ["data", "results", "reports", "configs"]}, "integration_tests": {"description": "集成测试环境", "subdirs": ["data", "results", "reports", "configs"]}, "performance_tests": {"description": "性能测试环境", "subdirs": ["data", "results", "reports", "configs", "benchmarks"]}, "regression_tests": {"description": "回归测试环境", "subdirs": ["data", "results", "reports", "configs", "baselines"]}, "minute_data_tests": {"description": "1分钟数据专项测试环境", "subdirs": ["input_data", "output_data", "expected_data", "backup_data", "results", "configs"]}, "data_quality_tests": {"description": "数据质量测试环境", "subdirs": ["sample_data", "validation_rules", "results", "reports"]}, "shared": {"description": "共享测试资源", "subdirs": ["fixtures", "mocks", "utilities", "templates", "docs"]}}}}, "migration_from": "Scattered directories + TestCase/01", "design_principles": ["测试环境与项目代码同步管理", "按测试类型和功能模块分层组织", "支持CI/CD集成和自动化测试", "便于版本控制和团队协作", "数据安全和环境隔离", "可扩展和可维护"]}