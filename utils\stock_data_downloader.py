#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
股票数据下载器
支持从多个免费数据源下载前复权股票数据
"""

import os
import sys
import time
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Any, Optional, Tuple
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.enhanced_error_handler import get_smart_logger, get_error_handler, ErrorCategory

logger = logging.getLogger(__name__)


class StockDataDownloader:
    """股票数据下载器"""

    def __init__(self, output_dir: str = None):
        """
        初始化下载器

        Args:
            output_dir: 输出目录（如果为None，从配置中读取）
        """
        self.smart_logger = get_smart_logger("StockDataDownloader")
        self.error_handler = get_error_handler()

        # 加载配置
        self._load_config()

        # 设置输出目录
        if output_dir is not None:
            self.output_dir = output_dir
        else:
            self.output_dir = self.config['output']['directory']

        # 确保输出目录存在
        os.makedirs(self.output_dir, exist_ok=True)

        # 初始化数据源
        self._init_data_sources()

    def _load_config(self):
        """加载配置"""
        try:
            from src.mythquant.core.config_manager import ConfigManager
            config_manager = ConfigManager()
            self.config = config_manager.get_internet_data_config()
            # 配置加载成功，不需要输出日志
        except Exception as e:
            self.smart_logger.warning(f"加载配置失败，使用默认配置: {e}")
            # 使用默认配置
            self.config = {
                'enabled': True,
                'data_sources': {
                    'pytdx': {'enabled': True, 'priority': 1, 'auto_server_discovery': True},
                    'baostock': {'enabled': True, 'priority': 2},
                    'akshare': {'enabled': True, 'priority': 3},
                    'yfinance': {'enabled': False, 'priority': 4}
                },
                'download_params': {
                    'start_date': '20150101',
                    'request_delay': 0.5,  # 优化：根据调研结果，AKShare限制每分钟200次，设置0.5秒间隔
                    'max_retries': 3,
                    'timeout': 30
                },
                'output': {
                    'directory': 'H:/MPV1.17/T0002/signals/',
                    'filename_suffix': '_来源互联网',
                    'encoding': 'utf-8'
                },
                'quality_control': {
                    'min_data_points': 10,
                    'price_diff_threshold': 0.01,
                    'validate_stock_code': True
                }
            }
    
    def _init_data_sources(self):
        """初始化数据源（基于配置）"""
        self.data_sources = []

        # 从配置中获取数据源设置
        data_sources_config = self.config.get('data_sources', {})

        # 按优先级排序的数据源列表
        source_configs = []
        for name, config in data_sources_config.items():
            if config.get('enabled', True):
                source_configs.append((name, config))

        # 按优先级排序
        source_configs.sort(key=lambda x: x[1].get('priority', 999))

        # 初始化每个启用的数据源
        for name, config in source_configs:
            try:
                if name == 'pytdx':
                    # 检查pytdx是否可用
                    import pytdx  # noqa: F401
                    from utils.pytdx_downloader import PytdxDownloader
                    self.data_sources.append({
                        'name': 'pytdx',
                        'module': PytdxDownloader,
                        'priority': config.get('priority', 1),
                        'available': True,
                        'config': config
                    })
                    # pytdx数据源初始化成功，不需要输出日志

                elif name == 'baostock':
                    import baostock as bs
                    self.data_sources.append({
                        'name': 'baostock',
                        'module': bs,
                        'priority': config.get('priority', 2),
                        'available': True,
                        'config': config
                    })
                    # BaoStock数据源初始化成功，不需要输出日志

                elif name == 'akshare':
                    import akshare as ak
                    self.data_sources.append({
                        'name': 'akshare',
                        'module': ak,
                        'priority': config.get('priority', 3),
                        'available': True,
                        'config': config
                    })
                    # AKShare数据源初始化成功，不需要输出日志

                elif name == 'yfinance':
                    import yfinance as yf
                    self.data_sources.append({
                        'name': 'yfinance',
                        'module': yf,
                        'priority': config.get('priority', 4),
                        'available': True,
                        'config': config
                    })
                    self.smart_logger.info("yfinance数据源初始化成功")

            except ImportError as e:
                self.smart_logger.warning(f"{name}未安装，请运行: pip install {name}")
                continue

        if not self.data_sources:
            raise RuntimeError("没有可用的数据源，请安装至少一个数据源包")

        # 数据源初始化完成，不需要输出详细日志
    
    def download_stock_data_baostock(self, stock_code: str, start_date: str, end_date: str, frequency: str = "d") -> Optional[pd.DataFrame]:
        """
        使用BaoStock下载股票数据（同时获取不复权和前复权数据）

        Args:
            stock_code: 股票代码（如000617）
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率（"d"=日线, "5"=5分钟, "15"=15分钟, "30"=30分钟, "60"=60分钟）

        Returns:
            DataFrame或None
        """
        try:
            bs = next(ds['module'] for ds in self.data_sources if ds['name'] == 'baostock')

            # 登录系统
            lg = bs.login()
            if lg.error_code != '0':
                self.smart_logger.error(f"BaoStock登录失败: {lg.error_msg}")
                return None

            # 转换股票代码格式
            if stock_code.startswith('6'):
                bs_code = f"sh.{stock_code}"
            else:
                bs_code = f"sz.{stock_code}"

            # 格式化日期
            start_date_fmt = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
            end_date_fmt = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

            # 先下载不复权数据
            rs_no_adj = bs.query_history_k_data_plus(
                bs_code,
                "date,code,open,high,low,close,preclose,volume,amount,adjustflag,turn,tradestatus,pctChg,isST",
                start_date=start_date_fmt,
                end_date=end_date_fmt,
                frequency=frequency,
                adjustflag="3"  # 3表示不复权
            )

            if rs_no_adj.error_code != '0':
                self.smart_logger.error(f"BaoStock不复权数据查询失败: {rs_no_adj.error_msg}")
                bs.logout()
                return None

            # 获取不复权数据
            no_adj_data_list = []
            while (rs_no_adj.error_code == '0') & rs_no_adj.next():
                no_adj_data_list.append(rs_no_adj.get_row_data())

            # 再下载前复权数据
            rs_qfq = bs.query_history_k_data_plus(
                bs_code,
                "date,code,close",  # 只需要日期、代码和收盘价
                start_date=start_date_fmt,
                end_date=end_date_fmt,
                frequency=frequency,
                adjustflag="2"  # 2表示前复权
            )

            if rs_qfq.error_code != '0':
                self.smart_logger.error(f"BaoStock前复权数据查询失败: {rs_qfq.error_msg}")
                bs.logout()
                return None

            # 获取前复权数据
            qfq_data_list = []
            while (rs_qfq.error_code == '0') & rs_qfq.next():
                qfq_data_list.append(rs_qfq.get_row_data())

            bs.logout()

            if not no_adj_data_list or not qfq_data_list:
                self.smart_logger.warning(f"BaoStock未获取到{stock_code}的完整数据")
                return None

            # 转换为DataFrame
            df_no_adj = pd.DataFrame(no_adj_data_list, columns=rs_no_adj.fields)
            df_qfq = pd.DataFrame(qfq_data_list, columns=rs_qfq.fields)

            # 合并数据
            df_qfq = df_qfq.rename(columns={'close': 'close_qfq'})
            df = df_no_adj.merge(df_qfq[['date', 'close_qfq']], on='date', how='left')

            # 数据类型转换
            numeric_columns = ['open', 'high', 'low', 'close', 'close_qfq', 'preclose', 'volume', 'amount', 'turn', 'pctChg']
            for col in numeric_columns:
                if col in df.columns:
                    df[col] = pd.to_numeric(df[col], errors='coerce')

            df['date'] = pd.to_datetime(df['date'])

            self.smart_logger.info(f"BaoStock成功获取{stock_code}数据，共{len(df)}条记录（包含不复权和前复权价格）")
            return df

        except Exception as e:
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.DATA_ACCESS,
                context={'stock_code': stock_code, 'data_source': 'baostock'},
                operation="BaoStock数据下载"
            )
            self.smart_logger.error(f"BaoStock下载失败 [错误ID: {error_id}]: {e}")
            return None
    
    def download_stock_data_akshare(self, stock_code: str, start_date: str, end_date: str, frequency: str = "daily") -> Optional[pd.DataFrame]:
        """
        使用AKShare下载股票数据（同时获取不复权和前复权数据）

        Args:
            stock_code: 股票代码（如000617）
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率（"daily"=日线, "1"=1分钟, "5"=5分钟, "15"=15分钟, "30"=30分钟, "60"=60分钟）

        Returns:
            DataFrame或None
        """
        try:
            ak = next(ds['module'] for ds in self.data_sources if ds['name'] == 'akshare')

            # 格式化日期
            start_date_fmt = f"{start_date[:4]}{start_date[4:6]}{start_date[6:8]}"
            end_date_fmt = f"{end_date[:4]}{end_date[4:6]}{end_date[6:8]}"

            # 根据频率选择不同的接口
            if frequency == "daily":
                # 下载不复权数据
                df_no_adj = ak.stock_zh_a_hist(
                    symbol=stock_code,
                    period="daily",
                    start_date=start_date_fmt,
                    end_date=end_date_fmt,
                    adjust=""  # 不复权
                )
            else:
                # 分钟级数据 - 使用不同的接口
                try:
                    # 检查接口是否存在
                    if not hasattr(ak, 'stock_zh_a_hist_min_em'):
                        self.smart_logger.warning(f"AKShare分钟级数据接口不存在")
                        return None

                    # 调用接口
                    df_no_adj = ak.stock_zh_a_hist_min_em(
                        symbol=stock_code,
                        period=frequency,
                        start_date=start_date_fmt + " 09:30:00",
                        end_date=end_date_fmt + " 15:00:00",
                        adjust=""  # 不复权
                    )

                    # 检查返回结果
                    if df_no_adj is None:
                        self.smart_logger.warning(f"AKShare分钟级数据接口返回None: {stock_code}")
                        return None

                    # 检查返回结果是否为空DataFrame
                    if hasattr(df_no_adj, 'empty') and df_no_adj.empty:
                        self.smart_logger.warning(f"AKShare分钟级数据接口返回空DataFrame: {stock_code}")
                        return None

                except AttributeError as e:
                    self.smart_logger.warning(f"AKShare分钟级数据接口属性错误: {e}")
                    return None
                except TypeError as e:
                    self.smart_logger.warning(f"AKShare分钟级数据接口参数错误: {e}")
                    return None
                except KeyError as e:
                    self.smart_logger.warning(f"AKShare分钟级数据接口键值错误: {e}")
                    return None
                except Exception as e:
                    self.smart_logger.warning(f"AKShare分钟级数据接口调用失败: {e}")
                    # 如果分钟级接口失败，返回None让系统尝试其他数据源
                    return None

            if df_no_adj is None or df_no_adj.empty:
                self.smart_logger.warning(f"AKShare未获取到{stock_code}的不复权数据")
                return None

            # 下载前复权数据
            if frequency == "daily":
                df_qfq = ak.stock_zh_a_hist(
                    symbol=stock_code,
                    period="daily",
                    start_date=start_date_fmt,
                    end_date=end_date_fmt,
                    adjust="qfq"  # 前复权
                )
            else:
                # 分钟级数据
                try:
                    df_qfq = ak.stock_zh_a_hist_min_em(
                        symbol=stock_code,
                        period=frequency,
                        start_date=start_date_fmt + " 09:30:00",
                        end_date=end_date_fmt + " 15:00:00",
                        adjust="qfq"  # 前复权
                    )
                except Exception as e:
                    self.smart_logger.warning(f"AKShare分钟级前复权数据接口调用失败: {e}")
                    return None

            if df_qfq is None or df_qfq.empty:
                self.smart_logger.warning(f"AKShare未获取到{stock_code}的前复权数据")
                return None

            # 重命名列以保持一致性（支持日线和分钟级数据）
            column_mapping = {
                '日期': 'date',
                '时间': 'date',  # 分钟级数据使用'时间'字段
                '开盘': 'open',
                '收盘': 'close',
                '最高': 'high',
                '最低': 'low',
                '成交量': 'volume',
                '成交额': 'amount',
                '振幅': 'amplitude',
                '涨跌幅': 'pctChg',
                '涨跌额': 'change',
                '换手率': 'turn'
            }

            df_no_adj = df_no_adj.rename(columns=column_mapping)
            df_qfq = df_qfq.rename(columns=column_mapping)

            # 合并数据，保留不复权数据作为主数据，添加前复权收盘价
            df_qfq_close = df_qfq[['date', 'close']].rename(columns={'close': 'close_qfq'})
            df = df_no_adj.merge(df_qfq_close, on='date', how='left')

            df['code'] = stock_code

            # 确保日期格式正确
            if 'date' in df.columns:
                df['date'] = pd.to_datetime(df['date'])

            self.smart_logger.info(f"AKShare成功获取{stock_code}数据，共{len(df)}条记录（包含不复权和前复权价格）")
            return df

        except Exception as e:
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.DATA_ACCESS,
                context={'stock_code': stock_code, 'data_source': 'akshare'},
                operation="AKShare数据下载"
            )
            self.smart_logger.error(f"AKShare下载失败 [错误ID: {error_id}]: {e}")
            return None

    def download_stock_data_pytdx(self, stock_code: str, start_date: str, end_date: str, frequency: str = "d", suppress_warnings: bool = False) -> Optional[pd.DataFrame]:
        """
        使用pytdx下载股票数据

        Args:
            stock_code: 股票代码（如000617）
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率（"d"=日线, "1"=1分钟, "5"=5分钟等）
            suppress_warnings: 是否抑制数据覆盖不足的警告

        Returns:
            DataFrame或None
        """
        try:
            # 获取pytdx下载器类
            PytdxDownloader = next(ds['module'] for ds in self.data_sources if ds['name'] == 'pytdx')

            # 创建下载器实例
            downloader = PytdxDownloader(output_dir=self.output_dir)

            # 转换频率格式
            frequency_map = {
                "d": "daily",
                "1": "1min",
                "5": "5min",
                "15": "15min",
                "30": "30min",
                "60": "60min"
            }

            pytdx_frequency = frequency_map.get(frequency, "1min")  # 默认使用1分钟
            self.smart_logger.info(f"📊 频率转换: {frequency} -> {pytdx_frequency}")

            # 下载数据
            df = downloader.download_minute_data(stock_code, start_date, end_date, pytdx_frequency, suppress_warnings)

            if df is None or df.empty:
                self.smart_logger.warning(f"pytdx未获取到{stock_code}的数据")
                return None

            # 转换为标准格式
            # pytdx返回的数据格式需要转换为与其他数据源一致的格式
            result_df = pd.DataFrame()

            # 基本字段映射
            # 保持完整的datetime信息，不要只取date部分
            result_df['date'] = df['datetime']  # 保持完整的时间信息
            result_df['code'] = [stock_code] * len(df)
            result_df['open'] = df['open']
            result_df['high'] = df['high']
            result_df['low'] = df['low']
            result_df['close'] = df['close']
            result_df['volume'] = df['vol']
            result_df['amount'] = df['amount']

            # pytdx数据是原始价格，这里暂时使用原始价格作为前复权价格
            # 后续可以集成复权因子计算
            result_df['close_qfq'] = df['close']

            # date列已经是datetime格式，无需再次转换

            # pytdx数据获取完成（简化输出）
            return result_df

        except Exception as e:
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.DATA_ACCESS,
                context={'stock_code': stock_code, 'data_source': 'pytdx'},
                operation="pytdx数据下载"
            )
            self.smart_logger.error(f"pytdx下载失败 [错误ID: {error_id}]: {e}")
            return None

    def download_stock_data(self, stock_code: str, start_date: str, end_date: str, frequency: str = "d", suppress_warnings: bool = False) -> Optional[pd.DataFrame]:
        """
        下载股票数据（自动选择最佳数据源）

        Args:
            stock_code: 股票代码
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率（"d"=日线, "5"=5分钟等）
            suppress_warnings: 是否抑制数据覆盖不足的警告

        Returns:
            DataFrame或None
        """
        # 对于分钟数据，强制使用pytdx（因为其他数据源没有分钟级时间信息）
        if frequency != "d":
            self.smart_logger.info(f"分钟数据强制使用pytdx下载{stock_code}数据")
            for data_source in self.data_sources:
                if data_source['name'] == 'pytdx' and data_source['available']:
                    try:
                        df = self.download_stock_data_pytdx(stock_code, start_date, end_date, frequency, suppress_warnings)
                        if df is not None and not df.empty:
                            # pytdx分钟数据下载完成（简化输出）
                            return df
                    except Exception as e:
                        self.smart_logger.error(f"pytdx分钟数据下载失败: {e}")
                        break

            self.smart_logger.error(f"pytdx不可用，无法下载{stock_code}的分钟数据")
            return None

        # 对于日线数据，使用原有的数据源选择逻辑
        for data_source in self.data_sources:
            if not data_source['available']:
                continue

            self.smart_logger.info(f"尝试使用{data_source['name']}下载{stock_code}数据")

            try:
                if data_source['name'] == 'pytdx':
                    df = self.download_stock_data_pytdx(stock_code, start_date, end_date, frequency, suppress_warnings)
                elif data_source['name'] == 'baostock':
                    df = self.download_stock_data_baostock(stock_code, start_date, end_date, frequency)
                elif data_source['name'] == 'akshare':
                    df = self.download_stock_data_akshare(stock_code, start_date, end_date, frequency)
                else:
                    continue

                if df is not None and not df.empty:
                    self.smart_logger.info(f"使用{data_source['name']}成功下载{stock_code}数据")
                    return df

            except Exception as e:
                self.smart_logger.warning(f"{data_source['name']}下载失败，尝试下一个数据源: {e}")
                continue
        
        self.smart_logger.error(f"所有数据源都无法下载{stock_code}的数据")
        return None
    
    def convert_to_target_format(self, df: pd.DataFrame, stock_code: str, data_type: str = "daily") -> pd.DataFrame:
        """
        转换数据格式以匹配目标格式

        Args:
            df: 原始数据DataFrame（包含close和close_qfq列）
            stock_code: 股票代码
            data_type: 数据类型（"daily"=日线, "minute"=分钟）

        Returns:
            转换后的DataFrame
        """
        try:
            # 创建目标格式的DataFrame
            result_df = pd.DataFrame()

            # 基本字段映射 - 确保股票代码格式正确（6位数字）
            formatted_stock_code = str(stock_code).zfill(6)
            result_df['股票编码'] = [formatted_stock_code] * len(df)

            # 根据数据类型设置时间格式
            if data_type == "minute":
                # 分钟级数据：12位数字格式 YYYYMMDDHHMM（如202501020931）
                result_df['时间'] = df['date'].dt.strftime('%Y%m%d%H%M')
            else:
                # 日线数据只保留到日
                result_df['时间'] = df['date'].dt.strftime('%Y%m%d')

            result_df['买卖差'] = [0.0] * len(df)  # 互联网数据无法提供买卖差

            # 使用不复权收盘价作为当日收盘价C
            result_df['当日收盘价C'] = df['close'].values

            # 使用前复权收盘价作为前复权收盘价C
            if 'close_qfq' in df.columns:
                result_df['前复权收盘价C'] = df['close_qfq'].values
                # 前复权数据使用完成（简化输出）
            else:
                # 如果没有前复权数据，记录错误并尝试计算
                self.smart_logger.error("❌ 未找到前复权数据！这是数据质量问题")
                self.smart_logger.error("💡 建议检查数据源API调用是否正确获取前复权数据")

                # 尝试使用本地前复权计算
                try:
                    adjusted_prices = self._calculate_forward_adjustment(df, stock_code)
                    if adjusted_prices is not None:
                        result_df['前复权收盘价C'] = adjusted_prices
                        self.smart_logger.info("✅ 使用本地前复权计算成功")
                    else:
                        # 最后的备用方案：使用原始价格但记录警告
                        result_df['前复权收盘价C'] = df['close'].values
                        self.smart_logger.warning("⚠️ 前复权计算失败，使用原始收盘价（数据质量不合格）")
                except Exception as e:
                    result_df['前复权收盘价C'] = df['close'].values
                    self.smart_logger.error(f"❌ 前复权计算异常: {e}")
                    self.smart_logger.warning("⚠️ 使用原始收盘价（数据质量不合格）")

            result_df['路径总长'] = [0.0] * len(df)  # 互联网数据无法提供L2指标
            result_df['主买'] = [0.0] * len(df)  # 互联网数据无法提供主买数据
            result_df['主卖'] = [0.0] * len(df)  # 互联网数据无法提供主卖数据

            # 数据转换完成（简化输出，不显示样本数据）
            if len(result_df) > 0:
                # 价格差异检查（静默）
                sample_row = result_df.iloc[0]
                price_diff_threshold = self.config['quality_control'].get('price_diff_threshold', 0.01)
                price_diff = abs(sample_row['当日收盘价C'] - sample_row['前复权收盘价C'])

                if price_diff > price_diff_threshold:
                    self.smart_logger.debug(f"前复权价格差异检查: {price_diff:.4f} > {price_diff_threshold}")
                else:
                    self.smart_logger.debug(f"前复权价格差异较小: {price_diff:.4f} <= {price_diff_threshold}")

            # 执行数据质量检查
            if not self._validate_data_quality(result_df, stock_code):
                self.smart_logger.warning(f"数据质量检查未通过: {stock_code}")
                # 根据配置决定是否返回数据
                # 这里选择返回数据但记录警告，也可以选择返回空DataFrame

            return result_df

        except Exception as e:
            self.smart_logger.error(f"数据格式转换失败: {e}")
            return pd.DataFrame()

    def _calculate_forward_adjustment(self, df: pd.DataFrame, stock_code: str) -> Optional[pd.Series]:
        """
        本地前复权计算（备用方案）

        Args:
            df: 原始数据DataFrame
            stock_code: 股票代码

        Returns:
            前复权价格Series或None
        """
        try:
            self.smart_logger.info(f"🔄 尝试本地前复权计算: {stock_code}")

            # 尝试使用现有的前复权处理器
            try:
                from main_v20230219_optimized import StockDataProcessor
                from utils.process_flow_optimizer import ProcessFlowOptimizer

                # 使用默认的tdx路径初始化
                processor = StockDataProcessor(tdx_path="H:/MPV1.17/T0002")

                # 设置处理上下文为分钟数据更新，以简化输出
                processor._current_processing_context = "minute_data"

                # 设置流程优化器上下文
                flow_optimizer = ProcessFlowOptimizer()
                flow_optimizer.set_context("minute_data_update")

                # 准备数据格式
                data_for_adjustment = df.copy()
                data_for_adjustment.index = pd.to_datetime(data_for_adjustment['date'])

                # 获取除权除息数据
                xdxr_data = processor.load_dividend_data(stock_code)

                if xdxr_data is not None and len(xdxr_data) > 0:
                    # 应用前复权处理
                    adjusted_data = processor.apply_forward_adjustment(data_for_adjustment, xdxr_data)

                    if adjusted_data is not None and 'close' in adjusted_data.columns:
                        self.smart_logger.info(f"✅ 本地前复权计算成功，处理了{len(xdxr_data)}个除权事件")
                        return adjusted_data['close']
                    else:
                        self.smart_logger.warning("本地前复权处理返回空数据")
                        return None
                else:
                    self.smart_logger.info(f"股票{stock_code}无除权除息数据，前复权价格等于原始价格")
                    return df['close']  # 无除权事件时，前复权价格等于原始价格

            except ImportError:
                self.smart_logger.warning("无法导入前复权处理器，跳过本地计算")
                return None
            except Exception as e:
                self.smart_logger.warning(f"本地前复权计算失败: {e}")
                return None

        except Exception as e:
            self.smart_logger.error(f"前复权计算异常: {e}")
            return None

    def _validate_data_quality(self, df: pd.DataFrame, stock_code: str) -> bool:
        """
        数据质量验证

        Args:
            df: 数据DataFrame
            stock_code: 股票代码

        Returns:
            是否通过质量检查
        """
        try:
            quality_config = self.config.get('quality_control', {})
            all_checks_passed = True

            # 1. 检查数据点数量（静默检查）
            min_data_points = quality_config.get('min_data_points', 10)
            if len(df) < min_data_points:
                self.smart_logger.warning(f"数据点数量不足: {len(df)} < {min_data_points}")
                all_checks_passed = False

            # 2. 检查股票代码格式（静默检查）
            if quality_config.get('validate_stock_code', True):
                if not self._validate_stock_code_format(stock_code):
                    self.smart_logger.warning(f"股票代码格式不正确: {stock_code}")
                    all_checks_passed = False

            # 3. 检查价格数据有效性
            if len(df) > 0:
                # 检查是否有空值
                price_columns = ['当日收盘价C', '前复权收盘价C']
                for col in price_columns:
                    if col in df.columns:
                        null_count = df[col].isnull().sum()
                        if null_count > 0:
                            self.smart_logger.warning(f"{col}存在{null_count}个空值")
                            all_checks_passed = False

                        # 检查是否有负值或零值
                        invalid_count = (df[col] <= 0).sum()
                        if invalid_count > 0:
                            self.smart_logger.warning(f"{col}存在{invalid_count}个无效值(<=0)")
                            all_checks_passed = False

                # 价格数据有效性检查完成（静默）

            # 4. 检查前复权价格差异
            price_diff_threshold = quality_config.get('price_diff_threshold', 0.01)
            if len(df) > 0 and '当日收盘价C' in df.columns and '前复权收盘价C' in df.columns:
                # 计算平均价格差异
                price_diffs = abs(df['当日收盘价C'] - df['前复权收盘价C'])
                avg_diff = price_diffs.mean()

                if avg_diff > price_diff_threshold:
                    self.smart_logger.debug(f"前复权价格差异检查通过，平均差异: {avg_diff:.4f}")
                else:
                    self.smart_logger.debug(f"前复权价格差异较小，平均差异: {avg_diff:.4f} <= {price_diff_threshold}")
                    # 这个不算严重问题，不影响整体质量评估

            return all_checks_passed

        except Exception as e:
            self.smart_logger.error(f"数据质量验证失败: {e}")
            return False

    def _validate_stock_code_format(self, stock_code: str) -> bool:
        """
        验证股票代码格式

        Args:
            stock_code: 股票代码

        Returns:
            是否格式正确
        """
        try:
            # 检查是否为6位数字
            if len(stock_code) != 6:
                return False

            if not stock_code.isdigit():
                return False

            # 检查是否为有效的股票代码范围
            # A股代码范围：000001-399999（深圳）, 600000-999999（上海）
            code_num = int(stock_code)

            # 深圳市场：000001-399999
            # 上海市场：600000-999999
            if (1 <= code_num <= 399999) or (600000 <= code_num <= 999999):
                return True

            return False

        except Exception:
            return False

    def _fill_missing_minute_data(self, filepath: str, stock_code: str) -> bool:
        """
        补全分钟级数据中的缺失分钟

        Args:
            filepath: 数据文件路径
            stock_code: 股票代码

        Returns:
            是否补全成功
        """
        try:
            self.smart_logger.info(f"🔧 检查并补全缺失的分钟数据: {os.path.basename(filepath)}")

            # 导入独立的缺失数据处理器（使用新的240行标准）
            from utils.missing_data_processor import MissingDataProcessor

            processor = MissingDataProcessor()

            # 使用新标准处理缺失数据
            success, validation_result = processor.process_missing_data_for_file(filepath, stock_code)

            if success:
                self.smart_logger.info("✅ 缺失数据补全完成")
                # 将验证结果保存到实例变量，供增量下载使用
                self._cached_validation_result = validation_result
            else:
                self.smart_logger.warning("⚠️ 缺失数据补全失败或部分失败")
                self._cached_validation_result = None

            return success

        except Exception as e:
            self.smart_logger.warning(f"缺失数据补全过程出错: {e}")
            return False
    
    def save_stock_data(self, stock_code: str, start_date: str, end_date: str, frequency: str = "d", data_type: str = "daily", file_frequency: Optional[str] = None, use_incremental: bool = True) -> bool:
        """
        下载并保存股票数据

        Args:
            stock_code: 股票代码
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率（"d"=日线, "5"=5分钟等）
            data_type: 数据类型（"daily"=日线, "minute"=分钟）
            use_incremental: 是否使用增量下载（仅对分钟级数据有效）

        Returns:
            是否成功
        """
        try:
            # 对于分钟级数据，检查是否使用增量下载
            if data_type == "minute" and use_incremental:
                return self._save_minute_data_incremental(stock_code, start_date, end_date, frequency, file_frequency)

            # 常规下载流程
            df = self.download_stock_data(stock_code, start_date, end_date, frequency)
            
            if df is None or df.empty:
                return False
            
            # 转换格式
            formatted_df = self.convert_to_target_format(df, stock_code, data_type)
            
            if formatted_df.empty:
                return False
            
            # 生成文件名（使用配置的后缀）
            filename_suffix = self.config['output'].get('filename_suffix', '_来源互联网')

            # 获取实际数据的时间范围（用于文件命名）
            actual_start_date = formatted_df['时间'].min()
            actual_end_date = formatted_df['时间'].max()

            # 转换为YYYYMMDD格式
            if len(str(actual_start_date)) >= 8:  # YYYYMMDD或YYYYMMDDHHMM格式
                actual_start_str = str(actual_start_date)[:8]
                actual_end_str = str(actual_end_date)[:8]
            else:  # 其他格式，尝试解析
                try:
                    actual_start_str = pd.to_datetime(str(actual_start_date)).strftime('%Y%m%d')
                    actual_end_str = pd.to_datetime(str(actual_end_date)).strftime('%Y%m%d')
                except:
                    # 如果解析失败，回退到请求范围
                    actual_start_str = start_date
                    actual_end_str = end_date

            # 根据数据类型生成不同的文件名前缀（使用实际数据时间范围）
            if data_type == "minute":
                # 使用文件频率作为文件名前缀（保持1min格式，而不是转换后的"1"）
                display_frequency = file_frequency if file_frequency else frequency
                filename_prefix = f"{display_frequency}_0_{stock_code}_{actual_start_str}-{actual_end_str}"
            else:
                filename_prefix = f"day_0_{stock_code}_{actual_start_str}-{actual_end_str}"

            # 添加时间戳到文件名
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d%H%M')

            # 检查是否需要添加时间戳（当文件已存在且数据相同时）
            base_filename = f"{filename_prefix}{filename_suffix}（{timestamp}）.txt"
            base_filepath = os.path.join(self.output_dir, base_filename)

            # 检查是否需要添加时间戳
            filename, filepath = self._generate_filename_with_timestamp_if_needed(
                base_filepath, filename_prefix, filename_suffix, formatted_df
            )

            # 确保股票编码为字符串格式
            formatted_df['股票编码'] = formatted_df['股票编码'].astype(str).str.zfill(6)

            # 保存文件（使用配置的编码）
            encoding = self.config['output'].get('encoding', 'utf-8')
            formatted_df.to_csv(
                filepath,
                sep='|',
                index=False,
                encoding=encoding
            )

            self.smart_logger.info(f"成功保存{stock_code}数据到: {filepath}")

            # 对于分钟级数据，尝试补全缺失的分钟数据
            if data_type == "minute":
                self._fill_missing_minute_data(filepath, stock_code)

            return True

        except Exception as e:
            error_id = self.error_handler.log_error(
                error=e,
                category=ErrorCategory.FILE_IO,
                context={'stock_code': stock_code},
                operation="股票数据保存"
            )
            self.smart_logger.error(f"保存股票数据失败 [错误ID: {error_id}]: {e}")
            return False

    def _generate_filename_with_timestamp_if_needed(self, base_filepath: str, filename_prefix: str,
                                                   filename_suffix: str, new_data: pd.DataFrame) -> Tuple[str, str]:
        """
        根据需要生成带时间戳的文件名

        Args:
            base_filepath: 基础文件路径
            filename_prefix: 文件名前缀
            filename_suffix: 文件名后缀
            new_data: 新数据DataFrame

        Returns:
            (文件名, 文件路径)
        """
        try:
            from datetime import datetime

            # 如果文件不存在，使用基础文件名
            if not os.path.exists(base_filepath):
                filename = os.path.basename(base_filepath)
                return filename, base_filepath

            # 文件存在，检查数据是否相同
            try:
                existing_df = pd.read_csv(base_filepath, sep='|', encoding='utf-8')

                # 比较数据行数和关键字段
                if len(existing_df) == len(new_data):
                    # 简单比较：如果行数相同且时间范围相同，认为数据相同
                    existing_times = set(existing_df['时间'].astype(str))
                    new_times = set(new_data['时间'].astype(str))

                    if existing_times == new_times:
                        # 数据相同，添加时间戳
                        current_time = datetime.now()
                        timestamp = current_time.strftime('%Y%m%d%H%M')

                        # 检查现有文件名是否已有时间戳
                        base_filename = os.path.basename(base_filepath)
                        if '（' in base_filename and '）' in base_filename:
                            # 更新现有时间戳
                            base_name = base_filename.split('（')[0]
                            extension = '.txt'
                            new_filename = f"{base_name}（{timestamp}）{extension}"
                        else:
                            # 添加新时间戳
                            base_name = base_filename.replace('.txt', '')
                            new_filename = f"{base_name}（{timestamp}）.txt"

                        new_filepath = os.path.join(os.path.dirname(base_filepath), new_filename)

                        self.smart_logger.info(f"数据无变化，将添加时间戳: {new_filename}")
                        print(f"📝 数据无变化，文件将标记处理时间: {new_filename}")

                        return new_filename, new_filepath

            except Exception as e:
                self.smart_logger.warning(f"比较现有数据失败: {e}")

            # 数据有变化或比较失败，使用基础文件名
            filename = os.path.basename(base_filepath)
            return filename, base_filepath

        except Exception as e:
            self.smart_logger.error(f"生成文件名失败: {e}")
            # 出错时使用基础文件名
            filename = os.path.basename(base_filepath)
            return filename, base_filepath

    def _save_minute_data_incremental(self, stock_code: str, start_date: str, end_date: str, frequency: str, file_frequency: Optional[str] = None, existing_file: Optional[str] = None) -> bool:
        """
        分钟级数据增量下载保存

        Args:
            stock_code: 股票代码
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率
            file_frequency: 文件名中使用的频率（如果不提供则使用frequency）

        Returns:
            是否成功
        """
        try:
            from utils.incremental_downloader import IncrementalDownloader
            from utils.incremental_download_validator import IncrementalDownloadValidator
            from utils.smart_incremental_analyzer import SmartIncrementalAnalyzer

            self.smart_logger.info(f"🔄 开始智能增量下载验证: {stock_code}")

            # 初始化验证器、分析器和下载管理器
            validator = IncrementalDownloadValidator()
            smart_analyzer = SmartIncrementalAnalyzer()
            incremental_mgr = IncrementalDownloader(self.output_dir)

            # 1. 智能查找现有文件（如果未提供existing_file参数）
            if existing_file is None:
                existing_file = incremental_mgr.find_existing_file(
                    stock_code=stock_code,
                    data_type="minute",
                    target_start=start_date.replace('-', ''),
                    target_end=end_date.replace('-', '')
                )

            if existing_file is None:
                # 没有现有文件，执行全量下载
                self.smart_logger.info("📥 未找到现有文件，执行全量下载")
                return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

            self.smart_logger.info(f"找到现有文件: {os.path.basename(existing_file)}")

            # 2. 智能时间范围分析
            self.smart_logger.info(f"📊 开始智能时间范围分析")

            # 分析现有文件的时间跨度
            existing_range = smart_analyzer.analyze_existing_file_time_range(existing_file)
            if not existing_range:
                self.smart_logger.info(f"无法分析现有文件时间跨度，执行全量下载")
                return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

            # 获取任务时间范围
            task_start = start_date.replace('-', '')
            task_end = end_date.replace('-', '')

            # 比较时间范围
            range_comparison = smart_analyzer.compare_time_ranges(existing_range, task_start, task_end)

            if not range_comparison.get('can_use_existing', False):
                self.smart_logger.info(f"时间范围无重叠，执行全量下载")
                return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

            # 3. 数据一致性验证（优先使用缓存的验证结果，避免重复验证）
            cached_validation = getattr(self, '_cached_validation_result', None)

            if cached_validation and cached_validation.get('validated_at') == 'missing_data_processor':
                # 使用缓存的验证结果
                self.smart_logger.info(f"✅ 使用缓存的验证结果，跳过重复验证")
                is_consistent = cached_validation.get('can_increment', False)
                reason = cached_validation.get('reason', '缓存验证结果')
                comparison_details = cached_validation.get('details', {})

                # 清理缓存
                self._cached_validation_result = None
            else:
                # 执行新的验证（原有逻辑）
                is_consistent, reason, comparison_details = smart_analyzer.validate_last_record_consistency(
                    existing_file, stock_code, self, frequency
                )

            if not is_consistent:
                self.smart_logger.info(f"❌ 数据一致性验证失败: {reason}")
                return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

            self.smart_logger.info(f"✅ 数据一致性验证通过，可以使用智能增量下载")

            # 4. 从智能分析器获取已验证的API数据（避免重复下载）
            api_last_record = comparison_details.get('api_record')
            if not api_last_record:
                self.smart_logger.warning("⚠️ 智能分析器未返回API记录，执行全量下载")
                return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

            # 5. 使用验证器判断是否可以增量下载（使用已获取的API数据）
            can_incremental, reason = validator.should_use_incremental_download(existing_file, api_last_record)

            if not can_incremental:
                self.smart_logger.warning(f"⚠️ {reason}")
                return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

            # 6. 数据一致性验证通过，执行传统增量下载逻辑
            self.smart_logger.info("✅ 数据一致性验证通过，执行增量下载")

            # 解析现有数据范围
            existing_start, existing_end = incremental_mgr.parse_existing_data_range(existing_file)

            if existing_start is None or existing_end is None:
                self.smart_logger.warning("⚠️ 无法解析现有数据范围，执行全量下载")
                return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

            # 使用智能分析器的结果计算增量下载范围（避免重复计算）
            time_range_comparison = smart_analyzer.compare_time_ranges(existing_range, task_start, task_end)

            # 检查是否需要补充数据
            need_download = time_range_comparison.get('need_prepend', False) or time_range_comparison.get('need_append', False)

            if not need_download:
                self.smart_logger.info("✅ 现有数据已覆盖目标范围，无需下载")
                # 为现有文件添加时间戳，表示已处理
                incremental_mgr._add_timestamp_to_existing_file(existing_file, stock_code)
                return True

            # 根据智能分析器的结果构建下载范围
            incremental_ranges = []
            if time_range_comparison.get('prepend_range'):
                prepend = time_range_comparison['prepend_range']
                incremental_ranges.append((prepend['start'], prepend['end']))
                self.smart_logger.info(f"需要下载早期数据: {prepend['start']} - {prepend['end']}")

            if time_range_comparison.get('append_range'):
                append = time_range_comparison['append_range']
                incremental_ranges.append((append['start'], append['end']))
                self.smart_logger.info(f"需要下载最新数据: {append['start']} - {append['end']}")

            # 执行增量下载和合并
            success = incremental_mgr.merge_data_files(
                existing_file, incremental_ranges, stock_code, self, frequency
            )

            if success:
                self.smart_logger.info("✅ 智能增量下载完成")
            else:
                self.smart_logger.error("❌ 增量下载失败，尝试全量下载")
                return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

            return success

        except Exception as e:
            self.smart_logger.error(f"分钟级增量下载失败: {e}")
            # 增量下载失败时回退到全量下载
            return self._save_minute_data_full(stock_code, start_date, end_date, frequency, file_frequency)

    def _save_minute_data_full(self, stock_code: str, start_date: str, end_date: str, frequency: str, file_frequency: Optional[str] = None) -> bool:
        """
        分钟级数据全量下载保存

        Args:
            stock_code: 股票代码
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            frequency: 数据频率
            file_frequency: 文件名中使用的频率（如果不提供则使用frequency）

        Returns:
            是否成功
        """
        try:
            self.smart_logger.info(f"📥 执行分钟级数据全量下载: {stock_code}")

            # 常规下载流程
            df = self.download_stock_data(stock_code, start_date, end_date, frequency)

            if df is None or df.empty:
                return False

            # 转换格式
            formatted_df = self.convert_to_target_format(df, stock_code, "minute")

            if formatted_df.empty:
                return False

            # 获取实际数据的时间范围（而不是请求范围）
            actual_start_date = formatted_df['时间'].min()
            actual_end_date = formatted_df['时间'].max()

            # 转换为YYYYMMDD格式
            if len(str(actual_start_date)) >= 8:  # YYYYMMDD或YYYYMMDDHHMM格式
                actual_start_str = str(actual_start_date)[:8]
                actual_end_str = str(actual_end_date)[:8]
            else:  # 其他格式，尝试解析
                try:
                    actual_start_str = pd.to_datetime(str(actual_start_date)).strftime('%Y%m%d')
                    actual_end_str = pd.to_datetime(str(actual_end_date)).strftime('%Y%m%d')
                except:
                    # 如果解析失败，回退到请求范围
                    actual_start_str = start_date
                    actual_end_str = end_date

            # 生成文件名（使用实际数据时间范围，添加时间戳）
            filename_prefix = file_frequency if file_frequency else frequency
            filename_suffix = self.config['output'].get('filename_suffix', '_来源互联网')

            # 添加时间戳
            from datetime import datetime
            timestamp = datetime.now().strftime('%Y%m%d%H%M')
            filename = f"{filename_prefix}_0_{stock_code}_{actual_start_str}-{actual_end_str}{filename_suffix}（{timestamp}）.txt"
            filepath = os.path.join(self.output_dir, filename)

            # 保存文件
            formatted_df.to_csv(filepath, sep='|', index=False, encoding='utf-8')

            file_size = os.path.getsize(filepath)
            self.smart_logger.info(f"✅ 数据保存完成: {filename} ({file_size} 字节)")

            return True

        except Exception as e:
            self.smart_logger.error(f"分钟级全量下载失败: {e}")
            return False

    def batch_download(self, stock_codes: List[str], start_date: str, end_date: str,
                      delay: float = None, frequency: str = "d", data_type: str = "daily",
                      original_frequency: Optional[str] = None) -> Dict[str, bool]:
        """
        批量下载股票数据
        
        Args:
            stock_codes: 股票代码列表
            start_date: 开始日期（YYYYMMDD）
            end_date: 结束日期（YYYYMMDD）
            delay: 请求间隔（秒），如果为None则使用配置值
            frequency: 数据频率（"d"=日线, "5"=5分钟等）
            data_type: 数据类型（"daily"=日线, "minute"=分钟）

        Returns:
            下载结果字典
        """
        results = {}

        # 使用配置的请求间隔 - 根据调研优化
        if delay is None:
            delay = self.config['download_params'].get('request_delay', 0.5)  # 默认0.5秒，符合AKShare限制

        self.smart_logger.info(f"开始批量下载{len(stock_codes)}只股票数据，请求间隔: {delay}秒")
        
        for i, stock_code in enumerate(stock_codes, 1):
            self.smart_logger.info(f"下载进度: {i}/{len(stock_codes)} - {stock_code}")
            
            # 使用原始频率用于文件命名，如果没有提供则使用转换后的频率
            file_frequency = original_frequency if original_frequency else frequency
            success = self.save_stock_data(stock_code, start_date, end_date, frequency, data_type, file_frequency)
            results[stock_code] = success
            
            if success:
                self.smart_logger.info(f"✅ {stock_code} 下载成功")
            else:
                self.smart_logger.error(f"❌ {stock_code} 下载失败")
            
            # 请求间隔，避免被限制
            if i < len(stock_codes):
                time.sleep(delay)
        
        # 统计结果
        success_count = sum(results.values())
        self.smart_logger.info(f"批量下载完成: {success_count}/{len(stock_codes)} 成功")
        
        return results


def main():
    """测试函数"""
    downloader = StockDataDownloader()
    
    # 测试单只股票下载
    success = downloader.save_stock_data("000617", "20240101", "20241231")
    print(f"测试下载结果: {success}")


if __name__ == '__main__':
    main()
