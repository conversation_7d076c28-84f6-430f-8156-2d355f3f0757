{"timestamp": "2025-07-23T23:56:12.167698", "error_statistics": {"error_counts": {"SYSTEM": 1}, "total_errors": 1, "recent_errors": [{"error_id": "SYSTEM_1753286067339", "timestamp": "2025-07-23T23:54:27.339119", "category": "SYSTEM", "error_type": "AttributeError", "error_message": "'ConfigManager' object has no attribute 'get_output_base_dir'", "stock_code": null, "operation": "系统概览显示", "context": {}, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\application.py\", line 77, in display_system_overview\n    config_info = self.config_manager.get_system_info()\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\config_manager.py\", line 157, in get_system_info\n    'output_dir': self.get_output_base_dir(),\nAttributeError: 'ConfigManager' object has no attribute 'get_output_base_dir'\n"}], "error_history_size": 1}, "performance_statistics": {}}