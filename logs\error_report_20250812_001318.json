{"timestamp": "2025-08-12T00:13:18.757901", "error_statistics": {"error_counts": {"SYSTEM": 1}, "total_errors": 1, "recent_errors": [{"error_id": "SYSTEM_1754928788524", "timestamp": "2025-08-12T00:13:08.524069", "category": "SYSTEM", "error_type": "KeyError", "error_message": "'inserted_count'", "stock_code": null, "operation": "基于pytdx特性的数据修复", "context": {"file_path": "H:/MPV1.17/T0002/signals\\1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt", "stock_code": "000617"}, "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\pytdx_data_repairer.py\", line 107, in repair_missing_data_with_full_download\n    'repaired_count': merge_result['inserted_count'],\nKeyError: 'inserted_count'\n"}], "error_history_size": 1}, "performance_statistics": {}}