#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最终验证测试脚本

验证项目结构清理后的功能完整性
"""

import sys
import os
from pathlib import Path

# 添加项目根目录到路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))

def test_core_imports():
    """测试核心模块导入"""
    print("🔍 测试核心模块导入...")
    
    try:
        import config_compatibility
        print("   ✅ config_compatibility 导入成功")
    except Exception as e:
        print(f"   ❌ config_compatibility 导入失败: {e}")
        return False
    
    try:
        import data_access_compatibility
        print("   ✅ data_access_compatibility 导入成功")
    except Exception as e:
        print(f"   ❌ data_access_compatibility 导入失败: {e}")
        return False
    
    try:
        import algorithm_compatibility
        print("   ✅ algorithm_compatibility 导入成功")
    except Exception as e:
        print(f"   ❌ algorithm_compatibility 导入失败: {e}")
        return False
    
    try:
        import io_compatibility
        print("   ✅ io_compatibility 导入成功")
    except Exception as e:
        print(f"   ❌ io_compatibility 导入失败: {e}")
        return False
    
    return True

def test_project_structure():
    """测试项目结构"""
    print("\n🔍 测试项目结构...")
    
    required_dirs = [
        'core', 'utils', 'algorithms', 'cache', 'file_io', 'ui',
        'tests', 'docs', 'tools', 'scripts', 'environments',
        'test_environments', 'logs', 'reports'
    ]
    
    missing_dirs = []
    for dir_name in required_dirs:
        dir_path = project_root / dir_name
        if dir_path.exists():
            print(f"   ✅ {dir_name}/ 存在")
        else:
            print(f"   ❌ {dir_name}/ 缺失")
            missing_dirs.append(dir_name)
    
    return len(missing_dirs) == 0

def test_core_files():
    """测试核心文件"""
    print("\n🔍 测试核心文件...")
    
    core_files = [
        'main.py',
        'user_config.py',
        'requirements.txt',
        'config_compatibility.py',
        'data_access_compatibility.py',
        'algorithm_compatibility.py',
        'io_compatibility.py'
    ]
    
    missing_files = []
    for file_name in core_files:
        file_path = project_root / file_name
        if file_path.exists():
            print(f"   ✅ {file_name} 存在")
        else:
            print(f"   ❌ {file_name} 缺失")
            missing_files.append(file_name)
    
    return len(missing_files) == 0

def test_root_directory_cleanliness():
    """测试根目录清洁度"""
    print("\n🔍 测试根目录清洁度...")
    
    root_files = [f.name for f in project_root.iterdir() if f.is_file()]
    print(f"   📊 根目录文件数: {len(root_files)}")
    
    # 理想状态：根目录文件数 <= 8
    if len(root_files) <= 8:
        print("   ✅ 根目录文件数理想（≤8个）")
        clean_status = True
    elif len(root_files) <= 12:
        print("   ⚠️ 根目录文件数可接受（≤12个）")
        clean_status = True
    else:
        print("   ❌ 根目录文件数过多（>12个）")
        clean_status = False
    
    print("   📄 根目录文件列表:")
    for file_name in sorted(root_files):
        print(f"      - {file_name}")
    
    return clean_status

def main():
    """主测试函数"""
    print("🧪 MythQuant 最终验证测试")
    print("=" * 50)
    
    tests = [
        ("核心模块导入", test_core_imports),
        ("项目结构", test_project_structure),
        ("核心文件", test_core_files),
        ("根目录清洁度", test_root_directory_cleanliness)
    ]
    
    passed = 0
    total = len(tests)
    
    for test_name, test_func in tests:
        print(f"\n🔬 执行测试: {test_name}")
        print("-" * 40)
        
        try:
            result = test_func()
            if result:
                print(f"   ✅ {test_name} 测试通过")
                passed += 1
            else:
                print(f"   ❌ {test_name} 测试失败")
        except Exception as e:
            print(f"   ❌ {test_name} 测试异常: {e}")
    
    print(f"\n📊 测试结果统计:")
    print(f"   通过: {passed}/{total}")
    print(f"   成功率: {passed/total*100:.1f}%")
    
    if passed == total:
        print("\n🎉 所有测试通过！项目结构清理成功！")
        return True
    else:
        print(f"\n⚠️ {total-passed} 个测试失败，需要进一步处理")
        return False

if __name__ == "__main__":
    try:
        success = main()
        if success:
            print("\n✅ 最终验证完成 - 项目状态良好")
        else:
            print("\n❌ 最终验证失败 - 需要修复问题")
    except Exception as e:
        print(f"\n💥 验证过程异常: {e}")
    
    input("\n按回车键退出...")
