#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接导入测试 - 避免触发主程序
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

print("🧪 直接导入测试开始")
print(f"📁 项目根目录: {project_root}")
print(f"📦 src路径: {src_path}")
print(f"🐍 Python路径: {sys.path[:3]}...")

# 测试1: 基本包导入
print("\n1️⃣ 测试基本包导入...")
try:
    import mythquant
    print(f"   ✅ mythquant 导入成功")
    print(f"   📦 版本: {getattr(mythquant, '__version__', 'Unknown')}")
    print(f"   👥 作者: {getattr(mythquant, '__author__', 'Unknown')}")
except Exception as e:
    print(f"   ❌ mythquant 导入失败: {e}")

# 测试2: 配置模块导入
print("\n2️⃣ 测试配置模块导入...")
try:
    from mythquant.config import ConfigManager
    print(f"   ✅ ConfigManager 导入成功")
    
    cm = ConfigManager()
    print(f"   ✅ ConfigManager 实例化成功")
    
    debug_mode = cm.is_debug_enabled()
    print(f"   ✅ 调试模式: {debug_mode}")
    
except Exception as e:
    print(f"   ❌ 配置模块测试失败: {e}")

# 测试3: 用户设置导入
print("\n3️⃣ 测试用户设置导入...")
try:
    from mythquant.config.user_settings import DEBUG, TDX_CONFIG
    print(f"   ✅ 用户设置导入成功")
    print(f"   📊 DEBUG: {DEBUG}")
    print(f"   📊 TDX路径: {TDX_CONFIG.get('tdx_path', 'Not set')}")
except Exception as e:
    print(f"   ❌ 用户设置测试失败: {e}")

# 测试4: 核心模块导入（不实例化）
print("\n4️⃣ 测试核心模块导入...")
try:
    from mythquant.core import MythQuantApplication
    print(f"   ✅ MythQuantApplication 导入成功")
    
    # 检查类属性
    if hasattr(MythQuantApplication, '__init__'):
        print(f"   ✅ __init__ 方法存在")
    
except Exception as e:
    print(f"   ❌ 核心模块测试失败: {e}")

print("\n🏁 直接导入测试完成")
print("=" * 50)
