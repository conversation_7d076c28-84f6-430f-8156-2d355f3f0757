#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件溯源实现

提供事件溯源聚合根和事件流功能
"""

import logging
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Type, Iterator
from datetime import datetime
from copy import deepcopy

from ...shared.patterns.architectural_patterns import DomainEvent, AggregateRoot
from .event_store import EventStore, EventRecord, get_event_store
from ...domain.exceptions import DomainException


logger = logging.getLogger(__name__)


class EventSourcingException(DomainException):
    """事件溯源异常"""
    pass


class EventStream:
    """事件流"""
    
    def __init__(self, aggregate_id: str, events: List[EventRecord]):
        self.aggregate_id = aggregate_id
        self.events = events
        self.version = len(events)
    
    def get_events_from_version(self, from_version: int) -> List[EventRecord]:
        """获取指定版本之后的事件"""
        return [event for event in self.events if event.event.version >= from_version]
    
    def get_events_by_type(self, event_type: str) -> List[EventRecord]:
        """按类型获取事件"""
        return [event for event in self.events if event.event.event_type == event_type]
    
    def get_events_in_range(self, start_version: int, end_version: int) -> List[EventRecord]:
        """获取版本范围内的事件"""
        return [event for event in self.events 
                if start_version <= event.event.version <= end_version]
    
    def append_event(self, event: DomainEvent) -> 'EventStream':
        """追加事件（返回新的事件流）"""
        new_record = EventRecord(event, len(self.events) + 1)
        new_events = self.events + [new_record]
        return EventStream(self.aggregate_id, new_events)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'aggregate_id': self.aggregate_id,
            'version': self.version,
            'events': [event.to_dict() for event in self.events]
        }


class EventSourcedAggregate(AggregateRoot):
    """事件溯源聚合根"""
    
    def __init__(self, aggregate_id: str):
        super().__init__(aggregate_id)
        self._version = 0
        self._uncommitted_events: List[DomainEvent] = []
        self._event_handlers: Dict[str, callable] = {}
        self._snapshots: List[Dict[str, Any]] = []
        self._register_event_handlers()
    
    @property
    def version(self) -> int:
        """聚合版本"""
        return self._version
    
    @property
    def uncommitted_events(self) -> List[DomainEvent]:
        """未提交的事件"""
        return self._uncommitted_events.copy()
    
    def mark_events_as_committed(self):
        """标记事件为已提交"""
        self._uncommitted_events.clear()
    
    def load_from_history(self, events: List[DomainEvent]):
        """从历史事件加载聚合状态"""
        logger.debug(f"从历史事件加载聚合: {self.id}, 事件数量: {len(events)}")
        
        for event in events:
            self._apply_event(event, is_new=False)
        
        logger.debug(f"聚合加载完成: {self.id}, 版本: {self._version}")
    
    def apply_event(self, event: DomainEvent):
        """应用新事件"""
        # 设置事件版本
        event.version = self._version + 1
        
        # 应用事件
        self._apply_event(event, is_new=True)
        
        # 添加到未提交事件列表
        self._uncommitted_events.append(event)
        
        logger.debug(f"应用新事件: {event.event_type}, 聚合: {self.id}, 版本: {self._version}")
    
    def create_snapshot(self) -> Dict[str, Any]:
        """创建快照"""
        snapshot = {
            'aggregate_id': self.id,
            'version': self._version,
            'timestamp': datetime.now().isoformat(),
            'state': self._get_snapshot_data()
        }
        
        self._snapshots.append(snapshot)
        logger.debug(f"创建快照: {self.id}, 版本: {self._version}")
        
        return snapshot
    
    def load_from_snapshot(self, snapshot: Dict[str, Any]):
        """从快照加载"""
        self._version = snapshot['version']
        self._load_snapshot_data(snapshot['state'])
        
        logger.debug(f"从快照加载: {self.id}, 版本: {self._version}")
    
    def get_latest_snapshot(self) -> Optional[Dict[str, Any]]:
        """获取最新快照"""
        return self._snapshots[-1] if self._snapshots else None
    
    def replay_events(self, from_version: int = 0) -> List[DomainEvent]:
        """重放事件"""
        # 这里应该从事件存储中获取事件
        # 为了简化，返回未提交的事件
        return [event for event in self._uncommitted_events 
                if event.version >= from_version]
    
    def _apply_event(self, event: DomainEvent, is_new: bool = True):
        """应用事件到聚合状态"""
        # 更新版本
        if is_new:
            self._version += 1
        else:
            self._version = max(self._version, event.version)
        
        # 查找并调用事件处理器
        handler_name = f"_handle_{event.event_type.lower()}"
        if handler_name in self._event_handlers:
            handler = self._event_handlers[handler_name]
            handler(event)
        else:
            logger.warning(f"未找到事件处理器: {handler_name}, 聚合: {self.id}")
    
    @abstractmethod
    def _register_event_handlers(self):
        """注册事件处理器"""
        pass
    
    @abstractmethod
    def _get_snapshot_data(self) -> Dict[str, Any]:
        """获取快照数据"""
        pass
    
    @abstractmethod
    def _load_snapshot_data(self, data: Dict[str, Any]):
        """加载快照数据"""
        pass
    
    def _register_handler(self, event_type: str, handler: callable):
        """注册事件处理器"""
        handler_name = f"_handle_{event_type.lower()}"
        self._event_handlers[handler_name] = handler


class EventSourcingRepository(ABC):
    """事件溯源仓储抽象基类"""
    
    @abstractmethod
    def save(self, aggregate: EventSourcedAggregate):
        """保存聚合"""
        pass
    
    @abstractmethod
    def load(self, aggregate_id: str) -> Optional[EventSourcedAggregate]:
        """加载聚合"""
        pass
    
    @abstractmethod
    def exists(self, aggregate_id: str) -> bool:
        """检查聚合是否存在"""
        pass


class GenericEventSourcingRepository(EventSourcingRepository):
    """通用事件溯源仓储"""
    
    def __init__(self, 
                 aggregate_type: Type[EventSourcedAggregate],
                 event_store: Optional[EventStore] = None):
        self._aggregate_type = aggregate_type
        self._event_store = event_store or get_event_store()
        self._snapshots: Dict[str, Dict[str, Any]] = {}
        self._snapshot_frequency = 10  # 每10个事件创建一次快照
    
    def save(self, aggregate: EventSourcedAggregate):
        """保存聚合"""
        uncommitted_events = aggregate.uncommitted_events
        
        if not uncommitted_events:
            logger.debug(f"没有未提交的事件: {aggregate.id}")
            return
        
        try:
            # 保存事件到事件存储
            sequence_numbers = self._event_store.append_events(uncommitted_events)
            
            # 标记事件为已提交
            aggregate.mark_events_as_committed()
            
            # 检查是否需要创建快照
            if aggregate.version % self._snapshot_frequency == 0:
                snapshot = aggregate.create_snapshot()
                self._snapshots[aggregate.id] = snapshot
            
            logger.info(f"聚合保存成功: {aggregate.id}, 事件数量: {len(uncommitted_events)}")
            
        except Exception as e:
            logger.error(f"聚合保存失败: {aggregate.id}, 错误: {e}")
            raise EventSourcingException(f"聚合保存失败: {e}") from e
    
    def load(self, aggregate_id: str) -> Optional[EventSourcedAggregate]:
        """加载聚合"""
        try:
            # 创建聚合实例
            aggregate = self._aggregate_type(aggregate_id)
            
            # 尝试从快照加载
            snapshot = self._snapshots.get(aggregate_id)
            from_version = 0
            
            if snapshot:
                aggregate.load_from_snapshot(snapshot)
                from_version = snapshot['version']
                logger.debug(f"从快照加载聚合: {aggregate_id}, 快照版本: {from_version}")
            
            # 加载快照之后的事件
            event_records = self._event_store.get_events(aggregate_id, from_version + 1)
            
            if not event_records and not snapshot:
                logger.debug(f"聚合不存在: {aggregate_id}")
                return None
            
            # 从事件历史重建聚合状态
            events = [record.event for record in event_records]
            if events:
                aggregate.load_from_history(events)
            
            logger.debug(f"聚合加载成功: {aggregate_id}, 版本: {aggregate.version}")
            return aggregate
            
        except Exception as e:
            logger.error(f"聚合加载失败: {aggregate_id}, 错误: {e}")
            raise EventSourcingException(f"聚合加载失败: {e}") from e
    
    def exists(self, aggregate_id: str) -> bool:
        """检查聚合是否存在"""
        try:
            # 检查是否有快照
            if aggregate_id in self._snapshots:
                return True
            
            # 检查是否有事件
            events = self._event_store.get_events(aggregate_id, 0)
            return len(events) > 0
            
        except Exception as e:
            logger.error(f"检查聚合存在性失败: {aggregate_id}, 错误: {e}")
            return False
    
    def get_event_stream(self, aggregate_id: str) -> Optional[EventStream]:
        """获取事件流"""
        try:
            event_records = self._event_store.get_events(aggregate_id, 0)
            
            if not event_records:
                return None
            
            return EventStream(aggregate_id, event_records)
            
        except Exception as e:
            logger.error(f"获取事件流失败: {aggregate_id}, 错误: {e}")
            raise EventSourcingException(f"获取事件流失败: {e}") from e
    
    def get_aggregate_history(self, aggregate_id: str) -> List[Dict[str, Any]]:
        """获取聚合历史"""
        try:
            event_stream = self.get_event_stream(aggregate_id)
            
            if not event_stream:
                return []
            
            history = []
            for event_record in event_stream.events:
                history.append({
                    'sequence_number': event_record.sequence_number,
                    'event_id': event_record.event.event_id,
                    'event_type': event_record.event.event_type,
                    'version': event_record.event.version,
                    'occurred_at': event_record.event.occurred_at.isoformat(),
                    'stored_at': event_record.stored_at.isoformat(),
                    'data': event_record.event.data
                })
            
            return history
            
        except Exception as e:
            logger.error(f"获取聚合历史失败: {aggregate_id}, 错误: {e}")
            raise EventSourcingException(f"获取聚合历史失败: {e}") from e
    
    def create_snapshot(self, aggregate_id: str) -> Optional[Dict[str, Any]]:
        """手动创建快照"""
        try:
            aggregate = self.load(aggregate_id)
            
            if not aggregate:
                return None
            
            snapshot = aggregate.create_snapshot()
            self._snapshots[aggregate_id] = snapshot
            
            logger.info(f"手动创建快照成功: {aggregate_id}, 版本: {aggregate.version}")
            return snapshot
            
        except Exception as e:
            logger.error(f"创建快照失败: {aggregate_id}, 错误: {e}")
            raise EventSourcingException(f"创建快照失败: {e}") from e


class EventProjection(ABC):
    """事件投影抽象基类"""
    
    @abstractmethod
    def project(self, event: DomainEvent):
        """投影事件"""
        pass
    
    @abstractmethod
    def can_handle(self, event: DomainEvent) -> bool:
        """判断是否可以处理该事件"""
        pass
    
    @property
    @abstractmethod
    def projection_name(self) -> str:
        """投影名称"""
        pass


class EventProjectionManager:
    """事件投影管理器"""
    
    def __init__(self, event_store: Optional[EventStore] = None):
        self._event_store = event_store or get_event_store()
        self._projections: List[EventProjection] = []
        self._last_processed_sequence = 0
    
    def register_projection(self, projection: EventProjection):
        """注册投影"""
        if projection not in self._projections:
            self._projections.append(projection)
            logger.info(f"注册事件投影: {projection.projection_name}")
    
    def unregister_projection(self, projection: EventProjection):
        """取消注册投影"""
        if projection in self._projections:
            self._projections.remove(projection)
            logger.info(f"取消注册事件投影: {projection.projection_name}")
    
    def rebuild_projections(self):
        """重建所有投影"""
        logger.info("开始重建所有投影...")
        
        # 获取所有事件
        all_events = self._event_store.get_all_events(0, limit=10000)
        
        # 应用到所有投影
        for event_record in all_events:
            for projection in self._projections:
                if projection.can_handle(event_record.event):
                    try:
                        projection.project(event_record.event)
                    except Exception as e:
                        logger.error(f"投影重建失败: {projection.projection_name}, "
                                   f"事件: {event_record.event.event_id}, 错误: {e}")
        
        self._last_processed_sequence = self._event_store.get_last_sequence_number()
        logger.info(f"投影重建完成，处理到序列号: {self._last_processed_sequence}")
    
    def process_new_events(self):
        """处理新事件"""
        # 获取新事件
        new_events = self._event_store.get_all_events(
            self._last_processed_sequence + 1, limit=1000
        )
        
        if not new_events:
            return
        
        logger.debug(f"处理新事件: {len(new_events)} 个")
        
        # 应用到投影
        for event_record in new_events:
            for projection in self._projections:
                if projection.can_handle(event_record.event):
                    try:
                        projection.project(event_record.event)
                    except Exception as e:
                        logger.error(f"投影处理失败: {projection.projection_name}, "
                                   f"事件: {event_record.event.event_id}, 错误: {e}")
            
            self._last_processed_sequence = event_record.sequence_number
        
        logger.debug(f"新事件处理完成，处理到序列号: {self._last_processed_sequence}")


# 工厂函数
def create_event_sourcing_repository(aggregate_type: Type[EventSourcedAggregate],
                                    **kwargs) -> GenericEventSourcingRepository:
    """创建事件溯源仓储"""
    return GenericEventSourcingRepository(aggregate_type, **kwargs)
