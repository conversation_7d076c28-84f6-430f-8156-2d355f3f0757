#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境管理器
负责测试资源的备份、还原和隔离管理
"""

import os
import shutil
import datetime
from pathlib import Path
from typing import Dict, List, Optional, Tuple
import logging

# 配置日志
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


class TestEnvironmentManager:
    """测试环境管理器"""
    
    def __init__(self):
        """初始化测试环境管理器"""
        self.test_root = Path("test_environments/minute_data_tests")
        self.input_data_dir = self.test_root / "input_data"
        self.output_data_dir = self.test_root / "output_data"
        self.backup_data_dir = self.test_root / "backup_data"
        self.results_dir = self.test_root / "results"
        
        # 确保目录存在
        self._ensure_directories()
        
        # 当前会话的备份记录
        self.session_backups: Dict[str, str] = {}
        self.session_id = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
        
    def _ensure_directories(self):
        """确保所有必要的目录存在"""
        directories = [
            self.input_data_dir,
            self.output_data_dir, 
            self.backup_data_dir,
            self.results_dir
        ]
        
        for directory in directories:
            directory.mkdir(parents=True, exist_ok=True)
            
    def backup_test_file(self, stock_code: str) -> Tuple[bool, str]:
        """
        备份测试文件
        
        Args:
            stock_code: 股票代码
            
        Returns:
            (是否成功, 备份文件路径或错误信息)
        """
        try:
            from user_config import get_environment_config
            
            env_config = get_environment_config('minute_data_download')
            test_files = env_config.get('test_files', {})
            
            if stock_code not in test_files:
                return False, f"未找到股票 {stock_code} 的测试文件配置"
                
            file_config = test_files[stock_code]
            original_file = file_config.get('original_file')
            
            if not original_file:
                return False, f"未配置股票 {stock_code} 的原始测试文件"
                
            # 原始文件路径
            original_path = self.input_data_dir / original_file
            
            if not original_path.exists():
                return False, f"原始测试文件不存在: {original_path}"
                
            # 备份文件路径（带时间戳）
            backup_filename = f"backup_{self.session_id}_{original_file}"
            backup_path = self.backup_data_dir / backup_filename
            
            # 执行备份
            shutil.copy2(original_path, backup_path)
            
            # 记录备份
            self.session_backups[stock_code] = str(backup_path)
            
            logger.info(f"✅ 测试文件备份成功: {original_file} → {backup_filename}")
            return True, str(backup_path)
            
        except Exception as e:
            error_msg = f"备份测试文件失败: {e}"
            logger.error(error_msg)
            return False, error_msg
            
    def create_working_copy(self, stock_code: str) -> Tuple[bool, str]:
        """
        创建工作副本
        
        Args:
            stock_code: 股票代码
            
        Returns:
            (是否成功, 工作文件路径或错误信息)
        """
        try:
            from user_config import get_environment_config
            
            env_config = get_environment_config('minute_data_download')
            test_files = env_config.get('test_files', {})
            
            if stock_code not in test_files:
                return False, f"未找到股票 {stock_code} 的测试文件配置"
                
            file_config = test_files[stock_code]
            original_file = file_config.get('original_file')
            working_file = file_config.get('working_file')
            
            if not original_file or not working_file:
                return False, f"测试文件配置不完整"
                
            # 文件路径
            original_path = self.input_data_dir / original_file
            working_path = self.input_data_dir / working_file
            
            if not original_path.exists():
                return False, f"原始测试文件不存在: {original_path}"
                
            # 创建工作副本
            shutil.copy2(original_path, working_path)
            
            logger.info(f"✅ 工作副本创建成功: {original_file} → {working_file}")
            return True, str(working_path)
            
        except Exception as e:
            error_msg = f"创建工作副本失败: {e}"
            logger.error(error_msg)
            return False, error_msg
            
    def restore_test_file(self, stock_code: str) -> Tuple[bool, str]:
        """
        还原测试文件
        
        Args:
            stock_code: 股票代码
            
        Returns:
            (是否成功, 消息)
        """
        try:
            if stock_code not in self.session_backups:
                return False, f"未找到股票 {stock_code} 的备份记录"
                
            backup_path = Path(self.session_backups[stock_code])
            
            if not backup_path.exists():
                return False, f"备份文件不存在: {backup_path}"
                
            from user_config import get_environment_config
            
            env_config = get_environment_config('minute_data_download')
            test_files = env_config.get('test_files', {})
            file_config = test_files[stock_code]
            
            original_file = file_config.get('original_file')
            working_file = file_config.get('working_file')
            
            # 还原原始文件
            original_path = self.input_data_dir / original_file
            shutil.copy2(backup_path, original_path)
            
            # 删除工作副本
            working_path = self.input_data_dir / working_file
            if working_path.exists():
                working_path.unlink()
                
            logger.info(f"✅ 测试文件还原成功: {original_file}")
            return True, f"测试文件已还原: {original_file}"
            
        except Exception as e:
            error_msg = f"还原测试文件失败: {e}"
            logger.error(error_msg)
            return False, error_msg
            
    def preserve_test_results(self, stock_code: str, result_files: List[str]) -> Tuple[bool, str]:
        """
        保存测试结果
        
        Args:
            stock_code: 股票代码
            result_files: 结果文件列表
            
        Returns:
            (是否成功, 消息)
        """
        try:
            # 创建本次测试的结果目录
            session_results_dir = self.results_dir / f"session_{self.session_id}"
            session_results_dir.mkdir(exist_ok=True)
            
            preserved_files = []
            
            for result_file in result_files:
                result_path = Path(result_file)
                
                if result_path.exists():
                    # 保存到结果目录
                    preserved_path = session_results_dir / result_path.name
                    shutil.copy2(result_path, preserved_path)
                    preserved_files.append(str(preserved_path))
                    
            if preserved_files:
                logger.info(f"✅ 测试结果已保存到: {session_results_dir}")
                return True, f"已保存 {len(preserved_files)} 个测试结果文件"
            else:
                return False, "没有找到需要保存的结果文件"
                
        except Exception as e:
            error_msg = f"保存测试结果失败: {e}"
            logger.error(error_msg)
            return False, error_msg
            
    def cleanup_session(self, stock_codes: List[str]) -> Tuple[bool, str]:
        """
        清理测试会话
        
        Args:
            stock_codes: 股票代码列表
            
        Returns:
            (是否成功, 消息)
        """
        try:
            cleanup_count = 0
            
            for stock_code in stock_codes:
                # 还原测试文件
                success, msg = self.restore_test_file(stock_code)
                if success:
                    cleanup_count += 1
                    
            # 清理备份记录
            self.session_backups.clear()
            
            logger.info(f"✅ 测试会话清理完成: 还原了 {cleanup_count} 个测试文件")
            return True, f"测试会话清理完成: 还原了 {cleanup_count} 个测试文件"
            
        except Exception as e:
            error_msg = f"清理测试会话失败: {e}"
            logger.error(error_msg)
            return False, error_msg
            
    def get_session_info(self) -> Dict:
        """获取当前会话信息"""
        return {
            'session_id': self.session_id,
            'test_root': str(self.test_root),
            'backup_count': len(self.session_backups),
            'backed_up_stocks': list(self.session_backups.keys())
        }


# 全局测试环境管理器实例
test_env_manager = TestEnvironmentManager()


def setup_test_environment(stock_codes: List[str]) -> Tuple[bool, str]:
    """
    设置测试环境
    
    Args:
        stock_codes: 股票代码列表
        
    Returns:
        (是否成功, 消息)
    """
    try:
        setup_count = 0
        
        for stock_code in stock_codes:
            # 备份原始文件
            backup_success, backup_msg = test_env_manager.backup_test_file(stock_code)
            if not backup_success:
                logger.warning(f"备份失败: {backup_msg}")
                continue
                
            # 创建工作副本
            working_success, working_msg = test_env_manager.create_working_copy(stock_code)
            if working_success:
                setup_count += 1
            else:
                logger.warning(f"创建工作副本失败: {working_msg}")
                
        if setup_count > 0:
            return True, f"测试环境设置成功: {setup_count} 个股票"
        else:
            return False, "测试环境设置失败"
            
    except Exception as e:
        return False, f"设置测试环境异常: {e}"


def cleanup_test_environment(stock_codes: List[str]) -> Tuple[bool, str]:
    """
    清理测试环境
    
    Args:
        stock_codes: 股票代码列表
        
    Returns:
        (是否成功, 消息)
    """
    return test_env_manager.cleanup_session(stock_codes)


if __name__ == "__main__":
    # 测试代码
    print("🧪 测试环境管理器测试")
    
    # 测试设置环境
    success, msg = setup_test_environment(['000617'])
    print(f"设置测试环境: {msg}")
    
    # 显示会话信息
    info = test_env_manager.get_session_info()
    print(f"会话信息: {info}")
    
    # 测试清理环境
    success, msg = cleanup_test_environment(['000617'])
    print(f"清理测试环境: {msg}")
