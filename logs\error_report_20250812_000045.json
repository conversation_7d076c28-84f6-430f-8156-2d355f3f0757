{"timestamp": "2025-08-12T00:00:45.906749", "error_statistics": {"error_counts": {"SYSTEM": 1}, "total_errors": 1, "recent_errors": [{"error_id": "SYSTEM_1754928036102", "timestamp": "2025-08-12T00:00:36.102277", "category": "SYSTEM", "error_type": "AttributeError", "error_message": "'PytdxDataRepairer' object has no attribute '_verify_repair_result_with_efficiency_metrics'", "stock_code": null, "operation": "基于pytdx特性的数据修复", "context": {"file_path": "H:/MPV1.17/T0002/signals\\1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt", "stock_code": "000617"}, "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\pytdx_data_repairer.py\", line 101, in repair_missing_data_with_full_download\n    verification_result = self._verify_repair_result_with_efficiency_metrics(\nAttributeError: 'PytdxDataRepairer' object has no attribute '_verify_repair_result_with_efficiency_metrics'\n"}], "error_history_size": 1}, "performance_statistics": {}}