2025-08-01 11:42:42,689 - Main - INFO - info:316 - ℹ️ 程序启动，日志文件: logs\mythquant_20250801_114242.log
2025-08-01 11:42:42,689 - Main - INFO - info:316 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-08-01 11:42:42,690 - Main - INFO - info:316 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-08-01 11:42:42,690 - Main - INFO - info:316 - ℹ️ 正在加载任务配置...
2025-08-01 11:42:42,690 - Main - INFO - info:316 - ℹ️ 加载了 7 个任务配置
2025-08-01 11:42:42,690 - Main - INFO - info:316 - ℹ️ 核心组件初始化完成
2025-08-01 11:42:42,690 - Main - INFO - info:316 - ℹ️ 应用程序初始化完成
2025-08-01 11:42:42,691 - Main - INFO - info:316 - ℹ️ 开始执行所有任务
2025-08-01 11:42:42,691 - Main - INFO - info:316 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-08-01 11:42:42,717 - Main - INFO - info:316 - ℹ️ 🎯 获取特定分钟数据: 000617 @ 202507041447
2025-08-01 11:42:42,717 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-08-01 11:42:42,906 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 5040条 (目标日期: 20250704, 交易日: 21天, 频率: 1min)
2025-08-01 11:42:42,906 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计5040条 -> 实际请求800条 (配置限制:800)
2025-08-01 11:42:42,906 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 11:42:42,906 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 11:42:42,959 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需4240条
2025-08-01 11:42:43,182 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 11:42:43,424 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 11:42:43,658 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 11:42:48,705 - Main - WARNING - warning:320 - ⚠️ 读取配置服务器失败: timed out
2025-08-01 11:42:48,707 - Main - INFO - info:316 - ℹ️ 未找到缓存的良好服务器
2025-08-01 11:42:48,707 - Main - INFO - info:316 - ℹ️ 🔄 尝试连接备用服务器: 119.147.212.81:7709
2025-08-01 11:42:53,712 - Main - INFO - info:316 - ℹ️ 🔄 尝试连接备用服务器: 114.80.63.12:7709
2025-08-01 11:42:58,723 - Main - INFO - info:316 - ℹ️ 🔄 尝试连接备用服务器: 180.153.39.51:7709
2025-08-01 11:43:03,726 - Main - ERROR - error:324 - ❌ ❌ 所有通达信服务器连接失败
2025-08-01 11:43:03,726 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计3200条数据
2025-08-01 11:43:03,726 - Main - WARNING - warning:320 - ⚠️ ⚠️ 数据覆盖不足: 需要5040条，实际获取3200条
2025-08-01 11:43:03,727 - Main - WARNING - warning:320 - ⚠️ ⚠️ 缺少约1840条数据（约7个交易日）
2025-08-01 11:43:03,727 - Main - WARNING - warning:320 - ⚠️ ⚠️ 实际数据范围: 2025-07- ~ 2025-08-
2025-08-01 11:43:03,727 - Main - WARNING - warning:320 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-08-01 11:43:03,735 - Main - WARNING - warning:320 - ⚠️ ⚠️ 时间范围内无数据: 20250704 - 20250704
2025-08-01 11:43:03,736 - Main - WARNING - warning:320 - ⚠️ ❌ 未获取到 20250704 的数据
2025-08-01 11:43:03,737 - Main - INFO - info:316 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-08-01 11:43:03,737 - Main - INFO - info:316 - ℹ️ 🔍 开始检测000617的缺失数据
2025-08-01 11:43:03,774 - Main - INFO - info:316 - ℹ️ 📊 数据统计: 总记录数17211, 覆盖72个交易日
2025-08-01 11:43:03,774 - Main - WARNING - warning:320 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-08-01 11:43:03,774 - Main - WARNING - warning:320 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-08-01 11:43:03,774 - Main - WARNING - warning:320 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-08-01 11:43:03,775 - Main - INFO - info:316 - ℹ️ 🔧 开始修复000617的缺失数据
2025-08-01 11:43:03,775 - Main - INFO - info:316 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-08-01 11:43:03,775 - Main - INFO - info:316 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-08-01 11:43:03,775 - Main - INFO - info:316 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-08-01 11:43:03,775 - Main - INFO - info:316 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-08-01 11:43:03,775 - Main - INFO - info:316 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-08-01 11:43:03,775 - Main - INFO - info:316 - ℹ️ ✅ 缺失数据修复完成
2025-08-01 11:43:04,069 - Main - INFO - info:316 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-08-01 11:43:04,069 - Main - INFO - info:316 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 11:43:04,069 - Main - INFO - info:316 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 11:43:04,069 - Main - INFO - info:316 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-08-01 11:43:04,247 - Main - INFO - info:316 - ℹ️ 📊 智能计算数据量: 33600条 (目标日期: 20250101, 交易日: 140天, 频率: 1min)
2025-08-01 11:43:04,247 - Main - INFO - info:316 - ℹ️ 📊 数据量限制: 预计33600条 -> 实际请求800条 (配置限制:800)
2025-08-01 11:43:04,247 - Main - INFO - info:316 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 11:43:04,247 - Main - INFO - info:316 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 11:43:04,300 - Main - INFO - info:316 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32800条
2025-08-01 11:43:04,532 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 11:43:04,764 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 11:43:04,989 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 11:43:05,214 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 11:43:05,451 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 11:43:05,673 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-08-01 11:43:05,900 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-08-01 11:43:06,130 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-08-01 11:43:06,351 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-08-01 11:43:06,578 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-08-01 11:43:06,796 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-08-01 11:43:07,015 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-08-01 11:43:07,245 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-08-01 11:43:07,463 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-08-01 11:43:07,688 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-08-01 11:43:07,910 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-08-01 11:43:08,141 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-08-01 11:43:08,362 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-08-01 11:43:08,598 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-08-01 11:43:08,842 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-08-01 11:43:09,059 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-08-01 11:43:09,304 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-08-01 11:43:09,529 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-08-01 11:43:09,750 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-08-01 11:43:09,985 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-08-01 11:43:10,206 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-08-01 11:43:10,442 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-08-01 11:43:10,665 - Main - INFO - info:316 - ℹ️ 📊 分批获取成功: +40条，总计22440条
2025-08-01 11:43:10,665 - Main - INFO - info:316 - ℹ️ ✅ 分批获取完成: 总计22440条数据
2025-08-01 11:43:10,670 - Main - WARNING - warning:320 - ⚠️ ⚠️ 数据覆盖不足: 需要33600条，实际获取22440条
2025-08-01 11:43:10,670 - Main - WARNING - warning:320 - ⚠️ ⚠️ 缺少约11160条数据（约46个交易日）
2025-08-01 11:43:10,670 - Main - WARNING - warning:320 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-08-
2025-08-01 11:43:10,670 - Main - WARNING - warning:320 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-08-01 11:43:10,670 - Main - WARNING - warning:320 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-08-01 11:43:10,725 - Main - INFO - info:316 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-08-01 11:43:10,871 - Main - INFO - info:316 - ℹ️ ✅ 数据保存完成: 1min_0_000617_20250318-20250725_来源互联网（202508011143）.txt (999617 字节)
2025-08-01 11:43:10,872 - Main - INFO - info:316 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-08-01 11:43:10,873 - Main - INFO - info:316 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-08-01 11:43:10,873 - Main - INFO - info:316 - ℹ️ 开始执行前复权数据比较分析任务
2025-08-01 11:43:10,873 - Main - INFO - info:316 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-08-01 11:43:10,873 - Main - INFO - info:316 - ℹ️ 正在分析股票: 000617
2025-08-01 11:43:10,873 - Main - INFO - info:316 - ℹ️ 开始比较股票 000617 的前复权数据
2025-08-01 11:43:10,873 - Main - INFO - info:316 - ℹ️ 找到文件 - TDX: 未找到
2025-08-01 11:43:10,873 - Main - INFO - info:316 - ℹ️ 找到文件 - 互联网: 未找到
2025-08-01 11:43:10,873 - Main - WARNING - warning:320 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-08-01 11:43:10,874 - Main - INFO - info:316 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250801_114310.txt
2025-08-01 11:43:10,874 - Main - INFO - info:316 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-08-01 11:43:10,874 - Main - ERROR - error:324 - ❌ 任务执行失败: 前复权数据比较分析
2025-08-01 11:43:10,874 - Main - INFO - info:316 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-08-01 11:43:10,875 - Main - INFO - info:316 - ℹ️ 开始清理应用程序资源
2025-08-01 11:43:10,875 - Main - INFO - info:316 - ℹ️ 开始清理股票处理器资源
2025-08-01 11:43:10,875 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-08-01 11:43:10,875 - Main - INFO - info:316 - ℹ️ 异步IO处理器资源清理完成
2025-08-01 11:43:10,875 - Main - INFO - info:316 - ℹ️ 股票处理器资源清理完成
2025-08-01 11:43:10,875 - Main - INFO - info:316 - ℹ️ 开始清理任务管理器资源
2025-08-01 11:43:10,876 - Main - INFO - info:316 - ℹ️ 任务管理器资源清理完成
2025-08-01 11:43:10,876 - Main - INFO - info:316 - ℹ️ 应用程序资源清理完成
