#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
将当前会话的关键问题添加到FAQ知识库
"""

import sys
import os
sys.path.append('.')

from knowledge_base.faq_manager import FAQManager

def add_current_session_faqs():
    """添加当前会话的FAQ条目"""
    print("📚 将当前会话问题添加到FAQ知识库")
    print("=" * 60)
    
    faq_manager = FAQManager()
    
    # 当前会话的关键问题和答案
    current_session_faqs = [
        {
            "question": "正则表达式无法匹配带时间戳的文件名问题",
            "answer": """用户发现智能文件选择器无法匹配带时间戳后缀的文件名。

解决方案：
1. 更新正则表达式支持时间戳后缀：(?:（\\d{12}）)?
2. 同步更新utils/smart_file_selector.py和utils/incremental_download_validator.py
3. 文件重命名时自动添加时间戳

技术要点：
- 使用非捕获组保持向后兼容
- 时间戳格式：（YYYYMMDDHHMM）
- 支持有无时间戳的文件名匹配""",
            "domain": "文件处理",
            "module": "智能文件选择器", 
            "tech_stack": "正则表达式"
        },
        
        {
            "question": "pytdx数据获取上限不足导致历史数据下载失败",
            "answer": """20250301等历史日期无法下载，报错"时间范围内无数据"。

根本原因：
- pytdx API返回"最新N条数据"而非"指定时间范围数据"
- 10000条上限无法覆盖长期历史数据（20250301需要30240条）

解决方案：
1. 智能数据量计算：基于实际交易日动态计算
2. 提升上限：从10000条提升到50000条
3. 精确交易日计算：排除周末，支持8-9个月历史数据

效果：
- 20250301: 30240条（完全覆盖105个交易日）
- 系统可处理任何历史日期的数据下载""",
            "domain": "数据下载",
            "module": "pytdx下载器",
            "tech_stack": "交易日计算"
        },
        
        {
            "question": "双入口政策废除和功能迁移决策",
            "answer": """项目存在main.py和main_v20230219_optimized.py两个入口，维护成本高。

分析过程：
1. 功能对比：main.py架构更优（171行 vs 4901行）
2. 功能迁移：成功将pytdx除权除息对比功能迁移到core/stock_processor.py
3. 等价性验证：确认功能完全等价且性能更好

最终决策：
- 统一使用main.py作为唯一入口
- 保留main_v20230219_optimized.py作为参考
- 更新项目规则，明确单一入口政策

收益：
- 代码量减少96%
- 维护成本大幅降低
- 模块化架构更易扩展""",
            "domain": "架构设计",
            "module": "主程序架构",
            "tech_stack": "模块化重构"
        }
    ]
    
    # 添加FAQ条目
    success_count = 0
    for faq in current_session_faqs:
        success = faq_manager.add_faq_entry(
            question=faq["question"],
            answer=faq["answer"],
            tags=[],
            domain=faq["domain"],
            module=faq["module"],
            tech_stack=faq["tech_stack"]
        )
        if success:
            success_count += 1
    
    print(f"\n📊 添加结果:")
    print(f"  成功添加: {success_count}/{len(current_session_faqs)} 个FAQ条目")
    print(f"  知识库位置: {faq_manager.faq_file_path}")
    
    # 演示搜索功能
    print(f"\n🔍 搜索验证:")
    results = faq_manager.search_faq(keyword="正则表达式")
    if results:
        print(f"  找到 {len(results)} 个相关条目")
        for result in results:
            print(f"    {result['question_num']}: {result['title'][:50]}...")
    else:
        print("  未找到相关条目")
    
    return success_count == len(current_session_faqs)

def show_faq_usage_guide():
    """显示FAQ使用指南"""
    print(f"\n📖 FAQ知识库使用指南")
    print("=" * 60)
    
    print("🔧 添加新FAQ条目:")
    print("```python")
    print("from knowledge_base.faq_manager import FAQManager")
    print("faq_manager = FAQManager()")
    print("faq_manager.add_faq_entry(")
    print("    question='问题描述',")
    print("    answer='详细答案',")
    print("    domain='领域',")
    print("    module='模块',")
    print("    tech_stack='技术栈'")
    print(")")
    print("```")
    
    print(f"\n🔍 搜索FAQ条目:")
    print("```python")
    print("# 按关键词搜索")
    print("results = faq_manager.search_faq(keyword='正则表达式')")
    print("# 按标签搜索")
    print("results = faq_manager.search_faq(tag='数据下载')")
    print("# 按日期搜索")
    print("results = faq_manager.search_faq(date='2025-07-26')")
    print("```")
    
    print(f"\n📁 文件结构:")
    print("knowledge_base/")
    print("├── faq_database.md          # FAQ知识库主文件")
    print("├── faq_manager.py           # FAQ管理工具")
    print("└── add_current_session_to_faq.py  # 批量添加脚本")
    
    print(f"\n💡 最佳实践:")
    print("1. 每次重要问题解决后，及时添加到FAQ")
    print("2. 使用清晰的标签分类，便于后续检索")
    print("3. 答案要包含问题描述、解决方案、技术要点")
    print("4. 定期整理和更新FAQ内容")

def main():
    """主函数"""
    print("🚀 FAQ知识库初始化和当前会话记录")
    print("=" * 80)
    
    # 添加当前会话的FAQ
    success = add_current_session_faqs()
    
    # 显示使用指南
    show_faq_usage_guide()
    
    # 总结
    print(f"\n🎯 总结:")
    print("=" * 60)
    
    if success:
        print("✅ FAQ知识库初始化成功")
        print("✅ 当前会话的关键问题已记录")
        print("✅ 后续可以使用FAQ管理器添加新问题")
        
        print(f"\n📋 您现在可以:")
        print("1. 查看knowledge_base/faq_database.md了解已记录的问题")
        print("2. 使用faq_manager.search_faq()搜索相关问题")
        print("3. 使用faq_manager.add_faq_entry()添加新问题")
        print("4. 在后续对话中引用FAQ中的解决方案")
        
    else:
        print("⚠️ 部分FAQ条目添加失败，请检查")
    
    # 清理自己
    try:
        os.remove(__file__)
        print(f"\n🧹 初始化脚本已自动清理")
    except:
        pass

if __name__ == '__main__':
    main()
