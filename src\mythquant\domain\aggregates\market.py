#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
市场聚合根

管理市场的交易时间、状态和相关业务逻辑
"""

from typing import List, Optional, Dict, Any, Set
from datetime import datetime, date, time
from dataclasses import dataclass, field
from enum import Enum

from ..value_objects.identifiers import MarketCode, StockCode
from ..value_objects.trading import MarketStatus, TradingSession
from ..events.market_events import (
    MarketOpened, MarketClosed, MarketHalted,
    TradingSessionStarted, TradingSessionEnded
)
from ..exceptions import (
    BusinessRuleViolationError, InvariantViolationError,
    InvalidTradingTimeError
)
from ...shared.patterns.architectural_patterns import AggregateRoot


class MarketType(Enum):
    """市场类型"""
    STOCK = "STOCK"          # 股票市场
    BOND = "BOND"            # 债券市场
    FUTURES = "FUTURES"      # 期货市场
    OPTIONS = "OPTIONS"      # 期权市场
    FOREX = "FOREX"          # 外汇市场
    CRYPTO = "CRYPTO"        # 加密货币市场


@dataclass
class TradingHours:
    """交易时间"""
    morning_start: time
    morning_end: time
    afternoon_start: time
    afternoon_end: time
    timezone: str = "Asia/Shanghai"
    
    def __post_init__(self):
        # 验证时间顺序
        if not (self.morning_start < self.morning_end < 
                self.afternoon_start < self.afternoon_end):
            raise InvalidTradingTimeError(
                f"交易时间顺序不正确: {self.morning_start}-{self.morning_end}, "
                f"{self.afternoon_start}-{self.afternoon_end}"
            )
    
    def is_trading_time(self, current_time: time) -> bool:
        """判断是否为交易时间"""
        return ((self.morning_start <= current_time <= self.morning_end) or
                (self.afternoon_start <= current_time <= self.afternoon_end))
    
    def is_morning_session(self, current_time: time) -> bool:
        """判断是否为上午交易时段"""
        return self.morning_start <= current_time <= self.morning_end
    
    def is_afternoon_session(self, current_time: time) -> bool:
        """判断是否为下午交易时段"""
        return self.afternoon_start <= current_time <= self.afternoon_end
    
    def is_lunch_break(self, current_time: time) -> bool:
        """判断是否为午休时间"""
        return self.morning_end < current_time < self.afternoon_start


@dataclass
class MarketInfo:
    """市场基本信息"""
    name: str
    full_name: str
    country: str
    currency: str
    market_type: MarketType
    established_date: date
    trading_hours: TradingHours
    
    def __post_init__(self):
        if not self.name or not self.full_name:
            raise ValueError("市场名称不能为空")
        if not self.country or not self.currency:
            raise ValueError("国家和货币不能为空")


@dataclass
class MarketHoliday:
    """市场假期"""
    date: date
    name: str
    is_full_day: bool = True
    trading_hours: Optional[TradingHours] = None  # 半日交易时使用
    
    def __post_init__(self):
        if not self.is_full_day and self.trading_hours is None:
            raise ValueError("半日交易必须指定交易时间")


class Market(AggregateRoot):
    """市场聚合根"""
    
    def __init__(self, market_code: MarketCode, market_info: MarketInfo):
        super().__init__(str(market_code))
        self._market_code = market_code
        self._market_info = market_info
        self._status = MarketStatus.CLOSED
        self._current_session: Optional[TradingSession] = None
        self._listed_stocks: Set[StockCode] = set()
        self._holidays: List[MarketHoliday] = []
        self._halt_reason: Optional[str] = None
        self._created_at = datetime.now()
        self._updated_at = datetime.now()
    
    @property
    def market_code(self) -> MarketCode:
        """市场代码"""
        return self._market_code
    
    @property
    def market_info(self) -> MarketInfo:
        """市场信息"""
        return self._market_info
    
    @property
    def status(self) -> MarketStatus:
        """市场状态"""
        return self._status
    
    @property
    def current_session(self) -> Optional[TradingSession]:
        """当前交易时段"""
        return self._current_session
    
    @property
    def is_open(self) -> bool:
        """市场是否开放"""
        return self._status == MarketStatus.OPEN
    
    @property
    def is_closed(self) -> bool:
        """市场是否关闭"""
        return self._status == MarketStatus.CLOSED
    
    @property
    def is_halted(self) -> bool:
        """市场是否暂停"""
        return self._status == MarketStatus.HALTED
    
    @property
    def listed_stocks_count(self) -> int:
        """上市股票数量"""
        return len(self._listed_stocks)
    
    @property
    def holidays(self) -> List[MarketHoliday]:
        """假期列表（只读）"""
        return self._holidays.copy()
    
    def open_market(self, session: TradingSession):
        """开市"""
        if self._status == MarketStatus.OPEN:
            raise BusinessRuleViolationError(
                "市场开市",
                f"市场{self._market_code}已经开市"
            )
        
        # 检查是否为交易日
        current_date = datetime.now().date()
        if self._is_holiday(current_date):
            raise BusinessRuleViolationError(
                "市场开市",
                f"今日({current_date})为假期，不能开市"
            )
        
        # 检查是否为交易时间
        current_time = datetime.now().time()
        if not self._market_info.trading_hours.is_trading_time(current_time):
            raise BusinessRuleViolationError(
                "市场开市",
                f"当前时间({current_time})不是交易时间"
            )
        
        old_status = self._status
        self._status = MarketStatus.OPEN
        self._current_session = session
        self._halt_reason = None
        self._updated_at = datetime.now()
        
        # 发布开市事件
        self.add_domain_event(MarketOpened(
            market_id=self.id,
            market_code=str(self._market_code),
            session=session,
            opened_at=datetime.now()
        ))
        
        # 发布交易时段开始事件
        self.add_domain_event(TradingSessionStarted(
            market_id=self.id,
            market_code=str(self._market_code),
            session=session,
            started_at=datetime.now()
        ))
    
    def close_market(self):
        """收市"""
        if self._status == MarketStatus.CLOSED:
            raise BusinessRuleViolationError(
                "市场收市",
                f"市场{self._market_code}已经收市"
            )
        
        old_session = self._current_session
        old_status = self._status
        
        self._status = MarketStatus.CLOSED
        self._current_session = None
        self._halt_reason = None
        self._updated_at = datetime.now()
        
        # 发布收市事件
        self.add_domain_event(MarketClosed(
            market_id=self.id,
            market_code=str(self._market_code),
            session=old_session,
            closed_at=datetime.now()
        ))
        
        # 如果有交易时段，发布时段结束事件
        if old_session:
            self.add_domain_event(TradingSessionEnded(
                market_id=self.id,
                market_code=str(self._market_code),
                session=old_session,
                ended_at=datetime.now()
            ))
    
    def halt_trading(self, reason: str):
        """暂停交易"""
        if self._status != MarketStatus.OPEN:
            raise BusinessRuleViolationError(
                "暂停交易",
                f"只有开市状态的市场才能暂停交易，当前状态: {self._status}"
            )
        
        self._status = MarketStatus.HALTED
        self._halt_reason = reason
        self._updated_at = datetime.now()
        
        # 发布暂停交易事件
        self.add_domain_event(MarketHalted(
            market_id=self.id,
            market_code=str(self._market_code),
            reason=reason,
            halted_at=datetime.now()
        ))
    
    def resume_trading(self):
        """恢复交易"""
        if self._status != MarketStatus.HALTED:
            raise BusinessRuleViolationError(
                "恢复交易",
                f"只有暂停状态的市场才能恢复交易，当前状态: {self._status}"
            )
        
        # 检查是否仍在交易时间内
        current_time = datetime.now().time()
        if not self._market_info.trading_hours.is_trading_time(current_time):
            raise BusinessRuleViolationError(
                "恢复交易",
                f"当前时间({current_time})不是交易时间，无法恢复交易"
            )
        
        self._status = MarketStatus.OPEN
        self._halt_reason = None
        self._updated_at = datetime.now()
        
        # 可以发布恢复交易事件
        # self.add_domain_event(TradingResumed(...))
    
    def list_stock(self, stock_code: StockCode):
        """股票上市"""
        if stock_code in self._listed_stocks:
            raise BusinessRuleViolationError(
                "股票上市",
                f"股票{stock_code}已在市场{self._market_code}上市"
            )
        
        self._listed_stocks.add(stock_code)
        self._updated_at = datetime.now()
        
        # 可以发布股票上市事件
        # self.add_domain_event(StockListed(...))
    
    def delist_stock(self, stock_code: StockCode):
        """股票退市"""
        if stock_code not in self._listed_stocks:
            raise BusinessRuleViolationError(
                "股票退市",
                f"股票{stock_code}未在市场{self._market_code}上市"
            )
        
        self._listed_stocks.remove(stock_code)
        self._updated_at = datetime.now()
        
        # 可以发布股票退市事件
        # self.add_domain_event(StockDelisted(...))
    
    def add_holiday(self, holiday: MarketHoliday):
        """添加假期"""
        # 检查是否已存在
        for existing_holiday in self._holidays:
            if existing_holiday.date == holiday.date:
                raise BusinessRuleViolationError(
                    "添加假期",
                    f"日期{holiday.date}已设置为假期"
                )
        
        self._holidays.append(holiday)
        self._holidays.sort(key=lambda h: h.date)  # 保持日期顺序
        self._updated_at = datetime.now()
    
    def remove_holiday(self, holiday_date: date):
        """移除假期"""
        original_count = len(self._holidays)
        self._holidays = [h for h in self._holidays if h.date != holiday_date]
        
        if len(self._holidays) == original_count:
            raise BusinessRuleViolationError(
                "移除假期",
                f"日期{holiday_date}未设置为假期"
            )
        
        self._updated_at = datetime.now()
    
    def is_trading_day(self, target_date: date) -> bool:
        """判断是否为交易日"""
        # 检查是否为周末
        if target_date.weekday() >= 5:  # 周六=5, 周日=6
            return False
        
        # 检查是否为假期
        return not self._is_holiday(target_date)
    
    def is_trading_time(self, target_datetime: datetime) -> bool:
        """判断是否为交易时间"""
        # 检查是否为交易日
        if not self.is_trading_day(target_datetime.date()):
            return False
        
        # 检查是否在交易时间内
        return self._market_info.trading_hours.is_trading_time(target_datetime.time())
    
    def get_next_trading_day(self, from_date: date) -> Optional[date]:
        """获取下一个交易日"""
        current_date = from_date
        max_days = 30  # 最多查找30天
        
        for _ in range(max_days):
            current_date = date.fromordinal(current_date.toordinal() + 1)
            if self.is_trading_day(current_date):
                return current_date
        
        return None
    
    def get_trading_days_in_range(self, start_date: date, end_date: date) -> List[date]:
        """获取指定范围内的交易日"""
        trading_days = []
        current_date = start_date
        
        while current_date <= end_date:
            if self.is_trading_day(current_date):
                trading_days.append(current_date)
            current_date = date.fromordinal(current_date.toordinal() + 1)
        
        return trading_days
    
    def _is_holiday(self, target_date: date) -> bool:
        """检查是否为假期"""
        for holiday in self._holidays:
            if holiday.date == target_date:
                return True
        return False
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'id': self.id,
            'market_code': str(self._market_code),
            'market_info': {
                'name': self._market_info.name,
                'full_name': self._market_info.full_name,
                'country': self._market_info.country,
                'currency': self._market_info.currency,
                'market_type': self._market_info.market_type.value,
                'established_date': self._market_info.established_date.isoformat(),
                'trading_hours': {
                    'morning_start': self._market_info.trading_hours.morning_start.isoformat(),
                    'morning_end': self._market_info.trading_hours.morning_end.isoformat(),
                    'afternoon_start': self._market_info.trading_hours.afternoon_start.isoformat(),
                    'afternoon_end': self._market_info.trading_hours.afternoon_end.isoformat(),
                    'timezone': self._market_info.trading_hours.timezone
                }
            },
            'status': self._status.value,
            'current_session': self._current_session.value if self._current_session else None,
            'listed_stocks_count': self.listed_stocks_count,
            'holidays_count': len(self._holidays),
            'halt_reason': self._halt_reason,
            'created_at': self._created_at.isoformat(),
            'updated_at': self._updated_at.isoformat()
        }


# 类型别名
MarketAggregate = Market


# 工厂函数
def create_market(market_code: str, name: str, full_name: str,
                 country: str, currency: str, market_type: str,
                 established_date: date, trading_hours: TradingHours) -> Market:
    """创建市场聚合根的便捷函数"""
    market_code_vo = MarketCode.from_string(market_code)
    market_info = MarketInfo(
        name=name,
        full_name=full_name,
        country=country,
        currency=currency,
        market_type=MarketType(market_type),
        established_date=established_date,
        trading_hours=trading_hours
    )
    
    return Market(market_code_vo, market_info)


def create_chinese_stock_market(market_code: str, name: str, full_name: str) -> Market:
    """创建中国股票市场的便捷函数"""
    from datetime import time
    
    trading_hours = TradingHours(
        morning_start=time(9, 30),
        morning_end=time(11, 30),
        afternoon_start=time(13, 0),
        afternoon_end=time(15, 0),
        timezone="Asia/Shanghai"
    )
    
    return create_market(
        market_code=market_code,
        name=name,
        full_name=full_name,
        country="中国",
        currency="CNY",
        market_type="STOCK",
        established_date=date(1990, 12, 19),  # 上交所成立日期
        trading_hours=trading_hours
    )
