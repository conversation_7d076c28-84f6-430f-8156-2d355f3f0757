#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的性能监控脚本
专门用于监控MythQuant系统的性能基准，无外部依赖
"""

import sys
import os
import time
import json
import subprocess
from datetime import datetime
from typing import Dict, List, Any
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 可选依赖：psutil用于系统监控
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class SimplePerformanceMonitor:
    """简化的性能监控器"""
    
    def __init__(self, project_root: str):
        self.project_root = project_root
        self.main_script = os.path.join(project_root, "main_v20230219_optimized.py")
        self.results_dir = os.path.join(project_root, "benchmark_results")
        
        # 确保结果目录存在
        os.makedirs(self.results_dir, exist_ok=True)
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = {
            'timestamp': datetime.now().isoformat(),
            'python_version': sys.version,
            'platform': sys.platform
        }
        
        if PSUTIL_AVAILABLE:
            try:
                info.update({
                    'cpu_count': psutil.cpu_count(),
                    'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                    'memory_available_gb': psutil.virtual_memory().available / (1024**3),
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'psutil_available': True
                })
            except Exception as e:
                info['psutil_error'] = str(e)
                info['psutil_available'] = False
        else:
            info['psutil_available'] = False
        
        return info
    
    def run_performance_test(self, test_name: str = "性能基准测试") -> Dict[str, Any]:
        """运行性能测试"""
        logger.info(f"🚀 开始性能测试: {test_name}")
        
        # 获取测试前系统状态
        pre_test_info = self.get_system_info()
        
        start_time = time.time()
        start_datetime = datetime.now()
        
        try:
            # 运行主程序
            result = subprocess.run(
                [sys.executable, self.main_script],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
                env=os.environ.copy()  # 继承当前环境变量
            )
            
            end_time = time.time()
            end_datetime = datetime.now()
            execution_time = end_time - start_time
            
            # 获取测试后系统状态
            post_test_info = self.get_system_info()
            
            # 验证输出文件
            output_verification = self._verify_output_files()
            
            test_result = {
                'test_name': test_name,
                'timestamp': start_datetime.isoformat(),
                'execution_time': execution_time,
                'return_code': result.returncode,
                'success': result.returncode == 0,
                'stdout_lines': len(result.stdout.split('\n')) if result.stdout else 0,
                'stderr_lines': len(result.stderr.split('\n')) if result.stderr else 0,
                'output_verification': output_verification,
                'system_info': {
                    'pre_test': pre_test_info,
                    'post_test': post_test_info
                }
            }
            
            if result.returncode == 0:
                logger.info(f"✅ 性能测试完成: {test_name} - {execution_time:.2f}秒")
            else:
                logger.error(f"❌ 性能测试失败: {test_name} - 返回码: {result.returncode}")
                if result.stderr:
                    logger.error(f"错误信息: {result.stderr[:200]}...")
            
            return test_result
            
        except subprocess.TimeoutExpired:
            logger.error(f"❌ 性能测试超时: {test_name}")
            return {
                'test_name': test_name,
                'timestamp': start_datetime.isoformat(),
                'execution_time': 300,
                'success': False,
                'timeout': True,
                'system_info': {'pre_test': pre_test_info}
            }
        
        except Exception as e:
            logger.error(f"❌ 性能测试异常: {test_name} - {e}")
            return {
                'test_name': test_name,
                'timestamp': start_datetime.isoformat(),
                'execution_time': 0,
                'success': False,
                'error': str(e),
                'system_info': {'pre_test': pre_test_info}
            }
    
    def _verify_output_files(self) -> Dict[str, Any]:
        """验证输出文件"""
        output_dir = "H:/MPV1.17/T0002/signals/"
        expected_files = ["day_0_000617_20150101-20250731.txt"]
        
        verification = {
            'files_checked': len(expected_files),
            'files_found': 0,
            'files_valid': 0,
            'total_size_bytes': 0,
            'total_lines': 0,
            'file_details': {}
        }
        
        for file_name in expected_files:
            file_path = os.path.join(output_dir, file_name)
            
            if os.path.exists(file_path):
                verification['files_found'] += 1
                
                try:
                    file_stats = os.stat(file_path)
                    file_size = file_stats.st_size
                    verification['total_size_bytes'] += file_size
                    
                    # 验证文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    line_count = len(lines)
                    verification['total_lines'] += line_count
                    
                    header_correct = len(lines) > 0 and "股票编码|时间|买卖差" in lines[0]
                    has_data = line_count > 1
                    
                    # 验证前复权数据
                    forward_adj_correct = False
                    if has_data and len(lines) > 1:
                        try:
                            data_line = lines[1].strip().split('|')
                            if len(data_line) >= 5:
                                original_price = float(data_line[3])
                                adjusted_price = float(data_line[4])
                                forward_adj_correct = abs(original_price - adjusted_price) > 0.01
                        except (ValueError, IndexError):
                            pass
                    
                    is_valid = header_correct and has_data and forward_adj_correct
                    if is_valid:
                        verification['files_valid'] += 1
                    
                    verification['file_details'][file_name] = {
                        'exists': True,
                        'size_bytes': file_size,
                        'size_mb': file_size / (1024 * 1024),
                        'line_count': line_count,
                        'header_correct': header_correct,
                        'has_data': has_data,
                        'forward_adj_correct': forward_adj_correct,
                        'valid': is_valid
                    }
                    
                except Exception as e:
                    verification['file_details'][file_name] = {
                        'exists': True,
                        'error': str(e),
                        'valid': False
                    }
            else:
                verification['file_details'][file_name] = {
                    'exists': False,
                    'valid': False
                }
        
        return verification
    
    def run_baseline_benchmark(self) -> Dict[str, Any]:
        """运行基线基准测试"""
        logger.info("📊 开始基线基准测试")
        
        # 运行3次测试取平均值
        test_results = []
        
        for i in range(3):
            test_name = f"基线测试_{i+1}"
            logger.info(f"🔄 执行第 {i+1}/3 次测试")
            
            result = self.run_performance_test(test_name)
            test_results.append(result)
            
            # 如果不是最后一次，等待5秒
            if i < 2:
                time.sleep(5)
        
        # 分析结果
        successful_tests = [r for r in test_results if r.get('success', False)]
        
        if not successful_tests:
            return {
                'baseline_established': False,
                'error': '所有基线测试都失败了',
                'test_results': test_results
            }
        
        execution_times = [r['execution_time'] for r in successful_tests]
        
        baseline = {
            'baseline_established': True,
            'timestamp': datetime.now().isoformat(),
            'test_count': len(test_results),
            'successful_count': len(successful_tests),
            'success_rate': len(successful_tests) / len(test_results),
            'performance_metrics': {
                'avg_execution_time': sum(execution_times) / len(execution_times),
                'min_execution_time': min(execution_times),
                'max_execution_time': max(execution_times),
                'execution_times': execution_times
            },
            'output_verification': successful_tests[0]['output_verification'],
            'system_info': successful_tests[0]['system_info']['pre_test'],
            'test_results': test_results
        }
        
        # 保存基线数据
        baseline_file = os.path.join(self.results_dir, "performance_baseline.json")
        with open(baseline_file, 'w', encoding='utf-8') as f:
            json.dump(baseline, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"📊 基线基准已建立并保存到: {baseline_file}")
        
        return baseline
    
    def generate_performance_report(self, baseline: Dict[str, Any]) -> str:
        """生成性能报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.results_dir, f"performance_report_{timestamp}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("MythQuant 性能基准报告\n")
            f.write("=" * 50 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            if baseline.get('baseline_established'):
                metrics = baseline['performance_metrics']
                f.write("📊 性能基准数据:\n")
                f.write(f"  测试次数: {baseline['test_count']}\n")
                f.write(f"  成功次数: {baseline['successful_count']}\n")
                f.write(f"  成功率: {baseline['success_rate']:.1%}\n")
                f.write(f"  平均执行时间: {metrics['avg_execution_time']:.2f} 秒\n")
                f.write(f"  最短执行时间: {metrics['min_execution_time']:.2f} 秒\n")
                f.write(f"  最长执行时间: {metrics['max_execution_time']:.2f} 秒\n\n")
                
                # 输出验证信息
                verification = baseline['output_verification']
                f.write("📋 输出文件验证:\n")
                f.write(f"  检查文件数: {verification['files_checked']}\n")
                f.write(f"  找到文件数: {verification['files_found']}\n")
                f.write(f"  有效文件数: {verification['files_valid']}\n")
                f.write(f"  总文件大小: {verification.get('total_size_bytes', 0):,} bytes\n")
                f.write(f"  总数据行数: {verification.get('total_lines', 0):,} 行\n\n")
                
                # 系统信息
                sys_info = baseline['system_info']
                f.write("💻 系统环境:\n")
                f.write(f"  Python版本: {sys_info.get('python_version', 'Unknown')}\n")
                f.write(f"  平台: {sys_info.get('platform', 'Unknown')}\n")
                if sys_info.get('psutil_available'):
                    f.write(f"  CPU核心数: {sys_info.get('cpu_count', 'Unknown')}\n")
                    f.write(f"  总内存: {sys_info.get('memory_total_gb', 0):.1f} GB\n")
                    f.write(f"  可用内存: {sys_info.get('memory_available_gb', 0):.1f} GB\n")
                
            else:
                f.write("❌ 基准测试失败\n")
                f.write(f"错误: {baseline.get('error', '未知错误')}\n")
        
        logger.info(f"📋 性能报告已生成: {report_file}")
        return report_file


def main():
    """主函数"""
    print("🚀 MythQuant 简化性能监控系统")
    print("=" * 50)
    
    # 初始化监控器
    project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
    monitor = SimplePerformanceMonitor(project_root)
    
    # 运行基线基准测试
    print("📊 建立性能基线...")
    baseline = monitor.run_baseline_benchmark()
    
    # 生成报告
    print("📋 生成性能报告...")
    report_file = monitor.generate_performance_report(baseline)
    
    # 输出摘要
    print("\n📈 性能基准摘要:")
    if baseline.get('baseline_established'):
        metrics = baseline['performance_metrics']
        print(f"   平均执行时间: {metrics['avg_execution_time']:.2f} 秒")
        print(f"   性能稳定性: {baseline['success_rate']:.1%}")
        print(f"   输出文件验证: {baseline['output_verification']['files_valid']}/{baseline['output_verification']['files_checked']} 通过")
    else:
        print("   ❌ 基准测试失败")
    
    print(f"\n📊 详细报告: {report_file}")
    
    return baseline.get('baseline_established', False)


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        sys.exit(1)
