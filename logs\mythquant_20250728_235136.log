2025-07-28 23:51:36,724 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_235136.log
2025-07-28 23:51:36,724 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-28 23:51:36,724 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-28 23:51:36,725 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 23:51:36,725 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 23:51:36,725 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 23:51:36,725 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 23:51:36,726 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 23:51:36,726 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 23:51:36,732 - Main - INFO - info:307 - ℹ️ ⚖️ 开始增量下载可行性分析
2025-07-28 23:51:37,209 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:51:37,209 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:51:37,209 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-28 23:51:37,414 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 2天, 频率: 1min)
2025-07-28 23:51:37,414 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:51:37,414 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:51:37,414 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:51:37,465 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 23:51:37,690 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 23:51:37,690 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 23:51:37,691 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 23:51:37,691 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:51:37,691 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250725
2025-07-28 23:51:37,696 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 23:51:37,697 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 23:51:37,697 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:51:37,699 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-28 23:51:37,699 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:51:37,700 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-28 23:51:37,700 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-28 23:51:37,700 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-28 23:51:37,700 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 23:51:37,700 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:51:37,700 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:51:37,706 - Main - INFO - info:307 - ℹ️ ✅ 增量下载可行性验证通过
2025-07-28 23:51:37,706 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-28 23:51:37,706 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-28 23:51:37,706 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-28 23:51:37,742 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数21360, 覆盖89个交易日
2025-07-28 23:51:37,742 - Main - INFO - info:307 - ℹ️ ✅ 数据完整，无缺失
2025-07-28 23:51:37,743 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-28 23:51:37,744 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-28 23:51:37,744 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-28 23:51:37,745 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250318-20250725_来源互联网（202507282350）.txt
2025-07-28 23:51:37,745 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250318-20250725_来源互联网（202507282350）.txt
2025-07-28 23:51:37,746 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-28 23:51:37,762 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:51:37,762 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:51:37,762 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-28 23:51:37,932 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 2天, 频率: 1min)
2025-07-28 23:51:37,933 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:51:37,933 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:51:37,933 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:51:37,985 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 23:51:38,211 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 23:51:38,211 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 23:51:38,212 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 23:51:38,212 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:51:38,212 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250725
2025-07-28 23:51:38,219 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 23:51:38,221 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 23:51:38,221 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:51:38,225 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-28 23:51:38,225 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:51:38,226 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-28 23:51:38,226 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-28 23:51:38,226 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-28 23:51:38,226 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 23:51:38,226 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:51:38,227 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:51:38,238 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-28 23:51:38,240 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-28 23:51:38,241 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250318 - 20250725
2025-07-28 23:51:38,241 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250317
2025-07-28 23:51:38,241 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250726 - 20250727
2025-07-28 23:51:38,261 - Main - INFO - info:307 - ℹ️ 读取现有数据: 21360 条记录
2025-07-28 23:51:38,261 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250317
2025-07-28 23:51:38,262 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:51:38,262 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:51:38,262 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250317
2025-07-28 23:51:38,451 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32640条 (目标日期: 20250101, 交易日: 136天, 频率: 1min)
2025-07-28 23:51:38,451 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32640条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:51:38,451 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:51:38,451 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:51:38,504 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需31840条
2025-07-28 23:51:38,723 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 23:51:38,945 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 23:51:39,171 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 23:51:39,401 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 23:51:39,635 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-28 23:51:39,875 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-28 23:51:40,099 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-28 23:51:40,321 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-28 23:51:40,550 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-28 23:51:40,782 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-28 23:51:41,003 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-28 23:51:41,227 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-28 23:51:41,451 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-28 23:51:41,681 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-28 23:51:41,898 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-28 23:51:42,129 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-28 23:51:42,357 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-28 23:51:42,581 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-28 23:51:42,799 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-28 23:51:43,020 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-28 23:51:43,245 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-28 23:51:43,464 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-28 23:51:43,698 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-28 23:51:43,919 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-28 23:51:44,154 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-28 23:51:44,368 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-28 23:51:44,587 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21600条数据
2025-07-28 23:51:44,592 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32640条，实际获取21600条
2025-07-28 23:51:44,592 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11040条数据（约46个交易日）
2025-07-28 23:51:44,593 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-28 23:51:44,593 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-28 23:51:44,593 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-28 23:51:44,646 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250101 - 20250317
2025-07-28 23:51:44,652 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-28 23:51:44,652 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-28 23:51:44,653 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250726 - 20250727
2025-07-28 23:51:44,653 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:51:44,653 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:51:44,653 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250726 - 20250727
2025-07-28 23:51:44,825 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250726, 交易日: 1天, 频率: 1min)
2025-07-28 23:51:44,825 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:51:44,825 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:51:44,825 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:51:44,875 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 23:51:45,091 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 23:51:45,092 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 23:51:45,092 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 23:51:45,092 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:51:45,092 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250726
2025-07-28 23:51:45,095 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250726 - 20250727
2025-07-28 23:51:45,096 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-28 23:51:45,096 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-28 23:51:45,096 - Main - INFO - info:307 - ℹ️ ✅ 现有数据已覆盖目标范围，无需下载新数据
2025-07-28 23:51:45,097 - Main - INFO - info:307 - ℹ️ ✅ 添加时间戳标识: 1min_0_000617_20250318-20250725_来源互联网（202507282351）.txt
2025-07-28 23:51:45,097 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-28 23:51:45,098 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-28 23:51:45,098 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 23:51:45,098 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-28 23:51:45,099 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-28 23:51:45,099 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-28 23:51:45,099 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-28 23:51:45,099 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-28 23:51:45,099 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-28 23:51:45,099 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-28 23:51:45,101 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250728_235145.txt
2025-07-28 23:51:45,101 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-28 23:51:45,101 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-28 23:51:45,101 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-28 23:51:45,103 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 23:51:45,103 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 23:51:45,103 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-28 23:51:45,103 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-28 23:51:45,103 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-28 23:51:45,103 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 23:51:45,103 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 23:51:45,104 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
