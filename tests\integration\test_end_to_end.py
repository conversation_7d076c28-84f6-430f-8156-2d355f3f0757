#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
端到端集成测试

验证整个系统的完整功能流程，确保所有组件协同工作
"""

import pytest
import asyncio
import time
from datetime import datetime, timedelta
from decimal import Decimal
from typing import List, Dict, Any

from src.mythquant.core.application import Application
from src.mythquant.core.config_manager import ConfigManager
from src.mythquant.domain.models.stock import Stock, StockCode, Price
from src.mythquant.domain.models.market import Market, TradingSession
from src.mythquant.domain.algorithms.financial import simple_return, sharpe_ratio
from src.mythquant.domain.algorithms.technical import sma, rsi, macd
from src.mythquant.infrastructure.caching.cache_manager import get_default_cache
from src.mythquant.infrastructure.monitoring.apm import get_apm_manager
from src.mythquant.infrastructure.monitoring.metrics import get_metrics_collector
from src.mythquant.infrastructure.events.event_bus import get_event_bus
from src.mythquant.infrastructure.events.event_store import get_event_store


class TestEndToEndIntegration:
    """端到端集成测试套件"""
    
    @pytest.fixture(autouse=True)
    def setup_system(self):
        """设置测试系统"""
        # 启动APM监控
        apm_manager = get_apm_manager()
        apm_manager.start()
        
        # 启动指标收集
        metrics_collector = get_metrics_collector()
        metrics_collector.start_collection(interval=1)
        
        # 清理缓存
        cache_manager = get_default_cache()
        cache_manager.clear()
        
        yield
        
        # 清理
        apm_manager.stop()
        metrics_collector.stop_collection()
        cache_manager.clear()
    
    def test_complete_stock_data_workflow(self):
        """测试完整的股票数据工作流"""
        # 1. 创建应用实例
        app = Application()
        
        # 2. 创建股票
        stock_code = StockCode("000001")
        stock = Stock.create(
            stock_code=stock_code,
            name="平安银行",
            market="SZ"
        )
        
        # 3. 添加价格数据
        prices = [
            Price(Decimal("10.00")),
            Price(Decimal("10.50")),
            Price(Decimal("11.00")),
            Price(Decimal("10.80")),
            Price(Decimal("11.20"))
        ]
        
        for price in prices:
            stock.update_price(price)
        
        # 4. 验证股票状态
        assert stock.current_price == Price(Decimal("11.20"))
        assert len(stock.price_history) == 5
        
        # 5. 计算技术指标
        price_values = [float(p.value) for p in prices]
        
        # SMA计算
        sma_result = sma(price_values, period=3)
        assert len(sma_result) == 3
        assert abs(sma_result[-1] - 11.0) < 0.01
        
        # RSI计算
        rsi_result = rsi(price_values, period=4)
        assert len(rsi_result) == 2
        assert 0 <= rsi_result[-1] <= 100
        
        # 6. 计算金融指标
        returns = simple_return(price_values)
        assert len(returns) == 4
        
        # 7. 验证事件发布
        event_store = get_event_store()
        events = event_store.get_all_events()
        assert len(events) >= 5  # 至少有5个价格变化事件
    
    def test_caching_system_integration(self):
        """测试缓存系统集成"""
        cache_manager = get_default_cache()
        
        # 1. 测试基本缓存操作
        test_key = "test:stock:000001"
        test_data = {
            "stock_code": "000001",
            "name": "平安银行",
            "price": 11.20,
            "timestamp": datetime.now().isoformat()
        }
        
        # 设置缓存
        success = cache_manager.set(test_key, test_data, ttl=60)
        assert success
        
        # 获取缓存
        cached_data = cache_manager.get(test_key)
        assert cached_data is not None
        assert cached_data["stock_code"] == "000001"
        assert cached_data["name"] == "平安银行"
        
        # 2. 测试缓存统计
        stats = cache_manager.get_stats()
        assert "manager_stats" in stats
        assert "provider_stats" in stats
        assert stats["manager_stats"]["hits"] >= 1
        assert stats["manager_stats"]["sets"] >= 1
        
        # 3. 测试缓存失效
        deleted = cache_manager.delete(test_key)
        assert deleted
        
        cached_data = cache_manager.get(test_key)
        assert cached_data is None
    
    def test_monitoring_system_integration(self):
        """测试监控系统集成"""
        apm_manager = get_apm_manager()
        
        # 1. 测试事务追踪
        tracer = apm_manager.transaction_tracer
        
        with tracer.transaction("test_calculation") as txn_id:
            # 模拟计算工作
            time.sleep(0.1)
            
            # 添加一些跨度
            with tracer.start_span("data_loading") as span:
                span.set_tag("data_source", "test")
                span.log("Loading test data")
                time.sleep(0.05)
            
            with tracer.start_span("calculation") as span:
                span.set_tag("algorithm", "sma")
                span.log("Calculating SMA")
                time.sleep(0.03)
        
        # 2. 验证事务记录
        completed_transactions = tracer.get_completed_transactions(limit=10)
        assert len(completed_transactions) >= 1
        
        test_txn = completed_transactions[-1]
        assert test_txn["name"] == "test_calculation"
        assert test_txn["status"] == "completed"
        assert test_txn["duration"] > 0.1
        
        # 3. 测试性能监控
        performance_monitor = apm_manager.performance_monitor
        current_stats = performance_monitor.get_current_stats()
        
        assert "cpu" in current_stats
        assert "memory" in current_stats
        assert "timestamp" in current_stats
        
        # 4. 测试指标收集
        metrics_collector = get_metrics_collector()
        samples = metrics_collector.collect_once()
        assert len(samples) >= 0  # 可能没有注册的指标
    
    def test_event_driven_architecture(self):
        """测试事件驱动架构"""
        event_bus = get_event_bus()
        event_store = get_event_store()
        
        # 1. 清理现有事件
        initial_count = event_store.get_event_count()
        
        # 2. 创建股票并触发事件
        stock = Stock.create(
            stock_code=StockCode("000002"),
            name="万科A",
            market="SZ"
        )
        
        # 3. 更新价格触发事件
        old_price = stock.current_price
        new_price = Price(Decimal("25.50"))
        stock.update_price(new_price)
        
        # 4. 发布事件到事件总线
        events = stock.uncommitted_events
        for event in events:
            event_bus.publish(event)
        
        # 5. 验证事件存储
        final_count = event_store.get_event_count()
        assert final_count > initial_count
        
        # 6. 验证事件总线统计
        bus_stats = event_bus.get_metrics()
        assert bus_stats["events_published"] >= len(events)
    
    def test_algorithm_performance_integration(self):
        """测试算法性能集成"""
        # 1. 生成测试数据
        test_data = []
        base_price = 100.0
        
        for i in range(1000):
            # 模拟价格波动
            change = (i % 10 - 5) * 0.1
            price = base_price + change + (i * 0.01)
            test_data.append(price)
        
        # 2. 测试算法性能
        start_time = time.time()
        
        # SMA计算
        sma_20 = sma(test_data, period=20)
        sma_50 = sma(test_data, period=50)
        
        # RSI计算
        rsi_14 = rsi(test_data, period=14)
        
        # MACD计算
        macd_result = macd(test_data, fast_period=12, slow_period=26, signal_period=9)
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        # 3. 验证结果
        assert len(sma_20) == len(test_data) - 19
        assert len(sma_50) == len(test_data) - 49
        assert len(rsi_14) == len(test_data) - 14
        assert len(macd_result["macd_line"]) > 0
        
        # 4. 验证性能
        assert execution_time < 1.0  # 应该在1秒内完成
        
        # 5. 验证数值合理性
        assert all(0 <= rsi_val <= 100 for rsi_val in rsi_14)
        assert all(isinstance(sma_val, float) for sma_val in sma_20)
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        # 1. 测试无效股票代码
        with pytest.raises(ValueError):
            StockCode("")  # 空股票代码
        
        with pytest.raises(ValueError):
            StockCode("INVALID")  # 无效格式
        
        # 2. 测试无效价格
        with pytest.raises(ValueError):
            Price(Decimal("-10.00"))  # 负价格
        
        # 3. 测试算法错误处理
        with pytest.raises(ValueError):
            sma([], period=10)  # 空数据
        
        with pytest.raises(ValueError):
            sma([1, 2, 3], period=5)  # 数据不足
        
        # 4. 测试缓存错误处理
        cache_manager = get_default_cache()
        
        # 无效键
        result = cache_manager.get("")
        assert result is None
        
        # 无效TTL
        success = cache_manager.set("test", "value", ttl=-1)
        assert not success
    
    def test_configuration_integration(self):
        """测试配置系统集成"""
        config_manager = ConfigManager()
        
        # 1. 测试配置加载
        config = config_manager.get_config()
        assert config is not None
        
        # 2. 测试配置访问
        debug_mode = config_manager.get("system.debug_mode", False)
        assert isinstance(debug_mode, bool)
        
        log_level = config_manager.get("system.log_level", "INFO")
        assert log_level in ["DEBUG", "INFO", "WARNING", "ERROR"]
        
        # 3. 测试配置验证
        assert config_manager.validate_config()
        
        # 4. 测试配置更新
        original_value = config_manager.get("system.max_workers", 4)
        config_manager.set("system.max_workers", 8)
        
        updated_value = config_manager.get("system.max_workers")
        assert updated_value == 8
        
        # 恢复原值
        config_manager.set("system.max_workers", original_value)
    
    def test_full_system_stress(self):
        """测试系统压力"""
        # 1. 创建多个股票
        stocks = []
        for i in range(10):
            stock_code = StockCode(f"00000{i}")
            stock = Stock.create(
                stock_code=stock_code,
                name=f"测试股票{i}",
                market="SZ"
            )
            stocks.append(stock)
        
        # 2. 批量更新价格
        for stock in stocks:
            for j in range(100):
                price = Price(Decimal(f"{10 + j * 0.1:.2f}"))
                stock.update_price(price)
        
        # 3. 批量计算指标
        cache_manager = get_default_cache()
        
        for i, stock in enumerate(stocks):
            price_values = [float(p.value) for p in stock.price_history]
            
            # 缓存计算结果
            cache_key = f"sma:20:{stock.stock_code.value}"
            sma_result = sma(price_values, period=20)
            cache_manager.set(cache_key, sma_result, ttl=300)
            
            cache_key = f"rsi:14:{stock.stock_code.value}"
            rsi_result = rsi(price_values, period=14)
            cache_manager.set(cache_key, rsi_result, ttl=300)
        
        # 4. 验证缓存性能
        cache_stats = cache_manager.get_stats()
        assert cache_stats["manager_stats"]["sets"] >= 20
        
        # 5. 验证系统稳定性
        apm_manager = get_apm_manager()
        performance_stats = apm_manager.performance_monitor.get_current_stats()
        
        # 内存使用应该在合理范围内
        memory_percent = performance_stats.get("memory", {}).get("percent", 0)
        assert memory_percent < 80  # 内存使用不应超过80%


@pytest.mark.asyncio
class TestAsyncIntegration:
    """异步集成测试"""
    
    async def test_async_operations(self):
        """测试异步操作"""
        # 1. 异步缓存操作
        cache_manager = get_default_cache()
        
        async def async_cache_operation():
            await asyncio.sleep(0.01)  # 模拟异步操作
            cache_manager.set("async:test", {"value": 123}, ttl=60)
            return cache_manager.get("async:test")
        
        result = await async_cache_operation()
        assert result is not None
        assert result["value"] == 123
        
        # 2. 并发操作测试
        async def concurrent_operations():
            tasks = []
            for i in range(10):
                task = asyncio.create_task(async_cache_operation())
                tasks.append(task)
            
            results = await asyncio.gather(*tasks)
            return results
        
        results = await concurrent_operations()
        assert len(results) == 10
        assert all(r is not None for r in results)


if __name__ == "__main__":
    pytest.main([__file__, "-v"])
