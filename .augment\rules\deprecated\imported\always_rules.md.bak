# MythQuant项目始终遵循规则

## 吹哨人原则
- 如果我提的问题、要求你觉得有不合理的地方，你可以对我提的问题进行优化修改并且通过交互与我确认，征求我的同意后即可使用你优化后的问题重新提问了

## 金融计算精度要求
- 所有价格计算必须使用 `decimal.Decimal` 类型 (2025-07-23)
- 价格比较使用容差比较：`abs(价格1 - 价格2) < Decimal('0.001')` (2025-07-23)
- 统一精度标准：价格保留3位小数，比率保留6位小数 (2025-07-23)
- 避免浮点数累积误差，特别是在循环计算中 (2025-07-23)

## 代码质量标准
- 数据输入必须进行验证：检查None值、空值、数据类型 (2025-07-23)
- 优先使用pandas向量化操作而非循环 (2025-07-23)
- 实现多级缓存：内存缓存、磁盘缓存、数据库缓存 (2025-07-23)
- 大数据处理使用分块处理策略 (2025-07-23)

## 架构设计原则
- 采用分层架构：数据层、业务层、展示层 (2025-07-24)
- 实现降级处理机制：主方法失败时有备用方案 (2025-07-24)
- 保持高内聚低耦合：模块职责单一，接口清晰 (2025-07-24)
- 使用安全默认值：配置项都有合理默认值 (2025-07-24)

## DDD架构实施规则 (2025-08-06)
- **分层架构严格遵循**：领域层(Domain) → 应用层(Application) → 基础设施层(Infrastructure) → 接口层(Interface) (2025-08-06)
- **依赖倒置原则**：领域层不依赖任何外部框架，通过接口定义依赖关系 (2025-08-06)
- **向后兼容性保证**：新架构必须完全兼容现有代码，用户无需修改使用方式 (2025-08-06)
- **配置管理统一**：通过DDD架构统一管理配置，但保持user_config.py的用户友好性 (2025-08-06)
- **门面模式应用**：使用门面模式隐藏DDD架构复杂性，提供简化的外部接口 (2025-08-06)

## 主程序架构规则
- **统一入口**：使用 `main.py` 作为唯一主程序入口，采用模块化架构设计 (2025-07-26)
- **模块化设计**：核心逻辑分离到专门模块，主程序专注于启动和流程控制 (2025-07-26)
- **功能完整性**：确保所有核心功能在模块化架构中得到完整实现 (2025-07-26)
- **向后兼容**：保留 `main_v20230219_optimized.py` 作为参考，但不再维护双入口 (2025-07-26)

## AI调试环境强制规则 (2025-08-10)
- **自动测试环境切换**：AI调试代码时必须自动走测试环境，通过user_config.py中的detect_environment()函数检测 (2025-08-09)
- **调试模式识别**：通过文件名模式识别AI调试（tools/、test_、debug_、detector、tracker、precise_等） (2025-08-09)
- **测试文件强制使用**：在测试环境中必须使用test_前缀的标准测试文件，不得使用生产环境文件 (2025-08-09)
- **环境隔离保证**：确保AI调试不影响生产环境，所有调试操作限制在测试环境中 (2025-08-09)
- **配置自动适配**：智能文件选择器等组件必须根据环境配置自动适配目录和文件选择策略 (2025-08-09)
- **测试条件一致性**：每次AI调试都使用相同的测试文件和初始状态，确保结果可比较 (2025-08-09)
- **禁止生产环境验证**：严禁直接运行main.py进行修复验证，必须在test_environments中创建专门的测试脚本 (2025-08-10)
- **测试脚本位置规范**：所有AI调试和验证脚本必须位于test_environments/对应测试类型/目录中 (2025-08-10)

## 反复问题预防规则 (2025-08-10)
- **问题模式识别**：建立常见问题的识别模式，防止同类问题反复出现 (2025-07-26)
- **根本原因修复**：不满足于表面修复，必须找到并解决问题的根本原因 (2025-07-26)
- **系统性预防机制**：建立预防机制而非被动修复，如格式验证、数据源限制检查 (2025-07-26)
- **用户反馈重视**：用户反复提及的问题说明修复不彻底，必须重新审视解决方案 (2025-07-26)
- **实际验证强制**：声称问题"已解决"前必须通过实际数据验证，不能基于理论推测 (2025-07-26)
- **知识沉淀机制**：每次解决反复问题后，必须将经验沉淀到知识库和规则中 (2025-07-26)
- **函数名称冲突预防**：严禁在同一模块中定义重复的函数名，建立自动化检测机制 (2025-08-10)
- **集成测试强制**：单元测试无法发现的集成问题必须通过专门的集成测试发现和预防 (2025-08-10)

## 数据质量验证标准 (2025-08-10)
- **文件命名格式验证**：频率前缀_0_股票代码_时间范围_来源.txt（如1min_0_000617_...而不是1_0_000617_...）
- **A股1分钟数据格式标准**：时间戳表示分钟结束时间，09:31是第一条数据（表示09:30-09:31），13:01是中午第一条数据（表示13:00-13:01） (2025-08-10)
- **数据完整性简化标准**：每个交易日240行数据即为完整，不需要检查具体时间点，避免过度复杂化 (2025-08-10)
- **前复权价格验证**：前复权收盘价与当日收盘价必须有合理差异，不能完全相同
- **数据完整性验证**：包含所有必要字段且格式正确，字段分隔符为管道符"|"
- **数据抽样检查**：必须随机检查生成文件的前10行数据质量，验证数据准确性
- **数据质量验证失败**：即使文件生成成功，如果数据质量不合格也算任务失败

---

**备份时间**: 2025-08-10
**原始文件**: .augment/rules/imported/always_rules.md
