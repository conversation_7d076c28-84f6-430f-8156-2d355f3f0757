#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
领域异常

定义领域层的业务异常
"""

from typing import Optional, Any, Dict


class DomainException(Exception):
    """领域异常基类"""
    
    def __init__(self, message: str, error_code: Optional[str] = None, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(message)
        self.message = message
        self.error_code = error_code or self.__class__.__name__
        self.context = context or {}
    
    def __str__(self) -> str:
        return f"[{self.error_code}] {self.message}"
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'error_type': self.__class__.__name__,
            'error_code': self.error_code,
            'message': self.message,
            'context': self.context
        }


# =============================================================================
# 业务规则异常
# =============================================================================

class BusinessRuleViolationError(DomainException):
    """业务规则违反异常"""
    
    def __init__(self, rule_name: str, message: str, 
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"业务规则违反 [{rule_name}]: {message}",
            error_code="BUSINESS_RULE_VIOLATION",
            context=context
        )
        self.rule_name = rule_name


class InvariantViolationError(DomainException):
    """不变量违反异常"""
    
    def __init__(self, invariant_name: str, message: str,
                 context: Optional[Dict[str, Any]] = None):
        super().__init__(
            message=f"不变量违反 [{invariant_name}]: {message}",
            error_code="INVARIANT_VIOLATION", 
            context=context
        )
        self.invariant_name = invariant_name


# =============================================================================
# 标识符异常
# =============================================================================

class InvalidIdentifierError(DomainException):
    """无效标识符异常基类"""
    pass


class InvalidStockCodeError(InvalidIdentifierError):
    """无效股票代码异常"""
    
    def __init__(self, stock_code: str, reason: Optional[str] = None):
        message = f"无效的股票代码: {stock_code}"
        if reason:
            message += f" - {reason}"
        
        super().__init__(
            message=message,
            error_code="INVALID_STOCK_CODE",
            context={'stock_code': stock_code, 'reason': reason}
        )
        self.stock_code = stock_code


class InvalidMarketCodeError(InvalidIdentifierError):
    """无效市场代码异常"""
    
    def __init__(self, market_code: str, reason: Optional[str] = None):
        message = f"无效的市场代码: {market_code}"
        if reason:
            message += f" - {reason}"
        
        super().__init__(
            message=message,
            error_code="INVALID_MARKET_CODE",
            context={'market_code': market_code, 'reason': reason}
        )
        self.market_code = market_code


# =============================================================================
# 价格和金额异常
# =============================================================================

class InvalidPriceError(DomainException):
    """无效价格异常"""
    
    def __init__(self, price: Any, reason: Optional[str] = None):
        message = f"无效的价格: {price}"
        if reason:
            message += f" - {reason}"
        
        super().__init__(
            message=message,
            error_code="INVALID_PRICE",
            context={'price': str(price), 'reason': reason}
        )
        self.price = price


class NegativePriceError(InvalidPriceError):
    """负价格异常"""
    
    def __init__(self, price: Any):
        super().__init__(
            price=price,
            reason="价格不能为负数"
        )


class ZeroPriceError(InvalidPriceError):
    """零价格异常"""
    
    def __init__(self, price: Any):
        super().__init__(
            price=price,
            reason="价格不能为零"
        )


class InvalidVolumeError(DomainException):
    """无效成交量异常"""
    
    def __init__(self, volume: Any, reason: Optional[str] = None):
        message = f"无效的成交量: {volume}"
        if reason:
            message += f" - {reason}"
        
        super().__init__(
            message=message,
            error_code="INVALID_VOLUME",
            context={'volume': str(volume), 'reason': reason}
        )
        self.volume = volume


class NegativeVolumeError(InvalidVolumeError):
    """负成交量异常"""
    
    def __init__(self, volume: Any):
        super().__init__(
            volume=volume,
            reason="成交量不能为负数"
        )


# =============================================================================
# 时间和日期异常
# =============================================================================

class InvalidDateRangeError(DomainException):
    """无效日期范围异常"""
    
    def __init__(self, start_date: Any, end_date: Any, reason: Optional[str] = None):
        message = f"无效的日期范围: {start_date} 到 {end_date}"
        if reason:
            message += f" - {reason}"
        
        super().__init__(
            message=message,
            error_code="INVALID_DATE_RANGE",
            context={
                'start_date': str(start_date),
                'end_date': str(end_date),
                'reason': reason
            }
        )
        self.start_date = start_date
        self.end_date = end_date


class MarketClosedError(DomainException):
    """市场关闭异常"""
    
    def __init__(self, market_code: str, current_time: Optional[Any] = None):
        message = f"市场 {market_code} 当前关闭"
        if current_time:
            message += f" (当前时间: {current_time})"
        
        super().__init__(
            message=message,
            error_code="MARKET_CLOSED",
            context={
                'market_code': market_code,
                'current_time': str(current_time) if current_time else None
            }
        )
        self.market_code = market_code


class InvalidTradingTimeError(DomainException):
    """无效交易时间异常"""
    
    def __init__(self, time: Any, reason: Optional[str] = None):
        message = f"无效的交易时间: {time}"
        if reason:
            message += f" - {reason}"
        
        super().__init__(
            message=message,
            error_code="INVALID_TRADING_TIME",
            context={'time': str(time), 'reason': reason}
        )
        self.time = time


# =============================================================================
# 投资组合异常
# =============================================================================

class PortfolioError(DomainException):
    """投资组合异常基类"""
    pass


class InsufficientFundsError(PortfolioError):
    """资金不足异常"""
    
    def __init__(self, required_amount: Any, available_amount: Any):
        message = f"资金不足: 需要 {required_amount}, 可用 {available_amount}"
        
        super().__init__(
            message=message,
            error_code="INSUFFICIENT_FUNDS",
            context={
                'required_amount': str(required_amount),
                'available_amount': str(available_amount)
            }
        )
        self.required_amount = required_amount
        self.available_amount = available_amount


class InsufficientPositionError(PortfolioError):
    """持仓不足异常"""
    
    def __init__(self, stock_code: str, required_quantity: int, available_quantity: int):
        message = f"持仓不足 {stock_code}: 需要 {required_quantity}, 可用 {available_quantity}"
        
        super().__init__(
            message=message,
            error_code="INSUFFICIENT_POSITION",
            context={
                'stock_code': stock_code,
                'required_quantity': required_quantity,
                'available_quantity': available_quantity
            }
        )
        self.stock_code = stock_code
        self.required_quantity = required_quantity
        self.available_quantity = available_quantity


class PositionNotFoundError(PortfolioError):
    """持仓未找到异常"""
    
    def __init__(self, stock_code: str, portfolio_id: str):
        message = f"在投资组合 {portfolio_id} 中未找到股票 {stock_code} 的持仓"
        
        super().__init__(
            message=message,
            error_code="POSITION_NOT_FOUND",
            context={
                'stock_code': stock_code,
                'portfolio_id': portfolio_id
            }
        )
        self.stock_code = stock_code
        self.portfolio_id = portfolio_id


# =============================================================================
# 数据异常
# =============================================================================

class DataIntegrityError(DomainException):
    """数据完整性异常"""
    
    def __init__(self, entity_type: str, entity_id: str, reason: str):
        message = f"数据完整性错误 {entity_type}[{entity_id}]: {reason}"
        
        super().__init__(
            message=message,
            error_code="DATA_INTEGRITY_ERROR",
            context={
                'entity_type': entity_type,
                'entity_id': entity_id,
                'reason': reason
            }
        )
        self.entity_type = entity_type
        self.entity_id = entity_id


class EntityNotFoundError(DomainException):
    """实体未找到异常"""
    
    def __init__(self, entity_type: str, entity_id: str):
        message = f"未找到 {entity_type}[{entity_id}]"
        
        super().__init__(
            message=message,
            error_code="ENTITY_NOT_FOUND",
            context={
                'entity_type': entity_type,
                'entity_id': entity_id
            }
        )
        self.entity_type = entity_type
        self.entity_id = entity_id


class DuplicateEntityError(DomainException):
    """重复实体异常"""
    
    def __init__(self, entity_type: str, entity_id: str):
        message = f"重复的 {entity_type}[{entity_id}]"
        
        super().__init__(
            message=message,
            error_code="DUPLICATE_ENTITY",
            context={
                'entity_type': entity_type,
                'entity_id': entity_id
            }
        )
        self.entity_type = entity_type
        self.entity_id = entity_id


# =============================================================================
# 算法和计算异常
# =============================================================================

class CalculationError(DomainException):
    """计算异常"""
    
    def __init__(self, calculation_type: str, reason: str, 
                 context: Optional[Dict[str, Any]] = None):
        message = f"计算错误 [{calculation_type}]: {reason}"
        
        super().__init__(
            message=message,
            error_code="CALCULATION_ERROR",
            context=context or {}
        )
        self.calculation_type = calculation_type


class InsufficientDataError(CalculationError):
    """数据不足异常"""
    
    def __init__(self, calculation_type: str, required_points: int, available_points: int):
        super().__init__(
            calculation_type=calculation_type,
            reason=f"数据点不足: 需要 {required_points}, 可用 {available_points}",
            context={
                'required_points': required_points,
                'available_points': available_points
            }
        )


class InvalidParameterError(CalculationError):
    """无效参数异常"""
    
    def __init__(self, calculation_type: str, parameter_name: str, 
                 parameter_value: Any, reason: str):
        super().__init__(
            calculation_type=calculation_type,
            reason=f"无效参数 {parameter_name}={parameter_value}: {reason}",
            context={
                'parameter_name': parameter_name,
                'parameter_value': str(parameter_value),
                'reason': reason
            }
        )


# =============================================================================
# 异常工厂函数
# =============================================================================

def create_business_rule_violation(rule_name: str, message: str, 
                                 **context) -> BusinessRuleViolationError:
    """创建业务规则违反异常"""
    return BusinessRuleViolationError(rule_name, message, context)


def create_invariant_violation(invariant_name: str, message: str,
                             **context) -> InvariantViolationError:
    """创建不变量违反异常"""
    return InvariantViolationError(invariant_name, message, context)


def create_entity_not_found(entity_type: str, entity_id: str) -> EntityNotFoundError:
    """创建实体未找到异常"""
    return EntityNotFoundError(entity_type, entity_id)


def create_insufficient_data(calculation_type: str, required: int, 
                           available: int) -> InsufficientDataError:
    """创建数据不足异常"""
    return InsufficientDataError(calculation_type, required, available)
