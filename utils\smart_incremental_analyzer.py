#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能增量分析器
实现精准的时间范围分析和增量下载优化
"""

import os
import pandas as pd
from datetime import datetime, timedelta
from typing import Optional, Dict, Tuple, List
from decimal import Decimal

from core.logging_service import logging_service


class SmartIncrementalAnalyzer:
    """智能增量分析器"""
    
    def __init__(self):
        self.smart_logger = logging_service

    def analyze_existing_file_time_range(self, filepath: str) -> Optional[Dict[str, str]]:
        """
        分析现有文件的时间跨度和数据完整性

        Args:
            filepath: 文件路径

        Returns:
            时间范围信息字典或None
        """
        try:
            if not os.path.exists(filepath):
                return None

            self.smart_logger.verbose_log('info', f"📊 分析现有文件时间跨度: {os.path.basename(filepath)}")

            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if len(lines) < 2:  # 至少需要表头和一条数据
                self.smart_logger.verbose_log('warning', "文件数据不足，无法分析时间跨度")
                return None

            # 获取第二行（第一条数据）和最后一行数据
            first_data_line = lines[1].strip()
            last_data_line = lines[-1].strip()

            if not first_data_line or not last_data_line:
                return None

            # 解析时间字段（假设时间字段在第二列，索引为1）
            first_data_parts = first_data_line.split('|')
            last_data_parts = last_data_line.split('|')

            if len(first_data_parts) < 2 or len(last_data_parts) < 2:
                self.smart_logger.verbose_log('warning', "数据格式异常，无法解析时间字段")
                return None
            
            first_time = first_data_parts[1].strip()
            last_time = last_data_parts[1].strip()
            
            # 提取日期部分（前8位）
            first_date = str(first_time).replace('.0', '')[:8]
            last_date = str(last_time).replace('.0', '')[:8]
            
            result = {
                'first_time': first_time,
                'last_time': last_time,
                'first_date': first_date,
                'last_date': last_date,
                'total_records': len(lines) - 1  # 减去表头
            }

            self.smart_logger.verbose_log('info', f"📅 现有文件时间跨度: {first_date} ~ {last_date} ({result['total_records']}条记录)")

            # 检查数据完整性
            completeness_result = self.check_intraday_completeness(filepath)
            if not completeness_result.get('error') and completeness_result.get('has_incomplete_days'):
                result['has_incomplete_data'] = True
                result['incomplete_days'] = completeness_result['incomplete_days']
                self.smart_logger.verbose_log('warning', f"⚠️ 检测到数据不完整，可能需要补充下载")

                # 立即处理缺失数据
                self._handle_incomplete_data(filepath, result)
            else:
                result['has_incomplete_data'] = False

            return result
            
        except Exception as e:
            self.smart_logger.log_error(f"分析文件时间跨度失败: {e}")
            return None

    def _handle_incomplete_data(self, filepath: str, analysis_result: Dict[str, any]) -> None:
        """
        处理检测到的数据不完整问题

        Args:
            filepath: 文件路径
            analysis_result: 分析结果
        """
        try:
            self.smart_logger.verbose_log('info', f"🔧 开始处理数据不完整问题: {os.path.basename(filepath)}")

            # 从文件名提取股票代码
            stock_code = self._extract_stock_code_from_filepath(filepath)
            if not stock_code:
                self.smart_logger.verbose_log('error', f"❌ 无法从文件名提取股票代码: {os.path.basename(filepath)}")
                return

            self.smart_logger.verbose_log('info', f"📊 股票代码: {stock_code}")

            # 使用独立的缺失数据处理器
            try:
                from utils.missing_data_processor import MissingDataProcessor

                processor = MissingDataProcessor()

                self.smart_logger.verbose_log('info', f"🚀 启动缺失数据补全流程...")
                # 注：在第四步中调用时使用静默模式，避免与第三步重复输出警告
                success, validation_result = processor.process_missing_data_for_file(filepath, stock_code)

                if success:
                    self.smart_logger.verbose_log('info', f"✅ 缺失数据处理成功")
                    # 更新分析结果
                    analysis_result['missing_data_processed'] = True
                    analysis_result['processing_success'] = True
                else:
                    self.smart_logger.verbose_log('error', f"❌ 缺失数据处理失败，停止后续处理")
                    analysis_result['missing_data_processed'] = True
                    analysis_result['processing_success'] = False
                    # 关键：前置验证失败时，直接返回，不继续执行后续的增量下载
                    return analysis_result

            except ImportError as e:
                self.smart_logger.verbose_log('error', f"❌ 无法导入缺失数据处理器: {e}")
                analysis_result['missing_data_processed'] = False
                analysis_result['processing_error'] = str(e)

        except Exception as e:
            self.smart_logger.verbose_log('error', f"❌ 处理数据不完整问题失败: {e}")
            analysis_result['missing_data_processed'] = False
            analysis_result['processing_error'] = str(e)

    def _extract_stock_code_from_filepath(self, filepath: str) -> str:
        """
        从文件路径提取股票代码

        Args:
            filepath: 文件路径

        Returns:
            股票代码或空字符串
        """
        try:
            filename = os.path.basename(filepath)

            # 匹配模式：1min_0_000617_... 或 day_0_000617_...
            import re
            pattern = r'(?:1min|day|5min|15min|30min|60min)_0_(\d{6})_'
            match = re.search(pattern, filename)

            if match:
                return match.group(1)
            else:
                self.smart_logger.verbose_log('warning', f"⚠️ 无法从文件名提取股票代码: {filename}")
                return ""

        except Exception as e:
            self.smart_logger.verbose_log('error', f"❌ 提取股票代码失败: {e}")
            return ""
    
    def compare_time_ranges(self, existing_range: Dict[str, str], 
                          task_start: str, task_end: str) -> Dict[str, any]:
        """
        比较现有文件时间范围与任务时间范围
        
        Args:
            existing_range: 现有文件时间范围
            task_start: 任务开始日期（YYYYMMDD）
            task_end: 任务结束日期（YYYYMMDD）
            
        Returns:
            比较结果字典
        """
        try:
            self.smart_logger.verbose_log('info', f"🔍 时间范围比较分析")

            # 兼容不同的键名格式
            existing_start_key = 'start' if 'start' in existing_range else 'first_date'
            existing_end_key = 'end' if 'end' in existing_range else 'last_date'

            existing_start_str = existing_range[existing_start_key]
            existing_end_str = existing_range[existing_end_key]

            self.smart_logger.verbose_log('info', f"  现有文件: {existing_start_str} ~ {existing_end_str}")
            self.smart_logger.verbose_log('info', f"  任务要求: {task_start} ~ {task_end}")

            # 转换为datetime对象进行比较
            existing_start = datetime.strptime(existing_start_str, '%Y%m%d')
            existing_end = datetime.strptime(existing_end_str, '%Y%m%d')
            task_start_dt = datetime.strptime(task_start, '%Y%m%d')
            task_end_dt = datetime.strptime(task_end, '%Y%m%d')
            
            # 分析关系
            need_prepend = task_start_dt < existing_start  # 需要前置补充
            need_append = task_end_dt > existing_end       # 需要后置补充
            has_overlap = not (task_end_dt < existing_start or task_start_dt > existing_end)  # 有重叠
            
            # 计算需要补充的范围
            prepend_range = None
            append_range = None
            
            if need_prepend:
                prepend_end = min(task_end_dt, existing_start - timedelta(days=1))
                if task_start_dt <= prepend_end:
                    prepend_range = {
                        'start': task_start,
                        'end': prepend_end.strftime('%Y%m%d')
                    }
            
            if need_append:
                append_start = max(task_start_dt, existing_end + timedelta(days=1))
                if append_start <= task_end_dt:
                    append_range = {
                        'start': append_start.strftime('%Y%m%d'),
                        'end': task_end
                    }
            
            result = {
                'has_overlap': has_overlap,
                'need_prepend': need_prepend,
                'need_append': need_append,
                'prepend_range': prepend_range,
                'append_range': append_range,
                'can_use_existing': has_overlap,
                'existing_covers_all': (existing_start <= task_start_dt and existing_end >= task_end_dt)
            }
            
            # 输出分析结果
            self.smart_logger.verbose_log('info', f"📊 时间范围分析结果:")
            self.smart_logger.verbose_log('info', f"  有重叠: {'是' if result['has_overlap'] else '否'}")
            self.smart_logger.verbose_log('info', f"  需要前置补充: {'是' if result['need_prepend'] else '否'}")
            self.smart_logger.verbose_log('info', f"  需要后置补充: {'是' if result['need_append'] else '否'}")
            self.smart_logger.verbose_log('info', f"  现有文件完全覆盖: {'是' if result['existing_covers_all'] else '否'}")
            
            if prepend_range:
                self.smart_logger.verbose_log('info', f"  前置补充范围: {prepend_range['start']} ~ {prepend_range['end']}")
            if append_range:
                self.smart_logger.verbose_log('info', f"  后置补充范围: {append_range['start']} ~ {append_range['end']}")
            
            return result
            
        except Exception as e:
            self.smart_logger.log_error(f"时间范围比较失败: {e}")
            return {'can_use_existing': False, 'error': str(e)}
    
    def validate_last_record_consistency(self, filepath: str, stock_code: str, 
                                       downloader, frequency: str) -> Tuple[bool, str, Dict]:
        """
        验证最后一条记录的一致性
        
        Args:
            filepath: 文件路径
            stock_code: 股票代码
            downloader: 数据下载器
            frequency: 数据频率
            
        Returns:
            (是否一致, 原因说明, 比对详情)
        """
        try:
            self.smart_logger.verbose_log('info', f"🔍 验证最后一条记录的数据一致性")
            
            # 获取文件最后一条记录
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < 2:
                return False, "文件数据不足", {}
            
            last_line = lines[-1].strip()
            if not last_line:
                return False, "最后一行为空", {}
            
            # 解析最后一条记录
            parts = last_line.split('|')
            if len(parts) < 5:
                return False, "数据格式异常", {}
            
            file_record = {
                '股票编码': parts[0].strip(),
                '时间': parts[1].strip(),
                '当日收盘价C': parts[3].strip(),
                '前复权收盘价C': parts[4].strip()
            }
            
            self.smart_logger.verbose_log('info', f"📋 文件最后记录: 时间={file_record['时间']}, 前复权价={file_record['前复权收盘价C']}")
            
            # 从API获取对应日期的数据进行比对
            last_time = str(file_record['时间']).replace('.0', '')
            if len(last_time) >= 8:
                query_date = last_time[:8]
            else:
                return False, f"时间格式异常: {file_record['时间']}", {}
            
            self.smart_logger.verbose_log('info', f"🔄 从API获取 {query_date} 的数据进行比对验证")
            
            # 获取API数据
            api_df = downloader.download_stock_data(stock_code, query_date, query_date, frequency, suppress_warnings=True)
            
            if api_df is None or api_df.empty:
                return False, f"无法获取API数据进行比对", {}
            
            # 转换API数据格式
            api_formatted = downloader.convert_to_target_format(api_df, stock_code, "minute")
            
            if api_formatted.empty:
                return False, "API数据格式转换失败", {}
            
            # 查找匹配的记录
            api_record = None
            for _, row in api_formatted.iterrows():
                api_time = str(row['时间']).replace('.0', '')
                if api_time == last_time:
                    api_record = {
                        '时间': api_time,
                        '前复权收盘价C': str(row['前复权收盘价C'])
                    }
                    break
            
            if not api_record:
                return False, f"API数据中未找到匹配的时间记录: {last_time}", {}
            
            self.smart_logger.verbose_log('info', f"📋 API对应记录: 时间={api_record['时间']}, 前复权价={api_record['前复权收盘价C']}")
            
            # 比对结果（包含API记录供后续使用，避免重复下载）
            comparison = {
                'file_time': file_record['时间'],
                'api_time': api_record['时间'],
                'file_price': file_record['前复权收盘价C'],
                'api_price': api_record['前复权收盘价C'],
                'time_match': str(file_record['时间']).replace('.0', '') == api_record['时间'],
                'price_match': abs(float(file_record['前复权收盘价C']) - float(api_record['前复权收盘价C'])) < 0.001,
                'api_record': api_record  # 添加API记录供后续使用
            }
            
            # 显性化输出比对结果
            self.smart_logger.verbose_log('info', f"📊 数据一致性比对结果:")
            self.smart_logger.verbose_log('info', f"  时间比对: 文件={comparison['file_time']} vs API={comparison['api_time']} -> {'✅ 一致' if comparison['time_match'] else '❌ 不一致'}")
            self.smart_logger.verbose_log('info', f"  价格比对: 文件={comparison['file_price']} vs API={comparison['api_price']} -> {'✅ 一致' if comparison['price_match'] else '❌ 不一致'}")
            
            is_consistent = comparison['time_match'] and comparison['price_match']
            reason = "数据一致，可以使用增量下载" if is_consistent else "数据不一致，需要全量重新下载"
            
            self.smart_logger.verbose_log('info', f"🎯 最终判断: {'✅ ' + reason if is_consistent else '❌ ' + reason}")
            
            return is_consistent, reason, comparison
            
        except Exception as e:
            error_msg = f"数据一致性验证失败: {e}"
            self.smart_logger.log_error(error_msg)
            return False, error_msg, {}
    
    def calculate_optimal_download_ranges(self, existing_range: Dict[str, str], 
                                        task_start: str, task_end: str,
                                        comparison_result: Dict[str, any]) -> List[Dict[str, str]]:
        """
        计算最优的下载范围
        
        Args:
            existing_range: 现有文件时间范围
            task_start: 任务开始日期
            task_end: 任务结束日期
            comparison_result: 时间范围比较结果
            
        Returns:
            下载范围列表
        """
        download_ranges = []
        
        try:
            if comparison_result.get('existing_covers_all', False):
                self.smart_logger.verbose_log('info', f"✅ 现有文件完全覆盖任务范围，无需下载")
                return download_ranges
            
            if comparison_result.get('prepend_range'):
                download_ranges.append({
                    'type': 'prepend',
                    'start': comparison_result['prepend_range']['start'],
                    'end': comparison_result['prepend_range']['end'],
                    'description': '前置补充数据'
                })
            
            if comparison_result.get('append_range'):
                download_ranges.append({
                    'type': 'append',
                    'start': comparison_result['append_range']['start'],
                    'end': comparison_result['append_range']['end'],
                    'description': '后置补充数据'
                })
            
            self.smart_logger.verbose_log('info', f"📋 计算出的最优下载范围:")
            for i, range_info in enumerate(download_ranges):
                self.smart_logger.verbose_log('info', f"  {i+1}. {range_info['description']}: {range_info['start']} ~ {range_info['end']}")
            
            return download_ranges
            
        except Exception as e:
            self.smart_logger.log_error(f"计算下载范围失败: {e}")
            return []

    def check_intraday_completeness(self, filepath: str) -> Dict[str, any]:
        """
        检查文件中每个交易日的数据完整性

        Args:
            filepath: 文件路径

        Returns:
            完整性检查结果
        """
        try:
            if not os.path.exists(filepath):
                return {'error': '文件不存在'}

            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()

            if len(lines) < 2:
                return {'error': '文件数据不足'}

            # 按日期分组统计数据
            daily_data = {}

            for line in lines[1:]:  # 跳过表头
                if not line.strip():
                    continue

                parts = line.strip().split('|')
                if len(parts) < 2:
                    continue

                time_str = str(parts[1]).replace('.0', '')
                if len(time_str) >= 8:
                    date_part = time_str[:8]
                    if date_part not in daily_data:
                        daily_data[date_part] = []
                    daily_data[date_part].append(time_str)

            # 检查每日数据完整性
            incomplete_days = []

            for date, times in daily_data.items():
                count = len(times)
                if count < 240:  # 正常交易日应该有240条1分钟数据
                    # 分析缺失的时间段
                    missing_info = self._analyze_missing_times(times, date)
                    incomplete_days.append({
                        'date': date,
                        'count': count,
                        'expected': 240,
                        'missing_info': missing_info
                    })

            result = {
                'total_days': len(daily_data),
                'incomplete_days': incomplete_days,
                'has_incomplete_days': len(incomplete_days) > 0,
                'completeness_ratio': (len(daily_data) - len(incomplete_days)) / len(daily_data) if daily_data else 0
            }

            if result['has_incomplete_days']:
                self.smart_logger.verbose_log('warning', f"⚠️ 发现{len(incomplete_days)}个不完整的交易日")
                for day_info in incomplete_days[:3]:  # 只显示前3个
                    self.smart_logger.verbose_log('warning', f"  {day_info['date']}: {day_info['count']}/240条 ({day_info['missing_info']})")

            return result

        except Exception as e:
            self.smart_logger.log_error(f"检查日内完整性失败: {e}")
            return {'error': str(e)}

    def _analyze_missing_times(self, existing_times: List[str], date: str) -> str:
        """
        分析缺失的时间信息

        Args:
            existing_times: 现有时间列表
            date: 日期

        Returns:
            缺失时间描述
        """
        try:
            if not existing_times:
                return "无数据"

            existing_times.sort()
            first_time = existing_times[0]
            last_time = existing_times[-1]

            # 检查开始时间
            expected_start = f"{date}0930"
            start_missing = ""
            if first_time > expected_start:
                start_missing = f"缺失开始0930-{first_time[8:12]}"

            # 检查结束时间
            expected_end = f"{date}1500"
            end_missing = ""
            if last_time < expected_end:
                end_missing = f"缺失结束{last_time[8:12]}-1500"

            # 组合描述
            missing_parts = []
            if start_missing:
                missing_parts.append(start_missing)
            if end_missing:
                missing_parts.append(end_missing)

            if missing_parts:
                return "; ".join(missing_parts)
            else:
                return f"中间缺失{240-len(existing_times)}条"

        except Exception as e:
            return f"分析失败: {e}"
