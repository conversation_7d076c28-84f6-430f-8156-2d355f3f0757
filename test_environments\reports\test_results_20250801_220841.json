{"success": false, "test_level": "standard", "start_time": "2025-08-01T22:08:41.338564", "end_time": "2025-08-01T22:08:41.728993", "suites": [{"suite_name": "test_price_consistency", "success": false, "return_code": 1, "stdout": "", "stderr": "\\u274c 测试环境初始化失败: 'gbk' codec can't encode character '\\u2705' in position 0: illegal multibyte sequence\nTraceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\test_environments\\test_suites\\test_price_consistency.py\", line 343, in <module>\n    exit_code = main()\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\test_environments\\test_suites\\test_price_consistency.py\", line 337, in main\n    test_suite = PriceConsistencyTestSuite()\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\test_environments\\test_suites\\test_price_consistency.py\", line 36, in __init__\n    self.setup_test_environment()\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\test_environments\\test_suites\\test_price_consistency.py\", line 46, in setup_test_environment\n    print(\"\\U0001f9ea 价格一致性检查测试套件\")\nUnicodeEncodeError: 'gbk' codec can't encode character '\\U0001f9ea' in position 0: illegal multibyte sequence\n", "duration": 0.390429, "timestamp": "2025-08-01T22:08:41.728993"}], "summary": {"total": 1, "passed": 0, "failed": 1, "pass_rate": 0.0, "duration": 0.390429}}