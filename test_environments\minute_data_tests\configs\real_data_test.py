#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
实际数据测试脚本

测试改进后的1分钟数据处理工作流程在真实环境中的表现

作者: AI Assistant
创建时间: 2025-07-30
"""

import sys
import os
import time
import glob
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader


def prepare_test_environment():
    """准备测试环境"""
    print("🔧 准备测试环境")
    print("-" * 60)
    
    try:
        # 检查测试环境目录
        test_dirs = [
            "test_environments/minute_data_tests/input_data",
            "test_environments/minute_data_tests/output_data",
            "test_environments/minute_data_tests/backup_data",
            "test_environments/minute_data_tests/results"
        ]
        
        for test_dir in test_dirs:
            if not os.path.exists(test_dir):
                os.makedirs(test_dir, exist_ok=True)
                print(f"   📁 创建目录: {test_dir}")
            else:
                print(f"   ✅ 目录存在: {test_dir}")
        
        # 检查现有测试文件
        input_files = glob.glob("test_environments/minute_data_tests/input_data/*.txt")
        print(f"   📊 现有测试文件: {len(input_files)} 个")
        
        for file_path in input_files[:3]:  # 显示前3个文件
            file_name = os.path.basename(file_path)
            file_size = os.path.getsize(file_path)
            print(f"      - {file_name} ({file_size:,} bytes)")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试环境准备失败: {e}")
        return False


def test_new_price_comparison_with_real_data():
    """使用真实数据测试新的价格比较接口"""
    print("\n🔍 测试1：新价格比较接口（真实数据）")
    print("-" * 60)
    
    try:
        from test_environments.shared.utilities.test_file_api_comparator import TestFileApiComparator
        
        # 查找真实测试文件
        test_files = glob.glob("test_environments/minute_data_tests/input_data/*.txt")
        
        if not test_files:
            print("   ⚠️ 未找到测试文件，跳过真实数据测试")
            return True
        
        # 使用第一个找到的文件
        test_file = test_files[0]
        stock_code = "000617"  # 从文件名中提取股票代码
        
        print(f"   📁 测试文件: {os.path.basename(test_file)}")
        print(f"   📊 文件大小: {os.path.getsize(test_file):,} bytes")
        
        # 创建比较器
        comparator = TestFileApiComparator()
        
        print(f"   🔍 执行价格比较...")
        start_time = time.time()
        
        result = comparator.compare_last_record_close_price(
            test_file_path=test_file,
            stock_code=stock_code,
            tolerance=0.001
        )
        
        end_time = time.time()
        execution_time = end_time - start_time
        
        print(f"   ⏱️ 执行时间: {execution_time:.2f}秒")
        
        if result['success']:
            file_info = result['file_info']
            api_info = result['api_info']
            comparison = result['comparison']
            
            print(f"   ✅ 比较执行成功:")
            print(f"      📅 文件最后时间: {file_info['last_time']}")
            print(f"      💰 文件收盘价: {file_info['last_close_price']:.3f}")
            
            if api_info['found']:
                print(f"      💰 API收盘价: {api_info['close_price']:.3f}")
                print(f"      📊 价格差异: {comparison['price_diff']:.6f}")
                print(f"      🎯 比较结果: {'✅ 一致' if result['is_equal'] else '❌ 不一致'}")
                
                if not result['is_equal']:
                    print(f"      💡 可能原因: 存在分红配股事件或数据源差异")
            else:
                print(f"      ⚠️ API未获取到对应时间点的数据")
                print(f"      💡 可能原因: 时间点超出pytdx数据范围或网络问题")
            
            return True
        else:
            print(f"   ❌ 比较执行失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"   ❌ 真实数据价格比较测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_complete_workflow_with_real_data():
    """使用真实数据测试完整的工作流程"""
    print("\n🚀 测试2：完整工作流程（真实数据）")
    print("-" * 60)
    
    try:
        # 创建下载器
        downloader = StructuredInternetMinuteDownloader()
        
        # 使用真实的测试参数
        test_stock = "000617"
        
        # 使用较短的时间范围进行测试，避免下载过多数据
        end_date = datetime.now().strftime("%Y%m%d")
        start_date = (datetime.now() - timedelta(days=3)).strftime("%Y%m%d")
        
        test_frequency = "1"
        test_original_frequency = "1min"
        
        print(f"   📋 测试参数:")
        print(f"      股票代码: {test_stock}")
        print(f"      时间范围: {start_date} ~ {end_date}")
        print(f"      数据频率: {test_frequency} ({test_original_frequency})")
        
        # 备份现有文件（如果存在）
        output_pattern = f"1min_0_{test_stock}_*.txt"
        existing_files = glob.glob(output_pattern)
        
        backup_files = []
        for existing_file in existing_files:
            backup_file = f"{existing_file}.backup_{int(time.time())}"
            try:
                import shutil
                shutil.copy2(existing_file, backup_file)
                backup_files.append((existing_file, backup_file))
                print(f"   💾 备份文件: {os.path.basename(existing_file)} -> {os.path.basename(backup_file)}")
            except Exception as e:
                print(f"   ⚠️ 备份失败: {e}")
        
        print(f"\n   🎬 开始执行完整工作流程...")
        
        # 执行完整的四步流程
        start_time = time.time()
        
        success = downloader._execute_four_step_process(
            stock_code=test_stock,
            start_date=start_date,
            end_date=end_date,
            frequency=test_frequency,
            original_frequency=test_original_frequency
        )
        
        end_time = time.time()
        total_time = end_time - start_time
        
        print(f"\n   📊 工作流程执行结果:")
        print(f"      ⏱️ 总执行时间: {total_time:.2f}秒")
        print(f"      🎯 执行结果: {'✅ 成功' if success else '❌ 失败'}")
        
        # 检查生成的文件
        new_files = glob.glob(output_pattern)
        generated_files = [f for f in new_files if not any(f == existing for existing, _ in backup_files)]
        
        if generated_files:
            print(f"      📁 生成文件: {len(generated_files)} 个")
            for generated_file in generated_files:
                file_size = os.path.getsize(generated_file)
                print(f"         - {os.path.basename(generated_file)} ({file_size:,} bytes)")
                
                # 简单验证文件内容
                try:
                    with open(generated_file, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                        if len(lines) > 1:
                            header = lines[0].strip()
                            first_data = lines[1].strip() if len(lines) > 1 else ""
                            print(f"         📋 数据行数: {len(lines) - 1}")
                            print(f"         📊 表头格式: {'✅ 正确' if '股票编码|时间|' in header else '❌ 错误'}")
                            
                            # 检查股票代码格式
                            if first_data:
                                fields = first_data.split('|')
                                if len(fields) > 0:
                                    stock_code_in_file = fields[0]
                                    print(f"         🏷️ 股票代码: {stock_code_in_file} ({'✅ 格式正确' if len(stock_code_in_file) == 6 else '❌ 格式错误'})")
                        else:
                            print(f"         ⚠️ 文件内容为空或只有表头")
                except Exception as e:
                    print(f"         ❌ 文件验证失败: {e}")
        else:
            print(f"      ⚠️ 未生成新文件")
        
        return success
        
    except Exception as e:
        print(f"   ❌ 完整工作流程测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_240_standard_in_real_scenario():
    """在真实场景中测试240行标准"""
    print("\n📊 测试3：240行标准（真实场景）")
    print("-" * 60)
    
    try:
        from utils.missing_data_processor import MissingDataProcessor
        
        # 查找真实测试文件
        test_files = glob.glob("test_environments/minute_data_tests/input_data/*.txt")
        
        if not test_files:
            print("   ⚠️ 未找到测试文件，跳过240行标准测试")
            return True
        
        processor = MissingDataProcessor()
        
        for test_file in test_files[:2]:  # 测试前2个文件
            print(f"   📁 测试文件: {os.path.basename(test_file)}")
            
            try:
                # 检测缺失数据
                missing_info = processor.detect_missing_minute_data(test_file, "000617")
                
                total_records = missing_info.get('total_records', 0)
                total_days = missing_info.get('total_days', 0)
                has_missing = missing_info.get('has_missing', False)
                
                print(f"      📊 总记录数: {total_records:,}")
                print(f"      📅 覆盖天数: {total_days}")
                
                if total_days > 0:
                    avg_records_per_day = total_records / total_days
                    print(f"      📈 平均每日记录数: {avg_records_per_day:.1f}")
                    
                    if abs(avg_records_per_day - 240) < 10:  # 允许一定误差
                        print(f"      ✅ 240行标准: 符合预期")
                    else:
                        print(f"      ⚠️ 240行标准: 偏差较大")
                
                if has_missing:
                    missing_days = missing_info.get('missing_days', [])
                    incomplete_days = missing_info.get('incomplete_days', [])
                    print(f"      ⚠️ 数据不完整: 完全缺失{len(missing_days)}天, 部分缺失{len(incomplete_days)}天")
                else:
                    print(f"      ✅ 数据完整性: 良好")
                
            except Exception as e:
                print(f"      ❌ 文件分析失败: {e}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 240行标准测试失败: {e}")
        return False


def cleanup_test_files():
    """清理测试文件"""
    print("\n🧹 清理测试文件")
    print("-" * 60)
    
    try:
        # 查找备份文件
        backup_files = glob.glob("*.backup_*")
        
        if backup_files:
            print(f"   📁 发现备份文件: {len(backup_files)} 个")
            
            user_input = input("   ❓ 是否删除备份文件? (y/N): ").strip().lower()
            
            if user_input == 'y':
                for backup_file in backup_files:
                    try:
                        os.remove(backup_file)
                        print(f"      🗑️ 删除: {backup_file}")
                    except Exception as e:
                        print(f"      ❌ 删除失败: {backup_file} - {e}")
            else:
                print("   💾 保留备份文件")
        else:
            print("   ✅ 无需清理")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 清理失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 实际数据测试")
    print("=" * 100)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 100)
    
    test_results = []
    
    # 准备测试环境
    env_ready = prepare_test_environment()
    if not env_ready:
        print("❌ 测试环境准备失败，终止测试")
        return False
    
    # 测试1: 新价格比较接口（真实数据）
    result1 = test_new_price_comparison_with_real_data()
    test_results.append(("新价格比较接口（真实数据）", result1))
    
    # 测试2: 完整工作流程（真实数据）
    result2 = test_complete_workflow_with_real_data()
    test_results.append(("完整工作流程（真实数据）", result2))
    
    # 测试3: 240行标准（真实场景）
    result3 = test_240_standard_in_real_scenario()
    test_results.append(("240行标准（真实场景）", result3))
    
    # 汇总测试结果
    print("\n📊 实际数据测试结果汇总")
    print("=" * 100)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    total_count = len(test_results)
    pass_rate = passed_count / total_count
    
    print(f"\n📈 总体通过率: {passed_count}/{total_count} ({pass_rate:.1%})")
    
    if pass_rate >= 0.8:
        print("🎉 实际数据测试整体通过！")
        print("💡 改进后的工作流程在真实环境中表现良好")
        print("🚀 可以正式投入生产使用")
    else:
        print("⚠️ 部分实际数据测试未通过")
        print("🔧 建议进一步调试和优化")
    
    # 清理测试文件
    cleanup_test_files()
    
    print("=" * 100)
    
    return pass_rate >= 0.8


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
