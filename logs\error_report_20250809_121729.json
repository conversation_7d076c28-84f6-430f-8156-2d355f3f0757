{"timestamp": "2025-08-09T12:17:29.043110", "error_statistics": {"error_counts": {"DATA_ACCESS": 1}, "total_errors": 1, "recent_errors": [{"error_id": "DATA_ACCESS_1754713049036", "timestamp": "2025-08-09T12:17:29.036108", "category": "DATA_ACCESS", "error_type": "TypeError", "error_message": "__init__() got an unexpected keyword argument 'output_dir'", "stock_code": null, "operation": "pytdx数据下载", "context": {"stock_code": "000617", "data_source": "pytdx"}, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\stock_data_downloader.py\", line 432, in download_stock_data_pytdx\n    downloader = PytdxDownloader(output_dir=self.output_dir)\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\tools\\find_early_pytdx_call_precise.py\", line 98, in tracked_init\n    return original_init(self, *args, **kwargs)\nTypeError: __init__() got an unexpected keyword argument 'output_dir'\n"}], "error_history_size": 1}, "performance_statistics": {}}