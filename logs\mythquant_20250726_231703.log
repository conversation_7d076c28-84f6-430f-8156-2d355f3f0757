2025-07-26 23:17:03,805 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_231703.log
2025-07-26 23:17:03,805 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 23:17:03,828 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 23:17:03,830 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 23:17:03,831 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 23:17:03,831 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-26 23:17:03,831 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-26 23:17:04,257 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.90.165 (115.238.90.165:7709) - 0.269s
2025-07-26 23:17:04,258 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.268s
2025-07-26 23:17:04,258 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.268s
2025-07-26 23:17:04,259 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************ (************:7709) - 0.268s
2025-07-26 23:17:04,259 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.269s
2025-07-26 23:17:04,259 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.268s
2025-07-26 23:17:04,265 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-26 23:17:04,266 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-26 23:17:04,266 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-26 23:17:04,266 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 23:17:04,266 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 23:17:04,267 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 23:17:04,267 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 23:17:04,792 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 23:17:04,792 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 23:17:04,799 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 23:17:04,799 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 23:17:04,799 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 23:17:04,799 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 23:17:04,799 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 23:17:04,799 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 23:17:04,800 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 23:17:04,803 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 23:17:04,804 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 23:17:04,804 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 23:17:04,804 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 23:17:04,812 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 23:17:05,284 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.47秒
2025-07-26 23:17:05,285 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.47秒
2025-07-26 23:17:05,285 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 23:17:05,285 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 23:17:05,285 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 23:17:05,285 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 23:17:05,285 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 23:17:05,286 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 23:17:05,287 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 23:17:05,287 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 23:17:05,287 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 23:17:05,288 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 23:17:05,288 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 23:17:05,428 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 23:17:05,428 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 23:17:05,436 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 23:17:05,436 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 23:17:05,436 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 23:17:05,436 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 23:17:05,436 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 23:17:05,436 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 23:17:05,436 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 23:17:05,440 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 23:17:05,440 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 23:17:05,440 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 23:17:05,441 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 23:17:05,448 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 23:17:06,227 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.78秒
2025-07-26 23:17:06,227 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.78秒
2025-07-26 23:17:17,890 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 23:17:44,484 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 23:17:44,484 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 23:17:44,484 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 23:17:44,494 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 23:17:45,100 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 23:17:45,100 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 23:17:45,100 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 23:17:45,100 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 23:17:45,102 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 23:17:45,102 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 23:17:45,103 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 23:17:45,103 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 23:17:45,103 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 23:17:45,163 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 23:17:45,716 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 23:17:45,736 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 23:17:45,748 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 23:17:45,762 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 23:17:45,777 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 23:17:45,792 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 23:17:45,807 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 23:17:45,821 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 23:17:45,838 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 23:17:45,852 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 23:17:45,871 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 23:17:45,889 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 23:17:45,907 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 23:17:45,919 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 23:17:45,931 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 23:17:45,948 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 23:17:45,962 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 23:17:45,976 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 23:17:45,994 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 23:17:46,012 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 23:17:46,026 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 23:17:46,026 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 23:17:50,738 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 23:17:50,739 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 23:17:50,739 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 23:17:50,739 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 23:17:50,739 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 23:17:50,742 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 23:17:50,836 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-26 23:17:50,836 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 23:17:50,836 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 23:17:50,837 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 23:17:50,837 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 23:17:50,837 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 23:17:50,837 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 23:17:50,848 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 23:17:51,012 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 23:17:51,528 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 23:17:51,727 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 23:17:51,727 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 23:17:52,001 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 23:17:52,002 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 23:17:52,003 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 23:17:52,003 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-26 23:17:52,003 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-26 23:17:52,003 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-26 23:17:52,003 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 23:17:52,017 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 46.73 秒
2025-07-26 23:17:52,017 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 23:17:52,017 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 23:17:52,017 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 23:17:52,018 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 23:17:52,019 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 23:17:52,414 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 23:17:52,426 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 23:17:52,426 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 23:17:52,426 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 23:17:52,426 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-26 23:17:52,426 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 23:17:52,426 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 23:17:52,426 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 23:17:52,426 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 23:17:52,433 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-26 23:17:52,433 - core.logging_service - INFO - verbose_log:67 - ✅ 加载智能文件选择器配置: 策略=smart_comprehensive
2025-07-26 23:17:52,434 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-26 23:17:52,434 - core.logging_service - INFO - verbose_log:67 - 找到唯一文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-26 23:17:52,434 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-26 23:17:52,435 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-26 23:17:52,435 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-26 23:17:52,435 - core.logging_service - INFO - verbose_log:67 - 📊 分析现有文件时间跨度: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-26 23:17:52,437 - core.logging_service - INFO - verbose_log:67 - 📅 现有文件时间跨度: 20250303 ~ 20250704 (20381条记录)
2025-07-26 23:17:52,437 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-26 23:17:52,438 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250704
2025-07-26 23:17:52,438 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-26 23:17:52,438 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-26 23:17:52,438 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-26 23:17:52,438 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-26 23:17:52,439 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-26 23:17:52,439 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-26 23:17:52,439 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-26 23:17:52,439 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250705 ~ 20250727
2025-07-26 23:17:52,439 - core.logging_service - INFO - verbose_log:67 - 🔍 验证最后一条记录的数据一致性
2025-07-26 23:17:52,441 - core.logging_service - INFO - verbose_log:67 - 📋 文件最后记录: 时间=202507041447, 前复权价=7.55
2025-07-26 23:17:52,441 - core.logging_service - INFO - verbose_log:67 - 🔄 从API获取 20250704 的数据进行比对验证
2025-07-26 23:17:52,441 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 23:17:52,441 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 23:17:52,441 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-26 23:17:52,441 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:52,624 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:52,624 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4608条 (目标日期: 20250704, 交易日: 16天, 频率: 1min)
2025-07-26 23:17:52,624 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4608条 -> 实际请求800条 (配置限制:800)
2025-07-26 23:17:52,625 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 23:17:52,625 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 23:17:52,678 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3808条
2025-07-26 23:17:52,678 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:52,856 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:52,905 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 23:17:52,905 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:53,087 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:53,141 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 23:17:53,141 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:53,325 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:53,378 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 23:17:53,378 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:53,549 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:53,599 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 23:17:53,599 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:53,782 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:53,833 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +608条，总计4608条
2025-07-26 23:17:53,833 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4608条数据
2025-07-26 23:17:53,847 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-26 23:17:53,850 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-26 23:17:53,850 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 23:17:53,853 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-26 23:17:53,853 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 23:17:53,854 - Main - INFO - info:307 - ℹ️   日期: 202507040931
2025-07-26 23:17:53,854 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-26 23:17:53,854 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-26 23:17:53,854 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 23:17:53,854 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-26 23:17:53,854 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 23:17:53,855 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 23:17:53,855 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 23:17:53,858 - core.logging_service - INFO - verbose_log:67 - 📋 API对应记录: 时间=202507041447, 前复权价=7.55
2025-07-26 23:17:53,859 - core.logging_service - INFO - verbose_log:67 - 📊 数据一致性比对结果:
2025-07-26 23:17:53,859 - core.logging_service - INFO - verbose_log:67 -   时间比对: 文件=202507041447 vs API=202507041447 -> ✅ 一致
2025-07-26 23:17:53,859 - core.logging_service - INFO - verbose_log:67 -   价格比对: 文件=7.55 vs API=7.55 -> ✅ 一致
2025-07-26 23:17:53,859 - core.logging_service - INFO - verbose_log:67 - 🎯 最终判断: ✅ 数据一致，可以使用增量下载
2025-07-26 23:17:53,859 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-26 23:17:53,861 - core.logging_service - INFO - verbose_log:67 - 获取最后一条记录: 时间=202507041447, 前复权价=7.55
2025-07-26 23:17:53,862 - core.logging_service - INFO - verbose_log:67 - 时间格式对比: 文件=202507041447, API=202507041447, 级别=1min
2025-07-26 23:17:53,862 - core.logging_service - INFO - verbose_log:67 - ✅ 时间格式一致性验证通过
2025-07-26 23:17:53,862 - core.logging_service - INFO - verbose_log:67 - ✅ 价格一致性验证通过
2025-07-26 23:17:53,862 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-26 23:17:53,862 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250303 - 20250704
2025-07-26 23:17:53,862 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250704
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-26 23:17:53,863 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250705 ~ 20250727
2025-07-26 23:17:53,864 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250302
2025-07-26 23:17:53,864 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-07-26 23:17:53,883 - Main - INFO - info:307 - ℹ️ 读取现有数据: 20381 条记录
2025-07-26 23:17:53,883 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250302
2025-07-26 23:17:53,883 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 23:17:53,884 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 23:17:53,884 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250302
2025-07-26 23:17:53,884 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:54,058 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:54,058 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 42624条 (目标日期: 20250101, 交易日: 148天, 频率: 1min)
2025-07-26 23:17:54,059 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计42624条 -> 实际请求800条 (配置限制:800)
2025-07-26 23:17:54,059 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 23:17:54,059 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 23:17:54,108 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需41824条
2025-07-26 23:17:54,109 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:54,289 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:54,341 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 23:17:54,341 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:54,523 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:54,574 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 23:17:54,575 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:54,748 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:54,799 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 23:17:54,799 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:54,969 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:55,022 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 23:17:55,022 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:55,201 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:55,253 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 23:17:55,253 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:55,427 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:55,479 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 23:17:55,479 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:55,655 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:55,710 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 23:17:55,710 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:55,875 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:55,927 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 23:17:55,927 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:56,103 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:56,155 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 23:17:56,156 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:56,336 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:56,390 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 23:17:56,390 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:56,575 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:56,628 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 23:17:56,628 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:56,811 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:56,865 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 23:17:56,865 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:57,046 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:57,099 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 23:17:57,099 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:57,271 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:57,321 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 23:17:57,322 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:57,494 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:57,544 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 23:17:57,544 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:57,722 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:57,773 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 23:17:57,773 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:57,948 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:58,001 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 23:17:58,001 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:58,173 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:58,225 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 23:17:58,225 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:58,408 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:58,463 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 23:17:58,463 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:58,645 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:58,696 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 23:17:58,696 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:58,872 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:58,922 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 23:17:58,922 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:59,105 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:59,157 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 23:17:59,157 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:59,335 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:59,389 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 23:17:59,389 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:59,570 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:59,620 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 23:17:59,620 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:17:59,807 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:17:59,860 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 23:17:59,860 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:00,035 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:00,091 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 23:18:00,091 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:00,265 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:00,318 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 23:18:00,318 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:00,495 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:00,549 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 23:18:00,549 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:00,721 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:00,770 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 23:18:00,771 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:00,950 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:00,996 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 23:18:00,996 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要42624条，实际获取24000条
2025-07-26 23:18:00,997 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约18624条数据（约77个交易日）
2025-07-26 23:18:00,997 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-26 23:18:00,997 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-26 23:18:01,056 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250101 - 20250302
2025-07-26 23:18:01,063 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-26 23:18:01,063 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-26 23:18:01,064 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-07-26 23:18:01,064 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 23:18:01,064 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 23:18:01,064 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-07-26 23:18:01,064 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:01,247 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:01,247 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4320条 (目标日期: 20250705, 交易日: 15天, 频率: 1min)
2025-07-26 23:18:01,247 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4320条 -> 实际请求800条 (配置限制:800)
2025-07-26 23:18:01,247 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 23:18:01,247 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 23:18:01,301 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3520条
2025-07-26 23:18:01,301 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:01,500 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:01,553 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 23:18:01,553 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:01,722 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:01,771 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 23:18:01,771 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:01,941 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:01,992 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 23:18:01,992 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:02,172 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 23:18:02,227 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 23:18:02,227 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 23:18:07,281 - Main - WARNING - warning:311 - ⚠️ 读取配置服务器失败: timed out
2025-07-26 23:18:07,282 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接缓存的良好服务器: **************:7709
2025-07-26 23:18:07,509 - Main - INFO - info:307 - ℹ️ ✅ 连接缓存的良好服务器成功: **************:7709
2025-07-26 23:18:07,567 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +320条，总计4320条
2025-07-26 23:18:07,567 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4320条数据
2025-07-26 23:18:07,579 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-07-26 23:18:07,581 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共3600条记录
2025-07-26 23:18:07,581 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 23:18:07,595 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.640, 前复权=7.640
2025-07-26 23:18:07,596 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 23:18:07,596 - Main - INFO - info:307 - ℹ️   日期: 202507070931
2025-07-26 23:18:07,596 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.64
2025-07-26 23:18:07,596 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.64
2025-07-26 23:18:07,596 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 23:18:07,597 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 3600 >= 5
2025-07-26 23:18:07,597 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 23:18:07,597 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 23:18:07,598 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 23:18:07,598 - Main - INFO - info:307 - ℹ️ 获得新数据: 3600 条记录
2025-07-26 23:18:07,601 - Main - INFO - info:307 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-07-26 23:18:07,610 - Main - INFO - info:307 - ℹ️ 合并后数据: 23981 条记录
2025-07-26 23:18:07,613 - core.logging_service - INFO - verbose_log:67 - 📝 时间范围变化，需要重命名:
2025-07-26 23:18:07,613 - core.logging_service - INFO - verbose_log:67 -   原始: 20250303-20250704
2025-07-26 23:18:07,613 - core.logging_service - INFO - verbose_log:67 -   更新: 202503030937-202507251500
2025-07-26 23:18:07,613 - core.logging_service - INFO - verbose_log:67 -   新文件名: 1min_0_000617_202503030937-202507251500_来源互联网（202507262318）.txt
2025-07-26 23:18:07,677 - Main - INFO - info:307 - ℹ️ 删除旧文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-26 23:18:07,677 - Main - INFO - info:307 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503030937-202507251500_来源互联网（202507262318）.txt
2025-07-26 23:18:07,678 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-26 23:18:07,678 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 23:18:07,678 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 23:18:07,678 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-26 23:18:07,679 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-26 23:18:07,679 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 23:18:07,679 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 23:18:07,680 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 23:18:07,680 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 23:18:07,680 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 23:18:07,680 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 23:18:07,680 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-26 23:18:07,680 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: True, 互联网: False
2025-07-26 23:18:07,682 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_231807.txt
2025-07-26 23:18:07,682 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-26 23:18:07,682 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-26 23:18:07,682 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 66.7%
2025-07-26 23:18:07,684 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 23:18:07,684 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 23:18:07,684 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 23:18:07,684 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 23:18:07,684 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 23:18:07,684 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 23:18:07,684 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
