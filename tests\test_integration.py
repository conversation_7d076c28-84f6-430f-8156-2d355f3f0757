#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
集成测试套件

测试各模块间的集成和完整的数据处理流程
"""

import unittest
import sys
import os
from pathlib import Path
import pandas as pd
import numpy as np

# 添加项目路径
project_root = Path(__file__).parent.parent
sys.path.insert(0, str(project_root))
sys.path.insert(0, str(project_root / "src"))

from mythquant.tests.fixtures import test_fixtures, cleanup_test_environment, create_test_data
from mythquant.tests.utils import assert_dataframe_equal, assert_file_exists, assert_output_format


class TestSystemIntegration(unittest.TestCase):
    """系统集成测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = test_fixtures.setup_temp_directory()
        self.test_stock_code = "000001"
        self.test_data = create_test_data("stock", stock_code=self.test_stock_code, periods=50)
    
    def tearDown(self):
        """测试后清理"""
        cleanup_test_environment()
    
    def test_complete_data_pipeline(self):
        """测试完整的数据处理流水线"""
        try:
            # 导入兼容性模块
            import config_compatibility as config
            import data_access_compatibility as data_access
            import algorithm_compatibility as algo
            import io_compatibility as io_compat
            
            # 1. 配置检查
            output_path = config.get_output_path()
            self.assertIsInstance(output_path, str)
            
            # 2. 数据访问测试（使用模拟数据）
            stock_data = self.test_data
            self.assertIsInstance(stock_data, pd.DataFrame)
            self.assertGreater(len(stock_data), 0)
            
            # 3. 算法处理
            l2_data = algo.calculate_l2_metrics(stock_data)
            self.assertIsInstance(l2_data, pd.DataFrame)
            self.assertIn('main_buy', l2_data.columns)
            self.assertIn('main_sell', l2_data.columns)
            
            # 4. 数据输出
            output_file = io_compat.write_stock_data_file(
                l2_data, self.test_stock_code, "day", "20230101", "20230228"
            )
            
            if output_file:
                self.assertTrue(assert_file_exists(output_file, min_size=100))
                
                # 验证输出格式
                with open(output_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                expected_header = "股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖"
                self.assertTrue(assert_output_format(content, expected_header))
                
                # 清理测试文件
                output_file.unlink()
            
        except Exception as e:
            self.fail(f"Complete data pipeline test failed: {e}")
    
    def test_data_source_integration(self):
        """测试数据源集成"""
        try:
            import data_access_compatibility as data_access
            
            # 测试数据源连通性
            connectivity = data_access.test_data_sources_connectivity()
            self.assertIsInstance(connectivity, dict)
            
            # 测试股票列表获取
            stock_list = data_access.get_stock_list()
            self.assertIsInstance(stock_list, list)
            
        except Exception as e:
            self.skipTest(f"Data source integration test skipped: {e}")
    
    def test_algorithm_integration(self):
        """测试算法集成"""
        try:
            import algorithm_compatibility as algo
            
            # 测试前复权计算
            adj_data = algo.calculate_forward_adjustment(self.test_data)
            if adj_data is not None and not adj_data.empty:
                self.assertIn('adj_close', adj_data.columns)
            
            # 测试L2指标计算
            l2_data = algo.calculate_l2_metrics(self.test_data)
            if l2_data is not None and not l2_data.empty:
                self.assertIn('main_buy', l2_data.columns)
                self.assertIn('main_sell', l2_data.columns)
            
            # 测试主买主卖计算
            buy_sell_data = algo.calculate_buy_sell_metrics(self.test_data)
            if buy_sell_data is not None and not buy_sell_data.empty:
                self.assertIn('main_buy', buy_sell_data.columns)
                self.assertIn('main_sell', buy_sell_data.columns)
            
        except Exception as e:
            self.fail(f"Algorithm integration test failed: {e}")
    
    def test_io_integration(self):
        """测试IO集成"""
        try:
            import io_compatibility as io_compat
            
            # 测试数据格式化
            formatted_content = io_compat.format_stock_data_output(
                self.test_data, self.test_stock_code, "day"
            )
            self.assertIsInstance(formatted_content, str)
            self.assertGreater(len(formatted_content), 0)
            
            # 测试CSV写入
            csv_file = io_compat.write_csv_file(self.test_data, "test_integration.csv")
            if csv_file and csv_file.exists():
                self.assertTrue(assert_file_exists(csv_file))
                csv_file.unlink()
            
        except Exception as e:
            self.fail(f"IO integration test failed: {e}")
    
    def test_error_handling_integration(self):
        """测试错误处理集成"""
        try:
            import algorithm_compatibility as algo
            import io_compatibility as io_compat
            
            # 测试空数据处理
            empty_data = pd.DataFrame()
            
            # 算法应该能处理空数据
            result = algo.calculate_l2_metrics(empty_data)
            self.assertIsInstance(result, pd.DataFrame)
            
            # IO应该能处理空数据
            output_file = io_compat.write_stock_data_file(empty_data, "000001", "day")
            self.assertIsNone(output_file)  # 应该返回None表示失败
            
        except Exception as e:
            self.fail(f"Error handling integration test failed: {e}")


class TestPerformanceIntegration(unittest.TestCase):
    """性能集成测试类"""
    
    def setUp(self):
        """测试前设置"""
        self.temp_dir = test_fixtures.setup_temp_directory()
        # 创建大量测试数据
        self.large_data = create_test_data("stock", periods=1000)
    
    def tearDown(self):
        """测试后清理"""
        cleanup_test_environment()
    
    def test_large_data_processing_performance(self):
        """测试大数据处理性能"""
        import time
        
        try:
            import algorithm_compatibility as algo
            import io_compatibility as io_compat
            
            # 测试算法处理性能
            start_time = time.time()
            l2_data = algo.calculate_l2_metrics(self.large_data)
            algorithm_time = time.time() - start_time
            
            # 1000条记录的算法处理应该在5秒内完成
            self.assertLess(algorithm_time, 5.0, f"Algorithm processing too slow: {algorithm_time:.3f}s")
            
            # 测试IO处理性能
            start_time = time.time()
            output_file = io_compat.write_stock_data_file(l2_data, "000001", "day")
            io_time = time.time() - start_time
            
            # IO处理应该在2秒内完成
            self.assertLess(io_time, 2.0, f"IO processing too slow: {io_time:.3f}s")
            
            # 清理测试文件
            if output_file and output_file.exists():
                output_file.unlink()
            
        except Exception as e:
            self.skipTest(f"Performance test skipped: {e}")
    
    def test_memory_usage(self):
        """测试内存使用"""
        try:
            import psutil
            import algorithm_compatibility as algo
            
            # 获取初始内存使用
            process = psutil.Process()
            initial_memory = process.memory_info().rss / 1024 / 1024  # MB
            
            # 处理大量数据
            for _ in range(10):
                result = algo.calculate_l2_metrics(self.large_data)
                del result
            
            # 获取最终内存使用
            final_memory = process.memory_info().rss / 1024 / 1024  # MB
            memory_increase = final_memory - initial_memory
            
            # 内存增长应该控制在100MB以内
            self.assertLess(memory_increase, 100, f"Memory usage increased by {memory_increase:.1f}MB")
            
        except ImportError:
            self.skipTest("psutil not available for memory testing")
        except Exception as e:
            self.skipTest(f"Memory test skipped: {e}")


class TestCompatibilityIntegration(unittest.TestCase):
    """兼容性集成测试类"""
    
    def test_backward_compatibility(self):
        """测试向后兼容性"""
        try:
            # 测试所有兼容性模块都能正常导入
            import config_compatibility
            import data_access_compatibility
            import algorithm_compatibility
            import io_compatibility
            
            # 测试关键函数存在
            self.assertTrue(hasattr(config_compatibility, 'get_tdx_path'))
            self.assertTrue(hasattr(data_access_compatibility, 'read_stock_day_data'))
            self.assertTrue(hasattr(algorithm_compatibility, 'calculate_l2_metrics'))
            self.assertTrue(hasattr(io_compatibility, 'write_stock_data_file'))
            
        except ImportError as e:
            self.fail(f"Backward compatibility test failed: {e}")
    
    def test_new_architecture_availability(self):
        """测试新架构可用性"""
        try:
            # 测试新架构模块导入
            from mythquant.config import config_manager
            from mythquant.algorithms import L2MetricsCalculator
            from mythquant.io import OutputWriter
            
            # 测试基本功能
            self.assertTrue(hasattr(config_manager, 'get_tdx_path'))
            
        except ImportError as e:
            self.skipTest(f"New architecture not fully available: {e}")


if __name__ == '__main__':
    # 运行集成测试
    unittest.main(verbosity=2)
