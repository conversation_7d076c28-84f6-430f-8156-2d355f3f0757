#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化的DDD配置架构测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_basic_imports():
    """测试基本导入"""
    print("🧪 测试基本导入")
    print("=" * 40)
    
    # 测试1: 用户配置
    try:
        import user_config
        print("✅ user_config导入成功")
    except Exception as e:
        print(f"❌ user_config导入失败: {e}")
        return False
    
    # 测试2: 值对象
    try:
        from src.mythquant.domain.config.value_objects.tdx_connection import TdxConnection
        print("✅ TdxConnection导入成功")
    except Exception as e:
        print(f"❌ TdxConnection导入失败: {e}")
        return False
    
    # 测试3: 实体
    try:
        from src.mythquant.domain.config.entities.trading_config import TradingConfig
        print("✅ TradingConfig导入成功")
    except Exception as e:
        print(f"❌ TradingConfig导入失败: {e}")
        return False
    
    # 测试4: 适配器
    try:
        from src.mythquant.infrastructure.config.adapters.user_config_adapter import UserConfigAdapter
        print("✅ UserConfigAdapter导入成功")
    except Exception as e:
        print(f"❌ UserConfigAdapter导入失败: {e}")
        return False
    
    # 测试5: 仓储
    try:
        from src.mythquant.infrastructure.config.repositories.file_config_repository import FileConfigRepository
        print("✅ FileConfigRepository导入成功")
    except Exception as e:
        print(f"❌ FileConfigRepository导入失败: {e}")
        return False
    
    return True

def test_basic_functionality():
    """测试基本功能"""
    print("\n🔧 测试基本功能")
    print("=" * 40)
    
    try:
        # 创建适配器
        from src.mythquant.infrastructure.config.adapters.user_config_adapter import UserConfigAdapter
        adapter = UserConfigAdapter()
        print("✅ UserConfigAdapter创建成功")
        
        # 获取配置数据
        trading_data = adapter.get_trading_config_data()
        print(f"✅ 交易配置数据获取成功")
        print(f"   TDX路径: {trading_data.get('tdx_path', 'N/A')}")
        
        # 创建仓储
        from src.mythquant.infrastructure.config.repositories.file_config_repository import FileConfigRepository
        repo = FileConfigRepository(adapter)
        print("✅ FileConfigRepository创建成功")
        
        # 获取交易配置
        trading_config = repo.get_trading_config()
        print("✅ 交易配置获取成功")
        print(f"   TDX连接: {trading_config.tdx_connection}")
        
        return True
        
    except Exception as e:
        print(f"❌ 基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 简化DDD配置架构测试")
    print("=" * 50)
    
    # 测试基本导入
    imports_ok = test_basic_imports()
    
    if not imports_ok:
        print("\n❌ 基本导入测试失败，停止后续测试")
        return False
    
    # 测试基本功能
    functionality_ok = test_basic_functionality()
    
    print("\n" + "=" * 50)
    if imports_ok and functionality_ok:
        print("🎉 简化测试通过！DDD架构基础组件工作正常")
        return True
    else:
        print("❌ 测试失败，需要进一步检查")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
