#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一测试环境创建脚本

基于现代软件开发最佳实践，创建体系化、专业化的项目内测试环境

设计原则：
1. 测试环境与项目代码同步管理
2. 按测试类型和功能模块分层组织
3. 支持CI/CD集成和自动化测试
4. 便于版本控制和团队协作

作者: AI Assistant
创建时间: 2025-07-29
"""

import os
import json
import shutil
from datetime import datetime
from pathlib import Path
from typing import Dict, List


class UnifiedTestEnvironmentCreator:
    """统一测试环境创建器"""
    
    def __init__(self):
        """初始化创建器"""
        self.project_root = Path(".")
        
        # 新的统一测试环境结构
        self.test_structure = {
            'test_environments': {
                'description': '统一测试环境根目录',
                'subdirs': {
                    'unit_tests': {
                        'description': '单元测试环境',
                        'subdirs': ['data', 'results', 'reports', 'configs']
                    },
                    'integration_tests': {
                        'description': '集成测试环境', 
                        'subdirs': ['data', 'results', 'reports', 'configs']
                    },
                    'performance_tests': {
                        'description': '性能测试环境',
                        'subdirs': ['data', 'results', 'reports', 'configs', 'benchmarks']
                    },
                    'regression_tests': {
                        'description': '回归测试环境',
                        'subdirs': ['data', 'results', 'reports', 'configs', 'baselines']
                    },
                    'minute_data_tests': {
                        'description': '1分钟数据专项测试环境',
                        'subdirs': ['input_data', 'output_data', 'expected_data', 'backup_data', 'results', 'configs']
                    },
                    'data_quality_tests': {
                        'description': '数据质量测试环境',
                        'subdirs': ['sample_data', 'validation_rules', 'results', 'reports']
                    },
                    'shared': {
                        'description': '共享测试资源',
                        'subdirs': ['fixtures', 'mocks', 'utilities', 'templates', 'docs']
                    }
                }
            }
        }
        
        # 当前需要迁移的目录
        self.legacy_dirs = {
            'tests': 'test_environments/shared/utilities',
            'test_cases': 'test_environments/regression_tests/data',
            'test_csv_output': 'test_environments/shared/fixtures/csv_samples',
            'test_txt_output': 'test_environments/shared/fixtures/txt_samples', 
            'test_results': 'test_environments/shared/results',
            'test_output_debug': 'DELETE'  # 删除，保持调试输出及时清理原则
        }
        
        # TestCase/01的数据迁移目标
        self.testcase_migration = {
            'source': 'H:/MPV1.17/T0002/signals/TestCase/01',
            'target': 'test_environments/minute_data_tests'
        }
    
    def create_unified_structure(self):
        """创建统一测试环境结构"""
        print("🏗️ 创建统一测试环境结构")
        print("=" * 80)
        
        # 创建主测试环境目录
        test_env_root = self.project_root / 'test_environments'
        test_env_root.mkdir(exist_ok=True)
        print(f"📁 创建主测试环境: {test_env_root}")
        
        # 创建各个测试环境
        for env_name, env_config in self.test_structure['test_environments']['subdirs'].items():
            env_path = test_env_root / env_name
            env_path.mkdir(exist_ok=True)
            print(f"📁 创建测试环境: {env_name} - {env_config['description']}")
            
            # 创建子目录
            for subdir in env_config['subdirs']:
                subdir_path = env_path / subdir
                subdir_path.mkdir(exist_ok=True)
                print(f"   📂 {subdir}/")
            
            # 创建环境配置文件
            config = {
                'environment_name': env_name,
                'description': env_config['description'],
                'created_time': datetime.now().isoformat(),
                'version': '1.0.0',
                'subdirectories': env_config['subdirs'],
                'purpose': self._get_environment_purpose(env_name),
                'usage_guidelines': self._get_usage_guidelines(env_name)
            }
            
            config_file = env_path / 'environment_config.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
            
            # 创建README文件
            readme_content = self._generate_environment_readme(env_name, env_config)
            readme_file = env_path / 'README.md'
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(readme_content)
        
        # 创建全局测试环境配置
        global_config = {
            'project_name': 'MythQuant',
            'test_environment_version': '2.0.0',
            'created_time': datetime.now().isoformat(),
            'structure': self.test_structure,
            'migration_from': 'Scattered directories + TestCase/01',
            'design_principles': [
                '测试环境与项目代码同步管理',
                '按测试类型和功能模块分层组织', 
                '支持CI/CD集成和自动化测试',
                '便于版本控制和团队协作',
                '数据安全和环境隔离',
                '可扩展和可维护'
            ]
        }
        
        global_config_file = test_env_root / 'test_environment_config.json'
        with open(global_config_file, 'w', encoding='utf-8') as f:
            json.dump(global_config, f, indent=2, ensure_ascii=False)
        
        print(f"\n✅ 统一测试环境结构创建完成")
    
    def _get_environment_purpose(self, env_name: str) -> str:
        """获取环境用途说明"""
        purposes = {
            'unit_tests': '验证单个函数、类、模块的功能正确性',
            'integration_tests': '验证多个模块间的集成和交互',
            'performance_tests': '评估系统性能、响应时间、资源使用',
            'regression_tests': '确保新代码不会破坏现有功能',
            'minute_data_tests': '专门测试1分钟数据处理的准确性和完整性',
            'data_quality_tests': '验证数据质量、格式、完整性',
            'shared': '提供各测试环境共享的资源和工具'
        }
        return purposes.get(env_name, '专项测试环境')
    
    def _get_usage_guidelines(self, env_name: str) -> List[str]:
        """获取使用指南"""
        guidelines = {
            'unit_tests': [
                '每个测试函数专注测试一个功能点',
                '使用mock对象隔离外部依赖',
                '保持测试快速执行（<1秒）',
                '测试覆盖率目标：>90%'
            ],
            'integration_tests': [
                '测试真实的模块间交互',
                '使用真实数据但控制数据量',
                '验证端到端的业务流程',
                '执行时间控制在合理范围内'
            ],
            'performance_tests': [
                '建立性能基线和目标',
                '使用代表性的数据集',
                '监控内存、CPU、IO使用',
                '记录性能回归情况'
            ],
            'regression_tests': [
                '保留历史问题的测试案例',
                '每次发布前必须全部通过',
                '维护测试数据的一致性',
                '记录测试失败的根本原因'
            ],
            'minute_data_tests': [
                '使用固定的测试数据集',
                '验证数据完整性（240条/交易日）',
                '检查前复权价格计算准确性',
                '测试数据格式和字段完整性'
            ],
            'data_quality_tests': [
                '定义数据质量标准',
                '自动化数据验证流程',
                '生成数据质量报告',
                '监控数据质量趋势'
            ],
            'shared': [
                '提供可重用的测试工具',
                '维护测试数据模板',
                '共享测试配置和脚本',
                '统一测试报告格式'
            ]
        }
        return guidelines.get(env_name, ['遵循测试最佳实践'])
    
    def _generate_environment_readme(self, env_name: str, env_config: Dict) -> str:
        """生成环境README文件"""
        purpose = self._get_environment_purpose(env_name)
        guidelines = self._get_usage_guidelines(env_name)
        
        readme = f"""# {env_config['description']}

## 📋 环境用途
{purpose}

## 🏗️ 目录结构
```
{env_name}/
"""
        
        for subdir in env_config['subdirs']:
            readme += f"├── {subdir}/\n"
        
        readme += f"""├── environment_config.json    # 环境配置文件
└── README.md                  # 本文件
```

## 📖 使用指南
"""
        
        for i, guideline in enumerate(guidelines, 1):
            readme += f"{i}. {guideline}\n"
        
        readme += f"""
## 🔧 快速开始

### 运行测试
```bash
# 在项目根目录执行
python -m pytest test_environments/{env_name}/
```

### 添加测试数据
```bash
# 将测试数据放入相应目录
cp your_test_data.txt test_environments/{env_name}/data/
```

### 查看测试结果
```bash
# 测试结果保存在results目录
ls test_environments/{env_name}/results/
```

## 📊 环境状态
- 创建时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}
- 版本: 1.0.0
- 状态: 活跃

## 🤝 贡献指南
1. 添加新测试前先查看现有测试
2. 遵循项目的测试命名规范
3. 更新相关文档和配置
4. 确保测试可重复执行

---
*本文档由测试环境管理系统自动生成*
"""
        return readme
    
    def migrate_legacy_data(self, dry_run=True):
        """迁移现有测试数据"""
        action = "试运行" if dry_run else "实际执行"
        print(f"\n🚀 {action}数据迁移")
        print("=" * 80)
        
        if dry_run:
            print("⚠️ 这是试运行，不会实际移动文件")
        
        # 迁移项目内的分散目录
        for source_dir, target_path in self.legacy_dirs.items():
            source = self.project_root / source_dir
            
            if target_path == 'DELETE':
                if source.exists():
                    print(f"🗑️ 删除: {source}")
                    if not dry_run:
                        shutil.rmtree(source)
                continue
            
            if source.exists():
                target = self.project_root / target_path
                print(f"📦 迁移: {source} → {target}")
                
                if not dry_run:
                    target.parent.mkdir(parents=True, exist_ok=True)
                    if target.exists():
                        shutil.rmtree(target)
                    shutil.move(str(source), str(target))
        
        # 迁移TestCase/01的数据
        testcase_source = Path(self.testcase_migration['source'])
        if testcase_source.exists():
            target = self.project_root / self.testcase_migration['target']
            print(f"📦 迁移TestCase数据: {testcase_source} → {target}")
            
            if not dry_run:
                target.mkdir(parents=True, exist_ok=True)
                
                # 复制文件而不是移动（保留原始位置作为备份）
                for item in testcase_source.iterdir():
                    if item.is_file() and item.name.startswith('test_'):
                        target_file = target / 'input_data' / item.name
                        target_file.parent.mkdir(exist_ok=True)
                        shutil.copy2(item, target_file)
                        print(f"   📄 复制: {item.name}")
        
        print(f"\n✅ {action}完成")
    
    def generate_migration_report(self):
        """生成迁移报告"""
        report = {
            'migration_time': datetime.now().isoformat(),
            'migration_type': 'Legacy to Unified Test Environment',
            'source_analysis': self._analyze_legacy_structure(),
            'target_structure': self.test_structure,
            'migration_mapping': self.legacy_dirs,
            'testcase_migration': self.testcase_migration,
            'benefits': [
                '统一的测试环境管理',
                '更好的版本控制集成',
                '支持CI/CD自动化',
                '清晰的测试分类',
                '便于团队协作',
                '符合现代开发实践'
            ]
        }
        
        report_file = f"test_environment_migration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 迁移报告已保存: {report_file}")
        return report
    
    def _analyze_legacy_structure(self):
        """分析现有结构"""
        analysis = {}
        
        for dir_name in self.legacy_dirs.keys():
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                files = list(dir_path.rglob('*'))
                file_count = len([f for f in files if f.is_file()])
                analysis[dir_name] = {
                    'exists': True,
                    'file_count': file_count
                }
            else:
                analysis[dir_name] = {
                    'exists': False,
                    'file_count': 0
                }
        
        return analysis


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='统一测试环境创建工具')
    parser.add_argument('--create', action='store_true', help='创建统一测试环境结构')
    parser.add_argument('--migrate', action='store_true', help='迁移现有数据（试运行）')
    parser.add_argument('--execute', action='store_true', help='实际执行迁移')
    parser.add_argument('--report', action='store_true', help='生成迁移报告')
    parser.add_argument('--all', action='store_true', help='执行完整流程')
    
    args = parser.parse_args()
    
    creator = UnifiedTestEnvironmentCreator()
    
    print("🧪 统一测试环境创建工具")
    print("=" * 80)
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    if args.create or args.all:
        creator.create_unified_structure()
    
    if args.migrate or args.all:
        creator.migrate_legacy_data(dry_run=not args.execute)
    
    if args.report or args.all:
        creator.generate_migration_report()
    
    if not any([args.create, args.migrate, args.report, args.all]):
        print("请指定操作选项，使用 --help 查看帮助")
        parser.print_help()


if __name__ == '__main__':
    main()
