#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
金融领域设计模式

专门针对金融量化交易领域的设计模式实现
"""

from abc import ABC, abstractmethod
from typing import Dict, List, Any, Optional, Tuple, Protocol, TypeVar, Generic
from dataclasses import dataclass, field
from enum import Enum
from datetime import datetime, date
from decimal import Decimal
import asyncio
from .architectural_patterns import (
    Strategy, Factory, Observer, Subject, 
    ChainOfResponsibility, State, StateMachine,
    Pipeline, EventBus
)


# =============================================================================
# 金融数据类型定义
# =============================================================================

@dataclass
class StockPrice:
    """股票价格"""
    symbol: str
    price: Decimal
    timestamp: datetime
    volume: int = 0
    
    def __post_init__(self):
        if isinstance(self.price, (int, float)):
            self.price = Decimal(str(self.price))


@dataclass
class MarketData:
    """市场数据"""
    symbol: str
    open_price: Decimal
    high_price: Decimal
    low_price: Decimal
    close_price: Decimal
    volume: int
    timestamp: datetime
    
    def __post_init__(self):
        for field_name in ['open_price', 'high_price', 'low_price', 'close_price']:
            value = getattr(self, field_name)
            if isinstance(value, (int, float)):
                setattr(self, field_name, Decimal(str(value)))


@dataclass
class TradingSignal:
    """交易信号"""
    symbol: str
    signal_type: str  # BUY, SELL, HOLD
    strength: float   # 0.0 - 1.0
    price: Decimal
    timestamp: datetime
    reason: str = ""


# =============================================================================
# 策略模式 - 技术指标计算
# =============================================================================

class TechnicalIndicatorStrategy(Strategy[List[MarketData], Dict[str, List[float]]]):
    """技术指标计算策略"""
    
    @abstractmethod
    async def execute(self, market_data: List[MarketData]) -> Dict[str, List[float]]:
        """计算技术指标"""
        pass
    
    @abstractmethod
    def get_indicator_name(self) -> str:
        """获取指标名称"""
        pass


class MovingAverageStrategy(TechnicalIndicatorStrategy):
    """移动平均线策略"""
    
    def __init__(self, period: int):
        self.period = period
    
    async def execute(self, market_data: List[MarketData]) -> Dict[str, List[float]]:
        """计算移动平均线"""
        if len(market_data) < self.period:
            return {self.get_indicator_name(): []}
        
        ma_values = []
        for i in range(self.period - 1, len(market_data)):
            period_data = market_data[i - self.period + 1:i + 1]
            avg_price = sum(float(data.close_price) for data in period_data) / self.period
            ma_values.append(avg_price)
        
        return {self.get_indicator_name(): ma_values}
    
    def get_indicator_name(self) -> str:
        return f"MA{self.period}"


class RSIStrategy(TechnicalIndicatorStrategy):
    """RSI相对强弱指标策略"""
    
    def __init__(self, period: int = 14):
        self.period = period
    
    async def execute(self, market_data: List[MarketData]) -> Dict[str, List[float]]:
        """计算RSI"""
        if len(market_data) < self.period + 1:
            return {self.get_indicator_name(): []}
        
        # 计算价格变化
        price_changes = []
        for i in range(1, len(market_data)):
            change = float(market_data[i].close_price - market_data[i-1].close_price)
            price_changes.append(change)
        
        # 计算RSI
        rsi_values = []
        for i in range(self.period - 1, len(price_changes)):
            period_changes = price_changes[i - self.period + 1:i + 1]
            
            gains = [change for change in period_changes if change > 0]
            losses = [-change for change in period_changes if change < 0]
            
            avg_gain = sum(gains) / len(gains) if gains else 0
            avg_loss = sum(losses) / len(losses) if losses else 0
            
            if avg_loss == 0:
                rsi = 100
            else:
                rs = avg_gain / avg_loss
                rsi = 100 - (100 / (1 + rs))
            
            rsi_values.append(rsi)
        
        return {self.get_indicator_name(): rsi_values}
    
    def get_indicator_name(self) -> str:
        return f"RSI{self.period}"


class IndicatorCalculatorContext:
    """技术指标计算上下文"""
    
    def __init__(self):
        self._strategies: List[TechnicalIndicatorStrategy] = []
    
    def add_strategy(self, strategy: TechnicalIndicatorStrategy):
        """添加策略"""
        self._strategies.append(strategy)
    
    async def calculate_all(self, market_data: List[MarketData]) -> Dict[str, List[float]]:
        """计算所有指标"""
        results = {}
        
        for strategy in self._strategies:
            indicator_result = await strategy.execute(market_data)
            results.update(indicator_result)
        
        return results


# =============================================================================
# 工厂模式 - 交易策略工厂
# =============================================================================

class TradingStrategy(ABC):
    """交易策略基类"""
    
    @abstractmethod
    async def generate_signal(self, market_data: List[MarketData]) -> Optional[TradingSignal]:
        """生成交易信号"""
        pass
    
    @abstractmethod
    def get_strategy_name(self) -> str:
        """获取策略名称"""
        pass


class MACrossoverStrategy(TradingStrategy):
    """移动平均线交叉策略"""
    
    def __init__(self, short_period: int = 5, long_period: int = 20):
        self.short_period = short_period
        self.long_period = long_period
        self.short_ma_strategy = MovingAverageStrategy(short_period)
        self.long_ma_strategy = MovingAverageStrategy(long_period)
    
    async def generate_signal(self, market_data: List[MarketData]) -> Optional[TradingSignal]:
        """生成交易信号"""
        if len(market_data) < self.long_period:
            return None
        
        # 计算移动平均线
        short_ma_result = await self.short_ma_strategy.execute(market_data)
        long_ma_result = await self.long_ma_strategy.execute(market_data)
        
        short_ma = short_ma_result[f"MA{self.short_period}"]
        long_ma = long_ma_result[f"MA{self.long_period}"]
        
        if not short_ma or not long_ma:
            return None
        
        # 检查交叉
        current_short = short_ma[-1]
        current_long = long_ma[-1]
        
        if len(short_ma) > 1 and len(long_ma) > 1:
            prev_short = short_ma[-2]
            prev_long = long_ma[-2]
            
            # 金叉 - 买入信号
            if prev_short <= prev_long and current_short > current_long:
                return TradingSignal(
                    symbol=market_data[-1].symbol,
                    signal_type="BUY",
                    strength=0.8,
                    price=market_data[-1].close_price,
                    timestamp=market_data[-1].timestamp,
                    reason=f"MA{self.short_period} 上穿 MA{self.long_period}"
                )
            
            # 死叉 - 卖出信号
            elif prev_short >= prev_long and current_short < current_long:
                return TradingSignal(
                    symbol=market_data[-1].symbol,
                    signal_type="SELL",
                    strength=0.8,
                    price=market_data[-1].close_price,
                    timestamp=market_data[-1].timestamp,
                    reason=f"MA{self.short_period} 下穿 MA{self.long_period}"
                )
        
        return None
    
    def get_strategy_name(self) -> str:
        return f"MA交叉策略({self.short_period},{self.long_period})"


class RSIStrategy(TradingStrategy):
    """RSI交易策略"""
    
    def __init__(self, period: int = 14, oversold: float = 30, overbought: float = 70):
        self.period = period
        self.oversold = oversold
        self.overbought = overbought
        self.rsi_calculator = RSIStrategy(period)
    
    async def generate_signal(self, market_data: List[MarketData]) -> Optional[TradingSignal]:
        """生成交易信号"""
        rsi_result = await self.rsi_calculator.execute(market_data)
        rsi_values = rsi_result.get(f"RSI{self.period}", [])
        
        if not rsi_values:
            return None
        
        current_rsi = rsi_values[-1]
        
        # 超卖 - 买入信号
        if current_rsi < self.oversold:
            return TradingSignal(
                symbol=market_data[-1].symbol,
                signal_type="BUY",
                strength=min(1.0, (self.oversold - current_rsi) / self.oversold),
                price=market_data[-1].close_price,
                timestamp=market_data[-1].timestamp,
                reason=f"RSI超卖 ({current_rsi:.1f})"
            )
        
        # 超买 - 卖出信号
        elif current_rsi > self.overbought:
            return TradingSignal(
                symbol=market_data[-1].symbol,
                signal_type="SELL",
                strength=min(1.0, (current_rsi - self.overbought) / (100 - self.overbought)),
                price=market_data[-1].close_price,
                timestamp=market_data[-1].timestamp,
                reason=f"RSI超买 ({current_rsi:.1f})"
            )
        
        return None
    
    def get_strategy_name(self) -> str:
        return f"RSI策略({self.period},{self.oversold},{self.overbought})"


class TradingStrategyFactory(Factory[TradingStrategy]):
    """交易策略工厂"""
    
    def create(self, strategy_type: str, **kwargs) -> TradingStrategy:
        """创建交易策略"""
        if strategy_type == "MA_CROSSOVER":
            short_period = kwargs.get("short_period", 5)
            long_period = kwargs.get("long_period", 20)
            return MACrossoverStrategy(short_period, long_period)
        
        elif strategy_type == "RSI":
            period = kwargs.get("period", 14)
            oversold = kwargs.get("oversold", 30)
            overbought = kwargs.get("overbought", 70)
            return RSIStrategy(period, oversold, overbought)
        
        else:
            raise ValueError(f"Unknown strategy type: {strategy_type}")


# =============================================================================
# 观察者模式 - 市场数据订阅
# =============================================================================

class MarketDataObserver(Observer):
    """市场数据观察者"""
    
    @abstractmethod
    async def update(self, market_data: MarketData):
        """更新市场数据"""
        pass


class TradingSignalGenerator(MarketDataObserver):
    """交易信号生成器"""
    
    def __init__(self, strategy: TradingStrategy):
        self.strategy = strategy
        self.market_data_buffer: List[MarketData] = []
        self.max_buffer_size = 100
    
    async def update(self, market_data: MarketData):
        """更新市场数据并生成信号"""
        # 添加到缓冲区
        self.market_data_buffer.append(market_data)
        
        # 保持缓冲区大小
        if len(self.market_data_buffer) > self.max_buffer_size:
            self.market_data_buffer.pop(0)
        
        # 生成交易信号
        signal = await self.strategy.generate_signal(self.market_data_buffer)
        
        if signal:
            print(f"🔔 交易信号: {signal.symbol} {signal.signal_type} "
                  f"强度:{signal.strength:.2f} 价格:{signal.price} "
                  f"原因:{signal.reason}")


class RiskMonitor(MarketDataObserver):
    """风险监控器"""
    
    def __init__(self, max_price_change: float = 0.05):
        self.max_price_change = max_price_change
        self.previous_prices: Dict[str, Decimal] = {}
    
    async def update(self, market_data: MarketData):
        """监控价格变化风险"""
        symbol = market_data.symbol
        current_price = market_data.close_price
        
        if symbol in self.previous_prices:
            prev_price = self.previous_prices[symbol]
            price_change = abs(float(current_price - prev_price) / float(prev_price))
            
            if price_change > self.max_price_change:
                print(f"⚠️ 风险警告: {symbol} 价格变化过大 "
                      f"{prev_price} -> {current_price} "
                      f"({price_change:.2%})")
        
        self.previous_prices[symbol] = current_price


class MarketDataPublisher(Subject):
    """市场数据发布者"""
    
    def __init__(self):
        super().__init__()
        self.latest_data: Dict[str, MarketData] = {}
    
    async def publish_market_data(self, market_data: MarketData):
        """发布市场数据"""
        self.latest_data[market_data.symbol] = market_data
        await self.notify(market_data)
    
    def get_latest_data(self, symbol: str) -> Optional[MarketData]:
        """获取最新数据"""
        return self.latest_data.get(symbol)


# =============================================================================
# 责任链模式 - 交易订单处理
# =============================================================================

@dataclass
class TradingOrder:
    """交易订单"""
    order_id: str
    symbol: str
    order_type: str  # MARKET, LIMIT, STOP
    side: str        # BUY, SELL
    quantity: int
    price: Optional[Decimal] = None
    status: str = "PENDING"
    timestamp: datetime = field(default_factory=datetime.now)


class OrderValidator(ChainOfResponsibility[TradingOrder]):
    """订单验证器"""
    
    async def _handle(self, order: TradingOrder) -> Optional[bool]:
        """验证订单"""
        # 基本验证
        if not order.symbol or not order.side or order.quantity <= 0:
            order.status = "REJECTED"
            print(f"❌ 订单验证失败: {order.order_id} - 基本信息不完整")
            return False
        
        # 限价单必须有价格
        if order.order_type == "LIMIT" and order.price is None:
            order.status = "REJECTED"
            print(f"❌ 订单验证失败: {order.order_id} - 限价单缺少价格")
            return False
        
        print(f"✅ 订单验证通过: {order.order_id}")
        return None  # 继续下一个处理器


class RiskChecker(ChainOfResponsibility[TradingOrder]):
    """风险检查器"""
    
    def __init__(self, max_order_value: Decimal = Decimal('100000')):
        super().__init__()
        self.max_order_value = max_order_value
    
    async def _handle(self, order: TradingOrder) -> Optional[bool]:
        """风险检查"""
        if order.price:
            order_value = order.price * order.quantity
            
            if order_value > self.max_order_value:
                order.status = "REJECTED"
                print(f"❌ 风险检查失败: {order.order_id} - "
                      f"订单金额过大 {order_value}")
                return False
        
        print(f"✅ 风险检查通过: {order.order_id}")
        return None  # 继续下一个处理器


class OrderExecutor(ChainOfResponsibility[TradingOrder]):
    """订单执行器"""
    
    async def _handle(self, order: TradingOrder) -> Optional[bool]:
        """执行订单"""
        # 模拟订单执行
        order.status = "EXECUTED"
        print(f"🎯 订单执行成功: {order.order_id} - "
              f"{order.side} {order.quantity} {order.symbol}")
        return True


# =============================================================================
# 状态模式 - 交易会话状态
# =============================================================================

class TradingSessionState(State['TradingSession']):
    """交易会话状态"""
    pass


class PreMarketState(TradingSessionState):
    """盘前状态"""
    
    async def handle(self, session: 'TradingSession') -> Optional[TradingSessionState]:
        """处理盘前状态"""
        current_time = datetime.now().time()
        
        # 9:30开盘
        if current_time >= session.market_open_time:
            print("📈 市场开盘")
            return MarketOpenState()
        
        return None


class MarketOpenState(TradingSessionState):
    """开盘状态"""
    
    async def handle(self, session: 'TradingSession') -> Optional[TradingSessionState]:
        """处理开盘状态"""
        current_time = datetime.now().time()
        
        # 11:30午休
        if current_time >= session.lunch_break_time:
            print("🍽️ 午休时间")
            return LunchBreakState()
        
        return None


class LunchBreakState(TradingSessionState):
    """午休状态"""
    
    async def handle(self, session: 'TradingSession') -> Optional[TradingSessionState]:
        """处理午休状态"""
        current_time = datetime.now().time()
        
        # 13:00下午开盘
        if current_time >= session.afternoon_open_time:
            print("📈 下午开盘")
            return AfternoonTradingState()
        
        return None


class AfternoonTradingState(TradingSessionState):
    """下午交易状态"""
    
    async def handle(self, session: 'TradingSession') -> Optional[TradingSessionState]:
        """处理下午交易状态"""
        current_time = datetime.now().time()
        
        # 15:00收盘
        if current_time >= session.market_close_time:
            print("📉 市场收盘")
            return PostMarketState()
        
        return None


class PostMarketState(TradingSessionState):
    """盘后状态"""
    
    async def handle(self, session: 'TradingSession') -> Optional[TradingSessionState]:
        """处理盘后状态"""
        # 可以添加盘后处理逻辑
        return None


class TradingSession:
    """交易会话"""
    
    def __init__(self):
        from datetime import time
        self.market_open_time = time(9, 30)
        self.lunch_break_time = time(11, 30)
        self.afternoon_open_time = time(13, 0)
        self.market_close_time = time(15, 0)
        
        self.state_machine = StateMachine(PreMarketState())
        self.state_machine.set_context(self)
    
    async def update_session_state(self):
        """更新会话状态"""
        await self.state_machine.transition()
    
    @property
    def current_state_name(self) -> str:
        """当前状态名称"""
        return self.state_machine.current_state.__class__.__name__


def demonstrate_financial_patterns():
    """演示金融设计模式"""
    print("💰 金融领域设计模式演示")
    print("=" * 50)
    
    # 创建模拟市场数据
    market_data = [
        MarketData("000001", Decimal("10.0"), Decimal("10.5"), Decimal("9.8"), Decimal("10.2"), 1000000, datetime.now()),
        MarketData("000001", Decimal("10.2"), Decimal("10.8"), Decimal("10.0"), Decimal("10.6"), 1200000, datetime.now()),
        MarketData("000001", Decimal("10.6"), Decimal("11.0"), Decimal("10.4"), Decimal("10.8"), 1100000, datetime.now()),
    ]
    
    print("📊 技术指标计算演示:")
    # 策略模式演示
    indicator_context = IndicatorCalculatorContext()
    indicator_context.add_strategy(MovingAverageStrategy(2))
    indicator_context.add_strategy(RSIStrategy(2))
    
    # 工厂模式演示
    print("\n🏭 交易策略工厂演示:")
    factory = TradingStrategyFactory()
    ma_strategy = factory.create("MA_CROSSOVER", short_period=5, long_period=10)
    print(f"创建策略: {ma_strategy.get_strategy_name()}")
    
    # 观察者模式演示
    print("\n👁️ 市场数据观察者演示:")
    publisher = MarketDataPublisher()
    signal_generator = TradingSignalGenerator(ma_strategy)
    risk_monitor = RiskMonitor()
    
    publisher.attach(signal_generator)
    publisher.attach(risk_monitor)
    
    print("模式演示完成！")


if __name__ == "__main__":
    demonstrate_financial_patterns()
