#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
技术指标算法模块

纯函数技术指标实现，包括移动平均、RSI、MACD、布林带等
"""

import math
from typing import List, Tuple, Optional, Dict
from .mathematical import moving_average, exponential_smoothing, standard_deviation
from ..exceptions import InsufficientDataError, InvalidParameterError


def sma(prices: List[float], period: int) -> List[float]:
    """
    简单移动平均 (Simple Moving Average)
    
    Args:
        prices: 价格序列
        period: 周期
    
    Returns:
        SMA序列
    """
    return moving_average(prices, period)


def ema(prices: List[float], period: int, alpha: Optional[float] = None) -> List[float]:
    """
    指数移动平均 (Exponential Moving Average)
    
    Args:
        prices: 价格序列
        period: 周期
        alpha: 平滑系数，如果为None则使用 2/(period+1)
    
    Returns:
        EMA序列
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 参数无效
    """
    if not prices:
        raise InsufficientDataError("EMA计算", 1, 0)
    
    if period <= 0:
        raise InvalidParameterError("EMA计算", "period", period, "周期必须大于0")
    
    if alpha is None:
        alpha = 2.0 / (period + 1)
    
    if not (0 < alpha <= 1):
        raise InvalidParameterError("EMA计算", "alpha", alpha, "alpha必须在(0,1]范围内")
    
    result = [prices[0]]  # 第一个值作为初始EMA
    
    for i in range(1, len(prices)):
        ema_value = alpha * prices[i] + (1 - alpha) * result[-1]
        result.append(ema_value)
    
    return result


def rsi(prices: List[float], period: int = 14) -> List[float]:
    """
    相对强弱指标 (Relative Strength Index)
    
    Args:
        prices: 价格序列
        period: 周期
    
    Returns:
        RSI序列
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 参数无效
    """
    if len(prices) < period + 1:
        raise InsufficientDataError("RSI计算", period + 1, len(prices))
    
    if period <= 0:
        raise InvalidParameterError("RSI计算", "period", period, "周期必须大于0")
    
    # 计算价格变化
    price_changes = [prices[i] - prices[i-1] for i in range(1, len(prices))]
    
    # 分离上涨和下跌
    gains = [max(0, change) for change in price_changes]
    losses = [max(0, -change) for change in price_changes]
    
    # 计算初始平均增益和损失
    avg_gain = sum(gains[:period]) / period
    avg_loss = sum(losses[:period]) / period
    
    rsi_values = []
    
    # 计算第一个RSI值
    if avg_loss == 0:
        rsi_values.append(100.0)
    else:
        rs = avg_gain / avg_loss
        rsi_values.append(100 - (100 / (1 + rs)))
    
    # 计算后续RSI值 (使用Wilder's平滑)
    for i in range(period, len(price_changes)):
        avg_gain = (avg_gain * (period - 1) + gains[i]) / period
        avg_loss = (avg_loss * (period - 1) + losses[i]) / period
        
        if avg_loss == 0:
            rsi_values.append(100.0)
        else:
            rs = avg_gain / avg_loss
            rsi_values.append(100 - (100 / (1 + rs)))
    
    return rsi_values


def macd(prices: List[float], 
         fast_period: int = 12, 
         slow_period: int = 26, 
         signal_period: int = 9) -> Tuple[List[float], List[float], List[float]]:
    """
    MACD指标 (Moving Average Convergence Divergence)
    
    Args:
        prices: 价格序列
        fast_period: 快线周期
        slow_period: 慢线周期
        signal_period: 信号线周期
    
    Returns:
        (MACD线, 信号线, 柱状图)
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 参数无效
    """
    if len(prices) < slow_period:
        raise InsufficientDataError("MACD计算", slow_period, len(prices))
    
    if fast_period >= slow_period:
        raise InvalidParameterError("MACD计算", "fast_period", fast_period, 
                                   f"快线周期必须小于慢线周期({slow_period})")
    
    if signal_period <= 0:
        raise InvalidParameterError("MACD计算", "signal_period", signal_period, 
                                   "信号线周期必须大于0")
    
    # 计算快线和慢线EMA
    fast_ema = ema(prices, fast_period)
    slow_ema = ema(prices, slow_period)
    
    # 计算MACD线 (快线EMA - 慢线EMA)
    # 需要对齐长度，从慢线EMA开始的位置计算
    start_index = slow_period - fast_period
    macd_line = [fast_ema[i + start_index] - slow_ema[i] for i in range(len(slow_ema))]
    
    # 计算信号线 (MACD线的EMA)
    if len(macd_line) < signal_period:
        return macd_line, [], []
    
    signal_line = ema(macd_line, signal_period)
    
    # 计算柱状图 (MACD线 - 信号线)
    histogram_start = len(macd_line) - len(signal_line)
    histogram = [macd_line[i + histogram_start] - signal_line[i] for i in range(len(signal_line))]
    
    return macd_line, signal_line, histogram


def bollinger_bands(prices: List[float], 
                   period: int = 20, 
                   std_dev: float = 2.0) -> Tuple[List[float], List[float], List[float]]:
    """
    布林带 (Bollinger Bands)
    
    Args:
        prices: 价格序列
        period: 周期
        std_dev: 标准差倍数
    
    Returns:
        (上轨, 中轨, 下轨)
    
    Raises:
        InsufficientDataError: 数据不足
        InvalidParameterError: 参数无效
    """
    if len(prices) < period:
        raise InsufficientDataError("布林带计算", period, len(prices))
    
    if period <= 0:
        raise InvalidParameterError("布林带计算", "period", period, "周期必须大于0")
    
    if std_dev <= 0:
        raise InvalidParameterError("布林带计算", "std_dev", std_dev, "标准差倍数必须大于0")
    
    # 计算中轨 (简单移动平均)
    middle_band = sma(prices, period)
    
    # 计算上轨和下轨
    upper_band = []
    lower_band = []
    
    for i in range(len(middle_band)):
        # 获取对应的价格窗口
        start_idx = i
        end_idx = i + period
        window_prices = prices[start_idx:end_idx]
        
        # 计算标准差
        std = standard_deviation(window_prices, ddof=0)
        
        # 计算上下轨
        upper_band.append(middle_band[i] + std_dev * std)
        lower_band.append(middle_band[i] - std_dev * std)
    
    return upper_band, middle_band, lower_band


def stochastic_oscillator(high_prices: List[float], 
                         low_prices: List[float], 
                         close_prices: List[float],
                         k_period: int = 14, 
                         d_period: int = 3) -> Tuple[List[float], List[float]]:
    """
    随机振荡器 (Stochastic Oscillator)
    
    Args:
        high_prices: 最高价序列
        low_prices: 最低价序列
        close_prices: 收盘价序列
        k_period: %K周期
        d_period: %D周期
    
    Returns:
        (%K, %D)
    
    Raises:
        InvalidParameterError: 数据长度不匹配或参数无效
        InsufficientDataError: 数据不足
    """
    if not (len(high_prices) == len(low_prices) == len(close_prices)):
        raise InvalidParameterError("随机振荡器计算", "数据长度", 
                                   [len(high_prices), len(low_prices), len(close_prices)],
                                   "高价、低价、收盘价序列长度必须相同")
    
    if len(close_prices) < k_period:
        raise InsufficientDataError("随机振荡器计算", k_period, len(close_prices))
    
    if k_period <= 0 or d_period <= 0:
        raise InvalidParameterError("随机振荡器计算", "period", [k_period, d_period], 
                                   "周期必须大于0")
    
    k_values = []
    
    # 计算%K值
    for i in range(k_period - 1, len(close_prices)):
        # 获取周期内的最高价和最低价
        period_high = max(high_prices[i - k_period + 1:i + 1])
        period_low = min(low_prices[i - k_period + 1:i + 1])
        
        if period_high == period_low:
            k_values.append(50.0)  # 避免除零
        else:
            k = 100 * (close_prices[i] - period_low) / (period_high - period_low)
            k_values.append(k)
    
    # 计算%D值 (%K的移动平均)
    if len(k_values) < d_period:
        return k_values, []
    
    d_values = sma(k_values, d_period)
    
    return k_values, d_values


def williams_r(high_prices: List[float], 
               low_prices: List[float], 
               close_prices: List[float],
               period: int = 14) -> List[float]:
    """
    威廉指标 (Williams %R)
    
    Args:
        high_prices: 最高价序列
        low_prices: 最低价序列
        close_prices: 收盘价序列
        period: 周期
    
    Returns:
        Williams %R序列
    """
    if not (len(high_prices) == len(low_prices) == len(close_prices)):
        raise InvalidParameterError("威廉指标计算", "数据长度", 
                                   [len(high_prices), len(low_prices), len(close_prices)],
                                   "高价、低价、收盘价序列长度必须相同")
    
    if len(close_prices) < period:
        raise InsufficientDataError("威廉指标计算", period, len(close_prices))
    
    williams_values = []
    
    for i in range(period - 1, len(close_prices)):
        # 获取周期内的最高价和最低价
        period_high = max(high_prices[i - period + 1:i + 1])
        period_low = min(low_prices[i - period + 1:i + 1])
        
        if period_high == period_low:
            williams_values.append(-50.0)  # 避免除零
        else:
            wr = -100 * (period_high - close_prices[i]) / (period_high - period_low)
            williams_values.append(wr)
    
    return williams_values


def atr(high_prices: List[float], 
        low_prices: List[float], 
        close_prices: List[float],
        period: int = 14) -> List[float]:
    """
    平均真实波幅 (Average True Range)
    
    Args:
        high_prices: 最高价序列
        low_prices: 最低价序列
        close_prices: 收盘价序列
        period: 周期
    
    Returns:
        ATR序列
    """
    if not (len(high_prices) == len(low_prices) == len(close_prices)):
        raise InvalidParameterError("ATR计算", "数据长度", 
                                   [len(high_prices), len(low_prices), len(close_prices)],
                                   "高价、低价、收盘价序列长度必须相同")
    
    if len(close_prices) < period + 1:
        raise InsufficientDataError("ATR计算", period + 1, len(close_prices))
    
    # 计算真实波幅 (True Range)
    true_ranges = []
    
    for i in range(1, len(close_prices)):
        tr1 = high_prices[i] - low_prices[i]
        tr2 = abs(high_prices[i] - close_prices[i-1])
        tr3 = abs(low_prices[i] - close_prices[i-1])
        
        true_range = max(tr1, tr2, tr3)
        true_ranges.append(true_range)
    
    # 计算ATR (真实波幅的移动平均)
    if len(true_ranges) < period:
        return []
    
    # 使用Wilder's平滑方法
    atr_values = []
    
    # 第一个ATR值是简单平均
    first_atr = sum(true_ranges[:period]) / period
    atr_values.append(first_atr)
    
    # 后续ATR值使用Wilder's平滑
    for i in range(period, len(true_ranges)):
        atr_value = (atr_values[-1] * (period - 1) + true_ranges[i]) / period
        atr_values.append(atr_value)
    
    return atr_values


def cci(high_prices: List[float], 
        low_prices: List[float], 
        close_prices: List[float],
        period: int = 20) -> List[float]:
    """
    商品通道指标 (Commodity Channel Index)
    
    Args:
        high_prices: 最高价序列
        low_prices: 最低价序列
        close_prices: 收盘价序列
        period: 周期
    
    Returns:
        CCI序列
    """
    if not (len(high_prices) == len(low_prices) == len(close_prices)):
        raise InvalidParameterError("CCI计算", "数据长度", 
                                   [len(high_prices), len(low_prices), len(close_prices)],
                                   "高价、低价、收盘价序列长度必须相同")
    
    if len(close_prices) < period:
        raise InsufficientDataError("CCI计算", period, len(close_prices))
    
    # 计算典型价格 (Typical Price)
    typical_prices = [(h + l + c) / 3 for h, l, c in zip(high_prices, low_prices, close_prices)]
    
    cci_values = []
    
    for i in range(period - 1, len(typical_prices)):
        # 计算周期内典型价格的移动平均
        period_tp = typical_prices[i - period + 1:i + 1]
        sma_tp = sum(period_tp) / period
        
        # 计算平均偏差
        mean_deviation = sum(abs(tp - sma_tp) for tp in period_tp) / period
        
        if mean_deviation == 0:
            cci_values.append(0.0)
        else:
            cci = (typical_prices[i] - sma_tp) / (0.015 * mean_deviation)
            cci_values.append(cci)
    
    return cci_values


def momentum(prices: List[float], period: int = 10) -> List[float]:
    """
    动量指标 (Momentum)
    
    Args:
        prices: 价格序列
        period: 周期
    
    Returns:
        动量指标序列
    """
    if len(prices) <= period:
        raise InsufficientDataError("动量指标计算", period + 1, len(prices))
    
    return [prices[i] - prices[i - period] for i in range(period, len(prices))]


def roc(prices: List[float], period: int = 10) -> List[float]:
    """
    变动率指标 (Rate of Change)
    
    Args:
        prices: 价格序列
        period: 周期
    
    Returns:
        ROC序列
    """
    if len(prices) <= period:
        raise InsufficientDataError("ROC计算", period + 1, len(prices))
    
    roc_values = []
    for i in range(period, len(prices)):
        if prices[i - period] == 0:
            roc_values.append(0.0)
        else:
            roc_val = 100 * (prices[i] - prices[i - period]) / prices[i - period]
            roc_values.append(roc_val)
    
    return roc_values


def obv(close_prices: List[float], volumes: List[float]) -> List[float]:
    """
    能量潮指标 (On-Balance Volume)
    
    Args:
        close_prices: 收盘价序列
        volumes: 成交量序列
    
    Returns:
        OBV序列
    """
    if len(close_prices) != len(volumes):
        raise InvalidParameterError("OBV计算", "volumes", len(volumes),
                                   f"成交量长度必须等于价格长度({len(close_prices)})")
    
    if len(close_prices) < 2:
        raise InsufficientDataError("OBV计算", 2, len(close_prices))
    
    obv_values = [volumes[0]]  # 第一个值等于第一天的成交量
    
    for i in range(1, len(close_prices)):
        if close_prices[i] > close_prices[i-1]:
            # 价格上涨，加上成交量
            obv_values.append(obv_values[-1] + volumes[i])
        elif close_prices[i] < close_prices[i-1]:
            # 价格下跌，减去成交量
            obv_values.append(obv_values[-1] - volumes[i])
        else:
            # 价格不变，OBV不变
            obv_values.append(obv_values[-1])
    
    return obv_values
