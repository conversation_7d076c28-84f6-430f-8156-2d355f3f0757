#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant项目统一测试配置文件
整合所有测试环境配置，提供统一的测试配置管理

创建时间: 2025-08-03
作者: AI Assistant
版本: 1.0.0
"""

import os
from pathlib import Path
from typing import Dict, List, Any, Optional

# ==================== 基础路径配置 ====================

# 项目根目录
PROJECT_ROOT = Path(__file__).parent

# 测试环境根目录
TEST_ENVIRONMENTS_ROOT = PROJECT_ROOT / 'test_environments'

# ==================== 测试环境配置 ====================

class TestEnvironmentConfig:
    """测试环境配置类"""
    
    # 测试环境类型定义
    ENVIRONMENT_TYPES = {
        'unit_tests': {
            'name': '单元测试环境',
            'description': '用于单个模块和函数的测试',
            'subdirs': ['data', 'results', 'reports', 'configs']
        },
        'integration_tests': {
            'name': '集成测试环境', 
            'description': '用于模块间集成测试',
            'subdirs': ['data', 'results', 'reports', 'configs']
        },
        'minute_data_tests': {
            'name': '1分钟数据专项测试环境',
            'description': '专门测试1分钟数据处理功能',
            'subdirs': ['input_data', 'output_data', 'expected_data', 'backup_data', 'results', 'configs']
        },
        'performance_tests': {
            'name': '性能测试环境',
            'description': '用于性能基准测试和优化验证',
            'subdirs': ['data', 'results', 'reports', 'configs', 'benchmarks']
        },
        'regression_tests': {
            'name': '回归测试环境',
            'description': '用于版本回归和功能验证',
            'subdirs': ['data', 'results', 'reports', 'configs', 'baselines']
        },
        'data_quality_tests': {
            'name': '数据质量测试环境',
            'description': '用于数据质量验证和规则检查',
            'subdirs': ['sample_data', 'validation_rules', 'results', 'reports']
        }
    }
    
    @classmethod
    def get_environment_path(cls, env_type: str) -> Path:
        """获取测试环境路径"""
        return TEST_ENVIRONMENTS_ROOT / env_type
    
    @classmethod
    def get_environment_config(cls, env_type: str) -> Dict[str, Any]:
        """获取测试环境配置"""
        return cls.ENVIRONMENT_TYPES.get(env_type, {})

# ==================== 分钟数据测试专项配置 ====================

class MinuteDataTestConfig:
    """1分钟数据测试专项配置"""
    
    # 测试环境路径
    BASE_PATH = TEST_ENVIRONMENTS_ROOT / 'minute_data_tests'
    INPUT_DATA_PATH = BASE_PATH / 'input_data'
    OUTPUT_DATA_PATH = BASE_PATH / 'output_data'
    EXPECTED_DATA_PATH = BASE_PATH / 'expected_data'
    BACKUP_DATA_PATH = BASE_PATH / 'backup_data'
    RESULTS_PATH = BASE_PATH / 'results'
    CONFIGS_PATH = BASE_PATH / 'configs'
    
    # 标准测试文件配置
    STANDARD_TEST_FILES = {
        'primary_test_file': {
            'filename': '1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt',
            'description': '主要测试文件，包含完整的1分钟数据',
            'expected_lines': 17213,  # 包含表头
            'expected_size_min': 800000,  # 最小文件大小（字节）
            'stock_code': '000617',
            'date_range': '20250320-20250704',
            'data_source': '来源互联网'
        },
        'sample_test_file': {
            'filename': 'test_1min_0_000617_sample.txt',
            'description': '样本测试文件，用于快速验证',
            'expected_lines': 10,
            'stock_code': '000617'
        }
    }
    
    # 数据质量验证标准
    DATA_QUALITY_STANDARDS = {
        'header_format': '股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖',
        'field_count': 8,
        'stock_code_format': {
            'length': 6,
            'type': 'digits_only'
        },
        'time_format': {
            'length': 12,
            'type': 'digits_only',
            'pattern': 'YYYYMMDDHHMM'
        },
        'price_fields': ['当日收盘价C', '前复权收盘价C'],
        'indicator_fields': ['买卖差', '路径总长', '主买', '主卖'],
        'minutes_per_trading_day': 240,  # 9:30-11:30 + 13:00-15:00
        'encoding': 'utf-8-sig'
    }
    
    # 测试执行配置
    EXECUTION_CONFIG = {
        'timeout_seconds': 300,  # 5分钟超时
        'max_memory_mb': 1024,   # 最大内存使用
        'parallel_tests': False,  # 是否并行执行测试
        'cleanup_after_test': True,  # 测试后清理临时文件
        'generate_reports': True,    # 是否生成测试报告
        'backup_results': True       # 是否备份测试结果
    }

# ==================== 测试文件管理配置 ====================

class TestFileManager:
    """测试文件管理配置"""
    
    # 文件命名规范
    NAMING_CONVENTIONS = {
        'minute_data': {
            'pattern': '{frequency}_0_{stock_code}_{date_range}_{source}.txt',
            'example': '1min_0_000617_20250320-20250704_来源互联网.txt',
            'components': {
                'frequency': ['1min', '5min', '15min', '30min', '60min'],
                'market_prefix': '0',  # 深证市场
                'stock_code': '6位数字',
                'date_range': 'YYYYMMDD-YYYYMMDD',
                'source': '数据来源描述'
            }
        },
        'test_prefix': {
            'pattern': 'test_{original_filename}',
            'description': '测试文件前缀，用于区分测试数据和生产数据'
        }
    }
    
    # 文件选择策略
    FILE_SELECTION_STRATEGY = {
        'priority_order': [
            'exact_filename_match',    # 精确文件名匹配
            'largest_file_size',       # 最大文件优先
            'most_recent_modified',    # 最新修改时间
            'complete_data_range'      # 完整数据范围
        ],
        'fallback_behavior': 'use_any_matching_file'
    }

# ==================== 测试结果验证配置 ====================

class TestValidationConfig:
    """测试结果验证配置"""
    
    # 成功率标准
    SUCCESS_RATE_STANDARDS = {
        'excellent': 0.95,    # 优秀：95%以上
        'good': 0.85,         # 良好：85%以上
        'acceptable': 0.75,   # 可接受：75%以上
        'poor': 0.60,         # 较差：60%以上
        'failed': 0.60        # 失败：60%以下
    }
    
    # 性能标准
    PERFORMANCE_STANDARDS = {
        'data_processing_time_max': 10.0,    # 数据处理最大时间（秒）
        'file_generation_time_max': 5.0,     # 文件生成最大时间（秒）
        'memory_usage_max_mb': 512,          # 最大内存使用（MB）
        'file_size_tolerance_percent': 5.0   # 文件大小容差百分比
    }
    
    # 数据质量标准
    DATA_QUALITY_STANDARDS = {
        'completeness_threshold': 0.95,      # 数据完整性阈值
        'accuracy_threshold': 0.99,          # 数据准确性阈值
        'consistency_threshold': 0.98,       # 数据一致性阈值
        'format_compliance': 1.0             # 格式合规性（必须100%）
    }

# ==================== 测试报告配置 ====================

class TestReportConfig:
    """测试报告配置"""
    
    # 报告格式
    REPORT_FORMATS = {
        'html': {
            'enabled': True,
            'template': 'test_report_template.html',
            'include_charts': True,
            'include_details': True
        },
        'json': {
            'enabled': True,
            'pretty_print': True,
            'include_raw_data': False
        },
        'markdown': {
            'enabled': True,
            'include_summary': True,
            'include_recommendations': True
        }
    }
    
    # 报告内容配置
    REPORT_CONTENT = {
        'include_test_summary': True,
        'include_performance_metrics': True,
        'include_data_quality_analysis': True,
        'include_error_details': True,
        'include_recommendations': True,
        'include_historical_comparison': True,
        'max_error_details': 50  # 最多显示的错误详情数量
    }
    
    # 报告存储配置
    STORAGE_CONFIG = {
        'base_directory': TEST_ENVIRONMENTS_ROOT / 'reports',
        'filename_pattern': 'test_report_{timestamp}.{format}',
        'retention_days': 30,  # 报告保留天数
        'auto_cleanup': True   # 自动清理过期报告
    }

# ==================== 全局测试配置 ====================

class GlobalTestConfig:
    """全局测试配置"""
    
    # 测试模式
    TEST_MODES = {
        'development': {
            'description': '开发模式，快速测试',
            'timeout_multiplier': 0.5,
            'detailed_logging': True,
            'cleanup_on_failure': False
        },
        'ci_cd': {
            'description': 'CI/CD模式，自动化测试',
            'timeout_multiplier': 1.0,
            'detailed_logging': False,
            'cleanup_on_failure': True
        },
        'production': {
            'description': '生产模式，完整测试',
            'timeout_multiplier': 2.0,
            'detailed_logging': True,
            'cleanup_on_failure': True
        }
    }
    
    # 默认配置
    DEFAULT_CONFIG = {
        'test_mode': 'development',
        'log_level': 'INFO',
        'parallel_execution': False,
        'stop_on_first_failure': False,
        'generate_coverage_report': True,
        'send_notifications': False
    }

# ==================== 配置验证和工具函数 ====================

def validate_test_environment() -> Dict[str, Any]:
    """验证测试环境配置"""
    validation_results = {
        'valid': True,
        'errors': [],
        'warnings': [],
        'info': []
    }
    
    # 检查测试环境目录
    if not TEST_ENVIRONMENTS_ROOT.exists():
        validation_results['errors'].append(f"测试环境根目录不存在: {TEST_ENVIRONMENTS_ROOT}")
        validation_results['valid'] = False
    
    # 检查各个测试环境
    for env_type in TestEnvironmentConfig.ENVIRONMENT_TYPES:
        env_path = TestEnvironmentConfig.get_environment_path(env_type)
        if not env_path.exists():
            validation_results['warnings'].append(f"测试环境目录不存在: {env_path}")
    
    # 检查分钟数据测试文件
    primary_file = MinuteDataTestConfig.INPUT_DATA_PATH / MinuteDataTestConfig.STANDARD_TEST_FILES['primary_test_file']['filename']
    if not primary_file.exists():
        validation_results['errors'].append(f"主要测试文件不存在: {primary_file}")
        validation_results['valid'] = False
    
    return validation_results

def get_test_config_summary() -> Dict[str, Any]:
    """获取测试配置摘要"""
    return {
        'project_root': str(PROJECT_ROOT),
        'test_environments_root': str(TEST_ENVIRONMENTS_ROOT),
        'available_environments': list(TestEnvironmentConfig.ENVIRONMENT_TYPES.keys()),
        'minute_data_test_files': len(list(MinuteDataTestConfig.INPUT_DATA_PATH.glob('*.txt')) if MinuteDataTestConfig.INPUT_DATA_PATH.exists() else []),
        'validation_status': validate_test_environment()
    }

# ==================== 测试模式控制 ====================

def enable_test_mode():
    """启用测试模式"""
    os.environ['MYTHQUANT_TEST_MODE'] = '1'
    print("✅ 测试模式已启用")

def disable_test_mode():
    """禁用测试模式"""
    if 'MYTHQUANT_TEST_MODE' in os.environ:
        del os.environ['MYTHQUANT_TEST_MODE']
    print("✅ 测试模式已禁用")

def is_test_mode() -> bool:
    """检查是否为测试模式"""
    return os.environ.get('MYTHQUANT_TEST_MODE') == '1'

# ==================== 导出配置 ====================

# 主要配置类导出
__all__ = [
    'TestEnvironmentConfig',
    'MinuteDataTestConfig',
    'TestFileManager',
    'TestValidationConfig',
    'TestReportConfig',
    'GlobalTestConfig',
    'validate_test_environment',
    'get_test_config_summary',
    'enable_test_mode',
    'disable_test_mode',
    'is_test_mode'
]

# 便捷访问的配置实例
TEST_ENV_CONFIG = TestEnvironmentConfig()
MINUTE_DATA_CONFIG = MinuteDataTestConfig()
FILE_MANAGER_CONFIG = TestFileManager()
VALIDATION_CONFIG = TestValidationConfig()
REPORT_CONFIG = TestReportConfig()
GLOBAL_CONFIG = GlobalTestConfig()

if __name__ == '__main__':
    # 配置验证和摘要输出
    print("🎯 MythQuant测试配置验证")
    print("=" * 60)
    
    summary = get_test_config_summary()
    print(f"📋 项目根目录: {summary['project_root']}")
    print(f"📋 测试环境根目录: {summary['test_environments_root']}")
    print(f"📋 可用测试环境: {len(summary['available_environments'])} 个")
    print(f"📋 分钟数据测试文件: {summary['minute_data_test_files']} 个")
    
    validation = summary['validation_status']
    if validation['valid']:
        print("✅ 测试环境配置验证通过")
    else:
        print("❌ 测试环境配置验证失败")
        for error in validation['errors']:
            print(f"   ❌ {error}")
    
    for warning in validation['warnings']:
        print(f"   ⚠️ {warning}")
