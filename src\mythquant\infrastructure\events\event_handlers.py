#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件处理器实现

定义事件处理器接口和具体实现
"""

import logging
from abc import ABC, abstractmethod
from typing import Type, Dict, Any, Optional, List
from datetime import datetime
import asyncio

from ...shared.patterns.architectural_patterns import DomainEvent
from ...domain.events.stock_events import (
    StockCreated, StockPriceChanged, StockSuspended, 
    StockResumed, StockDelisted, DividendAnnounced
)
from ...domain.events.market_events import (
    MarketOpened, MarketClosed, MarketHalted,
    TradingSessionStarted, TradingSessionEnded
)
from ...domain.exceptions import DomainException


logger = logging.getLogger(__name__)


class EventHandler(ABC):
    """事件处理器抽象基类"""
    
    @abstractmethod
    def can_handle(self, event: DomainEvent) -> bool:
        """判断是否可以处理该事件"""
        pass
    
    @abstractmethod
    def handle(self, event: DomainEvent):
        """处理事件"""
        pass
    
    @property
    @abstractmethod
    def handler_name(self) -> str:
        """处理器名称"""
        pass


class DomainEventHandler(EventHandler):
    """领域事件处理器基类"""
    
    def __init__(self, event_type: Type[DomainEvent]):
        self._event_type = event_type
        self._processed_events: List[str] = []
        self._error_count = 0
        self._last_error: Optional[Exception] = None
    
    def can_handle(self, event: DomainEvent) -> bool:
        """判断是否可以处理该事件"""
        return isinstance(event, self._event_type)
    
    def handle(self, event: DomainEvent):
        """处理事件"""
        if not self.can_handle(event):
            logger.warning(f"处理器 {self.handler_name} 无法处理事件 {event.event_type}")
            return
        
        try:
            logger.debug(f"处理器 {self.handler_name} 开始处理事件 {event.event_id}")
            
            self._handle_event(event)
            
            # 记录处理成功
            self._processed_events.append(event.event_id)
            logger.debug(f"处理器 {self.handler_name} 成功处理事件 {event.event_id}")
            
        except Exception as e:
            self._error_count += 1
            self._last_error = e
            logger.error(f"处理器 {self.handler_name} 处理事件失败: {event.event_id}, 错误: {e}")
            raise
    
    @abstractmethod
    def _handle_event(self, event: DomainEvent):
        """具体的事件处理逻辑"""
        pass
    
    @property
    def handler_name(self) -> str:
        """处理器名称"""
        return f"{self.__class__.__name__}({self._event_type.__name__})"
    
    def get_statistics(self) -> Dict[str, Any]:
        """获取处理器统计信息"""
        return {
            'handler_name': self.handler_name,
            'event_type': self._event_type.__name__,
            'processed_count': len(self._processed_events),
            'error_count': self._error_count,
            'last_error': str(self._last_error) if self._last_error else None
        }


class StockCreatedHandler(DomainEventHandler):
    """股票创建事件处理器"""
    
    def __init__(self):
        super().__init__(StockCreated)
    
    def _handle_event(self, event: StockCreated):
        """处理股票创建事件"""
        logger.info(f"股票创建: {event.stock_code} - {event.stock_name} 在市场 {event.market_code}")
        
        # 这里可以添加具体的业务逻辑，比如：
        # 1. 更新股票列表缓存
        # 2. 发送通知
        # 3. 初始化相关数据结构
        # 4. 触发其他业务流程
        
        # 示例：记录到审计日志
        self._log_audit_event("STOCK_CREATED", {
            'stock_code': event.stock_code,
            'stock_name': event.stock_name,
            'market_code': event.market_code,
            'created_at': event.created_at.isoformat()
        })
    
    def _log_audit_event(self, action: str, data: Dict[str, Any]):
        """记录审计事件"""
        logger.info(f"审计日志: {action} - {data}")


class StockPriceChangedHandler(DomainEventHandler):
    """股票价格变化事件处理器"""
    
    def __init__(self):
        super().__init__(StockPriceChanged)
        self._price_alerts: List[Dict[str, Any]] = []
    
    def _handle_event(self, event: StockPriceChanged):
        """处理股票价格变化事件"""
        logger.debug(f"股票价格变化: {event.stock_code} "
                    f"从 {event.old_price} 到 {event.new_price}")
        
        # 检查价格异常波动
        if event.old_price and event.price_change_percentage:
            change_pct = abs(float(event.price_change_percentage))
            if change_pct > 10.0:  # 超过10%的变化
                self._trigger_price_alert(event, change_pct)
        
        # 更新价格缓存
        self._update_price_cache(event)
        
        # 触发技术指标计算
        self._trigger_indicator_calculation(event)
    
    def _trigger_price_alert(self, event: StockPriceChanged, change_pct: float):
        """触发价格警报"""
        alert = {
            'stock_code': event.stock_code,
            'old_price': str(event.old_price),
            'new_price': str(event.new_price),
            'change_percentage': change_pct,
            'alert_time': datetime.now().isoformat(),
            'alert_type': 'PRICE_VOLATILITY'
        }
        
        self._price_alerts.append(alert)
        logger.warning(f"价格异常波动警报: {event.stock_code} 变化 {change_pct:.2f}%")
    
    def _update_price_cache(self, event: StockPriceChanged):
        """更新价格缓存"""
        # 这里可以更新Redis缓存或内存缓存
        logger.debug(f"更新价格缓存: {event.stock_code} = {event.new_price}")
    
    def _trigger_indicator_calculation(self, event: StockPriceChanged):
        """触发技术指标计算"""
        # 这里可以触发技术指标的重新计算
        logger.debug(f"触发技术指标计算: {event.stock_code}")


class MarketOpenedHandler(DomainEventHandler):
    """市场开市事件处理器"""
    
    def __init__(self):
        super().__init__(MarketOpened)
    
    def _handle_event(self, event: MarketOpened):
        """处理市场开市事件"""
        logger.info(f"市场开市: {event.market_code} - {event.session.display_name}")
        
        # 初始化交易日数据
        self._initialize_trading_day(event)
        
        # 启动实时数据流
        self._start_real_time_data_stream(event)
        
        # 发送开市通知
        self._send_market_notification(event, "MARKET_OPENED")
    
    def _initialize_trading_day(self, event: MarketOpened):
        """初始化交易日数据"""
        logger.info(f"初始化交易日数据: {event.market_code}")
        # 这里可以初始化当日的交易数据结构
    
    def _start_real_time_data_stream(self, event: MarketOpened):
        """启动实时数据流"""
        logger.info(f"启动实时数据流: {event.market_code}")
        # 这里可以启动实时数据订阅
    
    def _send_market_notification(self, event: MarketOpened, notification_type: str):
        """发送市场通知"""
        logger.info(f"发送市场通知: {notification_type} - {event.market_code}")


class MarketClosedHandler(DomainEventHandler):
    """市场收市事件处理器"""
    
    def __init__(self):
        super().__init__(MarketClosed)
    
    def _handle_event(self, event: MarketClosed):
        """处理市场收市事件"""
        logger.info(f"市场收市: {event.market_code}")
        
        # 停止实时数据流
        self._stop_real_time_data_stream(event)
        
        # 执行日终处理
        self._execute_end_of_day_processing(event)
        
        # 发送收市通知
        self._send_market_notification(event, "MARKET_CLOSED")
    
    def _stop_real_time_data_stream(self, event: MarketClosed):
        """停止实时数据流"""
        logger.info(f"停止实时数据流: {event.market_code}")
    
    def _execute_end_of_day_processing(self, event: MarketClosed):
        """执行日终处理"""
        logger.info(f"执行日终处理: {event.market_code}")
        # 这里可以执行数据汇总、报表生成等
    
    def _send_market_notification(self, event: MarketClosed, notification_type: str):
        """发送市场通知"""
        logger.info(f"发送市场通知: {notification_type} - {event.market_code}")


class DividendAnnouncedHandler(DomainEventHandler):
    """分红公告事件处理器"""
    
    def __init__(self):
        super().__init__(DividendAnnounced)
    
    def _handle_event(self, event: DividendAnnounced):
        """处理分红公告事件"""
        logger.info(f"分红公告: {event.stock_code} "
                   f"每股分红 {event.dividend_per_share} "
                   f"除权日 {event.ex_dividend_date}")
        
        # 计算除权价格
        self._calculate_ex_dividend_price(event)
        
        # 更新股票基本信息
        self._update_stock_info(event)
        
        # 发送分红通知
        self._send_dividend_notification(event)
    
    def _calculate_ex_dividend_price(self, event: DividendAnnounced):
        """计算除权价格"""
        logger.debug(f"计算除权价格: {event.stock_code}")
        # 这里可以计算除权价格并更新相关数据
    
    def _update_stock_info(self, event: DividendAnnounced):
        """更新股票信息"""
        logger.debug(f"更新股票信息: {event.stock_code}")
    
    def _send_dividend_notification(self, event: DividendAnnounced):
        """发送分红通知"""
        logger.info(f"发送分红通知: {event.stock_code}")


class AsyncEventHandler(EventHandler):
    """异步事件处理器"""
    
    def __init__(self, event_type: Type[DomainEvent]):
        self._event_type = event_type
        self._processed_events: List[str] = []
        self._error_count = 0
    
    def can_handle(self, event: DomainEvent) -> bool:
        """判断是否可以处理该事件"""
        return isinstance(event, self._event_type)
    
    async def handle_async(self, event: DomainEvent):
        """异步处理事件"""
        if not self.can_handle(event):
            logger.warning(f"异步处理器 {self.handler_name} 无法处理事件 {event.event_type}")
            return
        
        try:
            logger.debug(f"异步处理器 {self.handler_name} 开始处理事件 {event.event_id}")
            
            await self._handle_event_async(event)
            
            self._processed_events.append(event.event_id)
            logger.debug(f"异步处理器 {self.handler_name} 成功处理事件 {event.event_id}")
            
        except Exception as e:
            self._error_count += 1
            logger.error(f"异步处理器 {self.handler_name} 处理事件失败: {event.event_id}, 错误: {e}")
            raise
    
    def handle(self, event: DomainEvent):
        """同步处理事件（创建异步任务）"""
        loop = asyncio.get_event_loop()
        if loop.is_running():
            asyncio.create_task(self.handle_async(event))
        else:
            loop.run_until_complete(self.handle_async(event))
    
    @abstractmethod
    async def _handle_event_async(self, event: DomainEvent):
        """具体的异步事件处理逻辑"""
        pass
    
    @property
    def handler_name(self) -> str:
        """处理器名称"""
        return f"Async{self.__class__.__name__}({self._event_type.__name__})"


class EventHandlerRegistry:
    """事件处理器注册表"""
    
    def __init__(self):
        self._handlers: Dict[Type[DomainEvent], List[EventHandler]] = {}
        self._global_handlers: List[EventHandler] = []
    
    def register_handler(self, event_type: Type[DomainEvent], handler: EventHandler):
        """注册事件处理器"""
        if event_type not in self._handlers:
            self._handlers[event_type] = []
        
        if handler not in self._handlers[event_type]:
            self._handlers[event_type].append(handler)
            logger.info(f"注册事件处理器: {event_type.__name__} -> {handler.handler_name}")
    
    def register_global_handler(self, handler: EventHandler):
        """注册全局事件处理器"""
        if handler not in self._global_handlers:
            self._global_handlers.append(handler)
            logger.info(f"注册全局事件处理器: {handler.handler_name}")
    
    def unregister_handler(self, event_type: Type[DomainEvent], handler: EventHandler):
        """取消注册事件处理器"""
        if event_type in self._handlers and handler in self._handlers[event_type]:
            self._handlers[event_type].remove(handler)
            logger.info(f"取消注册事件处理器: {event_type.__name__} -> {handler.handler_name}")
    
    def get_handlers(self, event_type: Type[DomainEvent]) -> List[EventHandler]:
        """获取事件处理器"""
        handlers = []
        
        # 特定类型的处理器
        handlers.extend(self._handlers.get(event_type, []))
        
        # 全局处理器
        for global_handler in self._global_handlers:
            if global_handler.can_handle(DomainEvent("", "", "", datetime.now(), 1, {})):
                handlers.append(global_handler)
        
        return handlers
    
    def get_all_handlers(self) -> Dict[str, List[str]]:
        """获取所有处理器信息"""
        result = {}
        
        for event_type, handlers in self._handlers.items():
            result[event_type.__name__] = [h.handler_name for h in handlers]
        
        if self._global_handlers:
            result['GLOBAL'] = [h.handler_name for h in self._global_handlers]
        
        return result


# 创建默认的事件处理器
def create_default_handlers() -> List[EventHandler]:
    """创建默认的事件处理器"""
    return [
        StockCreatedHandler(),
        StockPriceChangedHandler(),
        MarketOpenedHandler(),
        MarketClosedHandler(),
        DividendAnnouncedHandler()
    ]


# 全局事件处理器注册表
_global_handler_registry: Optional[EventHandlerRegistry] = None


def get_handler_registry() -> EventHandlerRegistry:
    """获取全局事件处理器注册表"""
    global _global_handler_registry
    if _global_handler_registry is None:
        _global_handler_registry = EventHandlerRegistry()
        
        # 注册默认处理器
        default_handlers = create_default_handlers()
        for handler in default_handlers:
            if hasattr(handler, '_event_type'):
                _global_handler_registry.register_handler(handler._event_type, handler)
    
    return _global_handler_registry
