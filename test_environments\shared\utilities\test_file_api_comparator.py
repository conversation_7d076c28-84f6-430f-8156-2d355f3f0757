#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试文件与API数据比较器

专门用于比较测试文件的最后一行对应的未复权收盘价与通过API获取的同一时间点的未复权收盘价

作者: AI Assistant
创建时间: 2025-07-29
"""

import sys
import os
from typing import Dict, Optional, Tuple
from decimal import Decimal

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from test_environments.shared.utilities.specific_minute_data_fetcher import SpecificMinuteDataFetcher


class TestFileApiComparator:
    """测试文件与API数据比较器"""
    
    def __init__(self):
        """初始化比较器"""
        self.data_fetcher = SpecificMinuteDataFetcher()
    
    def compare_last_record_close_price(self, test_file_path: str, stock_code: str,
                                      tolerance: float = 0.001, verbose: bool = True) -> Dict[str, any]:
        """
        比较测试文件最后一行的未复权收盘价与API获取的同一时间点的未复权收盘价
        
        Args:
            test_file_path: 测试文件路径
            stock_code: 股票代码（如'000617'）
            tolerance: 价格比较容差（默认0.001元）
            
        Returns:
            比较结果字典：
            {
                'is_equal': True/False,           # 是否相等
                'file_info': {                    # 文件信息
                    'path': '文件路径',
                    'last_time': '202507241447',
                    'last_close_price': 10.52
                },
                'api_info': {                     # API信息
                    'datetime': '202507241447',
                    'close_price': 10.52,
                    'found': True
                },
                'comparison': {                   # 比较结果
                    'price_diff': 0.000,
                    'tolerance': 0.001,
                    'within_tolerance': True
                },
                'success': True,                  # 是否成功执行
                'message': '比较成功'
            }
        """
        try:
            # 根据环境自动调整显示文本（只在verbose模式下显示）
            if verbose:
                from core.environment_manager import get_environment_manager
                env_manager = get_environment_manager()

                if env_manager.is_test_mode():
                    print(f"🔍 比较测试文件与API的未复权收盘价")
                else:
                    print(f"🔍 比较数据文件与API的未复权收盘价")

                print(f"   文件: {os.path.basename(test_file_path)}")
                print(f"   股票: {stock_code}")
            
            # 第一步：获取测试文件的最后一条记录
            file_info = self._extract_last_record_from_file(test_file_path)
            
            if not file_info['success']:
                return {
                    'is_equal': False,
                    'file_info': file_info,
                    'api_info': {},
                    'comparison': {},
                    'success': False,
                    'message': f"获取文件最后记录失败: {file_info['message']}"
                }
            
            last_time = file_info['last_time']
            file_close_price = file_info['last_close_price']
            
            if verbose:
                print(f"   📋 文件最后记录: 时间={last_time}, 未复权收盘价={file_close_price}")
            
            # 第二步：从API获取同一时间点的数据（抑制数据覆盖警告）
            api_data = self.data_fetcher.get_minute_data(stock_code, last_time, suppress_warnings=True)

            if not api_data:
                # 检查是否是数据源覆盖范围问题
                try:
                    # 使用交易日计算器进行准确判断
                    from utils.trading_days_calculator import count_trading_days_to_now

                    target_date_str = last_time[:8]  # YYYYMMDD
                    trading_days = count_trading_days_to_now(target_date_str)

                    if trading_days > 100:  # pytdx大约只提供最近100个交易日的数据
                        message = f"数据源限制：pytdx只提供最近约100个交易日的数据，从{target_date_str}到现在有{trading_days}个交易日，超出覆盖范围"
                    else:
                        message = f"数据源未获取到时间点 {last_time} 的数据（交易日数={trading_days}，在pytdx覆盖范围内，可能是非交易时间或数据缺失）"

                except Exception as e:
                    message = f"数据源未获取到时间点 {last_time} 的数据（交易日计算失败: {e}）"

                return {
                    'is_equal': False,
                    'file_info': file_info,
                    'api_info': {'found': False},
                    'comparison': {},
                    'success': False,
                    'message': message
                }
            
            api_close_price = api_data.get('close', 0)

            # 第三步：进行价格比较
            comparison_result = self._compare_prices(file_close_price, api_close_price, tolerance)

            # 构建完整结果
            result = {
                'is_equal': comparison_result['is_equal'],
                'file_info': file_info,
                'api_info': {
                    'datetime': last_time,
                    'close_price': api_close_price,
                    'found': True,
                    'full_data': api_data
                },
                'comparison': comparison_result,
                'success': True,
                'message': '比较完成'
            }

            # 使用结果通知器输出标准化结果
            if verbose:
                self._notify_consistency_result(test_file_path, stock_code, result)

            return result
            
        except Exception as e:
            return {
                'is_equal': False,
                'file_info': {},
                'api_info': {},
                'comparison': {},
                'success': False,
                'message': f"比较过程异常: {e}"
            }

    def _notify_consistency_result(self, file_path: str, stock_code: str, result: dict):
        """使用结果通知器输出价格一致性检查结果"""
        try:
            from utils.result_notifier import notify_price_consistency_check

            # 构建通知结果（修复数据结构映射）
            file_info = result.get('file_info', {})
            api_info = result.get('api_info', {})

            # 将file_info的字段映射到result_notifier期望的格式
            file_last_record = {
                'datetime': file_info.get('last_time', 'N/A'),
                'close_price': file_info.get('last_close_price', 0)
            }

            # 将api_info的字段映射到result_notifier期望的格式
            api_data = {
                'datetime': api_info.get('datetime', 'N/A'),
                'close_price': api_info.get('close_price', 0)
            }

            consistency_result = {
                'file_path': os.path.basename(file_path),
                'stock_code': stock_code,
                'file_last_record': file_last_record,
                'api_data': api_data,
                'is_consistent': result.get('is_equal', False),
                'price_difference': result.get('comparison', {}).get('price_diff', 0),
                'tolerance': result.get('comparison', {}).get('tolerance', 0.001),
                'conclusion': '具备增量下载条件，可继续后续步骤' if result.get('is_equal', False) else '不具备增量下载条件，需要重新获取完整数据'
            }

            notify_price_consistency_check(consistency_result)

        except Exception as e:
            # 不影响主流程，继续执行
            pass
    
    def _extract_last_record_from_file(self, file_path: str) -> Dict[str, any]:
        """
        从测试文件中提取最后一条记录
        
        Args:
            file_path: 文件路径
            
        Returns:
            文件信息字典
        """
        try:
            if not os.path.exists(file_path):
                return {
                    'success': False,
                    'message': f"文件不存在: {file_path}"
                }
            
            with open(file_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < 2:  # 至少需要表头和一条数据
                return {
                    'success': False,
                    'message': "文件内容不足，至少需要表头和一条数据"
                }
            
            # 获取表头和最后一行数据
            header_line = lines[0].strip()
            
            # 从后往前查找最后一条有效数据行
            last_data_line = None
            for line in reversed(lines[1:]):  # 跳过表头
                line = line.strip()
                if line and not line.startswith('#'):  # 跳过空行和注释行
                    last_data_line = line
                    break
            
            if not last_data_line:
                return {
                    'success': False,
                    'message': "未找到有效的数据行"
                }
            
            # 解析表头和数据
            headers = header_line.split('|')
            data_values = last_data_line.split('|')
            
            if len(headers) != len(data_values):
                return {
                    'success': False,
                    'message': f"数据列数不匹配: 表头{len(headers)}列，数据{len(data_values)}列"
                }
            
            # 构建记录字典
            record = {}
            for i, header in enumerate(headers):
                if i < len(data_values):
                    record[header] = data_values[i]
            
            # 提取关键信息
            time_field = record.get('时间', '')
            close_price_field = record.get('当日收盘价C', '0')  # 未复权收盘价
            
            if not time_field:
                return {
                    'success': False,
                    'message': "未找到时间字段"
                }
            
            try:
                close_price = float(close_price_field)
            except (ValueError, TypeError):
                return {
                    'success': False,
                    'message': f"收盘价格式错误: {close_price_field}"
                }
            
            return {
                'success': True,
                'path': file_path,
                'last_time': time_field,
                'last_close_price': close_price,
                'full_record': record,
                'total_lines': len(lines),
                'message': '成功提取最后记录'
            }
            
        except Exception as e:
            return {
                'success': False,
                'message': f"提取文件记录失败: {e}"
            }
    
    def _compare_prices(self, file_price: float, api_price: float, tolerance: float) -> Dict[str, any]:
        """
        比较两个价格
        
        Args:
            file_price: 文件中的价格
            api_price: API获取的价格
            tolerance: 容差
            
        Returns:
            比较结果字典
        """
        try:
            # 使用Decimal进行精确计算
            file_decimal = Decimal(str(file_price))
            api_decimal = Decimal(str(api_price))
            tolerance_decimal = Decimal(str(tolerance))
            
            price_diff = abs(file_decimal - api_decimal)
            within_tolerance = price_diff <= tolerance_decimal
            
            return {
                'file_price': float(file_decimal),
                'api_price': float(api_decimal),
                'price_diff': float(price_diff),
                'tolerance': tolerance,
                'within_tolerance': within_tolerance,
                'is_equal': within_tolerance,
                'comparison_method': 'decimal_precision'
            }
            
        except Exception as e:
            return {
                'file_price': file_price,
                'api_price': api_price,
                'price_diff': abs(file_price - api_price),
                'tolerance': tolerance,
                'within_tolerance': False,
                'is_equal': False,
                'comparison_method': 'float_fallback',
                'error': str(e)
            }
    
    def batch_compare_files(self, file_paths: list, stock_code: str, 
                           tolerance: float = 0.001) -> Dict[str, Dict]:
        """
        批量比较多个测试文件
        
        Args:
            file_paths: 文件路径列表
            stock_code: 股票代码
            tolerance: 容差
            
        Returns:
            批量比较结果字典
        """
        try:
            print(f"🔍 批量比较 {len(file_paths)} 个测试文件")
            
            results = {}
            success_count = 0
            equal_count = 0
            
            for file_path in file_paths:
                file_name = os.path.basename(file_path)
                print(f"\n📋 处理文件: {file_name}")
                
                result = self.compare_last_record_close_price(file_path, stock_code, tolerance)
                results[file_name] = result
                
                if result['success']:
                    success_count += 1
                    if result['is_equal']:
                        equal_count += 1
            
            # 生成汇总信息
            summary = {
                'total_files': len(file_paths),
                'success_count': success_count,
                'equal_count': equal_count,
                'success_rate': success_count / len(file_paths) if file_paths else 0,
                'equal_rate': equal_count / success_count if success_count > 0 else 0
            }
            
            print(f"\n📊 批量比较汇总:")
            print(f"   总文件数: {summary['total_files']}")
            print(f"   成功比较: {summary['success_count']}")
            print(f"   价格一致: {summary['equal_count']}")
            print(f"   成功率: {summary['success_rate']:.2%}")
            print(f"   一致率: {summary['equal_rate']:.2%}")
            
            return {
                'results': results,
                'summary': summary
            }
            
        except Exception as e:
            return {
                'results': {},
                'summary': {},
                'error': f"批量比较失败: {e}"
            }


def compare_test_file_with_api(test_file_path: str, stock_code: str, 
                              tolerance: float = 0.001) -> bool:
    """
    便捷函数：比较测试文件与API的未复权收盘价
    
    Args:
        test_file_path: 测试文件路径
        stock_code: 股票代码
        tolerance: 容差
        
    Returns:
        是否相等
        
    Example:
        >>> is_equal = compare_test_file_with_api(
        ...     'test_environments/minute_data_tests/input_data/test_1min_0_000617.txt',
        ...     '000617'
        ... )
        >>> print(f"价格是否一致: {is_equal}")
    """
    comparator = TestFileApiComparator()
    result = comparator.compare_last_record_close_price(test_file_path, stock_code, tolerance)
    return result.get('is_equal', False)


def get_comparison_details(test_file_path: str, stock_code: str, 
                          tolerance: float = 0.001) -> Dict:
    """
    便捷函数：获取详细的比较结果
    
    Args:
        test_file_path: 测试文件路径
        stock_code: 股票代码
        tolerance: 容差
        
    Returns:
        详细比较结果
        
    Example:
        >>> details = get_comparison_details(
        ...     'test_environments/minute_data_tests/input_data/test_1min_0_000617.txt',
        ...     '000617'
        ... )
        >>> if details['success']:
        ...     print(f"文件价格: {details['file_info']['last_close_price']}")
        ...     print(f"API价格: {details['api_info']['close_price']}")
        ...     print(f"价格差异: {details['comparison']['price_diff']}")
    """
    comparator = TestFileApiComparator()
    return comparator.compare_last_record_close_price(test_file_path, stock_code, tolerance)


if __name__ == '__main__':
    # 测试功能
    print("🧪 测试文件与API数据比较器测试")
    print("=" * 60)
    
    # 创建比较器
    comparator = TestFileApiComparator()
    
    # 测试文件路径（需要根据实际情况调整）
    test_file = "test_environments/minute_data_tests/input_data/test_1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt"
    stock_code = "000617"
    
    if os.path.exists(test_file):
        print(f"📊 测试比较: {os.path.basename(test_file)}")
        
        result = comparator.compare_last_record_close_price(test_file, stock_code)
        
        if result['success']:
            print(f"✅ 比较成功:")
            print(f"   文件最后时间: {result['file_info']['last_time']}")
            print(f"   文件收盘价: {result['file_info']['last_close_price']}")
            print(f"   API收盘价: {result['api_info']['close_price']}")
            print(f"   价格差异: {result['comparison']['price_diff']}")
            print(f"   是否一致: {'是' if result['is_equal'] else '否'}")
        else:
            print(f"❌ 比较失败: {result['message']}")
    else:
        print(f"❌ 测试文件不存在: {test_file}")
        print(f"💡 请确保测试环境中有相应的测试文件")
