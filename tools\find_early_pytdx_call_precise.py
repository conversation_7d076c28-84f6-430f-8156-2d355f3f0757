#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确定位提前pytdx调用的工具
专门找到在TaskManager开始后、五步流程开始前的pytdx调用
"""

import os
import sys
import time
import traceback

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 全局状态跟踪
taskmanager_started = False
workflow_started = False
early_calls = []

def install_precise_tracking():
    """安装精确的调用追踪"""
    global taskmanager_started, workflow_started, early_calls
    
    try:
        # 1. 追踪TaskManager开始
        from src.mythquant.core.task_manager import TaskManager
        original_execute = TaskManager._execute_minute_task
        
        def tracked_execute(self, task, target_stocks):
            global taskmanager_started
            taskmanager_started = True
            print(f"\n=== TASKMANAGER STARTED ===")
            print(f"Time: {time.time()}")
            
            result = original_execute(self, task, target_stocks)
            
            print(f"=== TASKMANAGER ENDED ===")
            return result
        
        TaskManager._execute_minute_task = tracked_execute
        
        # 2. 追踪五步流程开始
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        original_five_step = StructuredInternetMinuteDownloader._execute_five_step_process
        
        def tracked_five_step(self, stock_code, start_date, end_date, frequency, original_frequency):
            global workflow_started
            workflow_started = True
            print(f"\n=== WORKFLOW STARTED ===")
            print(f"Time: {time.time()}")
            
            return original_five_step(self, stock_code, start_date, end_date, frequency, original_frequency)
        
        StructuredInternetMinuteDownloader._execute_five_step_process = tracked_five_step
        
        # 3. 追踪所有可能的pytdx入口点
        pytdx_classes = [
            ('utils.pytdx_downloader', 'PytdxDownloader'),
            ('test_environments.shared.utilities.specific_minute_data_fetcher', 'SpecificMinuteDataFetcher'),
        ]
        
        for module_name, class_name in pytdx_classes:
            try:
                module = __import__(module_name, fromlist=[class_name])
                cls = getattr(module, class_name)
                
                # 追踪类的实例化
                original_init = cls.__init__
                
                def make_tracked_init(original_class_name):
                    def tracked_init(self, *args, **kwargs):
                        global taskmanager_started, workflow_started, early_calls
                        current_time = time.time()
                        
                        print(f"\n*** {original_class_name} INSTANTIATION ***")
                        print(f"Time: {current_time}")
                        print(f"TaskManager started: {taskmanager_started}")
                        print(f"Workflow started: {workflow_started}")
                        
                        # 检查是否是提前调用
                        if taskmanager_started and not workflow_started:
                            print(f"!!! EARLY INSTANTIATION DETECTED !!!")
                            stack = traceback.format_stack()
                            early_calls.append({
                                'type': 'instantiation',
                                'class': original_class_name,
                                'time': current_time,
                                'stack': stack
                            })
                            
                            print("Call stack:")
                            for i, frame in enumerate(stack[-10:], 1):
                                print(f"  {i:2d}. {frame.strip()}")
                        
                        return original_init(self, *args, **kwargs)
                    return tracked_init
                
                cls.__init__ = make_tracked_init(class_name)
                
            except ImportError as e:
                print(f"Could not track {module_name}.{class_name}: {e}")
        
        # 4. 追踪pytdx的具体方法调用
        from utils.pytdx_downloader import PytdxDownloader
        
        # 追踪download_minute_data方法
        original_download = PytdxDownloader.download_minute_data
        
        def tracked_download(self, stock_code, start_date, end_date, frequency='1min', suppress_warnings=False):
            global taskmanager_started, workflow_started, early_calls
            current_time = time.time()
            
            print(f"\n*** PYTDX DOWNLOAD_MINUTE_DATA CALLED ***")
            print(f"Time: {current_time}")
            print(f"Stock: {stock_code}, Range: {start_date}-{end_date}")
            print(f"TaskManager started: {taskmanager_started}")
            print(f"Workflow started: {workflow_started}")
            
            # 检查是否是提前调用
            if taskmanager_started and not workflow_started:
                print(f"!!! EARLY DOWNLOAD CALL DETECTED !!!")
                stack = traceback.format_stack()
                early_calls.append({
                    'type': 'download_call',
                    'method': 'download_minute_data',
                    'stock_code': stock_code,
                    'date_range': f"{start_date}-{end_date}",
                    'time': current_time,
                    'stack': stack
                })
                
                print("Call stack:")
                for i, frame in enumerate(stack[-10:], 1):
                    print(f"  {i:2d}. {frame.strip()}")
            
            return original_download(self, stock_code, start_date, end_date, frequency, suppress_warnings)
        
        PytdxDownloader.download_minute_data = tracked_download
        
        # 5. 追踪其他可能的pytdx方法
        pytdx_methods = ['connect', 'get_security_bars', 'disconnect']
        
        for method_name in pytdx_methods:
            if hasattr(PytdxDownloader, method_name):
                original_method = getattr(PytdxDownloader, method_name)
                
                def make_tracked_method(method_name, original_method):
                    def tracked_method(self, *args, **kwargs):
                        global taskmanager_started, workflow_started, early_calls
                        current_time = time.time()
                        
                        print(f"\n*** PYTDX {method_name.upper()} CALLED ***")
                        print(f"Time: {current_time}")
                        print(f"TaskManager started: {taskmanager_started}")
                        print(f"Workflow started: {workflow_started}")
                        
                        if taskmanager_started and not workflow_started:
                            print(f"!!! EARLY {method_name.upper()} CALL DETECTED !!!")
                            stack = traceback.format_stack()
                            early_calls.append({
                                'type': 'method_call',
                                'method': method_name,
                                'time': current_time,
                                'stack': stack
                            })
                        
                        return original_method(self, *args, **kwargs)
                    return tracked_method
                
                setattr(PytdxDownloader, method_name, make_tracked_method(method_name, original_method))
        
        print("Precise pytdx call tracking installed")
        return True
        
    except Exception as e:
        print(f"Tracking installation failed: {e}")
        traceback.print_exc()
        return False

def run_tracked_test():
    """运行带追踪的测试"""
    print("\nRunning test with precise pytdx call tracking...")
    
    try:
        from main import main
        result = main()
        print(f"Test completed with result: {result}")
        return True
    except Exception as e:
        print(f"Test failed: {e}")
        traceback.print_exc()
        return False

def analyze_early_calls():
    """分析提前调用"""
    global early_calls
    
    print(f"\n=== EARLY PYTDX CALLS ANALYSIS ===")
    print(f"Total early calls detected: {len(early_calls)}")
    
    if not early_calls:
        print("No early pytdx calls detected")
        return
    
    for i, call in enumerate(early_calls, 1):
        print(f"\nEarly Call #{i}:")
        print(f"  Type: {call['type']}")
        print(f"  Time: {call['time']}")
        
        if 'class' in call:
            print(f"  Class: {call['class']}")
        if 'method' in call:
            print(f"  Method: {call['method']}")
        if 'stock_code' in call:
            print(f"  Stock: {call['stock_code']}")
        if 'date_range' in call:
            print(f"  Range: {call['date_range']}")
        
        print(f"  Key stack frames:")
        for frame in call['stack']:
            if any(keyword in frame for keyword in [
                'task_manager', 'TaskManager', 'execute_minute',
                'structured_internet', 'StructuredInternetMinuteDownloader'
            ]):
                print(f"    >>> {frame.strip()}")
        
        # 找到最可能的调用源头
        print(f"  Likely source:")
        for frame in call['stack']:
            if 'task_manager.py' in frame and 'execute_minute' in frame:
                print(f"    SOURCE: {frame.strip()}")
                break

def main():
    """主函数"""
    print("Precise Early Pytdx Call Detector")
    print("=" * 60)
    
    if not install_precise_tracking():
        return 1
    
    success = run_tracked_test()
    analyze_early_calls()
    
    print(f"\nDetection completed: {'SUCCESS' if success else 'FAILED'}")
    print(f"Early calls found: {len(early_calls)}")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
