#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
主买主卖计算模块

基于OHLCV数据，通过分析价格运行轨迹和成交量分布，计算主买主卖指标
"""

import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, Dict, List, Tuple
import logging

# 导入新架构的配置系统
from src.mythquant.config.manager import ConfigManager

logger = logging.getLogger(__name__)


class BuySellCalculator:
    """主买主卖计算器 - 基于价格轨迹分析的主买主卖计算"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化主买主卖计算器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.precision = Decimal('0.001')  # 价格精度：3位小数
        
        # 算法参数
        self.volume_weight = 0.3  # 成交量权重
        self.price_weight = 0.7   # 价格权重
        
        # 调试模式
        self.debug_mode = config_manager.is_debug_enabled()
    
    def calculate_buy_sell_metrics(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算主买主卖指标
        
        Args:
            df: 包含OHLCV数据的DataFrame
            
        Returns:
            包含主买主卖指标的DataFrame
        """
        if df is None or df.empty:
            logger.warning("输入数据为空，无法计算主买主卖")
            return pd.DataFrame()
        
        try:
            # 复制数据以避免修改原始数据
            result_df = df.copy()
            
            # 数据验证
            if not self._validate_input_data(result_df):
                logger.error("输入数据验证失败")
                return pd.DataFrame()
            
            # 计算价格运行轨迹
            result_df = self._calculate_price_trajectory(result_df)
            
            # 分析成交量分布
            result_df = self._analyze_volume_distribution(result_df)
            
            # 计算主买主卖
            result_df = self._calculate_main_buy_sell(result_df)
            
            # 应用精度控制
            result_df = self._apply_precision_control(result_df)
            
            logger.info(f"主买主卖计算完成，处理了{len(result_df)}条记录")
            return result_df
            
        except Exception as e:
            logger.error(f"主买主卖计算失败: {e}")
            return pd.DataFrame()
    
    def _validate_input_data(self, df: pd.DataFrame) -> bool:
        """
        验证输入数据的有效性
        
        Args:
            df: 输入数据
            
        Returns:
            是否有效
        """
        try:
            required_columns = ['open', 'high', 'low', 'close', 'volume']
            missing_columns = [col for col in required_columns if col not in df.columns]
            
            if missing_columns:
                logger.error(f"缺少必要列: {missing_columns}")
                return False
            
            # 检查数值有效性
            for col in required_columns:
                if df[col].isnull().any():
                    logger.error(f"列{col}包含空值")
                    return False
                
                if col != 'volume' and (df[col] <= 0).any():
                    logger.error(f"列{col}包含非正值")
                    return False
            
            return True
            
        except Exception as e:
            logger.error(f"数据验证异常: {e}")
            return False
    
    def _calculate_price_trajectory(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算价格运行轨迹
        
        Args:
            df: 输入数据
            
        Returns:
            包含价格轨迹分析的DataFrame
        """
        try:
            # 计算价格区间
            df['upper_range'] = df['high'] - np.maximum(df['open'], df['close'])  # 上影线
            df['lower_range'] = np.minimum(df['open'], df['close']) - df['low']   # 下影线
            df['body_range'] = np.abs(df['close'] - df['open'])                   # 实体
            
            # 计算价格运行方向
            df['is_bullish'] = df['close'] > df['open']  # 阳线
            df['is_bearish'] = df['close'] < df['open']  # 阴线
            df['is_doji'] = np.abs(df['close'] - df['open']) <= (df['open'] * 0.001)  # 十字星
            
            # 计算价格强度
            df['price_strength'] = np.abs(df['close'] - df['open']) / df['open']
            
            # 计算振幅
            df['amplitude'] = (df['high'] - df['low']) / df['open']
            
            return df
            
        except Exception as e:
            logger.error(f"计算价格轨迹失败: {e}")
            return df
    
    def _analyze_volume_distribution(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        分析成交量分布
        
        Args:
            df: 包含价格轨迹的DataFrame
            
        Returns:
            包含成交量分析的DataFrame
        """
        try:
            # 计算成交量相对强度
            df['volume_ma5'] = df['volume'].rolling(window=5, min_periods=1).mean()
            df['volume_ratio'] = df['volume'] / df['volume_ma5']
            
            # 根据价格区间分配成交量
            total_range = df['high'] - df['low']
            
            # 避免除零错误
            total_range = np.where(total_range == 0, 0.001, total_range)
            
            # 上影线成交量（假设为主卖）
            df['upper_volume'] = df['volume'] * (df['upper_range'] / total_range)
            
            # 下影线成交量（假设为主买）
            df['lower_volume'] = df['volume'] * (df['lower_range'] / total_range)
            
            # 实体成交量（根据方向分配）
            df['body_volume'] = df['volume'] * (df['body_range'] / total_range)
            
            return df
            
        except Exception as e:
            logger.error(f"分析成交量分布失败: {e}")
            return df
    
    def _calculate_main_buy_sell(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算主买主卖
        
        Args:
            df: 包含价格轨迹和成交量分析的DataFrame
            
        Returns:
            包含主买主卖的DataFrame
        """
        try:
            # 初始化主买主卖
            df['main_buy'] = 0.0
            df['main_sell'] = 0.0
            
            for idx, row in df.iterrows():
                # 获取基础数据
                open_price = row['open']
                high_price = row['high']
                low_price = row['low']
                close_price = row['close']
                volume = row['volume']
                
                # 计算价格区间
                upper_range = row['upper_range']
                lower_range = row['lower_range']
                body_range = row['body_range']
                total_range = high_price - low_price
                
                if total_range == 0:
                    # 一字板情况
                    if volume > 0:
                        df.at[idx, 'main_buy'] = volume * 0.6  # 假设60%为主买
                        df.at[idx, 'main_sell'] = volume * 0.4  # 假设40%为主卖
                    continue
                
                # 基于价格轨迹的主买主卖计算
                if row['is_bullish']:
                    # 阳线：上涨过程产生主买，回调产生主卖
                    main_buy_price = body_range + lower_range * 0.8  # 实体 + 大部分下影线
                    main_sell_price = upper_range * 0.8  # 大部分上影线
                elif row['is_bearish']:
                    # 阴线：下跌过程产生主卖，反弹产生主买
                    main_buy_price = upper_range * 0.8  # 大部分上影线
                    main_sell_price = body_range + lower_range * 0.8  # 实体 + 大部分下影线
                else:
                    # 十字星：主买主卖相对平衡
                    main_buy_price = (upper_range + lower_range) * 0.5
                    main_sell_price = (upper_range + lower_range) * 0.5
                
                # 应用成交量权重调整
                volume_factor = min(row.get('volume_ratio', 1.0), 2.0)  # 限制最大2倍
                
                df.at[idx, 'main_buy'] = main_buy_price * volume_factor
                df.at[idx, 'main_sell'] = main_sell_price * volume_factor
            
            return df
            
        except Exception as e:
            logger.error(f"计算主买主卖失败: {e}")
            return df
    
    def _apply_precision_control(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        应用精度控制
        
        Args:
            df: 输入数据
            
        Returns:
            精度控制后的DataFrame
        """
        try:
            # 对主买主卖列应用精度控制
            for col in ['main_buy', 'main_sell']:
                if col in df.columns:
                    df[col] = df[col].apply(lambda x: self._apply_decimal_precision(x))
            
            return df
            
        except Exception as e:
            logger.error(f"应用精度控制失败: {e}")
            return df
    
    def _apply_decimal_precision(self, value: float) -> float:
        """
        应用Decimal精度控制
        
        Args:
            value: 输入值
            
        Returns:
            精度控制后的值
        """
        try:
            decimal_value = Decimal(str(value))
            return float(decimal_value.quantize(self.precision, rounding=ROUND_HALF_UP))
        except Exception:
            return float(value)
    
    def calculate_buy_sell_ratio(self, df: pd.DataFrame) -> pd.DataFrame:
        """
        计算主买主卖比率
        
        Args:
            df: 包含主买主卖的DataFrame
            
        Returns:
            包含比率的DataFrame
        """
        try:
            # 计算主买主卖比率
            total_activity = df['main_buy'] + df['main_sell']
            
            # 避免除零错误
            df['buy_ratio'] = np.where(total_activity > 0, 
                                     df['main_buy'] / total_activity, 0.5)
            df['sell_ratio'] = np.where(total_activity > 0, 
                                      df['main_sell'] / total_activity, 0.5)
            
            # 计算净买入
            df['net_buy'] = df['main_buy'] - df['main_sell']
            
            return df
            
        except Exception as e:
            logger.error(f"计算主买主卖比率失败: {e}")
            return df
    
    def get_buy_sell_summary(self, df: pd.DataFrame) -> Dict[str, float]:
        """
        获取主买主卖汇总统计
        
        Args:
            df: 包含主买主卖的DataFrame
            
        Returns:
            汇总统计字典
        """
        try:
            if df.empty or 'main_buy' not in df.columns or 'main_sell' not in df.columns:
                return {}
            
            summary = {
                'total_main_buy': df['main_buy'].sum(),
                'total_main_sell': df['main_sell'].sum(),
                'avg_main_buy': df['main_buy'].mean(),
                'avg_main_sell': df['main_sell'].mean(),
                'max_main_buy': df['main_buy'].max(),
                'max_main_sell': df['main_sell'].max(),
                'buy_sell_ratio': df['main_buy'].sum() / (df['main_sell'].sum() + 1e-10),
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取主买主卖汇总失败: {e}")
            return {}


# 导出
__all__ = ['BuySellCalculator']
