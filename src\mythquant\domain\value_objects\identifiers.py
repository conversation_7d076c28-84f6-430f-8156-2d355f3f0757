#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
标识符值对象

用于唯一标识领域对象的值对象
"""

import re
from dataclasses import dataclass
from typing import Optional
from ..exceptions import InvalidStockCodeError, InvalidMarketCodeError
from ...shared.patterns.architectural_patterns import ValueObject


@dataclass(frozen=True)
class StockCode(ValueObject):
    """股票代码值对象"""
    
    value: str
    
    def __post_init__(self):
        if not self._is_valid_stock_code(self.value):
            raise InvalidStockCodeError(f"无效的股票代码: {self.value}")
    
    @staticmethod
    def _is_valid_stock_code(code: str) -> bool:
        """验证股票代码格式"""
        if not code or not isinstance(code, str):
            return False
        
        # 中国A股代码格式：6位数字
        if re.match(r'^\d{6}$', code):
            return True
        
        # 美股代码格式：1-5位字母
        if re.match(r'^[A-Z]{1,5}$', code.upper()):
            return True
        
        return False
    
    @classmethod
    def from_string(cls, code_str: str) -> 'StockCode':
        """从字符串创建股票代码"""
        # 标准化处理
        normalized_code = code_str.strip().upper()
        
        # 如果是数字，补齐到6位
        if normalized_code.isdigit():
            normalized_code = normalized_code.zfill(6)
        
        return cls(normalized_code)
    
    @property
    def market_type(self) -> str:
        """获取市场类型"""
        if self.value.isdigit():
            if self.value.startswith('0'):
                return 'SZ'  # 深圳
            elif self.value.startswith('6'):
                return 'SH'  # 上海
            elif self.value.startswith('3'):
                return 'SZ'  # 创业板
            else:
                return 'UNKNOWN'
        else:
            return 'US'  # 美股
    
    @property
    def is_a_share(self) -> bool:
        """是否为A股"""
        return self.value.isdigit() and len(self.value) == 6
    
    @property
    def is_us_stock(self) -> bool:
        """是否为美股"""
        return self.value.isalpha()
    
    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class MarketCode(ValueObject):
    """市场代码值对象"""
    
    value: str
    
    def __post_init__(self):
        if not self._is_valid_market_code(self.value):
            raise InvalidMarketCodeError(f"无效的市场代码: {self.value}")
    
    @staticmethod
    def _is_valid_market_code(code: str) -> bool:
        """验证市场代码格式"""
        valid_markets = {'SH', 'SZ', 'NASDAQ', 'NYSE', 'AMEX', 'HK'}
        return code and code.upper() in valid_markets
    
    @classmethod
    def from_string(cls, code_str: str) -> 'MarketCode':
        """从字符串创建市场代码"""
        return cls(code_str.strip().upper())
    
    @property
    def is_chinese_market(self) -> bool:
        """是否为中国市场"""
        return self.value in {'SH', 'SZ'}
    
    @property
    def is_us_market(self) -> bool:
        """是否为美国市场"""
        return self.value in {'NASDAQ', 'NYSE', 'AMEX'}
    
    @property
    def full_name(self) -> str:
        """市场全名"""
        names = {
            'SH': '上海证券交易所',
            'SZ': '深圳证券交易所', 
            'NASDAQ': '纳斯达克',
            'NYSE': '纽约证券交易所',
            'AMEX': '美国证券交易所',
            'HK': '香港交易所'
        }
        return names.get(self.value, self.value)
    
    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class PortfolioId(ValueObject):
    """投资组合ID值对象"""
    
    value: str
    
    def __post_init__(self):
        if not self._is_valid_portfolio_id(self.value):
            raise ValueError(f"无效的投资组合ID: {self.value}")
    
    @staticmethod
    def _is_valid_portfolio_id(portfolio_id: str) -> bool:
        """验证投资组合ID格式"""
        if not portfolio_id or not isinstance(portfolio_id, str):
            return False
        
        # 格式：字母开头，后跟字母数字下划线，长度3-50
        return re.match(r'^[A-Za-z][A-Za-z0-9_]{2,49}$', portfolio_id) is not None
    
    @classmethod
    def generate(cls, prefix: str = "PORTFOLIO") -> 'PortfolioId':
        """生成新的投资组合ID"""
        import uuid
        suffix = str(uuid.uuid4()).replace('-', '')[:8].upper()
        return cls(f"{prefix}_{suffix}")
    
    @classmethod
    def from_string(cls, id_str: str) -> 'PortfolioId':
        """从字符串创建投资组合ID"""
        return cls(id_str.strip())
    
    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class UserId(ValueObject):
    """用户ID值对象"""
    
    value: str
    
    def __post_init__(self):
        if not self._is_valid_user_id(self.value):
            raise ValueError(f"无效的用户ID: {self.value}")
    
    @staticmethod
    def _is_valid_user_id(user_id: str) -> bool:
        """验证用户ID格式"""
        if not user_id or not isinstance(user_id, str):
            return False
        
        # 格式：字母数字下划线，长度3-30
        return re.match(r'^[A-Za-z0-9_]{3,30}$', user_id) is not None
    
    @classmethod
    def from_string(cls, id_str: str) -> 'UserId':
        """从字符串创建用户ID"""
        return cls(id_str.strip())
    
    def __str__(self) -> str:
        return self.value


@dataclass(frozen=True)
class TransactionId(ValueObject):
    """交易ID值对象"""
    
    value: str
    
    def __post_init__(self):
        if not self._is_valid_transaction_id(self.value):
            raise ValueError(f"无效的交易ID: {self.value}")
    
    @staticmethod
    def _is_valid_transaction_id(transaction_id: str) -> bool:
        """验证交易ID格式"""
        if not transaction_id or not isinstance(transaction_id, str):
            return False
        
        # UUID格式或自定义格式
        uuid_pattern = r'^[0-9a-fA-F]{8}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{4}-[0-9a-fA-F]{12}$'
        custom_pattern = r'^TXN_[A-Z0-9]{8,16}$'
        
        return (re.match(uuid_pattern, transaction_id) is not None or
                re.match(custom_pattern, transaction_id) is not None)
    
    @classmethod
    def generate(cls) -> 'TransactionId':
        """生成新的交易ID"""
        import uuid
        return cls(str(uuid.uuid4()))
    
    @classmethod
    def generate_custom(cls, prefix: str = "TXN") -> 'TransactionId':
        """生成自定义格式的交易ID"""
        import uuid
        suffix = str(uuid.uuid4()).replace('-', '')[:12].upper()
        return cls(f"{prefix}_{suffix}")
    
    @classmethod
    def from_string(cls, id_str: str) -> 'TransactionId':
        """从字符串创建交易ID"""
        return cls(id_str.strip())
    
    def __str__(self) -> str:
        return self.value


# 工厂函数
def create_stock_code(code: str) -> StockCode:
    """创建股票代码的便捷函数"""
    return StockCode.from_string(code)


def create_market_code(code: str) -> MarketCode:
    """创建市场代码的便捷函数"""
    return MarketCode.from_string(code)


def create_portfolio_id(id_str: Optional[str] = None) -> PortfolioId:
    """创建投资组合ID的便捷函数"""
    if id_str:
        return PortfolioId.from_string(id_str)
    else:
        return PortfolioId.generate()


# 验证函数
def is_valid_stock_code(code: str) -> bool:
    """验证股票代码是否有效"""
    try:
        StockCode.from_string(code)
        return True
    except InvalidStockCodeError:
        return False


def is_valid_market_code(code: str) -> bool:
    """验证市场代码是否有效"""
    try:
        MarketCode.from_string(code)
        return True
    except InvalidMarketCodeError:
        return False
