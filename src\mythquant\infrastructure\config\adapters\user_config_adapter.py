#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
用户配置适配器

将user_config.py适配到DDD架构中
"""

import sys
import os
from typing import Dict, Any, List, Optional
from pathlib import Path


class UserConfigAdapter:
    """用户配置适配器"""
    
    def __init__(self):
        """初始化适配器"""
        self._config_cache = None
        self._load_user_config()
    
    def _load_user_config(self) -> None:
        """加载用户配置文件"""
        try:
            # 确保项目根目录在Python路径中
            project_root = Path(__file__).parent.parent.parent.parent.parent
            if str(project_root) not in sys.path:
                sys.path.insert(0, str(project_root))
            
            # 导入用户配置
            import user_config
            
            # 缓存配置
            self._config_cache = {
                'debug': getattr(user_config, 'debug', False),
                'verbose_mode': getattr(user_config, 'verbose_mode', {}),
                'smart_file_selector': getattr(user_config, 'smart_file_selector', {}),
                'tdx': getattr(user_config, 'tdx', {}),
                'output_config': getattr(user_config, 'output_config', {}),
                'data_processing': getattr(user_config, 'data_processing', {}),
                'intelligent_features': getattr(user_config, 'intelligent_features', {}),
                'error_handling': getattr(user_config, 'error_handling', {}),
                'user_interface': getattr(user_config, 'user_interface', {}),
                'internet_data_sources': getattr(user_config, 'internet_data_sources', {}),
                'precision_config': getattr(user_config, 'precision_config', {})
            }
            
        except ImportError as e:
            raise RuntimeError(f"无法导入用户配置文件: {str(e)}")
        except Exception as e:
            raise RuntimeError(f"加载用户配置失败: {str(e)}")
    
    def get_raw_config(self) -> Dict[str, Any]:
        """获取原始配置数据"""
        if self._config_cache is None:
            self._load_user_config()
        return self._config_cache.copy()
    
    def get_trading_config_data(self) -> Dict[str, Any]:
        """获取交易配置数据"""
        config = self.get_raw_config()
        tdx_config = config.get('tdx', {})
        
        return {
            'tdx_path': tdx_config.get('tdx_path', ''),
            'tdx_ip': tdx_config.get('pytdx_ip', '*************'),
            'tdx_port': tdx_config.get('pytdx_port', 7709),
            'tdx_test_path': tdx_config.get('tdx_test', ''),
            'tdx_min_path': tdx_config.get('tdx_min_path', ''),
            'tdx_day_path': tdx_config.get('tdx_day_path', ''),
            'output_path': config.get('output_config', {}).get('base_output_path', ''),
            'target_stocks_file': tdx_config.get('目标股票代码'),
            'gbbq_path': tdx_config.get('csv_gbbq', 'T0002/hq_cache/gbbq'),
            'pytdx_auto_detect': tdx_config.get('pytdx_auto_detect', False),
            'pytdx_show_top5': tdx_config.get('pytdx_show_top5', True),
            'pytdx_smart_detect': tdx_config.get('pytdx_smart_detect', True),
            'pytdx_blacklist_enabled': tdx_config.get('pytdx_blacklist_enabled', True),
            'pytdx_blacklist_timeout': tdx_config.get('pytdx_blacklist_timeout', 7200),
            'kline_limits': tdx_config.get('pytdx_kline_limits', {}),
            'data_buffer_factor': tdx_config.get('pytdx_data_buffer_factor', 1.0)
        }
    
    def get_data_source_config_data(self) -> Dict[str, Any]:
        """获取数据源配置数据"""
        config = self.get_raw_config()
        tdx_config = config.get('tdx', {})
        internet_config = config.get('internet_data_sources', {})
        
        return {
            'tdx_path': tdx_config.get('tdx_path', ''),
            'tdx_ip': tdx_config.get('pytdx_ip', '*************'),
            'tdx_port': tdx_config.get('pytdx_port', 7709),
            'network_config': internet_config.get('network', {}),
            'error_handling': config.get('error_handling', {}),
            'monitoring': internet_config.get('monitoring', {}),
            'internet_sources': {
                'enabled': True,
                'primary_source': 'default',
                'fallback_sources': [],
                'cache_enabled': True,
                'cache_ttl': 3600
            }
        }
    
    def get_processing_config_data(self) -> Dict[str, Any]:
        """获取数据处理配置数据"""
        config = self.get_raw_config()
        
        return {
            'smart_file_selector': config.get('smart_file_selector', {}),
            'data_processing': config.get('data_processing', {}),
            'intelligent_features': config.get('intelligent_features', {}),
            'user_interface': config.get('user_interface', {}),
            'verbose_mode': config.get('verbose_mode', {}),
            'debug_enabled': config.get('debug', False)
        }
    
    def get_task_configs_data(self) -> List[Dict[str, Any]]:
        """获取任务配置数据"""
        try:
            # 读取user_config.py中的实际配置
            config = self.get_raw_config()

            # 直接导入user_config模块获取time_ranges
            import user_config
            time_ranges = getattr(user_config, 'time_ranges', {})

            task_configs = []

            # 分钟级数据任务配置
            minute_config = time_ranges.get('internet_minute', {})
            if minute_config.get('enabled', False):
                # 使用配置文件中的实际日期
                start_date = minute_config.get('start_date', '20250101')
                end_date = minute_config.get('end_date', '20250807')

                task_configs.append({
                    'name': '分钟级数据生成',
                    'data_type': 'minute',  # 将根据配置决定是否使用structured flow
                    'enabled': True,
                    'start_time': start_date,  # 使用配置文件中的实际日期
                    'end_time': end_date,      # 使用配置文件中的实际日期
                    'use_multithread': True,
                    'max_workers': 4,
                    'frequency': minute_config.get('frequency', '1min'),
                    'use_structured_flow': True  # 启用结构化四步流程
                })

            # 如果没有启用的任务，返回默认配置（使用当前时间）
            if not task_configs:
                from datetime import datetime, timedelta
                today = datetime.now()
                yesterday = today - timedelta(days=1)

                task_configs = [
                    {
                        'name': '分钟级数据生成',
                        'data_type': 'minute',
                        'enabled': True,
                        'start_time': yesterday.strftime('%Y%m%d'),
                        'end_time': today.strftime('%Y%m%d'),
                        'use_multithread': True,
                        'max_workers': 4,
                        'frequency': '1min',
                        'use_structured_flow': True
                    }
                ]

            return task_configs

        except Exception as e:
            # 出错时返回默认配置
            from datetime import datetime, timedelta
            today = datetime.now()
            yesterday = today - timedelta(days=1)

            return [
                {
                    'name': '分钟级数据生成',
                    'data_type': 'minute',
                    'enabled': True,
                    'start_time': yesterday.strftime('%Y%m%d'),
                    'end_time': today.strftime('%Y%m%d'),
                    'use_multithread': True,
                    'max_workers': 4,
                    'frequency': '1min',
                    'use_structured_flow': True
                }
            ]
    
    def reload_config(self) -> None:
        """重新加载配置"""
        self._config_cache = None
        self._load_user_config()
    
    def is_config_file_exists(self) -> bool:
        """检查配置文件是否存在"""
        try:
            project_root = Path(__file__).parent.parent.parent.parent.parent
            config_file = project_root / 'user_config.py'
            return config_file.exists()
        except Exception:
            return False
    
    def get_config_file_path(self) -> str:
        """获取配置文件路径"""
        project_root = Path(__file__).parent.parent.parent.parent.parent
        config_file = project_root / 'user_config.py'
        return str(config_file)
    
    def validate_config_structure(self) -> Dict[str, Any]:
        """验证配置结构"""
        config = self.get_raw_config()
        
        validation_result = {
            'is_valid': True,
            'missing_sections': [],
            'invalid_values': [],
            'warnings': []
        }
        
        # 检查必需的配置节
        required_sections = ['tdx', 'output_config', 'smart_file_selector']
        for section in required_sections:
            if section not in config or not config[section]:
                validation_result['missing_sections'].append(section)
                validation_result['is_valid'] = False
        
        # 检查TDX配置
        if 'tdx' in config:
            tdx_config = config['tdx']
            if not tdx_config.get('tdx_path'):
                validation_result['invalid_values'].append('tdx.tdx_path为空')
                validation_result['is_valid'] = False
            
            if not tdx_config.get('pytdx_ip'):
                validation_result['invalid_values'].append('tdx.pytdx_ip为空')
                validation_result['is_valid'] = False
        
        # 检查输出配置
        if 'output_config' in config:
            output_config = config['output_config']
            if not output_config.get('base_output_path'):
                validation_result['invalid_values'].append('output_config.base_output_path为空')
                validation_result['is_valid'] = False
        
        return validation_result
