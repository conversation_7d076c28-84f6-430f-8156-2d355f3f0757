#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试智能文件选择器是否在生产环境中被正确调用
"""

import os
import sys
from pathlib import Path

def test_smart_file_selector_in_production():
    """测试智能文件选择器在生产环境中的调用"""
    print("🔍 测试智能文件选择器在生产环境中的调用")
    print("=" * 60)
    
    try:
        # 1. 测试智能文件选择器直接调用
        print("\n📋 [1/3] 测试智能文件选择器直接调用...")
        
        from utils.smart_file_selector import SmartFileSelector
        
        # 使用生产环境路径
        output_dir = "./signals"  # 生产环境路径
        selector = SmartFileSelector(output_dir)
        print(f"   ✅ 智能文件选择器初始化成功")
        
        # 检查配置是否启用
        config = selector.config
        enabled = config.get('enabled', True)
        strategy = config.get('default_strategy', 'smart_comprehensive')
        
        print(f"   📊 配置状态:")
        print(f"      启用状态: {'✅ 启用' if enabled else '❌ 禁用'}")
        print(f"      默认策略: {strategy}")
        
        # 2. 测试结构化下载器中的调用
        print("\n📋 [2/3] 测试结构化下载器中的智能文件选择器调用...")
        
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        downloader = StructuredInternetMinuteDownloader()
        print(f"   ✅ 结构化下载器初始化成功")
        
        # 测试第一步方法
        existing_file, file_info = downloader._step1_smart_file_selection(
            stock_code="000617",
            start_date="20250401", 
            end_date="20250731"
        )
        
        if existing_file:
            print(f"   ✅ 智能文件选择成功: {Path(existing_file).name}")
            if file_info:
                print(f"   ✅ 文件分析成功: 评分={getattr(file_info, 'total_score', 'N/A')}")
            else:
                print(f"   ⚠️ 文件分析失败")
        else:
            print(f"   ℹ️ 未找到现有文件（正常情况）")
        
        # 3. 测试任务管理器中的调用链
        print("\n📋 [3/3] 测试任务管理器调用链...")
        
        # 检查任务配置
        from mythquant.config import config_manager
        task_configs = config_manager.get_task_configs()
        
        internet_minute_tasks = [
            task for task in task_configs 
            if task.get('data_type') == 'internet_minute' and task.get('enabled', False)
        ]
        
        print(f"   📊 互联网分钟级任务配置:")
        print(f"      总任务数: {len(task_configs)}")
        print(f"      分钟级任务数: {len(internet_minute_tasks)}")
        
        if internet_minute_tasks:
            for task in internet_minute_tasks:
                print(f"      ✅ 任务: {task.get('name', 'Unknown')}")
                print(f"         启用状态: {'✅ 启用' if task.get('enabled') else '❌ 禁用'}")
                print(f"         时间范围: {task.get('start_time')} ~ {task.get('end_time')}")
        else:
            print(f"      ⚠️ 未找到启用的互联网分钟级任务")
        
        # 总结
        print(f"\n📊 智能文件选择器调用链验证总结:")
        print(f"   ✅ 智能文件选择器: 功能正常")
        print(f"   ✅ 结构化下载器: 正确集成智能文件选择器")
        print(f"   ✅ 任务管理器: 正确调用结构化下载器")
        print(f"   {'✅ 调用链完整' if enabled else '⚠️ 智能文件选择器被禁用'}")
        
        return enabled
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_user_config():
    """检查用户配置中的智能文件选择器设置"""
    print("\n🔍 检查用户配置中的智能文件选择器设置")
    print("=" * 60)
    
    try:
        import user_config
        
        if hasattr(user_config, 'smart_file_selector'):
            config = user_config.smart_file_selector
            print(f"   ✅ 找到智能文件选择器配置")
            print(f"   📊 配置详情:")
            print(f"      启用状态: {'✅ 启用' if config.get('enabled', True) else '❌ 禁用'}")
            print(f"      默认策略: {config.get('default_strategy', 'smart_comprehensive')}")
            
            # 检查评分权重
            if 'scoring_weights' in config:
                weights = config['scoring_weights']
                print(f"      评分权重:")
                print(f"         新鲜度: {weights.get('freshness_weight', 0.3)}")
                print(f"         覆盖度: {weights.get('coverage_weight', 0.4)}")
                print(f"         匹配度: {weights.get('match_weight', 0.3)}")
            
            return config.get('enabled', True)
        else:
            print(f"   ⚠️ 未找到智能文件选择器配置")
            print(f"   💡 将使用默认配置（启用状态）")
            return True
            
    except ImportError:
        print(f"   ❌ 无法导入用户配置")
        return False
    except Exception as e:
        print(f"   ❌ 检查配置失败: {e}")
        return False


if __name__ == '__main__':
    print("🎯 智能文件选择器调用验证测试")
    print("=" * 80)
    
    # 检查用户配置
    config_enabled = check_user_config()
    
    # 测试调用链
    call_chain_ok = test_smart_file_selector_in_production()
    
    # 最终结论
    print(f"\n🎊 最终验证结果:")
    print(f"=" * 60)
    print(f"   用户配置: {'✅ 启用' if config_enabled else '❌ 禁用'}")
    print(f"   调用链: {'✅ 正常' if call_chain_ok else '❌ 异常'}")
    
    if config_enabled and call_chain_ok:
        print(f"   🎉 结论: 智能文件选择器在生产环境中被正确调用")
    elif not config_enabled:
        print(f"   ⚠️ 结论: 智能文件选择器被用户配置禁用")
    else:
        print(f"   ❌ 结论: 智能文件选择器调用链存在问题")
