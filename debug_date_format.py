#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试日期格式问题
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def debug_pytdx_date_format():
    """调试pytdx数据的日期格式"""
    print("🔍 调试pytdx数据的日期格式")
    print("=" * 50)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        downloader = PytdxDownloader()
        print("✅ PytdxDownloader创建成功")
        
        # 下载一些测试数据
        print("\n📊 下载测试数据...")
        df = downloader.download_minute_data('000617', '20250801', '20250801', '1min')
        
        if df is None or df.empty:
            print("❌ 无法获取测试数据")
            return False
        
        print(f"✅ 获取到 {len(df)} 条数据")
        print(f"📊 数据列名: {list(df.columns)}")
        
        # 检查时间列的格式
        if 'datetime' in df.columns:
            print(f"\n📅 datetime列的数据类型: {df['datetime'].dtype}")
            print(f"📅 前5个datetime值:")
            for i, dt in enumerate(df['datetime'].head()):
                print(f"   {i+1}: {dt} (类型: {type(dt)})")
            
            print(f"📅 后5个datetime值:")
            for i, dt in enumerate(df['datetime'].tail()):
                print(f"   {i+1}: {dt} (类型: {type(dt)})")
        
        # 测试_check_actual_data_coverage方法
        print(f"\n🔍 测试_check_actual_data_coverage方法...")
        coverage = downloader._check_actual_data_coverage(df, '20250801')
        
        print(f"📊 覆盖情况分析:")
        print(f"   covers_target: {coverage.get('covers_target')}")
        print(f"   earliest_date: {coverage.get('earliest_date')} (类型: {type(coverage.get('earliest_date'))})")
        print(f"   latest_date: {coverage.get('latest_date')} (类型: {type(coverage.get('latest_date'))})")
        print(f"   reason: {coverage.get('reason')}")
        
        # 检查日期字符串的长度
        earliest = coverage.get('earliest_date')
        latest = coverage.get('latest_date')
        
        if earliest:
            print(f"📏 earliest_date长度: {len(str(earliest))}")
            print(f"📏 earliest_date内容: '{earliest}'")
        
        if latest:
            print(f"📏 latest_date长度: {len(str(latest))}")
            print(f"📏 latest_date内容: '{latest}'")
        
        return True
        
    except Exception as e:
        print(f"❌ 调试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def debug_date_extraction():
    """调试日期提取逻辑"""
    print("\n🔍 调试日期提取逻辑")
    print("=" * 50)
    
    # 模拟不同格式的时间数据
    test_cases = [
        "2025-07-25 15:00:00",
        "20250725",
        "202507251500",
        "2025-07-25",
        "2025-07-",  # 可能的问题格式
        "2025-08-",  # 可能的问题格式
    ]
    
    for time_str in test_cases:
        print(f"\n📅 测试时间字符串: '{time_str}'")
        print(f"   长度: {len(time_str)}")
        
        # 模拟pytdx_downloader中的日期提取逻辑
        if len(time_str) >= 10 and '-' in time_str:
            # 格式如 "2025-07-25 15:00:00"
            date_part = time_str[:10].replace('-', '')
            print(f"   提取结果 (格式1): '{date_part}'")
        elif len(time_str) >= 8:
            date_part = time_str[:8]
            print(f"   提取结果 (格式2): '{date_part}'")
        else:
            print(f"   ❌ 无法提取日期")

def main():
    """主函数"""
    print("🚀 日期格式调试")
    print("=" * 60)
    
    # 调试pytdx数据格式
    pytdx_ok = debug_pytdx_date_format()
    
    # 调试日期提取逻辑
    debug_date_extraction()
    
    print("\n" + "=" * 60)
    print("🔍 调试结果总结")
    print("=" * 60)
    print(f"pytdx数据格式: {'✅' if pytdx_ok else '❌'}")
    
    if not pytdx_ok:
        print("\n💡 问题分析：pytdx数据获取或格式解析有问题")
        print("   可能原因：网络连接、数据格式变化、或日期提取逻辑错误")

if __name__ == '__main__':
    main()
