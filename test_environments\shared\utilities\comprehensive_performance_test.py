#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
MythQuant 整体性能验证测试
验证所有优化后的系统能否正常工作，并对比性能提升
"""

import sys
import os
import time
import subprocess
import pandas as pd
import numpy as np
from datetime import datetime
import logging
import shutil

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class ComprehensivePerformanceTest:
    """综合性能测试器"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.main_script = os.path.join(self.project_root, "main_v20230219_optimized.py")
        self.output_dir = "H:/MPV1.17/T0002/signals/"
        self.test_results = {}
        
        # 测试配置
        self.test_stock = "000617"
        self.expected_files = [
            f"day_0_{self.test_stock}_20150101-20250731.txt"
        ]
    
    def backup_existing_files(self):
        """备份现有的输出文件"""
        print("📦 备份现有输出文件...")
        
        backup_dir = os.path.join(self.output_dir, "backup_" + datetime.now().strftime("%Y%m%d_%H%M%S"))
        
        if not os.path.exists(backup_dir):
            os.makedirs(backup_dir)
        
        backed_up_files = []
        for file_name in self.expected_files:
            file_path = os.path.join(self.output_dir, file_name)
            if os.path.exists(file_path):
                backup_path = os.path.join(backup_dir, file_name)
                shutil.copy2(file_path, backup_path)
                backed_up_files.append(file_name)
                print(f"  ✅ 备份: {file_name}")
        
        self.backup_dir = backup_dir
        print(f"📦 备份完成，共备份 {len(backed_up_files)} 个文件到: {backup_dir}")
        return backed_up_files
    
    def run_main_program(self, test_name="性能测试"):
        """运行主程序并记录性能"""
        print(f"\n🚀 运行主程序 - {test_name}...")
        
        start_time = time.time()
        
        try:
            # 运行主程序
            result = subprocess.run(
                [sys.executable, self.main_script],
                cwd=self.project_root,
                capture_output=True,
                text=True,
                timeout=300,  # 5分钟超时
                env=os.environ.copy()  # 继承当前环境变量
            )
            
            end_time = time.time()
            execution_time = end_time - start_time
            
            # 记录结果
            test_result = {
                'execution_time': execution_time,
                'return_code': result.returncode,
                'success': result.returncode == 0,
                'stdout_lines': len(result.stdout.split('\n')) if result.stdout else 0,
                'stderr_lines': len(result.stderr.split('\n')) if result.stderr else 0
            }
            
            if result.returncode == 0:
                print(f"✅ 程序执行成功")
                print(f"⏱️  执行时间: {execution_time:.2f} 秒")
                print(f"📊 输出行数: {test_result['stdout_lines']}")
            else:
                print(f"❌ 程序执行失败，返回码: {result.returncode}")
                if result.stderr:
                    print(f"错误信息: {result.stderr[:500]}...")
            
            self.test_results[test_name] = test_result
            return test_result
            
        except subprocess.TimeoutExpired:
            print(f"❌ 程序执行超时（超过5分钟）")
            return {'execution_time': 300, 'return_code': -1, 'success': False, 'timeout': True}
        
        except Exception as e:
            print(f"❌ 程序执行异常: {e}")
            return {'execution_time': 0, 'return_code': -1, 'success': False, 'error': str(e)}
    
    def verify_output_files(self):
        """验证输出文件的生成和正确性"""
        print("\n📋 验证输出文件...")
        
        verification_results = {}
        
        for file_name in self.expected_files:
            file_path = os.path.join(self.output_dir, file_name)
            
            if not os.path.exists(file_path):
                print(f"❌ 文件不存在: {file_name}")
                verification_results[file_name] = {'exists': False}
                continue
            
            # 获取文件信息
            file_stats = os.stat(file_path)
            file_size = file_stats.st_size
            
            # 读取文件内容进行验证
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    lines = f.readlines()
                
                line_count = len(lines)
                
                # 验证文件格式
                if line_count > 0:
                    header = lines[0].strip()
                    expected_header = "股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖"
                    header_correct = header == expected_header
                    
                    # 验证数据行
                    data_lines = lines[1:6] if len(lines) > 5 else lines[1:]
                    sample_data = []
                    
                    for line in data_lines:
                        parts = line.strip().split('|')
                        if len(parts) >= 8:
                            try:
                                # 验证前复权计算是否正确（当日收盘价 ≠ 前复权收盘价）
                                original_price = float(parts[3])
                                adjusted_price = float(parts[4])
                                sample_data.append({
                                    'original_price': original_price,
                                    'adjusted_price': adjusted_price,
                                    'price_diff': abs(original_price - adjusted_price)
                                })
                            except ValueError:
                                pass
                    
                    # 检查前复权计算是否正确
                    forward_adj_correct = any(data['price_diff'] > 0.01 for data in sample_data)
                    
                else:
                    header_correct = False
                    forward_adj_correct = False
                    sample_data = []
                
                verification_results[file_name] = {
                    'exists': True,
                    'size_bytes': file_size,
                    'size_mb': file_size / 1024 / 1024,
                    'line_count': line_count,
                    'header_correct': header_correct,
                    'forward_adj_correct': forward_adj_correct,
                    'sample_data': sample_data[:3]  # 只保留前3行样本
                }
                
                print(f"✅ {file_name}:")
                print(f"   大小: {file_size:,} bytes ({file_size/1024/1024:.2f} MB)")
                print(f"   行数: {line_count:,}")
                print(f"   格式: {'✅' if header_correct else '❌'}")
                print(f"   前复权: {'✅' if forward_adj_correct else '❌'}")
                
                if sample_data:
                    print(f"   样本数据:")
                    for i, data in enumerate(sample_data[:2]):
                        print(f"     行{i+2}: 当日价格={data['original_price']:.2f}, "
                              f"前复权价格={data['adjusted_price']:.2f}, "
                              f"差异={data['price_diff']:.2f}")
                
            except Exception as e:
                print(f"❌ 读取文件失败 {file_name}: {e}")
                verification_results[file_name] = {'exists': True, 'read_error': str(e)}
        
        return verification_results
    
    def compare_with_backup(self):
        """与备份文件进行对比"""
        print("\n🔍 与备份文件对比...")
        
        if not hasattr(self, 'backup_dir') or not os.path.exists(self.backup_dir):
            print("⚠️  没有找到备份文件，跳过对比")
            return {}
        
        comparison_results = {}
        
        for file_name in self.expected_files:
            current_file = os.path.join(self.output_dir, file_name)
            backup_file = os.path.join(self.backup_dir, file_name)
            
            if not os.path.exists(current_file) or not os.path.exists(backup_file):
                print(f"⚠️  跳过对比 {file_name}（文件不存在）")
                continue
            
            try:
                # 比较文件大小
                current_size = os.path.getsize(current_file)
                backup_size = os.path.getsize(backup_file)
                size_diff = current_size - backup_size
                
                # 比较行数
                with open(current_file, 'r', encoding='utf-8') as f:
                    current_lines = len(f.readlines())
                
                with open(backup_file, 'r', encoding='utf-8') as f:
                    backup_lines = len(f.readlines())
                
                line_diff = current_lines - backup_lines
                
                comparison_results[file_name] = {
                    'current_size': current_size,
                    'backup_size': backup_size,
                    'size_diff': size_diff,
                    'current_lines': current_lines,
                    'backup_lines': backup_lines,
                    'line_diff': line_diff,
                    'size_match': abs(size_diff) < 100,  # 允许100字节的差异
                    'line_match': line_diff == 0
                }
                
                print(f"📊 {file_name}:")
                print(f"   大小: {current_size:,} vs {backup_size:,} bytes "
                      f"({'✅' if abs(size_diff) < 100 else '⚠️'} {size_diff:+d})")
                print(f"   行数: {current_lines:,} vs {backup_lines:,} "
                      f"({'✅' if line_diff == 0 else '⚠️'} {line_diff:+d})")
                
            except Exception as e:
                print(f"❌ 对比失败 {file_name}: {e}")
                comparison_results[file_name] = {'error': str(e)}
        
        return comparison_results
    
    def generate_performance_report(self):
        """生成性能测试报告"""
        print("\n📊 生成性能测试报告...")
        
        report = {
            'test_time': datetime.now().isoformat(),
            'test_results': self.test_results,
            'summary': {}
        }
        
        # 计算总体统计
        successful_tests = sum(1 for result in self.test_results.values() if result.get('success', False))
        total_tests = len(self.test_results)
        
        if total_tests > 0:
            avg_execution_time = np.mean([result['execution_time'] for result in self.test_results.values()])
            report['summary'] = {
                'total_tests': total_tests,
                'successful_tests': successful_tests,
                'success_rate': successful_tests / total_tests,
                'avg_execution_time': avg_execution_time
            }
        
        # 保存报告
        report_file = f"performance_test_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        import json
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False, default=str)
        
        print(f"📋 性能报告已保存: {report_file}")
        
        # 打印摘要
        if total_tests > 0:
            print(f"\n📈 测试摘要:")
            print(f"   总测试数: {total_tests}")
            print(f"   成功测试: {successful_tests}")
            print(f"   成功率: {successful_tests/total_tests:.1%}")
            print(f"   平均执行时间: {avg_execution_time:.2f} 秒")
        
        return report
    
    def run_comprehensive_test(self):
        """运行综合性能测试"""
        print("🚀 MythQuant 整体性能验证测试")
        print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print("=" * 80)
        
        try:
            # 1. 备份现有文件
            self.backup_existing_files()
            
            # 2. 运行主程序
            result = self.run_main_program("整体性能测试")
            
            if not result.get('success', False):
                print("❌ 主程序执行失败，终止测试")
                return False
            
            # 3. 验证输出文件
            verification_results = self.verify_output_files()
            
            # 4. 与备份文件对比
            comparison_results = self.compare_with_backup()
            
            # 5. 生成性能报告
            report = self.generate_performance_report()
            
            # 6. 总体评估
            print("\n🎯 总体评估:")
            
            # 检查所有文件是否正确生成
            all_files_exist = all(
                verification_results.get(file_name, {}).get('exists', False) 
                for file_name in self.expected_files
            )
            
            # 检查前复权计算是否正确
            forward_adj_correct = all(
                verification_results.get(file_name, {}).get('forward_adj_correct', False)
                for file_name in self.expected_files
                if verification_results.get(file_name, {}).get('exists', False)
            )
            
            if all_files_exist and forward_adj_correct and result.get('success', False):
                print("🎉 整体性能验证测试通过！")
                print("✨ 所有优化功能正常工作")
                print("📝 系统已准备好进行下一阶段优化")
                return True
            else:
                print("❌ 整体性能验证测试失败")
                if not all_files_exist:
                    print("   - 部分输出文件未正确生成")
                if not forward_adj_correct:
                    print("   - 前复权计算存在问题")
                return False
                
        except Exception as e:
            print(f"❌ 测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    tester = ComprehensivePerformanceTest()
    success = tester.run_comprehensive_test()
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
