#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置领域 (Configuration Domain)

包含配置相关的实体、值对象、仓储接口和领域服务
"""

try:
    from .entities.trading_config import TradingConfig
    from .entities.data_source_config import DataSourceConfig
    from .entities.processing_config import ProcessingConfig
    from .value_objects.tdx_connection import TdxConnection
    from .value_objects.file_path import FilePath
    from .value_objects.time_range import TimeRange
    from .repositories.config_repository import ConfigRepository
    from .services.config_validation_service import ConfigValidationService
except ImportError as e:
    # 处理导入错误，提供更好的错误信息
    import sys
    print(f"Warning: Failed to import domain config components: {e}", file=sys.stderr)
    # 定义空的类以避免导入错误
    class TradingConfig: pass
    class DataSourceConfig: pass
    class ProcessingConfig: pass
    class TdxConnection: pass
    class FilePath: pass
    class TimeRange: pass
    class ConfigRepository: pass
    class ConfigValidationService: pass

__all__ = [
    'TradingConfig',
    'DataSourceConfig', 
    'ProcessingConfig',
    'TdxConnection',
    'FilePath',
    'TimeRange',
    'ConfigRepository',
    'ConfigValidationService'
]
