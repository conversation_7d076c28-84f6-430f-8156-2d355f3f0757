2025-07-28 23:49:45,116 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250728_234945.log
2025-07-28 23:49:45,116 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-28 23:49:45,117 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-28 23:49:45,117 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-28 23:49:45,117 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-28 23:49:45,117 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-28 23:49:45,117 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-28 23:49:45,118 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-28 23:49:45,118 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-28 23:49:45,125 - Main - INFO - info:307 - ℹ️ ⚖️ 开始增量下载可行性分析
2025-07-28 23:49:45,576 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:49:45,577 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:49:45,577 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-28 23:49:45,780 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 2天, 频率: 1min)
2025-07-28 23:49:45,780 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:49:45,780 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:49:45,780 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:49:45,836 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 23:49:46,067 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 23:49:46,067 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 23:49:46,068 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 23:49:46,068 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:49:46,068 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250725
2025-07-28 23:49:46,072 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 23:49:46,073 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 23:49:46,073 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:49:46,075 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-28 23:49:46,076 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:49:46,076 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-28 23:49:46,076 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-28 23:49:46,076 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-28 23:49:46,076 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 23:49:46,076 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:49:46,077 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:49:46,083 - Main - INFO - info:307 - ℹ️ ✅ 增量下载可行性验证通过
2025-07-28 23:49:46,084 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-28 23:49:46,084 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-28 23:49:46,084 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-28 23:49:46,120 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数21291, 覆盖89个交易日
2025-07-28 23:49:46,120 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-07-28 23:49:46,121 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-07-28 23:49:46,121 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-07-28 23:49:46,121 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-07-28 23:49:46,121 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-07-28 23:49:46,121 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-07-28 23:49:46,121 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-07-28 23:49:46,121 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-07-28 23:49:46,121 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-07-28 23:49:46,122 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-07-28 23:49:46,122 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-28 23:49:46,123 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-28 23:49:46,123 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-28 23:49:46,124 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_202503180931-202507251500_来源互联网（202507282341）.txt
2025-07-28 23:49:46,124 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_202503180931-202507251500_来源互联网（202507282341）.txt
2025-07-28 23:49:46,124 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-28 23:49:46,152 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-07-28 23:49:46,372 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-07-28 23:49:46,377 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:49:46,377 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:49:46,377 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-28 23:49:46,545 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1000条 (目标日期: 20250725, 交易日: 2天, 频率: 1min)
2025-07-28 23:49:46,545 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1000条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:49:46,546 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:49:46,546 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:49:46,595 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需200条
2025-07-28 23:49:46,827 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +200条，总计1000条
2025-07-28 23:49:46,827 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1000条数据
2025-07-28 23:49:46,827 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1000条，实际获取1000条
2025-07-28 23:49:46,827 - Main - WARNING - warning:311 - ⚠️ ⚠️ 但数据覆盖范围不足: 2025-07- ~ 2025-07-
2025-07-28 23:49:46,827 - Main - WARNING - warning:311 - ⚠️ ⚠️ 未能覆盖到目标日期: 20250725
2025-07-28 23:49:46,831 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-28 23:49:46,833 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-28 23:49:46,833 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:49:46,835 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=8.970, 前复权=8.970
2025-07-28 23:49:46,835 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:49:46,836 - Main - INFO - info:307 - ℹ️   日期: 202507250931
2025-07-28 23:49:46,836 - Main - INFO - info:307 - ℹ️   当日收盘价C: 8.97
2025-07-28 23:49:46,836 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 8.97
2025-07-28 23:49:46,836 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-28 23:49:46,836 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:49:46,836 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:49:46,840 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-28 23:49:46,841 - Main - WARNING - warning:311 - ⚠️ ⚠️ 无法解析文件名格式
2025-07-28 23:49:46,841 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-28 23:49:46,841 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-28 23:49:46,841 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-28 23:49:46,842 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-28 23:49:47,019 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32640条 (目标日期: 20250101, 交易日: 136天, 频率: 1min)
2025-07-28 23:49:47,019 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32640条 -> 实际请求800条 (配置限制:800)
2025-07-28 23:49:47,019 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-28 23:49:47,019 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-28 23:49:47,070 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需31840条
2025-07-28 23:49:47,291 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-28 23:49:47,522 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-28 23:49:47,746 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-28 23:49:47,985 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-28 23:49:48,206 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-28 23:49:48,428 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-28 23:49:48,666 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-28 23:49:48,908 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-28 23:49:49,131 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-28 23:49:49,354 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-28 23:49:49,584 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-28 23:49:49,816 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-28 23:49:50,040 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-28 23:49:50,271 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-28 23:49:50,505 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-28 23:49:50,727 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-28 23:49:50,955 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-28 23:49:51,173 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-28 23:49:51,404 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-28 23:49:51,636 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-28 23:49:51,875 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-28 23:49:52,099 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-28 23:49:52,326 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-28 23:49:52,547 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-28 23:49:52,788 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-28 23:49:53,010 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-28 23:49:53,235 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21600条数据
2025-07-28 23:49:53,240 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32640条，实际获取21600条
2025-07-28 23:49:53,240 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11040条数据（约46个交易日）
2025-07-28 23:49:53,240 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-28 23:49:53,240 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-28 23:49:53,240 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-28 23:49:53,296 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-07-28 23:49:53,307 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共21360条记录
2025-07-28 23:49:53,307 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-28 23:49:53,386 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.600, 前复权=6.600
2025-07-28 23:49:53,390 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-28 23:49:53,390 - Main - INFO - info:307 - ℹ️   日期: 202503180931
2025-07-28 23:49:53,390 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.6
2025-07-28 23:49:53,390 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.6
2025-07-28 23:49:53,390 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 21360 >= 5
2025-07-28 23:49:53,390 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-28 23:49:53,391 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-28 23:49:53,445 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250318-20250725_来源互联网（202507282349）.txt (999617 字节)
2025-07-28 23:49:53,446 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-28 23:49:53,447 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-28 23:49:53,447 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-28 23:49:53,448 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-28 23:49:53,448 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-28 23:49:53,448 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-28 23:49:53,448 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-28 23:49:53,448 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-28 23:49:53,448 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-28 23:49:53,449 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250728_234953.txt
2025-07-28 23:49:53,449 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-28 23:49:53,450 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-28 23:49:53,450 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-28 23:49:53,450 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-28 23:49:53,451 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-28 23:49:53,451 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-28 23:49:53,451 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-28 23:49:53,451 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-28 23:49:53,451 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-28 23:49:53,451 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-28 23:49:53,451 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
