#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细的pytdx连接测试
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pytdx_connection_detailed():
    """详细测试pytdx连接"""
    print("🔍 详细测试pytdx连接")
    print("=" * 50)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        downloader = PytdxDownloader()
        print("✅ PytdxDownloader创建成功")
        
        # 测试连接
        print("\n🔗 测试pytdx连接...")
        api = downloader.connect_to_server()
        
        if api is None:
            print("❌ pytdx连接失败")
            return False
        else:
            print("✅ pytdx连接成功")
        
        # 测试数据下载（使用最近的交易日）
        print("\n📊 测试数据下载...")
        # 使用最近的交易日（2025年8月1日是周四，应该有数据）
        df = downloader.download_minute_data('000617', '20250801', '20250801', '1min')
        
        if df is None or df.empty:
            print("❌ 数据下载失败或无数据")
            print("   这可能是因为：")
            print("   1. 请求的日期是非交易日")
            print("   2. pytdx服务器限制（只提供最近100个交易日）")
            print("   3. 网络连接问题")
            
            # 尝试下载更早的数据
            print("\n🔄 尝试下载更早的数据...")
            df = downloader.download_minute_data('000617', '20250730', '20250730', '1min')
            
            if df is None or df.empty:
                print("❌ 更早的数据也无法下载")
                return False
            else:
                print(f"✅ 成功下载更早的数据: {len(df)} 条记录")
                print(f"   数据时间范围: {df['datetime'].min()} ~ {df['datetime'].max()}")
                return True
        else:
            print(f"✅ 数据下载成功: {len(df)} 条记录")
            print(f"   数据时间范围: {df['datetime'].min()} ~ {df['datetime'].max()}")
            return True
            
    except Exception as e:
        print(f"❌ pytdx测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_data_downloader():
    """测试股票数据下载器中的pytdx"""
    print("\n🏭 测试股票数据下载器中的pytdx")
    print("=" * 50)
    
    try:
        from utils.stock_data_downloader import StockDataDownloader
        downloader = StockDataDownloader()
        print("✅ StockDataDownloader创建成功")
        
        # 检查pytdx数据源状态
        pytdx_source = None
        for source in downloader.data_sources:
            if source['name'] == 'pytdx':
                pytdx_source = source
                break
        
        if pytdx_source is None:
            print("❌ pytdx数据源未找到")
            return False
        
        print(f"📊 pytdx数据源状态:")
        print(f"   可用性: {'可用' if pytdx_source['available'] else '不可用'}")
        print(f"   优先级: {pytdx_source['priority']}")
        
        if not pytdx_source['available']:
            print("❌ pytdx数据源标记为不可用")
            return False
        
        # 测试下载
        print("\n📥 测试通过StockDataDownloader下载数据...")
        df = downloader.download_stock_data('000617', '20250801', '20250801', '1')
        
        if df is None or df.empty:
            print("❌ 通过StockDataDownloader下载失败")
            return False
        else:
            print(f"✅ 通过StockDataDownloader下载成功: {len(df)} 条记录")
            return True
            
    except Exception as e:
        print(f"❌ StockDataDownloader测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 详细pytdx连接诊断")
    print("=" * 60)
    
    # 测试pytdx连接
    connection_ok = test_pytdx_connection_detailed()
    
    # 测试股票数据下载器
    downloader_ok = test_stock_data_downloader()
    
    print("\n" + "=" * 60)
    if connection_ok and downloader_ok:
        print("🎉 pytdx连接和下载功能正常！")
        return True
    else:
        print("❌ pytdx存在问题，需要进一步检查")
        if not connection_ok:
            print("   - pytdx连接或数据下载失败")
        if not downloader_ok:
            print("   - StockDataDownloader中的pytdx不可用")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
