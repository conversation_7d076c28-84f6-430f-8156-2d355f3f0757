#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据格式化器模块

提供各种数据格式化功能，包括股票数据格式化、输出格式标准化等
"""

import pandas as pd
import numpy as np
from decimal import Decimal, ROUND_HALF_UP
from typing import Optional, List, Dict, Any, Union
import logging
from datetime import datetime

# 导入新架构的配置系统
from src.mythquant.config.manager import ConfigManager

logger = logging.getLogger(__name__)


class DataFormatter:
    """数据格式化器 - 统一的数据格式化接口"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化数据格式化器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.price_precision = Decimal('0.001')  # 价格精度：3位小数
        self.ratio_precision = Decimal('0.000001')  # 比率精度：6位小数
        
        # 输出格式配置
        self.date_format = "%Y-%m-%d"
        self.datetime_format = "%Y-%m-%d %H:%M:%S"
        self.field_separator = "|"  # 字段分隔符
    
    def format_stock_data_for_output(self, df: pd.DataFrame, stock_code: str, 
                                   data_type: str = "day") -> str:
        """
        格式化股票数据用于输出
        
        Args:
            df: 股票数据DataFrame
            stock_code: 股票代码
            data_type: 数据类型 ("day", "minute")
            
        Returns:
            格式化后的字符串
        """
        if df is None or df.empty:
            logger.warning("数据为空，无法格式化")
            return ""
        
        try:
            # 确保股票代码格式正确
            formatted_stock_code = self._format_stock_code(stock_code)
            
            # 生成表头
            header = self._generate_header(data_type)
            
            # 格式化数据行
            formatted_rows = []
            for _, row in df.iterrows():
                formatted_row = self._format_data_row(row, formatted_stock_code, data_type)
                if formatted_row:
                    formatted_rows.append(formatted_row)
            
            # 组合结果
            result = header + "\n" + "\n".join(formatted_rows)
            
            logger.debug(f"成功格式化{len(formatted_rows)}行{data_type}数据")
            return result
            
        except Exception as e:
            logger.error(f"格式化股票数据失败: {e}")
            return ""
    
    def _format_stock_code(self, stock_code: str) -> str:
        """
        格式化股票代码
        
        Args:
            stock_code: 原始股票代码
            
        Returns:
            格式化后的股票代码
        """
        try:
            # 确保股票代码为6位数字
            if isinstance(stock_code, (int, float)):
                return f"{int(stock_code):06d}"
            elif isinstance(stock_code, str):
                # 移除非数字字符
                numeric_code = ''.join(filter(str.isdigit, stock_code))
                if numeric_code:
                    return f"{int(numeric_code):06d}"
            
            logger.warning(f"无法格式化股票代码: {stock_code}")
            return str(stock_code)
            
        except Exception as e:
            logger.error(f"格式化股票代码失败: {e}")
            return str(stock_code)
    
    def _generate_header(self, data_type: str) -> str:
        """
        生成数据表头
        
        Args:
            data_type: 数据类型
            
        Returns:
            表头字符串
        """
        if data_type == "minute":
            return "股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖"
        else:  # day
            return "股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖"
    
    def _format_data_row(self, row: pd.Series, stock_code: str, data_type: str) -> str:
        """
        格式化数据行
        
        Args:
            row: 数据行
            stock_code: 股票代码
            data_type: 数据类型
            
        Returns:
            格式化后的数据行
        """
        try:
            # 格式化时间
            if data_type == "minute":
                time_str = self._format_datetime(row.get('datetime', row.get('date')))
            else:
                time_str = self._format_date(row.get('date', row.get('datetime')))
            
            # 获取价格数据
            close_price = self._format_price(row.get('close', 0))
            adj_close_price = self._format_price(row.get('adj_close', close_price))
            
            # 计算买卖差
            buy_sell_diff = self._calculate_buy_sell_diff(row)
            
            # 获取路径总长、主买、主卖
            path_length = self._format_price(row.get('path_length', 0))
            main_buy = self._format_price(row.get('main_buy', 0))
            main_sell = self._format_price(row.get('main_sell', 0))
            
            # 组合数据行
            fields = [
                stock_code,
                time_str,
                self._format_price(buy_sell_diff),
                close_price,
                adj_close_price,
                path_length,
                main_buy,
                main_sell
            ]
            
            return self.field_separator.join(fields)
            
        except Exception as e:
            logger.error(f"格式化数据行失败: {e}")
            return ""
    
    def _format_date(self, date_value: Any) -> str:
        """
        格式化日期
        
        Args:
            date_value: 日期值
            
        Returns:
            格式化后的日期字符串
        """
        try:
            if pd.isna(date_value):
                return ""
            
            if isinstance(date_value, str):
                # 尝试解析字符串日期
                date_obj = pd.to_datetime(date_value)
            elif isinstance(date_value, (pd.Timestamp, datetime)):
                date_obj = date_value
            else:
                date_obj = pd.to_datetime(str(date_value))
            
            return date_obj.strftime(self.date_format)
            
        except Exception as e:
            logger.error(f"格式化日期失败: {e}")
            return str(date_value)
    
    def _format_datetime(self, datetime_value: Any) -> str:
        """
        格式化日期时间
        
        Args:
            datetime_value: 日期时间值
            
        Returns:
            格式化后的日期时间字符串
        """
        try:
            if pd.isna(datetime_value):
                return ""
            
            if isinstance(datetime_value, str):
                # 尝试解析字符串日期时间
                datetime_obj = pd.to_datetime(datetime_value)
            elif isinstance(datetime_value, (pd.Timestamp, datetime)):
                datetime_obj = datetime_value
            else:
                datetime_obj = pd.to_datetime(str(datetime_value))
            
            return datetime_obj.strftime(self.datetime_format)
            
        except Exception as e:
            logger.error(f"格式化日期时间失败: {e}")
            return str(datetime_value)
    
    def _format_price(self, price_value: Any) -> str:
        """
        格式化价格
        
        Args:
            price_value: 价格值
            
        Returns:
            格式化后的价格字符串
        """
        try:
            if pd.isna(price_value) or price_value is None:
                return "0.000"
            
            # 转换为Decimal进行精确计算
            decimal_price = Decimal(str(price_value))
            formatted_price = decimal_price.quantize(self.price_precision, rounding=ROUND_HALF_UP)
            
            return f"{formatted_price:.3f}"
            
        except Exception as e:
            logger.error(f"格式化价格失败: {e}")
            return "0.000"
    
    def _calculate_buy_sell_diff(self, row: pd.Series) -> float:
        """
        计算买卖差
        
        Args:
            row: 数据行
            
        Returns:
            买卖差值
        """
        try:
            main_buy = float(row.get('main_buy', 0))
            main_sell = float(row.get('main_sell', 0))
            
            return main_buy - main_sell
            
        except Exception as e:
            logger.error(f"计算买卖差失败: {e}")
            return 0.0
    
    def format_filename(self, stock_code: str, data_type: str, 
                       start_date: str, end_date: str, source: str = "mythquant") -> str:
        """
        格式化输出文件名
        
        Args:
            stock_code: 股票代码
            data_type: 数据类型 ("day", "1min", "5min", etc.)
            start_date: 开始日期
            end_date: 结束日期
            source: 数据来源
            
        Returns:
            格式化后的文件名
        """
        try:
            # 格式化股票代码
            formatted_code = self._format_stock_code(stock_code)
            
            # 格式化数据类型
            if data_type == "day":
                type_prefix = "day"
            elif data_type == "minute" or data_type == "1min":
                type_prefix = "1min"
            elif data_type == "5min":
                type_prefix = "5min"
            else:
                type_prefix = data_type
            
            # 格式化日期
            formatted_start = self._format_date_for_filename(start_date)
            formatted_end = self._format_date_for_filename(end_date)
            
            # 生成文件名
            filename = f"{type_prefix}_0_{formatted_code}_{formatted_start}-{formatted_end}_来源{source}.txt"
            
            return filename
            
        except Exception as e:
            logger.error(f"格式化文件名失败: {e}")
            return f"{data_type}_{stock_code}_{start_date}-{end_date}.txt"
    
    def _format_date_for_filename(self, date_str: str) -> str:
        """
        格式化日期用于文件名
        
        Args:
            date_str: 日期字符串
            
        Returns:
            格式化后的日期字符串
        """
        try:
            # 尝试解析各种日期格式
            date_obj = pd.to_datetime(date_str)
            return date_obj.strftime("%Y%m%d")
            
        except Exception as e:
            logger.error(f"格式化文件名日期失败: {e}")
            return str(date_str).replace("-", "").replace("/", "")
    
    def validate_data_format(self, df: pd.DataFrame, data_type: str) -> tuple[bool, List[str]]:
        """
        验证数据格式
        
        Args:
            df: 数据DataFrame
            data_type: 数据类型
            
        Returns:
            (是否有效, 错误信息列表)
        """
        errors = []
        
        try:
            if df is None or df.empty:
                errors.append("数据为空")
                return False, errors
            
            # 检查必要列
            if data_type == "minute":
                required_columns = ['datetime', 'close']
            else:
                required_columns = ['date', 'close']
            
            missing_columns = [col for col in required_columns if col not in df.columns]
            if missing_columns:
                errors.append(f"缺少必要列: {missing_columns}")
            
            # 检查数据类型
            if 'close' in df.columns:
                if not pd.api.types.is_numeric_dtype(df['close']):
                    errors.append("收盘价列不是数值类型")
                
                if (df['close'] <= 0).any():
                    errors.append("收盘价包含非正值")
            
            # 检查时间格式
            time_column = 'datetime' if data_type == "minute" else 'date'
            if time_column in df.columns:
                try:
                    pd.to_datetime(df[time_column])
                except Exception:
                    errors.append(f"{time_column}列时间格式无效")
            
            is_valid = len(errors) == 0
            return is_valid, errors
            
        except Exception as e:
            errors.append(f"数据验证异常: {e}")
            return False, errors


# 导出
__all__ = ['DataFormatter']
