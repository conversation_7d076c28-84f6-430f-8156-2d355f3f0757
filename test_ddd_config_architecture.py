#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDD配置架构测试脚本

验证新的DDD配置管理架构是否正常工作
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_ddd_config_architecture():
    """测试DDD配置架构"""
    print("🧪 测试DDD配置管理架构")
    print("=" * 60)
    
    success_count = 0
    total_tests = 0
    
    # 测试1: 导入DDD架构组件
    total_tests += 1
    try:
        from src.mythquant.interfaces.config.config_facade import ConfigFacade
        from src.mythquant.application.config.services.config_application_service import ConfigApplicationService
        from src.mythquant.domain.config.entities.trading_config import TradingConfig
        from src.mythquant.infrastructure.config.repositories.file_config_repository import FileConfigRepository
        print("✅ 1. DDD架构组件导入成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 1. DDD架构组件导入失败: {e}")
    
    # 测试2: 创建配置门面
    total_tests += 1
    try:
        config_facade = ConfigFacade()
        print("✅ 2. 配置门面创建成功")
        success_count += 1
    except Exception as e:
        print(f"❌ 2. 配置门面创建失败: {e}")
        return
    
    # 测试3: 测试交易配置访问
    total_tests += 1
    try:
        trading_config = config_facade.get_trading_config()
        print(f"✅ 3. 交易配置访问成功")
        print(f"   TDX路径: {trading_config.get('tdx_path', 'N/A')}")
        print(f"   输出路径: {trading_config.get('output_path', 'N/A')}")
        success_count += 1
    except Exception as e:
        print(f"❌ 3. 交易配置访问失败: {e}")
    
    # 测试4: 测试数据源配置访问
    total_tests += 1
    try:
        data_source_config = config_facade.get_data_source_config()
        print(f"✅ 4. 数据源配置访问成功")
        print(f"   连接超时: {data_source_config.get('connection_timeout', 'N/A')}秒")
        print(f"   最大连接数: {data_source_config.get('max_connections', 'N/A')}")
        success_count += 1
    except Exception as e:
        print(f"❌ 4. 数据源配置访问失败: {e}")
    
    # 测试5: 测试数据处理配置访问
    total_tests += 1
    try:
        processing_config = config_facade.get_processing_config()
        print(f"✅ 5. 数据处理配置访问成功")
        print(f"   智能文件选择器: {'启用' if processing_config.get('smart_file_selector_enabled') else '禁用'}")
        print(f"   调试模式: {'启用' if processing_config.get('debug_enabled') else '禁用'}")
        success_count += 1
    except Exception as e:
        print(f"❌ 5. 数据处理配置访问失败: {e}")
    
    # 测试6: 测试任务配置访问
    total_tests += 1
    try:
        task_configs = config_facade.get_task_configs()
        print(f"✅ 6. 任务配置访问成功")
        print(f"   任务数量: {len(task_configs)}")
        if task_configs:
            first_task = task_configs[0]
            print(f"   第一个任务: {first_task.get('name', 'N/A')} ({first_task.get('data_type', 'N/A')})")
        success_count += 1
    except Exception as e:
        print(f"❌ 6. 任务配置访问失败: {e}")
    
    # 测试7: 测试配置验证
    total_tests += 1
    try:
        validation_result = config_facade.validate_all_configs()
        print(f"✅ 7. 配置验证成功")
        print(f"   验证结果: {'通过' if validation_result.get('is_valid') else '失败'}")
        if validation_result.get('errors'):
            print(f"   错误数量: {len(validation_result['errors'])}")
        if validation_result.get('warnings'):
            print(f"   警告数量: {len(validation_result['warnings'])}")
        success_count += 1
    except Exception as e:
        print(f"❌ 7. 配置验证失败: {e}")
    
    # 测试8: 测试向后兼容接口
    total_tests += 1
    try:
        from src.mythquant.config import get_config_manager
        legacy_manager = get_config_manager()
        
        tdx_path = legacy_manager.get_tdx_path()
        is_debug = legacy_manager.is_debug_enabled()
        
        print(f"✅ 8. 向后兼容接口测试成功")
        print(f"   TDX路径: {tdx_path}")
        print(f"   调试模式: {'启用' if is_debug else '禁用'}")
        success_count += 1
    except Exception as e:
        print(f"❌ 8. 向后兼容接口测试失败: {e}")
    
    # 测试9: 测试便捷函数
    total_tests += 1
    try:
        from src.mythquant.config import get_trading_config, get_task_configs
        
        trading_config = get_trading_config()
        task_configs = get_task_configs()
        
        print(f"✅ 9. 便捷函数测试成功")
        print(f"   交易配置获取: 成功")
        print(f"   任务配置获取: 成功 ({len(task_configs)}个任务)")
        success_count += 1
    except Exception as e:
        print(f"❌ 9. 便捷函数测试失败: {e}")
    
    # 测试10: 测试配置文件信息
    total_tests += 1
    try:
        config_info = config_facade.get_config_file_info()
        print(f"✅ 10. 配置文件信息获取成功")
        print(f"    文件存在: {'是' if config_info.get('exists') else '否'}")
        if config_info.get('exists'):
            print(f"    文件路径: {config_info.get('path', 'N/A')}")
            print(f"    文件大小: {config_info.get('size', 'N/A')} 字节")
        success_count += 1
    except Exception as e:
        print(f"❌ 10. 配置文件信息获取失败: {e}")
    
    # 输出测试结果
    print("\n" + "=" * 60)
    print(f"📊 测试结果: {success_count}/{total_tests} 通过")
    
    if success_count == total_tests:
        print("🎉 所有测试通过！DDD配置架构工作正常")
        return True
    else:
        print(f"⚠️ {total_tests - success_count} 个测试失败，需要检查配置")
        return False

def test_user_config_compatibility():
    """测试user_config.py兼容性"""
    print("\n🔍 测试user_config.py兼容性")
    print("-" * 40)
    
    try:
        # 测试直接导入user_config
        import user_config
        print("✅ user_config.py导入成功")
        
        # 测试关键配置项
        if hasattr(user_config, 'tdx'):
            print("✅ TDX配置存在")
        else:
            print("❌ TDX配置缺失")
        
        if hasattr(user_config, 'smart_file_selector'):
            print("✅ 智能文件选择器配置存在")
        else:
            print("❌ 智能文件选择器配置缺失")
        
        if hasattr(user_config, 'output_config'):
            print("✅ 输出配置存在")
        else:
            print("❌ 输出配置缺失")
        
        return True
        
    except ImportError as e:
        print(f"❌ user_config.py导入失败: {e}")
        print("   请确保user_config.py文件存在于项目根目录")
        return False
    except Exception as e:
        print(f"❌ user_config.py兼容性测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🚀 DDD配置管理架构测试")
    print("=" * 60)
    
    # 测试user_config.py兼容性
    user_config_ok = test_user_config_compatibility()
    
    if not user_config_ok:
        print("\n⚠️ user_config.py存在问题，可能影响DDD架构测试")
        print("建议先检查user_config.py文件")
    
    # 测试DDD架构
    ddd_ok = test_ddd_config_architecture()
    
    print("\n" + "=" * 60)
    if user_config_ok and ddd_ok:
        print("🎊 恭喜！DDD配置管理架构部署成功！")
        print("✅ user_config.py保持为用户友好的配置接口")
        print("✅ DDD架构提供了强大的配置管理能力")
        print("✅ 向后兼容性得到保证")
    else:
        print("❌ 部署过程中发现问题，需要进一步检查")
    
    return user_config_ok and ddd_ok

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
