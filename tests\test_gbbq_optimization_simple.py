#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的GBBQ输出优化测试

验证我们的优化是否工作
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append('.')

def test_gbbq_optimization():
    """测试GBBQ输出优化"""
    print("🧪 测试GBBQ输出优化")
    print("=" * 50)
    
    try:
        from main_v20230219_optimized import StockDataProcessor
        import user_config as ucfg
        
        # 创建处理器
        processor = StockDataProcessor(ucfg.tdx['tdx_path'])
        
        # 获取测试数据
        xdxr_data = processor.load_dividend_data('000617')
        if xdxr_data is None or len(xdxr_data) == 0:
            print("❌ 无法获取000617的除权除息数据")
            return False
        
        print(f"✅ 获取到除权除息数据: {len(xdxr_data)}个事件")
        
        # 测试1：不设置上下文（应该显示详细信息）
        print("\n📋 测试1：默认场景（详细输出）")
        print("-" * 30)
        processor._print_gbbq_data_summary(xdxr_data, '000617', context="analysis")
        
        # 测试2：设置分钟数据上下文（应该显示简要信息）
        print("\n📋 测试2：分钟数据场景（简要输出）")
        print("-" * 30)
        processor._current_processing_context = "minute_data"
        processor._print_gbbq_data_summary(xdxr_data, '000617', context="minute_update")
        
        # 清理上下文
        if hasattr(processor, '_current_processing_context'):
            delattr(processor, '_current_processing_context')
        
        print("\n✅ 测试完成")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == '__main__':
    test_gbbq_optimization()
