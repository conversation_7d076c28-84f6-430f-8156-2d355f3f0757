#!/usr/bin/env python3
"""
数据质量检查集成测试

专门测试第三步数据质量检查与修复的完整流程，
包括函数调用、错误处理、数据转换等关键环节。

这个测试应该能够发现：
1. 函数名称冲突问题
2. 变量作用域问题
3. 数据转换逻辑错误
4. 修复器初始化问题
"""

import sys
import os
import unittest
import tempfile
import pandas as pd
from unittest.mock import Mock, patch, MagicMock

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.join(current_dir, '..', '..')
sys.path.insert(0, project_root)

class TestDataQualityCheckIntegration(unittest.TestCase):
    """数据质量检查集成测试"""
    
    def setUp(self):
        """测试前准备"""
        self.test_data_dir = tempfile.mkdtemp()
        self.test_file_path = os.path.join(self.test_data_dir, "test_1min_data.txt")
        
        # 创建测试数据文件
        self._create_test_data_file()
    
    def tearDown(self):
        """测试后清理"""
        import shutil
        if os.path.exists(self.test_data_dir):
            shutil.rmtree(self.test_data_dir)
    
    def _create_test_data_file(self):
        """创建测试数据文件"""
        # 创建包含不完整数据的测试文件
        test_data = []
        
        # 第一个交易日：完整的240行
        for i in range(240):
            if i < 120:  # 上午
                time_str = f"202507010{931 + i:04d}"
            else:  # 下午
                time_str = f"202507011{301 + (i - 120):04d}"
            
            test_data.append(f"000617|{time_str}|0.0|7.50|7.50|0.01|0.005|0.005")
        
        # 第二个交易日：不完整的200行
        for i in range(200):
            if i < 120:  # 上午
                time_str = f"202507020{931 + i:04d}"
            else:  # 下午
                time_str = f"202507021{301 + (i - 120):04d}"
            
            test_data.append(f"000617|{time_str}|0.0|7.55|7.55|0.01|0.005|0.005")
        
        # 写入文件
        with open(self.test_file_path, 'w', encoding='utf-8') as f:
            f.write("股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖\n")
            f.write("\n".join(test_data))
    
    def test_convert_completeness_function_exists(self):
        """测试转换函数是否存在且可调用"""
        try:
            from src.mythquant.core.task_manager import _convert_completeness_to_missing_structure
            
            # 测试函数是否可调用
            self.assertTrue(callable(_convert_completeness_to_missing_structure))
            
            # 测试基本调用
            test_result = {
                'incomplete_days': [
                    {'date': '20250702', 'missing_count': 40, 'actual_count': 200}
                ],
                'overall_completeness': 95.0
            }
            
            missing_structure = _convert_completeness_to_missing_structure(test_result)
            
            # 验证返回结果结构
            self.assertIn('missing_periods', missing_structure)
            self.assertIn('total_missing_minutes', missing_structure)
            self.assertIn('affected_trading_days', missing_structure)
            
            print("✅ 转换函数存在且可正常调用")
            
        except ImportError as e:
            self.fail(f"❌ 转换函数导入失败: {e}")
        except Exception as e:
            self.fail(f"❌ 转换函数调用失败: {e}")
    
    def test_missing_data_processor_integration(self):
        """测试缺失数据处理器集成"""
        try:
            from utils.missing_data_processor import MissingDataProcessor
            
            processor = MissingDataProcessor()
            
            # 测试简化的完整性检查
            result = processor.check_daily_data_completeness(self.test_file_path, "000617")
            
            # 验证检查结果
            self.assertIsInstance(result, dict)
            self.assertIn('has_missing', result)
            self.assertIn('incomplete_days', result)
            self.assertIn('total_days', result)
            
            # 验证检测到不完整数据
            self.assertTrue(result['has_missing'])
            self.assertEqual(len(result['incomplete_days']), 1)
            self.assertEqual(result['total_days'], 2)
            
            print("✅ 缺失数据处理器集成测试通过")
            
        except Exception as e:
            self.fail(f"❌ 缺失数据处理器集成测试失败: {e}")
    
    def test_pytdx_data_repairer_initialization(self):
        """测试pytdx数据修复器初始化"""
        try:
            from utils.pytdx_data_repairer import PytdxDataRepairer
            
            # 测试修复器初始化
            repairer = PytdxDataRepairer()
            
            # 验证修复器有必要的方法
            self.assertTrue(hasattr(repairer, 'calculate_optimal_download_strategy'))
            self.assertTrue(hasattr(repairer, 'repair_missing_data_with_full_download'))
            
            print("✅ pytdx数据修复器初始化成功")
            
        except Exception as e:
            self.fail(f"❌ pytdx数据修复器初始化失败: {e}")
    
    def test_complete_data_quality_workflow(self):
        """测试完整的数据质量检查工作流程"""
        try:
            from utils.missing_data_processor import MissingDataProcessor
            from utils.pytdx_data_repairer import PytdxDataRepairer
            from src.mythquant.core.task_manager import _convert_completeness_to_missing_structure
            
            # 第1步：执行完整性检查
            processor = MissingDataProcessor()
            completeness_result = processor.check_daily_data_completeness(self.test_file_path, "000617")
            
            # 第2步：转换数据结构
            missing_structure = _convert_completeness_to_missing_structure(completeness_result)
            
            # 第3步：初始化修复器
            repairer = PytdxDataRepairer()
            
            # 第4步：计算下载策略
            download_strategy = repairer.calculate_optimal_download_strategy(missing_structure)
            
            # 验证整个流程没有异常
            self.assertIsInstance(completeness_result, dict)
            self.assertIsInstance(missing_structure, dict)
            self.assertIsInstance(download_strategy, dict)
            
            # 验证数据流转正确
            self.assertTrue(completeness_result['has_missing'])
            self.assertEqual(missing_structure['total_missing_minutes'], 40)
            self.assertIn('strategy_description', download_strategy)
            
            print("✅ 完整数据质量检查工作流程测试通过")
            
        except Exception as e:
            self.fail(f"❌ 完整工作流程测试失败: {e}")
    
    def test_function_name_conflict_detection(self):
        """测试函数名称冲突检测"""
        try:
            import src.mythquant.core.task_manager as tm_module
            
            # 检查是否存在重复的函数定义
            function_names = []
            for name in dir(tm_module):
                if name == '_convert_completeness_to_missing_structure':
                    function_names.append(name)
            
            # 应该只有一个独立函数，不应该有重复
            # 注意：这里我们检查的是模块级别的函数，不包括类方法
            module_level_functions = [name for name in dir(tm_module) 
                                    if callable(getattr(tm_module, name)) 
                                    and not name.startswith('__')
                                    and name == '_convert_completeness_to_missing_structure']
            
            # 验证只有一个模块级别的转换函数
            self.assertEqual(len(module_level_functions), 1, 
                           f"发现函数名称冲突: {module_level_functions}")
            
            print("✅ 函数名称冲突检测通过")
            
        except Exception as e:
            self.fail(f"❌ 函数名称冲突检测失败: {e}")
    
    def test_error_handling_in_repair_workflow(self):
        """测试修复工作流程中的错误处理"""
        try:
            from src.mythquant.core.task_manager import _convert_completeness_to_missing_structure
            
            # 测试异常输入的处理
            invalid_inputs = [
                {},  # 空字典
                {'incomplete_days': []},  # 无缺失数据
                {'incomplete_days': None},  # None值
                {'incomplete_days': [{'date': '', 'missing_count': 0}]},  # 无效数据
            ]
            
            for invalid_input in invalid_inputs:
                try:
                    result = _convert_completeness_to_missing_structure(invalid_input)
                    
                    # 验证错误处理返回合理的默认值
                    self.assertIsInstance(result, dict)
                    self.assertIn('missing_periods', result)
                    self.assertIn('total_missing_minutes', result)
                    
                except Exception as e:
                    self.fail(f"❌ 错误处理失败，输入: {invalid_input}, 错误: {e}")
            
            print("✅ 错误处理测试通过")
            
        except Exception as e:
            self.fail(f"❌ 错误处理测试失败: {e}")
    
    def test_variable_scope_issues(self):
        """测试变量作用域问题"""
        try:
            # 模拟在函数内部调用转换函数的情况
            def simulate_process_function():
                from src.mythquant.core.task_manager import _convert_completeness_to_missing_structure
                
                test_data = {
                    'incomplete_days': [
                        {'date': '20250701', 'missing_count': 50, 'actual_count': 190}
                    ],
                    'overall_completeness': 90.0
                }
                
                # 这里应该能正常调用，不会出现变量作用域问题
                result = _convert_completeness_to_missing_structure(test_data)
                return result
            
            # 执行模拟函数
            result = simulate_process_function()
            
            # 验证结果
            self.assertIsInstance(result, dict)
            self.assertEqual(result['total_missing_minutes'], 50)
            
            print("✅ 变量作用域测试通过")
            
        except NameError as e:
            self.fail(f"❌ 变量作用域问题: {e}")
        except Exception as e:
            self.fail(f"❌ 变量作用域测试失败: {e}")

def run_integration_tests():
    """运行集成测试"""
    print("🚀 数据质量检查集成测试")
    print("=" * 80)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestDataQualityCheckIntegration)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    # 输出测试结果
    if result.wasSuccessful():
        print(f"\n🎉 所有集成测试通过")
        print(f"   运行测试: {result.testsRun}")
        print(f"   失败测试: {len(result.failures)}")
        print(f"   错误测试: {len(result.errors)}")
        return True
    else:
        print(f"\n❌ 集成测试失败")
        print(f"   运行测试: {result.testsRun}")
        print(f"   失败测试: {len(result.failures)}")
        print(f"   错误测试: {len(result.errors)}")
        
        # 输出失败详情
        for test, traceback in result.failures:
            print(f"\n失败测试: {test}")
            print(f"错误信息: {traceback}")
        
        for test, traceback in result.errors:
            print(f"\n错误测试: {test}")
            print(f"错误信息: {traceback}")
        
        return False

if __name__ == '__main__':
    success = run_integration_tests()
    sys.exit(0 if success else 1)
