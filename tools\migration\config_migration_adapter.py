#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置迁移适配器

提供从旧配置系统到新配置系统的无缝迁移支持
让现有代码可以逐步使用新的配置架构
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 导入新的配置系统
from mythquant.config.manager import Config<PERSON>anager, config_manager
from mythquant.config.user_settings import *

# 导入旧的配置系统作为备份
try:
    import user_config as old_config
    OLD_CONFIG_AVAILABLE = True
except ImportError:
    OLD_CONFIG_AVAILABLE = False
    old_config = None

class ConfigMigrationAdapter:
    """配置迁移适配器 - 提供新旧配置系统的桥接"""
    
    def __init__(self):
        """初始化适配器"""
        self.new_config = config_manager
        self.old_config = old_config if OLD_CONFIG_AVAILABLE else None
        
        print("🔄 配置迁移适配器初始化完成")
        if OLD_CONFIG_AVAILABLE:
            print("   ✅ 检测到旧配置系统，将提供兼容性支持")
        else:
            print("   ℹ️ 未检测到旧配置系统，使用纯新配置模式")
    
    def get_config_value(self, key: str, default=None):
        """
        获取配置值（新配置优先，旧配置备用）
        
        Args:
            key: 配置键
            default: 默认值
            
        Returns:
            配置值
        """
        # 首先尝试从新配置系统获取
        try:
            value = self.new_config.get(key, None)
            if value is not None:
                return value
        except Exception as e:
            print(f"⚠️ 从新配置系统获取 {key} 失败: {e}")
        
        # 如果新配置系统没有，尝试从旧配置系统获取
        if self.old_config and hasattr(self.old_config, key):
            try:
                value = getattr(self.old_config, key)
                print(f"📋 使用旧配置值: {key} = {value}")
                return value
            except Exception as e:
                print(f"⚠️ 从旧配置系统获取 {key} 失败: {e}")
        
        return default
    
    def migrate_tdx_config(self):
        """迁移TDX配置"""
        print("\n🔧 迁移TDX配置...")
        
        # 获取TDX路径配置
        new_tdx_path = self.new_config.get_tdx_path()
        old_tdx_path = self.get_config_value('tdx_path') if self.old_config else None
        
        print(f"   新配置TDX路径: {new_tdx_path}")
        if old_tdx_path:
            print(f"   旧配置TDX路径: {old_tdx_path}")
            
            if new_tdx_path != old_tdx_path:
                print(f"   ⚠️ TDX路径不一致，建议检查配置")
        
        # 验证路径有效性
        if new_tdx_path and os.path.exists(new_tdx_path):
            print(f"   ✅ TDX路径有效: {new_tdx_path}")
            return new_tdx_path
        elif old_tdx_path and os.path.exists(old_tdx_path):
            print(f"   📋 使用旧TDX路径: {old_tdx_path}")
            return old_tdx_path
        else:
            print(f"   ❌ TDX路径无效，请检查配置")
            return None
    
    def migrate_output_config(self):
        """迁移输出配置"""
        print("\n🔧 迁移输出配置...")
        
        # 获取输出路径配置
        new_output_path = self.new_config.get_output_path()
        old_output_path = self.get_config_value('output_path') if self.old_config else None
        
        print(f"   新配置输出路径: {new_output_path}")
        if old_output_path:
            print(f"   旧配置输出路径: {old_output_path}")
        
        # 选择有效的输出路径
        if new_output_path:
            print(f"   ✅ 使用新输出路径: {new_output_path}")
            return new_output_path
        elif old_output_path:
            print(f"   📋 使用旧输出路径: {old_output_path}")
            return old_output_path
        else:
            default_path = "output"
            print(f"   🔧 使用默认输出路径: {default_path}")
            return default_path
    
    def migrate_debug_config(self):
        """迁移调试配置"""
        print("\n🔧 迁移调试配置...")
        
        # 获取调试配置
        new_debug = self.new_config.is_debug_enabled()
        old_debug = self.get_config_value('DEBUG', False) if self.old_config else False
        
        print(f"   新配置调试模式: {new_debug}")
        if self.old_config:
            print(f"   旧配置调试模式: {old_debug}")
        
        # 选择调试配置
        debug_mode = new_debug or old_debug
        print(f"   ✅ 最终调试模式: {debug_mode}")
        return debug_mode
    
    def create_compatibility_module(self):
        """创建兼容性模块"""
        print("\n🔧 创建兼容性模块...")
        
        compatibility_code = f'''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置兼容性模块 - 自动生成

提供新旧配置系统的兼容性接口，让现有代码可以无缝使用新配置系统
"""

import sys
import os
from pathlib import Path

# 添加src目录到Python路径
project_root = Path(__file__).parent
src_path = project_root / "src"
sys.path.insert(0, str(src_path))

# 导入新配置系统
from mythquant.config.manager import config_manager

# ==================== 兼容性配置变量 ====================

# TDX配置
tdx_path = "{self.migrate_tdx_config() or ''}"
output_path = "{self.migrate_output_config() or ''}"

# 调试配置
DEBUG = {self.migrate_debug_config()}

# 智能功能配置
smart_file_selector_enabled = {self.new_config.is_smart_file_selector_enabled()}
incremental_download_enabled = {self.new_config.is_incremental_download_enabled()}

# 输出配置
enable_console = {self.new_config.is_console_enabled()}
file_encoding = "{self.new_config.get_file_encoding()}"

# ==================== 兼容性函数 ====================

def get_config(key, default=None):
    """获取配置值的兼容性函数"""
    return config_manager.get(key, default)

def is_debug_enabled():
    """检查是否启用调试模式"""
    return config_manager.is_debug_enabled()

def get_tdx_path():
    """获取TDX路径"""
    return config_manager.get_tdx_path()

def get_output_path():
    """获取输出路径"""
    return config_manager.get_output_path()

# ==================== 向后兼容性支持 ====================

# 为了兼容旧代码中的直接访问方式
class CompatibilityConfig:
    """兼容性配置类"""
    
    def __init__(self):
        self.tdx_path = tdx_path
        self.output_path = output_path
        self.DEBUG = DEBUG
        self.smart_file_selector_enabled = smart_file_selector_enabled
        self.incremental_download_enabled = incremental_download_enabled
        self.enable_console = enable_console
        self.file_encoding = file_encoding
    
    def __getattr__(self, name):
        """动态获取配置属性"""
        return config_manager.get(name)

# 创建兼容性配置实例
compat_config = CompatibilityConfig()

print("✅ 配置兼容性模块加载完成")
print(f"   TDX路径: {{tdx_path}}")
print(f"   输出路径: {{output_path}}")
print(f"   调试模式: {{DEBUG}}")
'''
        
        # 写入兼容性模块文件
        with open("config_compatibility.py", "w", encoding="utf-8") as f:
            f.write(compatibility_code)
        
        print("   ✅ 兼容性模块已创建: config_compatibility.py")
        return True
    
    def test_migration(self):
        """测试迁移结果"""
        print("\n🧪 测试配置迁移结果...")
        
        try:
            # 测试新配置系统
            print("   测试新配置系统...")
            tdx_path = self.new_config.get_tdx_path()
            debug_mode = self.new_config.is_debug_enabled()
            print(f"   ✅ 新配置系统正常: TDX={tdx_path}, DEBUG={debug_mode}")
            
            # 测试兼容性模块
            print("   测试兼容性模块...")
            import config_compatibility as compat
            print(f"   ✅ 兼容性模块正常: TDX={compat.tdx_path}, DEBUG={compat.DEBUG}")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 配置迁移测试失败: {e}")
            return False
    
    def generate_migration_report(self):
        """生成迁移报告"""
        print("\n📊 生成配置迁移报告...")
        
        report = f"""
# 配置迁移报告

## 迁移概况
- 迁移时间: {os.popen('date /t & time /t').read().strip()}
- 旧配置系统: {'可用' if OLD_CONFIG_AVAILABLE else '不可用'}
- 新配置系统: 可用

## 配置对比
| 配置项 | 新配置值 | 旧配置值 | 状态 |
|--------|----------|----------|------|
| TDX路径 | {self.new_config.get_tdx_path()} | {getattr(self.old_config, 'tdx_path', 'N/A') if self.old_config else 'N/A'} | ✅ |
| 输出路径 | {self.new_config.get_output_path()} | {getattr(self.old_config, 'output_path', 'N/A') if self.old_config else 'N/A'} | ✅ |
| 调试模式 | {self.new_config.is_debug_enabled()} | {getattr(self.old_config, 'DEBUG', 'N/A') if self.old_config else 'N/A'} | ✅ |

## 迁移文件
- ✅ config_compatibility.py - 兼容性模块已创建

## 下一步建议
1. 测试现有功能是否正常工作
2. 逐步将代码中的 `import user_config` 替换为 `import config_compatibility`
3. 验证所有配置项都能正确访问
4. 完成迁移后可以移除旧配置文件
"""
        
        with open("config_migration_report.md", "w", encoding="utf-8") as f:
            f.write(report)
        
        print("   ✅ 迁移报告已生成: config_migration_report.md")

def main():
    """主函数"""
    print("🚀 MythQuant 配置迁移工具")
    print("=" * 50)
    
    # 创建迁移适配器
    adapter = ConfigMigrationAdapter()
    
    # 执行迁移
    print("\n📋 开始配置迁移...")
    
    # 创建兼容性模块
    if adapter.create_compatibility_module():
        print("✅ 兼容性模块创建成功")
    else:
        print("❌ 兼容性模块创建失败")
        return False
    
    # 测试迁移结果
    if adapter.test_migration():
        print("✅ 配置迁移测试通过")
    else:
        print("❌ 配置迁移测试失败")
        return False
    
    # 生成迁移报告
    adapter.generate_migration_report()
    
    print("\n" + "=" * 50)
    print("🎉 配置迁移完成！")
    print("\n💡 使用方法:")
    print("1. 将现有代码中的 'import user_config' 替换为 'import config_compatibility'")
    print("2. 配置访问方式保持不变，如 config_compatibility.tdx_path")
    print("3. 新功能建议直接使用新配置系统: from mythquant.config import config_manager")
    
    return True

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
