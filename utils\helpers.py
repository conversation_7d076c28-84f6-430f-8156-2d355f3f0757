#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
工具函数模块
包含通用的辅助函数，不依赖具体业务逻辑
"""

import os
import sys
import datetime
from typing import Dict, Union, Optional

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


def get_output_directory(data_type: str) -> str:
    """
    根据数据类型获取输出目录路径
    
    参数:
    data_type: str - 数据类型 ('minute', 'daily', 'weekly')
    
    返回:
    str - 输出目录路径
    """
    try:
        # 尝试导入用户配置
        import user_config
        output_config = getattr(user_config, 'output_config', {})
        
        base_path = output_config.get('base_output_path', './output')
        use_subdirectories = output_config.get('use_subdirectories', False)
        custom_subdirs = output_config.get('custom_subdirs', {})
        
        if not use_subdirectories:
            return base_path
            
        subdir = custom_subdirs.get(data_type, data_type)
        output_dir = os.path.join(base_path, subdir)
        
        # 确保目录存在
        os.makedirs(output_dir, exist_ok=True)
        
        return output_dir
        
    except ImportError:
        # 如果无法导入用户配置，使用默认路径
        default_path = './output'
        os.makedirs(default_path, exist_ok=True)
        return default_path


def get_stock_market_info(stock_code: Union[str, int]) -> Dict[str, Union[int, str]]:
    """
    根据股票代码判断所属交易所和市场代码
    
    参数:
    stock_code: str|int - 6位股票代码
    
    返回:
    dict - 包含交易所信息的字典
    {
        'market_code': int,    # 市场代码：0=深证, 1=上证, 2=北交所
        'exchange': str,       # 交易所名称：'深证'、'上证'、'北交所'
        'prefix': str         # 文件前缀：'0_'、'1_'、'2_'
    }
    """
    # 确保是6位代码
    code = str(stock_code).zfill(6)
    
    # 上海证券交易所 - 市场代码1
    if code.startswith(('600', '601', '603', '605', '688', '900')):
        return {
            'market_code': 1,
            'exchange': '上证',
            'prefix': '1_'
        }
    
    # 深圳证券交易所 - 市场代码0  
    elif code.startswith(('000', '001', '002', '300', '301', '200')):
        return {
            'market_code': 0,
            'exchange': '深证', 
            'prefix': '0_'
        }
    
    # 北京证券交易所 - 市场代码2
    elif code.startswith(('430', '831', '832', '833', '834', '835', '836', '837', '838', '839')):
        return {
            'market_code': 2,
            'exchange': '北交所',
            'prefix': '2_'
        }
    
    # 默认归类为深证
    else:
        return {
            'market_code': 0,
            'exchange': '深证',
            'prefix': '0_'
        }


def clean_stock_code(stock_code: Union[str, int]) -> str:
    """
    清理和标准化股票代码
    
    参数:
    stock_code: str|int - 原始股票代码
    
    返回:
    str - 标准化的6位股票代码
    """
    # 转换为字符串并去除空白字符
    code = str(stock_code).strip()
    
    # 去除常见的前缀
    if code.startswith(('sz', 'sh', 'bj')):
        code = code[2:]
    
    # 确保是6位数字
    code = code.zfill(6)
    
    return code


def safe_filename(filename: str) -> str:
    """
    生成安全的文件名，去除不安全字符
    
    参数:
    filename: str - 原始文件名
    
    返回:
    str - 安全的文件名
    """
    # 定义不安全字符
    unsafe_chars = '<>:"/\\|?*'
    
    # 替换不安全字符
    safe_name = filename
    for char in unsafe_chars:
        safe_name = safe_name.replace(char, '_')
    
    return safe_name


def format_time_range(start_time: str, end_time: str) -> str:
    """
    格式化时间范围字符串
    
    参数:
    start_time: str - 开始时间（YYYYMMDD格式）
    end_time: str - 结束时间（YYYYMMDD格式）
    
    返回:
    str - 格式化的时间范围字符串
    """
    try:
        # 尝试解析日期
        start_date = datetime.datetime.strptime(start_time, '%Y%m%d')
        end_date = datetime.datetime.strptime(end_time, '%Y%m%d')
        
        # 格式化输出
        return f"{start_date.strftime('%Y-%m-%d')} 至 {end_date.strftime('%Y-%m-%d')}"
    except ValueError:
        # 如果解析失败，直接返回原始字符串
        return f"{start_time} 至 {end_time}"
