# MythQuant 精度配置使用指南

## 概述

本指南介绍如何使用 `user_config.py` 中的 `precision_config` 配置项来控制除权参考价和复权因子的计算精度，以提高前复权计算的准确性。

## 配置项详解

### 1. 基础精度设置

```python
'calculation_precision': 15  # 计算过程中的总精度位数（建议10-20位）
```

- **作用**: 控制 Decimal 计算的总精度
- **建议值**: 15-20位，平衡精度和性能
- **注意**: 此值应大于所有其他精度设置

### 2. 除权参考价精度配置

```python
'ex_dividend_price': {
    'decimal_places': 6,        # 除权参考价小数位数（默认6位）
    'round_method': 'ROUND_HALF_UP',  # 舍入方法
    'intermediate_round': False,      # 是否对中间结果进行舍入
    'display_precision': 4,           # 显示和打印时的精度（默认4位小数）
}
```

**配置说明**:
- `decimal_places`: 控制最终除权参考价的小数位数
- `round_method`: 舍入方法，可选：
  - `ROUND_HALF_UP`: 四舍五入（默认）
  - `ROUND_DOWN`: 向下舍入
  - `ROUND_UP`: 向上舍入
- `intermediate_round`: 是否在计算过程中对中间结果舍入
- `display_precision`: 控制显示时的小数位数

### 3. 复权因子精度配置

```python
'adjustment_factor': {
    'single_factor_places': 12,      # 单次复权因子小数位数（默认12位）
    'cumulative_factor_places': 15,  # 累计复权因子小数位数（默认15位）
    'round_method': 'ROUND_HALF_UP',  # 舍入方法
    'intermediate_round': False,      # 是否对中间计算结果进行舍入
    'display_precision': 8,           # 显示时的精度（默认8位小数）
}
```

**配置说明**:
- `single_factor_places`: 单次除权事件产生的复权因子精度
- `cumulative_factor_places`: 累计复权因子精度（应该较高，减少累积误差）
- 其他参数与除权参考价配置类似

### 4. 分红数据精度配置

```python
'dividend_data': {
    'per_share_places': 10,          # 每股分红金额精度（默认10位）
    'ratio_places': 12,              # 送转股比例精度（默认12位）
    'price_places': 6,               # 配股价格精度（默认6位）
    'normalize_precision': 3,        # 数据标准化时的精度（默认3位：到毫）
}
```

### 5. 持久化存储精度配置

```python
'persistence': {
    'ex_price_storage_places': 6,    # 除权参考价存储精度
    'factor_storage_places': 12,     # 复权因子存储精度
    'csv_output_places': 4,          # CSV输出文件精度
    'database_places': 8,            # 数据库存储精度
}
```

### 6. 特定股票精度策略

```python
'stock_specific': {
    'enabled': False,                # 是否启用股票特定精度策略
    'stock_configs': {
        # 示例：为特定股票设置不同的精度
        '000617': {
            'factor_places': 15, 
            'ex_price_places': 8
        }
    }
}
```

## 使用示例

### 基本用法

```python
from precision_enhanced_forward_adjustment import PrecisionEnhancedForwardAdjustment

# 创建计算器（自动读取配置）
calculator = PrecisionEnhancedForwardAdjustment()

# 计算除权参考价
ex_price, details = calculator.calculate_ex_dividend_price(
    registration_price=7.43,
    dividend_per_10=0.57,
    stock_code='000617'
)

# 计算复权因子
factor, factor_details = calculator.calculate_adjustment_factor(
    registration_price=7.43,
    ex_dividend_price=ex_price,
    stock_code='000617'
)

print(f"除权参考价: {details['ex_price_final']:.{details['precision_applied']}f}")
print(f"复权因子: {factor_details['factor_final']:.{factor_details['precision_applied']}f}")
```

### 格式化输出

```python
# 根据不同输出需求格式化数值
csv_output = calculator.format_for_output(factor, 'csv')        # CSV文件输出
db_output = calculator.format_for_output(factor, 'database')    # 数据库存储
display_output = calculator.format_for_output(factor, 'display') # 屏幕显示

print(f"CSV输出: {csv_output}")
print(f"数据库存储: {db_output}")
print(f"显示格式: {display_output}")
```

## 精度配置建议

### 1. 一般用途配置

```python
precision_config = {
    'calculation_precision': 15,
    'ex_dividend_price': {
        'decimal_places': 6,
        'round_method': 'ROUND_HALF_UP',
        'intermediate_round': False,
        'display_precision': 4,
    },
    'adjustment_factor': {
        'single_factor_places': 12,
        'cumulative_factor_places': 15,
        'round_method': 'ROUND_HALF_UP',
        'intermediate_round': False,
        'display_precision': 8,
    }
}
```

### 2. 高精度需求配置

```python
precision_config = {
    'calculation_precision': 20,
    'ex_dividend_price': {
        'decimal_places': 10,
        'round_method': 'ROUND_HALF_UP',
        'intermediate_round': False,
        'display_precision': 6,
    },
    'adjustment_factor': {
        'single_factor_places': 15,
        'cumulative_factor_places': 20,
        'round_method': 'ROUND_HALF_UP',
        'intermediate_round': False,
        'display_precision': 12,
    }
}
```

### 3. 通达信兼容配置

```python
precision_config = {
    'calculation_precision': 12,
    'ex_dividend_price': {
        'decimal_places': 4,
        'round_method': 'ROUND_HALF_UP',
        'intermediate_round': True,  # 启用中间舍入
        'display_precision': 4,
    },
    'adjustment_factor': {
        'single_factor_places': 8,
        'cumulative_factor_places': 10,
        'round_method': 'ROUND_HALF_UP',
        'intermediate_round': True,
        'display_precision': 6,
    }
}
```

## 性能与精度平衡

### 精度等级建议

| 应用场景 | calculation_precision | factor_places | ex_price_places | 性能影响 |
|----------|----------------------|---------------|-----------------|----------|
| 快速计算 | 12 | 8 | 4 | 低 |
| 标准精度 | 15 | 12 | 6 | 中等 |
| 高精度 | 20 | 15 | 10 | 较高 |
| 极高精度 | 30 | 20 | 15 | 高 |

### 注意事项

1. **精度递增**: `calculation_precision` > `cumulative_factor_places` > `single_factor_places`
2. **性能考虑**: 精度过高会影响计算性能，建议根据实际需要设置
3. **累积误差**: 累计复权因子的精度应该比单次因子高，以减少累积误差
4. **显示精度**: 显示精度可以比计算精度低，保持界面简洁

## 验证和调试

### 配置验证

```python
# 验证配置合理性
validation = calculator.validate_precision_config()
if not validation['is_valid']:
    print("配置错误:")
    for error in validation['errors']:
        print(f"  - {error}")

if validation['warnings']:
    print("配置警告:")
    for warning in validation['warnings']:
        print(f"  - {warning}")
```

### 精度对比测试

```python
# 对比不同精度设置的结果
test_cases = [
    {'calculation_precision': 12, 'factor_places': 8},
    {'calculation_precision': 15, 'factor_places': 12},
    {'calculation_precision': 20, 'factor_places': 15},
]

for config in test_cases:
    # 应用配置并计算
    # 比较结果差异
```

## 常见问题

### Q: 如何选择合适的精度？
A: 
- 一般用途：12-15位计算精度，6-8位因子精度
- 高精度需求：15-20位计算精度，10-15位因子精度
- 性能优先：10-12位计算精度，6位因子精度

### Q: 累计复权因子精度为什么要设置得更高？
A: 累计复权因子是多个单次因子相乘的结果，精度误差会累积放大，因此需要更高的精度来控制最终误差。

### Q: 什么时候需要启用中间结果舍入？
A: 
- 需要与特定软件（如通达信）保持一致时
- 有明确的舍入策略要求时
- 一般情况下建议保持 `False`，让系统自动处理精度

### Q: 如何处理特定股票的精度需求？
A: 使用 `stock_specific` 配置，为特定股票代码设置专门的精度参数。

## 总结

通过合理配置 `precision_config`，可以：

1. **提高计算精度**: 减少浮点数运算误差
2. **控制累积误差**: 通过高精度累计因子计算减少长期误差
3. **兼容性支持**: 通过不同舍入策略模拟其他软件的计算方式
4. **灵活输出**: 根据不同用途格式化输出精度
5. **性能优化**: 在精度和性能之间找到平衡点

建议根据具体应用场景选择合适的精度配置，定期验证计算结果的准确性。 