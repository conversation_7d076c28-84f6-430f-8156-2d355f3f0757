# 集成测试环境

## 📋 环境用途
验证多个模块间的集成和交互

## 🏗️ 目录结构
```
integration_tests/
├── data/
├── results/
├── reports/
├── configs/
├── environment_config.json    # 环境配置文件
└── README.md                  # 本文件
```

## 📖 使用指南
1. 测试真实的模块间交互
2. 使用真实数据但控制数据量
3. 验证端到端的业务流程
4. 执行时间控制在合理范围内

## 🔧 快速开始

### 运行输出格式迁移验证测试
```bash
# 在项目根目录执行
python test_environments/integration_tests/configs/migration_verification_test.py
```

### 运行其他集成测试
```bash
# 在项目根目录执行
python -m pytest test_environments/integration_tests/
```

### 添加测试数据
```bash
# 将测试数据放入相应目录
cp your_test_data.txt test_environments/integration_tests/data/
```

### 查看测试结果
```bash
# 测试结果保存在results目录
ls test_environments/integration_tests/results/
```

## 📋 测试脚本说明

### migration_verification_test.py
**功能**: 验证各模块是否正确迁移到新的结构化输出格式

**检查项目**:
- 模块导入检查：验证是否正确导入结构化输出格式器
- 旧格式检查：检查是否还有旧的print格式
- 功能测试：验证模块功能是否正常

**测试模块**:
- main.py (高优先级)
- utils.structured_internet_minute_downloader (高优先级)
- core.application (中优先级)
- tools.data_integrity_auditor (中优先级)
- utils.smart_file_selector (中优先级)
- utils.missing_data_processor (中优先级)

## 📊 环境状态
- 创建时间: 2025-07-30 00:28:06
- 版本: 1.0.0
- 状态: 活跃

## 🤝 贡献指南
1. 添加新测试前先查看现有测试
2. 遵循项目的测试命名规范
3. 更新相关文档和配置
4. 确保测试可重复执行

---
*本文档由测试环境管理系统自动生成*
