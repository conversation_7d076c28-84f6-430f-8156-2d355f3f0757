#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
算法模块测试脚本
验证L2指标计算、主买主卖计算、时间框架重采样等功能的正确性
"""

import sys
import os
import pandas as pd
import numpy as np
from decimal import Decimal
import unittest
from datetime import datetime, timedelta

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from algorithms import L2MetricsCalculator, BuySellCalculator, TimeFrameResampler


class TestAlgorithms(unittest.TestCase):
    """算法模块测试类"""
    
    def setUp(self):
        """测试前准备"""
        self.l2_calculator = L2MetricsCalculator()
        self.buy_sell_calculator = BuySellCalculator()
        self.resampler = TimeFrameResampler()
        
        # 创建测试数据
        self.test_data = self._create_test_data()
    
    def _create_test_data(self):
        """创建测试用的股票数据"""
        dates = pd.date_range('2024-01-01 09:30:00', periods=100, freq='1min')
        
        # 模拟股票价格数据
        base_price = 10.0
        data = []
        
        for i, date in enumerate(dates):
            # 模拟价格波动
            open_price = base_price + np.random.normal(0, 0.1)
            high_price = open_price + abs(np.random.normal(0, 0.2))
            low_price = open_price - abs(np.random.normal(0, 0.2))
            close_price = open_price + np.random.normal(0, 0.15)
            volume = np.random.randint(1000, 10000)
            
            # 确保价格关系合理
            high_price = max(high_price, open_price, close_price)
            low_price = min(low_price, open_price, close_price)
            
            data.append({
                '日期': date.strftime('%Y%m%d %H:%M:%S'),
                '股票代码': '000617',
                'open': round(open_price, 3),
                'high': round(high_price, 3),
                'low': round(low_price, 3),
                'close': round(close_price, 3),
                'volume': volume,
                'amount': volume * close_price,
                '上周期C': round(base_price if i == 0 else data[i-1]['close'], 3)
            })
            
            base_price = close_price  # 下一个周期的基准价格
        
        return pd.DataFrame(data)
    
    def test_l2_metrics_calculation(self):
        """测试L2指标计算功能"""
        print("\n🧪 测试L2指标计算功能...")
        
        # 执行L2指标计算
        result_df = self.l2_calculator.calculate_l2_metrics(self.test_data.copy())
        
        # 验证结果
        self.assertIsNotNone(result_df, "L2指标计算结果不应为空")
        self.assertEqual(len(result_df), len(self.test_data), "结果行数应与输入数据一致")
        
        # 验证必要的列存在
        required_columns = ['路径总长', '主买', '主卖', '资金效率', '买卖差', '涨跌幅']
        for col in required_columns:
            self.assertIn(col, result_df.columns, f"结果应包含{col}列")
        
        # 验证数值合理性
        self.assertTrue((result_df['路径总长'] >= 0).all(), "路径总长应为非负值")
        self.assertTrue((result_df['主买'] >= 0).all(), "主买应为非负值")
        self.assertTrue((result_df['主卖'] >= 0).all(), "主卖应为非负值")
        
        print(f"✅ L2指标计算测试通过 - 处理了{len(result_df)}条数据")
        print(f"   平均路径总长: {result_df['路径总长'].mean():.4f}")
        print(f"   平均买卖差: {result_df['买卖差'].mean():.4f}")
    
    def test_buy_sell_calculator(self):
        """测试主买主卖计算功能"""
        print("\n🧪 测试主买主卖计算功能...")
        
        # 准备测试数据
        df = self.test_data.copy()
        df['路径总长'] = 0.0
        df['主买'] = 0.0
        df['主卖'] = 0.0
        
        # 创建测试掩码
        close_ge_open = df['close'] >= df['open']
        open_ge_prev = df['open'] >= df['上周期C']
        mask1 = close_ge_open & open_ge_prev
        mask2 = close_ge_open & ~open_ge_prev
        mask3 = ~close_ge_open & open_ge_prev
        mask4 = ~close_ge_open & ~open_ge_prev
        mask_normal = pd.Series([True] * len(df), index=df.index)
        
        high_low_diff = df['high'] - df['low']
        open_prev_diff = df['open'] - df['上周期C']
        
        # 执行主买主卖计算
        self.buy_sell_calculator.calculate_main_buy_sell(
            df, mask1, mask2, mask3, mask4, high_low_diff, open_prev_diff, mask_normal
        )
        
        # 验证结果
        self.assertTrue((df['主买'] >= 0).all(), "主买应为非负值")
        self.assertTrue((df['主卖'] >= 0).all(), "主卖应为非负值")
        
        print(f"✅ 主买主卖计算测试通过")
        print(f"   平均主买: {df['主买'].mean():.4f}")
        print(f"   平均主卖: {df['主卖'].mean():.4f}")
    
    def test_time_frame_resampling(self):
        """测试时间框架重采样功能"""
        print("\n🧪 测试时间框架重采样功能...")
        
        # 先计算L2指标
        df_with_l2 = self.l2_calculator.calculate_l2_metrics(self.test_data.copy())
        
        # 执行重采样
        df_daily, df_weekly = self.resampler.resample_to_timeframes(df_with_l2, 0)
        
        # 验证结果
        if df_daily is not None:
            self.assertGreater(len(df_daily), 0, "日线数据不应为空")
            print(f"✅ 日线重采样测试通过 - 生成{len(df_daily)}条日线数据")
        
        if df_weekly is not None:
            self.assertGreater(len(df_weekly), 0, "周线数据不应为空")
            print(f"✅ 周线重采样测试通过 - 生成{len(df_weekly)}条周线数据")
    
    def test_data_consistency(self):
        """测试数据一致性"""
        print("\n🧪 测试数据一致性...")
        
        # 执行完整的L2指标计算
        result_df = self.l2_calculator.calculate_l2_metrics(self.test_data.copy())
        
        # 验证数据一致性
        for i in range(1, len(result_df)):
            prev_close = result_df.iloc[i-1]['close']
            current_prev_c = result_df.iloc[i]['上周期C']
            
            # 验证上周期C的一致性（允许小的浮点误差）
            if abs(prev_close - current_prev_c) > 0.001:
                print(f"警告：第{i}行上周期C不一致 - 前收盘:{prev_close}, 上周期C:{current_prev_c}")
        
        print("✅ 数据一致性测试完成")
    
    def test_edge_cases(self):
        """测试边界情况"""
        print("\n🧪 测试边界情况...")
        
        # 测试空数据
        empty_df = pd.DataFrame()
        result = self.l2_calculator.calculate_l2_metrics(empty_df)
        self.assertTrue(result.empty, "空数据应返回空结果")
        
        # 测试单行数据
        single_row = self.test_data.iloc[:1].copy()
        result = self.l2_calculator.calculate_l2_metrics(single_row)
        self.assertEqual(len(result), 1, "单行数据应返回单行结果")
        
        # 测试一字板数据
        yizi_data = self.test_data.iloc[:5].copy()
        yizi_data['open'] = yizi_data['high'] = yizi_data['low'] = yizi_data['close'] = 10.0
        result = self.l2_calculator.calculate_l2_metrics(yizi_data)
        self.assertTrue((result['路径总长'] == 0).all(), "一字板的路径总长应为0")
        
        print("✅ 边界情况测试通过")


def run_algorithm_tests():
    """运行算法模块测试"""
    print("🚀 开始算法模块测试...")
    print("=" * 60)
    
    # 创建测试套件
    suite = unittest.TestLoader().loadTestsFromTestCase(TestAlgorithms)
    
    # 运行测试
    runner = unittest.TextTestRunner(verbosity=2)
    result = runner.run(suite)
    
    print("=" * 60)
    if result.wasSuccessful():
        print("🎉 所有算法模块测试通过！")
        return True
    else:
        print("❌ 部分测试失败，请检查算法实现")
        return False


if __name__ == '__main__':
    success = run_algorithm_tests()
    sys.exit(0 if success else 1)
