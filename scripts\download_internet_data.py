#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互联网股票数据下载脚本
根据当前目标股票代码，从互联网下载前复权数据
"""

import sys
import os
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.stock_data_downloader import StockDataDownloader
from utils.enhanced_error_handler import setup_project_logging, get_smart_logger
from core.config_manager import ConfigManager

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def get_target_stocks() -> list:
    """获取目标股票列表"""
    try:
        config_manager = ConfigManager()
        
        # 尝试从配置中获取目标股票
        target_stocks = config_manager.get_target_stocks()
        
        if target_stocks and len(target_stocks) > 0:
            logger.info(f"从配置文件获取到{len(target_stocks)}只目标股票")
            return target_stocks
        else:
            # 如果没有配置目标股票，使用默认股票
            default_stocks = ["000617", "000001", "000002", "600000", "600036"]
            logger.info(f"使用默认股票列表: {default_stocks}")
            return default_stocks
            
    except Exception as e:
        logger.error(f"获取目标股票失败: {e}")
        # 返回默认股票
        return ["000617"]


def install_required_packages():
    """安装必需的数据源包"""
    import subprocess
    
    packages = [
        "baostock",
        "akshare", 
        "yfinance"
    ]
    
    print("🔧 检查并安装必需的数据源包...")
    
    for package in packages:
        try:
            __import__(package)
            print(f"✅ {package} 已安装")
        except ImportError:
            print(f"📦 正在安装 {package}...")
            try:
                subprocess.check_call([sys.executable, "-m", "pip", "install", package])
                print(f"✅ {package} 安装成功")
            except subprocess.CalledProcessError as e:
                print(f"❌ {package} 安装失败: {e}")
                print(f"💡 请手动运行: pip install {package}")


def main():
    """主函数"""
    print("🌐 MythQuant 互联网股票数据下载工具")
    print("=" * 80)
    
    try:
        # 设置日志
        log_file = setup_project_logging("logs")
        smart_logger = get_smart_logger("InternetDataDownloader")
        smart_logger.info("开始互联网股票数据下载")
        
        # 检查并安装必需包
        install_required_packages()
        
        # 获取目标股票
        print("\n📋 获取目标股票列表...")
        target_stocks = get_target_stocks()
        
        if not target_stocks:
            print("❌ 未找到目标股票，程序退出")
            return 1
        
        print(f"🎯 将下载 {len(target_stocks)} 只股票的数据:")
        for i, stock in enumerate(target_stocks, 1):
            print(f"   {i:2d}. {stock}")
        
        # 设置日期范围（从配置中读取）
        from core.config_manager import ConfigManager
        config_manager = ConfigManager()

        # 获取时间范围配置
        time_ranges = config_manager.get_time_ranges()
        internet_daily_time = time_ranges.get('internet_daily', {})
        tdx_daily_time = time_ranges.get('tdx_daily', {})

        # 获取配置的开始日期
        start_date = internet_daily_time.get('start_date', '20150101')

        # 处理结束日期
        configured_end_date = internet_daily_time.get('end_date', 'current')
        if configured_end_date == 'current':
            end_date = datetime.now().strftime("%Y%m%d")
        else:
            end_date = configured_end_date

        # 显示时间范围对比
        print(f"📋 互联网日线数据时间范围: {start_date} - {end_date}")
        print(f"📋 TDX日线数据时间范围: {tdx_daily_time.get('start_date', '未配置')} - {tdx_daily_time.get('end_date', '未配置')}")

        # 如果时间范围不同，给出提示
        if (start_date != tdx_daily_time.get('start_date') or
            end_date != tdx_daily_time.get('end_date')):
            print("⚠️ 注意：互联网日线数据与TDX日线数据的时间范围不同")
            print("💡 可以在 user_config.py 的 time_ranges['internet_daily'] 和 time_ranges['tdx_daily'] 中调整时间配置")
        
        print(f"\n📅 数据时间范围: {start_date} - {end_date}")
        
        # 用户确认
        confirm = input("\n❓ 确认开始下载吗？(y/N): ").strip().lower()
        if confirm not in ['y', 'yes']:
            print("❌ 用户取消下载")
            return 0
        
        # 初始化下载器
        print("\n🚀 初始化数据下载器...")
        downloader = StockDataDownloader()
        
        # 批量下载
        print(f"\n📥 开始批量下载数据...")
        print("=" * 60)
        
        results = downloader.batch_download(
            stock_codes=target_stocks,
            start_date=start_date,
            end_date=end_date,
            delay=2.0  # 2秒间隔，避免请求过于频繁
        )
        
        # 统计结果
        success_count = sum(results.values())
        failed_stocks = [stock for stock, success in results.items() if not success]
        
        print("\n" + "=" * 80)
        print("📊 下载结果统计:")
        print("=" * 80)
        print(f"✅ 成功下载: {success_count}/{len(target_stocks)} 只股票")
        print(f"📊 成功率: {success_count/len(target_stocks)*100:.1f}%")
        
        if failed_stocks:
            print(f"\n❌ 下载失败的股票 ({len(failed_stocks)} 只):")
            for stock in failed_stocks:
                print(f"   - {stock}")
            
            print(f"\n💡 建议:")
            print(f"   1. 检查网络连接")
            print(f"   2. 确认股票代码是否正确")
            print(f"   3. 稍后重试失败的股票")
        
        # 显示输出文件位置
        output_dir = downloader.output_dir
        print(f"\n📁 输出文件位置: {output_dir}")
        print(f"📝 文件命名格式: day_0_{{股票代码}}_{{开始日期}}-{{结束日期}}_来源互联网.txt")
        
        if success_count > 0:
            print(f"\n🎉 数据下载完成！成功下载了 {success_count} 只股票的前复权数据")
            smart_logger.info(f"互联网数据下载完成，成功率: {success_count/len(target_stocks)*100:.1f}%")
        else:
            print(f"\n😞 所有股票下载都失败了，请检查网络连接和数据源状态")
            smart_logger.error("所有股票下载都失败")
        
        return 0 if success_count > 0 else 1
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断下载")
        return 130
        
    except Exception as e:
        print(f"\n❌ 下载过程中发生异常: {e}")
        logger.error(f"下载异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    try:
        result = main()
        sys.exit(result)
    except Exception as e:
        print(f"程序异常退出: {e}")
        sys.exit(1)
