#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Error修复验证测试脚本

专门测试修复后的功能，确保terminal中的error和warning得到解决

作者: AI Assistant
创建时间: 2025-07-30
"""

import sys
import os
from datetime import datetime

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))


def test_file_info_attributes():
    """测试FileInfo属性修复"""
    print("🔍 测试1：FileInfo属性修复验证")
    print("-" * 60)
    
    try:
        from utils.smart_file_selector import SmartFileSelector, FileInfo
        
        # 创建智能文件选择器
        selector = SmartFileSelector("test_environments/minute_data_tests/output_data")
        print("   ✅ SmartFileSelector 创建成功")
        
        # 查找测试文件
        test_file = "test_environments/minute_data_tests/input_data/1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
        
        if os.path.exists(test_file):
            print(f"   📁 测试文件存在: {os.path.basename(test_file)}")
            
            # 分析文件信息
            file_info = selector.analyze_file(test_file, "20250320", "20250704")
            
            if file_info:
                print("   ✅ 文件分析成功")
                
                # 检查关键属性
                required_attrs = ['start_date', 'end_date', 'coverage_days', 'total_score']
                for attr in required_attrs:
                    if hasattr(file_info, attr):
                        value = getattr(file_info, attr)
                        print(f"      ✅ {attr}: {value}")
                    else:
                        print(f"      ❌ 缺少属性: {attr}")
                        return False
                
                return True
            else:
                print("   ❌ 文件分析失败")
                return False
        else:
            print(f"   ⚠️ 测试文件不存在: {test_file}")
            return True  # 文件不存在不算错误
            
    except Exception as e:
        print(f"   ❌ FileInfo属性测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_structured_downloader_step1():
    """测试结构化下载器第一步修复"""
    print("\n🚀 测试2：结构化下载器第一步修复")
    print("-" * 60)
    
    try:
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        
        # 创建下载器
        downloader = StructuredInternetMinuteDownloader()
        print("   ✅ StructuredInternetMinuteDownloader 创建成功")
        
        # 测试第一步：智能文件选择
        print("   🔍 测试第一步：智能文件选择")
        
        existing_file, file_info = downloader._step1_smart_file_selection(
            stock_code="000617",
            start_date="20250320",
            end_date="20250704"
        )
        
        if existing_file:
            print(f"   ✅ 找到现有文件: {os.path.basename(existing_file)}")
            
            if file_info:
                print("   ✅ 文件信息获取成功")
                print(f"      📅 时间范围: {file_info.start_date} ~ {file_info.end_date}")
                print(f"      📊 覆盖天数: {file_info.coverage_days}天")
                print(f"      🎯 质量评分: {file_info.total_score}")
            else:
                print("   ⚠️ 文件信息获取失败")
        else:
            print("   ℹ️ 未找到现有文件（这是正常情况）")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 结构化下载器第一步测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_price_comparison_with_real_file():
    """测试价格比较功能（使用真实文件）"""
    print("\n💰 测试3：价格比较功能（真实文件）")
    print("-" * 60)
    
    try:
        from test_environments.shared.utilities.test_file_api_comparator import TestFileApiComparator
        
        # 使用真实的测试文件
        test_file = "test_environments/minute_data_tests/input_data/test_1min_0_000617_sample.txt"
        
        if not os.path.exists(test_file):
            print(f"   ⚠️ 测试文件不存在: {test_file}")
            return True
        
        print(f"   📁 使用测试文件: {os.path.basename(test_file)}")
        
        # 创建比较器
        comparator = TestFileApiComparator()
        
        # 执行比较
        print("   🔍 执行价格比较...")
        result = comparator.compare_last_record_close_price(
            test_file_path=test_file,
            stock_code="000617",
            tolerance=0.001
        )
        
        if result['success']:
            print("   ✅ 价格比较执行成功")
            
            file_info = result['file_info']
            print(f"      📅 文件最后时间: {file_info['last_time']}")
            print(f"      💰 文件收盘价: {file_info['last_close_price']:.3f}")
            
            api_info = result['api_info']
            if api_info.get('found', False):
                print(f"      💰 API收盘价: {api_info['close_price']:.3f}")
                print(f"      📊 价格差异: {result['comparison']['price_diff']:.6f}")
                print(f"      🎯 比较结果: {'一致' if result['is_equal'] else '不一致'}")
            else:
                print("      ⚠️ API数据获取失败（可能是网络问题或时间超出范围）")
            
            return True
        else:
            print(f"   ❌ 价格比较失败: {result['message']}")
            return False
            
    except Exception as e:
        print(f"   ❌ 价格比较测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def test_environment_correctness():
    """测试环境正确性"""
    print("\n🏗️ 测试4：测试环境正确性")
    print("-" * 60)
    
    try:
        # 检查关键目录
        required_dirs = [
            "test_environments/minute_data_tests/input_data",
            "test_environments/minute_data_tests/output_data",
            "test_environments/minute_data_tests/backup_data",
            "test_environments/minute_data_tests/expected_data",
            "test_environments/minute_data_tests/results",
            "test_environments/minute_data_tests/configs"
        ]
        
        for dir_path in required_dirs:
            if os.path.exists(dir_path):
                print(f"   ✅ 目录存在: {dir_path}")
            else:
                print(f"   ❌ 目录缺失: {dir_path}")
                return False
        
        # 检查测试文件
        test_files = [
            "test_environments/minute_data_tests/input_data/test_1min_0_000617_sample.txt",
            "test_environments/minute_data_tests/input_data/1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
        ]
        
        for file_path in test_files:
            if os.path.exists(file_path):
                file_size = os.path.getsize(file_path)
                print(f"   ✅ 测试文件存在: {os.path.basename(file_path)} ({file_size:,} bytes)")
            else:
                print(f"   ⚠️ 测试文件缺失: {os.path.basename(file_path)}")
        
        # 检查环境配置文件
        config_file = "test_environments/minute_data_tests/environment_config.json"
        if os.path.exists(config_file):
            print(f"   ✅ 环境配置文件存在: {os.path.basename(config_file)}")
        else:
            print(f"   ⚠️ 环境配置文件缺失: {os.path.basename(config_file)}")
        
        return True
        
    except Exception as e:
        print(f"   ❌ 测试环境检查失败: {e}")
        return False


def test_240_standard_consistency():
    """测试240行标准一致性"""
    print("\n📊 测试5：240行标准一致性")
    print("-" * 60)
    
    try:
        from utils.missing_data_processor import MissingDataProcessor
        
        processor = MissingDataProcessor()
        
        # 检查240标准常量
        if hasattr(processor, 'STANDARD_MINUTES_PER_DAY'):
            standard_value = processor.STANDARD_MINUTES_PER_DAY
            print(f"   ✅ STANDARD_MINUTES_PER_DAY: {standard_value}")
            
            if standard_value == 240:
                print("   ✅ 240行标准正确")
                return True
            else:
                print(f"   ❌ 240行标准错误: 期望240，实际{standard_value}")
                return False
        else:
            print("   ❌ 缺少STANDARD_MINUTES_PER_DAY常量")
            return False
            
    except Exception as e:
        print(f"   ❌ 240行标准测试失败: {e}")
        return False


def main():
    """主测试函数"""
    print("🧪 Error修复验证测试")
    print("=" * 100)
    print(f"📅 测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("🎯 目标: 验证terminal中error和warning的修复效果")
    print("=" * 100)
    
    test_results = []
    
    # 测试1: FileInfo属性修复
    result1 = test_file_info_attributes()
    test_results.append(("FileInfo属性修复", result1))
    
    # 测试2: 结构化下载器第一步修复
    result2 = test_structured_downloader_step1()
    test_results.append(("结构化下载器第一步修复", result2))
    
    # 测试3: 价格比较功能
    result3 = test_price_comparison_with_real_file()
    test_results.append(("价格比较功能", result3))
    
    # 测试4: 测试环境正确性
    result4 = test_environment_correctness()
    test_results.append(("测试环境正确性", result4))
    
    # 测试5: 240行标准一致性
    result5 = test_240_standard_consistency()
    test_results.append(("240行标准一致性", result5))
    
    # 汇总测试结果
    print("\n📊 Error修复验证结果汇总")
    print("=" * 100)
    
    passed_count = 0
    for test_name, result in test_results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"   {test_name}: {status}")
        if result:
            passed_count += 1
    
    total_count = len(test_results)
    pass_rate = passed_count / total_count
    
    print(f"\n📈 总体通过率: {passed_count}/{total_count} ({pass_rate:.1%})")
    
    if pass_rate >= 0.8:
        print("🎉 Error修复验证通过！")
        print("💡 Terminal中的主要error和warning已得到修复")
        print("🚀 测试环境配置正确，可以继续进行完整测试")
    else:
        print("⚠️ 部分Error修复验证未通过")
        print("🔧 需要进一步检查和修复失败的测试项")
    
    print("=" * 100)
    
    return pass_rate >= 0.8


if __name__ == '__main__':
    success = main()
    print(f"\n🏁 Error修复验证完成，退出码: {0 if success else 1}")
    sys.exit(0 if success else 1)
