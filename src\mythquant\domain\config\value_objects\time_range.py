#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
时间范围值对象

表示时间区间
"""

from dataclasses import dataclass
from datetime import datetime, date
from typing import Union, Optional


@dataclass(frozen=True)
class TimeRange:
    """时间范围值对象"""
    
    start_date: Union[str, date, datetime]
    end_date: Union[str, date, datetime]
    
    def __post_init__(self):
        """初始化后验证"""
        start = self._parse_date(self.start_date)
        end = self._parse_date(self.end_date)
        
        if start > end:
            raise ValueError(f"开始时间不能晚于结束时间: {start} > {end}")
    
    def _parse_date(self, date_input: Union[str, date, datetime]) -> date:
        """解析日期"""
        if isinstance(date_input, datetime):
            return date_input.date()
        elif isinstance(date_input, date):
            return date_input
        elif isinstance(date_input, str):
            # 支持多种日期格式
            formats = [
                '%Y-%m-%d',
                '%Y%m%d',
                '%Y/%m/%d',
                '%d/%m/%Y',
                '%d-%m-%Y'
            ]
            
            for fmt in formats:
                try:
                    return datetime.strptime(date_input, fmt).date()
                except ValueError:
                    continue
            
            raise ValueError(f"无法解析日期格式: {date_input}")
        else:
            raise ValueError(f"不支持的日期类型: {type(date_input)}")
    
    def get_start_date(self) -> date:
        """获取开始日期"""
        return self._parse_date(self.start_date)
    
    def get_end_date(self) -> date:
        """获取结束日期"""
        return self._parse_date(self.end_date)
    
    def get_duration_days(self) -> int:
        """获取持续天数"""
        start = self.get_start_date()
        end = self.get_end_date()
        return (end - start).days + 1
    
    def contains_date(self, check_date: Union[str, date, datetime]) -> bool:
        """检查是否包含指定日期"""
        target = self._parse_date(check_date)
        start = self.get_start_date()
        end = self.get_end_date()
        return start <= target <= end
    
    def overlaps_with(self, other: 'TimeRange') -> bool:
        """检查是否与另一个时间范围重叠"""
        start1, end1 = self.get_start_date(), self.get_end_date()
        start2, end2 = other.get_start_date(), other.get_end_date()
        
        return not (end1 < start2 or end2 < start1)
    
    def get_overlap_days(self, other: 'TimeRange') -> int:
        """获取与另一个时间范围的重叠天数"""
        if not self.overlaps_with(other):
            return 0
        
        start1, end1 = self.get_start_date(), self.get_end_date()
        start2, end2 = other.get_start_date(), other.get_end_date()
        
        overlap_start = max(start1, start2)
        overlap_end = min(end1, end2)
        
        return (overlap_end - overlap_start).days + 1
    
    def to_string(self, format_str: str = '%Y-%m-%d') -> str:
        """转换为字符串表示"""
        start = self.get_start_date().strftime(format_str)
        end = self.get_end_date().strftime(format_str)
        return f"{start} ~ {end}"
    
    def __str__(self) -> str:
        return self.to_string()
    
    def __repr__(self) -> str:
        return f"TimeRange('{self.start_date}', '{self.end_date}')"
