#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
领域事件 (Domain Events)

表示领域中发生的重要业务事件
"""

from .stock_events import (
    StockCreated, StockPriceChanged, StockSuspended, 
    StockResumed, StockDelisted, DividendAnnounced
)
from .market_events import (
    MarketOpened, MarketClosed, MarketHalted,
    TradingSessionStarted, TradingSessionEnded
)
from .portfolio_events import (
    PortfolioCreated, PositionOpened, PositionClosed,
    PositionAdjusted, DividendReceived
)

__all__ = [
    # 股票事件
    'StockCreated',
    'StockPriceChanged', 
    'StockSuspended',
    'StockResumed',
    'StockDelisted',
    'DividendAnnounced',
    
    # 市场事件
    'MarketOpened',
    'MarketClosed',
    'MarketHalted',
    'TradingSessionStarted',
    'TradingSessionEnded',
    
    # 投资组合事件
    'PortfolioCreated',
    'PositionOpened',
    'PositionClosed',
    'PositionAdjusted',
    'DividendReceived'
]
