# 1分钟数据专项测试环境

## 📋 环境用途
专门测试1分钟数据处理的准确性和完整性

## 🏗️ 目录结构
```
minute_data_tests/
├── input_data/
├── output_data/
├── expected_data/
├── backup_data/
├── results/
├── configs/
├── environment_config.json    # 环境配置文件
└── README.md                  # 本文件
```

## 📖 使用指南
1. 使用固定的测试数据集
2. 验证数据完整性（240条/交易日）
3. 检查前复权价格计算准确性
4. 测试数据格式和字段完整性

## 🔧 快速开始

### 运行测试
```bash
# 在项目根目录执行
python -m pytest test_environments/minute_data_tests/
```

### 添加测试数据
```bash
# 将测试数据放入相应目录
cp your_test_data.txt test_environments/minute_data_tests/data/
```

### 查看测试结果
```bash
# 测试结果保存在results目录
ls test_environments/minute_data_tests/results/
```

## 📊 环境状态
- 创建时间: 2025-07-30 00:28:06
- 版本: 1.0.0
- 状态: 活跃

## 🤝 贡献指南
1. 添加新测试前先查看现有测试
2. 遵循项目的测试命名规范
3. 更新相关文档和配置
4. 确保测试可重复执行

---
*本文档由测试环境管理系统自动生成*
