2025-07-26 11:31:38,121 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_113138.log
2025-07-26 11:31:38,121 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 11:31:38,140 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 11:31:38,141 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 11:31:38,141 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 11:31:38,152 - Main - INFO - info:307 - ℹ️ 开始并行测试 54 个服务器...
2025-07-26 11:31:41,168 - Main - WARNING - warning:311 - ⚠️ ❌ 杭州行情主站 (218.108.50.178:7711) - 连接失败
2025-07-26 11:31:41,168 - Main - WARNING - warning:311 - ⚠️ ❌ 广州行情主站 (119.147.171.206:443) - 连接失败
2025-07-26 11:31:41,169 - Main - WARNING - warning:311 - ⚠️ ❌ 深圳行情主站 (113.105.73.88:7709) - 连接失败
2025-07-26 11:31:41,169 - Main - WARNING - warning:311 - ⚠️ ❌ 上海行情主站 (114.80.80.222:7711) - 连接失败
2025-07-26 11:31:41,169 - Main - WARNING - warning:311 - ⚠️ ❌ 深圳行情主站 (113.105.73.88:7711) - 连接失败
2025-07-26 11:31:41,169 - Main - WARNING - warning:311 - ⚠️ ❌ 北京行情主站1 (106.120.74.86:7711) - 连接失败
2025-07-26 11:31:41,169 - Main - WARNING - warning:311 - ⚠️ ❌ 广州行情主站 (119.147.171.206:80) - 连接失败
2025-07-26 11:31:41,169 - Main - WARNING - warning:311 - ⚠️ ❌ 移动行情主站 (117.184.140.156:7711) - 连接失败
2025-07-26 11:31:44,176 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.74 (112.95.140.74:7709) - 连接失败
2025-07-26 11:31:44,177 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_106.120.74.86 (106.120.74.86:7709) - 连接失败
2025-07-26 11:31:44,177 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.67.61.70 (114.67.61.70:7709) - 连接失败
2025-07-26 11:31:44,177 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.93 (112.95.140.93:7709) - 连接失败
2025-07-26 11:31:44,177 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.22 (114.80.149.22:7709) - 连接失败
2025-07-26 11:31:44,177 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.19 (114.80.149.19:7709) - 连接失败
2025-07-26 11:31:44,177 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.92 (112.95.140.92:7709) - 连接失败
2025-07-26 11:31:44,177 - Main - WARNING - warning:311 - ⚠️ ❌ 北京行情主站2 (221.194.181.176:7711) - 连接失败
2025-07-26 11:31:44,467 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.90.165 (115.238.90.165:7709) - 0.192s
2025-07-26 11:31:44,472 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.56.198 (115.238.56.198:7709) - 0.192s
2025-07-26 11:31:47,190 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_117.184.140.156 (117.184.140.156:7709) - 连接失败
2025-07-26 11:31:47,190 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.80.222 (114.80.80.222:7709) - 连接失败
2025-07-26 11:31:47,190 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.147.171.206 (119.147.171.206:7709) - 连接失败
2025-07-26 11:31:47,191 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.29.51.30 (119.29.51.30:7709) - 连接失败
2025-07-26 11:31:47,191 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.84 (114.80.149.84:7709) - 连接失败
2025-07-26 11:31:47,191 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.147.164.60 (119.147.164.60:7709) - 连接失败
2025-07-26 11:31:47,470 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.104.70 (121.14.104.70:7709) - 连接失败
2025-07-26 11:31:47,486 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.104.72 (121.14.104.72:7709) - 连接失败
2025-07-26 11:31:47,785 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_180.153.18.170 (180.153.18.170:7709) - 0.208s
2025-07-26 11:31:49,300 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_123.125.108.24 (123.125.108.24:7709) - 连接失败
2025-07-26 11:31:49,301 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_123.125.108.23 (123.125.108.23:7709) - 连接失败
2025-07-26 11:31:50,194 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_124.160.88.183 (124.160.88.183:7709) - 连接失败
2025-07-26 11:31:50,194 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.18.17 (180.153.18.17:7709) - 连接失败
2025-07-26 11:31:50,194 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.2.7 (121.14.2.7:7709) - 连接失败
2025-07-26 11:31:50,194 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.110.194 (121.14.110.194:7709) - 连接失败
2025-07-26 11:31:50,487 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.18.171 (180.153.18.171:7709) - 连接失败
2025-07-26 11:31:50,503 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_218.75.126.9 (218.75.126.9:7709) - 0.200s
2025-07-26 11:31:50,765 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.171s
2025-07-26 11:31:50,800 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.39.51 (180.153.39.51:7709) - 连接失败
2025-07-26 11:31:51,082 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.191.117.167 (60.191.117.167:7709) - 0.208s
2025-07-26 11:31:52,308 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.47.69 (218.108.47.69:7709) - 连接失败
2025-07-26 11:31:52,308 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.50.178 (218.108.50.178:7709) - 连接失败
2025-07-26 11:31:53,199 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.98.244 (218.108.98.244:7709) - 连接失败
2025-07-26 11:31:53,199 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.9.148.108 (218.9.148.108:7709) - 连接失败
2025-07-26 11:31:53,199 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_221.194.181.176 (221.194.181.176:7709) - 连接失败
2025-07-26 11:31:53,495 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_59.173.18.69 (59.173.18.69:7709) - 连接失败
2025-07-26 11:31:53,806 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_60.28.29.69 (60.28.29.69:7709) - 连接失败
2025-07-26 11:31:54,098 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.135.142.73 (61.135.142.73:7709) - 连接失败
2025-07-26 11:31:55,323 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.135.142.88 (61.135.142.88:7709) - 连接失败
2025-07-26 11:31:55,323 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.152.107.168 (61.152.107.168:7721) - 连接失败
2025-07-26 11:31:56,157 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_113.105.142.162 (113.105.142.162:7721) - 连接失败
2025-07-26 11:31:56,208 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.152.249.56 (61.152.249.56:7709) - 连接失败
2025-07-26 11:31:56,208 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.153.209.138 (61.153.209.138:7709) - 连接失败
2025-07-26 11:31:56,208 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.153.144.179 (61.153.144.179:7709) - 连接失败
2025-07-26 11:31:56,503 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_************** (**************:7709) - 连接失败
2025-07-26 11:31:57,106 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_************** (**************:7721) - 连接失败
2025-07-26 11:31:57,148 - Main - INFO - info:307 - ℹ️ 配置文件已备份到: user_config.py.backup
2025-07-26 11:31:57,148 - Main - INFO - info:307 - ℹ️ 配置文件更新成功: user_config.py
2025-07-26 11:31:57,148 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已更新为: *************
2025-07-26 11:31:57,148 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器端口已更新为: 7709
2025-07-26 11:31:57,148 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-26 11:31:57,150 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-26 11:31:57,150 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 11:31:57,150 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 11:31:57,150 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 11:31:57,150 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 11:31:57,698 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 11:31:57,698 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 11:31:57,708 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 11:31:57,708 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 11:31:57,709 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 11:31:57,709 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 11:31:57,709 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 11:31:57,709 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 11:31:57,710 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 11:31:57,713 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 11:31:57,713 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 11:31:57,713 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 11:31:57,713 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 11:31:57,720 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 11:31:58,083 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.36秒
2025-07-26 11:31:58,084 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.36秒
2025-07-26 11:31:58,084 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 11:31:58,084 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 11:31:58,084 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 11:31:58,084 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 11:31:58,084 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 11:31:58,085 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 11:31:58,085 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 11:31:58,085 - main_v20230219_optimized - INFO - generate_daily_data_task:3275 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 11:31:58,085 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:3814 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 11:31:58,085 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 11:31:58,086 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 11:31:58,193 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 11:31:58,193 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 11:31:58,200 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 11:31:58,200 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 11:31:58,200 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 11:31:58,200 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 11:31:58,200 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 11:31:58,201 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 11:31:58,201 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 11:31:58,204 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 11:31:58,204 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 11:31:58,204 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 11:31:58,204 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 11:31:58,211 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 11:31:58,820 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.61秒
2025-07-26 11:31:58,820 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.61秒
2025-07-26 11:32:09,028 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 11:32:31,489 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 11:32:31,489 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 11:32:31,489 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 11:32:31,498 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 11:32:32,079 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 11:32:32,079 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 11:32:32,079 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 11:32:32,080 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 11:32:32,081 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 11:32:32,082 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 11:32:32,082 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 11:32:32,082 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 11:32:32,082 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 11:32:32,140 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 11:32:32,661 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 11:32:32,675 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 11:32:32,691 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 11:32:32,706 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 11:32:32,722 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 11:32:32,737 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 11:32:32,753 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 11:32:32,769 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 11:32:32,785 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 11:32:32,802 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 11:32:32,815 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 11:32:32,831 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 11:32:32,844 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 11:32:32,861 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 11:32:32,877 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 11:32:32,893 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 11:32:32,909 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 11:32:32,922 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 11:32:32,938 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 11:32:32,954 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 11:32:32,967 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 11:32:32,967 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 11:32:37,027 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 11:32:37,028 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 11:32:37,028 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 11:32:37,028 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 11:32:37,028 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 11:32:37,030 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 11:32:37,169 - main_v20230219_optimized - INFO - apply_forward_adjustment:1911 - 保留交易时间数据后: 320880条
2025-07-26 11:32:37,169 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 11:32:37,169 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 11:32:37,170 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 11:32:37,170 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 11:32:37,170 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 11:32:37,170 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 11:32:37,181 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 11:32:37,337 - main_v20230219_optimized - INFO - load_and_process_minute_data:2380 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 11:32:37,779 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 11:32:38,006 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 11:32:38,007 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 11:32:38,098 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 11:32:38,098 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 11:32:38,099 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3993 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 11:32:38,099 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3994 - 📊 成功生成: 1 个文件
2025-07-26 11:32:38,099 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3995 - ❌ 处理失败: 0 个股票
2025-07-26 11:32:38,099 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3996 - 📈 成功率: 100.0%
2025-07-26 11:32:38,099 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3997 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 11:32:38,113 - main_v20230219_optimized - INFO - generate_daily_data_task:3285 - ✅ 日线数据生成完成，耗时: 40.03 秒
2025-07-26 11:32:38,113 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 11:32:38,113 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网日线数据下载
2025-07-26 11:32:38,113 - Main - INFO - info:307 - ℹ️ 开始执行互联网日线数据下载任务
2025-07-26 11:32:38,114 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 11:32:38,115 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 11:32:38,443 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 11:32:38,454 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 互联网数据下载时间范围: 20170101 - 20250720
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的互联网数据
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 📊 频率转换: d -> daily
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 daily 数据: 20170101 - 20250720
2025-07-26 11:32:38,455 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:38,672 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:38,672 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计749520条 -> 实际请求800条 (配置限制:800)
2025-07-26 11:32:38,672 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 daily 数据
2025-07-26 11:32:38,672 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=9, market=0, code=000617, count=800
2025-07-26 11:32:38,737 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需748720条
2025-07-26 11:32:38,737 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:38,967 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:39,033 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 11:32:39,033 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:39,233 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:39,291 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 11:32:39,291 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:39,481 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:39,538 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 11:32:39,538 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:39,744 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:39,807 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 11:32:39,807 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:40,010 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:40,070 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 11:32:40,071 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:40,275 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:40,334 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 11:32:40,334 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:40,518 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:40,572 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 11:32:40,572 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:40,779 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:40,836 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +355条，总计6755条
2025-07-26 11:32:40,836 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计6755条数据
2025-07-26 11:32:40,853 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 2073 条 daily 数据
2025-07-26 11:32:40,856 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共2073条记录
2025-07-26 11:32:40,856 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 11:32:40,862 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=19.030, 前复权=19.030
2025-07-26 11:32:40,863 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 11:32:40,863 - Main - INFO - info:307 - ℹ️   日期: 20170103
2025-07-26 11:32:40,863 - Main - INFO - info:307 - ℹ️   当日收盘价C: 19.03
2025-07-26 11:32:40,863 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 19.03
2025-07-26 11:32:40,863 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 11:32:40,863 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 2073 >= 5
2025-07-26 11:32:40,863 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 11:32:40,864 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 11:32:40,864 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 11:32:40,867 - Main - INFO - info:307 - ℹ️ 数据无变化，将添加时间戳: day_0_000617_20170101-20250720_来源互联网（202507261132）.txt
2025-07-26 11:32:40,888 - Main - INFO - info:307 - ℹ️ 成功保存000617数据到: H:/MPV1.17/T0002/signals\day_0_000617_20170101-20250720_来源互联网（202507261132）.txt
2025-07-26 11:32:40,888 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ 互联网数据下载完成: 1/1 成功 (100.0%)
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网日线数据下载
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 11:32:40,889 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 11:32:40,890 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 11:32:40,890 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 11:32:40,890 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250726
2025-07-26 11:32:40,890 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 11:32:40,890 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 11:32:40,890 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 11:32:40,890 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 11:32:40,891 - Main - INFO - info:307 - ℹ️ 🔄 开始分钟级数据增量下载: 000617
2025-07-26 11:32:40,891 - Main - INFO - info:307 - ℹ️ 找到现有文件: min_0_000617_20250101-20250725_来源互联网.txt
2025-07-26 11:32:40,891 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250101 - 20250725
2025-07-26 11:32:40,893 - Main - INFO - info:307 - ℹ️ 获取最后一条记录: 时间=20250725 15:00, 前复权价=8.9
2025-07-26 11:32:40,893 - Main - INFO - info:307 - ℹ️ 检查 000617 在 20250725 的前复权价一致性
2025-07-26 11:32:40,893 - Main - INFO - info:307 - ℹ️ 🎯 严格使用配置的频率: 1 (不允许自动切换频率)
2025-07-26 11:32:40,894 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 11:32:40,894 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 11:32:40,894 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-26 11:32:40,894 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:41,100 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:41,100 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计240条 -> 实际请求240条 (配置限制:800)
2025-07-26 11:32:41,100 - Main - INFO - info:307 - ℹ️ 📊 预计获取 240 条 1min 数据
2025-07-26 11:32:41,100 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=240
2025-07-26 11:32:41,158 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-26 11:32:41,160 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-26 11:32:41,160 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 11:32:41,162 - Main - INFO - info:307 - ℹ️ 前复权价对比: 存储=8.900, 当前=8.970, 差异=0.070000
2025-07-26 11:32:41,162 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价不一致，需要全量重新下载
2025-07-26 11:32:41,162 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价不一致，执行全量下载
2025-07-26 11:32:41,162 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-26 11:32:41,162 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 11:32:41,162 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 11:32:41,162 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250726
2025-07-26 11:32:41,162 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:41,342 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:41,342 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计49680条 -> 实际请求800条 (配置限制:800)
2025-07-26 11:32:41,342 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 11:32:41,342 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 11:32:41,394 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需48880条
2025-07-26 11:32:41,395 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:41,569 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:41,623 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 11:32:41,623 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:41,795 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:41,846 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 11:32:41,846 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:42,036 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:42,090 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 11:32:42,090 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:42,284 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:42,340 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 11:32:42,340 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:42,525 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:42,578 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 11:32:42,578 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:42,766 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:42,823 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 11:32:42,823 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:42,986 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:43,036 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 11:32:43,036 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:43,245 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:43,306 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 11:32:43,306 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:43,500 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:43,556 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 11:32:43,556 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:43,754 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:43,811 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 11:32:43,811 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:44,013 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:44,070 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 11:32:44,071 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:44,247 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:44,302 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 11:32:44,302 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:44,493 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:44,549 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 11:32:44,549 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:44,782 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:44,847 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 11:32:44,847 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:45,062 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:45,125 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 11:32:45,126 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:45,328 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:45,388 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 11:32:45,389 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:45,566 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:45,619 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 11:32:45,620 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:45,833 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:45,892 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 11:32:45,892 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:46,109 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:46,171 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 11:32:46,171 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:46,368 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:46,427 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 11:32:46,427 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:46,613 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:46,669 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 11:32:46,669 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:46,854 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:46,910 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 11:32:46,910 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:47,120 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:47,182 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 11:32:47,182 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:47,368 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:47,423 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 11:32:47,423 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:47,605 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:47,658 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 11:32:47,658 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:47,858 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:47,920 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 11:32:47,920 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:48,125 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:48,183 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 11:32:48,183 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:48,416 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:48,481 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 11:32:48,481 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:48,698 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:48,759 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 11:32:48,759 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 218.75.126.9:7709
2025-07-26 11:32:48,971 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 218.75.126.9:7709
2025-07-26 11:32:49,027 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 11:32:49,092 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 24000 条 1min 数据
2025-07-26 11:32:49,103 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共24000条记录
2025-07-26 11:32:49,104 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 11:32:49,188 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.290, 前复权=6.290
2025-07-26 11:32:49,192 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 11:32:49,193 - Main - INFO - info:307 - ℹ️   日期: 20250303 00:00
2025-07-26 11:32:49,193 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.29
2025-07-26 11:32:49,193 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.29
2025-07-26 11:32:49,193 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 11:32:49,193 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 24000 >= 5
2025-07-26 11:32:49,193 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 11:32:49,194 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 11:32:49,194 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 11:32:49,279 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1_0_000617_20250101-20250726_来源互联网.txt (1171291 字节)
2025-07-26 11:32:49,280 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 11:32:49,280 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 11:32:49,280 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-26 11:32:49,280 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-26 11:32:49,280 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 11:32:49,281 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 11:32:49,281 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 11:32:49,281 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 11:32:49,282 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 11:32:49,282 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 11:32:49,282 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: day_0_000617_20240701-20240731_来源互联网.txt
2025-07-26 11:32:49,299 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20200101-20250724.txt, 数据行数: 1337
2025-07-26 11:32:49,301 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20240701-20240731_来源互联网.txt, 数据行数: 23
2025-07-26 11:32:49,302 - Main - INFO - info:307 - ℹ️ 数据对齐完成，共同日期数: 23
2025-07-26 11:32:49,304 - Main - INFO - info:307 - ℹ️ 价格差异计算完成，数据点数: 23
2025-07-26 11:32:49,307 - Main - INFO - info:307 - ℹ️ 股票 000617 数据比较完成
2025-07-26 11:32:49,307 - Main - INFO - info:307 - ℹ️ ✅ 000617 分析完成
2025-07-26 11:32:49,310 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_113249.txt
2025-07-26 11:32:49,310 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 1/1 成功 (100.0%)
2025-07-26 11:32:49,310 - Main - INFO - info:307 - ℹ️ 任务执行成功: 前复权数据比较分析
2025-07-26 11:32:49,310 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 100.0%
2025-07-26 11:32:49,311 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 11:32:49,311 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 11:32:49,311 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 11:32:49,311 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 11:32:49,311 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 11:32:49,311 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 11:32:49,312 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
