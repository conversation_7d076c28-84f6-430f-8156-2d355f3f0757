#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
互联网数据源模块

提供基于互联网API的数据获取功能
"""

import pandas as pd
import requests
from typing import Dict, List, Optional, Tuple, Any
from datetime import datetime, timedelta
import logging
import time

# 导入新架构的配置系统
from src.mythquant.config.manager import ConfigManager

logger = logging.getLogger(__name__)


class InternetDataSource:
    """互联网数据源类 - 在线API数据获取接口"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化互联网数据源
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.session = requests.Session()
        
        # 设置请求头
        self.session.headers.update({
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        })
        
        # 请求限制
        self.request_delay = 0.1  # 请求间隔（秒）
        self.last_request_time = 0
    
    def _rate_limit(self):
        """请求频率限制"""
        current_time = time.time()
        elapsed = current_time - self.last_request_time
        if elapsed < self.request_delay:
            time.sleep(self.request_delay - elapsed)
        self.last_request_time = time.time()
    
    def get_stock_data_from_sina(self, stock_code: str, 
                                data_type: str = "day") -> Optional[pd.DataFrame]:
        """
        从新浪财经获取股票数据
        
        Args:
            stock_code: 股票代码
            data_type: 数据类型 ("day", "minute")
            
        Returns:
            股票数据DataFrame或None
        """
        try:
            self._rate_limit()
            
            # 构建请求URL
            if data_type == "day":
                url = f"http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData"
                params = {
                    'symbol': self._format_stock_code_for_sina(stock_code),
                    'scale': '240',  # 日线
                    'ma': 'no',
                    'datalen': '1000'
                }
            elif data_type == "minute":
                url = f"http://money.finance.sina.com.cn/quotes_service/api/json_v2.php/CN_MarketData.getKLineData"
                params = {
                    'symbol': self._format_stock_code_for_sina(stock_code),
                    'scale': '1',  # 1分钟
                    'ma': 'no',
                    'datalen': '240'
                }
            else:
                logger.error(f"不支持的数据类型: {data_type}")
                return None
            
            response = self.session.get(url, params=params, timeout=10)
            response.raise_for_status()
            
            # 解析数据
            data = response.json()
            if data:
                df = pd.DataFrame(data)
                # 重命名列
                df = df.rename(columns={
                    'day': 'datetime',
                    'open': 'open',
                    'high': 'high',
                    'low': 'low',
                    'close': 'close',
                    'volume': 'volume'
                })
                
                # 转换数据类型
                df['datetime'] = pd.to_datetime(df['datetime'])
                for col in ['open', 'high', 'low', 'close']:
                    df[col] = pd.to_numeric(df[col], errors='coerce')
                df['volume'] = pd.to_numeric(df['volume'], errors='coerce')
                
                logger.debug(f"成功从新浪获取{stock_code} {data_type}数据，共{len(df)}条记录")
                return df
            else:
                logger.warning(f"新浪财经未返回{stock_code}的数据")
                return None
                
        except Exception as e:
            logger.error(f"从新浪财经获取{stock_code}数据失败: {e}")
            return None
    
    def get_stock_data_from_tencent(self, stock_code: str) -> Optional[pd.DataFrame]:
        """
        从腾讯财经获取股票数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            股票数据DataFrame或None
        """
        try:
            self._rate_limit()
            
            # 构建请求URL
            formatted_code = self._format_stock_code_for_tencent(stock_code)
            url = f"http://qt.gtimg.cn/q={formatted_code}"
            
            response = self.session.get(url, timeout=10)
            response.raise_for_status()
            
            # 解析数据
            content = response.text
            if content and '~' in content:
                parts = content.split('~')
                if len(parts) > 40:
                    data = {
                        'code': stock_code,
                        'name': parts[1],
                        'current_price': float(parts[3]) if parts[3] else 0,
                        'yesterday_close': float(parts[4]) if parts[4] else 0,
                        'open': float(parts[5]) if parts[5] else 0,
                        'high': float(parts[33]) if parts[33] else 0,
                        'low': float(parts[34]) if parts[34] else 0,
                        'volume': int(parts[6]) if parts[6] else 0,
                        'amount': float(parts[37]) if parts[37] else 0,
                    }
                    
                    df = pd.DataFrame([data])
                    logger.debug(f"成功从腾讯获取{stock_code}实时数据")
                    return df
                else:
                    logger.warning(f"腾讯财经返回数据格式异常: {stock_code}")
                    return None
            else:
                logger.warning(f"腾讯财经未返回{stock_code}的数据")
                return None
                
        except Exception as e:
            logger.error(f"从腾讯财经获取{stock_code}数据失败: {e}")
            return None
    
    def get_stock_list_from_internet(self) -> List[str]:
        """
        从互联网获取股票列表
        
        Returns:
            股票代码列表
        """
        try:
            self._rate_limit()
            
            # 从新浪财经获取股票列表
            url = "http://money.finance.sina.com.cn/d/api/openapi_proxy.php"
            params = {
                '__s': '["hq","hs_a","",0,1,5000]'
            }
            
            response = self.session.get(url, params=params, timeout=15)
            response.raise_for_status()
            
            data = response.json()
            stock_list = []
            
            if data and isinstance(data, list) and len(data) > 0:
                stocks_data = data[0]
                if 'items' in stocks_data:
                    for item in stocks_data['items']:
                        if len(item) > 0:
                            stock_code = item[0]
                            # 过滤A股代码
                            if (stock_code.startswith(('60', '68', '00', '30')) and 
                                len(stock_code) == 6):
                                stock_list.append(stock_code)
            
            logger.info(f"从互联网获取到{len(stock_list)}只股票")
            return sorted(stock_list)
            
        except Exception as e:
            logger.error(f"从互联网获取股票列表失败: {e}")
            return []
    
    def _format_stock_code_for_sina(self, stock_code: str) -> str:
        """
        格式化股票代码用于新浪财经
        
        Args:
            stock_code: 原始股票代码
            
        Returns:
            格式化后的股票代码
        """
        if stock_code.startswith(('60', '68')):
            return f"sh{stock_code}"
        elif stock_code.startswith(('00', '30')):
            return f"sz{stock_code}"
        else:
            return stock_code
    
    def _format_stock_code_for_tencent(self, stock_code: str) -> str:
        """
        格式化股票代码用于腾讯财经
        
        Args:
            stock_code: 原始股票代码
            
        Returns:
            格式化后的股票代码
        """
        if stock_code.startswith(('60', '68')):
            return f"sh{stock_code}"
        elif stock_code.startswith(('00', '30')):
            return f"sz{stock_code}"
        else:
            return stock_code
    
    def get_trading_calendar(self, year: int = None) -> List[datetime]:
        """
        获取交易日历
        
        Args:
            year: 年份，默认为当前年份
            
        Returns:
            交易日列表
        """
        if year is None:
            year = datetime.now().year
        
        try:
            # 简单实现：生成工作日，排除周末
            # 实际应该从专业的交易日历API获取
            trading_days = []
            start_date = datetime(year, 1, 1)
            end_date = datetime(year, 12, 31)
            
            current_date = start_date
            while current_date <= end_date:
                if current_date.weekday() < 5:  # 周一到周五
                    trading_days.append(current_date)
                current_date += timedelta(days=1)
            
            logger.info(f"{year}年交易日历生成完成，共{len(trading_days)}个交易日")
            return trading_days
            
        except Exception as e:
            logger.error(f"获取{year}年交易日历失败: {e}")
            return []
    
    def cleanup(self):
        """清理资源"""
        if self.session:
            self.session.close()
        logger.debug("互联网数据源资源已清理")
    
    def __enter__(self):
        """上下文管理器入口"""
        return self
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        """上下文管理器出口"""
        self.cleanup()


# 导出
__all__ = ['InternetDataSource']
