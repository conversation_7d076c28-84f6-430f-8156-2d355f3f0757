2025-08-01 00:41:47,305 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250801_004147.log
2025-08-01 00:41:47,305 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-08-01 00:41:47,306 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-08-01 00:41:47,306 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-08-01 00:41:47,306 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-08-01 00:41:47,306 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-08-01 00:41:47,306 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-08-01 00:41:47,306 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-08-01 00:41:47,306 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-08-01 00:41:47,331 - Main - INFO - info:307 - ℹ️ 🎯 获取特定分钟数据: 000617 @ 202507251500
2025-08-01 00:41:47,331 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-08-01 00:41:47,519 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1440条 (目标日期: 20250725, 交易日: 6天, 频率: 1min)
2025-08-01 00:41:47,519 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1440条 -> 实际请求800条 (配置限制:800)
2025-08-01 00:41:47,519 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 00:41:47,519 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 00:41:47,569 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需640条
2025-08-01 00:41:47,801 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +640条，总计1440条
2025-08-01 00:41:47,801 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1440条数据
2025-08-01 00:41:47,802 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1440条，实际获取1440条
2025-08-01 00:41:47,807 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-01 00:41:47,810 - Main - INFO - info:307 - ℹ️ ℹ️ pytdx提供未复权原始价格，正在计算前复权价格以完善数据字段...
2025-08-01 00:41:47,810 - Main - INFO - info:307 - ℹ️ 🔄 pytdx前复权计算: 000617
2025-08-01 00:41:47,810 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-08-01 00:41:47,810 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-08-01 00:41:48,242 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-08-01 00:41:48,242 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-08-01 00:41:48,249 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-08-01 00:41:48,249 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17/T0002
2025-08-01 00:41:48,249 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-08-01 00:41:48,250 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-08-01 00:41:48,250 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 未找到任何市场的分钟数据文件
2025-08-01 00:41:48,250 - main_v20230219_optimized - WARNING - _get_optimal_tdx_path:255 - ⚠️  主路径下未找到分钟数据文件，但将继续使用配置路径
2025-08-01 00:41:48,250 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17/T0002，缓存方法: memory
2025-08-01 00:42:00,435 - cache.gbbq_cache - ERROR - _load_all_data:288 - 加载GBBQ数据失败: '类别'
2025-08-01 00:42:12,085 - main_v20230219_optimized - INFO - _ensure_pickle_cache:441 - 🔄 更新pickle缓存...
2025-08-01 00:42:12,085 - main_v20230219_optimized - ERROR - _ensure_pickle_cache:495 - ❌ pickle缓存更新失败: 'StockDataProcessor' object has no attribute 'tdx_path'
2025-08-01 00:42:12,643 - main_v20230219_optimized - WARNING - _extract_stock_code_from_data:2515 - 无法从数据中提取股票代码，使用target_stocks中的第一个
2025-08-01 00:42:12,653 - main_v20230219_optimized - INFO - apply_forward_adjustment:2203 - 保留交易时间数据后: 240条
2025-08-01 00:42:12,654 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功，处理了20个除权事件
2025-08-01 00:42:12,668 - Main - INFO - info:307 - ℹ️ ✅ pytdx前复权计算成功
2025-08-01 00:42:12,668 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价格与原始价格相同，可能无除权事件
2025-08-01 00:42:12,668 - Main - INFO - info:307 - ℹ️ 📊 获取到 240 条数据，查找目标时间 202507251500
2025-08-01 00:42:12,672 - Main - INFO - info:307 - ℹ️ ✅ 找到目标数据: 收盘价=8.900, 前复权=nan
2025-08-01 00:42:12,673 - Main - INFO - info:307 - ℹ️ ✅ 增量下载前提条件验证通过
2025-08-01 00:42:12,673 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-08-01 00:42:12,673 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-08-01 00:42:12,707 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数21291, 覆盖89个交易日
2025-08-01 00:42:12,707 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-08-01 00:42:12,707 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-08-01 00:42:12,707 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-08-01 00:42:12,707 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-08-01 00:42:12,707 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-08-01 00:42:12,707 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-08-01 00:42:12,707 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-08-01 00:42:12,707 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-08-01 00:42:12,707 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-08-01 00:42:12,707 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-08-01 00:42:13,075 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-08-01 00:42:13,076 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-08-01 00:42:13,076 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_202503180931-202507251500_来源互联网（202507312355）.txt
2025-08-01 00:42:13,076 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_202503180931-202507251500_来源互联网（202507312355）.txt
2025-08-01 00:42:13,076 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-08-01 00:42:13,094 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成 (测试模式: False)
2025-08-01 00:42:13,319 - core.logging_service - ERROR - log_error:133 - 🚨 关键错误 【前置验证】: 前置验证失败: 无法获取API对比数据
2025-08-01 00:42:13,321 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 00:42:13,321 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 00:42:13,321 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-08-01 00:42:13,500 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 1440条 (目标日期: 20250725, 交易日: 6天, 频率: 1min)
2025-08-01 00:42:13,500 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计1440条 -> 实际请求800条 (配置限制:800)
2025-08-01 00:42:13,500 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 00:42:13,500 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 00:42:13,554 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需640条
2025-08-01 00:42:13,777 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +640条，总计1440条
2025-08-01 00:42:13,777 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计1440条数据
2025-08-01 00:42:13,777 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要1440条，实际获取1440条
2025-08-01 00:42:13,782 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-08-01 00:42:13,788 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-08-01 00:42:13,789 - Main - WARNING - warning:311 - ⚠️ ⚠️ 无法解析文件名格式
2025-08-01 00:42:13,789 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-08-01 00:42:13,789 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-08-01 00:42:13,789 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-08-01 00:42:13,789 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-08-01 00:42:13,967 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 33600条 (目标日期: 20250101, 交易日: 140天, 频率: 1min)
2025-08-01 00:42:13,967 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计33600条 -> 实际请求800条 (配置限制:800)
2025-08-01 00:42:13,967 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-08-01 00:42:13,967 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-08-01 00:42:14,018 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32800条
2025-08-01 00:42:14,247 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-08-01 00:42:14,466 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-08-01 00:42:14,686 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-08-01 00:42:14,916 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-08-01 00:42:15,136 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-08-01 00:42:15,368 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-08-01 00:42:15,602 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-08-01 00:42:15,821 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-08-01 00:42:16,050 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-08-01 00:42:16,277 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-08-01 00:42:16,502 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-08-01 00:42:16,724 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-08-01 00:42:16,950 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-08-01 00:42:17,183 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-08-01 00:42:17,413 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-08-01 00:42:17,634 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-08-01 00:42:17,851 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-08-01 00:42:18,074 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-08-01 00:42:18,300 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-08-01 00:42:18,532 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-08-01 00:42:18,762 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-08-01 00:42:18,980 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-08-01 00:42:19,199 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-08-01 00:42:19,422 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-08-01 00:42:19,667 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-08-01 00:42:19,889 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-08-01 00:42:20,112 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +720条，总计22320条
2025-08-01 00:42:20,112 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计22320条数据
2025-08-01 00:42:20,117 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要33600条，实际获取22320条
2025-08-01 00:42:20,117 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11280条数据（约47个交易日）
2025-08-01 00:42:20,117 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-08-01 00:42:20,117 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-08-01 00:42:20,117 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-08-01 00:42:20,166 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-08-01 00:42:20,293 - Main - INFO - info:307 - ℹ️ ✅ 数据保存完成: 1min_0_000617_20250318-20250725_来源互联网（202508010042）.txt (999617 字节)
2025-08-01 00:42:20,294 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-08-01 00:42:20,294 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-08-01 00:42:20,294 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-08-01 00:42:20,295 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-08-01 00:42:20,295 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-08-01 00:42:20,295 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-08-01 00:42:20,295 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-08-01 00:42:20,295 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-08-01 00:42:20,295 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-08-01 00:42:20,295 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250801_004220.txt
2025-08-01 00:42:20,295 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-08-01 00:42:20,296 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-08-01 00:42:20,296 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-08-01 00:42:20,296 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-08-01 00:42:20,296 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-08-01 00:42:20,296 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-08-01 00:42:20,296 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-08-01 00:42:20,296 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-08-01 00:42:20,296 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-08-01 00:42:20,296 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-08-01 00:42:20,296 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
