#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简化版正确测试环境检测器
"""

import os
import sys
import time

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
test_env_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(os.path.dirname(test_env_dir))

# 强制切换到测试环境
os.chdir(test_env_dir)
sys.path.insert(0, project_root)

print("=== Test Environment Setup ===")
print(f"Current Directory: {os.getcwd()}")
print(f"Project Root: {project_root}")

# 验证测试文件
TARGET_FILE = "input_data/test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
if not os.path.exists(TARGET_FILE):
    print(f"ERROR: Target test file not found: {TARGET_FILE}")
    sys.exit(1)

print(f"Target test file found: {TARGET_FILE}")

# 检查文件信息
file_size = os.path.getsize(TARGET_FILE)
with open(TARGET_FILE, 'r', encoding='utf-8') as f:
    lines = sum(1 for _ in f)
print(f"File info: {file_size} bytes, {lines} lines")

# 全局状态
taskmanager_time = None
workflow_time = None
early_calls = []

def install_monitoring():
    """安装监控"""
    global taskmanager_time, workflow_time, early_calls
    
    try:
        # 监控TaskManager
        from src.mythquant.core.task_manager import TaskManager
        original_execute = TaskManager._execute_minute_task
        
        def monitored_execute(self, task, target_stocks):
            global taskmanager_time
            taskmanager_time = time.time()
            print(f"\n*** TASKMANAGER START: {taskmanager_time} ***")
            print(f"Working in: {os.getcwd()}")
            
            result = original_execute(self, task, target_stocks)
            print(f"*** TASKMANAGER END ***")
            return result
        
        TaskManager._execute_minute_task = monitored_execute
        
        # 监控五步流程
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        original_five_step = StructuredInternetMinuteDownloader._execute_five_step_process
        
        def monitored_five_step(self, stock_code, start_date, end_date, frequency, original_frequency):
            global workflow_time
            workflow_time = time.time()
            print(f"\n*** WORKFLOW START: {workflow_time} ***")
            return original_five_step(self, stock_code, start_date, end_date, frequency, original_frequency)
        
        StructuredInternetMinuteDownloader._execute_five_step_process = monitored_five_step
        
        # 强制使用测试文件
        from utils.smart_file_selector import SmartFileSelector
        original_find = SmartFileSelector.find_best_file
        
        def force_test_file(self, stock_code, data_type, target_start, target_end, strategy='smart_comprehensive'):
            print(f"\n*** FORCING TEST FILE SELECTION ***")
            print(f"Target file: {TARGET_FILE}")
            
            if os.path.exists(TARGET_FILE):
                file_info = {
                    'file_path': TARGET_FILE,
                    'start_date': '20250320',
                    'end_date': '20250704',
                    'total_days': 107,
                    'file_size': os.path.getsize(TARGET_FILE),
                    'quality_score': 100.0
                }
                return TARGET_FILE, file_info
            else:
                return original_find(self, stock_code, data_type, target_start, target_end, strategy)
        
        SmartFileSelector.find_best_file = force_test_file
        
        # 监控pytdx调用
        from utils.pytdx_downloader import PytdxDownloader
        original_download = PytdxDownloader.download_minute_data
        
        def monitored_download(self, stock_code, start_date, end_date, frequency='1min', suppress_warnings=False):
            global taskmanager_time, workflow_time, early_calls
            current_time = time.time()
            
            print(f"\n*** PYTDX DOWNLOAD CALL ***")
            print(f"Time: {current_time}")
            print(f"Stock: {stock_code}, Range: {start_date}-{end_date}")
            print(f"TaskManager time: {taskmanager_time}")
            print(f"Workflow time: {workflow_time}")
            
            # 检查是否提前调用
            if taskmanager_time and workflow_time:
                if taskmanager_time < current_time < workflow_time:
                    print("!!! EARLY CALL DETECTED !!!")
                    early_calls.append({
                        'stock_code': stock_code,
                        'date_range': f"{start_date}-{end_date}",
                        'time': current_time
                    })
            elif taskmanager_time and not workflow_time:
                time_diff = current_time - taskmanager_time
                if time_diff > 0.1:
                    print("!!! EARLY CALL DETECTED (before workflow) !!!")
                    early_calls.append({
                        'stock_code': stock_code,
                        'date_range': f"{start_date}-{end_date}",
                        'time': current_time
                    })
            
            return original_download(self, stock_code, start_date, end_date, frequency, suppress_warnings)
        
        PytdxDownloader.download_minute_data = monitored_download
        
        print("Monitoring installed")
        return True
        
    except Exception as e:
        print(f"Monitoring failed: {e}")
        return False

def run_test():
    """运行测试"""
    print(f"\n=== Running test in correct environment ===")
    print(f"Current dir: {os.getcwd()}")
    print(f"Target file: {TARGET_FILE}")
    
    try:
        from main import main
        result = main()
        print(f"Test result: {result}")
        return True
    except Exception as e:
        print(f"Test failed: {e}")
        return False

def analyze_results():
    """分析结果"""
    print(f"\n=== Analysis Results ===")
    print(f"Early calls detected: {len(early_calls)}")
    
    if early_calls:
        for i, call in enumerate(early_calls, 1):
            print(f"Early call #{i}:")
            print(f"  Stock: {call['stock_code']}")
            print(f"  Range: {call['date_range']}")
            print(f"  Time: {call['time']}")
    else:
        print("No early calls detected")

def main():
    """主函数"""
    print("Correct Test Environment Detector")
    print("=" * 50)
    
    if not install_monitoring():
        return 1
    
    success = run_test()
    analyze_results()
    
    print(f"\nTest completed: {success}")
    print(f"Early calls: {len(early_calls)}")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
