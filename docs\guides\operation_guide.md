# MythQuant 程序运行指南

## 主程序入口

### 直接运行
经过模块化重构后，程序的运行方式**完全保持不变**：

```bash
python main_v20230219_optimized.py
```

## 运行前检查

### 1. 依赖检查
确保所有依赖模块都已正确安装：
```bash
python dependency_check.py
```

### 2. 配置检查
确认 `user_config.py` 中的关键配置：
- `tdx_path` - 通达信数据路径
- `target_stocks_file` - 目标股票列表文件
- `output_base_path` - 输出路径
- 任务开关（daily_task_enabled、minute_task_enabled等）

### 3. 模块化验证（可选）
如果想验证新的模块化架构是否正常：
```bash
python test_modular_refactor.py
```

## 常用运行场景

### 场景1：完整数据生成
```bash
# 生成所有配置的数据类型（日线、分钟线等）
python main_v20230219_optimized.py
```

### 场景2：调试模式运行
在 `user_config.py` 中启用详细模式：
```python
verbose_mode = {
    'enabled': True,
    'show_detailed_calculations': True,
    'show_forward_adj_details': True
}
```
然后运行：
```bash
python main_v20230219_optimized.py
```

### 场景3：单独测试某个功能
```bash
# 测试前复权功能
python test_corrected_forward_adj.py

# 测试数据输出
python test_main_output.py

# 测试GBBQ性能
python test_enhanced_gbbq_performance.py
```

## 输出说明

### 启动信息
程序启动时会显示：
- 📁 TDX路径配置
- 📊 目标股票列表
- 🔧 任务配置状态  
- 💾 缓存状态
- ⚙️  精度设置

### 处理过程
运行中会显示：
- 🔄 股票处理进度
- 📈 数据生成状态
- ⚠️  警告和错误信息
- 📊 性能统计

### 完成信息  
程序结束时会显示：
- 🎯 处理效率统计
- ⏱️  总耗时
- 🎉 任务完成状态

## 输出文件位置

根据 `user_config.py` 中的配置，输出文件将生成在：
- **默认路径**: `H:/MPV1.17/T0002/signals/`
- **日线数据**: `day_*.txt`
- **分钟数据**: `min_*.txt` 
- **信号数据**: `datacfg.dat`

## 故障排除

### 模块导入错误
如果出现导入错误：
```bash
# 检查Python路径
python -c "import sys; print('\n'.join(sys.path))"

# 检查模块是否存在
python -c "import utils; import core; print('模块导入成功')"
```

### 配置问题
如果出现配置相关错误：
```bash
# 检查配置文件
python -c "import user_config; print('配置文件正常')"

# 检查配置管理器
python -c "from core import config_manager; print('配置管理器正常')"
```

### 回滚到重构前版本（如有需要）
```bash
# 使用备份版本
python main_v20230219_optimized_backup.py
```

## 注意事项

1. **向后兼容性**: 所有原有的运行方式和参数都保持不变
2. **新功能**: 新的模块化架构提供了更好的错误提示和日志信息
3. **性能**: 模块化后的版本性能与原版本基本一致
4. **配置**: 可以通过 `user_config.py` 调整详细日志级别和输出选项

## 快速开始

对于首次使用：
```bash
# 1. 检查依赖
python dependency_check.py

# 2. 检查配置
# 编辑 user_config.py 设置正确的路径

# 3. 运行程序
python main_v20230219_optimized.py
```

程序会自动处理所有配置的任务并生成相应的输出文件。 