#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
执行项目根目录清理脚本
安全地清理和归档项目根目录中的无关文件夹
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class RootDirectoryCleanup:
    """根目录清理执行器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backup_root = self.project_root.parent / f'MythQuant_backup_{datetime.now().strftime("%Y%m%d_%H%M%S")}'
        self.archive_root = self.project_root / 'archive'
        self.cleanup_log = []
        
    def create_full_backup(self):
        """创建完整项目备份"""
        print("📦 创建完整项目备份...")
        
        try:
            # 排除一些不需要备份的目录
            def ignore_patterns(dir, files):
                return [f for f in files if f in ['__pycache__', '.git', 'node_modules']]
            
            shutil.copytree(self.project_root, self.backup_root, ignore=ignore_patterns)
            print(f"   ✅ 备份创建完成: {self.backup_root}")
            self.cleanup_log.append(f"创建完整备份: {self.backup_root}")
            return True
        except Exception as e:
            print(f"   ❌ 备份创建失败: {e}")
            return False
    
    def create_archive_structure(self):
        """创建归档目录结构"""
        print("🏗️ 创建归档目录结构...")
        
        archive_dirs = {
            'backups': '历史备份归档',
            'legacy': '遗留代码归档',
            'temp_files': '临时文件归档',
            'logs': '旧日志归档'
        }
        
        for dir_name, description in archive_dirs.items():
            archive_dir = self.archive_root / dir_name
            archive_dir.mkdir(parents=True, exist_ok=True)
            
            # 创建说明文件
            readme_file = archive_dir / 'README.md'
            with open(readme_file, 'w', encoding='utf-8') as f:
                f.write(f"# {description}\n\n")
                f.write(f"归档时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
                f.write(f"归档原因: DDD架构升级后的项目结构清理\n\n")
        
        print(f"   ✅ 归档结构创建完成: {self.archive_root}")
        self.cleanup_log.append(f"创建归档结构: {self.archive_root}")
    
    def delete_temporary_directories(self):
        """删除临时目录"""
        print("🗑️ 删除临时目录...")
        
        temp_dirs = ['__pycache__']
        
        for temp_dir in temp_dirs:
            temp_path = self.project_root / temp_dir
            if temp_path.exists():
                try:
                    shutil.rmtree(temp_path)
                    print(f"   ✅ 删除: {temp_dir}")
                    self.cleanup_log.append(f"删除临时目录: {temp_dir}")
                except Exception as e:
                    print(f"   ⚠️ 删除失败 {temp_dir}: {e}")
    
    def archive_backup_directories(self):
        """归档备份目录"""
        print("📦 归档备份目录...")
        
        backup_dirs = [
            'architecture_migration_final_archive_20250802_233447',
            'backup_test_integration_20250803_232702'
        ]
        
        target_dir = self.archive_root / 'backups'
        
        for backup_dir in backup_dirs:
            source_path = self.project_root / backup_dir
            if source_path.exists():
                try:
                    target_path = target_dir / backup_dir
                    shutil.move(str(source_path), str(target_path))
                    print(f"   ✅ 归档: {backup_dir}")
                    self.cleanup_log.append(f"归档备份目录: {backup_dir} -> archive/backups/")
                except Exception as e:
                    print(f"   ⚠️ 归档失败 {backup_dir}: {e}")
    
    def archive_legacy_directories(self):
        """归档遗留目录"""
        print("📚 归档遗留目录...")
        
        legacy_dirs = ['environments', 'lib', 'assets']
        target_dir = self.archive_root / 'legacy'
        
        for legacy_dir in legacy_dirs:
            source_path = self.project_root / legacy_dir
            if source_path.exists():
                try:
                    target_path = target_dir / legacy_dir
                    shutil.move(str(source_path), str(target_path))
                    print(f"   ✅ 归档: {legacy_dir}")
                    self.cleanup_log.append(f"归档遗留目录: {legacy_dir} -> archive/legacy/")
                except Exception as e:
                    print(f"   ⚠️ 归档失败 {legacy_dir}: {e}")
    
    def archive_temporary_files(self):
        """归档临时文件"""
        print("📄 归档临时文件...")
        
        temp_files = [
            'comprehensive_1min_test.py',
            'comprehensive_test_with_proper_config.py',
            'comprehensive_test_report.md',
            'architecture_cleanup_report_20241220.json',
            'cleanup_report_20250802_233731.txt',
            'test_integration_report_20250803_232702.json'
        ]
        
        target_dir = self.archive_root / 'temp_files'
        
        for temp_file in temp_files:
            source_path = self.project_root / temp_file
            if source_path.exists():
                try:
                    target_path = target_dir / temp_file
                    shutil.move(str(source_path), str(target_path))
                    print(f"   ✅ 归档: {temp_file}")
                    self.cleanup_log.append(f"归档临时文件: {temp_file} -> archive/temp_files/")
                except Exception as e:
                    print(f"   ⚠️ 归档失败 {temp_file}: {e}")
    
    def clean_old_logs(self):
        """清理旧日志文件"""
        print("🧹 清理旧日志文件...")
        
        logs_dir = self.project_root / 'logs'
        archive_logs_dir = self.archive_root / 'logs'
        
        if not logs_dir.exists():
            print("   ℹ️ logs目录不存在")
            return
        
        # 获取30天前的时间戳
        thirty_days_ago = datetime.now().timestamp() - (30 * 24 * 60 * 60)
        
        archived_count = 0
        for log_file in logs_dir.iterdir():
            if log_file.is_file() and log_file.suffix in ['.log', '.json']:
                # 检查文件修改时间
                if log_file.stat().st_mtime < thirty_days_ago:
                    try:
                        target_path = archive_logs_dir / log_file.name
                        shutil.move(str(log_file), str(target_path))
                        archived_count += 1
                    except Exception as e:
                        print(f"   ⚠️ 归档日志失败 {log_file.name}: {e}")
        
        print(f"   ✅ 归档了 {archived_count} 个旧日志文件")
        self.cleanup_log.append(f"归档旧日志文件: {archived_count} 个")
    
    def clean_data_directory(self):
        """清理数据目录"""
        print("🗂️ 清理数据目录...")
        
        data_dir = self.project_root / 'data'
        if not data_dir.exists():
            print("   ℹ️ data目录不存在")
            return
        
        # 保留重要配置文件
        keep_files = ['tdx_servers.json']
        archive_data_dir = self.archive_root / 'temp_files'
        
        archived_count = 0
        for data_file in data_dir.iterdir():
            if data_file.is_file() and data_file.name not in keep_files:
                try:
                    target_path = archive_data_dir / f"data_{data_file.name}"
                    shutil.move(str(data_file), str(target_path))
                    archived_count += 1
                except Exception as e:
                    print(f"   ⚠️ 归档数据文件失败 {data_file.name}: {e}")
        
        print(f"   ✅ 归档了 {archived_count} 个临时数据文件")
        self.cleanup_log.append(f"归档临时数据文件: {archived_count} 个")
    
    def validate_cleanup_result(self):
        """验证清理结果"""
        print("✅ 验证清理结果...")
        
        # 检查核心目录是否完整
        core_dirs = ['src', 'tests', 'docs', 'scripts', 'test_environments']
        missing_dirs = []
        
        for core_dir in core_dirs:
            if not (self.project_root / core_dir).exists():
                missing_dirs.append(core_dir)
        
        if missing_dirs:
            print(f"   ⚠️ 核心目录缺失: {missing_dirs}")
            return False
        
        # 统计当前根目录内容
        current_dirs = [d.name for d in self.project_root.iterdir() if d.is_dir()]
        current_files = [f.name for f in self.project_root.iterdir() if f.is_file()]
        
        print(f"   📊 当前目录数: {len(current_dirs)}")
        print(f"   📊 当前文件数: {len(current_files)}")
        print(f"   📊 核心目录完整: ✅")
        
        return True
    
    def generate_cleanup_report(self):
        """生成清理报告"""
        print("📋 生成清理报告...")
        
        report = {
            'cleanup_time': datetime.now().isoformat(),
            'backup_location': str(self.backup_root),
            'archive_location': str(self.archive_root),
            'cleanup_operations': self.cleanup_log,
            'final_structure': {
                'directories': [d.name for d in self.project_root.iterdir() if d.is_dir()],
                'files': [f.name for f in self.project_root.iterdir() if f.is_file()]
            }
        }
        
        report_file = self.project_root / f'root_cleanup_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ 清理报告已生成: {report_file}")
        return report_file
    
    def execute_cleanup(self):
        """执行完整清理流程"""
        print("🎯 开始执行项目根目录清理")
        print("=" * 60)
        
        try:
            # 1. 创建完整备份
            if not self.create_full_backup():
                print("❌ 备份创建失败，停止清理操作")
                return False
            
            # 2. 创建归档结构
            self.create_archive_structure()
            
            # 3. 删除临时目录
            self.delete_temporary_directories()
            
            # 4. 归档备份目录
            self.archive_backup_directories()
            
            # 5. 归档遗留目录
            self.archive_legacy_directories()
            
            # 6. 归档临时文件
            self.archive_temporary_files()
            
            # 7. 清理旧日志
            self.clean_old_logs()
            
            # 8. 清理数据目录
            self.clean_data_directory()
            
            # 9. 验证清理结果
            if not self.validate_cleanup_result():
                print("⚠️ 清理结果验证失败")
                return False
            
            # 10. 生成清理报告
            report_file = self.generate_cleanup_report()
            
            print(f"\n🎉 项目根目录清理完成！")
            print(f"📦 完整备份: {self.backup_root}")
            print(f"📁 归档位置: {self.archive_root}")
            print(f"📋 清理报告: {report_file}")
            
            return True
            
        except Exception as e:
            print(f"\n❌ 清理过程出现异常: {e}")
            import traceback
            traceback.print_exc()
            return False


def main():
    """主函数"""
    cleanup = RootDirectoryCleanup()
    
    print("⚠️  重要提示:")
    print("   此操作将清理项目根目录结构")
    print("   删除临时目录，归档遗留目录和备份")
    print("   操作前会自动创建完整项目备份")
    print("   清理后项目结构将更加清晰和专业")
    print()
    
    print("📋 清理计划:")
    print("   🗑️ 删除: __pycache__ 等临时目录")
    print("   📦 归档: 历史备份目录")
    print("   📚 归档: environments, lib, assets 等遗留目录")
    print("   📄 归档: 临时测试文件和报告")
    print("   🧹 清理: 30天前的旧日志文件")
    print("   🗂️ 清理: data目录中的临时文件")
    print()
    
    response = input("是否继续执行根目录清理? (y/N): ")
    if response.lower() != 'y':
        print("❌ 用户取消操作")
        return False
    
    success = cleanup.execute_cleanup()
    
    if success:
        print("\n🎉 项目根目录清理成功完成！")
        print("\n📋 下一步建议:")
        print("1. 验证项目功能是否正常 (运行 python main.py)")
        print("2. 运行测试确保无问题 (python -m pytest)")
        print("3. 检查归档的文件是否还需要")
        print("4. 如果一切正常，可以删除备份目录")
        print("5. 更新团队成员关于项目结构变更")
        return True
    else:
        print("\n❌ 项目清理失败！")
        print("请检查错误信息，必要时从备份恢复")
        print(f"备份位置: {cleanup.backup_root}")
        return False


if __name__ == '__main__':
    success = main()
    exit(0 if success else 1)
