#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境合规性检查脚本

用于自动检查是否遵守测试环境规范，防止在故障排查时使用生产环境数据。

使用方法:
    python scripts/check_test_environment_compliance.py
    python scripts/check_test_environment_compliance.py --file path/to/file.txt
    python scripts/check_test_environment_compliance.py --scan-recent

作者: AI Assistant
创建时间: 2025-07-29
"""

import os
import sys
import argparse
import glob
from datetime import datetime, timedelta
from typing import List

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.test_environment_validator import TestEnvironmentValidator


def scan_recent_files(hours: int = 24) -> List[str]:
    """扫描最近修改的文件"""
    recent_files = []
    
    # 扫描生产环境目录
    production_patterns = [
        "H:/MPV1.17/T0002/signals/*.txt",
        "H:/MPV1.17/T0002/signals/**/*.txt"
    ]
    
    cutoff_time = datetime.now() - timedelta(hours=hours)
    
    for pattern in production_patterns:
        try:
            files = glob.glob(pattern, recursive=True)
            for file_path in files:
                try:
                    mtime = datetime.fromtimestamp(os.path.getmtime(file_path))
                    if mtime > cutoff_time:
                        recent_files.append(file_path)
                except (OSError, ValueError):
                    continue
        except Exception:
            continue
    
    return recent_files


def check_ai_analysis_history() -> List[str]:
    """检查AI分析历史中可能使用的文件"""
    suspicious_files = []
    
    # 检查日志文件中提到的文件路径
    log_patterns = [
        "logs/*.log",
        "*.log"
    ]
    
    for pattern in log_patterns:
        try:
            log_files = glob.glob(pattern)
            for log_file in log_files:
                try:
                    with open(log_file, 'r', encoding='utf-8', errors='ignore') as f:
                        content = f.read()
                        
                        # 查找可能的文件路径
                        import re
                        file_patterns = [
                            r'H:/MPV1\.17/T0002/signals/[^/\s]+\.txt',
                            r'1min_0_\d+_\d+-\d+.*\.txt',
                            r'day_0_\d+_\d+-\d+.*\.txt'
                        ]
                        
                        for file_pattern in file_patterns:
                            matches = re.findall(file_pattern, content)
                            for match in matches:
                                if not match.startswith("H:/MPV1.17/T0002/signals/TestCase/"):
                                    suspicious_files.append(match)
                
                except Exception:
                    continue
        except Exception:
            continue
    
    return list(set(suspicious_files))  # 去重


def main():
    """主函数"""
    parser = argparse.ArgumentParser(description='测试环境合规性检查')
    parser.add_argument('--file', '-f', help='检查指定文件')
    parser.add_argument('--scan-recent', '-r', action='store_true', help='扫描最近修改的文件')
    parser.add_argument('--hours', type=int, default=24, help='扫描最近N小时的文件（默认24小时）')
    parser.add_argument('--check-logs', '-l', action='store_true', help='检查日志中的文件引用')
    parser.add_argument('--verbose', '-v', action='store_true', help='详细输出')
    
    args = parser.parse_args()
    
    print("🔍 测试环境合规性检查工具")
    print("=" * 60)
    print(f"检查时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    validator = TestEnvironmentValidator()
    files_to_check = []
    
    # 根据参数确定要检查的文件
    if args.file:
        files_to_check = [args.file]
        print(f"📁 检查指定文件: {args.file}")
    
    elif args.scan_recent:
        print(f"🔍 扫描最近 {args.hours} 小时修改的文件...")
        recent_files = scan_recent_files(args.hours)
        files_to_check.extend(recent_files)
        print(f"📊 找到 {len(recent_files)} 个最近修改的文件")
    
    if args.check_logs:
        print(f"📋 检查日志中的文件引用...")
        log_files = check_ai_analysis_history()
        files_to_check.extend(log_files)
        print(f"📊 从日志中找到 {len(log_files)} 个可疑文件引用")
    
    # 如果没有指定文件，显示帮助
    if not files_to_check:
        print("ℹ️ 未指定要检查的文件")
        validator.print_checklist()
        print(f"\n💡 使用示例:")
        print(f"  python {sys.argv[0]} --scan-recent")
        print(f"  python {sys.argv[0]} --file path/to/file.txt")
        print(f"  python {sys.argv[0]} --check-logs")
        return
    
    # 去重
    files_to_check = list(set(files_to_check))
    
    if args.verbose:
        print(f"\n📋 待检查文件列表:")
        for i, file_path in enumerate(files_to_check, 1):
            print(f"  {i}. {file_path}")
    
    print(f"\n🔍 开始检查 {len(files_to_check)} 个文件...")
    print("-" * 60)
    
    # 执行验证
    validation_result = validator.validate_analysis_environment(files_to_check)
    
    # 输出总结
    print(f"\n📊 检查总结:")
    print("=" * 60)
    
    if validation_result['is_compliant']:
        print("✅ 所有文件都符合测试环境规范")
        exit_code = 0
    else:
        print("❌ 发现违规行为！")
        print(f"🚨 违规文件数量: {len([f for f, r in validation_result['file_checks'].items() if not r['is_valid']])}")
        print(f"⚠️ 警告文件数量: {len([f for f, r in validation_result['file_checks'].items() if r['warnings']])}")
        
        print(f"\n🔧 建议措施:")
        print("  1. 立即停止使用生产环境数据进行分析")
        print("  2. 将相关文件复制到测试环境")
        print("  3. 使用test_前缀重命名测试文件")
        print("  4. 设置测试文件为只读保护")
        print("  5. 重新在测试环境中进行分析")
        
        exit_code = 1
    
    if validation_result['warnings']:
        print(f"\n⚠️ 需要注意的问题:")
        for warning in validation_result['warnings']:
            print(f"  - {warning}")
    
    print(f"\n📋 合规性检查完成")
    print(f"检查文件数: {len(files_to_check)}")
    print(f"合规文件数: {len([f for f, r in validation_result['file_checks'].items() if r['is_valid']])}")
    print(f"违规文件数: {len([f for f, r in validation_result['file_checks'].items() if not r['is_valid']])}")
    
    sys.exit(exit_code)


if __name__ == '__main__':
    main()
