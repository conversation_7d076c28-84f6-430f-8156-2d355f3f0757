#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件存储实现

提供事件的持久化存储和检索功能，支持事件溯源
"""

import json
import os
import threading
from abc import ABC, abstractmethod
from typing import List, Dict, Any, Optional, Iterator
from datetime import datetime
from pathlib import Path
import sqlite3
from dataclasses import asdict

from ...shared.patterns.architectural_patterns import DomainEvent
from ...domain.exceptions import DomainException


class EventStoreException(DomainException):
    """事件存储异常"""
    pass


class EventRecord:
    """事件记录"""
    
    def __init__(self, event: DomainEvent, sequence_number: int, 
                 stored_at: Optional[datetime] = None):
        self.event = event
        self.sequence_number = sequence_number
        self.stored_at = stored_at or datetime.now()
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'event_id': self.event.event_id,
            'aggregate_id': self.event.aggregate_id,
            'event_type': self.event.event_type,
            'occurred_at': self.event.occurred_at.isoformat(),
            'version': self.event.version,
            'data': self.event.data,
            'sequence_number': self.sequence_number,
            'stored_at': self.stored_at.isoformat()
        }
    
    @classmethod
    def from_dict(cls, data: Dict[str, Any]) -> 'EventRecord':
        """从字典创建"""
        # 重建领域事件
        event = DomainEvent(
            event_id=data['event_id'],
            aggregate_id=data['aggregate_id'],
            event_type=data['event_type'],
            occurred_at=datetime.fromisoformat(data['occurred_at']),
            version=data['version'],
            data=data['data']
        )
        
        return cls(
            event=event,
            sequence_number=data['sequence_number'],
            stored_at=datetime.fromisoformat(data['stored_at'])
        )


class EventStore(ABC):
    """事件存储抽象基类"""
    
    @abstractmethod
    def append_event(self, event: DomainEvent) -> int:
        """追加事件，返回序列号"""
        pass
    
    @abstractmethod
    def append_events(self, events: List[DomainEvent]) -> List[int]:
        """批量追加事件"""
        pass
    
    @abstractmethod
    def get_events(self, aggregate_id: str, 
                   from_version: int = 0) -> List[EventRecord]:
        """获取聚合的事件"""
        pass
    
    @abstractmethod
    def get_events_by_type(self, event_type: str,
                          from_sequence: int = 0,
                          limit: int = 100) -> List[EventRecord]:
        """按类型获取事件"""
        pass
    
    @abstractmethod
    def get_all_events(self, from_sequence: int = 0,
                      limit: int = 100) -> List[EventRecord]:
        """获取所有事件"""
        pass
    
    @abstractmethod
    def get_event_count(self) -> int:
        """获取事件总数"""
        pass
    
    @abstractmethod
    def get_last_sequence_number(self) -> int:
        """获取最后的序列号"""
        pass


class InMemoryEventStore(EventStore):
    """内存事件存储"""
    
    def __init__(self):
        self._events: List[EventRecord] = []
        self._aggregate_events: Dict[str, List[EventRecord]] = {}
        self._type_events: Dict[str, List[EventRecord]] = {}
        self._sequence_counter = 0
        self._lock = threading.RLock()
    
    def append_event(self, event: DomainEvent) -> int:
        """追加单个事件"""
        with self._lock:
            self._sequence_counter += 1
            record = EventRecord(event, self._sequence_counter)
            
            # 存储到主列表
            self._events.append(record)
            
            # 按聚合ID索引
            if event.aggregate_id not in self._aggregate_events:
                self._aggregate_events[event.aggregate_id] = []
            self._aggregate_events[event.aggregate_id].append(record)
            
            # 按事件类型索引
            if event.event_type not in self._type_events:
                self._type_events[event.event_type] = []
            self._type_events[event.event_type].append(record)
            
            return self._sequence_counter
    
    def append_events(self, events: List[DomainEvent]) -> List[int]:
        """批量追加事件"""
        sequence_numbers = []
        for event in events:
            seq_num = self.append_event(event)
            sequence_numbers.append(seq_num)
        return sequence_numbers
    
    def get_events(self, aggregate_id: str, 
                   from_version: int = 0) -> List[EventRecord]:
        """获取聚合的事件"""
        with self._lock:
            aggregate_events = self._aggregate_events.get(aggregate_id, [])
            return [record for record in aggregate_events 
                   if record.event.version >= from_version]
    
    def get_events_by_type(self, event_type: str,
                          from_sequence: int = 0,
                          limit: int = 100) -> List[EventRecord]:
        """按类型获取事件"""
        with self._lock:
            type_events = self._type_events.get(event_type, [])
            filtered = [record for record in type_events 
                       if record.sequence_number >= from_sequence]
            return filtered[:limit]
    
    def get_all_events(self, from_sequence: int = 0,
                      limit: int = 100) -> List[EventRecord]:
        """获取所有事件"""
        with self._lock:
            filtered = [record for record in self._events 
                       if record.sequence_number >= from_sequence]
            return filtered[:limit]
    
    def get_event_count(self) -> int:
        """获取事件总数"""
        with self._lock:
            return len(self._events)
    
    def get_last_sequence_number(self) -> int:
        """获取最后的序列号"""
        with self._lock:
            return self._sequence_counter
    
    def clear(self):
        """清除所有事件"""
        with self._lock:
            self._events.clear()
            self._aggregate_events.clear()
            self._type_events.clear()
            self._sequence_counter = 0


class FileEventStore(EventStore):
    """文件事件存储"""
    
    def __init__(self, storage_path: str = "events"):
        self.storage_path = Path(storage_path)
        self.storage_path.mkdir(exist_ok=True)
        self._sequence_file = self.storage_path / "sequence.txt"
        self._events_file = self.storage_path / "events.jsonl"
        self._lock = threading.RLock()
        
        # 初始化序列号
        self._sequence_counter = self._load_sequence_number()
    
    def append_event(self, event: DomainEvent) -> int:
        """追加单个事件"""
        with self._lock:
            self._sequence_counter += 1
            record = EventRecord(event, self._sequence_counter)
            
            # 写入事件文件
            with open(self._events_file, 'a', encoding='utf-8') as f:
                json.dump(record.to_dict(), f, ensure_ascii=False)
                f.write('\n')
            
            # 更新序列号文件
            self._save_sequence_number()
            
            return self._sequence_counter
    
    def append_events(self, events: List[DomainEvent]) -> List[int]:
        """批量追加事件"""
        with self._lock:
            sequence_numbers = []
            records = []
            
            for event in events:
                self._sequence_counter += 1
                record = EventRecord(event, self._sequence_counter)
                records.append(record)
                sequence_numbers.append(self._sequence_counter)
            
            # 批量写入
            with open(self._events_file, 'a', encoding='utf-8') as f:
                for record in records:
                    json.dump(record.to_dict(), f, ensure_ascii=False)
                    f.write('\n')
            
            self._save_sequence_number()
            return sequence_numbers
    
    def get_events(self, aggregate_id: str, 
                   from_version: int = 0) -> List[EventRecord]:
        """获取聚合的事件"""
        events = []
        for record in self._read_all_events():
            if (record.event.aggregate_id == aggregate_id and 
                record.event.version >= from_version):
                events.append(record)
        return events
    
    def get_events_by_type(self, event_type: str,
                          from_sequence: int = 0,
                          limit: int = 100) -> List[EventRecord]:
        """按类型获取事件"""
        events = []
        for record in self._read_all_events():
            if (record.event.event_type == event_type and 
                record.sequence_number >= from_sequence):
                events.append(record)
                if len(events) >= limit:
                    break
        return events
    
    def get_all_events(self, from_sequence: int = 0,
                      limit: int = 100) -> List[EventRecord]:
        """获取所有事件"""
        events = []
        for record in self._read_all_events():
            if record.sequence_number >= from_sequence:
                events.append(record)
                if len(events) >= limit:
                    break
        return events
    
    def get_event_count(self) -> int:
        """获取事件总数"""
        if not self._events_file.exists():
            return 0
        
        count = 0
        with open(self._events_file, 'r', encoding='utf-8') as f:
            for _ in f:
                count += 1
        return count
    
    def get_last_sequence_number(self) -> int:
        """获取最后的序列号"""
        with self._lock:
            return self._sequence_counter
    
    def _read_all_events(self) -> Iterator[EventRecord]:
        """读取所有事件"""
        if not self._events_file.exists():
            return
        
        with open(self._events_file, 'r', encoding='utf-8') as f:
            for line in f:
                if line.strip():
                    try:
                        data = json.loads(line)
                        yield EventRecord.from_dict(data)
                    except (json.JSONDecodeError, KeyError) as e:
                        # 跳过损坏的行
                        continue
    
    def _load_sequence_number(self) -> int:
        """加载序列号"""
        if self._sequence_file.exists():
            try:
                with open(self._sequence_file, 'r') as f:
                    return int(f.read().strip())
            except (ValueError, IOError):
                pass
        return 0
    
    def _save_sequence_number(self):
        """保存序列号"""
        with open(self._sequence_file, 'w') as f:
            f.write(str(self._sequence_counter))


class SQLiteEventStore(EventStore):
    """SQLite事件存储"""
    
    def __init__(self, db_path: str = "events.db"):
        self.db_path = db_path
        self._lock = threading.RLock()
        self._init_database()
    
    def _init_database(self):
        """初始化数据库"""
        with sqlite3.connect(self.db_path) as conn:
            conn.execute('''
                CREATE TABLE IF NOT EXISTS events (
                    sequence_number INTEGER PRIMARY KEY AUTOINCREMENT,
                    event_id TEXT NOT NULL,
                    aggregate_id TEXT NOT NULL,
                    event_type TEXT NOT NULL,
                    occurred_at TEXT NOT NULL,
                    version INTEGER NOT NULL,
                    data TEXT NOT NULL,
                    stored_at TEXT NOT NULL,
                    UNIQUE(event_id)
                )
            ''')
            
            # 创建索引
            conn.execute('CREATE INDEX IF NOT EXISTS idx_aggregate_id ON events(aggregate_id)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_event_type ON events(event_type)')
            conn.execute('CREATE INDEX IF NOT EXISTS idx_occurred_at ON events(occurred_at)')
            
            conn.commit()
    
    def append_event(self, event: DomainEvent) -> int:
        """追加单个事件"""
        with self._lock:
            with sqlite3.connect(self.db_path) as conn:
                cursor = conn.execute('''
                    INSERT INTO events (event_id, aggregate_id, event_type, 
                                      occurred_at, version, data, stored_at)
                    VALUES (?, ?, ?, ?, ?, ?, ?)
                ''', (
                    event.event_id,
                    event.aggregate_id,
                    event.event_type,
                    event.occurred_at.isoformat(),
                    event.version,
                    json.dumps(event.data, ensure_ascii=False),
                    datetime.now().isoformat()
                ))
                
                return cursor.lastrowid
    
    def append_events(self, events: List[DomainEvent]) -> List[int]:
        """批量追加事件"""
        with self._lock:
            sequence_numbers = []
            
            with sqlite3.connect(self.db_path) as conn:
                for event in events:
                    cursor = conn.execute('''
                        INSERT INTO events (event_id, aggregate_id, event_type, 
                                          occurred_at, version, data, stored_at)
                        VALUES (?, ?, ?, ?, ?, ?, ?)
                    ''', (
                        event.event_id,
                        event.aggregate_id,
                        event.event_type,
                        event.occurred_at.isoformat(),
                        event.version,
                        json.dumps(event.data, ensure_ascii=False),
                        datetime.now().isoformat()
                    ))
                    sequence_numbers.append(cursor.lastrowid)
                
                conn.commit()
            
            return sequence_numbers
    
    def get_events(self, aggregate_id: str, 
                   from_version: int = 0) -> List[EventRecord]:
        """获取聚合的事件"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT * FROM events 
                WHERE aggregate_id = ? AND version >= ?
                ORDER BY sequence_number
            ''', (aggregate_id, from_version))
            
            return [self._row_to_record(row) for row in cursor.fetchall()]
    
    def get_events_by_type(self, event_type: str,
                          from_sequence: int = 0,
                          limit: int = 100) -> List[EventRecord]:
        """按类型获取事件"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT * FROM events 
                WHERE event_type = ? AND sequence_number >= ?
                ORDER BY sequence_number
                LIMIT ?
            ''', (event_type, from_sequence, limit))
            
            return [self._row_to_record(row) for row in cursor.fetchall()]
    
    def get_all_events(self, from_sequence: int = 0,
                      limit: int = 100) -> List[EventRecord]:
        """获取所有事件"""
        with sqlite3.connect(self.db_path) as conn:
            conn.row_factory = sqlite3.Row
            cursor = conn.execute('''
                SELECT * FROM events 
                WHERE sequence_number >= ?
                ORDER BY sequence_number
                LIMIT ?
            ''', (from_sequence, limit))
            
            return [self._row_to_record(row) for row in cursor.fetchall()]
    
    def get_event_count(self) -> int:
        """获取事件总数"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute('SELECT COUNT(*) FROM events')
            return cursor.fetchone()[0]
    
    def get_last_sequence_number(self) -> int:
        """获取最后的序列号"""
        with sqlite3.connect(self.db_path) as conn:
            cursor = conn.execute('SELECT MAX(sequence_number) FROM events')
            result = cursor.fetchone()[0]
            return result if result is not None else 0
    
    def _row_to_record(self, row: sqlite3.Row) -> EventRecord:
        """将数据库行转换为事件记录"""
        event = DomainEvent(
            event_id=row['event_id'],
            aggregate_id=row['aggregate_id'],
            event_type=row['event_type'],
            occurred_at=datetime.fromisoformat(row['occurred_at']),
            version=row['version'],
            data=json.loads(row['data'])
        )
        
        return EventRecord(
            event=event,
            sequence_number=row['sequence_number'],
            stored_at=datetime.fromisoformat(row['stored_at'])
        )


# 事件存储工厂
def create_event_store(store_type: str = "memory", **kwargs) -> EventStore:
    """创建事件存储"""
    if store_type == "memory":
        return InMemoryEventStore()
    elif store_type == "file":
        return FileEventStore(**kwargs)
    elif store_type == "sqlite":
        return SQLiteEventStore(**kwargs)
    else:
        raise ValueError(f"不支持的事件存储类型: {store_type}")


# 全局事件存储实例
_global_event_store: Optional[EventStore] = None


def get_event_store() -> EventStore:
    """获取全局事件存储"""
    global _global_event_store
    if _global_event_store is None:
        _global_event_store = create_event_store("memory")
    return _global_event_store


def set_event_store(event_store: EventStore):
    """设置全局事件存储"""
    global _global_event_store
    _global_event_store = event_store
