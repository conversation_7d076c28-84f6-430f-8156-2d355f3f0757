#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统一接口层
提供测试和生产环境的统一接口，自动适配环境
"""

import os
from pathlib import Path
from typing import Dict, Any, Optional, Tuple
from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader

class UnifiedDataDownloader:
    """统一数据下载器"""
    
    def __init__(self):
        self.downloader = StructuredInternetMinuteDownloader()
        self.test_mode = self._detect_test_mode()
        
    def _detect_test_mode(self) -> bool:
        """检测是否为测试模式"""
        # 检查环境变量
        if os.environ.get('MYTHQUANT_TEST_MODE') == '1':
            return True
        
        # 检查是否在测试环境目录中运行
        current_path = Path.cwd()
        if 'test_environments' in str(current_path):
            return True
            
        return False
    
    def _resolve_file_path(self, logical_path: str) -> str:
        """解析逻辑路径到实际路径"""
        if self.test_mode:
            # 测试环境：转换到测试路径
            test_base = Path(__file__).parent.parent / 'test_environments' / 'minute_data_tests' / 'input_data'
            
            # 处理test_前缀
            filename = Path(logical_path).name
            if not filename.startswith('test_'):
                filename = f'test_{filename}'
            
            return str(test_base / filename)
        else:
            # 生产环境：使用原始路径
            return logical_path
    
    def check_incremental_download_prerequisite(self, existing_file: str, stock_code: str) -> Tuple[bool, Dict[str, Any]]:
        """
        检查增量下载前提条件（统一接口）
        
        Args:
            existing_file: 现有文件路径（逻辑路径，自动适配环境）
            stock_code: 股票代码
            
        Returns:
            (是否具备条件, 详细信息)
        """
        # 解析实际文件路径
        actual_file_path = self._resolve_file_path(existing_file)
        
        # 调用底层实现
        has_prerequisite, details = self.downloader.check_incremental_download_prerequisite(
            actual_file_path, stock_code
        )
        
        # 添加环境信息
        if details:
            details['environment_info'] = {
                'mode': 'test' if self.test_mode else 'production',
                'logical_path': existing_file,
                'actual_path': actual_file_path
            }
        
        return has_prerequisite, details


def get_unified_data_downloader() -> UnifiedDataDownloader:
    """获取统一数据下载器实例"""
    return UnifiedDataDownloader()


def check_incremental_download_prerequisite(existing_file: str, stock_code: str) -> Tuple[bool, Dict[str, Any]]:
    """
    检查增量下载前提条件（便捷函数）
    
    Args:
        existing_file: 现有文件路径（逻辑路径，自动适配环境）
        stock_code: 股票代码
        
    Returns:
        (是否具备条件, 详细信息)
    """
    downloader = get_unified_data_downloader()
    return downloader.check_incremental_download_prerequisite(existing_file, stock_code)


def enable_test_mode():
    """启用测试模式"""
    os.environ['MYTHQUANT_TEST_MODE'] = '1'


def disable_test_mode():
    """禁用测试模式"""
    if 'MYTHQUANT_TEST_MODE' in os.environ:
        del os.environ['MYTHQUANT_TEST_MODE']


def is_test_mode() -> bool:
    """检查是否为测试模式"""
    return os.environ.get('MYTHQUANT_TEST_MODE') == '1'


class UnifiedMissingDataProcessor:
    """统一缺失数据处理器"""

    def __init__(self):
        from utils.missing_data_processor import MissingDataProcessor
        self.processor = MissingDataProcessor()
        self.test_mode = is_test_mode()

    def process_missing_data_for_file(self, filepath: str, stock_code: str) -> Tuple[bool, Dict[str, Any]]:
        """
        处理文件的缺失数据（统一接口）

        Args:
            filepath: 文件路径（逻辑路径，自动适配环境）
            stock_code: 股票代码

        Returns:
            (是否成功, 验证结果)
        """
        # 解析实际文件路径
        if self.test_mode:
            test_base = Path(__file__).parent.parent / 'test_environments' / 'minute_data_tests' / 'input_data'
            filename = Path(filepath).name
            if not filename.startswith('test_'):
                filename = f'test_{filename}'
            actual_filepath = str(test_base / filename)
        else:
            actual_filepath = filepath

        # 调用底层处理器
        return self.processor.process_missing_data_for_file(actual_filepath, stock_code)


def get_unified_missing_data_processor() -> UnifiedMissingDataProcessor:
    """获取统一缺失数据处理器实例"""
    return UnifiedMissingDataProcessor()


def process_missing_data_for_file(filepath: str, stock_code: str) -> Tuple[bool, Dict[str, Any]]:
    """
    处理文件的缺失数据（便捷函数）

    Args:
        filepath: 文件路径（逻辑路径，自动适配环境）
        stock_code: 股票代码

    Returns:
        (是否成功, 验证结果)
    """
    processor = get_unified_missing_data_processor()
    return processor.process_missing_data_for_file(filepath, stock_code)


# 导出主要接口
__all__ = [
    'UnifiedDataDownloader',
    'UnifiedMissingDataProcessor',
    'get_unified_data_downloader',
    'get_unified_missing_data_processor',
    'check_incremental_download_prerequisite',
    'process_missing_data_for_file',
    'enable_test_mode',
    'disable_test_mode',
    'is_test_mode'
]
