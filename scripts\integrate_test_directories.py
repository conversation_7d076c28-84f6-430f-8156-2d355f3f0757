#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试目录整合脚本
整合分散的测试目录，符合DDD架构和knowledge_base规范
同时确保现有测试体系不受影响

创建时间: 2025-08-03
版本: 1.0.0
"""

import os
import shutil
import json
from pathlib import Path
from datetime import datetime
from typing import Dict, List, Any

class TestDirectoryIntegrator:
    """测试目录整合器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent.parent
        self.backup_dir = self.project_root / f"backup_test_integration_{datetime.now().strftime('%Y%m%d_%H%M%S')}"
        self.integration_log = []
        
    def analyze_current_structure(self) -> Dict[str, Any]:
        """分析当前测试目录结构"""
        print("🔍 分析当前测试目录结构...")
        
        analysis = {
            'test_environments': {
                'path': self.project_root / 'test_environments',
                'status': 'keep',  # 保留，符合规范
                'description': '标准化测试环境，符合knowledge_base规范',
                'subdirs': [],
                'files': []
            },
            'tests': {
                'path': self.project_root / 'tests',
                'status': 'keep',  # 保留，符合DDD架构
                'description': 'DDD架构标准测试目录',
                'subdirs': [],
                'files': []
            },
            'test_tdx': {
                'path': self.project_root / 'test_tdx',
                'status': 'integrate',  # 需要整合
                'description': 'TDX数据源测试，需要整合到标准结构',
                'subdirs': [],
                'files': []
            },
            'root_test_scripts': {
                'path': self.project_root,
                'status': 'organize',  # 需要整理
                'description': '根目录散落的测试脚本，需要分类整理',
                'files': []
            }
        }
        
        # 分析各个目录
        for key, info in analysis.items():
            path = info['path']
            if path.exists():
                if key == 'root_test_scripts':
                    # 查找根目录的测试脚本
                    test_files = []
                    for file in path.glob('test_*.py'):
                        if file.is_file():
                            test_files.append(file.name)
                    info['files'] = test_files
                else:
                    # 分析子目录和文件
                    if path.is_dir():
                        info['subdirs'] = [d.name for d in path.iterdir() if d.is_dir()]
                        info['files'] = [f.name for f in path.iterdir() if f.is_file()]
                
                print(f"   📁 {key}: {len(info.get('subdirs', []))} 子目录, {len(info.get('files', []))} 文件")
            else:
                print(f"   ❌ {key}: 目录不存在")
        
        return analysis
    
    def create_integration_plan(self, analysis: Dict[str, Any]) -> Dict[str, Any]:
        """创建整合计划"""
        print("\n📋 创建整合计划...")
        
        plan = {
            'backup_operations': [],
            'move_operations': [],
            'create_operations': [],
            'cleanup_operations': [],
            'update_operations': []
        }
        
        # 1. 备份操作
        plan['backup_operations'] = [
            {
                'action': 'backup_directory',
                'source': 'test_tdx',
                'target': f'backup_test_integration_{datetime.now().strftime("%Y%m%d_%H%M%S")}/test_tdx',
                'reason': '备份TDX测试目录'
            },
            {
                'action': 'backup_files',
                'source': 'root_test_scripts',
                'files': analysis['root_test_scripts']['files'],
                'target': f'backup_test_integration_{datetime.now().strftime("%Y%m%d_%H%M%S")}/root_scripts',
                'reason': '备份根目录测试脚本'
            }
        ]
        
        # 2. 移动操作 - 整合test_tdx到test_environments
        if analysis['test_tdx']['path'].exists():
            plan['move_operations'].append({
                'action': 'move_directory',
                'source': 'test_tdx',
                'target': 'test_environments/data_sources/tdx',
                'reason': '将TDX测试数据整合到标准测试环境'
            })
        
        # 3. 创建操作 - 建立统一的测试管理结构
        plan['create_operations'] = [
            {
                'action': 'create_directory',
                'path': 'test_environments/data_sources',
                'reason': '创建数据源测试目录'
            },
            {
                'action': 'create_directory', 
                'path': 'test_environments/legacy_scripts',
                'reason': '创建遗留脚本整理目录'
            },
            {
                'action': 'create_file',
                'path': 'test_environments/README.md',
                'content': 'test_environments_readme',
                'reason': '创建测试环境说明文档'
            },
            {
                'action': 'create_file',
                'path': 'tests/README.md', 
                'content': 'tests_readme',
                'reason': '创建DDD测试目录说明文档'
            }
        ]
        
        # 4. 整理操作 - 分类根目录测试脚本
        root_scripts = analysis['root_test_scripts']['files']
        for script in root_scripts:
            if 'config' in script.lower():
                target_dir = 'test_environments/legacy_scripts/config_tests'
            elif 'pytdx' in script.lower() or 'connection' in script.lower():
                target_dir = 'test_environments/legacy_scripts/connection_tests'
            elif 'comprehensive' in script.lower():
                target_dir = 'test_environments/legacy_scripts/comprehensive_tests'
            elif 'minute' in script.lower():
                target_dir = 'test_environments/legacy_scripts/minute_data_tests'
            else:
                target_dir = 'test_environments/legacy_scripts/misc_tests'
            
            plan['move_operations'].append({
                'action': 'move_file',
                'source': script,
                'target': f'{target_dir}/{script}',
                'reason': f'整理{script}到对应分类目录'
            })
        
        # 5. 更新操作 - 更新配置和文档
        plan['update_operations'] = [
            {
                'action': 'update_test_config',
                'file': 'test_config.py',
                'reason': '更新测试配置以反映新的目录结构'
            },
            {
                'action': 'update_user_config',
                'file': 'user_config.py',
                'reason': '更新用户配置中的测试路径'
            }
        ]
        
        return plan
    
    def execute_integration_plan(self, plan: Dict[str, Any]) -> bool:
        """执行整合计划"""
        print("\n🚀 执行整合计划...")
        
        try:
            # 1. 创建备份目录
            self.backup_dir.mkdir(exist_ok=True)
            print(f"   📦 创建备份目录: {self.backup_dir}")
            
            # 2. 执行备份操作
            print("\n   📦 执行备份操作...")
            for op in plan['backup_operations']:
                if op['action'] == 'backup_directory':
                    source = self.project_root / op['source']
                    target = self.backup_dir / op['source']
                    if source.exists():
                        shutil.copytree(source, target)
                        print(f"      ✅ 备份目录: {op['source']} -> {target}")
                        self.integration_log.append(f"备份目录: {op['source']}")
                
                elif op['action'] == 'backup_files':
                    target_dir = self.backup_dir / 'root_scripts'
                    target_dir.mkdir(exist_ok=True)
                    for file_name in op['files']:
                        source_file = self.project_root / file_name
                        target_file = target_dir / file_name
                        if source_file.exists():
                            shutil.copy2(source_file, target_file)
                            print(f"      ✅ 备份文件: {file_name}")
                            self.integration_log.append(f"备份文件: {file_name}")
            
            # 3. 执行创建操作
            print("\n   🏗️ 执行创建操作...")
            for op in plan['create_operations']:
                if op['action'] == 'create_directory':
                    dir_path = self.project_root / op['path']
                    dir_path.mkdir(parents=True, exist_ok=True)
                    print(f"      ✅ 创建目录: {op['path']}")
                    self.integration_log.append(f"创建目录: {op['path']}")
                
                elif op['action'] == 'create_file':
                    file_path = self.project_root / op['path']
                    content = self.get_template_content(op['content'])
                    with open(file_path, 'w', encoding='utf-8') as f:
                        f.write(content)
                    print(f"      ✅ 创建文件: {op['path']}")
                    self.integration_log.append(f"创建文件: {op['path']}")
            
            # 4. 执行移动操作
            print("\n   📦 执行移动操作...")
            for op in plan['move_operations']:
                if op['action'] == 'move_directory':
                    source = self.project_root / op['source']
                    target = self.project_root / op['target']
                    if source.exists():
                        target.parent.mkdir(parents=True, exist_ok=True)
                        shutil.move(str(source), str(target))
                        print(f"      ✅ 移动目录: {op['source']} -> {op['target']}")
                        self.integration_log.append(f"移动目录: {op['source']} -> {op['target']}")
                
                elif op['action'] == 'move_file':
                    source = self.project_root / op['source']
                    target = self.project_root / op['target']
                    if source.exists():
                        target.parent.mkdir(parents=True, exist_ok=True)
                        shutil.move(str(source), str(target))
                        print(f"      ✅ 移动文件: {op['source']} -> {op['target']}")
                        self.integration_log.append(f"移动文件: {op['source']} -> {op['target']}")
            
            # 5. 执行更新操作
            print("\n   🔄 执行更新操作...")
            for op in plan['update_operations']:
                if op['action'] == 'update_test_config':
                    self.update_test_config()
                    print(f"      ✅ 更新测试配置")
                    self.integration_log.append("更新测试配置")
                
                elif op['action'] == 'update_user_config':
                    self.update_user_config()
                    print(f"      ✅ 更新用户配置")
                    self.integration_log.append("更新用户配置")
            
            return True
            
        except Exception as e:
            print(f"   ❌ 整合执行失败: {e}")
            return False
    
    def get_template_content(self, template_name: str) -> str:
        """获取模板内容"""
        templates = {
            'test_environments_readme': """# 测试环境目录

## 目录结构

### 标准测试环境
- `minute_data_tests/` - 1分钟数据专项测试环境
- `unit_tests/` - 单元测试环境
- `integration_tests/` - 集成测试环境
- `performance_tests/` - 性能测试环境
- `regression_tests/` - 回归测试环境
- `data_quality_tests/` - 数据质量测试环境

### 数据源测试
- `data_sources/tdx/` - TDX数据源测试数据

### 遗留脚本
- `legacy_scripts/` - 整理后的遗留测试脚本

## 使用说明

1. 使用 `test_config.py` 进行统一配置管理
2. 运行 `comprehensive_test_with_proper_config.py` 进行完整测试
3. 各专项测试环境独立运行，互不干扰

## 维护指南

- 新增测试数据放入对应的测试环境目录
- 遗留脚本经过验证后可以删除或迁移到标准测试中
- 定期清理过期的测试结果和报告
""",
            'tests_readme': """# DDD架构测试目录

## 目录结构

### 测试分层
- `unit/` - 单元测试（测试单个模块）
- `integration/` - 集成测试（测试模块间协作）
- `performance/` - 性能测试（验证性能指标）
- `domain/` - 领域层测试
- `fixtures/` - 测试夹具和数据

## 测试原则

1. **单元测试** - 快速、独立、可重复
2. **集成测试** - 验证模块间协作
3. **性能测试** - 确保性能指标达标
4. **领域测试** - 验证业务逻辑正确性

## 运行测试

```bash
# 运行所有测试
python -m pytest tests/

# 运行单元测试
python -m pytest tests/unit/

# 运行集成测试
python -m pytest tests/integration/
```

## 测试配置

- 使用 `pytest.ini` 进行pytest配置
- 使用 `tests/test_config.py` 进行测试专用配置
- 测试数据存放在 `test_environments/` 目录
"""
        }
        
        return templates.get(template_name, "# 模板内容")
    
    def update_test_config(self):
        """更新测试配置"""
        # 这里可以添加具体的配置更新逻辑
        pass
    
    def update_user_config(self):
        """更新用户配置"""
        # 这里可以添加具体的用户配置更新逻辑
        pass
    
    def generate_integration_report(self) -> str:
        """生成整合报告"""
        report = {
            'integration_time': datetime.now().isoformat(),
            'backup_directory': str(self.backup_dir),
            'operations_performed': self.integration_log,
            'status': 'completed',
            'next_steps': [
                '验证测试脚本是否正常工作',
                '运行完整测试套件确认功能正常',
                '清理不需要的遗留文件',
                '更新相关文档'
            ]
        }
        
        report_file = self.project_root / f'test_integration_report_{datetime.now().strftime("%Y%m%d_%H%M%S")}.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        return str(report_file)
    
    def run_integration(self) -> bool:
        """运行完整的整合流程"""
        print("🎯 测试目录整合开始")
        print("=" * 60)
        print("📋 目标: 整合分散的测试目录，符合DDD架构和knowledge_base规范")
        print("=" * 60)
        
        try:
            # 1. 分析当前结构
            analysis = self.analyze_current_structure()
            
            # 2. 创建整合计划
            plan = self.create_integration_plan(analysis)
            
            # 3. 显示计划摘要
            print(f"\n📊 整合计划摘要:")
            print(f"   备份操作: {len(plan['backup_operations'])} 项")
            print(f"   移动操作: {len(plan['move_operations'])} 项")
            print(f"   创建操作: {len(plan['create_operations'])} 项")
            print(f"   更新操作: {len(plan['update_operations'])} 项")
            
            # 4. 执行整合
            success = self.execute_integration_plan(plan)
            
            # 5. 生成报告
            report_file = self.generate_integration_report()
            
            if success:
                print(f"\n🎉 测试目录整合完成！")
                print(f"📋 整合报告: {report_file}")
                print(f"📦 备份目录: {self.backup_dir}")
                print(f"\n✅ 整合后的目录结构:")
                print(f"   📁 test_environments/ - 标准化测试环境（保留）")
                print(f"   📁 tests/ - DDD架构测试目录（保留）")
                print(f"   📁 test_environments/data_sources/tdx/ - TDX测试数据（整合）")
                print(f"   📁 test_environments/legacy_scripts/ - 遗留脚本（整理）")
                return True
            else:
                print(f"\n❌ 测试目录整合失败")
                return False
                
        except Exception as e:
            print(f"\n❌ 整合过程出现异常: {e}")
            return False


def main():
    """主函数"""
    integrator = TestDirectoryIntegrator()
    success = integrator.run_integration()
    
    if success:
        print("\n🎯 下一步建议:")
        print("1. 运行 comprehensive_test_with_proper_config.py 验证测试功能")
        print("2. 检查整合后的目录结构是否符合预期")
        print("3. 更新相关文档和配置")
        print("4. 清理不需要的备份文件")
    else:
        print("\n🔧 失败处理:")
        print("1. 检查错误日志")
        print("2. 从备份目录恢复文件")
        print("3. 手动调整目录结构")
    
    return success


if __name__ == '__main__':
    main()
