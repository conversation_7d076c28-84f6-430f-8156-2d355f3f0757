#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
智能服务器管理器
实现服务器黑名单管理和智能检测功能
"""

import os
import json
import time
from typing import Dict, List, Optional, Tuple
from datetime import datetime, timedelta

class SmartServerManager:
    """智能服务器管理器"""
    
    def __init__(self, cache_dir: str = "cache"):
        """
        初始化智能服务器管理器
        
        Args:
            cache_dir: 缓存目录
        """
        self.cache_dir = cache_dir
        self.server_cache_file = os.path.join(cache_dir, "server_status_cache.json")
        self.blacklist_file = os.path.join(cache_dir, "server_blacklist.json")
        
        # 确保缓存目录存在
        os.makedirs(cache_dir, exist_ok=True)
        
        # 加载缓存数据
        self.server_cache = self._load_server_cache()
        self.blacklist = self._load_blacklist()
    
    def _load_server_cache(self) -> Dict:
        """加载服务器状态缓存"""
        try:
            if os.path.exists(self.server_cache_file):
                with open(self.server_cache_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}
    
    def _save_server_cache(self):
        """保存服务器状态缓存"""
        try:
            with open(self.server_cache_file, 'w', encoding='utf-8') as f:
                json.dump(self.server_cache, f, ensure_ascii=False, indent=2)
        except Exception:
            pass
    
    def _load_blacklist(self) -> Dict:
        """加载服务器黑名单"""
        try:
            if os.path.exists(self.blacklist_file):
                with open(self.blacklist_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception:
            pass
        return {}
    
    def _save_blacklist(self):
        """保存服务器黑名单"""
        try:
            with open(self.blacklist_file, 'w', encoding='utf-8') as f:
                json.dump(self.blacklist, f, ensure_ascii=False, indent=2)
        except Exception:
            pass
    
    def is_server_blacklisted(self, server_ip: str, timeout: int = 3600) -> bool:
        """
        检查服务器是否在黑名单中
        
        Args:
            server_ip: 服务器IP
            timeout: 黑名单超时时间（秒）
            
        Returns:
            是否在黑名单中
        """
        if server_ip not in self.blacklist:
            return False
        
        blacklist_time = self.blacklist[server_ip].get('timestamp', 0)
        current_time = time.time()
        
        # 检查是否超时
        if current_time - blacklist_time > timeout:
            # 超时，从黑名单移除
            del self.blacklist[server_ip]
            self._save_blacklist()
            return False
        
        return True
    
    def add_to_blacklist(self, server_ip: str, reason: str = "连接失败"):
        """
        添加服务器到黑名单
        
        Args:
            server_ip: 服务器IP
            reason: 加入黑名单的原因
        """
        self.blacklist[server_ip] = {
            'timestamp': time.time(),
            'reason': reason,
            'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        self._save_blacklist()
    
    def remove_from_blacklist(self, server_ip: str):
        """
        从黑名单移除服务器
        
        Args:
            server_ip: 服务器IP
        """
        if server_ip in self.blacklist:
            del self.blacklist[server_ip]
            self._save_blacklist()
    
    def update_server_status(self, server_ip: str, port: int, success: bool, 
                           response_time: float = 0.0, name: str = ""):
        """
        更新服务器状态
        
        Args:
            server_ip: 服务器IP
            port: 端口
            success: 是否连接成功
            response_time: 响应时间
            name: 服务器名称
        """
        server_key = f"{server_ip}:{port}"
        
        self.server_cache[server_key] = {
            'ip': server_ip,
            'port': port,
            'name': name,
            'success': success,
            'response_time': response_time,
            'last_check': time.time(),
            'datetime': datetime.now().strftime('%Y-%m-%d %H:%M:%S')
        }
        
        # 如果连接失败，加入黑名单
        if not success:
            self.add_to_blacklist(server_ip, "连接测试失败")
        else:
            # 如果连接成功，从黑名单移除
            self.remove_from_blacklist(server_ip)
        
        self._save_server_cache()
    
    def get_good_servers(self, max_age: int = 3600) -> List[Dict]:
        """
        获取状态良好的服务器列表
        
        Args:
            max_age: 缓存最大有效期（秒）
            
        Returns:
            良好服务器列表
        """
        current_time = time.time()
        good_servers = []
        
        for server_key, server_info in self.server_cache.items():
            # 检查缓存是否过期
            if current_time - server_info.get('last_check', 0) > max_age:
                continue
            
            # 检查是否成功且不在黑名单中
            if (server_info.get('success', False) and 
                not self.is_server_blacklisted(server_info['ip'])):
                good_servers.append(server_info)
        
        # 按响应时间排序
        good_servers.sort(key=lambda x: x.get('response_time', 999))
        return good_servers
    
    def filter_servers_for_smart_detect(self, all_servers: List[Tuple], 
                                      blacklist_timeout: int = 3600) -> List[Tuple]:
        """
        为智能检测过滤服务器列表
        
        Args:
            all_servers: 所有服务器列表 [(ip, port, name), ...]
            blacklist_timeout: 黑名单超时时间
            
        Returns:
            过滤后的服务器列表
        """
        filtered_servers = []
        
        for ip, port, name in all_servers:
            # 检查是否在黑名单中
            if not self.is_server_blacklisted(ip, blacklist_timeout):
                filtered_servers.append((ip, port, name))
        
        return filtered_servers
    
    def get_cached_best_server(self, max_age: int = 300) -> Optional[Dict]:
        """
        获取缓存的最佳服务器
        
        Args:
            max_age: 缓存最大有效期（秒）
            
        Returns:
            最佳服务器信息或None
        """
        good_servers = self.get_good_servers(max_age)
        
        if good_servers:
            return good_servers[0]  # 返回响应时间最快的
        
        return None
    
    def get_blacklist_summary(self) -> Dict:
        """获取黑名单摘要信息"""
        current_time = time.time()
        active_blacklist = {}
        expired_count = 0
        
        for ip, info in self.blacklist.items():
            age = current_time - info.get('timestamp', 0)
            if age < 3600:  # 1小时内的算作活跃
                active_blacklist[ip] = {
                    'reason': info.get('reason', '未知'),
                    'age_minutes': int(age / 60),
                    'datetime': info.get('datetime', '未知')
                }
            else:
                expired_count += 1
        
        return {
            'active_count': len(active_blacklist),
            'expired_count': expired_count,
            'active_list': active_blacklist
        }
    
    def cleanup_expired_cache(self, max_age: int = 86400):
        """
        清理过期缓存
        
        Args:
            max_age: 最大保留时间（秒），默认24小时
        """
        current_time = time.time()
        
        # 清理服务器缓存
        expired_servers = []
        for server_key, server_info in self.server_cache.items():
            if current_time - server_info.get('last_check', 0) > max_age:
                expired_servers.append(server_key)
        
        for server_key in expired_servers:
            del self.server_cache[server_key]
        
        # 清理黑名单
        expired_blacklist = []
        for ip, info in self.blacklist.items():
            if current_time - info.get('timestamp', 0) > max_age:
                expired_blacklist.append(ip)
        
        for ip in expired_blacklist:
            del self.blacklist[ip]
        
        # 保存清理后的数据
        if expired_servers:
            self._save_server_cache()
        if expired_blacklist:
            self._save_blacklist()
        
        return len(expired_servers), len(expired_blacklist)
