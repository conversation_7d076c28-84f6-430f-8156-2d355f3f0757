# MythQuant项目始终遵循规则

## 金融计算精度要求
- 所有价格计算必须使用 `decimal.Decimal` 类型
- 价格比较使用容差比较：`abs(价格1 - 价格2) < Decimal('0.001')`
- 统一精度标准：价格保留3位小数，比率保留6位小数
- 避免浮点数累积误差，特别是在循环计算中

## 代码质量标准
- 数据输入必须进行验证：检查None值、空值、数据类型
- 优先使用pandas向量化操作而非循环
- 实现多级缓存：内存缓存、磁盘缓存、数据库缓存
- 大数据处理使用分块处理策略

## 架构设计原则
- 采用分层架构：数据层、业务层、展示层
- 实现降级处理机制：主方法失败时有备用方案
- 保持高内聚低耦合：模块职责单一，接口清晰
- 使用安全默认值：配置项都有合理默认值

## 性能优化要求
- 数据类型优化：numpy数组、pandas DataFrame
- 智能缓存策略：LRU缓存、文件哈希验证
- 向量化计算：避免Python循环，使用numpy/pandas操作
- 异步IO处理：文件读写、网络请求使用异步模式

## 错误处理标准
- 所有外部数据源访问必须有异常处理
- 记录详细的调试信息但不影响性能
- 提供用户友好的错误信息
- 实现优雅降级而非崩溃退出 