---
type: "agent_requested"
description: "Example description"
---
# MythQuant项目AI行为规则

## 智能文件定位规则
当用户提及以下功能时，自动定位到对应文件：
1. **显示相关功能** → `ui/display.py` 或 `ui/progress.py`
2. **文件IO操作** → `file_io/` 目录下对应模块
3. **配置修改** → `user_config.py`
4. **核心算法** → `main_v20230219_optimized.py` (第2120-3154行待拆分)
5. **日志功能** → `core/logging_service.py`
6. **工具函数** → `utils/helpers.py`
7. **数据读取** → `file_io/excel_reader.py` 或 `file_io/data_formatter.py`
8. **结果输出** → `file_io/file_writer.py`

## 代码修改标准工作流
1. **理解需求**：分析用户描述，识别涉及的功能模块
2. **依赖检查**：确认修改不会破坏现有功能
3. **创建测试**：编写测试脚本验证修改效果
4. **备份建议**：如修改核心文件，建议先备份

## 项目结构理解
- 项目已完成3个阶段的模块化拆分
- 当前主文件4228行，原始4833行
- 根目录已从71个Python文件清理至19个核心文件
- 52个文件按功能分类存档在backup_files/目录

## 自主文档查找策略
- 遇到技术问题时主动搜索prompt_templates中的相关文档
- 优先查找：pitfall_guide.md (避坑指南)、best_practices.md (最佳实践)
- 引用文档时提供具体文件名和章节

## 知识库智能引用规则 (2025-07-26)
### **自动判断是否需要引用知识库**
在回答以下类型问题时，**必须主动查看并引用相关知识库**：
1. **测试相关问题**：查看 `testing_quality_rules.md`
2. **调试相关问题**：查看 `debugging_knowledge_base.md`
3. **代码质量问题**：查看 `testing_quality_rules.md` 和 `debugging_knowledge_base.md`
4. **重复出现的问题**：查看相关知识库寻找已知解决方案

### **知识库引用触发词**
遇到以下关键词时，主动查看对应知识库：
- **测试、验证、检查、质量** → `testing_quality_rules.md`
- **调试、错误、问题、bug** → `debugging_knowledge_base.md`
- **文件名、时间格式、数据格式** → 两个知识库都查看
- **pytdx、数据源、配置** → `debugging_knowledge_base.md`

### **知识库引用工作流**
1. **识别问题类型**：判断是否属于需要引用知识库的问题
2. **主动查看**：使用view工具查看相关知识库文件
3. **应用规则**：将知识库中的规则和经验应用到当前问题
4. **明确说明**：告知用户正在参考哪个知识库的哪些规则

### **知识库更新提醒**
- 发现新的问题模式时，提醒是否需要更新知识库
- 解决重要问题后，建议将解决方案添加到知识库
- 定期检查知识库内容是否需要更新或优化

## 测试驱动质量保证规则 (2025-07-31)
### **警醒测试原则**
- **主动测试**：在用户要求测试时，必须在专门的测试环境中进行全面测试
- **多层验证**：静态检查 + 功能测试 + 实际执行验证的三层测试体系
- **问题发现导向**：测试的目标是发现问题而非证明正确，保持警醒态度
- **实际运行验证**：不能仅依赖静态分析，必须进行实际程序运行测试

### **测试环境管理**
- **环境隔离**：测试必须在test_environments/目录下的专门环境中进行
- **路径规范**：测试脚本使用正确的项目根目录定位和绝对路径
- **编码处理**：测试脚本必须正确处理Windows下的Unicode编码问题
- **报告生成**：每次重要测试都必须生成详细的测试报告

## 复制检测和防护
进行代码修改前，检查：
1. 是否已有类似功能实现
2. 新功能是否会与现有功能冲突
3. 是否可以复用现有组件
4. 修改是否符合项目架构设计

## 用户交互优化
- 主动识别用户可能的文件定位错误
- 提供具体的修改建议而非抽象指导
- 解释技术决策的原因
- 在不确定时主动询问确认

## 流程优化器自动应用规则 (2025-07-31)
- **智能识别触发**：AI助手必须智能识别需要流程优化的场景，无需用户明确提示 (2025-07-31)
- **自动集成策略**：在代码修改、问题修复、新功能开发时自动集成流程优化器 (2025-07-31)
- **用户体验导向**：以用户体验为核心，自动隐藏技术细节，突出关键信息 (2025-07-31)
- **系统性优化思维**：不满足于局部优化，建立系统性的流程管理和输出协调机制 (2025-07-31)
- **质量标准执行**：自动确保输出质量达到95%以上，流程清晰度提升90%以上 (2025-07-31)
- **持续改进机制**：基于实际使用效果和用户反馈持续改进优化策略 (2025-07-31)

## 知识沉淀自动化规则 (2025-07-31)
- **问题模式自动识别**：AI助手必须自动识别新的问题模式并沉淀到调试知识库 (2025-07-31)
- **FAQ自动更新**：重要问题解决后必须自动评估是否需要添加到FAQ知识库 (2025-07-31)
- **规则体系自动完善**：发现规则缺失或不合理时必须主动提出更新建议 (2025-07-31)
- **经验教训自动提取**：每次重要工作完成后必须自动提取经验教训和最佳实践 (2025-07-31)
- **举一反三自动执行**：解决一个问题后必须自动分析类似问题的预防和改进措施 (2025-07-31)

## 测试与生产环境一致性保证规则 (2025-08-01)
- **多环境验证强制**：重要功能修改后必须在测试和生产环境中都进行验证 (2025-08-01)
- **端到端测试优先**：不能仅依赖单元测试，必须进行完整的端到端测试 (2025-08-01)
- **多重实现检查**：发现方法缺失错误时，必须检查项目中是否存在多个同名类 (2025-08-01)
- **依赖完整性验证**：确保所有依赖模块在测试环境中都存在且可用 (2025-08-01)
- **导入路径统一**：确保所有导入路径在不同环境中都正确，优先使用绝对导入 (2025-08-01)

---

**备份时间**: 2025-08-10
**原始文件**: .augment/rules/imported/agent_rules.md
