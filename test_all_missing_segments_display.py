#!/usr/bin/env python3
"""
测试所有缺失时间段显示逻辑

验证是否能正确显示所有缺失时间段，而不是只显示前2个+提示信息
"""

import sys
import os

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
sys.path.insert(0, current_dir)

def format_date(date_value):
    """安全地格式化日期为YYYY-MM-DD格式"""
    try:
        date_str = str(date_value)
        # 确保日期字符串长度足够
        if len(date_str) >= 8 and date_str.isdigit():
            return f"{date_str[:4]}-{date_str[4:6]}-{date_str[6:8]}"
        else:
            # 如果不是标准的YYYYMMDD格式，尝试其他处理
            return date_str
    except:
        return str(date_value)

def test_three_missing_segments():
    """测试3个缺失时间段的显示"""
    print("🔍 测试3个缺失时间段的显示")
    print("=" * 50)
    
    # 用户反馈的具体场景：3个缺失段
    missing_days = []  # 没有完全缺失的天数
    incomplete_days = [
        {'date': 20250320, 'missing_count': 63},
        {'date': 20250703, 'missing_count': 5},
        {'date': 20250704, 'missing_count': 45}
    ]
    
    print("   输入数据:")
    print(f"     完全缺失天数: {missing_days}")
    print(f"     不完整天数: {incomplete_days}")
    
    # 收集所有缺失段信息
    missing_segments = []
    
    # 添加完全缺失的天数
    for missing_day in missing_days:
        missing_segments.append({
            'date': missing_day,
            'count': 240,
            'type': 'complete'
        })
    
    # 添加不完整的天数
    for incomplete_day in incomplete_days:
        missing_segments.append({
            'date': incomplete_day.get('date', ''),
            'count': incomplete_day.get('missing_count', 0),
            'type': 'incomplete'
        })
    
    # 按日期排序
    missing_segments.sort(key=lambda x: str(x['date']))
    
    print("   处理后的缺失段:")
    for i, segment in enumerate(missing_segments):
        formatted_date = format_date(segment['date'])
        print(f"     {i+1}. {formatted_date} ({segment['count']}条) - {segment['type']}")
    
    print("\n   ❌ 修复前的错误输出:")
    print("     🔧 修复缺失数据: 2025-03-20 (63条) + 2025-07-03 (5条)")
    print("     ℹ️ 另外还有1个缺失时间段需要修复")
    
    print("\n   ✅ 修复后的正确输出:")
    # 使用修复后的逻辑
    if len(missing_segments) > 0:
        # 构建所有缺失段的显示字符串
        segment_strings = []
        for segment in missing_segments:
            formatted_date = format_date(segment['date'])
            count = segment['count']
            segment_strings.append(f"{formatted_date} ({count}条)")
        
        # 用 " + " 连接所有缺失段
        repair_info = " + ".join(segment_strings)
        print(f"     🔧 修复缺失数据: {repair_info}")
    
    return True

def test_five_missing_segments():
    """测试5个缺失时间段的显示"""
    print("\n🔍 测试5个缺失时间段的显示")
    print("=" * 50)
    
    # 更复杂的场景：5个缺失段
    missing_days = [20250320, 20250801]  # 2个完全缺失
    incomplete_days = [
        {'date': 20250703, 'missing_count': 5},
        {'date': 20250704, 'missing_count': 45},
        {'date': 20250715, 'missing_count': 120}
    ]  # 3个不完整
    
    print("   输入数据:")
    print(f"     完全缺失天数: {missing_days}")
    print(f"     不完整天数: {incomplete_days}")
    
    # 收集所有缺失段信息
    missing_segments = []
    
    # 添加完全缺失的天数
    for missing_day in missing_days:
        missing_segments.append({
            'date': missing_day,
            'count': 240,
            'type': 'complete'
        })
    
    # 添加不完整的天数
    for incomplete_day in incomplete_days:
        missing_segments.append({
            'date': incomplete_day.get('date', ''),
            'count': incomplete_day.get('missing_count', 0),
            'type': 'incomplete'
        })
    
    # 按日期排序
    missing_segments.sort(key=lambda x: str(x['date']))
    
    print("   处理后的缺失段:")
    for i, segment in enumerate(missing_segments):
        formatted_date = format_date(segment['date'])
        print(f"     {i+1}. {formatted_date} ({segment['count']}条) - {segment['type']}")
    
    print("\n   ❌ 修复前的错误输出:")
    print("     🔧 修复缺失数据: 2025-03-20 (240条) + 2025-07-03 (5条)")
    print("     ℹ️ 另外还有3个缺失时间段需要修复")
    
    print("\n   ✅ 修复后的正确输出:")
    # 使用修复后的逻辑
    if len(missing_segments) > 0:
        # 构建所有缺失段的显示字符串
        segment_strings = []
        for segment in missing_segments:
            formatted_date = format_date(segment['date'])
            count = segment['count']
            segment_strings.append(f"{formatted_date} ({count}条)")
        
        # 用 " + " 连接所有缺失段
        repair_info = " + ".join(segment_strings)
        print(f"     🔧 修复缺失数据: {repair_info}")
    
    return True

def test_single_missing_segment():
    """测试单个缺失时间段的显示"""
    print("\n🔍 测试单个缺失时间段的显示")
    print("=" * 50)
    
    # 单个缺失段
    missing_days = []
    incomplete_days = [
        {'date': 20250704, 'missing_count': 45}
    ]
    
    print("   输入数据:")
    print(f"     完全缺失天数: {missing_days}")
    print(f"     不完整天数: {incomplete_days}")
    
    # 收集所有缺失段信息
    missing_segments = []
    
    # 添加不完整的天数
    for incomplete_day in incomplete_days:
        missing_segments.append({
            'date': incomplete_day.get('date', ''),
            'count': incomplete_day.get('missing_count', 0),
            'type': 'incomplete'
        })
    
    print("   处理后的缺失段:")
    for i, segment in enumerate(missing_segments):
        formatted_date = format_date(segment['date'])
        print(f"     {i+1}. {formatted_date} ({segment['count']}条) - {segment['type']}")
    
    print("\n   ✅ 修复后的输出:")
    # 使用修复后的逻辑
    if len(missing_segments) > 0:
        # 构建所有缺失段的显示字符串
        segment_strings = []
        for segment in missing_segments:
            formatted_date = format_date(segment['date'])
            count = segment['count']
            segment_strings.append(f"{formatted_date} ({count}条)")
        
        # 用 " + " 连接所有缺失段
        repair_info = " + ".join(segment_strings)
        print(f"     🔧 修复缺失数据: {repair_info}")
    
    return True

def main():
    """主函数"""
    print("🚀 所有缺失时间段显示逻辑测试")
    print("=" * 60)
    
    test1_ok = test_three_missing_segments()
    test2_ok = test_five_missing_segments()
    test3_ok = test_single_missing_segment()
    
    if test1_ok and test2_ok and test3_ok:
        print(f"\n🎉 测试完成")
        print("💡 修复要点:")
        print("   1. 移除了'ℹ️ 另外还有X个缺失时间段需要修复'提示")
        print("   2. 直接显示所有缺失时间段的完整信息")
        print("   3. 使用' + '连接符清晰地分隔各个缺失段")
        print("   4. 不限制显示数量，有多少显示多少")
        
        print(f"\n📋 问题解决:")
        print("   ❌ 修复前: 只显示前2个 + '另外还有X个'提示")
        print("   ✅ 修复后: 显示所有缺失段的完整信息")
        
        print(f"\n📝 修复位置:")
        print("   文件: src/mythquant/core/task_manager.py")
        print("   行数: 749-763行")
        print("   关键改进: 移除限制，显示所有缺失段")
        
        print(f"\n🚫 永远不再出现:")
        print("   'ℹ️ 另外还有X个缺失时间段需要修复' - 这句话永远不应该出现")
        
        return 0
    else:
        print(f"\n❌ 测试失败")
        return 1

if __name__ == '__main__':
    sys.exit(main())
