#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境感知配置系统验证脚本
验证新的user_config.py环境检测和智能文件选择器的环境感知功能
"""

import os
import sys

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
test_env_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(os.path.dirname(test_env_dir))

# 强制切换到测试环境
os.chdir(test_env_dir)
sys.path.insert(0, project_root)

print("=== 环境感知配置系统测试 ===")
print(f"当前工作目录: {os.getcwd()}")
print(f"项目根目录: {project_root}")

def test_environment_detection():
    """测试环境检测功能"""
    print("\n1. 测试环境检测功能")
    print("-" * 40)
    
    try:
        from user_config import detect_environment, CURRENT_ENVIRONMENT, get_environment_config
        
        detected_env = detect_environment()
        print(f"检测到的环境: {detected_env}")
        print(f"全局环境变量: {CURRENT_ENVIRONMENT}")
        
        # 测试获取环境配置
        env_config = get_environment_config('minute_data_download')
        print(f"环境配置:")
        for key, value in env_config.items():
            print(f"  {key}: {value}")
        
        return detected_env == 'test'
        
    except Exception as e:
        print(f"环境检测测试失败: {e}")
        return False

def test_smart_file_selector():
    """测试智能文件选择器的环境感知功能"""
    print("\n2. 测试智能文件选择器环境感知")
    print("-" * 40)
    
    try:
        from utils.smart_file_selector import SmartFileSelector
        from user_config import smart_file_selector as config
        
        selector = SmartFileSelector(config)
        
        # 测试文件选择
        stock_code = '000617'
        result = selector.find_best_file(
            stock_code=stock_code,
            data_type='minute',
            target_start='20250320',
            target_end='20250704'
        )
        
        print(f"文件选择结果: {result}")
        
        if result:
            if isinstance(result, tuple):
                file_path, file_info = result
                print(f"选择的文件: {file_path}")
                print(f"文件信息: {file_info}")
                
                # 验证是否选择了测试文件
                if 'test_1min_0_000617' in file_path:
                    print("✅ 成功选择测试文件")
                    return True
                else:
                    print("❌ 未选择测试文件")
                    return False
            else:
                print(f"选择的文件: {result}")
                if 'test_1min_0_000617' in result:
                    print("✅ 成功选择测试文件")
                    return True
                else:
                    print("❌ 未选择测试文件")
                    return False
        else:
            print("❌ 未找到任何文件")
            return False
            
    except Exception as e:
        print(f"智能文件选择器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_workflow_integration():
    """测试与workflow的集成"""
    print("\n3. 测试workflow集成")
    print("-" * 40)
    
    try:
        # 验证测试文件存在
        target_file = "input_data/test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
        if os.path.exists(target_file):
            print(f"✅ 目标测试文件存在: {target_file}")
            
            # 检查文件基本信息
            file_size = os.path.getsize(target_file)
            with open(target_file, 'r', encoding='utf-8') as f:
                lines = sum(1 for _ in f)
            print(f"📊 文件信息: {file_size} bytes, {lines} 行")
            
            return True
        else:
            print(f"❌ 目标测试文件不存在: {target_file}")
            return False
            
    except Exception as e:
        print(f"workflow集成测试失败: {e}")
        return False

def test_ai_debug_detection():
    """测试AI调试模式检测"""
    print("\n4. 测试AI调试模式检测")
    print("-" * 40)
    
    try:
        from user_config import detect_environment
        
        # 当前脚本应该被识别为AI调试模式
        current_env = detect_environment()
        print(f"当前环境检测结果: {current_env}")
        
        # 检查调用栈
        import traceback
        stack = traceback.format_stack()
        ai_debug_indicators = [
            'tools/', 'test_', 'debug_', 'detector', 'tracker', 
            'precise_', 'simple_', 'correct_', 'find_early'
        ]
        
        found_indicators = []
        for frame in stack:
            for indicator in ai_debug_indicators:
                if indicator in frame:
                    found_indicators.append(indicator)
        
        print(f"发现的AI调试指示器: {found_indicators}")
        
        if current_env == 'test':
            print("✅ 成功检测到AI调试模式")
            return True
        else:
            print("❌ 未能检测到AI调试模式")
            return False
            
    except Exception as e:
        print(f"AI调试模式检测失败: {e}")
        return False

def run_comprehensive_test():
    """运行综合测试"""
    print("\n=== 综合测试开始 ===")
    
    tests = [
        ("环境检测", test_environment_detection),
        ("智能文件选择器", test_smart_file_selector),
        ("workflow集成", test_workflow_integration),
        ("AI调试模式检测", test_ai_debug_detection),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
            print(f"\n{test_name}: {'✅ 通过' if result else '❌ 失败'}")
        except Exception as e:
            print(f"\n{test_name}: ❌ 异常 - {e}")
            results.append((test_name, False))
    
    print("\n=== 测试结果汇总 ===")
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{test_name}: {status}")
    
    print(f"\n总体结果: {passed}/{total} 通过")
    
    if passed == total:
        print("🎉 所有测试通过！环境感知配置系统工作正常")
        return True
    else:
        print("⚠️ 部分测试失败，需要进一步调试")
        return False

def main():
    """主函数"""
    print("环境感知配置系统验证脚本")
    print("=" * 60)
    
    success = run_comprehensive_test()
    
    print(f"\n验证完成: {'SUCCESS' if success else 'FAILED'}")
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
