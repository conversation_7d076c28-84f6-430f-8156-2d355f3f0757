#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
日志服务模块
提供统一的日志输出功能，支持不同级别和类别的日志
"""

import logging
from typing import Optional
from .config_manager import config_manager


class LoggingService:
    """日志服务 - 统一的日志输出管理"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 日志级别映射
        self.level_mapping = {
            'info': logging.INFO,
            'warning': logging.WARNING,
            'error': logging.ERROR,
            'critical': logging.CRITICAL
        }
        
        # 视觉标记
        self.markers = {
            'FORWARD_ADJ': '🔧',
            'PERFORMANCE': '⚠️',
            'DATA_PROC': '📊',
            'CACHE': '💾',
            'CRITICAL': '🚨',
            'GENERAL': '📋'
        }
    
    def verbose_log(self, level: str, message: str, category: str = "GENERAL", force: bool = False) -> None:
        """
        详细模式日志输出
        
        参数:
        level: str - 日志级别 ('info', 'warning', 'error', 'critical')
        message: str - 日志消息
        category: str - 日志类别 (FORWARD_ADJ, PERFORMANCE, DATA_PROC, CACHE, CRITICAL)
        force: bool - 强制输出，忽略详细模式设置
        """
        if not force and not config_manager.is_verbose_enabled():
            return
        
        # 检查特定类别的开关
        if not force and not config_manager.get_verbose_config(category):
            return
        
        # 添加视觉标记
        marker = self.markers.get(category, '📋')
        formatted_message = f"{marker} {message}"
        
        # 关键信息高亮
        if category == 'CRITICAL':
            # 使用highlight_critical_info方法进行高亮处理
            formatted_message = config_manager.highlight_critical_info(formatted_message)

        # 直接输出到控制台（避免与console handler重复）
        print(formatted_message)
    
    def log_step(self, step_name: str, status: str = "开始", details: str = "") -> None:
        """
        记录处理步骤
        
        参数:
        step_name: str - 步骤名称
        status: str - 状态 ('开始', '完成', '失败')
        details: str - 详细信息
        """
        detail_str = f" - {details}" if details else ""
        message = f"【{step_name}】{status}{detail_str}"
        self.verbose_log('info', message, 'DATA_PROC')
    
    def log_performance_warning(self, message: str) -> None:
        """
        记录性能警告
        
        参数:
        message: str - 警告信息
        """
        self.verbose_log('warning', message, 'PERFORMANCE')
    
    def log_forward_adj_detail(self, message: str) -> None:
        """
        记录前复权处理详情
        
        参数:
        message: str - 详情信息
        """
        self.verbose_log('info', message, 'FORWARD_ADJ')
    
    def log_cache_status(self, message: str) -> None:
        """
        记录缓存状态
        
        参数:
        message: str - 缓存状态信息
        """
        self.verbose_log('info', message, 'CACHE')
    
    def log_critical_info(self, message: str) -> None:
        """
        记录关键信息（强制显示）
        
        参数:
        message: str - 关键信息
        """
        self.verbose_log('info', message, 'CRITICAL', force=True)
    
    def log_error(self, message: str, exception: Optional[Exception] = None) -> None:
        """
        记录错误信息
        
        参数:
        message: str - 错误信息
        exception: Exception - 异常对象（可选）
        """
        if exception:
            full_message = f"{message}: {str(exception)}"
        else:
            full_message = message
        
        self.verbose_log('error', full_message, 'GENERAL', force=True)
        
        # 同时记录到标准logger
        if exception:
            self.logger.exception(message)
        else:
            self.logger.error(message)


# 全局日志服务实例
logging_service = LoggingService()

# 便捷函数（向后兼容）
def verbose_log(level: str, message: str, category: str = "GENERAL", force: bool = False) -> None:
    """向后兼容的日志函数"""
    logging_service.verbose_log(level, message, category, force)

def log_step(step_name: str, status: str = "开始", details: str = "") -> None:
    """向后兼容的步骤日志函数"""
    logging_service.log_step(step_name, status, details)

def log_performance_warning(message: str) -> None:
    """向后兼容的性能警告函数"""
    logging_service.log_performance_warning(message)

def log_forward_adj_detail(message: str) -> None:
    """向后兼容的前复权详情函数"""
    logging_service.log_forward_adj_detail(message)

def log_cache_status(message: str) -> None:
    """向后兼容的缓存状态函数"""
    logging_service.log_cache_status(message)

def log_critical_info(message: str) -> None:
    """向后兼容的关键信息函数"""
    logging_service.log_critical_info(message) 