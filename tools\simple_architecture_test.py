#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单的架构测试脚本
专门测试新的src/mythquant架构，避免与现有代码冲突
"""

import os
import sys
from pathlib import Path

def test_directory_structure():
    """测试目录结构"""
    print("🔍 测试目录结构...")
    
    required_dirs = [
        "src",
        "src/mythquant",
        "src/mythquant/config",
        "src/mythquant/core",
        "src/mythquant/data",
        "src/mythquant/io",
        "src/mythquant/algorithms",
        "src/mythquant/utils",
        "src/mythquant/ui",
        "src/mythquant/cache"
    ]
    
    missing_dirs = []
    for dir_path in required_dirs:
        if not os.path.exists(dir_path):
            missing_dirs.append(dir_path)
        else:
            print(f"   ✅ {dir_path}")
    
    if missing_dirs:
        print(f"   ❌ 缺失目录: {missing_dirs}")
        return False
    
    print("   🎉 目录结构完整！")
    return True

def test_config_files():
    """测试配置文件"""
    print("\n🔍 测试项目配置文件...")
    
    config_files = [
        "pyproject.toml",
        "setup.py", 
        "MANIFEST.in",
        ".pre-commit-config.yaml",
        "tox.ini"
    ]
    
    missing_files = []
    for file_path in config_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            # 检查文件大小
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
    
    if missing_files:
        print(f"   ❌ 缺失文件: {missing_files}")
        return False
    
    print("   🎉 配置文件完整！")
    return True

def test_init_files():
    """测试__init__.py文件"""
    print("\n🔍 测试包初始化文件...")
    
    init_files = [
        "src/mythquant/__init__.py",
        "src/mythquant/config/__init__.py",
        "src/mythquant/core/__init__.py",
        "src/mythquant/data/__init__.py",
        "src/mythquant/io/__init__.py",
        "src/mythquant/algorithms/__init__.py",
        "src/mythquant/utils/__init__.py",
        "src/mythquant/ui/__init__.py",
        "src/mythquant/cache/__init__.py"
    ]
    
    missing_files = []
    for file_path in init_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            print(f"   ✅ {file_path}")
    
    if missing_files:
        print(f"   ❌ 缺失文件: {missing_files}")
        return False
    
    print("   🎉 包初始化文件完整！")
    return True

def test_core_module_files():
    """测试核心模块文件"""
    print("\n🔍 测试核心模块文件...")
    
    core_files = [
        "src/mythquant/config/manager.py",
        "src/mythquant/config/user_settings.py", 
        "src/mythquant/config/validators.py",
        "src/mythquant/core/application.py",
        "src/mythquant/core/stock_processor.py",
        "src/mythquant/core/task_manager.py",
        "src/mythquant/core/logging_service.py"
    ]
    
    missing_files = []
    for file_path in core_files:
        if not os.path.exists(file_path):
            missing_files.append(file_path)
        else:
            size = os.path.getsize(file_path)
            print(f"   ✅ {file_path} ({size} bytes)")
    
    if missing_files:
        print(f"   ❌ 缺失文件: {missing_files}")
        return False
    
    print("   🎉 核心模块文件完整！")
    return True

def test_file_content():
    """测试关键文件内容"""
    print("\n🔍 测试关键文件内容...")
    
    # 测试主包__init__.py
    main_init = "src/mythquant/__init__.py"
    if os.path.exists(main_init):
        with open(main_init, 'r', encoding='utf-8') as f:
            content = f.read()
            if '__version__' in content:
                print(f"   ✅ {main_init} 包含版本信息")
            else:
                print(f"   ⚠️ {main_init} 缺少版本信息")
    
    # 测试pyproject.toml
    pyproject = "pyproject.toml"
    if os.path.exists(pyproject):
        with open(pyproject, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'mythquant' in content and 'src' in content:
                print(f"   ✅ {pyproject} 配置正确")
            else:
                print(f"   ⚠️ {pyproject} 配置可能有问题")
    
    # 测试配置管理器
    config_manager = "src/mythquant/config/manager.py"
    if os.path.exists(config_manager):
        with open(config_manager, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'ConfigManager' in content and 'class' in content:
                print(f"   ✅ {config_manager} 包含ConfigManager类")
            else:
                print(f"   ⚠️ {config_manager} 可能缺少ConfigManager类")
    
    return True

def test_import_syntax():
    """测试导入语法（不实际导入）"""
    print("\n🔍 测试导入语法...")
    
    # 检查application.py中的导入语句
    app_file = "src/mythquant/core/application.py"
    if os.path.exists(app_file):
        with open(app_file, 'r', encoding='utf-8') as f:
            content = f.read()
            if 'from mythquant.config.manager import' in content:
                print(f"   ✅ application.py 使用新的导入路径")
            else:
                print(f"   ⚠️ application.py 可能仍使用旧的导入路径")
    
    return True

def main():
    """主测试函数"""
    print("🚀 MythQuant 架构重构成果测试")
    print("=" * 50)
    
    tests = [
        ("目录结构", test_directory_structure),
        ("配置文件", test_config_files),
        ("包初始化文件", test_init_files),
        ("核心模块文件", test_core_module_files),
        ("文件内容", test_file_content),
        ("导入语法", test_import_syntax)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"   ❌ 测试 {test_name} 发生错误: {e}")
            results.append((test_name, False))
    
    # 汇总结果
    print("\n" + "=" * 50)
    print("📊 测试结果汇总:")
    
    passed = 0
    total = len(results)
    
    for test_name, success in results:
        status = "✅ 通过" if success else "❌ 失败"
        print(f"   {status} {test_name}")
        if success:
            passed += 1
    
    print(f"\n📈 统计: {passed}/{total} 测试通过 ({passed/total*100:.1f}%)")
    
    if passed == total:
        print("\n🎉 所有测试通过！新架构基础结构完整。")
        print("\n✅ 重构成果验证:")
        print("   • src/mythquant 包结构已建立")
        print("   • 现代化项目配置文件已创建")
        print("   • 配置系统已重构完成")
        print("   • 核心模块已迁移")
        print("   • 包初始化体系已建立")
        
        print("\n🚀 下一步建议:")
        print("   1. 继续完成数据处理模块重组")
        print("   2. 迁移IO和算法模块")
        print("   3. 建立测试架构")
        print("   4. 完善文档体系")
        print("   5. 执行归档和清理")
        
        return True
    else:
        print(f"\n⚠️ {total-passed} 个测试失败，需要修复后再继续。")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n{'='*50}")
    if success:
        print("🎯 重构成果测试完成 - 架构基础稳定！")
    else:
        print("🔧 重构成果测试完成 - 需要修复问题！")
