#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
统计算法模块

纯函数统计算法实现，包括描述性统计、假设检验、时间序列分析等
"""

import math
from typing import List, Tuple, Optional, Dict
from scipy import stats
import numpy as np
from .mathematical import standard_deviation, variance, percentile
from ..exceptions import InsufficientDataError, InvalidParameterError


def z_score(data: List[float]) -> List[float]:
    """
    Z分数标准化
    
    Args:
        data: 数据序列
    
    Returns:
        Z分数序列
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 2:
        raise InsufficientDataError("Z分数计算", 2, len(data))
    
    mean_val = sum(data) / len(data)
    std_val = standard_deviation(data, ddof=1)
    
    if std_val == 0:
        return [0.0] * len(data)
    
    return [(x - mean_val) / std_val for x in data]


def quantile(data: List[float], q: float) -> float:
    """
    分位数
    
    Args:
        data: 数据序列
        q: 分位数 (0-1)
    
    Returns:
        分位数值
    """
    return percentile(data, q * 100)


def skewness(data: List[float]) -> float:
    """
    偏度 (Skewness)
    
    Args:
        data: 数据序列
    
    Returns:
        偏度值
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 3:
        raise InsufficientDataError("偏度计算", 3, len(data))
    
    n = len(data)
    mean_val = sum(data) / n
    std_val = standard_deviation(data, ddof=0)
    
    if std_val == 0:
        return 0.0
    
    # 计算三阶中心矩
    third_moment = sum((x - mean_val) ** 3 for x in data) / n
    
    return third_moment / (std_val ** 3)


def kurtosis(data: List[float], fisher: bool = True) -> float:
    """
    峰度 (Kurtosis)
    
    Args:
        data: 数据序列
        fisher: 是否使用Fisher定义 (减去3)
    
    Returns:
        峰度值
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 4:
        raise InsufficientDataError("峰度计算", 4, len(data))
    
    n = len(data)
    mean_val = sum(data) / n
    std_val = standard_deviation(data, ddof=0)
    
    if std_val == 0:
        return 0.0
    
    # 计算四阶中心矩
    fourth_moment = sum((x - mean_val) ** 4 for x in data) / n
    
    kurt = fourth_moment / (std_val ** 4)
    
    if fisher:
        kurt -= 3  # Fisher定义：正态分布的峰度为0
    
    return kurt


def jarque_bera_test(data: List[float]) -> Tuple[float, float]:
    """
    Jarque-Bera正态性检验
    
    Args:
        data: 数据序列
    
    Returns:
        (JB统计量, p值)
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 8:
        raise InsufficientDataError("JB检验", 8, len(data))
    
    n = len(data)
    s = skewness(data)
    k = kurtosis(data, fisher=True)
    
    # JB统计量
    jb_stat = n * (s**2 / 6 + k**2 / 24)
    
    # p值 (卡方分布，自由度为2)
    p_value = 1 - stats.chi2.cdf(jb_stat, df=2)
    
    return jb_stat, p_value


def adf_test(data: List[float], 
             regression: str = "c",
             autolag: str = "AIC") -> Tuple[float, float, int, int, Dict[str, float]]:
    """
    Augmented Dickey-Fuller单位根检验
    
    Args:
        data: 数据序列
        regression: 回归类型 ("c", "ct", "ctt", "nc")
        autolag: 滞后阶数选择方法
    
    Returns:
        (ADF统计量, p值, 使用的滞后阶数, 观测数, 临界值字典)
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 12:
        raise InsufficientDataError("ADF检验", 12, len(data))
    
    # 使用scipy.stats进行ADF检验
    try:
        from statsmodels.tsa.stattools import adfuller
        result = adfuller(data, regression=regression, autolag=autolag)
        
        adf_stat = result[0]
        p_value = result[1]
        used_lag = result[2]
        n_obs = result[3]
        critical_values = result[4]
        
        return adf_stat, p_value, used_lag, n_obs, critical_values
    
    except ImportError:
        # 如果没有statsmodels，使用简化版本
        # 计算一阶差分
        diff_data = [data[i] - data[i-1] for i in range(1, len(data))]
        
        # 简化的ADF统计量 (实际应该包含回归分析)
        mean_diff = sum(diff_data) / len(diff_data)
        std_diff = standard_deviation(diff_data, ddof=1)
        
        if std_diff == 0:
            adf_stat = 0.0
        else:
            adf_stat = mean_diff / std_diff * math.sqrt(len(diff_data))
        
        # 简化的p值估计
        p_value = 2 * (1 - stats.norm.cdf(abs(adf_stat)))
        
        critical_values = {"1%": -3.43, "5%": -2.86, "10%": -2.57}
        
        return adf_stat, p_value, 1, len(data)-1, critical_values


def ljung_box_test(residuals: List[float], lags: int = 10) -> Tuple[float, float]:
    """
    Ljung-Box自相关检验
    
    Args:
        residuals: 残差序列
        lags: 滞后阶数
    
    Returns:
        (LB统计量, p值)
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(residuals) <= lags:
        raise InsufficientDataError("LB检验", lags + 1, len(residuals))
    
    n = len(residuals)
    
    # 计算自相关系数
    autocorrs = []
    mean_val = sum(residuals) / n
    
    # 计算方差
    var_val = sum((x - mean_val) ** 2 for x in residuals) / n
    
    if var_val == 0:
        return 0.0, 1.0
    
    for lag in range(1, lags + 1):
        # 计算滞后lag的自相关系数
        numerator = sum((residuals[i] - mean_val) * (residuals[i - lag] - mean_val) 
                       for i in range(lag, n))
        autocorr = numerator / (n * var_val)
        autocorrs.append(autocorr)
    
    # 计算LB统计量
    lb_stat = n * (n + 2) * sum(autocorr**2 / (n - lag - 1) 
                                for lag, autocorr in enumerate(autocorrs))
    
    # p值 (卡方分布)
    p_value = 1 - stats.chi2.cdf(lb_stat, df=lags)
    
    return lb_stat, p_value


def autocorrelation(data: List[float], max_lags: int = 20) -> List[float]:
    """
    自相关函数
    
    Args:
        data: 数据序列
        max_lags: 最大滞后阶数
    
    Returns:
        自相关系数序列
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) <= max_lags:
        raise InsufficientDataError("自相关计算", max_lags + 1, len(data))
    
    n = len(data)
    mean_val = sum(data) / n
    
    # 计算方差
    var_val = sum((x - mean_val) ** 2 for x in data) / n
    
    if var_val == 0:
        return [1.0] + [0.0] * max_lags
    
    autocorrs = [1.0]  # lag=0的自相关系数为1
    
    for lag in range(1, max_lags + 1):
        # 计算滞后lag的自相关系数
        numerator = sum((data[i] - mean_val) * (data[i - lag] - mean_val) 
                       for i in range(lag, n))
        autocorr = numerator / (n * var_val)
        autocorrs.append(autocorr)
    
    return autocorrs


def partial_autocorrelation(data: List[float], max_lags: int = 20) -> List[float]:
    """
    偏自相关函数
    
    Args:
        data: 数据序列
        max_lags: 最大滞后阶数
    
    Returns:
        偏自相关系数序列
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) <= max_lags:
        raise InsufficientDataError("偏自相关计算", max_lags + 1, len(data))
    
    # 计算自相关系数
    autocorrs = autocorrelation(data, max_lags)
    
    # 使用Yule-Walker方程计算偏自相关
    pacf = [1.0]  # lag=0的偏自相关系数为1
    
    for k in range(1, max_lags + 1):
        if k == 1:
            pacf.append(autocorrs[1])
        else:
            # 构建Yule-Walker方程组
            # 这里使用简化的递推公式
            numerator = autocorrs[k]
            for j in range(1, k):
                numerator -= pacf[j] * autocorrs[k - j]
            
            denominator = 1.0
            for j in range(1, k):
                denominator -= pacf[j] * autocorrs[j]
            
            if denominator == 0:
                pacf.append(0.0)
            else:
                pacf.append(numerator / denominator)
    
    return pacf


def runs_test(data: List[float], cutoff: Optional[float] = None) -> Tuple[float, float]:
    """
    游程检验 (随机性检验)
    
    Args:
        data: 数据序列
        cutoff: 分割点，如果为None则使用中位数
    
    Returns:
        (Z统计量, p值)
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 10:
        raise InsufficientDataError("游程检验", 10, len(data))
    
    if cutoff is None:
        cutoff = percentile(data, 50)  # 使用中位数
    
    # 将数据转换为二进制序列
    binary_seq = [1 if x >= cutoff else 0 for x in data]
    
    # 计算游程数
    runs = 1
    for i in range(1, len(binary_seq)):
        if binary_seq[i] != binary_seq[i-1]:
            runs += 1
    
    # 计算1和0的个数
    n1 = sum(binary_seq)
    n0 = len(binary_seq) - n1
    
    if n1 == 0 or n0 == 0:
        return 0.0, 1.0
    
    # 期望游程数
    expected_runs = (2 * n1 * n0) / (n1 + n0) + 1
    
    # 游程数的方差
    var_runs = (2 * n1 * n0 * (2 * n1 * n0 - n1 - n0)) / ((n1 + n0)**2 * (n1 + n0 - 1))
    
    if var_runs <= 0:
        return 0.0, 1.0
    
    # Z统计量
    z_stat = (runs - expected_runs) / math.sqrt(var_runs)
    
    # p值 (双侧检验)
    p_value = 2 * (1 - stats.norm.cdf(abs(z_stat)))
    
    return z_stat, p_value


def anderson_darling_test(data: List[float]) -> Tuple[float, List[float], List[float]]:
    """
    Anderson-Darling正态性检验
    
    Args:
        data: 数据序列
    
    Returns:
        (AD统计量, 临界值列表, 显著性水平列表)
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data) < 8:
        raise InsufficientDataError("AD检验", 8, len(data))
    
    # 使用scipy.stats进行AD检验
    try:
        result = stats.anderson(data, dist='norm')
        return result.statistic, result.critical_values.tolist(), result.significance_level.tolist()
    
    except Exception:
        # 简化版本的AD检验
        n = len(data)
        sorted_data = sorted(data)
        
        # 标准化数据
        mean_val = sum(data) / n
        std_val = standard_deviation(data, ddof=1)
        
        if std_val == 0:
            return 0.0, [0.576, 0.656, 0.787, 0.918, 1.092], [15.0, 10.0, 5.0, 2.5, 1.0]
        
        standardized = [(x - mean_val) / std_val for x in sorted_data]
        
        # 计算AD统计量
        ad_stat = 0.0
        for i in range(n):
            cdf_val = stats.norm.cdf(standardized[i])
            if cdf_val > 0 and cdf_val < 1:
                ad_stat += (2*i + 1) * (math.log(cdf_val) + math.log(1 - stats.norm.cdf(standardized[n-1-i])))
        
        ad_stat = -n - ad_stat / n
        
        # 临界值 (近似)
        critical_values = [0.576, 0.656, 0.787, 0.918, 1.092]
        significance_levels = [15.0, 10.0, 5.0, 2.5, 1.0]
        
        return ad_stat, critical_values, significance_levels


def kolmogorov_smirnov_test(data1: List[float], 
                           data2: Optional[List[float]] = None,
                           distribution: str = "norm") -> Tuple[float, float]:
    """
    Kolmogorov-Smirnov检验
    
    Args:
        data1: 第一个数据序列
        data2: 第二个数据序列 (如果为None则进行单样本检验)
        distribution: 理论分布 (仅用于单样本检验)
    
    Returns:
        (KS统计量, p值)
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data1) < 5:
        raise InsufficientDataError("KS检验", 5, len(data1))
    
    if data2 is None:
        # 单样本KS检验
        if distribution == "norm":
            # 标准化数据
            mean_val = sum(data1) / len(data1)
            std_val = standard_deviation(data1, ddof=1)
            
            if std_val == 0:
                return 0.0, 1.0
            
            standardized = [(x - mean_val) / std_val for x in data1]
            ks_stat, p_value = stats.kstest(standardized, 'norm')
            return ks_stat, p_value
        else:
            raise InvalidParameterError("KS检验", "distribution", distribution, 
                                       "目前只支持正态分布检验")
    else:
        # 双样本KS检验
        if len(data2) < 5:
            raise InsufficientDataError("KS检验", 5, len(data2))
        
        ks_stat, p_value = stats.ks_2samp(data1, data2)
        return ks_stat, p_value


def mann_whitney_u_test(data1: List[float], data2: List[float]) -> Tuple[float, float]:
    """
    Mann-Whitney U检验 (非参数检验)
    
    Args:
        data1: 第一组数据
        data2: 第二组数据
    
    Returns:
        (U统计量, p值)
    
    Raises:
        InsufficientDataError: 数据不足
    """
    if len(data1) < 3 or len(data2) < 3:
        raise InsufficientDataError("Mann-Whitney U检验", 3, min(len(data1), len(data2)))
    
    u_stat, p_value = stats.mannwhitneyu(data1, data2, alternative='two-sided')
    return u_stat, p_value


def wilcoxon_signed_rank_test(data1: List[float], data2: List[float]) -> Tuple[float, float]:
    """
    Wilcoxon符号秩检验
    
    Args:
        data1: 第一组数据
        data2: 第二组数据
    
    Returns:
        (W统计量, p值)
    
    Raises:
        InvalidParameterError: 数据长度不匹配
        InsufficientDataError: 数据不足
    """
    if len(data1) != len(data2):
        raise InvalidParameterError("Wilcoxon检验", "data2", len(data2),
                                   f"数据长度必须相等，data1长度为{len(data1)}")
    
    if len(data1) < 6:
        raise InsufficientDataError("Wilcoxon检验", 6, len(data1))
    
    w_stat, p_value = stats.wilcoxon(data1, data2)
    return w_stat, p_value
