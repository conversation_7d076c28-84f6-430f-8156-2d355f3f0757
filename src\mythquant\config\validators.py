#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
配置验证器模块

提供配置项的验证功能，确保配置的正确性和一致性。
"""

import os
from typing import Dict, Any, List, Tuple, Optional
from pathlib import Path


class ConfigValidator:
    """配置验证器"""
    
    def __init__(self):
        """初始化验证器"""
        self.validation_rules = self._load_validation_rules()
    
    def _load_validation_rules(self) -> Dict[str, Any]:
        """加载验证规则"""
        return {
            'tdx_path': {
                'type': str,
                'required': True,
                'validator': self._validate_path_exists,
                'description': 'TDX主路径必须存在'
            },
            'output_path': {
                'type': str,
                'required': True,
                'validator': self._validate_output_path,
                'description': '输出路径必须可写'
            },
            'scoring_weights': {
                'type': dict,
                'required': False,
                'validator': self._validate_scoring_weights,
                'description': '评分权重总和必须为1.0'
            },
            'file_encoding': {
                'type': str,
                'required': False,
                'validator': self._validate_encoding,
                'description': '文件编码必须有效'
            }
        }
    
    def validate_config(self, config: Dict[str, Any]) -> Tuple[bool, List[str], List[str]]:
        """
        验证配置
        
        Returns:
            Tuple[bool, List[str], List[str]]: (是否有效, 错误列表, 警告列表)
        """
        errors = []
        warnings = []
        
        # 验证TDX配置
        tdx_errors, tdx_warnings = self._validate_tdx_config(config.get('tdx', {}))
        errors.extend(tdx_errors)
        warnings.extend(tdx_warnings)
        
        # 验证输出配置
        output_errors, output_warnings = self._validate_output_config(config.get('output_config', {}))
        errors.extend(output_errors)
        warnings.extend(output_warnings)
        
        # 验证智能文件选择器配置
        selector_errors, selector_warnings = self._validate_file_selector_config(
            config.get('smart_file_selector', {})
        )
        errors.extend(selector_errors)
        warnings.extend(selector_warnings)
        
        # 验证数据处理配置
        data_errors, data_warnings = self._validate_data_processing_config(
            config.get('data_processing', {})
        )
        errors.extend(data_errors)
        warnings.extend(data_warnings)
        
        is_valid = len(errors) == 0
        return is_valid, errors, warnings
    
    def _validate_tdx_config(self, tdx_config: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """验证TDX配置"""
        errors = []
        warnings = []
        
        # 验证主路径
        tdx_path = tdx_config.get('tdx_path', '')
        if not tdx_path:
            errors.append("TDX主路径不能为空")
        elif not os.path.exists(tdx_path):
            warnings.append(f"TDX主路径不存在: {tdx_path}")
        
        # 验证GBBQ路径
        gbbq_path = tdx_config.get('csv_gbbq', '')
        if gbbq_path and tdx_path:
            full_gbbq_path = os.path.join(tdx_path, gbbq_path)
            if not os.path.exists(full_gbbq_path):
                warnings.append(f"GBBQ路径不存在: {full_gbbq_path}")
        
        return errors, warnings
    
    def _validate_output_config(self, output_config: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """验证输出配置"""
        errors = []
        warnings = []
        
        # 验证输出路径
        output_path = output_config.get('base_output_path', '')
        if not output_path:
            errors.append("输出路径不能为空")
        else:
            # 检查目录是否存在
            output_dir = os.path.dirname(output_path) if os.path.isfile(output_path) else output_path
            if not os.path.exists(output_dir):
                warnings.append(f"输出目录不存在: {output_dir}")
            elif not os.access(output_dir, os.W_OK):
                errors.append(f"输出目录不可写: {output_dir}")
        
        # 验证文件编码
        encoding = output_config.get('file_encoding', 'utf-8')
        if not self._validate_encoding(encoding):
            errors.append(f"无效的文件编码: {encoding}")
        
        return errors, warnings
    
    def _validate_file_selector_config(self, selector_config: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """验证智能文件选择器配置"""
        errors = []
        warnings = []
        
        # 验证策略
        strategy = selector_config.get('default_strategy', '')
        valid_strategies = ['latest_first', 'max_coverage', 'best_match', 'smart_comprehensive']
        if strategy and strategy not in valid_strategies:
            errors.append(f"无效的文件选择策略: {strategy}，有效策略: {valid_strategies}")
        
        # 验证评分权重
        weights = selector_config.get('scoring_weights', {})
        if weights:
            weight_errors = self._validate_scoring_weights(weights)
            if weight_errors:
                errors.extend(weight_errors)
        
        return errors, warnings
    
    def _validate_data_processing_config(self, data_config: Dict[str, Any]) -> Tuple[List[str], List[str]]:
        """验证数据处理配置"""
        errors = []
        warnings = []
        
        # 验证透明处理配置
        transparent = data_config.get('transparent_processing', {})
        if transparent.get('enabled') and not transparent.get('validation_enabled'):
            warnings.append("启用透明处理时建议同时启用数据验证")
        
        # 验证质量验证配置
        quality = data_config.get('quality_verification', {})
        if quality.get('strict_mode') and not quality.get('auto_fix'):
            warnings.append("严格模式下建议启用自动修复功能")
        
        return errors, warnings
    
    def _validate_path_exists(self, path: str) -> bool:
        """验证路径是否存在"""
        return os.path.exists(path) if path else False
    
    def _validate_output_path(self, path: str) -> bool:
        """验证输出路径是否可写"""
        if not path:
            return False
        
        # 如果是文件路径，检查目录
        if os.path.isfile(path):
            directory = os.path.dirname(path)
        else:
            directory = path
        
        # 检查目录是否存在且可写
        return os.path.exists(directory) and os.access(directory, os.W_OK)
    
    def _validate_scoring_weights(self, weights: Dict[str, float]) -> List[str]:
        """验证评分权重"""
        errors = []
        
        required_weights = ['freshness_weight', 'coverage_weight', 'match_weight']
        
        # 检查必需的权重
        for weight_name in required_weights:
            if weight_name not in weights:
                errors.append(f"缺少必需的权重配置: {weight_name}")
            elif not isinstance(weights[weight_name], (int, float)):
                errors.append(f"权重必须是数字: {weight_name}")
            elif not 0 <= weights[weight_name] <= 1:
                errors.append(f"权重必须在0-1之间: {weight_name} = {weights[weight_name]}")
        
        # 检查权重总和
        if all(w in weights for w in required_weights):
            total = sum(weights[w] for w in required_weights)
            if abs(total - 1.0) > 0.01:
                errors.append(f"权重总和必须为1.0，当前为: {total:.3f}")
        
        return errors
    
    def _validate_encoding(self, encoding: str) -> bool:
        """验证文件编码是否有效"""
        try:
            "test".encode(encoding)
            return True
        except (LookupError, TypeError):
            return False


# 全局验证器实例
config_validator = ConfigValidator()

# 导出
__all__ = ['ConfigValidator', 'config_validator']
