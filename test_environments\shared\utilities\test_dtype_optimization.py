#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据类型优化测试脚本
验证数据类型优化的效果和正确性
"""

import sys
import os
import pandas as pd
import numpy as np
from datetime import datetime
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

from utils.dtype_optimizer import DataTypeOptimizer

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


def create_test_dataframe():
    """创建测试用的DataFrame"""
    np.random.seed(42)  # 确保结果可重现
    
    n_rows = 10000
    
    data = {
        # 整数列 - 可以优化
        'small_int': np.random.randint(0, 100, n_rows),  # 可以优化为uint8
        'medium_int': np.random.randint(-1000, 1000, n_rows),  # 可以优化为int16
        'large_int': np.random.randint(0, 1000000, n_rows),  # 可以优化为uint32
        
        # 浮点数列 - 可以优化
        'float_as_int': np.random.randint(0, 255, n_rows).astype(float),  # 可以转为uint8
        'small_float': np.random.uniform(-100, 100, n_rows).astype(np.float64),  # 可以优化为float32
        
        # 字符串列 - 可以优化
        'category_col': np.random.choice(['A', 'B', 'C', 'D'], n_rows),  # 可以优化为category
        'unique_strings': [f"item_{i}" for i in range(n_rows)],  # 保持string类型
        
        # 日期时间列
        'date_strings': pd.date_range('2020-01-01', periods=n_rows, freq='D').strftime('%Y-%m-%d'),
        'datetime_col': pd.date_range('2020-01-01', periods=n_rows, freq='H'),
        
        # 混合类型
        'mixed_col': ['text'] * (n_rows // 2) + [1, 2, 3] * (n_rows // 6) + ['more_text'] * (n_rows - n_rows // 2 - n_rows // 6 * 3)
    }
    
    return pd.DataFrame(data)


def test_numeric_optimization():
    """测试数值列优化"""
    print("\n🧪 测试数值列优化...")
    
    # 创建测试数据
    df = pd.DataFrame({
        'uint8_candidate': np.random.randint(0, 255, 1000),
        'int8_candidate': np.random.randint(-128, 127, 1000),
        'float32_candidate': np.random.uniform(-100, 100, 1000).astype(np.float64),
        'int_as_float': np.random.randint(0, 100, 1000).astype(float)
    })
    
    print(f"优化前数据类型:")
    for col in df.columns:
        print(f"  {col}: {df[col].dtype}")
    
    memory_before = df.memory_usage(deep=True).sum()
    print(f"优化前内存使用: {memory_before / 1024:.2f} KB")
    
    # 应用优化
    df_optimized = DataTypeOptimizer.optimize_numeric_columns(df)
    
    print(f"\n优化后数据类型:")
    for col in df_optimized.columns:
        print(f"  {col}: {df_optimized[col].dtype}")
    
    memory_after = df_optimized.memory_usage(deep=True).sum()
    print(f"优化后内存使用: {memory_after / 1024:.2f} KB")
    
    memory_saved = memory_before - memory_after
    if memory_saved > 0:
        print(f"✅ 节省内存: {memory_saved / 1024:.2f} KB ({memory_saved / memory_before * 100:.1f}%)")
    else:
        print(f"⚠️  内存使用增加: {abs(memory_saved) / 1024:.2f} KB")
    
    # 验证数据一致性
    for col in df.columns:
        if pd.api.types.is_numeric_dtype(df[col]) and pd.api.types.is_numeric_dtype(df_optimized[col]):
            if not np.allclose(df[col].fillna(0), df_optimized[col].fillna(0), rtol=1e-6):
                print(f"❌ 数据一致性检查失败: {col}")
                return False
    
    print("✅ 数据一致性检查通过")
    return True


def test_string_optimization():
    """测试字符串列优化"""
    print("\n🧪 测试字符串列优化...")
    
    # 创建测试数据
    df = pd.DataFrame({
        'category_candidate': np.random.choice(['A', 'B', 'C'], 1000),  # 重复值多
        'unique_strings': [f"unique_{i}" for i in range(1000)],  # 唯一值多
        'mixed_strings': ['common'] * 800 + [f"rare_{i}" for i in range(200)]  # 混合情况
    })
    
    print(f"优化前数据类型:")
    for col in df.columns:
        unique_ratio = df[col].nunique() / len(df[col])
        print(f"  {col}: {df[col].dtype}, 唯一值比例: {unique_ratio:.2%}")
    
    memory_before = df.memory_usage(deep=True).sum()
    print(f"优化前内存使用: {memory_before / 1024:.2f} KB")
    
    # 应用优化
    df_optimized = DataTypeOptimizer.optimize_string_columns(df)
    
    print(f"\n优化后数据类型:")
    for col in df_optimized.columns:
        print(f"  {col}: {df_optimized[col].dtype}")
    
    memory_after = df_optimized.memory_usage(deep=True).sum()
    print(f"优化后内存使用: {memory_after / 1024:.2f} KB")
    
    memory_saved = memory_before - memory_after
    if memory_saved > 0:
        print(f"✅ 节省内存: {memory_saved / 1024:.2f} KB ({memory_saved / memory_before * 100:.1f}%)")
    else:
        print(f"⚠️  内存使用变化: {abs(memory_saved) / 1024:.2f} KB")
    
    # 验证数据一致性
    for col in df.columns:
        try:
            if df_optimized[col].dtype.name == 'category':
                # category类型转换为字符串进行比较
                if not df[col].astype(str).equals(df_optimized[col].astype(str)):
                    print(f"❌ 数据一致性检查失败: {col}")
                    return False
            elif df_optimized[col].dtype.name == 'string':
                # string类型转换为object进行比较
                if not df[col].equals(df_optimized[col].astype('object')):
                    print(f"❌ 数据一致性检查失败: {col}")
                    return False
            else:
                # 其他类型直接比较
                if not df[col].equals(df_optimized[col]):
                    print(f"❌ 数据一致性检查失败: {col}")
                    return False
        except Exception as e:
            print(f"❌ 数据一致性检查异常 {col}: {e}")
            return False
    
    print("✅ 数据一致性检查通过")
    return True


def test_datetime_optimization():
    """测试日期时间列优化"""
    print("\n🧪 测试日期时间列优化...")
    
    # 创建测试数据
    df = pd.DataFrame({
        'date_strings': pd.date_range('2020-01-01', periods=1000, freq='D').strftime('%Y-%m-%d'),
        'datetime_strings': pd.date_range('2020-01-01', periods=1000, freq='h').strftime('%Y-%m-%d %H:%M:%S'),
        'already_datetime': pd.date_range('2020-01-01', periods=1000, freq='D')
    })
    
    print(f"优化前数据类型:")
    for col in df.columns:
        print(f"  {col}: {df[col].dtype}")
    
    memory_before = df.memory_usage(deep=True).sum()
    print(f"优化前内存使用: {memory_before / 1024:.2f} KB")
    
    # 应用优化
    df_optimized = DataTypeOptimizer.optimize_datetime_columns(df)
    
    print(f"\n优化后数据类型:")
    for col in df_optimized.columns:
        print(f"  {col}: {df_optimized[col].dtype}")
    
    memory_after = df_optimized.memory_usage(deep=True).sum()
    print(f"优化后内存使用: {memory_after / 1024:.2f} KB")
    
    memory_change = memory_before - memory_after
    if abs(memory_change) > 1024:
        if memory_change > 0:
            print(f"✅ 节省内存: {memory_change / 1024:.2f} KB")
        else:
            print(f"⚠️  内存使用增加: {abs(memory_change) / 1024:.2f} KB (但提升了处理性能)")
    
    print("✅ 日期时间优化完成")
    return True


def test_full_optimization():
    """测试完整的DataFrame优化"""
    print("\n🧪 测试完整DataFrame优化...")
    
    # 创建复杂的测试数据
    df = create_test_dataframe()
    
    print(f"原始DataFrame信息:")
    print(f"  行数: {len(df)}")
    print(f"  列数: {len(df.columns)}")
    
    memory_before = df.memory_usage(deep=True).sum()
    print(f"  内存使用: {memory_before / 1024 / 1024:.2f} MB")
    
    # 获取优化建议
    suggestions = DataTypeOptimizer.suggest_optimizations(df)
    print(f"\n优化建议:")
    for suggestion in suggestions[:5]:  # 只显示前5个建议
        print(f"  • {suggestion}")
    
    # 应用完整优化
    df_optimized = DataTypeOptimizer.optimize_dataframe(df)
    
    memory_after = df_optimized.memory_usage(deep=True).sum()
    memory_saved = memory_before - memory_after
    
    print(f"\n优化结果:")
    print(f"  优化后内存使用: {memory_after / 1024 / 1024:.2f} MB")
    if memory_saved > 0:
        print(f"  节省内存: {memory_saved / 1024 / 1024:.2f} MB ({memory_saved / memory_before * 100:.1f}%)")
    else:
        print(f"  内存变化: {abs(memory_saved) / 1024 / 1024:.2f} MB")
    
    # 获取详细的内存使用报告
    report = DataTypeOptimizer.get_memory_usage_report(df_optimized)
    print(f"\n内存使用报告:")
    print(f"  总内存: {report['total_memory_mb']:.2f} MB")
    print(f"  行数: {report['row_count']}")
    print(f"  列数: {report['column_count']}")
    
    print("✅ 完整优化测试完成")
    return True


def main():
    """主测试函数"""
    print("🚀 数据类型优化测试")
    print(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print("=" * 60)
    
    success = True
    
    # 运行各项测试
    tests = [
        ("数值列优化测试", test_numeric_optimization),
        ("字符串列优化测试", test_string_optimization),
        ("日期时间列优化测试", test_datetime_optimization),
        ("完整DataFrame优化测试", test_full_optimization)
    ]
    
    for test_name, test_func in tests:
        print(f"\n🔍 执行 {test_name}...")
        try:
            if not test_func():
                success = False
                print(f"❌ {test_name} 失败")
            else:
                print(f"✅ {test_name} 通过")
        except Exception as e:
            success = False
            print(f"❌ {test_name} 异常: {e}")
    
    print("=" * 60)
    if success:
        print("🎉 所有数据类型优化测试通过！")
        print("✨ 数据类型优化器工作正常")
    else:
        print("❌ 部分数据类型优化测试失败")
        print("🔧 请检查优化器实现")
    
    return success


if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
