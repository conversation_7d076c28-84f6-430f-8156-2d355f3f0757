#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
精确追踪警告信息的来源

找出"⚠️ 发现缺失数据: 完全缺失0天, 不完整2天"这些警告是从哪里产生的
"""

import os
import sys
import builtins

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

# 保存原始的print函数
original_print = builtins.print

def patched_print(*args, **kwargs):
    """打补丁的print函数，追踪所有print调用"""
    # 检查是否包含我们关心的警告信息
    message = ' '.join(str(arg) for arg in args)
    
    if any(keyword in message for keyword in [
        "发现缺失数据", "数据覆盖不足", "pytdx服务器限制", 
        "缺少约", "实际数据范围", "建议：使用其他数据源"
    ]):
        import traceback
        
        print("\n" + "="*100)
        print(f"🚨 捕获到目标警告信息: {message}")
        print("="*100)
        
        print("\n📋 完整调用堆栈:")
        stack = traceback.format_stack()
        for i, frame in enumerate(stack[:-1], 1):  # 排除当前frame
            print(f"   {i:2d}. {frame.strip()}")
        
        print("\n" + "="*100)
    
    # 调用原始print函数
    return original_print(*args, **kwargs)

def install_print_patch():
    """安装print补丁"""
    builtins.print = patched_print
    print("✅ print补丁安装成功，开始追踪警告信息来源")

def test_task_execution():
    """测试任务执行过程"""
    print("\n🔍 开始测试任务执行过程")
    print("=" * 80)
    
    try:
        # 导入必要模块
        from src.mythquant.core.task_manager import TaskManager
        from src.mythquant.core.stock_processor import StockDataProcessor
        
        print("📋 步骤1: 创建组件")
        processor = StockDataProcessor("")
        task_manager = TaskManager(processor)
        print("   ✅ 组件创建成功")
        
        print("\n📋 步骤2: 获取任务列表")
        tasks = task_manager.get_enabled_tasks()
        print(f"   ✅ 获取到 {len(tasks)} 个启用的任务")
        
        if tasks:
            task = tasks[0]
            print(f"   📋 任务信息: {task.name}, 类型: {task.data_type}")
            
            print("\n📋 步骤3: 执行任务（这里可能产生警告）")
            print("   🔄 开始执行任务...")
            
            # 这里应该会触发那些警告信息
            result = task_manager.execute_task(task)
            
            print(f"   ✅ 任务执行完成，结果: {result}")
        else:
            print("   ⚠️ 没有启用的任务")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 精确追踪警告信息来源")
    print("=" * 120)
    
    # 安装print补丁
    install_print_patch()
    
    # 执行测试
    success = test_task_execution()
    
    print(f"\n🎯 追踪结果")
    print("=" * 80)
    
    if success:
        print("✅ 测试执行完成")
        print("💡 如果看到了调用堆栈，说明成功捕获了警告信息的来源")
    else:
        print("❌ 测试执行失败")
    
    print("\n💡 分析说明:")
    print("   - 如果看到调用堆栈，请重点关注堆栈中的关键调用路径")
    print("   - 特别注意TaskManager.execute_task之后的调用链")
    print("   - 查看是否有意外的模块导入或初始化触发了数据处理")

if __name__ == '__main__':
    main()
