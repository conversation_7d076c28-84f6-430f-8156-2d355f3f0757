#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
项目结构重组脚本

将混乱的根目录文件按照现代化架构重新组织
"""

import os
import shutil
from pathlib import Path
from datetime import datetime
import json


class ProjectReorganizer:
    """项目重组器"""
    
    def __init__(self):
        self.project_root = Path(__file__).parent
        self.backup_dir = self.project_root / "reorganization_backup"
        
        # 定义目标目录结构 - 基于实际项目文件
        self.target_structure = {
            # 核心兼容性模块（保留在根目录 - 最重要）
            '.': [
                'config_compatibility.py',
                'data_access_compatibility.py',
                'algorithm_compatibility.py',
                'io_compatibility.py',
                'user_config.py',
                'main.py',
                'requirements.txt',
                'README.md'
            ],

            # 新架构源码（已存在，保持不变）
            'src': [
                'src/**/*'
            ],

            # 旧版本代码归档
            'legacy': [
                'func.py',
                'func_Tdx.py',
                'func_Tdx1.py',
                'func_Util.py',
                'main_v20230219_optimized.py',
                'main_v20230219_optimized_backup.py',
                'readTDX_cw.py',
                'read_dat_file.py',
                'gbbq_*.py',
                'minute_path_helper.py',
                'check_last_record.py',
                '*.backup*'
            ],

            # 测试相关（整合现有tests目录和根目录测试文件）
            'tests': [
                'test_*.py',
                'run_tests.py',
                'final_validation_test.py',
                'comprehensive_migration_test.py',
                '*test*.py',
                'quick_*.py',
                'simple_*.py'
            ],

            # 工具和脚本
            'tools': [
                'create_*.py',
                'install_*.py',
                'fix_*.py',
                'find_*.py',
                'migrate_*.py',
                'prepare_*.py',
                'verify_*.py',
                'analyze_*.py',
                'reorganize_*.py',
                'generate_*.py',
                'dependency_check.py',
                'direct_import_test.py'
            ],

            # 文档（整合现有docs和根目录md文件）
            'docs': [
                '*.md',
                'MIGRATION_*.md',
                'PROJECT_*.md'
            ],

            # 配置和环境
            'config': [
                '*.yml',
                '*.yaml',
                'pyproject.toml',
                'setup.py',
                'tox.ini',
                'MANIFEST.in',
                'environment.yml',
                'data_processing_config.yaml',
                '*_config.py'
            ],

            # 数据、缓存和信号
            'data': [
                'tdx_servers.json',
                'project_structure_analysis.json',
                'cache_*.json'
            ],

            # 输出和结果（整合现有目录）
            'output': [
                'img.png'
            ],

            # 归档文件
            'archive': [
                '*.rar',
                '*.zip',
                '*.docx',
                'stock-analysis.zip',
                'main1v*.rar'
            ],

            # 临时文件清理
            'temp': [
                '*.pyc'
            ]
        }
    
    def reorganize(self) -> bool:
        """执行项目重组"""
        print("🔄 MythQuant 项目结构重组")
        print("=" * 50)
        
        try:
            # 1. 创建备份
            self.create_backup()
            
            # 2. 分析当前结构
            self.analyze_current_structure()
            
            # 3. 创建目标目录结构
            self.create_target_directories()
            
            # 4. 移动文件
            self.move_files()
            
            # 5. 清理空目录
            self.cleanup_empty_directories()
            
            # 6. 生成重组报告
            self.generate_reorganization_report()
            
            print("\n✅ 项目结构重组完成！")
            return True
            
        except Exception as e:
            print(f"\n❌ 项目重组失败: {e}")
            return False
    
    def create_backup(self):
        """创建备份"""
        print("💾 创建重组前备份...")
        
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_name = f"before_reorganization_{timestamp}"
        backup_path = self.backup_dir / backup_name
        
        # 创建备份目录
        backup_path.mkdir(parents=True, exist_ok=True)
        
        # 备份关键文件
        important_files = [
            '*.py',
            '*.md',
            'user_config.py',
            'requirements.txt'
        ]
        
        backup_count = 0
        for pattern in important_files:
            for file_path in self.project_root.glob(pattern):
                if file_path.is_file() and not file_path.name.startswith('.'):
                    dst = backup_path / file_path.name
                    shutil.copy2(file_path, dst)
                    backup_count += 1
        
        print(f"   ✅ 已备份 {backup_count} 个重要文件到: {backup_path}")
    
    def analyze_current_structure(self):
        """分析当前结构"""
        print("\n🔍 分析当前项目结构...")
        
        # 统计文件类型
        file_stats = {}
        total_files = 0
        
        for file_path in self.project_root.rglob('*'):
            if file_path.is_file():
                suffix = file_path.suffix.lower()
                file_stats[suffix] = file_stats.get(suffix, 0) + 1
                total_files += 1
        
        print(f"   📊 总文件数: {total_files}")
        print("   📊 文件类型分布:")
        for suffix, count in sorted(file_stats.items(), key=lambda x: x[1], reverse=True)[:10]:
            print(f"      {suffix or '(无扩展名)'}: {count} 个")
        
        # 统计根目录文件
        root_files = [f for f in self.project_root.iterdir() if f.is_file()]
        print(f"   📁 根目录文件数: {len(root_files)}")
    
    def create_target_directories(self):
        """创建目标目录结构"""
        print("\n📁 创建目标目录结构...")
        
        for target_dir in self.target_structure.keys():
            if target_dir != '.':  # 跳过根目录
                dir_path = self.project_root / target_dir
                dir_path.mkdir(parents=True, exist_ok=True)
                print(f"   ✅ 创建目录: {target_dir}")
    
    def move_files(self):
        """移动文件到目标位置"""
        print("\n🚚 移动文件到目标位置...")
        
        moved_files = 0
        
        for target_dir, patterns in self.target_structure.items():
            if target_dir == '.':  # 跳过根目录
                continue
                
            print(f"\n   📂 处理目录: {target_dir}")
            
            for pattern in patterns:
                # 处理通配符模式
                if '**' in pattern:
                    # 递归模式
                    base_pattern = pattern.replace('/**/*', '')
                    source_dir = self.project_root / base_pattern
                    if source_dir.exists() and source_dir.is_dir():
                        target_path = self.project_root / target_dir / base_pattern
                        if not target_path.exists():
                            shutil.move(str(source_dir), str(target_path))
                            print(f"      ✅ 移动目录: {base_pattern} -> {target_dir}/{base_pattern}")
                            moved_files += 1
                else:
                    # 文件模式
                    for file_path in self.project_root.glob(pattern):
                        if file_path.is_file() and file_path.parent == self.project_root:
                            target_path = self.project_root / target_dir / file_path.name
                            
                            # 避免覆盖已存在的文件
                            if target_path.exists():
                                target_path = self.project_root / target_dir / f"{file_path.stem}_moved{file_path.suffix}"
                            
                            shutil.move(str(file_path), str(target_path))
                            print(f"      ✅ 移动文件: {file_path.name} -> {target_dir}/")
                            moved_files += 1
        
        print(f"\n   📊 总共移动了 {moved_files} 个文件/目录")
    
    def cleanup_empty_directories(self):
        """清理空目录"""
        print("\n🧹 清理空目录...")
        
        cleaned_dirs = 0
        
        # 多次清理，因为删除子目录后父目录可能变空
        for _ in range(3):
            for dir_path in self.project_root.rglob('*'):
                if dir_path.is_dir() and dir_path != self.project_root:
                    try:
                        if not any(dir_path.iterdir()):  # 目录为空
                            dir_path.rmdir()
                            print(f"   🗑️ 删除空目录: {dir_path.relative_to(self.project_root)}")
                            cleaned_dirs += 1
                    except OSError:
                        pass  # 目录不为空或无法删除
        
        print(f"   📊 清理了 {cleaned_dirs} 个空目录")
    
    def generate_reorganization_report(self):
        """生成重组报告"""
        print("\n📄 生成重组报告...")
        
        # 分析重组后的结构
        new_structure = {}
        for item in self.project_root.iterdir():
            if item.is_dir():
                file_count = len(list(item.rglob('*')))
                new_structure[item.name] = file_count
            elif item.is_file():
                new_structure.setdefault('根目录文件', 0)
                new_structure['根目录文件'] += 1
        
        # 生成报告
        report = {
            'reorganization_date': datetime.now().isoformat(),
            'target_structure': dict(self.target_structure),
            'new_structure_stats': new_structure,
            'recommendations': [
                '检查兼容性模块是否在根目录正常工作',
                '验证src/mythquant新架构是否完整',
                '确认重要配置文件位置正确',
                '运行测试验证重组后功能正常'
            ]
        }
        
        report_file = self.project_root / 'reorganization_report.json'
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"   ✅ 重组报告已保存: {report_file}")
        
        # 打印新结构概览
        print("\n📊 重组后项目结构:")
        for dir_name, count in sorted(new_structure.items()):
            print(f"   📁 {dir_name}: {count} 个文件")
    
    def create_clean_structure_guide(self):
        """创建清洁结构指南"""
        guide_content = """# MythQuant 重组后项目结构

## 目录说明

### 根目录（保留核心文件）
- `*_compatibility.py` - 兼容性模块（核心）
- `user_config.py` - 用户配置文件
- `main.py` - 主程序入口

### src/mythquant/（新架构源码）
- `config/` - 配置管理模块
- `data/` - 数据访问模块
- `algorithms/` - 算法计算模块
- `io/` - 输入输出模块
- `tests/` - 测试框架

### legacy/（旧版本代码）
- `func*.py` - 旧版本功能模块
- `main_v*.py` - 旧版本主程序
- 其他旧版本文件

### tests/（测试相关）
- 单元测试
- 集成测试
- 测试工具

### tools/（工具脚本）
- 开发工具
- 部署脚本
- 维护工具

### docs/（文档）
- API文档
- 用户指南
- 架构文档

### config/（配置和环境）
- 环境配置
- 项目配置文件

### data/（数据和缓存）
- 缓存文件
- 配置数据

### output/（输出和结果）
- 日志文件
- 报告文件
- 基准测试结果

### archive/（归档文件）
- 历史版本
- 备份文件

## 使用建议

1. **日常开发**: 主要在 `src/mythquant/` 中进行
2. **兼容性**: 使用根目录的兼容性模块
3. **测试**: 运行 `tests/` 中的测试套件
4. **文档**: 查看 `docs/` 获取帮助

## 注意事项

- 兼容性模块保持在根目录，确保现有代码正常工作
- 新功能开发使用 `src/mythquant/` 架构
- 定期清理 `temp/` 和 `output/` 目录
"""
        
        guide_file = self.project_root / 'PROJECT_STRUCTURE_GUIDE.md'
        with open(guide_file, 'w', encoding='utf-8') as f:
            f.write(guide_content)
        
        print(f"   📖 结构指南已创建: {guide_file}")


def main():
    """主函数"""
    reorganizer = ProjectReorganizer()
    
    print("⚠️  重要提示:")
    print("   此操作将重新组织项目文件结构")
    print("   建议在执行前确保重要文件已备份")
    print()
    
    response = input("是否继续执行项目重组? (y/N): ")
    if response.lower() != 'y':
        print("❌ 用户取消操作")
        return False
    
    success = reorganizer.reorganize()
    
    if success:
        reorganizer.create_clean_structure_guide()
        print("\n🎉 项目结构重组成功完成！")
        print("\n📋 下一步建议:")
        print("1. 检查兼容性模块是否正常工作")
        print("2. 运行测试验证功能完整性")
        print("3. 查看 PROJECT_STRUCTURE_GUIDE.md 了解新结构")
        print("4. 根据需要调整导入路径")
        return True
    else:
        print("\n❌ 项目重组失败！")
        print("请检查错误信息，必要时从备份恢复")
        return False


if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
