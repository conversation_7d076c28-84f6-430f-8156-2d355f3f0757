# Workflow规范遵循知识库

## 概述
本文档记录了MythQuant项目中workflow规范遵循的重要知识和经验教训，帮助开发者避免违反workflow规范的问题。

## 核心原则

### 1. 文档驱动开发
- **规则**：任何核心流程代码修改前，必须先检查相关workflow文档
- **重要性**：workflow文档是系统架构的重要约束，违反会导致系统行为不一致
- **实施方法**：建立workflow文档的快速查询机制，在代码审查中增加规范检查

### 2. 增量更新过程的智能文件选择限制
- **规范来源**：`1min_workflow_improved.md` 第310行
- **明确规定**：增量更新过程不能再进行智能文件选择，而应以当前程序正在处置的文件为更新的标的
- **违反后果**：导致重复的智能文件选择，影响用户体验和系统性能

## 典型违规案例分析

### 案例1：IncrementalDownloader违反智能文件选择限制

#### 问题描述
```python
# 违规代码
class IncrementalDownloader:
    def __init__(self, output_dir: str):
        self.file_selector = SmartFileSelector(output_dir)  # ❌ 违规
```

#### 问题表现
- terminal中出现重复的智能文件选择器配置加载信息
- 在增量下载过程中重新进行文件选择，违反workflow规范
- 用户看到冗余的候选文件分析表格

#### 解决方案
```python
# 修复后代码
class IncrementalDownloader:
    def __init__(self, output_dir: str):
        self.output_dir = output_dir
        # 移除智能文件选择器依赖，遵循workflow规范
        
    def find_existing_file(self, ...):
        # 废弃此方法，直接传递已选择的文件
        self.smart_logger.warning("⚠️ 违反workflow规范，应直接传递文件路径")
        return None
```

#### 经验教训
1. **架构设计时要考虑workflow约束**：不能仅从技术角度设计，要考虑业务流程规范
2. **依赖关系要符合workflow要求**：避免在不应该的阶段引入不必要的依赖
3. **参数传递优于重新选择**：通过参数传递已选择的资源，而不是重新选择

### 案例2：TaskManager违反流程顺序执行数据质量稽核

#### 问题描述
```python
# 违规代码
class MythQuantApplication:
    def run_all_tasks(self):
        for task in tasks:
            # 在文件选择前就执行数据质量稽核
            self.flow_optimizer.suppress_technical_details("数据质量稽核")  # ❌ 违规
            success = self.task_manager.execute_task(task)
```

#### 问题表现
- terminal中在"执行任务"后立即显示"⚡ 执行数据质量稽核"
- 在没有选择具体文件的情况下就进行数据质量稽核
- 违反了workflow规定的标准化四步流程顺序

#### 解决方案
```python
# 修复后代码
class MythQuantApplication:
    def run_all_tasks(self):
        for task in tasks:
            # 注释掉违反workflow规范的调用
            # self.flow_optimizer.suppress_technical_details("数据质量稽核")  # 违反workflow规范，已屏蔽
            success = self.task_manager.execute_task(task)
```

#### 经验教训
1. **流程顺序不能随意调整**：必须严格按照workflow文档规定的顺序执行
2. **操作时机要合理**：在没有上下文的情况下执行操作是不合理的
3. **注释代码要彻底**：确保被注释的代码真正被注释，避免意外执行

## 常见违规模式

### 1. 重复调用智能组件
- **模式**：在同一流程中多次调用智能文件选择器、智能分析器等
- **检测方法**：观察terminal输出是否有重复的配置加载信息
- **预防措施**：建立调用链分析，确保智能组件只在合适的阶段调用一次

### 2. 违反流程阶段限制
- **模式**：在不应该的流程阶段调用特定功能
- **检测方法**：检查调用堆栈，确认调用发生的流程阶段
- **预防措施**：建立流程阶段检查机制，在运行时验证调用的合法性

### 3. 依赖关系违规
- **模式**：类的初始化过程中引入违反workflow的依赖
- **检测方法**：分析类的依赖关系图，检查是否符合workflow要求
- **预防措施**：采用依赖注入，在合适的时机注入依赖

### 4. 流程时机错误
- **模式**：在错误的时机执行操作，如在文件选择前进行文件质量稽核
- **检测方法**：检查操作的前置条件是否满足，分析上下文依赖关系
- **预防措施**：建立流程合理性检查机制，确保操作在正确的时机执行

### 5. 注释代码意外执行
- **模式**：被注释的代码实际上没有被注释，导致违规代码意外执行
- **检测方法**：仔细检查代码文件，确认注释符号的存在
- **预防措施**：使用代码审查工具，建立自动化的注释检查机制

## 规范遵循检查清单

### 代码修改前检查
- [ ] 是否查阅了相关的workflow文档？
- [ ] 修改是否违反了workflow中的明确规定？
- [ ] 新增的依赖关系是否符合workflow要求？
- [ ] 是否会导致重复调用智能组件？

### 代码审查检查
- [ ] 是否有违反workflow规范的调用？
- [ ] 是否有重复的输出或处理逻辑？
- [ ] 依赖关系是否合理且符合规范？
- [ ] 是否建立了必要的规范检查机制？

### 测试验证检查
- [ ] terminal输出是否简洁无重复？
- [ ] 是否按照workflow规定的流程执行？
- [ ] 是否有违规的智能组件调用？
- [ ] 用户体验是否符合预期？

## 自动化检测机制

### 1. 调用堆栈分析
```python
# 示例：检测违规调用
def detect_workflow_violation():
    import traceback
    stack = traceback.format_stack()
    for frame in stack:
        if '_save_minute_data_incremental' in frame and 'SmartFileSelector' in frame:
            raise WorkflowViolationError("违反增量更新规范")
```

### 2. 输出重复检测
```python
# 示例：检测重复输出
class OutputDuplicationDetector:
    def __init__(self):
        self.seen_outputs = set()
    
    def check_duplication(self, output_text):
        if output_text in self.seen_outputs:
            raise DuplicationError(f"重复输出: {output_text}")
        self.seen_outputs.add(output_text)
```

## 最佳实践

### 1. 参数传递优于重新计算
```python
# ✅ 好的做法
def incremental_download(existing_file: str):
    # 直接使用传入的文件，不重新选择
    pass

# ❌ 不好的做法  
def incremental_download(stock_code: str):
    # 重新进行文件选择，违反workflow规范
    file_selector = SmartFileSelector()
    existing_file = file_selector.find_best_file(stock_code)
```

### 2. 静默模式支持
```python
# ✅ 支持静默模式，避免重复输出
def compare_prices(file_path: str, stock_code: str, verbose: bool = True):
    if verbose:
        print("开始价格比较...")
    # 执行比较逻辑
```

### 3. 废弃方法的正确处理
```python
# ✅ 正确的废弃方法处理
def deprecated_method(self):
    self.logger.warning("⚠️ 此方法已废弃，违反workflow规范")
    self.logger.warning("⚠️ 请使用新的参数传递方式")
    return None
```

## 持续改进

### 1. 定期审查
- 每月审查一次workflow规范遵循情况
- 检查是否有新的违规模式出现
- 更新检测机制和预防措施

### 2. 知识分享
- 将违规案例和解决方案分享给团队
- 建立workflow规范培训机制
- 持续更新知识库内容

### 3. 工具改进
- 开发更好的自动化检测工具
- 集成到CI/CD流程中
- 提供实时的规范检查反馈

## 总结

workflow规范遵循是确保系统架构一致性和用户体验的重要保障。通过建立完善的检查机制、自动化检测工具和最佳实践，可以有效避免违规问题，提升系统质量。

**关键要点**：
1. 文档驱动开发，规范优先
2. 参数传递优于重新计算
3. 建立自动化检测机制
4. 持续改进和知识分享
