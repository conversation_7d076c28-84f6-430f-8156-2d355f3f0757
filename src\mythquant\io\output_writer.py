#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
输出写入器模块

提供统一的数据输出功能，支持多种格式和输出目标
"""

import pandas as pd
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
import logging
from datetime import datetime

# 导入新架构的模块
from src.mythquant.config.manager import ConfigManager
from .file_manager import FileManager
from .data_formatter import DataFormatter

logger = logging.getLogger(__name__)


class OutputWriter:
    """输出写入器 - 统一的数据输出接口"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化输出写入器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.file_manager = FileManager(config_manager)
        self.data_formatter = DataFormatter(config_manager)
        
        # 输出配置
        self.enable_backup = True
        self.auto_create_dirs = True
    
    def write_stock_data(self, df: pd.DataFrame, stock_code: str, 
                        data_type: str = "day", start_date: str = None, 
                        end_date: str = None, source: str = "mythquant") -> Optional[Path]:
        """
        写入股票数据到文件
        
        Args:
            df: 股票数据DataFrame
            stock_code: 股票代码
            data_type: 数据类型 ("day", "minute", "1min", "5min")
            start_date: 开始日期
            end_date: 结束日期
            source: 数据来源
            
        Returns:
            输出文件路径或None
        """
        if df is None or df.empty:
            logger.warning("数据为空，无法写入")
            return None
        
        try:
            # 数据验证
            is_valid, errors = self.data_formatter.validate_data_format(df, data_type)
            if not is_valid:
                logger.error(f"数据格式验证失败: {errors}")
                return None
            
            # 确定实际的数据时间范围
            actual_start, actual_end = self._get_actual_date_range(df, data_type)
            if start_date is None:
                start_date = actual_start
            if end_date is None:
                end_date = actual_end
            
            # 生成文件名
            filename = self.data_formatter.format_filename(
                stock_code, data_type, start_date, end_date, source
            )
            
            # 获取输出路径
            output_path = self.file_manager.get_output_path(filename)
            
            # 备份现有文件
            if output_path.exists() and self.enable_backup:
                backup_path = self.file_manager.backup_file(output_path)
                if backup_path:
                    logger.info(f"已备份现有文件: {backup_path}")
            
            # 格式化数据
            formatted_content = self.data_formatter.format_stock_data_for_output(
                df, stock_code, data_type
            )
            
            if not formatted_content:
                logger.error("数据格式化失败")
                return None
            
            # 写入文件
            success = self.file_manager.write_text_file(
                output_path, formatted_content, create_dirs=self.auto_create_dirs
            )
            
            if success:
                logger.info(f"成功写入股票数据: {output_path}")
                return output_path
            else:
                logger.error(f"写入股票数据失败: {output_path}")
                return None
                
        except Exception as e:
            logger.error(f"写入股票数据异常: {e}")
            return None
    
    def _get_actual_date_range(self, df: pd.DataFrame, data_type: str) -> tuple[str, str]:
        """
        获取数据的实际日期范围
        
        Args:
            df: 数据DataFrame
            data_type: 数据类型
            
        Returns:
            (开始日期, 结束日期)
        """
        try:
            time_column = 'datetime' if data_type == "minute" else 'date'
            
            if time_column not in df.columns:
                # 如果没有时间列，使用当前日期
                current_date = datetime.now().strftime("%Y%m%d")
                return current_date, current_date
            
            # 转换为datetime
            time_series = pd.to_datetime(df[time_column])
            
            start_date = time_series.min().strftime("%Y%m%d")
            end_date = time_series.max().strftime("%Y%m%d")
            
            return start_date, end_date
            
        except Exception as e:
            logger.error(f"获取实际日期范围失败: {e}")
            current_date = datetime.now().strftime("%Y%m%d")
            return current_date, current_date
    
    def write_csv_data(self, df: pd.DataFrame, filename: str, 
                      subdirectory: str = None) -> Optional[Path]:
        """
        写入CSV数据
        
        Args:
            df: 数据DataFrame
            filename: 文件名
            subdirectory: 子目录
            
        Returns:
            输出文件路径或None
        """
        if df is None or df.empty:
            logger.warning("数据为空，无法写入CSV")
            return None
        
        try:
            # 确保文件名有.csv扩展名
            if not filename.endswith('.csv'):
                filename += '.csv'
            
            # 获取输出路径
            output_path = self.file_manager.get_output_path(filename, subdirectory)
            
            # 备份现有文件
            if output_path.exists() and self.enable_backup:
                self.file_manager.backup_file(output_path)
            
            # 写入CSV
            df.to_csv(output_path, index=False, encoding=self.file_manager.file_encoding)
            
            logger.info(f"成功写入CSV数据: {output_path}")
            return output_path
            
        except Exception as e:
            logger.error(f"写入CSV数据失败: {e}")
            return None
    
    def write_json_data(self, data: Dict[str, Any], filename: str, 
                       subdirectory: str = None) -> Optional[Path]:
        """
        写入JSON数据
        
        Args:
            data: JSON数据
            filename: 文件名
            subdirectory: 子目录
            
        Returns:
            输出文件路径或None
        """
        try:
            # 确保文件名有.json扩展名
            if not filename.endswith('.json'):
                filename += '.json'
            
            # 获取输出路径
            output_path = self.file_manager.get_output_path(filename, subdirectory)
            
            # 备份现有文件
            if output_path.exists() and self.enable_backup:
                self.file_manager.backup_file(output_path)
            
            # 写入JSON
            success = self.file_manager.write_json_file(output_path, data)
            
            if success:
                logger.info(f"成功写入JSON数据: {output_path}")
                return output_path
            else:
                logger.error(f"写入JSON数据失败: {output_path}")
                return None
                
        except Exception as e:
            logger.error(f"写入JSON数据异常: {e}")
            return None
    
    def write_report(self, content: str, report_name: str, 
                    subdirectory: str = "reports") -> Optional[Path]:
        """
        写入报告文件
        
        Args:
            content: 报告内容
            report_name: 报告名称
            subdirectory: 子目录，默认为reports
            
        Returns:
            输出文件路径或None
        """
        try:
            # 添加时间戳到报告名称
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            if not report_name.endswith('.txt') and not report_name.endswith('.md'):
                filename = f"{report_name}_{timestamp}.txt"
            else:
                name_part, ext = report_name.rsplit('.', 1)
                filename = f"{name_part}_{timestamp}.{ext}"
            
            # 获取输出路径
            output_path = self.file_manager.get_output_path(filename, subdirectory)
            
            # 写入报告
            success = self.file_manager.write_text_file(output_path, content)
            
            if success:
                logger.info(f"成功写入报告: {output_path}")
                return output_path
            else:
                logger.error(f"写入报告失败: {output_path}")
                return None
                
        except Exception as e:
            logger.error(f"写入报告异常: {e}")
            return None
    
    def batch_write_stock_data(self, data_dict: Dict[str, pd.DataFrame], 
                              data_type: str = "day", source: str = "mythquant") -> Dict[str, Optional[Path]]:
        """
        批量写入股票数据
        
        Args:
            data_dict: 股票代码到数据DataFrame的映射
            data_type: 数据类型
            source: 数据来源
            
        Returns:
            股票代码到输出文件路径的映射
        """
        results = {}
        
        try:
            total_stocks = len(data_dict)
            logger.info(f"开始批量写入{total_stocks}只股票的{data_type}数据")
            
            for i, (stock_code, df) in enumerate(data_dict.items(), 1):
                try:
                    output_path = self.write_stock_data(df, stock_code, data_type, source=source)
                    results[stock_code] = output_path
                    
                    if i % 10 == 0 or i == total_stocks:
                        logger.info(f"批量写入进度: {i}/{total_stocks}")
                        
                except Exception as e:
                    logger.error(f"写入{stock_code}数据失败: {e}")
                    results[stock_code] = None
            
            success_count = sum(1 for path in results.values() if path is not None)
            logger.info(f"批量写入完成: {success_count}/{total_stocks} 成功")
            
            return results
            
        except Exception as e:
            logger.error(f"批量写入股票数据异常: {e}")
            return results
    
    def get_output_summary(self, output_paths: List[Optional[Path]]) -> Dict[str, Any]:
        """
        获取输出摘要
        
        Args:
            output_paths: 输出文件路径列表
            
        Returns:
            输出摘要字典
        """
        try:
            valid_paths = [path for path in output_paths if path is not None]
            
            summary = {
                'total_files': len(output_paths),
                'successful_files': len(valid_paths),
                'failed_files': len(output_paths) - len(valid_paths),
                'success_rate': len(valid_paths) / len(output_paths) * 100 if output_paths else 0,
                'output_directory': str(self.file_manager.base_output_path),
                'files': [str(path) for path in valid_paths]
            }
            
            return summary
            
        except Exception as e:
            logger.error(f"获取输出摘要失败: {e}")
            return {}


# 导出
__all__ = ['OutputWriter']
