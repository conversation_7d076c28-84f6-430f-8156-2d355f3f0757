#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
解决方案模板系统
为常见问题提供标准化的解决方案模板
"""

from typing import Dict, List, Optional
from enum import Enum
from dataclasses import dataclass

class TemplateType(Enum):
    """模板类型"""
    BUG_FIX = "bug修复模板"
    FEATURE_IMPLEMENTATION = "功能实现模板"
    PERFORMANCE_OPTIMIZATION = "性能优化模板"
    ARCHITECTURE_REFACTOR = "架构重构模板"
    CONFIGURATION_SETUP = "配置设置模板"
    DATA_PROCESSING = "数据处理模板"
    FILE_HANDLING = "文件处理模板"
    ERROR_HANDLING = "错误处理模板"

@dataclass
class SolutionTemplate:
    """解决方案模板"""
    name: str
    template_type: TemplateType
    description: str
    steps: List[str]
    code_examples: Dict[str, str]
    files_to_modify: List[str]
    testing_checklist: List[str]
    common_pitfalls: List[str]
    related_templates: List[str]
    tags: List[str]

class SolutionTemplateManager:
    """解决方案模板管理器"""
    
    def __init__(self):
        self.templates = self._init_templates()
    
    def get_template(self, template_name: str) -> Optional[SolutionTemplate]:
        """获取指定模板"""
        return self.templates.get(template_name)
    
    def find_templates_by_type(self, template_type: TemplateType) -> List[SolutionTemplate]:
        """按类型查找模板"""
        return [template for template in self.templates.values() 
                if template.template_type == template_type]
    
    def find_templates_by_tag(self, tag: str) -> List[SolutionTemplate]:
        """按标签查找模板"""
        return [template for template in self.templates.values() 
                if tag in template.tags]
    
    def suggest_template(self, problem_description: str, problem_type: str) -> Optional[SolutionTemplate]:
        """根据问题描述推荐模板"""
        problem_lower = problem_description.lower()
        
        # 关键词匹配
        keyword_mapping = {
            '正则表达式': 'regex_fix_template',
            '文件匹配': 'file_matching_template',
            '数据下载': 'data_download_template',
            '性能优化': 'performance_optimization_template',
            '配置问题': 'configuration_fix_template',
            '架构重构': 'architecture_refactor_template'
        }
        
        for keyword, template_name in keyword_mapping.items():
            if keyword in problem_lower:
                return self.get_template(template_name)
        
        return None
    
    def generate_solution_from_template(self, template_name: str, 
                                      context: Dict[str, str]) -> str:
        """基于模板生成解决方案"""
        template = self.get_template(template_name)
        if not template:
            return "未找到指定模板"
        
        solution_parts = []
        
        # 添加描述
        solution_parts.append(f"**解决方案**: {template.description}")
        
        # 添加步骤
        solution_parts.append("**实施步骤**:")
        for i, step in enumerate(template.steps, 1):
            formatted_step = self._format_template_string(step, context)
            solution_parts.append(f"{i}. {formatted_step}")
        
        # 添加代码示例
        if template.code_examples:
            solution_parts.append("**代码示例**:")
            for lang, code in template.code_examples.items():
                formatted_code = self._format_template_string(code, context)
                solution_parts.append(f"```{lang}\n{formatted_code}\n```")
        
        # 添加测试清单
        if template.testing_checklist:
            solution_parts.append("**测试验证**:")
            for check in template.testing_checklist:
                formatted_check = self._format_template_string(check, context)
                solution_parts.append(f"- {formatted_check}")
        
        # 添加注意事项
        if template.common_pitfalls:
            solution_parts.append("**注意事项**:")
            for pitfall in template.common_pitfalls:
                solution_parts.append(f"⚠️ {pitfall}")
        
        return "\n\n".join(solution_parts)
    
    def _format_template_string(self, template_str: str, context: Dict[str, str]) -> str:
        """格式化模板字符串"""
        try:
            return template_str.format(**context)
        except KeyError:
            return template_str
    
    def _init_templates(self) -> Dict[str, SolutionTemplate]:
        """初始化模板库"""
        templates = {}
        
        # 正则表达式修复模板
        templates['regex_fix_template'] = SolutionTemplate(
            name="正则表达式修复模板",
            template_type=TemplateType.BUG_FIX,
            description="修复正则表达式匹配问题的标准流程",
            steps=[
                "分析当前正则表达式的匹配逻辑",
                "识别无法匹配的具体模式",
                "设计新的正则表达式支持目标格式",
                "使用非捕获组保持向后兼容性",
                "在相关文件中同步更新正则表达式",
                "编写测试用例验证匹配效果"
            ],
            code_examples={
                "python": """# 原正则表达式
old_pattern = r'(\\d+min)_0_(\\d{{6}})_(\\d{{8}})-(\\d{{8}})_来源互联网\\.txt'

# 修复后的正则表达式
new_pattern = r'(\\d+min)_0_(\\d{{6}})_(\\d{{8}})-(\\d{{8}})_来源互联网(?:（\\d{{12}}）)?\\.txt'

# 测试匹配
import re
test_files = [
    '{filename_without_timestamp}',
    '{filename_with_timestamp}'
]

for filename in test_files:
    if re.match(new_pattern, filename):
        print(f"✅ 匹配成功: {{filename}}")
    else:
        print(f"❌ 匹配失败: {{filename}}")"""
            },
            files_to_modify=[
                "utils/smart_file_selector.py",
                "utils/incremental_download_validator.py"
            ],
            testing_checklist=[
                "测试无时间戳文件名的匹配",
                "测试带时间戳文件名的匹配",
                "验证向后兼容性",
                "检查相关模块的正则表达式同步"
            ],
            common_pitfalls=[
                "忘记在所有相关文件中同步更新正则表达式",
                "新正则表达式破坏了向后兼容性",
                "没有充分测试各种文件名格式"
            ],
            related_templates=["file_matching_template"],
            tags=["正则表达式", "文件处理", "bug修复"]
        )
        
        # 数据下载优化模板
        templates['data_download_template'] = SolutionTemplate(
            name="数据下载优化模板",
            template_type=TemplateType.PERFORMANCE_OPTIMIZATION,
            description="优化数据下载性能和可靠性的标准方案",
            steps=[
                "分析当前数据获取机制的限制",
                "计算实际需要的数据量",
                "实现智能数据量计算逻辑",
                "调整数据获取的上下限",
                "添加数据获取的缓冲机制",
                "实现失败重试和降级策略"
            ],
            code_examples={
                "python": """def _calculate_smart_data_count(self, start_date: str, frequency: str) -> int:
    \"\"\"智能计算需要获取的数据条数\"\"\"
    # 计算交易日数量
    trading_days = self._count_trading_days(start_date, datetime.now())
    
    # 根据频率计算每日数据条数
    bars_per_day = 240 // int(frequency.replace('min', ''))
    
    # 计算总需求量（含缓冲）
    required_count = int(trading_days * bars_per_day * 1.2)
    
    # 应用合理上下限
    return max({min_count}, min(required_count, {max_count}))"""
            },
            files_to_modify=[
                "utils/pytdx_downloader.py",
                "utils/stock_data_downloader.py"
            ],
            testing_checklist=[
                "测试近期日期的数据获取",
                "测试历史日期的数据获取",
                "验证数据量计算的准确性",
                "检查边界条件处理"
            ],
            common_pitfalls=[
                "数据量上限设置过低",
                "没有考虑交易日的实际计算",
                "缺少失败重试机制"
            ],
            related_templates=["performance_optimization_template"],
            tags=["数据下载", "性能优化", "pytdx"]
        )
        
        # 配置管理模板
        templates['configuration_fix_template'] = SolutionTemplate(
            name="配置管理修复模板",
            template_type=TemplateType.CONFIGURATION_SETUP,
            description="解决配置相关问题的标准流程",
            steps=[
                "识别配置问题的具体表现",
                "检查配置文件的结构和格式",
                "验证配置项的类型和取值范围",
                "实现配置验证和默认值机制",
                "添加配置错误的友好提示",
                "更新配置文档和示例"
            ],
            code_examples={
                "python": """# 配置验证示例
def validate_config(self, config: dict) -> bool:
    \"\"\"验证配置的有效性\"\"\"
    required_keys = ['{required_key1}', '{required_key2}']
    
    for key in required_keys:
        if key not in config:
            self.logger.error(f"缺少必需的配置项: {{key}}")
            return False
    
    # 类型验证
    if not isinstance(config.get('{key_name}'), {expected_type}):
        self.logger.error(f"配置项 {key_name} 类型错误")
        return False
    
    return True"""
            },
            files_to_modify=[
                "user_config.py",
                "core/config_manager.py"
            ],
            testing_checklist=[
                "测试默认配置的加载",
                "测试无效配置的处理",
                "验证配置验证逻辑",
                "检查配置文档的准确性"
            ],
            common_pitfalls=[
                "配置验证不够严格",
                "缺少合理的默认值",
                "错误信息不够友好"
            ],
            related_templates=["error_handling_template"],
            tags=["配置管理", "错误处理", "验证"]
        )
        
        return templates

def demonstrate_solution_templates():
    """演示解决方案模板系统"""
    print("🚀 解决方案模板系统演示")
    print("=" * 50)
    
    template_manager = SolutionTemplateManager()
    
    # 展示可用模板
    print("📋 可用模板:")
    for name, template in template_manager.templates.items():
        print(f"  - {template.name} ({template.template_type.value})")
    
    # 演示模板推荐
    print(f"\n🔍 模板推荐演示:")
    problem = "正则表达式无法匹配带时间戳的文件名"
    suggested = template_manager.suggest_template(problem, "bug_fix")
    if suggested:
        print(f"  问题: {problem}")
        print(f"  推荐模板: {suggested.name}")
    
    # 演示解决方案生成
    print(f"\n📝 解决方案生成演示:")
    context = {
        'filename_without_timestamp': '1min_0_000617_20250401-20250726_来源互联网.txt',
        'filename_with_timestamp': '1min_0_000617_20250401-20250726_来源互联网（202507261741）.txt',
        'min_count': '1000',
        'max_count': '50000'
    }
    
    solution = template_manager.generate_solution_from_template('regex_fix_template', context)
    print("生成的解决方案:")
    print(solution[:300] + "..." if len(solution) > 300 else solution)
    
    print("\n✅ 解决方案模板系统初始化完成")

if __name__ == '__main__':
    demonstrate_solution_templates()
