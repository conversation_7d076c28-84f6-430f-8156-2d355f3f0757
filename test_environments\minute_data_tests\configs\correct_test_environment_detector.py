#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
正确的测试环境Workflow违规检测器
确保在测试环境中使用正确的test_前缀文件进行测试
"""

import os
import sys
import time
import traceback

# 获取项目根目录
current_dir = os.path.dirname(os.path.abspath(__file__))
test_env_dir = os.path.dirname(current_dir)
project_root = os.path.dirname(os.path.dirname(test_env_dir))

# 强制切换到测试环境
os.chdir(test_env_dir)
sys.path.insert(0, project_root)

print(f"=== 测试环境设置 ===")
print(f"当前工作目录: {os.getcwd()}")
print(f"项目根目录: {project_root}")

# 验证测试文件存在
TARGET_TEST_FILE = "input_data/test_1min_0_000617_20250320-20250704_来源互联网（202507262158）.txt"
if not os.path.exists(TARGET_TEST_FILE):
    print(f"❌ 错误：目标测试文件不存在: {TARGET_TEST_FILE}")
    print("请确保测试文件存在后再运行")
    sys.exit(1)

print(f"✅ 目标测试文件存在: {TARGET_TEST_FILE}")

# 检查文件基本信息
file_size = os.path.getsize(TARGET_TEST_FILE)
with open(TARGET_TEST_FILE, 'r', encoding='utf-8') as f:
    lines = sum(1 for _ in f)
print(f"📊 测试文件信息: {file_size} bytes, {lines} 行")

# 全局状态跟踪
taskmanager_started = False
workflow_started = False
early_pytdx_calls = []

def install_test_environment_monitoring():
    """安装测试环境专用监控"""
    global taskmanager_started, workflow_started, early_pytdx_calls
    
    try:
        # 1. 监控TaskManager开始
        from src.mythquant.core.task_manager import TaskManager
        original_execute = TaskManager._execute_minute_task
        
        def test_env_execute(self, task, target_stocks):
            global taskmanager_started
            taskmanager_started = True
            current_time = time.time()
            
            print(f"\n=== TASKMANAGER STARTED IN TEST ENV ===")
            print(f"Time: {current_time}")
            print(f"Working Directory: {os.getcwd()}")
            print(f"Target Stocks: {target_stocks}")
            
            result = original_execute(self, task, target_stocks)
            
            print(f"=== TASKMANAGER ENDED ===")
            return result
        
        TaskManager._execute_minute_task = test_env_execute
        
        # 2. 监控五步流程开始
        from utils.structured_internet_minute_downloader import StructuredInternetMinuteDownloader
        original_five_step = StructuredInternetMinuteDownloader._execute_five_step_process
        
        def test_env_five_step(self, stock_code, start_date, end_date, frequency, original_frequency):
            global workflow_started
            workflow_started = True
            current_time = time.time()
            
            print(f"\n=== WORKFLOW STARTED IN TEST ENV ===")
            print(f"Time: {current_time}")
            print(f"Stock: {stock_code}")
            print(f"Date Range: {start_date} - {end_date}")
            
            return original_five_step(self, stock_code, start_date, end_date, frequency, original_frequency)
        
        StructuredInternetMinuteDownloader._execute_five_step_process = test_env_five_step
        
        # 3. 强制智能文件选择器使用测试文件
        from utils.smart_file_selector import SmartFileSelector
        original_find_best = SmartFileSelector.find_best_file
        
        def test_env_find_best(self, stock_code, data_type, target_start, target_end, strategy='smart_comprehensive'):
            print(f"\n=== SMART FILE SELECTOR IN TEST ENV ===")
            print(f"Working Directory: {os.getcwd()}")
            print(f"Stock: {stock_code}, Type: {data_type}")
            print(f"Target Range: {target_start} - {target_end}")
            
            # 在测试环境中，强制使用测试文件
            if os.path.exists(TARGET_TEST_FILE):
                print(f"✅ 强制使用测试文件: {TARGET_TEST_FILE}")
                
                # 构造文件信息
                file_info = {
                    'file_path': TARGET_TEST_FILE,
                    'start_date': '20250320',
                    'end_date': '20250704',
                    'total_days': 107,
                    'file_size': os.path.getsize(TARGET_TEST_FILE),
                    'quality_score': 100.0,
                    'selection_reason': 'Forced test file selection'
                }
                
                return TARGET_TEST_FILE, file_info
            else:
                print(f"❌ 测试文件不存在，回退到原始逻辑")
                return original_find_best(self, stock_code, data_type, target_start, target_end, strategy)
        
        SmartFileSelector.find_best_file = test_env_find_best
        
        # 4. 监控所有pytdx调用
        from utils.pytdx_downloader import PytdxDownloader
        
        # 监控实例化
        original_init = PytdxDownloader.__init__
        
        def test_env_init(self, *args, **kwargs):
            global taskmanager_started, workflow_started, early_pytdx_calls
            current_time = time.time()
            
            print(f"\n*** PYTDX DOWNLOADER INIT IN TEST ENV ***")
            print(f"Time: {current_time}")
            print(f"TaskManager started: {taskmanager_started}")
            print(f"Workflow started: {workflow_started}")
            
            # 检查是否是提前调用
            if taskmanager_started and not workflow_started:
                print(f"!!! EARLY PYTDX INIT DETECTED IN TEST ENV !!!")
                stack = traceback.format_stack()
                early_pytdx_calls.append({
                    'type': 'init',
                    'time': current_time,
                    'stack': stack
                })
                
                print("Call stack:")
                for i, frame in enumerate(stack[-8:], 1):
                    print(f"  {i:2d}. {frame.strip()}")
            
            return original_init(self, *args, **kwargs)
        
        PytdxDownloader.__init__ = test_env_init
        
        # 监控下载方法
        original_download = PytdxDownloader.download_minute_data
        
        def test_env_download(self, stock_code, start_date, end_date, frequency='1min', suppress_warnings=False):
            global taskmanager_started, workflow_started, early_pytdx_calls
            current_time = time.time()
            
            print(f"\n*** PYTDX DOWNLOAD IN TEST ENV ***")
            print(f"Time: {current_time}")
            print(f"Stock: {stock_code}, Range: {start_date}-{end_date}")
            print(f"TaskManager started: {taskmanager_started}")
            print(f"Workflow started: {workflow_started}")
            
            # 检查是否是提前调用
            if taskmanager_started and not workflow_started:
                print(f"!!! EARLY PYTDX DOWNLOAD DETECTED IN TEST ENV !!!")
                stack = traceback.format_stack()
                early_pytdx_calls.append({
                    'type': 'download',
                    'stock_code': stock_code,
                    'date_range': f"{start_date}-{end_date}",
                    'time': current_time,
                    'stack': stack
                })
                
                print("Call stack:")
                for i, frame in enumerate(stack[-8:], 1):
                    print(f"  {i:2d}. {frame.strip()}")
            
            return original_download(self, stock_code, start_date, end_date, frequency, suppress_warnings)
        
        PytdxDownloader.download_minute_data = test_env_download
        
        print("✅ 测试环境监控安装完成")
        return True
        
    except Exception as e:
        print(f"❌ 监控安装失败: {e}")
        traceback.print_exc()
        return False

def run_test_in_correct_environment():
    """在正确的测试环境中运行测试"""
    print(f"\n=== 在测试环境中运行main.py ===")
    print(f"当前目录: {os.getcwd()}")
    print(f"目标测试文件: {TARGET_TEST_FILE}")
    
    try:
        # 确保使用项目根目录的main.py
        sys.path.insert(0, project_root)
        from main import main
        
        result = main()
        print(f"✅ 测试完成，结果: {result}")
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        traceback.print_exc()
        return False

def analyze_test_results():
    """分析测试结果"""
    global early_pytdx_calls
    
    print(f"\n=== 测试环境分析结果 ===")
    print(f"提前pytdx调用数量: {len(early_pytdx_calls)}")
    
    if early_pytdx_calls:
        print(f"\n🚨 发现提前pytdx调用:")
        for i, call in enumerate(early_pytdx_calls, 1):
            print(f"\n提前调用 #{i}:")
            print(f"  类型: {call['type']}")
            print(f"  时间: {call['time']}")
            
            if 'stock_code' in call:
                print(f"  股票: {call['stock_code']}")
            if 'date_range' in call:
                print(f"  范围: {call['date_range']}")
            
            print(f"  关键调用栈:")
            for frame in call['stack']:
                if any(keyword in frame for keyword in [
                    'task_manager', 'TaskManager', 'execute_minute'
                ]):
                    print(f"    >>> {frame.strip()}")
    else:
        print(f"✅ 未发现提前pytdx调用")

def main():
    """主函数"""
    print("正确的测试环境Workflow违规检测器")
    print("=" * 60)
    print(f"目标测试文件: {TARGET_TEST_FILE}")
    print("=" * 60)
    
    if not install_test_environment_monitoring():
        return 1
    
    success = run_test_in_correct_environment()
    analyze_test_results()
    
    print(f"\n=== 检测完成 ===")
    print(f"测试成功: {success}")
    print(f"提前调用数量: {len(early_pytdx_calls)}")
    print(f"当前工作目录: {os.getcwd()}")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
