{"timestamp": "2025-07-31T22:47:36.354216", "error_statistics": {"error_counts": {"BUSINESS": 1}, "total_errors": 1, "recent_errors": [{"error_id": "BUSINESS_1753973256348", "timestamp": "2025-07-31T22:47:36.348215", "category": "BUSINESS", "error_type": "TypeError", "error_message": "print_sub_process() got an unexpected keyword argument 'details'", "stock_code": null, "operation": "结构化下载流程", "context": {"stock_code": "000617"}, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\structured_internet_minute_downloader.py\", line 68, in execute_structured_download\n    success = self._execute_four_step_process(\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\structured_internet_minute_downloader.py\", line 98, in _execute_four_step_process\n    print_sub_process(\nTypeError: print_sub_process() got an unexpected keyword argument 'details'\n"}], "error_history_size": 1}, "performance_statistics": {}}