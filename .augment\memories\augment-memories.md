# MythQuant项目记忆库

## 反复出现的问题模式

### 测试与生产环境不一致问题 (2025-08-06)
**问题描述**：测试通过但生产环境仍有错误
**典型表现**：
- 单元测试或组件测试通过
- 主程序运行时出现导入错误、方法缺失等问题
- 配置在不同上下文中加载失败

**根本原因**：
1. 测试环境与生产环境的导入路径不一致
2. 多个同名类存在，测试时调用了不同的实现
3. 依赖模块在测试时可用但生产时缺失

**解决方案**：
- 测试后必须在生产环境中再次验证
- 使用端到端测试而非仅依赖单元测试
- 确保所有导入路径在不同环境中都正确
- 检查项目中是否存在多个同名类或方法

### 多重配置管理器问题 (2025-08-06)
**问题描述**：项目中存在多个ConfigManager类，导致方法缺失错误
**典型表现**：
- `'ConfigManager' object has no attribute 'get_xxx_config'`
- 不同模块调用不同的ConfigManager实现
- 新增方法只在部分ConfigManager中实现

**根本原因**：
1. 历史遗留导致多个ConfigManager共存
2. 新功能只在部分实现中添加
3. 缺乏统一的配置管理接口

**解决方案**：
- 识别项目中所有ConfigManager实现
- 确保所有实现都包含相同的核心方法
- 逐步统一到DDD架构的配置管理器
- 使用接口或抽象基类确保一致性

### 依赖模块缺失问题 (2025-08-06)
**问题描述**：运行时发现缺少必要的模块
**典型表现**：
- `No module named 'xxx'`
- 从归档文件中复制模块后问题解决
- 测试时模块可用但生产时缺失

**根本原因**：
1. 模块被误删或移动到归档目录
2. 依赖关系没有正确维护
3. 模块重构时遗漏了某些文件

**解决方案**：
- 建立完整的依赖关系图
- 定期检查关键模块的存在性
- 从归档中恢复缺失的模块
- 使用自动化工具检测依赖完整性

### 导入路径问题 (2025-08-06)
**问题描述**：导入语句在不同环境下失败
**典型表现**：
- `ModuleNotFoundError`
- 相对导入和绝对导入混用
- 路径在不同操作系统下不一致

**根本原因**：
1. Python路径配置不一致
2. 相对导入和绝对导入使用不当
3. 项目结构变更后导入未更新

**解决方案**：
- 统一使用绝对导入
- 确保项目根目录在Python路径中
- 使用标准的包结构和导入方式
- 避免动态修改sys.path

## 成功的解决模式

### DDD架构实施成功模式 (2025-08-06)
**成功要素**：
1. **渐进式实施**：不破坏现有功能的前提下逐步引入
2. **向后兼容**：确保现有代码无需修改即可使用
3. **门面模式**：使用门面隐藏架构复杂性
4. **适配器模式**：将旧接口适配到新架构
5. **完整测试**：组件测试 + 集成测试 + 端到端测试

**关键步骤**：
1. 创建完整的DDD分层架构
2. 实现适配器保持向后兼容
3. 逐步迁移功能到新架构
4. 保持用户友好的配置接口
5. 进行全面的测试验证

### 配置管理统一化成功模式 (2025-08-06)
**成功要素**：
1. **保持用户接口不变**：user_config.py继续作为用户配置入口
2. **内部架构升级**：使用DDD架构管理配置
3. **多重实现兼容**：确保所有ConfigManager都有必要方法
4. **渐进式迁移**：逐步将功能迁移到统一架构

## 技术债务记录

### 需要清理的技术债务 (2025-08-06)
1. **多重ConfigManager**：存在多个ConfigManager实现，需要统一
2. **导入路径混乱**：部分模块使用了不正确的导入路径
3. **依赖关系不清晰**：某些模块的依赖关系需要梳理
4. **测试覆盖不足**：缺乏端到端测试，主要依赖单元测试

### 已解决的技术债务 (2025-08-06)
1. **配置管理混乱**：通过DDD架构统一了配置管理
2. **向后兼容性问题**：通过适配器模式解决
3. **用户体验问题**：保持了user_config.py的简单性

## 最佳实践总结

### 架构升级最佳实践 (2025-08-06)
1. **渐进式升级**：不要一次性替换整个架构
2. **向后兼容优先**：确保现有用户不受影响
3. **测试驱动**：先写测试，再进行架构升级
4. **文档同步**：架构变更时同步更新文档
5. **用户沟通**：提前告知用户架构变更的影响

### 问题排查最佳实践 (2025-08-06)
1. **系统性思考**：不要只看表面错误，要分析根本原因
2. **环境一致性**：确保测试和生产环境的一致性
3. **依赖完整性**：检查所有依赖模块是否完整
4. **多重实现检查**：识别项目中的多重实现问题
5. **端到端验证**：进行完整的端到端测试

### 配置管理最佳实践 (2025-08-06)
1. **用户友好性**：保持配置接口的简单性
2. **内部复杂性隐藏**：使用门面模式隐藏内部复杂性
3. **统一接口**：确保所有配置管理器提供一致的接口
4. **验证机制**：实现完整的配置验证机制
5. **备份恢复**：提供配置备份和恢复功能
