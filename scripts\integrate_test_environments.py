#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试环境整合脚本

将分散的测试目录整合到统一的TestCase体系中

作者: AI Assistant
创建时间: 2025-07-29
"""

import os
import shutil
import json
from datetime import datetime
from pathlib import Path


class TestEnvironmentIntegrator:
    """测试环境整合器"""
    
    def __init__(self):
        """初始化整合器"""
        # 目标TestCase根目录
        self.testcase_root = Path("H:/MPV1.17/T0002/signals/TestCase")
        
        # 项目根目录
        self.project_root = Path(".")
        
        # 当前分散的测试目录
        self.scattered_dirs = {
            'tests': 'tests',
            'test_cases': 'test_cases', 
            'test_csv_output': 'test_csv_output',
            'test_txt_output': 'test_txt_output',
            'test_output_debug': 'test_output_debug',
            'test_results': 'test_results'
        }
        
        # 新的TestCase结构
        self.new_structure = {
            '01': '1分钟数据测试环境',
            '02': '日线数据测试环境', 
            '03': '性能测试环境',
            '04': '回归测试环境',
            '05': '集成测试环境',
            'shared': '共享测试资源'
        }
    
    def analyze_current_structure(self):
        """分析当前测试目录结构"""
        print("🔍 分析当前测试目录结构")
        print("=" * 80)
        
        analysis = {
            'testcase_exists': self.testcase_root.exists(),
            'scattered_dirs': {},
            'total_files': 0,
            'total_size_mb': 0
        }
        
        # 分析TestCase目录
        if self.testcase_root.exists():
            print(f"✅ TestCase根目录存在: {self.testcase_root}")
            for subdir in self.testcase_root.iterdir():
                if subdir.is_dir():
                    file_count = len(list(subdir.rglob('*')))
                    print(f"   📁 {subdir.name}: {file_count} 个文件")
        else:
            print(f"❌ TestCase根目录不存在: {self.testcase_root}")
        
        # 分析分散的测试目录
        print(f"\n📊 分散测试目录分析:")
        for dir_name, dir_path in self.scattered_dirs.items():
            full_path = self.project_root / dir_path
            if full_path.exists():
                files = list(full_path.rglob('*'))
                file_count = len([f for f in files if f.is_file()])
                
                total_size = 0
                for file in files:
                    if file.is_file():
                        try:
                            total_size += file.stat().st_size
                        except:
                            pass
                
                size_mb = total_size / (1024 * 1024)
                analysis['scattered_dirs'][dir_name] = {
                    'exists': True,
                    'file_count': file_count,
                    'size_mb': size_mb
                }
                analysis['total_files'] += file_count
                analysis['total_size_mb'] += size_mb
                
                print(f"   ✅ {dir_name}: {file_count} 文件, {size_mb:.2f} MB")
            else:
                analysis['scattered_dirs'][dir_name] = {
                    'exists': False,
                    'file_count': 0,
                    'size_mb': 0
                }
                print(f"   ❌ {dir_name}: 不存在")
        
        print(f"\n📋 总计: {analysis['total_files']} 文件, {analysis['total_size_mb']:.2f} MB")
        return analysis
    
    def create_integrated_structure(self):
        """创建整合后的目录结构"""
        print(f"\n🏗️ 创建整合后的TestCase结构")
        print("=" * 80)
        
        # 确保TestCase根目录存在
        self.testcase_root.mkdir(exist_ok=True)
        print(f"📁 确保TestCase根目录: {self.testcase_root}")
        
        # 创建各个测试环境目录
        for env_id, env_desc in self.new_structure.items():
            env_path = self.testcase_root / env_id
            env_path.mkdir(exist_ok=True)
            print(f"📁 创建测试环境: {env_id} - {env_desc}")
            
            if env_id == 'shared':
                # 共享目录结构
                subdirs = ['scripts', 'tools', 'templates', 'docs']
            else:
                # 标准测试环境结构
                subdirs = ['input', 'output', 'expected', 'backup', 'archive', 'results', 'configs']
            
            for subdir in subdirs:
                (env_path / subdir).mkdir(exist_ok=True)
                print(f"   📂 {subdir}/")
            
            # 创建环境配置文件
            config = {
                'environment_id': env_id,
                'description': env_desc,
                'created_time': datetime.now().isoformat(),
                'structure_version': '1.0.0',
                'subdirectories': subdirs
            }
            
            config_file = env_path / 'env_config.json'
            with open(config_file, 'w', encoding='utf-8') as f:
                json.dump(config, f, indent=2, ensure_ascii=False)
    
    def generate_migration_plan(self):
        """生成迁移计划"""
        print(f"\n📋 生成迁移计划")
        print("=" * 80)
        
        migration_plan = {
            'tests/': {
                'target': 'TestCase/shared/scripts/',
                'action': 'move',
                'description': '单元测试和性能测试脚本迁移到共享脚本目录'
            },
            'test_cases/stock_code_and_missing_data_fix_20250727_084724/': {
                'target': 'TestCase/04/stock_code_fix/',
                'action': 'move', 
                'description': '股票代码修复回归测试案例迁移到回归测试环境'
            },
            'test_results/': {
                'target': 'TestCase/*/results/',
                'action': 'distribute',
                'description': '测试结果按类型分发到各测试环境的results目录'
            },
            'test_csv_output/ + test_txt_output/': {
                'target': 'TestCase/*/output/',
                'action': 'merge_distribute',
                'description': '测试输出文件合并后按类型分发到各环境的output目录'
            },
            'test_output_debug/': {
                'target': 'DELETE',
                'action': 'delete',
                'description': '调试输出目录删除（应保持空状态）'
            }
        }
        
        for source, plan in migration_plan.items():
            print(f"📦 {source}")
            print(f"   → {plan['target']}")
            print(f"   📝 {plan['description']}")
            print(f"   🔧 操作: {plan['action']}")
            print()
        
        return migration_plan
    
    def execute_migration(self, dry_run=True):
        """执行迁移（默认为试运行）"""
        action = "试运行" if dry_run else "实际执行"
        print(f"\n🚀 {action}迁移")
        print("=" * 80)
        
        if dry_run:
            print("⚠️ 这是试运行，不会实际移动文件")
            print("   使用 --execute 参数进行实际迁移")
        
        # 1. 迁移tests/到shared/scripts/
        source = self.project_root / 'tests'
        target = self.testcase_root / 'shared' / 'scripts'
        if source.exists():
            print(f"📦 迁移: {source} → {target}")
            if not dry_run:
                if target.exists():
                    shutil.rmtree(target)
                shutil.move(str(source), str(target))
        
        # 2. 迁移test_cases/到04/
        source = self.project_root / 'test_cases'
        target = self.testcase_root / '04'
        if source.exists():
            print(f"📦 迁移: {source} → {target}")
            if not dry_run:
                for item in source.iterdir():
                    target_item = target / item.name
                    if item.is_dir():
                        if target_item.exists():
                            shutil.rmtree(target_item)
                        shutil.move(str(item), str(target_item))
        
        # 3. 清理输出目录
        output_dirs = ['test_csv_output', 'test_txt_output', 'test_output_debug']
        for dir_name in output_dirs:
            dir_path = self.project_root / dir_name
            if dir_path.exists():
                print(f"🗑️ 清理: {dir_path}")
                if not dry_run:
                    shutil.rmtree(dir_path)
        
        # 4. 迁移test_results/
        source = self.project_root / 'test_results'
        target = self.testcase_root / 'shared' / 'results'
        if source.exists():
            print(f"📦 迁移: {source} → {target}")
            if not dry_run:
                target.mkdir(exist_ok=True)
                for item in source.iterdir():
                    target_item = target / item.name
                    shutil.move(str(item), str(target_item))
                source.rmdir()
        
        print(f"\n✅ {action}完成")
    
    def create_integration_report(self):
        """创建整合报告"""
        report = {
            'integration_time': datetime.now().isoformat(),
            'before_analysis': self.analyze_current_structure(),
            'migration_plan': self.generate_migration_plan(),
            'new_structure': self.new_structure
        }
        
        report_file = f"test_integration_report_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        with open(report_file, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        
        print(f"📄 整合报告已保存: {report_file}")
        return report


def main():
    """主函数"""
    import argparse
    
    parser = argparse.ArgumentParser(description='测试环境整合工具')
    parser.add_argument('--analyze', action='store_true', help='分析当前结构')
    parser.add_argument('--create-structure', action='store_true', help='创建新结构')
    parser.add_argument('--plan', action='store_true', help='生成迁移计划')
    parser.add_argument('--migrate', action='store_true', help='执行迁移（试运行）')
    parser.add_argument('--execute', action='store_true', help='实际执行迁移')
    parser.add_argument('--report', action='store_true', help='生成整合报告')
    
    args = parser.parse_args()
    
    integrator = TestEnvironmentIntegrator()
    
    print("🔧 测试环境整合工具")
    print("=" * 80)
    print(f"执行时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    print()
    
    if args.analyze:
        integrator.analyze_current_structure()
    elif args.create_structure:
        integrator.create_integrated_structure()
    elif args.plan:
        integrator.generate_migration_plan()
    elif args.migrate:
        integrator.execute_migration(dry_run=not args.execute)
    elif args.report:
        integrator.create_integration_report()
    else:
        print("请指定操作选项，使用 --help 查看帮助")
        parser.print_help()


if __name__ == '__main__':
    main()
