{"timestamp": "2025-07-23T23:47:55.967294", "error_statistics": {"error_counts": {"SYSTEM": 4}, "total_errors": 4, "recent_errors": [{"error_id": "SYSTEM_1753285603625", "timestamp": "2025-07-23T23:46:43.625300", "category": "SYSTEM", "error_type": "AttributeError", "error_message": "'ConfigManager' object has no attribute 'get_task_configs'", "stock_code": null, "operation": "任务配置加载", "context": {}, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\task_manager.py\", line 70, in _load_task_configurations\n    task_configs = config_manager.get_task_configs()\nAttributeError: 'ConfigManager' object has no attribute 'get_task_configs'\n"}, {"error_id": "SYSTEM_1753285603626", "timestamp": "2025-07-23T23:46:43.626300", "category": "SYSTEM", "error_type": "AttributeError", "error_message": "CRITICAL", "stock_code": null, "operation": "组件初始化", "context": {}, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\application.py\", line 58, in _initialize_components\n    self.task_manager = TaskManager(self.stock_processor)\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\task_manager.py\", line 61, in __init__\n    self._load_task_configurations()\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\task_manager.py\", line 89, in _load_task_configurations\n    error_id = self.error_handler.log_error(\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\enhanced_error_handler.py\", line 127, in log_error\n    if category in [ErrorCategory.CRITICAL, ErrorCategory.SYSTEM]:\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\enum.py\", line 429, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: CRITICAL\n"}, {"error_id": "SYSTEM_1753285603640", "timestamp": "2025-07-23T23:46:43.640300", "category": "SYSTEM", "error_type": "AttributeError", "error_message": "CRITICAL", "stock_code": null, "operation": "应用程序初始化", "context": {}, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\main.py\", line 45, in initialize_application\n    app = MythQuantApplication(config_manager)\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\application.py\", line 37, in __init__\n    self._initialize_components()\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\core\\application.py\", line 63, in _initialize_components\n    error_id = self.error_handler.log_error(\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\enhanced_error_handler.py\", line 127, in log_error\n    if category in [ErrorCategory.CRITICAL, ErrorCategory.SYSTEM]:\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\enum.py\", line 429, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: CRITICAL\n"}, {"error_id": "SYSTEM_1753285603641", "timestamp": "2025-07-23T23:46:43.641300", "category": "SYSTEM", "error_type": "AttributeError", "error_message": "CRITICAL", "stock_code": null, "operation": "主程序执行", "context": {}, "traceback": "Traceback (most recent call last):\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\main.py\", line 71, in main\n    app = initialize_application()\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\main.py\", line 52, in initialize_application\n    error_id = error_handler.log_error(\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\enhanced_error_handler.py\", line 127, in log_error\n    if category in [ErrorCategory.CRITICAL, ErrorCategory.SYSTEM]:\n  File \"C:\\Users\\<USER>\\AppData\\Local\\Programs\\Python\\Python39\\lib\\enum.py\", line 429, in __getattr__\n    raise AttributeError(name) from None\nAttributeError: CRITICAL\n"}], "error_history_size": 4}, "performance_statistics": {}}