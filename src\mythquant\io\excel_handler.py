#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Excel处理器模块

提供Excel文件的读写和处理功能
"""

import pandas as pd
from pathlib import Path
from typing import Optional, List, Dict, Any, Union
import logging

# 导入新架构的模块
from src.mythquant.config.manager import ConfigManager
from .file_manager import FileManager

logger = logging.getLogger(__name__)


class ExcelHandler:
    """Excel处理器 - Excel文件读写和处理"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化Excel处理器
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.file_manager = FileManager(config_manager)
        
        # 检查openpyxl是否可用
        self.excel_available = self._check_excel_support()
    
    def _check_excel_support(self) -> bool:
        """检查Excel支持"""
        try:
            import openpyxl
            return True
        except ImportError:
            logger.warning("openpyxl未安装，Excel功能不可用")
            return False
    
    def read_excel_file(self, file_path: Union[str, Path], 
                       sheet_name: Union[str, int] = 0) -> Optional[pd.DataFrame]:
        """
        读取Excel文件
        
        Args:
            file_path: Excel文件路径
            sheet_name: 工作表名称或索引
            
        Returns:
            DataFrame或None
        """
        if not self.excel_available:
            logger.error("Excel支持不可用，无法读取Excel文件")
            return None
        
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"Excel文件不存在: {file_path}")
                return None
            
            df = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
            logger.info(f"成功读取Excel文件: {file_path}, 工作表: {sheet_name}")
            
            return df
            
        except Exception as e:
            logger.error(f"读取Excel文件失败 {file_path}: {e}")
            return None
    
    def write_excel_file(self, df: pd.DataFrame, file_path: Union[str, Path], 
                        sheet_name: str = "Sheet1", index: bool = False) -> bool:
        """
        写入Excel文件
        
        Args:
            df: 数据DataFrame
            file_path: 输出文件路径
            sheet_name: 工作表名称
            index: 是否包含索引
            
        Returns:
            是否写入成功
        """
        if not self.excel_available:
            logger.error("Excel支持不可用，无法写入Excel文件")
            return False
        
        if df is None or df.empty:
            logger.warning("数据为空，无法写入Excel文件")
            return False
        
        try:
            file_path = Path(file_path)
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 备份现有文件
            if file_path.exists():
                self.file_manager.backup_file(file_path)
            
            # 写入Excel文件
            df.to_excel(file_path, sheet_name=sheet_name, index=index, engine='openpyxl')
            
            logger.info(f"成功写入Excel文件: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"写入Excel文件失败 {file_path}: {e}")
            return False
    
    def write_multiple_sheets(self, data_dict: Dict[str, pd.DataFrame], 
                             file_path: Union[str, Path]) -> bool:
        """
        写入多个工作表到Excel文件
        
        Args:
            data_dict: 工作表名称到DataFrame的映射
            file_path: 输出文件路径
            
        Returns:
            是否写入成功
        """
        if not self.excel_available:
            logger.error("Excel支持不可用，无法写入多工作表Excel文件")
            return False
        
        if not data_dict:
            logger.warning("数据字典为空，无法写入Excel文件")
            return False
        
        try:
            file_path = Path(file_path)
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 备份现有文件
            if file_path.exists():
                self.file_manager.backup_file(file_path)
            
            # 写入多个工作表
            with pd.ExcelWriter(file_path, engine='openpyxl') as writer:
                for sheet_name, df in data_dict.items():
                    if df is not None and not df.empty:
                        df.to_excel(writer, sheet_name=sheet_name, index=False)
                        logger.debug(f"写入工作表: {sheet_name}")
            
            logger.info(f"成功写入多工作表Excel文件: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"写入多工作表Excel文件失败 {file_path}: {e}")
            return False
    
    def read_all_sheets(self, file_path: Union[str, Path]) -> Optional[Dict[str, pd.DataFrame]]:
        """
        读取Excel文件的所有工作表
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            工作表名称到DataFrame的映射或None
        """
        if not self.excel_available:
            logger.error("Excel支持不可用，无法读取Excel文件")
            return None
        
        try:
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"Excel文件不存在: {file_path}")
                return None
            
            # 读取所有工作表
            sheets_dict = pd.read_excel(file_path, sheet_name=None, engine='openpyxl')
            
            logger.info(f"成功读取Excel文件的所有工作表: {file_path}")
            logger.debug(f"工作表列表: {list(sheets_dict.keys())}")
            
            return sheets_dict
            
        except Exception as e:
            logger.error(f"读取Excel文件所有工作表失败 {file_path}: {e}")
            return None
    
    def get_sheet_names(self, file_path: Union[str, Path]) -> Optional[List[str]]:
        """
        获取Excel文件的工作表名称列表
        
        Args:
            file_path: Excel文件路径
            
        Returns:
            工作表名称列表或None
        """
        if not self.excel_available:
            logger.error("Excel支持不可用")
            return None
        
        try:
            import openpyxl
            
            file_path = Path(file_path)
            
            if not file_path.exists():
                logger.error(f"Excel文件不存在: {file_path}")
                return None
            
            # 使用openpyxl获取工作表名称
            workbook = openpyxl.load_workbook(file_path, read_only=True)
            sheet_names = workbook.sheetnames
            workbook.close()
            
            logger.debug(f"Excel文件工作表: {sheet_names}")
            return sheet_names
            
        except Exception as e:
            logger.error(f"获取Excel工作表名称失败 {file_path}: {e}")
            return None
    
    def create_formatted_excel(self, df: pd.DataFrame, file_path: Union[str, Path], 
                              title: str = None, auto_width: bool = True) -> bool:
        """
        创建格式化的Excel文件
        
        Args:
            df: 数据DataFrame
            file_path: 输出文件路径
            title: 标题
            auto_width: 是否自动调整列宽
            
        Returns:
            是否创建成功
        """
        if not self.excel_available:
            logger.error("Excel支持不可用，无法创建格式化Excel文件")
            return False
        
        if df is None or df.empty:
            logger.warning("数据为空，无法创建Excel文件")
            return False
        
        try:
            import openpyxl
            from openpyxl.styles import Font, Alignment, PatternFill
            
            file_path = Path(file_path)
            
            # 确保目录存在
            file_path.parent.mkdir(parents=True, exist_ok=True)
            
            # 备份现有文件
            if file_path.exists():
                self.file_manager.backup_file(file_path)
            
            # 创建工作簿
            workbook = openpyxl.Workbook()
            worksheet = workbook.active
            worksheet.title = "数据"
            
            # 添加标题
            if title:
                worksheet.merge_cells('A1:' + openpyxl.utils.get_column_letter(len(df.columns)) + '1')
                title_cell = worksheet['A1']
                title_cell.value = title
                title_cell.font = Font(size=14, bold=True)
                title_cell.alignment = Alignment(horizontal='center')
                start_row = 3
            else:
                start_row = 1
            
            # 写入表头
            for col_idx, column_name in enumerate(df.columns, 1):
                cell = worksheet.cell(row=start_row, column=col_idx)
                cell.value = column_name
                cell.font = Font(bold=True)
                cell.fill = PatternFill(start_color="CCCCCC", end_color="CCCCCC", fill_type="solid")
                cell.alignment = Alignment(horizontal='center')
            
            # 写入数据
            for row_idx, (_, row) in enumerate(df.iterrows(), start_row + 1):
                for col_idx, value in enumerate(row, 1):
                    cell = worksheet.cell(row=row_idx, column=col_idx)
                    cell.value = value
            
            # 自动调整列宽
            if auto_width:
                for column in worksheet.columns:
                    max_length = 0
                    column_letter = column[0].column_letter
                    
                    for cell in column:
                        try:
                            if len(str(cell.value)) > max_length:
                                max_length = len(str(cell.value))
                        except:
                            pass
                    
                    adjusted_width = min(max_length + 2, 50)
                    worksheet.column_dimensions[column_letter].width = adjusted_width
            
            # 保存文件
            workbook.save(file_path)
            workbook.close()
            
            logger.info(f"成功创建格式化Excel文件: {file_path}")
            return True
            
        except Exception as e:
            logger.error(f"创建格式化Excel文件失败 {file_path}: {e}")
            return False


# 导出
__all__ = ['ExcelHandler']
