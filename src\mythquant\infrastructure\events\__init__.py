#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
事件驱动基础设施

实现事件总线、事件存储、事件处理器等事件驱动架构组件
"""

from .event_bus import EventBus, InMemoryEventBus, AsyncEventBus
from .event_store import EventStore, InMemoryEventStore, FileEventStore
from .event_handlers import EventHandler, DomainEventHandler
from .event_dispatcher import EventDispatcher, AsyncEventDispatcher
from .event_sourcing import EventSourcedAggregate, EventStream
from .message_queue import MessageQueue, InMemoryMessageQueue, RedisMessageQueue

__all__ = [
    # 事件总线
    'EventBus',
    'InMemoryEventBus', 
    'AsyncEventBus',
    
    # 事件存储
    'EventStore',
    'InMemoryEventStore',
    'FileEventStore',
    
    # 事件处理器
    'EventHandler',
    'DomainEventHandler',
    
    # 事件分发器
    'EventDispatcher',
    'AsyncEventDispatcher',
    
    # 事件溯源
    'EventSourcedAggregate',
    'EventStream',
    
    # 消息队列
    'MessageQueue',
    'InMemoryMessageQueue',
    'RedisMessageQueue'
]
