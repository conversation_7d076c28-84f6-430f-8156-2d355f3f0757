
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>MythQuant 测试报告</title>
    <style>
        body { font-family: Arial, sans-serif; margin: 20px; }
        .header { background: #f5f5f5; padding: 20px; border-radius: 5px; }
        .summary { margin: 20px 0; }
        .suite { margin: 10px 0; padding: 10px; border: 1px solid #ddd; border-radius: 5px; }
        .success { background: #d4edda; border-color: #c3e6cb; }
        .failure { background: #f8d7da; border-color: #f5c6cb; }
        .details { margin-top: 10px; font-family: monospace; font-size: 12px; }
    </style>
</head>
<body>
    <div class="header">
        <h1>🧪 MythQuant 测试报告</h1>
        <p><strong>测试时间:</strong> 2025-08-01T22:08:41.338564 - 2025-08-01T22:08:41.728993</p>
        <p><strong>测试级别:</strong> standard</p>
        <p><strong>总体结果:</strong> ❌ 失败</p>
    </div>
    
    <div class="summary">
        <h2>📊 测试汇总</h2>
        <ul>
            <li>总测试套件数: 1</li>
            <li>通过: 0</li>
            <li>失败: 1</li>
            <li>通过率: 0.0%</li>
            <li>总耗时: 0.4秒</li>
        </ul>
    </div>
    
    <div class="suites">
        <h2>🧪 测试套件详情</h2>

        <div class="suite failure">
            <h3>❌ test_price_consistency</h3>
            <p><strong>结果:</strong> 失败</p>
            <p><strong>耗时:</strong> 0.4秒</p>
            <p><strong>返回码:</strong> 1</p>

            <div class="details">
                <strong>错误信息:</strong><br>
                <pre>\u274c 测试环境初始化失败: 'gbk' codec can't encode character '\u2705' in position 0: illegal multibyte sequence
Traceback (most recent call last):
  File "C:\Users\<USER>\PycharmProjects\MythQuant\test_environments\test_suites\test_price_consistency.py", line 343, in <module>
    exit_code = main()
  File "C:\Users\<USER>\PycharmProjects\MythQuant\test_environments\test_suites\test_price_consistency.py", line 337, in main
    test_suite = PriceConsistencyTestSuite()
  File "C:\Users\<USER>\PycharmProjects\MythQuant\test_environments\test_suites\test_price_consistency.py", line 36, in __init__
    self.setup_test_environment()
  File "C:\Users\<USER>\PycharmProjects\MythQuant\test_environments\test_suites\test_price_consistency.py", line 46, in setup_test_environment
    print("\U0001f9ea 价格一致性检查测试套件")
UnicodeEncodeError: 'gbk' codec can't encode character '\U0001f9ea' in position 0: illegal multibyte sequence
</pre>
            </div>
        </div>

    </div>
</body>
</html>
