2025-07-26 15:39:20,244 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_153920.log
2025-07-26 15:39:20,244 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 15:39:20,263 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 15:39:20,265 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 15:39:20,265 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 15:39:20,276 - Main - INFO - info:307 - ℹ️ 开始并行测试 54 个服务器...
2025-07-26 15:39:23,290 - Main - WARNING - warning:311 - ⚠️ ❌ 杭州行情主站 (218.108.50.178:7711) - 连接失败
2025-07-26 15:39:23,291 - Main - WARNING - warning:311 - ⚠️ ❌ 移动行情主站 (117.184.140.156:7711) - 连接失败
2025-07-26 15:39:23,291 - Main - WARNING - warning:311 - ⚠️ ❌ 广州行情主站 (119.147.171.206:443) - 连接失败
2025-07-26 15:39:23,291 - Main - WARNING - warning:311 - ⚠️ ❌ 深圳行情主站 (113.105.73.88:7709) - 连接失败
2025-07-26 15:39:23,291 - Main - WARNING - warning:311 - ⚠️ ❌ 北京行情主站1 (106.120.74.86:7711) - 连接失败
2025-07-26 15:39:23,291 - Main - WARNING - warning:311 - ⚠️ ❌ 深圳行情主站 (113.105.73.88:7711) - 连接失败
2025-07-26 15:39:23,291 - Main - WARNING - warning:311 - ⚠️ ❌ 广州行情主站 (119.147.171.206:80) - 连接失败
2025-07-26 15:39:23,291 - Main - WARNING - warning:311 - ⚠️ ❌ 上海行情主站 (114.80.80.222:7711) - 连接失败
2025-07-26 15:39:26,295 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.22 (114.80.149.22:7709) - 连接失败
2025-07-26 15:39:26,295 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.67.61.70 (114.67.61.70:7709) - 连接失败
2025-07-26 15:39:26,296 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.74 (112.95.140.74:7709) - 连接失败
2025-07-26 15:39:26,296 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.93 (112.95.140.93:7709) - 连接失败
2025-07-26 15:39:26,296 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.92 (112.95.140.92:7709) - 连接失败
2025-07-26 15:39:26,296 - Main - WARNING - warning:311 - ⚠️ ❌ 北京行情主站2 (221.194.181.176:7711) - 连接失败
2025-07-26 15:39:26,296 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_106.120.74.86 (106.120.74.86:7709) - 连接失败
2025-07-26 15:39:26,296 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.19 (114.80.149.19:7709) - 连接失败
2025-07-26 15:39:26,645 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.90.165 (115.238.90.165:7709) - 0.229s
2025-07-26 15:39:26,675 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.56.198 (115.238.56.198:7709) - 0.252s
2025-07-26 15:39:29,302 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.29.51.30 (119.29.51.30:7709) - 连接失败
2025-07-26 15:39:29,302 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.84 (114.80.149.84:7709) - 连接失败
2025-07-26 15:39:29,302 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.80.222 (114.80.80.222:7709) - 连接失败
2025-07-26 15:39:29,303 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_117.184.140.156 (117.184.140.156:7709) - 连接失败
2025-07-26 15:39:29,303 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.147.164.60 (119.147.164.60:7709) - 连接失败
2025-07-26 15:39:29,303 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.147.171.206 (119.147.171.206:7709) - 连接失败
2025-07-26 15:39:29,660 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.104.70 (121.14.104.70:7709) - 连接失败
2025-07-26 15:39:29,675 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.104.72 (121.14.104.72:7709) - 连接失败
2025-07-26 15:39:29,979 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_180.153.18.170 (180.153.18.170:7709) - 0.209s
2025-07-26 15:39:31,425 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_123.125.108.23 (123.125.108.23:7709) - 连接失败
2025-07-26 15:39:31,425 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_123.125.108.24 (123.125.108.24:7709) - 连接失败
2025-07-26 15:39:32,311 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.110.194 (121.14.110.194:7709) - 连接失败
2025-07-26 15:39:32,311 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_124.160.88.183 (124.160.88.183:7709) - 连接失败
2025-07-26 15:39:32,311 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.2.7 (121.14.2.7:7709) - 连接失败
2025-07-26 15:39:32,311 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.18.17 (180.153.18.17:7709) - 连接失败
2025-07-26 15:39:32,682 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.18.171 (180.153.18.171:7709) - 连接失败
2025-07-26 15:39:32,735 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_218.75.126.9 (218.75.126.9:7709) - 0.281s
2025-07-26 15:39:32,989 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.39.51 (180.153.39.51:7709) - 连接失败
2025-07-26 15:39:33,009 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************* (*************:7709) - 0.181s
2025-07-26 15:39:33,358 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.191.117.167 (60.191.117.167:7709) - 0.244s
2025-07-26 15:39:34,427 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.50.178 (218.108.50.178:7709) - 连接失败
2025-07-26 15:39:34,427 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.47.69 (218.108.47.69:7709) - 连接失败
2025-07-26 15:39:35,317 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_221.194.181.176 (221.194.181.176:7709) - 连接失败
2025-07-26 15:39:35,317 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.9.148.108 (218.9.148.108:7709) - 连接失败
2025-07-26 15:39:35,317 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.98.244 (218.108.98.244:7709) - 连接失败
2025-07-26 15:39:35,691 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_59.173.18.69 (59.173.18.69:7709) - 连接失败
2025-07-26 15:39:36,018 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_60.28.29.69 (60.28.29.69:7709) - 连接失败
2025-07-26 15:39:36,372 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.135.142.73 (61.135.142.73:7709) - 连接失败
2025-07-26 15:39:37,438 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.135.142.88 (61.135.142.88:7709) - 连接失败
2025-07-26 15:39:37,438 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.152.107.168 (61.152.107.168:7721) - 连接失败
2025-07-26 15:39:38,329 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.152.249.56 (61.152.249.56:7709) - 连接失败
2025-07-26 15:39:38,329 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.153.209.138 (61.153.209.138:7709) - 连接失败
2025-07-26 15:39:38,329 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.153.144.179 (61.153.144.179:7709) - 连接失败
2025-07-26 15:39:38,431 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_*************** (***************:7721) - 连接失败
2025-07-26 15:39:38,701 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_************** (**************:7709) - 连接失败
2025-07-26 15:39:39,386 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_************** (**************:7721) - 连接失败
2025-07-26 15:39:39,428 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: *************
2025-07-26 15:39:39,429 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-26 15:39:39,429 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: *************:7709
2025-07-26 15:39:39,429 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 15:39:39,429 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 15:39:39,430 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 15:39:39,430 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 15:39:39,888 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 15:39:39,888 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 15:39:39,895 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 15:39:39,895 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 15:39:39,896 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 15:39:39,896 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 15:39:39,896 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 15:39:39,896 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 15:39:39,897 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 15:39:39,900 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 15:39:39,900 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 15:39:39,901 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 15:39:39,901 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 15:39:39,907 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 15:39:40,274 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.37秒
2025-07-26 15:39:40,275 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.37秒
2025-07-26 15:39:40,275 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 15:39:40,275 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 15:39:40,275 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 15:39:40,275 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 15:39:40,275 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 15:39:40,276 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 15:39:40,276 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 15:39:40,276 - main_v20230219_optimized - INFO - generate_daily_data_task:3275 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 15:39:40,276 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:3814 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 15:39:40,276 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 15:39:40,277 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 15:39:40,387 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 15:39:40,387 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 15:39:40,394 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 15:39:40,394 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 15:39:40,394 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 15:39:40,394 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 15:39:40,394 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 15:39:40,395 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 15:39:40,395 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 15:39:40,398 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 15:39:40,398 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 15:39:40,398 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 15:39:40,399 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 15:39:40,406 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 15:39:41,028 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.62秒
2025-07-26 15:39:41,028 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.62秒
2025-07-26 15:39:51,339 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 15:40:14,794 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 15:40:14,794 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 15:40:14,794 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 15:40:14,804 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 15:40:15,213 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 15:40:15,213 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 15:40:15,213 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 15:40:15,213 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 15:40:15,215 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 15:40:15,215 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 15:40:15,216 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 15:40:15,216 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 15:40:15,216 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 15:40:15,272 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 15:40:15,694 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 15:40:15,711 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 15:40:15,728 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 15:40:15,745 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 15:40:15,762 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 15:40:15,779 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 15:40:15,796 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 15:40:15,814 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 15:40:15,831 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 15:40:15,842 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 15:40:15,859 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 15:40:15,875 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 15:40:15,891 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 15:40:15,907 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 15:40:15,924 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 15:40:15,936 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 15:40:15,956 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 15:40:15,973 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 15:40:15,984 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 15:40:15,996 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 15:40:16,013 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 15:40:16,014 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 15:40:20,420 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 15:40:20,420 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 15:40:20,421 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 15:40:20,421 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 15:40:20,421 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 15:40:20,424 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 15:40:20,525 - main_v20230219_optimized - INFO - apply_forward_adjustment:1911 - 保留交易时间数据后: 320880条
2025-07-26 15:40:20,525 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 15:40:20,526 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 15:40:20,526 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 15:40:20,526 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 15:40:20,526 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 15:40:20,526 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 15:40:20,537 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 15:40:20,679 - main_v20230219_optimized - INFO - load_and_process_minute_data:2380 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 15:40:21,129 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 15:40:21,352 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 15:40:21,352 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 15:40:21,462 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 15:40:21,462 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 15:40:21,463 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3993 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 15:40:21,463 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3994 - 📊 成功生成: 1 个文件
2025-07-26 15:40:21,463 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3995 - ❌ 处理失败: 0 个股票
2025-07-26 15:40:21,463 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3996 - 📈 成功率: 100.0%
2025-07-26 15:40:21,463 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3997 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 15:40:21,477 - main_v20230219_optimized - INFO - generate_daily_data_task:3285 - ✅ 日线数据生成完成，耗时: 41.20 秒
2025-07-26 15:40:21,478 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 15:40:21,478 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网日线数据下载
2025-07-26 15:40:21,478 - Main - INFO - info:307 - ℹ️ 开始执行互联网日线数据下载任务
2025-07-26 15:40:21,479 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 15:40:21,479 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 15:40:21,798 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 15:40:21,808 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 15:40:21,808 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 15:40:21,808 - Main - INFO - info:307 - ℹ️ 互联网数据下载时间范围: 20170101 - 20250720
2025-07-26 15:40:21,808 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的互联网数据
2025-07-26 15:40:21,809 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 15:40:21,809 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 15:40:21,809 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 15:40:21,809 - Main - INFO - info:307 - ℹ️ 📊 频率转换: d -> daily
2025-07-26 15:40:21,809 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 daily 数据: 20170101 - 20250720
2025-07-26 15:40:21,809 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:21,981 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:21,981 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计749520条 -> 实际请求800条 (配置限制:800)
2025-07-26 15:40:21,981 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 daily 数据
2025-07-26 15:40:21,981 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=9, market=0, code=000617, count=800
2025-07-26 15:40:22,033 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需748720条
2025-07-26 15:40:22,033 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:22,198 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:22,248 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 15:40:22,248 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:22,423 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:22,476 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 15:40:22,476 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:22,655 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:22,706 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 15:40:22,706 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:22,879 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:22,931 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 15:40:22,932 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:23,103 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:23,155 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 15:40:23,155 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:23,334 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:23,387 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 15:40:23,387 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:23,565 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:23,618 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 15:40:23,618 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:23,788 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:23,836 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +355条，总计6755条
2025-07-26 15:40:23,836 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计6755条数据
2025-07-26 15:40:23,852 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 2073 条 daily 数据
2025-07-26 15:40:23,855 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共2073条记录
2025-07-26 15:40:23,855 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 15:40:23,862 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=19.030, 前复权=19.030
2025-07-26 15:40:23,862 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 15:40:23,862 - Main - INFO - info:307 - ℹ️   日期: 20170103
2025-07-26 15:40:23,862 - Main - INFO - info:307 - ℹ️   当日收盘价C: 19.03
2025-07-26 15:40:23,863 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 19.03
2025-07-26 15:40:23,863 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 15:40:23,863 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 2073 >= 5
2025-07-26 15:40:23,863 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 15:40:23,863 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 15:40:23,864 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 15:40:23,867 - Main - INFO - info:307 - ℹ️ 数据无变化，将添加时间戳: day_0_000617_20170101-20250720_来源互联网（202507261540）.txt
2025-07-26 15:40:23,875 - Main - INFO - info:307 - ℹ️ 成功保存000617数据到: H:/MPV1.17/T0002/signals\day_0_000617_20170101-20250720_来源互联网（202507261540）.txt
2025-07-26 15:40:23,875 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 15:40:23,875 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 15:40:23,875 - Main - INFO - info:307 - ℹ️ 互联网数据下载完成: 1/1 成功 (100.0%)
2025-07-26 15:40:23,875 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网日线数据下载
2025-07-26 15:40:23,875 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 15:40:23,875 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 15:40:23,875 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️   时间范围: 20250401 - 20250726
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 15:40:23,876 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 15:40:23,877 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-26 15:40:23,877 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250101-20250726_来源互联网.txt
2025-07-26 15:40:23,878 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250101-20250726_来源互联网.txt
2025-07-26 15:40:23,880 - Main - ERROR - error:315 - ❌ 分钟级增量下载失败: 'LoggingService' object has no attribute 'error'
2025-07-26 15:40:23,880 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-26 15:40:23,880 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-26 15:40:23,880 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 15:40:23,880 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250401 - 20250726
2025-07-26 15:40:23,880 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:24,060 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:24,061 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计28080条 -> 实际请求800条 (配置限制:800)
2025-07-26 15:40:24,061 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 15:40:24,061 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 15:40:24,118 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需27280条
2025-07-26 15:40:24,118 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:24,288 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:24,338 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 15:40:24,339 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:24,516 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:24,567 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 15:40:24,568 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:24,734 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:24,784 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 15:40:24,784 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:24,951 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:25,009 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 15:40:25,009 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:25,201 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:25,254 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 15:40:25,255 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:25,431 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:25,482 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 15:40:25,482 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:25,662 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:25,716 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 15:40:25,716 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:25,903 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:25,957 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 15:40:25,958 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:26,133 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:26,185 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 15:40:26,185 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:26,364 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:26,420 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 15:40:26,420 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:26,593 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:26,647 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 15:40:26,647 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:26,826 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:26,878 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 15:40:26,878 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:27,050 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:27,101 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 15:40:27,102 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:27,273 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:27,324 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 15:40:27,324 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:27,501 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:27,553 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 15:40:27,553 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:27,728 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:27,780 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 15:40:27,780 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:27,949 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:28,002 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 15:40:28,002 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:28,172 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:28,223 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 15:40:28,223 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:28,395 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:28,445 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 15:40:28,445 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:28,625 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:28,677 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 15:40:28,677 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:28,848 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:28,899 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 15:40:28,899 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:29,078 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:29,130 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 15:40:29,130 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:29,299 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:29,349 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 15:40:29,349 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:29,526 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:29,579 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 15:40:29,579 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:29,757 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:29,811 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 15:40:29,811 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:29,991 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:30,044 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 15:40:30,044 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:30,215 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:30,268 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 15:40:30,268 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:30,448 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:30,507 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 15:40:30,507 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:30,685 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:30,739 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 15:40:30,739 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: *************:7709
2025-07-26 15:40:30,912 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: *************:7709
2025-07-26 15:40:30,956 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 15:40:31,010 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 18960 条 1min 数据
2025-07-26 15:40:31,017 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共18960条记录
2025-07-26 15:40:31,018 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-26 15:40:31,118 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.390, 前复权=6.390
2025-07-26 15:40:31,125 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 15:40:31,125 - Main - INFO - info:307 - ℹ️   日期: 202504010931
2025-07-26 15:40:31,125 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.39
2025-07-26 15:40:31,125 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.39
2025-07-26 15:40:31,125 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 15:40:31,125 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 18960 >= 5
2025-07-26 15:40:31,125 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 15:40:31,126 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 15:40:31,126 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 15:40:31,164 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250401-20250726_来源互联网.txt (887167 字节)
2025-07-26 15:40:31,165 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 15:40:31,165 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 15:40:31,165 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-26 15:40:31,165 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-26 15:40:31,165 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 15:40:31,166 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 15:40:31,166 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 15:40:31,166 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 15:40:31,167 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 15:40:31,167 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 15:40:31,167 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: day_0_000617_20240701-20240731_来源互联网.txt
2025-07-26 15:40:31,184 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20200101-20250724.txt, 数据行数: 1337
2025-07-26 15:40:31,186 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20240701-20240731_来源互联网.txt, 数据行数: 23
2025-07-26 15:40:31,187 - Main - INFO - info:307 - ℹ️ 数据对齐完成，共同日期数: 23
2025-07-26 15:40:31,189 - Main - INFO - info:307 - ℹ️ 价格差异计算完成，数据点数: 23
2025-07-26 15:40:31,211 - Main - INFO - info:307 - ℹ️ 股票 000617 数据比较完成
2025-07-26 15:40:31,211 - Main - INFO - info:307 - ℹ️ ✅ 000617 分析完成
2025-07-26 15:40:31,214 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_154031.txt
2025-07-26 15:40:31,214 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 1/1 成功 (100.0%)
2025-07-26 15:40:31,214 - Main - INFO - info:307 - ℹ️ 任务执行成功: 前复权数据比较分析
2025-07-26 15:40:31,214 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 100.0%
2025-07-26 15:40:31,215 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 15:40:31,215 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 15:40:31,215 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 15:40:31,215 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 15:40:31,215 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 15:40:31,215 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 15:40:31,215 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
