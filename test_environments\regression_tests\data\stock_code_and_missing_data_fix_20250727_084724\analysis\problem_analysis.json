{"stock_code_issues": [{"file": "1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt", "issue_type": "incorrect_stock_code", "problematic_code": "617", "expected_code": "000617", "affected_records": 20381, "description": "股票代码617应该是000617"}, {"file": "1min_0_000617_202503030937-202507251500_来源互联网（202507270050）.txt", "issue_type": "incorrect_stock_code", "problematic_code": "617", "expected_code": "000617", "affected_records": 23981, "description": "股票代码617应该是000617"}, {"file": "day_0_000617_20200101-20250724.txt", "issue_type": "incorrect_stock_code", "problematic_code": "617", "expected_code": "000617", "affected_records": 1337, "description": "股票代码617应该是000617"}, {"file": "pytdx_xdxr_000617_20250726_112733.txt", "error": "Error tokenizing data. C error: Expected 1 fields in line 6, saw 16\n"}, {"file": "pytdx_xdxr_000617_20250726_182055.txt", "error": "Error tokenizing data. C error: Expected 1 fields in line 5, saw 16\n"}, {"file": "pytdx_xdxr_000617_20250726_182117.txt", "error": "Error tokenizing data. C error: Expected 1 fields in line 5, saw 16\n"}], "missing_data_issues": [{"file": "1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt", "issue_type": "missing_morning_data", "date": "20250303", "missing_times": ["0930", "0931", "0932", "0933", "0934", "0935", "0936"], "description": "20250303缺失开盘时段数据: ['0930', '0931', '0932', '0933', '0934', '0935', '0936']"}, {"file": "1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt", "issue_type": "missing_afternoon_data", "date": "20250704", "missing_times": ["1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1500"], "description": "20250704缺失收盘时段数据: ['1448', '1449', '1450', '1451', '1452', '1453', '1454', '1455', '1456', '1457', '1458', '1459', '1500']"}, {"file": "1min_0_000617_202503030937-202507251500_来源互联网（202507270050）.txt", "issue_type": "missing_morning_data", "date": "20250303", "missing_times": ["0930", "0931", "0932", "0933", "0934", "0935", "0936"], "description": "20250303缺失开盘时段数据: ['0930', '0931', '0932', '0933', '0934', '0935', '0936']"}, {"file": "1min_0_000617_202503030937-202507251500_来源互联网（202507270050）.txt", "issue_type": "missing_afternoon_data", "date": "20250704", "missing_times": ["1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1500"], "description": "20250704缺失收盘时段数据: ['1448', '1449', '1450', '1451', '1452', '1453', '1454', '1455', '1456', '1457', '1458', '1459', '1500']"}, {"file": "day_0_000617_20200101-20250724.txt", "issue_type": "missing_morning_data", "date": "20250303", "missing_times": ["0930", "0931", "0932", "0933", "0934", "0935", "0936", "0937"], "description": "20250303缺失开盘时段数据: ['0930', '0931', '0932', '0933', '0934', '0935', '0936', '0937']"}, {"file": "day_0_000617_20200101-20250724.txt", "issue_type": "missing_afternoon_data", "date": "20250704", "missing_times": ["1447", "1448", "1449", "1450", "1451", "1452", "1453", "1454", "1455", "1456", "1457", "1458", "1459", "1500"], "description": "20250704缺失收盘时段数据: ['1447', '1448', '1449', '1450', '1451', '1452', '1453', '1454', '1455', '1456', '1457', '1458', '1459', '1500']"}, {"file": "pytdx_xdxr_000617_20250726_112733.txt", "error": "Error tokenizing data. C error: Expected 1 fields in line 6, saw 16\n"}, {"file": "pytdx_xdxr_000617_20250726_182055.txt", "error": "Error tokenizing data. C error: Expected 1 fields in line 5, saw 16\n"}, {"file": "pytdx_xdxr_000617_20250726_182117.txt", "error": "Error tokenizing data. C error: Expected 1 fields in line 5, saw 16\n"}], "file_naming_issues": [{"file": "day_0_000617_20200101-20250724.txt", "issue_type": "incorrect_filename_prefix", "description": "文件名应该以1min_0_000617_开头"}, {"file": "pytdx_xdxr_000617_20250726_112733.txt", "issue_type": "incorrect_filename_prefix", "description": "文件名应该以1min_0_000617_开头"}, {"file": "pytdx_xdxr_000617_20250726_182055.txt", "issue_type": "incorrect_filename_prefix", "description": "文件名应该以1min_0_000617_开头"}, {"file": "pytdx_xdxr_000617_20250726_182117.txt", "issue_type": "incorrect_filename_prefix", "description": "文件名应该以1min_0_000617_开头"}], "total_files_analyzed": 6}