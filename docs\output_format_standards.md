# MythQuant 输出格式规范文档

## 📋 概述

本文档定义了MythQuant项目中所有终端输出的统一格式规范，确保用户界面的一致性和专业性。

## 🎯 设计原则

### 核心原则
1. **一致性优先**：所有模块使用统一的符号和格式
2. **层级清晰**：信息按重要性和逻辑关系分层显示
3. **信息精简**：每条输出都有明确价值，避免冗余
4. **用户友好**：易读、易理解、视觉舒适
5. **关注分离**：print专注用户界面，logger专注调试信息

### 设计哲学
- **结构化**：横幅 → 主流程 → 子流程 → 步骤 → 操作 → 结果
- **标准化**：统一符号体系和缩进规范
- **专业化**：提升系统专业形象和用户信任度

## 🎨 符号体系

### 主要流程符号
| 符号 | 用途 | 示例 |
|------|------|------|
| 🚀 | 主要流程开始 | `🚀 应用程序初始化` |
| 📊 | 子流程 | `📊 【1/2】 1分钟数据处理工作流程` |
| 🔍 | 步骤 | `🔍 [1/4] 智能文件选择和分析` |
| ⚡ | 具体操作 | `⚡ 加载智能文件选择器配置` |

### 状态符号
| 符号 | 用途 | 示例 |
|------|------|------|
| ✅ | 成功 | `✅ 文件分析成功` |
| ❌ | 错误 | `❌ 文件分析失败` |
| ⚠️ | 警告 | `⚠️ 数据完整性: 95.6%` |
| ℹ️ | 信息 | `ℹ️ 文件最后时间: 202507041447` |
| 🔄 | 进行中 | `🔄 正在下载数据...` |

### 数据符号
| 符号 | 用途 | 示例 |
|------|------|------|
| 📁 | 文件 | `📁 找到候选文件: test.txt` |
| 📋 | 数据 | `📋 总记录数: 17,212` |
| ⏰ | 时间 | `⏰ 启动时间: 2025-07-31 22:26:15` |
| 📈 | 统计 | `📈 执行统计:` |
| ⚙️ | 配置 | `⚙️ 加载配置文件` |

### 结果符号
| 符号 | 用途 | 示例 |
|------|------|------|
| 🎯 | 结果 | `🎯 比较结果: 一致` |
| 📊 | 汇总 | `📊 任务执行结果` |
| 🎉 | 完成 | `🎉 程序执行完成！` |

## 📏 缩进层级规范

### 层级定义
```
Level 0: 无缩进     - 主标题、横幅
Level 1: 2个空格    - 主流程内容
Level 2: 4个空格    - 子流程内容、步骤结果
Level 3: 6个空格    - 详细数据、三级信息
Level 4: 8个空格    - 最详细信息（谨慎使用）
```

### 缩进示例
```
🚀 主要流程                    # Level 0
  ℹ️ 主流程信息                # Level 1
📊 [1/2] 子流程                # Level 0
  🔍 [1/4] 步骤                # Level 1
    ⚡ 具体操作                 # Level 2
    ✅ 操作结果                 # Level 2
      📋 详细数据: 值           # Level 3
        补充信息                # Level 4
```

## 🔧 分隔符规范

### 分隔符类型
```python
SEPARATORS = {
    'major': '=' * 80,         # 主要分隔符（程序开始/结束）
    'minor': '-' * 60,         # 次要分隔符（主流程）
    'section': '-' * 40,       # 章节分隔符（子流程）
}
```

### 使用场景
- **主要分隔符**：程序横幅、完成信息
- **次要分隔符**：主要流程分隔
- **章节分隔符**：子流程分隔

## 📋 输出类型规范

### 1. 程序横幅
```python
print_banner(
    "MythQuant 量化交易数据处理系统",
    "前复权数据生成 & L2指标计算 | 高性能优化版本"
)
```

### 2. 主要流程
```python
print_main_process("应用程序初始化")
print_info("加载配置文件", level=1)
print_result("初始化完成", True, level=1)
```

### 3. 子流程
```python
print_sub_process("1分钟数据处理工作流程", 1, 2)
```

### 4. 步骤流程
```python
print_step("智能文件选择和分析", 1, 4)
print_action("加载智能文件选择器配置")
print_result("找到候选文件", True)
```

### 5. 数据信息
```python
print_data_info("时间范围", "20250320 ~ 20250704", level=3)
print_file_info("test.txt", "17,212 bytes", level=2)
```

### 6. 统计表格
```python
stats = {"执行任务数": 2, "成功率": "50.0%"}
print_stats_table("执行统计", stats)
```

### 7. 汇总信息
```python
items = [
    {"name": "数据下载", "status": "success"},
    {"name": "数据分析", "status": "failed"}
]
print_summary("任务执行结果", items)
```

### 8. 完成信息
```python
stats = {"总执行时间": "7.21 秒"}
print_completion("程序执行完成", True, stats)
```

## 🚫 禁止事项

### 不要使用的符号
- 🔧 🔨 🛠️ （工具符号，容易混淆）
- 📥 📤 📦 （包裹符号，不够专业）
- 🌟 ⭐ 💫 （装饰符号，过于花哨）
- 🎪 🎭 🎨 （娱乐符号，不够严肃）

### 不要的格式
- 混合使用不同的缩进（3空格、5空格等）
- 在同一层级使用不同符号
- 过度嵌套（超过Level 4）
- 信息冗余和重复

## 📚 使用指南

### 导入格式器
```python
from utils.structured_output_formatter import (
    print_banner, print_main_process, print_sub_process,
    print_step, print_action, print_result, print_info,
    print_warning, print_error, print_data_info,
    print_completion
)
```

### 典型使用流程
```python
# 1. 程序开始
print_banner("程序名称", "程序描述")

# 2. 主要流程
print_main_process("主要任务")
print_info("任务详情", level=1)

# 3. 子流程
print_sub_process("具体任务", 1, 3)

# 4. 步骤执行
print_step("步骤名称", 1, 4)
print_action("具体操作")
print_result("操作结果", True)

# 5. 程序结束
stats = {"关键指标": "值"}
print_completion("任务完成", True, stats)
```

## 🔄 迁移指南

### 现有代码迁移步骤
1. **识别输出类型**：确定每个print语句的用途
2. **选择合适函数**：根据用途选择对应的格式器函数
3. **调整层级**：设置合适的level参数
4. **测试验证**：确保输出格式正确

### 迁移优先级
1. **高优先级**：main.py、核心业务模块
2. **中优先级**：工具模块、处理器模块
3. **低优先级**：测试模块、辅助脚本

## 📊 质量检查

### 检查清单
- [ ] 符号使用是否统一
- [ ] 缩进层级是否正确
- [ ] 信息层级是否清晰
- [ ] 是否有冗余信息
- [ ] 用户体验是否友好

### 验证方法
1. **视觉检查**：输出是否整齐、专业
2. **逻辑检查**：信息流是否合理
3. **用户测试**：是否易于理解

## 📝 更新记录

| 版本 | 日期 | 更新内容 |
|------|------|----------|
| 1.0 | 2025-07-30 | 初始版本，建立基础规范 |

---

**注意**：本规范是项目级强制标准，所有新代码必须遵循，现有代码应逐步迁移。
