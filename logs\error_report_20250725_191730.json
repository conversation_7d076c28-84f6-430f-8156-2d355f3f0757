{"timestamp": "2025-07-25T19:17:30.550892", "error_statistics": {"error_counts": {"DATA_ACCESS": 1}, "total_errors": 1, "recent_errors": [{"error_id": "DATA_ACCESS_1753442250514", "timestamp": "2025-07-25T19:17:30.514891", "category": "DATA_ACCESS", "error_type": "KeyError", "error_message": "\"['date'] not in index\"", "stock_code": null, "operation": "AKShare数据下载", "context": {"stock_code": "000617", "data_source": "akshare"}, "traceback": "Traceback (most recent call last):\n  File \"c:\\Users\\<USER>\\PycharmProjects\\MythQuant\\utils\\stock_data_downloader.py\", line 341, in download_stock_data_akshare\n    df_qfq_close = df_qfq[['date', 'close']].rename(columns={'close': 'close_qfq'})\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\.venv\\lib\\site-packages\\pandas\\core\\frame.py\", line 4113, in __getitem__\n    indexer = self.columns._get_indexer_strict(key, \"columns\")[1]\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\.venv\\lib\\site-packages\\pandas\\core\\indexes\\base.py\", line 6212, in _get_indexer_strict\n    self._raise_if_missing(keyarr, indexer, axis_name)\n  File \"C:\\Users\\<USER>\\PycharmProjects\\MythQuant\\.venv\\lib\\site-packages\\pandas\\core\\indexes\\base.py\", line 6264, in _raise_if_missing\n    raise KeyError(f\"{not_found} not in index\")\nKeyError: \"['date'] not in index\"\n"}], "error_history_size": 1}, "performance_statistics": {}}