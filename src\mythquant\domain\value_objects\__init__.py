#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
值对象 (Value Objects)

不可变的领域概念，通过值相等性进行比较
"""

from .identifiers import StockCode, MarketCode, PortfolioId
from .measurements import Price, Volume, Percentage, Amount
from .trading import TradingSession, MarketStatus, OrderType

# 注意：ranges.py 文件不存在，暂时注释掉相关导入
# from .ranges import DateRange, TimeRange, PriceRange

__all__ = [
    # 标识符
    'StockCode',
    'MarketCode', 
    'PortfolioId',
    
    # 度量值
    'Price',
    'Volume',
    'Percentage',
    'Amount',
    
    # 范围值（暂时注释，ranges.py文件不存在）
    # 'DateRange',
    # 'TimeRange',
    # 'PriceRange',
    
    # 交易相关
    'TradingSession',
    'MarketStatus',
    'OrderType'
]
