[tool:pytest]
# 测试发现
testpaths = tests
python_files = test_*.py *_test.py
python_classes = Test*
python_functions = test_*

# 最小版本要求
minversion = 7.0

# 添加选项
addopts = 
    --strict-markers
    --strict-config
    --verbose
    --tb=short
    --cov=src
    --cov-branch
    --cov-report=term-missing
    --cov-report=html:htmlcov
    --cov-report=xml:coverage.xml
    --cov-fail-under=95
    --junitxml=junit.xml
    --maxfail=5
    --durations=10

# 标记定义
markers =
    unit: 单元测试
    integration: 集成测试
    performance: 性能测试
    slow: 慢速测试
    external: 需要外部依赖的测试
    smoke: 冒烟测试
    regression: 回归测试
    security: 安全测试

# 过滤警告
filterwarnings =
    error
    ignore::UserWarning
    ignore::DeprecationWarning
    ignore::PendingDeprecationWarning

# 测试超时
timeout = 300

# 并行测试
# -n auto 会自动检测CPU核心数
# 可以通过 pytest -n 4 手动指定进程数
