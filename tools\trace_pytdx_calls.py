#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Trace pytdx_downloader calls to find workflow violation
"""

import os
import sys

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def install_pytdx_patch():
    """Install pytdx tracing patch"""
    try:
        from utils.pytdx_downloader import PytdxDownloader
        
        original_download = PytdxDownloader.download_minute_data
        
        def patched_download(self, stock_code, start_date, end_date, frequency='1min'):
            """Patched download_minute_data method"""
            import traceback
            
            print(f"\n*** PytdxDownloader.download_minute_data CALLED ***")
            print(f"Stock: {stock_code}")
            print(f"Date Range: {start_date} - {end_date}")
            print(f"Frequency: {frequency}")

            print(f"\nCall stack:")
            stack = traceback.format_stack()
            for i, frame in enumerate(stack[-10:], 1):  # Show last 10 frames
                if any(keyword in frame for keyword in ['task_manager', 'TaskManager', 'stock_processor', 'StockProcessor']):
                    print(f">>> {i:2d}. {frame.strip()}")
                else:
                    print(f"    {i:2d}. {frame.strip()}")

            print("=" * 100)

            return original_download(self, stock_code, start_date, end_date, frequency)
        
        PytdxDownloader.download_minute_data = patched_download
        
        print("PytdxDownloader patch installed successfully")
        return True
        
    except Exception as e:
        print(f"PytdxDownloader patch installation failed: {e}")
        return False

def run_test():
    """Run the test"""
    print("\nRunning main.py with pytdx tracing...")
    
    try:
        from main import main
        result = main()
        print(f"Main completed with result: {result}")
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    print("PytdxDownloader Call Tracer")
    print("=" * 80)
    
    if not install_pytdx_patch():
        return 1
    
    success = run_test()
    
    print(f"\nTrace completed: {'SUCCESS' if success else 'FAILED'}")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
