2025-07-27 17:15:57,257 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250727_171557.log
2025-07-27 17:15:57,257 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-27 17:15:57,275 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-27 17:15:57,277 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-27 17:15:57,277 - Main - INFO - info:307 - ℹ️ ✅ 使用缓存的最佳服务器: **************:7709
2025-07-27 17:15:57,278 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: **************
2025-07-27 17:15:57,279 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: **************:7709
2025-07-27 17:15:57,279 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-27 17:15:57,279 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-27 17:15:57,279 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-27 17:15:57,279 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-27 17:15:57,736 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-27 17:15:57,736 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-27 17:15:57,743 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-27 17:15:57,743 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-27 17:15:57,744 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-27 17:15:57,744 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-27 17:15:57,744 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-27 17:15:57,744 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-27 17:15:57,745 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-27 17:15:57,748 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-27 17:15:57,749 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-27 17:15:57,749 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-27 17:15:57,749 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-27 17:15:57,756 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-27 17:15:58,197 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.44秒
2025-07-27 17:15:58,198 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.44秒
2025-07-27 17:15:58,198 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-27 17:15:58,198 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-27 17:15:58,198 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-27 17:15:58,198 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-27 17:15:58,198 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-27 17:15:58,199 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-27 17:15:58,199 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: TDX日线级别数据生成
2025-07-27 17:15:58,200 - Main - INFO - info:307 - ℹ️ 📊 发现 5 个现有数据文件，开始稽核
2025-07-27 17:15:58,499 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 4 个文件质量不达标
2025-07-27 17:15:58,500 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-27 17:15:58,500 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-27 17:15:58,500 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-27 17:15:58,500 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-27 17:15:58,500 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-27 17:15:58,619 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-27 17:15:58,619 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-27 17:15:58,629 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-27 17:15:58,630 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-27 17:15:58,630 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-27 17:15:58,630 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-27 17:15:58,630 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-27 17:15:58,630 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-27 17:15:58,630 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-27 17:15:58,637 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-27 17:15:58,637 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-27 17:15:58,637 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-27 17:15:58,637 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-27 17:15:58,647 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-27 17:15:59,182 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.53秒
2025-07-27 17:15:59,182 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.53秒
