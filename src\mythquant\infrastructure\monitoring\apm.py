#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
APM (Application Performance Monitoring) 实现

提供应用性能监控、事务追踪、性能分析等功能
"""

import time
import threading
import logging
from typing import Dict, List, Any, Optional, Callable, ContextManager
from datetime import datetime, timedelta
from dataclasses import dataclass, field
from contextlib import contextmanager
from collections import defaultdict, deque
import functools
# import psutil  # 暂时禁用，避免依赖问题

# 简化的系统信息获取（不依赖psutil）
class SimpleSystemInfo:
    @staticmethod
    def cpu_percent():
        return 0.0  # 占位符

    @staticmethod
    def memory_info():
        return type('obj', (object,), {'percent': 0.0, 'available': 0, 'used': 0})()

    @staticmethod
    def disk_usage(path):
        return type('obj', (object,), {'percent': 0.0, 'free': 0, 'used': 0})()
import gc

logger = logging.getLogger(__name__)


@dataclass
class Transaction:
    """事务记录"""
    transaction_id: str
    name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    status: str = "running"  # running, completed, failed
    metadata: Dict[str, Any] = field(default_factory=dict)
    spans: List['Span'] = field(default_factory=list)
    error: Optional[Exception] = None
    
    def complete(self, status: str = "completed", error: Optional[Exception] = None):
        """完成事务"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
        self.status = status
        self.error = error
    
    def add_span(self, span: 'Span'):
        """添加子跨度"""
        self.spans.append(span)
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'transaction_id': self.transaction_id,
            'name': self.name,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'status': self.status,
            'metadata': self.metadata,
            'spans_count': len(self.spans),
            'error': str(self.error) if self.error else None
        }


@dataclass
class Span:
    """跨度记录"""
    span_id: str
    name: str
    start_time: float
    end_time: Optional[float] = None
    duration: Optional[float] = None
    parent_id: Optional[str] = None
    tags: Dict[str, Any] = field(default_factory=dict)
    logs: List[Dict[str, Any]] = field(default_factory=list)
    
    def finish(self):
        """结束跨度"""
        self.end_time = time.time()
        self.duration = self.end_time - self.start_time
    
    def add_tag(self, key: str, value: Any):
        """添加标签"""
        self.tags[key] = value
    
    def add_log(self, message: str, level: str = "info", **kwargs):
        """添加日志"""
        self.logs.append({
            'timestamp': time.time(),
            'message': message,
            'level': level,
            **kwargs
        })


class PerformanceMonitor:
    """性能监控器"""
    
    def __init__(self, sample_interval: float = 1.0, history_size: int = 1000):
        self._sample_interval = sample_interval
        self._history_size = history_size
        self._running = False
        self._thread: Optional[threading.Thread] = None
        self._lock = threading.RLock()
        
        # 性能数据历史
        self._cpu_history = deque(maxlen=history_size)
        self._memory_history = deque(maxlen=history_size)
        self._disk_history = deque(maxlen=history_size)
        self._network_history = deque(maxlen=history_size)
        
        # 当前进程（简化版本）
        self._process = SimpleSystemInfo()
    
    def start(self):
        """启动监控"""
        with self._lock:
            if self._running:
                return
            
            self._running = True
            self._thread = threading.Thread(target=self._monitor_worker, daemon=True)
            self._thread.start()
            logger.info("性能监控器已启动")
    
    def stop(self):
        """停止监控"""
        with self._lock:
            self._running = False
            if self._thread:
                self._thread.join(timeout=5)
            logger.info("性能监控器已停止")
    
    def get_current_stats(self) -> Dict[str, Any]:
        """获取当前性能统计（简化版本）"""
        try:
            # 简化的性能统计
            return {
                'timestamp': time.time(),
                'cpu': {
                    'percent': 0.0,
                    'times': {'user': 0.0, 'system': 0.0}
                },
                'memory': {
                    'rss': 0,
                    'vms': 0,
                    'percent': 0.0,
                    'available': 0
                },
                'io': {
                    'read_count': 0,
                    'write_count': 0,
                    'read_bytes': 0,
                    'write_bytes': 0
                },
                'threads': 1,
                'file_descriptors': 0,
                'gc_stats': {
                    'collections': gc.get_stats(),
                    'count': gc.get_count()
                }
            }
        except Exception as e:
            logger.error(f"获取性能统计失败: {e}")
            return {}
    
    def get_history_stats(self, metric: str, duration: int = 300) -> List[Dict[str, Any]]:
        """获取历史性能统计"""
        with self._lock:
            if metric == "cpu":
                history = list(self._cpu_history)
            elif metric == "memory":
                history = list(self._memory_history)
            elif metric == "disk":
                history = list(self._disk_history)
            elif metric == "network":
                history = list(self._network_history)
            else:
                return []
            
            # 过滤指定时间范围内的数据
            cutoff_time = time.time() - duration
            return [stat for stat in history if stat.get('timestamp', 0) >= cutoff_time]
    
    def get_summary_stats(self) -> Dict[str, Any]:
        """获取汇总统计"""
        current = self.get_current_stats()
        
        with self._lock:
            cpu_history = list(self._cpu_history)
            memory_history = list(self._memory_history)
        
        summary = {
            'current': current,
            'averages': {},
            'peaks': {},
            'trends': {}
        }
        
        # 计算平均值和峰值
        if cpu_history:
            cpu_values = [stat.get('percent', 0) for stat in cpu_history]
            summary['averages']['cpu'] = sum(cpu_values) / len(cpu_values)
            summary['peaks']['cpu'] = max(cpu_values)
        
        if memory_history:
            memory_values = [stat.get('percent', 0) for stat in memory_history]
            summary['averages']['memory'] = sum(memory_values) / len(memory_values)
            summary['peaks']['memory'] = max(memory_values)
        
        return summary
    
    def _monitor_worker(self):
        """监控工作线程"""
        while self._running:
            try:
                stats = self.get_current_stats()
                
                if stats:
                    with self._lock:
                        # 保存到历史记录
                        self._cpu_history.append({
                            'timestamp': stats['timestamp'],
                            'percent': stats['cpu']['percent']
                        })
                        
                        self._memory_history.append({
                            'timestamp': stats['timestamp'],
                            'percent': stats['memory']['percent'],
                            'rss': stats['memory']['rss']
                        })
                
                time.sleep(self._sample_interval)
                
            except Exception as e:
                logger.error(f"性能监控采样失败: {e}")
                time.sleep(self._sample_interval)


class TransactionTracer:
    """事务追踪器"""
    
    def __init__(self, max_transactions: int = 1000):
        self._max_transactions = max_transactions
        self._transactions: Dict[str, Transaction] = {}
        self._completed_transactions = deque(maxlen=max_transactions)
        self._lock = threading.RLock()
        self._transaction_counter = 0
    
    def start_transaction(self, name: str, metadata: Optional[Dict[str, Any]] = None) -> str:
        """开始事务"""
        with self._lock:
            self._transaction_counter += 1
            transaction_id = f"txn_{self._transaction_counter}_{int(time.time() * 1000)}"
            
            transaction = Transaction(
                transaction_id=transaction_id,
                name=name,
                start_time=time.time(),
                metadata=metadata or {}
            )
            
            self._transactions[transaction_id] = transaction
            logger.debug(f"开始事务: {name} ({transaction_id})")
            return transaction_id
    
    def end_transaction(self, transaction_id: str, status: str = "completed", 
                       error: Optional[Exception] = None):
        """结束事务"""
        with self._lock:
            transaction = self._transactions.get(transaction_id)
            if not transaction:
                logger.warning(f"事务不存在: {transaction_id}")
                return
            
            transaction.complete(status, error)
            
            # 移动到已完成事务
            self._completed_transactions.append(transaction)
            del self._transactions[transaction_id]
            
            logger.debug(f"结束事务: {transaction.name} ({transaction_id}), "
                        f"耗时: {transaction.duration:.3f}s")
    
    def add_span(self, transaction_id: str, span: Span):
        """添加跨度到事务"""
        with self._lock:
            transaction = self._transactions.get(transaction_id)
            if transaction:
                transaction.add_span(span)
    
    @contextmanager
    def transaction(self, name: str, metadata: Optional[Dict[str, Any]] = None):
        """事务上下文管理器"""
        transaction_id = self.start_transaction(name, metadata)
        try:
            yield transaction_id
            self.end_transaction(transaction_id, "completed")
        except Exception as e:
            self.end_transaction(transaction_id, "failed", e)
            raise
    
    def get_active_transactions(self) -> List[Dict[str, Any]]:
        """获取活跃事务"""
        with self._lock:
            return [txn.to_dict() for txn in self._transactions.values()]
    
    def get_completed_transactions(self, limit: int = 100) -> List[Dict[str, Any]]:
        """获取已完成事务"""
        with self._lock:
            transactions = list(self._completed_transactions)[-limit:]
            return [txn.to_dict() for txn in transactions]
    
    def get_transaction_stats(self) -> Dict[str, Any]:
        """获取事务统计"""
        with self._lock:
            completed = list(self._completed_transactions)
            
            if not completed:
                return {
                    'total_transactions': 0,
                    'active_transactions': len(self._transactions),
                    'average_duration': 0,
                    'success_rate': 0
                }
            
            durations = [txn.duration for txn in completed if txn.duration]
            successful = [txn for txn in completed if txn.status == "completed"]
            
            return {
                'total_transactions': len(completed),
                'active_transactions': len(self._transactions),
                'average_duration': sum(durations) / len(durations) if durations else 0,
                'max_duration': max(durations) if durations else 0,
                'min_duration': min(durations) if durations else 0,
                'success_rate': len(successful) / len(completed) if completed else 0,
                'transaction_types': self._get_transaction_type_stats(completed)
            }
    
    def _get_transaction_type_stats(self, transactions: List[Transaction]) -> Dict[str, Any]:
        """获取事务类型统计"""
        type_stats = defaultdict(lambda: {'count': 0, 'total_duration': 0, 'failures': 0})
        
        for txn in transactions:
            stats = type_stats[txn.name]
            stats['count'] += 1
            if txn.duration:
                stats['total_duration'] += txn.duration
            if txn.status == "failed":
                stats['failures'] += 1
        
        # 计算平均值
        result = {}
        for name, stats in type_stats.items():
            result[name] = {
                'count': stats['count'],
                'average_duration': stats['total_duration'] / stats['count'] if stats['count'] > 0 else 0,
                'failure_rate': stats['failures'] / stats['count'] if stats['count'] > 0 else 0
            }
        
        return result


class APMManager:
    """APM管理器"""
    
    def __init__(self):
        self._performance_monitor = PerformanceMonitor()
        self._transaction_tracer = TransactionTracer()
        self._enabled = True
        self._lock = threading.RLock()
    
    def start(self):
        """启动APM监控"""
        with self._lock:
            if not self._enabled:
                return
            
            self._performance_monitor.start()
            logger.info("APM监控已启动")
    
    def stop(self):
        """停止APM监控"""
        with self._lock:
            self._performance_monitor.stop()
            logger.info("APM监控已停止")
    
    def enable(self):
        """启用APM"""
        with self._lock:
            self._enabled = True
    
    def disable(self):
        """禁用APM"""
        with self._lock:
            self._enabled = False
    
    @property
    def performance_monitor(self) -> PerformanceMonitor:
        """性能监控器"""
        return self._performance_monitor
    
    @property
    def transaction_tracer(self) -> TransactionTracer:
        """事务追踪器"""
        return self._transaction_tracer
    
    def get_dashboard_data(self) -> Dict[str, Any]:
        """获取仪表板数据"""
        return {
            'enabled': self._enabled,
            'performance': self._performance_monitor.get_summary_stats(),
            'transactions': self._transaction_tracer.get_transaction_stats(),
            'active_transactions': self._transaction_tracer.get_active_transactions(),
            'recent_transactions': self._transaction_tracer.get_completed_transactions(10)
        }


# 装饰器支持
def monitor_transaction(name: Optional[str] = None, 
                       metadata: Optional[Dict[str, Any]] = None):
    """事务监控装饰器"""
    def decorator(func: Callable) -> Callable:
        transaction_name = name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            tracer = get_apm_manager().transaction_tracer
            
            with tracer.transaction(transaction_name, metadata):
                return func(*args, **kwargs)
        
        return wrapper
    
    return decorator


def monitor_performance(func: Callable) -> Callable:
    """性能监控装饰器"""
    @functools.wraps(func)
    def wrapper(*args, **kwargs):
        start_time = time.time()
        
        try:
            result = func(*args, **kwargs)
            duration = time.time() - start_time
            
            logger.debug(f"函数执行: {func.__name__}, 耗时: {duration:.3f}s")
            return result
            
        except Exception as e:
            duration = time.time() - start_time
            logger.error(f"函数执行失败: {func.__name__}, 耗时: {duration:.3f}s, 错误: {e}")
            raise
    
    return wrapper


# 全局APM管理器
_global_apm_manager: Optional[APMManager] = None


def get_apm_manager() -> APMManager:
    """获取全局APM管理器"""
    global _global_apm_manager
    if _global_apm_manager is None:
        _global_apm_manager = APMManager()
    return _global_apm_manager


def start_apm():
    """启动APM监控"""
    get_apm_manager().start()


def stop_apm():
    """停止APM监控"""
    get_apm_manager().stop()
