#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试夹具模块

提供测试所需的数据夹具和模拟对象
"""

import pandas as pd
import numpy as np
from datetime import datetime, timedelta
from decimal import Decimal
from typing import Dict, List, Optional, Any
import tempfile
import shutil
from pathlib import Path


class TestFixtures:
    """测试夹具类 - 提供各种测试数据和环境"""
    
    def __init__(self):
        self.temp_dir = None
    
    def setup_temp_directory(self) -> Path:
        """创建临时测试目录"""
        if self.temp_dir is None:
            self.temp_dir = Path(tempfile.mkdtemp(prefix="mythquant_test_"))
        return self.temp_dir
    
    def cleanup_temp_directory(self):
        """清理临时测试目录"""
        if self.temp_dir and self.temp_dir.exists():
            shutil.rmtree(self.temp_dir)
            self.temp_dir = None
    
    def create_sample_stock_data(self, 
                                stock_code: str = "000001",
                                start_date: str = "2023-01-01",
                                periods: int = 100,
                                freq: str = "D") -> pd.DataFrame:
        """
        创建样本股票数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            periods: 数据条数
            freq: 频率 ("D"=日, "1min"=分钟)
            
        Returns:
            股票数据DataFrame
        """
        # 生成时间序列
        if freq == "D":
            dates = pd.date_range(start=start_date, periods=periods, freq="B")  # 工作日
            time_col = "date"
        else:
            dates = pd.date_range(start=start_date, periods=periods, freq=freq)
            time_col = "datetime"
        
        # 生成价格数据
        np.random.seed(42)  # 确保可重现
        base_price = 10.0
        
        # 生成随机价格走势
        returns = np.random.normal(0, 0.02, periods)  # 2%日波动率
        prices = [base_price]
        
        for ret in returns[1:]:
            new_price = prices[-1] * (1 + ret)
            prices.append(max(new_price, 0.01))  # 确保价格为正
        
        # 生成OHLC数据
        data = []
        for i, (date, close) in enumerate(zip(dates, prices)):
            # 生成开盘价
            if i == 0:
                open_price = close
            else:
                open_price = prices[i-1] * (1 + np.random.normal(0, 0.005))
            
            # 生成最高最低价
            high = max(open_price, close) * (1 + abs(np.random.normal(0, 0.01)))
            low = min(open_price, close) * (1 - abs(np.random.normal(0, 0.01)))
            
            # 生成成交量
            volume = int(np.random.uniform(1000, 10000))
            
            data.append({
                time_col: date,
                'stock_code': stock_code,
                'open': round(open_price, 3),
                'high': round(high, 3),
                'low': round(low, 3),
                'close': round(close, 3),
                'volume': volume,
                'amount': volume * close
            })
        
        return pd.DataFrame(data)
    
    def create_sample_dividend_data(self, 
                                   stock_code: str = "000001",
                                   start_date: str = "2023-01-01",
                                   periods: int = 4) -> pd.DataFrame:
        """
        创建样本除权除息数据
        
        Args:
            stock_code: 股票代码
            start_date: 开始日期
            periods: 除权次数
            
        Returns:
            除权除息数据DataFrame
        """
        dates = pd.date_range(start=start_date, periods=periods, freq="Q")  # 季度
        
        data = []
        for date in dates:
            data.append({
                'date': date,
                'stock_code': stock_code,
                'dividend': round(np.random.uniform(0.1, 0.5), 3),  # 分红
                'split_ratio': 1.0,  # 拆股比例
                'bonus_ratio': 0.0   # 送股比例
            })
        
        return pd.DataFrame(data)
    
    def create_sample_l2_data(self, base_data: pd.DataFrame) -> pd.DataFrame:
        """
        基于基础数据创建L2指标数据
        
        Args:
            base_data: 基础OHLCV数据
            
        Returns:
            包含L2指标的DataFrame
        """
        result_df = base_data.copy()
        
        # 添加L2指标
        for idx, row in result_df.iterrows():
            high = row['high']
            low = row['low']
            open_price = row['open']
            close = row['close']
            
            # 简化的L2指标计算
            if close > open_price:
                # 阳线
                main_buy = (close - open_price) + (high - close) * 0.6
                main_sell = (open_price - low) * 0.4
            else:
                # 阴线
                main_buy = (high - open_price) * 0.4
                main_sell = (open_price - close) + (close - low) * 0.6
            
            result_df.at[idx, 'main_buy'] = round(main_buy, 3)
            result_df.at[idx, 'main_sell'] = round(main_sell, 3)
            result_df.at[idx, 'path_length'] = round(main_buy + main_sell, 3)
            result_df.at[idx, 'buy_sell_diff'] = round(main_buy - main_sell, 3)
        
        return result_df
    
    def create_sample_config(self) -> Dict[str, Any]:
        """
        创建样本配置数据
        
        Returns:
            配置字典
        """
        temp_dir = self.setup_temp_directory()
        
        return {
            'tdx_path': str(temp_dir / "tdx"),
            'output_path': str(temp_dir / "output"),
            'debug': True,
            'file_encoding': 'utf-8',
            'precision': {
                'price': 3,
                'ratio': 6
            },
            'data_sources': {
                'tdx': {'enabled': True, 'priority': 1},
                'pytdx': {'enabled': True, 'priority': 2},
                'internet': {'enabled': True, 'priority': 3}
            }
        }
    
    def create_sample_output_file(self, 
                                 stock_code: str = "000001",
                                 data_type: str = "day") -> str:
        """
        创建样本输出文件内容
        
        Args:
            stock_code: 股票代码
            data_type: 数据类型
            
        Returns:
            文件内容字符串
        """
        header = "股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖"
        
        # 创建几行样本数据
        sample_rows = [
            f"{stock_code}|2023-01-01|0.100|10.000|9.800|0.500|0.300|0.200",
            f"{stock_code}|2023-01-02|0.150|10.200|10.000|0.600|0.375|0.225",
            f"{stock_code}|2023-01-03|-0.050|10.100|9.900|0.400|0.175|0.225"
        ]
        
        return header + "\n" + "\n".join(sample_rows)
    
    def create_mock_data_source_response(self, 
                                       stock_code: str = "000001",
                                       success: bool = True) -> Dict[str, Any]:
        """
        创建模拟数据源响应
        
        Args:
            stock_code: 股票代码
            success: 是否成功
            
        Returns:
            模拟响应数据
        """
        if success:
            return {
                'status': 'success',
                'data': self.create_sample_stock_data(stock_code, periods=10),
                'source': 'mock',
                'timestamp': datetime.now().isoformat()
            }
        else:
            return {
                'status': 'error',
                'error': 'Mock data source error',
                'data': None,
                'source': 'mock',
                'timestamp': datetime.now().isoformat()
            }


# 全局测试夹具实例
test_fixtures = TestFixtures()

# 便捷函数
def create_test_data(data_type: str = "stock", **kwargs) -> pd.DataFrame:
    """创建测试数据的便捷函数"""
    if data_type == "stock":
        return test_fixtures.create_sample_stock_data(**kwargs)
    elif data_type == "dividend":
        return test_fixtures.create_sample_dividend_data(**kwargs)
    elif data_type == "l2":
        base_data = test_fixtures.create_sample_stock_data(**kwargs)
        return test_fixtures.create_sample_l2_data(base_data)
    else:
        raise ValueError(f"Unknown data type: {data_type}")

def setup_test_environment() -> Path:
    """设置测试环境"""
    return test_fixtures.setup_temp_directory()

def cleanup_test_environment():
    """清理测试环境"""
    test_fixtures.cleanup_temp_directory()


# 导出
__all__ = [
    'TestFixtures',
    'test_fixtures',
    'create_test_data',
    'setup_test_environment',
    'cleanup_test_environment'
]
