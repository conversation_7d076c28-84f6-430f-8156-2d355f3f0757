#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
基础DOCX文本提取工具

使用标准库提取.docx文件的文本内容
"""

import zipfile
import xml.etree.ElementTree as ET
import sys
from pathlib import Path


def extract_text_from_docx(docx_path):
    """从DOCX文件提取文本"""
    try:
        text_content = []
        
        with zipfile.ZipFile(docx_path, 'r') as zip_file:
            # 读取主文档XML
            xml_content = zip_file.read('word/document.xml')
            
            # 解析XML
            root = ET.fromstring(xml_content)
            
            # 提取所有文本节点
            for elem in root.iter():
                if elem.text and elem.tag.endswith('}t'):
                    text_content.append(elem.text)
                elif elem.tag.endswith('}p'):  # 段落结束添加换行
                    text_content.append('\n')
        
        return ''.join(text_content)
    
    except Exception as e:
        return f"读取失败: {str(e)}"


def main():
    if len(sys.argv) != 2:
        print("用法: python basic_docx_extractor.py <docx文件路径>")
        return
    
    docx_path = sys.argv[1]
    
    if not Path(docx_path).exists():
        print(f"文件不存在: {docx_path}")
        return
    
    print(f"正在读取: {docx_path}")
    print("=" * 60)
    
    text = extract_text_from_docx(docx_path)
    print(text)
    
    # 同时保存为文本文件
    output_path = Path(docx_path).with_suffix('.txt')
    with open(output_path, 'w', encoding='utf-8') as f:
        f.write(text)
    
    print("\n" + "=" * 60)
    print(f"文本已保存到: {output_path}")


if __name__ == "__main__":
    main()
