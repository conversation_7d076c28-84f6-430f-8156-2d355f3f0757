#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
真实数据验证器

用于验证数据修复的实际效果，而不是依赖返回值
"""

import os
import pandas as pd
from typing import Dict, Any

class RealDataValidator:
    """真实数据验证器"""
    
    def validate_repair_claims(self, file_path: str, repair_claims: Dict[str, Any]) -> Dict[str, Any]:
        """
        验证修复声明的真实性
        
        Args:
            file_path: 数据文件路径
            repair_claims: 修复声明
            
        Returns:
            验证结果
        """
        try:
            if not os.path.exists(file_path):
                return {'status': 'error', 'message': '文件不存在'}
            
            # 读取实际数据
            df = pd.read_csv(file_path, sep='|', encoding='utf-8')
            df['日期'] = df['时间'].astype(str).str[:8]
            
            # 验证每个修复声明
            validation_results = []
            repair_details = repair_claims.get('repair_details', [])
            
            for detail in repair_details:
                date = detail.get('date', '').replace('-', '')
                claimed_repaired = detail.get('repaired_count', 0)
                
                # 检查实际数据
                date_data = df[df['日期'] == date]
                actual_count = len(date_data)
                expected_count = 240  # A股标准
                actual_missing = max(0, expected_count - actual_count)
                
                # 验证声明
                is_valid = claimed_repaired == 0 or actual_missing == 0
                
                validation_results.append({
                    'date': date,
                    'claimed_repaired': claimed_repaired,
                    'actual_count': actual_count,
                    'expected_count': expected_count,
                    'actual_missing': actual_missing,
                    'claim_valid': is_valid,
                    'discrepancy': claimed_repaired - (expected_count - actual_missing) if claimed_repaired > 0 else 0
                })
            
            return {
                'status': 'success',
                'validation_results': validation_results,
                'overall_valid': all(r['claim_valid'] for r in validation_results)
            }
            
        except Exception as e:
            return {'status': 'error', 'message': str(e)}

def validate_file(file_path: str):
    """验证文件的修复声明"""
    validator = RealDataValidator()
    
    # 模拟修复声明（基于terminal输出）
    repair_claims = {
        'repair_details': [
            {'date': '2025-03-20', 'repaired_count': 56},
            {'date': '2025-07-04', 'repaired_count': 13}
        ]
    }
    
    result = validator.validate_repair_claims(file_path, repair_claims)
    
    if result['status'] == 'success':
        print("📊 验证结果:")
        for validation in result['validation_results']:
            date = validation['date']
            claimed = validation['claimed_repaired']
            actual_count = validation['actual_count']
            actual_missing = validation['actual_missing']
            valid = validation['claim_valid']
            
            status = "✅" if valid else "❌"
            print(f"  {status} {date}: 声称修复{claimed}分钟, 实际{actual_count}/240条, 仍缺失{actual_missing}分钟")
        
        overall_valid = result['overall_valid']
        print(f"\n🎯 总体验证结果: {'✅ 声明属实' if overall_valid else '❌ 声明虚假'}")
    else:
        print(f"❌ 验证失败: {result['message']}")

if __name__ == '__main__':
    # 验证最新数据文件
    data_file = "H:/MPV1.17/T0002/signals/1min_0_000617_202503180931-202508071500_来源互联网（202508080002）.txt"
    validate_file(data_file)
