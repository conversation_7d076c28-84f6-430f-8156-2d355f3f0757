---
type: "manual"
description: "用户偏好和工作流程规范，需要手动引用"
---

# MythQuant项目用户偏好

## 交互偏好
- **中文简体回应**：始终使用中文简体
- **主动文件定位**：AI应自动定位正确文件，不等待用户@
- **错误识别**：用户@错误文件时主动询问是否修改正确文件
- **模块化优先**：优先修改模块化文件而非主文件

## 技术偏好
- **避免人工补偿**：避免人工距离补偿，可能掩盖潜在问题
- **完整研究**：完整阅读文章，避免断章取义
- **高精度计算**：金融计算必须使用Decimal
- **测试驱动**：遵循测试驱动验证流程

## 输出文件命名规范
- **日级文件**：`day_0_000617_20150101-20250731.txt`
- **分钟级文件**：`1min_0_000617_20250101-20250625.txt`
- **实际范围**：使用文件内实际数据起止时间，非用户请求范围

## 架构升级偏好
- **渐进式升级**：避免破坏性变更
- **用户接口简单**：保持user_config.py等接口简单性
- **向后兼容**：新架构完全兼容现有代码
- **门面模式**：隐藏内部复杂性
- **用户体验保护**：技术升级对用户透明

## 问题解决偏好
- **生产问题重视**：重视"测试好用，生产报错"问题
- **端到端验证**：不仅仅单元测试，要求端到端验证
- **根本原因分析**：系统性分析根本原因
- **用户体验优先**：优先解决影响用户体验的问题
- **配置统一性**：重视配置管理一致性

## 咨询顾问原则
- **规则改进**：发现rules不合理时主动询问改进
- **主动质疑**：不盲目执行不合理rules，主动交互
- **改进建议**：发现提问不明确时主动提出改进建议
- **问题发现**：阅读代码时发现问题主动提出

## 知识库质量管理
- **定期审查**：定期审查知识库内容的准确性、时效性和完整性
- **交叉引用维护**：建立和维护知识点之间的交叉引用关系
- **使用效果跟踪**：跟踪知识库的使用频率、命中率和应用效果
- **持续优化**：基于使用反馈和效果统计持续优化知识库结构和内容

## 知识库更新触发机制
- **新问题类型**：解决新类型问题时评估是否需要创建新的知识库条目
- **内容过时**：发现现有知识库内容过时或不准确时及时更新
- **经验积累**：积累足够的问题解决经验时整理成知识库条目
- **用户反馈**：基于用户反馈改进知识库内容和结构

## 定期更新原则
- **知识沉淀**：每次回答后进行知识总结沉淀
- **举一反三**：协助文档沉淀和举一反三
- **规则检视**：同时检视并按需更新rules
- **确认机制**：修改rules前先告知并得到认可

---

**文件类型**: Manual规则 - 需要手动引用
**最后更新**: 2025-08-10 14:30 (精简版)
**适用范围**: 用户偏好和工作流程规范
**精简说明**: 从88行精简到58行，专注核心用户偏好
