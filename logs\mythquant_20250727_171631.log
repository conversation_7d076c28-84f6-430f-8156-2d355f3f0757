2025-07-27 17:16:31,410 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250727_171631.log
2025-07-27 17:16:31,410 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-27 17:16:31,430 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-27 17:16:31,431 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-27 17:16:31,431 - Main - INFO - info:307 - ℹ️ ✅ 使用缓存的最佳服务器: **************:7709
2025-07-27 17:16:31,434 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已经是最新的: **************
2025-07-27 17:16:31,434 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: **************:7709
2025-07-27 17:16:31,434 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-27 17:16:31,434 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-27 17:16:31,435 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-27 17:16:31,435 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-27 17:16:31,885 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-27 17:16:31,885 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-27 17:16:31,892 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-27 17:16:31,892 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-27 17:16:31,892 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-27 17:16:31,892 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-27 17:16:31,893 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-27 17:16:31,893 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-27 17:16:31,893 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-27 17:16:31,898 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-27 17:16:31,899 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-27 17:16:31,899 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-27 17:16:31,899 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-27 17:16:31,908 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-27 17:16:32,314 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.40秒
2025-07-27 17:16:32,315 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.40秒
2025-07-27 17:16:32,315 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-27 17:16:32,315 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-27 17:16:32,315 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-27 17:16:32,315 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-27 17:16:32,315 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-27 17:16:32,316 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-27 17:16:32,316 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: TDX日线级别数据生成
2025-07-27 17:16:32,316 - Main - INFO - info:307 - ℹ️ 📊 发现 4 个现有数据文件，开始稽核
2025-07-27 17:16:32,537 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 3 个文件质量不达标
2025-07-27 17:16:32,537 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-27 17:16:32,537 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-27 17:16:32,537 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-27 17:16:32,537 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-27 17:16:32,537 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-27 17:16:32,656 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-27 17:16:32,656 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-27 17:16:32,662 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-27 17:16:32,662 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-27 17:16:32,663 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-27 17:16:32,663 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-27 17:16:32,663 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-27 17:16:32,663 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-27 17:16:32,663 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-27 17:16:32,667 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-27 17:16:32,667 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-27 17:16:32,667 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-27 17:16:32,667 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-27 17:16:32,674 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-27 17:16:33,246 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.57秒
2025-07-27 17:16:33,246 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.57秒
2025-07-27 17:16:43,152 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-27 17:17:07,077 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-27 17:17:07,077 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-27 17:17:07,077 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-27 17:17:07,084 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-27 17:17:07,628 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-27 17:17:07,628 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-27 17:17:07,628 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-27 17:17:07,629 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-27 17:17:07,630 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-27 17:17:07,631 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-27 17:17:07,631 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-27 17:17:07,631 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-27 17:17:07,631 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-27 17:17:07,684 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-27 17:17:08,126 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-27 17:17:08,139 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-27 17:17:08,155 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-27 17:17:08,166 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-27 17:17:08,177 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-27 17:17:08,187 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-27 17:17:08,196 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-27 17:17:08,207 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-27 17:17:08,218 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-27 17:17:08,228 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-27 17:17:08,239 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-27 17:17:08,252 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-27 17:17:08,261 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-27 17:17:08,272 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-27 17:17:08,282 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-27 17:17:08,296 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-27 17:17:08,307 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-27 17:17:08,317 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-27 17:17:08,327 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-27 17:17:08,338 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-27 17:17:08,348 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-27 17:17:08,348 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-27 17:17:12,747 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-27 17:17:12,747 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-27 17:17:12,747 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-27 17:17:12,748 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-27 17:17:12,748 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-27 17:17:12,750 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-27 17:17:12,854 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-27 17:17:12,855 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-27 17:17:12,855 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-27 17:17:12,855 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-27 17:17:12,855 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-27 17:17:12,856 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-27 17:17:12,856 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-27 17:17:12,865 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-27 17:17:13,031 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-27 17:17:13,525 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-27 17:17:13,698 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-27 17:17:13,698 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-27 17:17:13,775 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-27 17:17:13,776 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-27 17:17:13,776 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-27 17:17:13,776 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-27 17:17:13,776 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-27 17:17:13,777 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-27 17:17:13,777 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-27 17:17:13,792 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 41.25 秒
2025-07-27 17:17:13,792 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-27 17:17:13,792 - Main - INFO - info:307 - ℹ️ 🔍 执行任务后数据质量稽核: TDX日线级别数据生成
2025-07-27 17:17:13,793 - Main - INFO - info:307 - ℹ️ 📊 发现 1 个新生成文件，开始稽核
2025-07-27 17:17:13,841 - Main - INFO - info:307 - ℹ️ ✅ 执行后稽核通过: 0 优秀, 1 良好
2025-07-27 17:17:13,842 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: 互联网分钟级数据下载
2025-07-27 17:17:13,842 - Main - INFO - info:307 - ℹ️ 📊 发现 5 个现有数据文件，开始稽核
2025-07-27 17:17:14,149 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 4 个文件质量不达标
2025-07-27 17:17:14,149 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-27 17:17:14,149 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-27 17:17:14,150 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-27 17:17:14,151 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-27 17:17:14,540 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-27 17:17:14,552 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-27 17:17:14,552 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-27 17:17:14,553 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-27 17:17:14,553 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-27 17:17:14,553 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-27 17:17:14,553 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-27 17:17:14,554 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-27 17:17:14,554 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-27 17:17:14,558 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-27 17:17:14,558 - core.logging_service - INFO - verbose_log:67 - ✅ 加载智能文件选择器配置: 策略=smart_comprehensive
2025-07-27 17:17:14,558 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-27 17:17:14,558 - core.logging_service - INFO - verbose_log:67 - 找到唯一文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:17:14,558 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:17:14,559 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:17:14,559 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-27 17:17:14,559 - core.logging_service - INFO - verbose_log:67 - 📊 分析现有文件时间跨度: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:17:14,561 - core.logging_service - INFO - verbose_log:67 - 📅 现有文件时间跨度: 20250303 ~ 20250704 (20381条记录)
2025-07-27 17:17:14,575 - core.logging_service - WARNING - verbose_log:67 - ⚠️ 发现2个不完整的交易日
2025-07-27 17:17:14,576 - core.logging_service - WARNING - verbose_log:67 -   20250303: 234/240条 (缺失开始0930-0937)
2025-07-27 17:17:14,576 - core.logging_service - WARNING - verbose_log:67 -   20250704: 227/240条 (缺失开始0930-0931; 缺失结束1447-1500)
2025-07-27 17:17:14,576 - core.logging_service - WARNING - verbose_log:67 - ⚠️ 检测到数据不完整，可能需要补充下载
2025-07-27 17:17:14,576 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-27 17:17:14,576 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250704
2025-07-27 17:17:14,577 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-27 17:17:14,577 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-27 17:17:14,577 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-27 17:17:14,577 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-27 17:17:14,577 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-27 17:17:14,577 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-27 17:17:14,577 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-27 17:17:14,577 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250705 ~ 20250727
2025-07-27 17:17:14,578 - core.logging_service - INFO - verbose_log:67 - 🔍 验证最后一条记录的数据一致性
2025-07-27 17:17:14,579 - core.logging_service - INFO - verbose_log:67 - 📋 文件最后记录: 时间=202507041447, 前复权价=7.55
2025-07-27 17:17:14,580 - core.logging_service - INFO - verbose_log:67 - 🔄 从API获取 20250704 的数据进行比对验证
2025-07-27 17:17:14,580 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-27 17:17:14,580 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-27 17:17:14,580 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-27 17:17:14,580 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:14,797 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:14,797 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4608条 (目标日期: 20250704, 交易日: 16天, 频率: 1min)
2025-07-27 17:17:14,797 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4608条 -> 实际请求800条 (配置限制:800)
2025-07-27 17:17:14,797 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-27 17:17:14,797 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-27 17:17:14,858 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3808条
2025-07-27 17:17:14,858 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:15,106 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:15,175 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-27 17:17:15,175 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:15,402 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:15,468 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-27 17:17:15,468 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:15,689 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:15,750 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-27 17:17:15,751 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:15,984 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:16,049 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-27 17:17:16,050 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:16,256 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:16,314 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +608条，总计4608条
2025-07-27 17:17:16,314 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4608条数据
2025-07-27 17:17:16,328 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-27 17:17:16,331 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-27 17:17:16,331 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-27 17:17:16,333 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-27 17:17:16,334 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-27 17:17:16,334 - Main - INFO - info:307 - ℹ️   日期: 202507040931
2025-07-27 17:17:16,334 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-27 17:17:16,334 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-27 17:17:16,334 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-27 17:17:16,334 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-27 17:17:16,335 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-27 17:17:16,335 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-27 17:17:16,335 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-27 17:17:16,338 - core.logging_service - INFO - verbose_log:67 - 📋 API对应记录: 时间=202507041447, 前复权价=7.55
2025-07-27 17:17:16,339 - core.logging_service - INFO - verbose_log:67 - 📊 数据一致性比对结果:
2025-07-27 17:17:16,339 - core.logging_service - INFO - verbose_log:67 -   时间比对: 文件=202507041447 vs API=202507041447 -> ✅ 一致
2025-07-27 17:17:16,339 - core.logging_service - INFO - verbose_log:67 -   价格比对: 文件=7.55 vs API=7.55 -> ✅ 一致
2025-07-27 17:17:16,339 - core.logging_service - INFO - verbose_log:67 - 🎯 最终判断: ✅ 数据一致，可以使用增量下载
2025-07-27 17:17:16,339 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-27 17:17:16,341 - core.logging_service - INFO - verbose_log:67 - 获取最后一条记录: 时间=202507041447, 前复权价=7.55
2025-07-27 17:17:16,342 - core.logging_service - INFO - verbose_log:67 - 时间格式对比: 文件=202507041447, API=202507041447, 级别=1min
2025-07-27 17:17:16,342 - core.logging_service - INFO - verbose_log:67 - ✅ 时间格式一致性验证通过
2025-07-27 17:17:16,342 - core.logging_service - INFO - verbose_log:67 - ✅ 价格一致性验证通过
2025-07-27 17:17:16,342 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-27 17:17:16,342 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250303 - 20250704
2025-07-27 17:17:16,342 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-27 17:17:16,343 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250704
2025-07-27 17:17:16,343 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-27 17:17:16,343 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-27 17:17:16,343 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-27 17:17:16,343 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-27 17:17:16,343 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-27 17:17:16,344 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-27 17:17:16,344 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-27 17:17:16,344 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250705 ~ 20250727
2025-07-27 17:17:16,344 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250302
2025-07-27 17:17:16,344 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-07-27 17:17:16,357 - Main - INFO - info:307 - ℹ️ 读取现有数据: 20381 条记录
2025-07-27 17:17:16,357 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250302
2025-07-27 17:17:16,357 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-27 17:17:16,357 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-27 17:17:16,357 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250302
2025-07-27 17:17:16,357 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:16,564 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:16,565 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 42624条 (目标日期: 20250101, 交易日: 148天, 频率: 1min)
2025-07-27 17:17:16,565 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计42624条 -> 实际请求800条 (配置限制:800)
2025-07-27 17:17:16,565 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-27 17:17:16,565 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-27 17:17:16,625 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需41824条
2025-07-27 17:17:16,625 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:16,849 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:16,914 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-27 17:17:16,914 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:17,124 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:17,186 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-27 17:17:17,186 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:17,391 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:17,453 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-27 17:17:17,453 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:17,704 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:17,772 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-27 17:17:17,772 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:17,980 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:18,040 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-27 17:17:18,040 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:18,256 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:18,321 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-27 17:17:18,321 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:18,562 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:18,634 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-27 17:17:18,634 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:18,853 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:18,924 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-27 17:17:18,925 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:19,116 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:19,172 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-27 17:17:19,172 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:19,370 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:19,431 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-27 17:17:19,431 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:19,636 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:19,696 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-27 17:17:19,697 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:19,904 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:19,965 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-27 17:17:19,965 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:20,208 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:20,281 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-27 17:17:20,281 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:20,494 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:20,556 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-27 17:17:20,556 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:20,764 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:20,826 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-27 17:17:20,827 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:21,079 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:21,153 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-27 17:17:21,153 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:21,354 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:21,414 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-27 17:17:21,415 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:21,636 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:21,696 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-27 17:17:21,697 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:21,942 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:22,006 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-27 17:17:22,006 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:22,222 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:22,286 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-27 17:17:22,286 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:22,494 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:22,558 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-27 17:17:22,558 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:22,795 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:22,861 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-27 17:17:22,861 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:23,046 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:23,104 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-27 17:17:23,105 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:23,335 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:23,397 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-27 17:17:23,397 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:23,612 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:23,673 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-27 17:17:23,673 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:23,904 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:23,978 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-27 17:17:23,979 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:24,211 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:24,279 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-27 17:17:24,279 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:24,499 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:24,561 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-27 17:17:24,561 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:24,786 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:24,852 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-27 17:17:24,852 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:25,090 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:25,151 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-27 17:17:25,152 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要42624条，实际获取24000条
2025-07-27 17:17:25,152 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约18624条数据（约77个交易日）
2025-07-27 17:17:25,152 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-27 17:17:25,152 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-27 17:17:25,211 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250101 - 20250302
2025-07-27 17:17:25,216 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-27 17:17:25,216 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-27 17:17:25,217 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-07-27 17:17:25,217 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-27 17:17:25,217 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-27 17:17:25,217 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-07-27 17:17:25,217 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:25,429 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:25,429 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4320条 (目标日期: 20250705, 交易日: 15天, 频率: 1min)
2025-07-27 17:17:25,430 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4320条 -> 实际请求800条 (配置限制:800)
2025-07-27 17:17:25,430 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-27 17:17:25,430 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-27 17:17:25,491 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3520条
2025-07-27 17:17:25,491 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:25,730 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:25,797 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-27 17:17:25,797 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:25,992 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:26,048 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-27 17:17:26,048 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:26,261 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:26,327 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-27 17:17:26,327 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:26,548 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:26,612 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-27 17:17:26,612 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: **************:7709
2025-07-27 17:17:26,815 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: **************:7709
2025-07-27 17:17:26,873 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +320条，总计4320条
2025-07-27 17:17:26,873 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4320条数据
2025-07-27 17:17:26,885 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-07-27 17:17:26,889 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共3600条记录
2025-07-27 17:17:26,889 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-27 17:17:26,903 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.640, 前复权=7.640
2025-07-27 17:17:26,904 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-27 17:17:26,904 - Main - INFO - info:307 - ℹ️   日期: 202507070931
2025-07-27 17:17:26,904 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.64
2025-07-27 17:17:26,905 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.64
2025-07-27 17:17:26,905 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-27 17:17:26,905 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 3600 >= 5
2025-07-27 17:17:26,905 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-27 17:17:26,905 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-27 17:17:26,906 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-27 17:17:26,906 - Main - INFO - info:307 - ℹ️ 获得新数据: 3600 条记录
2025-07-27 17:17:26,909 - Main - INFO - info:307 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-07-27 17:17:26,918 - Main - INFO - info:307 - ℹ️ 合并后数据: 23981 条记录
2025-07-27 17:17:26,921 - core.logging_service - INFO - verbose_log:67 - 📝 时间范围变化，需要重命名:
2025-07-27 17:17:26,921 - core.logging_service - INFO - verbose_log:67 -   原始: 20250303-20250704
2025-07-27 17:17:26,921 - core.logging_service - INFO - verbose_log:67 -   更新: 202503030937-202507251500
2025-07-27 17:17:26,921 - core.logging_service - INFO - verbose_log:67 -   新文件名: 1min_0_000617_202503030937-202507251500_来源互联网（202507271717）.txt
2025-07-27 17:17:26,976 - Main - INFO - info:307 - ℹ️ 删除旧文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:17:26,976 - Main - INFO - info:307 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503030937-202507251500_来源互联网（202507271717）.txt
2025-07-27 17:17:26,977 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-27 17:17:26,977 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-27 17:17:26,977 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-27 17:17:26,977 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-27 17:17:26,977 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-27 17:17:26,977 - Main - INFO - info:307 - ℹ️ 🔍 执行任务后数据质量稽核: 互联网分钟级数据下载
2025-07-27 17:17:26,978 - Main - INFO - info:307 - ℹ️ 📊 发现 2 个新生成文件，开始稽核
2025-07-27 17:17:27,385 - Main - INFO - info:307 - ℹ️ ✅ 执行后稽核通过: 1 优秀, 1 良好
2025-07-27 17:17:27,386 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: 前复权数据比较分析
2025-07-27 17:17:27,386 - Main - INFO - info:307 - ℹ️ 📊 发现 5 个现有数据文件，开始稽核
2025-07-27 17:17:27,748 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 4 个文件质量不达标
2025-07-27 17:17:27,748 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-27 17:17:27,748 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-27 17:17:27,749 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-27 17:17:27,749 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-27 17:17:27,750 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-27 17:17:27,750 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-27 17:17:27,750 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-27 17:17:27,750 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: True, 互联网: False
2025-07-27 17:17:27,751 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250727_171727.txt
2025-07-27 17:17:27,751 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-27 17:17:27,751 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-27 17:17:27,752 - Main - INFO - info:307 - ℹ️ 🔍 执行最终数据质量稽核汇总
2025-07-27 17:17:27,752 - Main - INFO - info:307 - ℹ️ 📊 对 5 个数据文件进行最终稽核
2025-07-27 17:17:28,135 - Main - INFO - info:307 - ℹ️ 📊 最终数据质量稽核汇总:
2025-07-27 17:17:28,136 - Main - INFO - info:307 - ℹ️   总文件数: 2
2025-07-27 17:17:28,136 - Main - INFO - info:307 - ℹ️   优秀文件: 1 (50.0%)
2025-07-27 17:17:28,136 - Main - INFO - info:307 - ℹ️   良好文件: 1 (50.0%)
2025-07-27 17:17:28,136 - Main - INFO - info:307 - ℹ️   较差文件: 0 (0.0%)
2025-07-27 17:17:28,136 - Main - INFO - info:307 - ℹ️   平均完整率: 95.89%
2025-07-27 17:17:28,136 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 66.7%
2025-07-27 17:17:28,137 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-27 17:17:28,137 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-27 17:17:28,137 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-27 17:17:28,137 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-27 17:17:28,138 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-27 17:17:28,138 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-27 17:17:28,138 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
