#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
特定分钟数据获取示例

演示如何使用新的特定分钟数据获取接口

作者: AI Assistant
创建时间: 2025-07-29
"""

import sys
import os

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))))

from test_environments.shared.utilities.specific_minute_data_fetcher import (
    SpecificMinuteDataFetcher,
    get_specific_minute_data,
    get_price_at_time
)


def example_1_basic_usage():
    """示例1：基本使用方法"""
    print("📋 示例1：基本使用方法")
    print("-" * 40)
    
    # 方法1：使用便捷函数
    data = get_specific_minute_data('000617', '202507241447')
    
    if data:
        print(f"✅ 获取成功:")
        print(f"   股票代码: {data['stock_code']}")
        print(f"   时间: {data['datetime']}")
        print(f"   收盘价: {data['close']:.3f}")
        print(f"   前复权价: {data['close_qfq']:.3f}")
    else:
        print("❌ 未获取到数据")
    
    # 方法2：只获取价格
    price = get_price_at_time('000617', '202507241447', 'close_qfq')
    if price:
        print(f"✅ 前复权收盘价: {price:.3f}")


def example_2_class_usage():
    """示例2：使用类方法"""
    print("\n📋 示例2：使用类方法")
    print("-" * 40)
    
    # 创建获取器
    fetcher = SpecificMinuteDataFetcher()
    
    # 获取数据
    data = fetcher.get_minute_data('000617', '202507241447')
    
    if data:
        print(f"✅ 完整数据:")
        for key, value in data.items():
            if isinstance(value, float):
                print(f"   {key}: {value:.3f}")
            else:
                print(f"   {key}: {value}")


def example_3_batch_usage():
    """示例3：批量获取"""
    print("\n📋 示例3：批量获取多个时间点")
    print("-" * 40)
    
    fetcher = SpecificMinuteDataFetcher()
    
    # 获取多个时间点的数据
    times = ['202507241447', '202507241448', '202507241449']
    batch_data = fetcher.get_multiple_minutes('000617', times)
    
    print(f"📊 批量获取结果:")
    for time_str, data in batch_data.items():
        if data:
            print(f"   {time_str}: 前复权价={data['close_qfq']:.3f}")
        else:
            print(f"   {time_str}: 无数据")


def example_4_price_comparison():
    """示例4：价格比较"""
    print("\n📋 示例4：价格比较（原始价格 vs 前复权价格）")
    print("-" * 40)
    
    fetcher = SpecificMinuteDataFetcher()
    
    comparison = fetcher.compare_prices_at_time('000617', '202507241447')
    
    if comparison:
        print(f"✅ 价格比较结果:")
        print(f"   原始收盘价: {comparison['original_price']:.3f}")
        print(f"   前复权价格: {comparison['adjusted_price']:.3f}")
        print(f"   调整比例: {comparison['adjustment_ratio']:.4f}")
        print(f"   调整幅度: {comparison['adjustment_factor']:.3f}")
        print(f"   是否有除权: {'是' if comparison['has_adjustment'] else '否'}")


def example_5_trading_session():
    """示例5：获取交易时段数据"""
    print("\n📋 示例5：获取交易时段数据")
    print("-" * 40)
    
    fetcher = SpecificMinuteDataFetcher()
    
    # 获取上午时段的前5分钟数据
    morning_data = fetcher.get_trading_session_data('000617', '20250724', 'morning')
    
    if morning_data:
        print(f"✅ 上午时段数据（前5条）:")
        for i, data in enumerate(morning_data[:5]):
            print(f"   {data['datetime']}: 前复权价={data['close_qfq']:.3f}")
    else:
        print("❌ 未获取到上午时段数据")


def main():
    """主函数"""
    print("🧪 特定分钟数据获取接口使用示例")
    print("=" * 60)
    
    try:
        example_1_basic_usage()
        example_2_class_usage()
        example_3_batch_usage()
        example_4_price_comparison()
        example_5_trading_session()
        
        print(f"\n✅ 所有示例执行完成")
        
    except Exception as e:
        print(f"\n❌ 示例执行失败: {e}")
        import traceback
        traceback.print_exc()


if __name__ == '__main__':
    main()
