2025-07-29 00:19:02,939 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250729_001902.log
2025-07-29 00:19:02,939 - Main - INFO - info:307 - ℹ️ pytdx自动检测已禁用，跳过服务器检测
2025-07-29 00:19:02,939 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成: H:/MPV1.17
2025-07-29 00:19:02,939 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-29 00:19:02,939 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-29 00:19:02,940 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-29 00:19:02,940 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-29 00:19:02,941 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-29 00:19:02,941 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-29 00:19:03,370 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-29 00:19:03,370 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 00:19:03,370 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 00:19:03,371 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-29 00:19:03,575 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4320条 (目标日期: 20250704, 交易日: 18天, 频率: 1min)
2025-07-29 00:19:03,575 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4320条 -> 实际请求800条 (配置限制:800)
2025-07-29 00:19:03,575 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 00:19:03,575 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 00:19:03,629 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3520条
2025-07-29 00:19:03,857 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-29 00:19:04,089 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-29 00:19:04,324 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-29 00:19:04,551 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-29 00:19:04,768 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +320条，总计4320条
2025-07-29 00:19:04,768 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4320条数据
2025-07-29 00:19:04,769 - Main - INFO - info:307 - ℹ️ 📊 数据量充足: 需要4320条，实际获取4320条
2025-07-29 00:19:04,784 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-29 00:19:04,786 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-29 00:19:04,786 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-29 00:19:04,788 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-29 00:19:04,789 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-29 00:19:04,789 - Main - INFO - info:307 - ℹ️   日期: 202507040931
2025-07-29 00:19:04,789 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-29 00:19:04,789 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-29 00:19:04,794 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250704-20250704_来源互联网（202507290019）.txt (11331 字节)
2025-07-29 00:19:04,795 - Main - INFO - info:307 - ℹ️ ℹ️ 增量下载前提条件不满足: 不具备增量下载前提条件: 前复权价格不一致，存在分红配股影响
2025-07-29 00:19:04,795 - Main - INFO - info:307 - ℹ️ 🔧 开始缺失数据稽核与修复
2025-07-29 00:19:04,795 - Main - INFO - info:307 - ℹ️ 🔧 缺失数据处理器初始化完成
2025-07-29 00:19:04,796 - Main - INFO - info:307 - ℹ️ 🔍 开始检测000617的缺失数据
2025-07-29 00:19:04,827 - Main - INFO - info:307 - ℹ️ 📊 数据统计: 总记录数17211, 覆盖72个交易日
2025-07-29 00:19:04,828 - Main - WARNING - warning:311 - ⚠️ ⚠️ 发现缺失数据: 完全缺失0天, 不完整2天
2025-07-29 00:19:04,828 - Main - WARNING - warning:311 - ⚠️    📅 2025-03-20: 实际184行, 缺失56行
2025-07-29 00:19:04,828 - Main - WARNING - warning:311 - ⚠️    📅 2025-07-04: 实际227行, 缺失13行
2025-07-29 00:19:04,828 - Main - INFO - info:307 - ℹ️ 🔧 开始修复000617的缺失数据
2025-07-29 00:19:04,828 - Main - INFO - info:307 - ℹ️ 🔧 尝试修复2个不完整的交易日
2025-07-29 00:19:04,828 - Main - INFO - info:307 - ℹ️ 🔧 分析2个不完整交易日的缺失时间段
2025-07-29 00:19:04,829 - Main - INFO - info:307 - ℹ️ 📊 分析2025-03-20: 实际184行, 缺失56行
2025-07-29 00:19:04,829 - Main - INFO - info:307 - ℹ️ 📊 分析2025-07-04: 实际227行, 缺失13行
2025-07-29 00:19:04,829 - Main - INFO - info:307 - ℹ️ ℹ️ 标记2个不完整交易日需要修复（实际修复逻辑待实现）
2025-07-29 00:19:04,829 - Main - INFO - info:307 - ℹ️ ✅ 缺失数据修复完成
2025-07-29 00:19:04,829 - Main - INFO - info:307 - ℹ️ 📥 开始数据下载
2025-07-29 00:19:04,829 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-29 00:19:04,829 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-29 00:19:04,829 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-29 00:19:04,830 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250727
2025-07-29 00:19:05,009 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 32880条 (目标日期: 20250101, 交易日: 137天, 频率: 1min)
2025-07-29 00:19:05,009 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计32880条 -> 实际请求800条 (配置限制:800)
2025-07-29 00:19:05,009 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-29 00:19:05,010 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-29 00:19:05,063 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需32080条
2025-07-29 00:19:05,292 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-29 00:19:05,526 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-29 00:19:05,752 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-29 00:19:05,977 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-29 00:19:06,198 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-29 00:19:06,433 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-29 00:19:06,657 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-29 00:19:06,875 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-29 00:19:07,104 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-29 00:19:07,338 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-29 00:19:07,559 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-29 00:19:07,786 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-29 00:19:08,018 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-29 00:19:08,239 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-29 00:19:08,460 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-29 00:19:08,689 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-29 00:19:08,919 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-29 00:19:09,144 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-29 00:19:09,380 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-29 00:19:09,612 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-29 00:19:09,830 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-29 00:19:10,064 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-29 00:19:10,287 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-29 00:19:10,520 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-29 00:19:10,746 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-29 00:19:10,967 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-29 00:19:11,195 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计21600条数据
2025-07-29 00:19:11,201 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要32880条，实际获取21600条
2025-07-29 00:19:11,201 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约11280条数据（约47个交易日）
2025-07-29 00:19:11,202 - Main - WARNING - warning:311 - ⚠️ ⚠️ 实际数据范围: 2025-03- ~ 2025-07-
2025-07-29 00:19:11,202 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-29 00:19:11,202 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-29 00:19:11,256 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 21360 条 1min 数据
2025-07-29 00:19:11,265 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共21360条记录
2025-07-29 00:19:11,266 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-29 00:19:11,337 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.600, 前复权=6.600
2025-07-29 00:19:11,341 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-29 00:19:11,341 - Main - INFO - info:307 - ℹ️   日期: 202503180931
2025-07-29 00:19:11,341 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.6
2025-07-29 00:19:11,341 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.6
2025-07-29 00:19:11,394 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1min_0_000617_20250318-20250725_来源互联网（202507290019）.txt (999617 字节)
2025-07-29 00:19:11,395 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-29 00:19:11,395 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-29 00:19:11,395 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-29 00:19:11,396 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-29 00:19:11,396 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-29 00:19:11,396 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-29 00:19:11,396 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: 未找到
2025-07-29 00:19:11,396 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-29 00:19:11,396 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: False, 互联网: False
2025-07-29 00:19:11,398 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250729_001911.txt
2025-07-29 00:19:11,398 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-29 00:19:11,398 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-29 00:19:11,398 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 50.0%
2025-07-29 00:19:11,399 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-29 00:19:11,399 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-29 00:19:11,399 - utils.async_io_processor - INFO - cleanup:353 - 异步IO处理器资源清理完成
2025-07-29 00:19:11,399 - Main - INFO - info:307 - ℹ️ 异步IO处理器资源清理完成
2025-07-29 00:19:11,399 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-29 00:19:11,399 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-29 00:19:11,399 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-29 00:19:11,399 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
