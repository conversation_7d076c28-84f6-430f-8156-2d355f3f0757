2025-07-26 12:08:26,472 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250726_120826.log
2025-07-26 12:08:26,472 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-26 12:08:26,492 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-26 12:08:26,501 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-26 12:08:26,501 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-26 12:08:26,501 - Main - INFO - info:307 - ℹ️ 🚫 智能过滤: 跳过48个黑名单服务器
2025-07-26 12:08:26,501 - Main - INFO - info:307 - ℹ️ 开始并行测试 6 个服务器...
2025-07-26 12:08:26,760 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.12.136.250 (60.12.136.250:7709) - 0.167s
2025-07-26 12:08:26,787 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.191.117.167 (60.191.117.167:7709) - 0.185s
2025-07-26 12:08:26,791 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.185s
2025-07-26 12:08:26,791 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.188s
2025-07-26 12:08:26,795 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************ (************:7709) - 0.187s
2025-07-26 12:08:26,799 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.200s
2025-07-26 12:08:26,804 - Main - WARNING - warning:311 - ⚠️ 未找到pytdx_ip配置项，尝试其他模式
2025-07-26 12:08:26,804 - Main - WARNING - warning:311 - ⚠️ 配置文件中未找到pytdx_ip配置项
2025-07-26 12:08:26,804 - Main - ERROR - error:315 - ❌ 配置文件更新失败
2025-07-26 12:08:26,805 - Main - WARNING - warning:311 - ⚠️ pytdx服务器检测失败
2025-07-26 12:08:26,805 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-26 12:08:26,805 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-26 12:08:26,805 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 12:08:26,805 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 12:08:27,313 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 12:08:27,313 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 12:08:27,321 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 12:08:27,321 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 12:08:27,321 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 12:08:27,321 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 12:08:27,321 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 12:08:27,322 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 12:08:27,322 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 12:08:27,326 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 12:08:27,326 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 12:08:27,326 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 12:08:27,326 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 12:08:27,333 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 12:08:27,721 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.39秒
2025-07-26 12:08:27,721 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.39秒
2025-07-26 12:08:27,721 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-26 12:08:27,722 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-26 12:08:27,722 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-26 12:08:27,722 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-26 12:08:27,722 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-26 12:08:27,722 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-26 12:08:27,722 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-26 12:08:27,723 - main_v20230219_optimized - INFO - generate_daily_data_task:3275 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-26 12:08:27,723 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:3814 - 🧵 启动多线程日线级别txt文件生成
2025-07-26 12:08:27,723 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-26 12:08:27,723 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-26 12:08:27,832 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-26 12:08:27,833 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-26 12:08:27,839 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-26 12:08:27,839 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-26 12:08:27,839 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-26 12:08:27,840 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-26 12:08:27,840 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-26 12:08:27,840 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-26 12:08:27,840 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-26 12:08:27,843 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-26 12:08:27,843 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-26 12:08:27,843 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-26 12:08:27,843 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-26 12:08:27,850 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-26 12:08:28,563 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.71秒
2025-07-26 12:08:28,563 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.71秒
2025-07-26 12:08:38,407 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-26 12:09:01,357 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-26 12:09:01,358 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-26 12:09:01,358 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-26 12:09:01,366 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-26 12:09:01,820 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-26 12:09:01,821 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-26 12:09:01,821 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-26 12:09:01,821 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-26 12:09:01,823 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-26 12:09:01,823 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-26 12:09:01,823 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-26 12:09:01,823 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-26 12:09:01,824 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-26 12:09:01,879 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-26 12:09:02,303 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-26 12:09:02,320 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-26 12:09:02,338 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-26 12:09:02,354 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-26 12:09:02,369 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-26 12:09:02,378 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-26 12:09:02,395 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-26 12:09:02,412 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-26 12:09:02,427 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-26 12:09:02,444 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-26 12:09:02,458 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-26 12:09:02,473 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-26 12:09:02,488 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-26 12:09:02,505 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-26 12:09:02,522 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-26 12:09:02,539 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-26 12:09:02,553 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-26 12:09:02,568 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-26 12:09:02,582 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-26 12:09:02,592 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-26 12:09:02,602 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-26 12:09:02,602 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-26 12:09:06,561 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-26 12:09:06,561 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-26 12:09:06,562 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-26 12:09:06,562 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-26 12:09:06,562 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-26 12:09:06,564 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-26 12:09:06,658 - main_v20230219_optimized - INFO - apply_forward_adjustment:1911 - 保留交易时间数据后: 320880条
2025-07-26 12:09:06,658 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-26 12:09:06,658 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-26 12:09:06,659 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-26 12:09:06,659 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-26 12:09:06,659 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-26 12:09:06,659 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-26 12:09:06,669 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-26 12:09:06,842 - main_v20230219_optimized - INFO - load_and_process_minute_data:2380 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-26 12:09:07,310 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-26 12:09:07,504 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-26 12:09:07,505 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-26 12:09:07,557 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-26 12:09:07,558 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-26 12:09:07,558 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3993 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-26 12:09:07,558 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3994 - 📊 成功生成: 1 个文件
2025-07-26 12:09:07,558 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3995 - ❌ 处理失败: 0 个股票
2025-07-26 12:09:07,558 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3996 - 📈 成功率: 100.0%
2025-07-26 12:09:07,558 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:3997 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-26 12:09:07,572 - main_v20230219_optimized - INFO - generate_daily_data_task:3285 - ✅ 日线数据生成完成，耗时: 39.85 秒
2025-07-26 12:09:07,572 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-26 12:09:07,572 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网日线数据下载
2025-07-26 12:09:07,572 - Main - INFO - info:307 - ℹ️ 开始执行互联网日线数据下载任务
2025-07-26 12:09:07,573 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 12:09:07,574 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 12:09:07,909 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 12:09:07,921 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 互联网数据下载时间范围: 20170101 - 20250720
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的互联网数据
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 📊 频率转换: d -> daily
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 daily 数据: 20170101 - 20250720
2025-07-26 12:09:07,922 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:08,090 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:08,090 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计749520条 -> 实际请求800条 (配置限制:800)
2025-07-26 12:09:08,090 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 daily 数据
2025-07-26 12:09:08,090 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=9, market=0, code=000617, count=800
2025-07-26 12:09:08,141 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需748720条
2025-07-26 12:09:08,141 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:08,312 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:08,364 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 12:09:08,364 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:08,534 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:08,585 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 12:09:08,585 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:08,758 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:08,810 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 12:09:08,810 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:08,989 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:09,042 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 12:09:09,042 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:09,217 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:09,268 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 12:09:09,269 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:09,447 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:09,499 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 12:09:09,499 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:09,671 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:09,722 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 12:09:09,722 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:09,889 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:09,936 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +355条，总计6755条
2025-07-26 12:09:09,936 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计6755条数据
2025-07-26 12:09:09,952 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 2073 条 daily 数据
2025-07-26 12:09:09,955 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共2073条记录
2025-07-26 12:09:09,955 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 12:09:09,962 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=19.030, 前复权=19.030
2025-07-26 12:09:09,962 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 12:09:09,963 - Main - INFO - info:307 - ℹ️   日期: 20170103
2025-07-26 12:09:09,963 - Main - INFO - info:307 - ℹ️   当日收盘价C: 19.03
2025-07-26 12:09:09,963 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 19.03
2025-07-26 12:09:09,963 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 12:09:09,963 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 2073 >= 5
2025-07-26 12:09:09,963 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 12:09:09,963 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 12:09:09,963 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 12:09:09,966 - Main - INFO - info:307 - ℹ️ 数据无变化，将添加时间戳: day_0_000617_20170101-20250720_来源互联网（202507261209）.txt
2025-07-26 12:09:09,979 - Main - INFO - info:307 - ℹ️ 成功保存000617数据到: H:/MPV1.17/T0002/signals\day_0_000617_20170101-20250720_来源互联网（202507261209）.txt
2025-07-26 12:09:09,979 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 12:09:09,979 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 12:09:09,979 - Main - INFO - info:307 - ℹ️ 互联网数据下载完成: 1/1 成功 (100.0%)
2025-07-26 12:09:09,979 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网日线数据下载
2025-07-26 12:09:09,980 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-26 12:09:09,980 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-26 12:09:09,980 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-26 12:09:09,980 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-26 12:09:09,980 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-26 12:09:09,980 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-26 12:09:09,980 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-26 12:09:09,981 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-26 12:09:09,981 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250726
2025-07-26 12:09:09,981 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-26 12:09:09,981 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-26 12:09:09,981 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-26 12:09:09,981 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-26 12:09:09,982 - Main - INFO - info:307 - ℹ️ 🔄 开始分钟级数据增量下载: 000617
2025-07-26 12:09:09,982 - Main - INFO - info:307 - ℹ️ 找到现有文件: min_0_000617_20250101-20250725_来源互联网.txt
2025-07-26 12:09:09,982 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250101 - 20250725
2025-07-26 12:09:09,984 - Main - INFO - info:307 - ℹ️ 获取最后一条记录: 时间=20250725 15:00, 前复权价=8.9
2025-07-26 12:09:09,985 - Main - INFO - info:307 - ℹ️ 检查 000617 在 20250725 的前复权价一致性
2025-07-26 12:09:09,985 - Main - INFO - info:307 - ℹ️ 🎯 严格使用配置的频率: 1 (不允许自动切换频率)
2025-07-26 12:09:09,985 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 12:09:09,985 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 12:09:09,985 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250725 - 20250725
2025-07-26 12:09:09,985 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:10,161 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:10,161 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计240条 -> 实际请求240条 (配置限制:800)
2025-07-26 12:09:10,161 - Main - INFO - info:307 - ℹ️ 📊 预计获取 240 条 1min 数据
2025-07-26 12:09:10,162 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=240
2025-07-26 12:09:10,210 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-26 12:09:10,212 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-26 12:09:10,212 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 12:09:10,213 - Main - INFO - info:307 - ℹ️ 前复权价对比: 存储=8.900, 当前=8.970, 差异=0.070000
2025-07-26 12:09:10,213 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价不一致，需要全量重新下载
2025-07-26 12:09:10,213 - Main - WARNING - warning:311 - ⚠️ ⚠️ 前复权价不一致，执行全量下载
2025-07-26 12:09:10,213 - Main - INFO - info:307 - ℹ️ 📥 执行分钟级数据全量下载: 000617
2025-07-26 12:09:10,213 - Main - INFO - info:307 - ℹ️ 尝试使用pytdx下载000617数据
2025-07-26 12:09:10,213 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-26 12:09:10,213 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250726
2025-07-26 12:09:10,213 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:10,385 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:10,385 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计49680条 -> 实际请求800条 (配置限制:800)
2025-07-26 12:09:10,385 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-26 12:09:10,385 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-26 12:09:10,438 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需48880条
2025-07-26 12:09:10,438 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:10,614 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:10,667 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-26 12:09:10,667 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:10,836 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:10,890 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-26 12:09:10,890 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:11,061 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:11,111 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-26 12:09:11,111 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:11,297 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:11,363 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-26 12:09:11,363 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:11,538 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:11,588 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-26 12:09:11,588 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:11,763 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:11,813 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-26 12:09:11,813 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:11,986 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:12,036 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-26 12:09:12,036 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:12,217 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:12,270 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-26 12:09:12,270 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:12,446 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:12,495 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-26 12:09:12,496 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:12,668 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:12,719 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-26 12:09:12,720 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:12,895 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:12,946 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-26 12:09:12,946 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:13,122 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:13,173 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-26 12:09:13,173 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:13,339 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:13,397 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-26 12:09:13,397 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:13,592 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:13,644 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-26 12:09:13,644 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:13,824 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:13,883 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-26 12:09:13,884 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:14,056 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:14,106 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-26 12:09:14,107 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:14,279 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:14,329 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-26 12:09:14,329 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:14,500 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:14,550 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-26 12:09:14,550 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:14,731 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:14,782 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-26 12:09:14,783 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:14,986 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:15,038 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-26 12:09:15,038 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:15,217 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:15,270 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-26 12:09:15,270 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:15,440 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:15,494 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-26 12:09:15,495 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:15,672 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:15,724 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-26 12:09:15,724 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:15,902 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:15,956 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-26 12:09:15,956 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:16,127 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:16,180 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-26 12:09:16,180 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:16,355 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:16,407 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-26 12:09:16,407 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:16,582 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:16,635 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-26 12:09:16,635 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:16,816 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:16,869 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-26 12:09:16,870 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:17,046 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:17,097 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-26 12:09:17,097 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-26 12:09:17,260 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-26 12:09:17,303 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-26 12:09:17,358 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 24000 条 1min 数据
2025-07-26 12:09:17,369 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共24000条记录
2025-07-26 12:09:17,369 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617数据
2025-07-26 12:09:17,442 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=6.290, 前复权=6.290
2025-07-26 12:09:17,446 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-26 12:09:17,446 - Main - INFO - info:307 - ℹ️   日期: 202503030000
2025-07-26 12:09:17,446 - Main - INFO - info:307 - ℹ️   当日收盘价C: 6.29
2025-07-26 12:09:17,447 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 6.29
2025-07-26 12:09:17,447 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-26 12:09:17,447 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 24000 >= 5
2025-07-26 12:09:17,447 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-26 12:09:17,448 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-26 12:09:17,448 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-26 12:09:17,521 - Main - INFO - info:307 - ℹ️ ✅ 全量下载完成: 1_0_000617_20250101-20250726_来源互联网.txt (1123291 字节)
2025-07-26 12:09:17,521 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-26 12:09:17,521 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-26 12:09:17,522 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-26 12:09:17,522 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-26 12:09:17,522 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-26 12:09:17,522 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-26 12:09:17,523 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-26 12:09:17,523 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-26 12:09:17,523 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-26 12:09:17,523 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-26 12:09:17,523 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: day_0_000617_20240701-20240731_来源互联网.txt
2025-07-26 12:09:17,540 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20200101-20250724.txt, 数据行数: 1337
2025-07-26 12:09:17,543 - Main - INFO - info:307 - ℹ️ 成功加载数据文件: day_0_000617_20240701-20240731_来源互联网.txt, 数据行数: 23
2025-07-26 12:09:17,544 - Main - INFO - info:307 - ℹ️ 数据对齐完成，共同日期数: 23
2025-07-26 12:09:17,546 - Main - INFO - info:307 - ℹ️ 价格差异计算完成，数据点数: 23
2025-07-26 12:09:17,549 - Main - INFO - info:307 - ℹ️ 股票 000617 数据比较完成
2025-07-26 12:09:17,550 - Main - INFO - info:307 - ℹ️ ✅ 000617 分析完成
2025-07-26 12:09:17,554 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250726_120917.txt
2025-07-26 12:09:17,554 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 1/1 成功 (100.0%)
2025-07-26 12:09:17,554 - Main - INFO - info:307 - ℹ️ 任务执行成功: 前复权数据比较分析
2025-07-26 12:09:17,554 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 100.0%
2025-07-26 12:09:17,555 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-26 12:09:17,555 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-26 12:09:17,555 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-26 12:09:17,555 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-26 12:09:17,555 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-26 12:09:17,555 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-26 12:09:17,555 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
