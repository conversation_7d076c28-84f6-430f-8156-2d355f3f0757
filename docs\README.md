# MythQuant - 股票数据处理系统

## 🎯 项目概述

MythQuant是一个专业的股票数据处理系统，专注于从通达信数据源读取股票分钟级数据，并进行前复权处理和技术指标计算。

## 📦 依赖管理

### 核心依赖库

**必需依赖（9个）**：
- **pandas** (>=1.5.0) - 数据处理和分析
- **numpy** (>=1.24.0) - 数值计算
- **mootdx** (>=1.2.0) - 通达信数据读取
- **pytdx** (>=1.72) - 通达信数据格式解析
- **openpyxl** (>=3.0.0) - Excel文件读写(.xlsx)
- **xlrd** (>=2.0.0) - Excel文件读取(.xls)
- **requests** (>=2.28.0) - HTTP网络请求
- **tqdm** (>=4.64.0) - 进度条显示
- **retry** (>=0.9.2) - 重试机制

**可选依赖（2个）**：
- **terminaltables** (>=3.1.10) - 表格显示
- **tushare** (>=1.2.89) - 股票数据接口

### 🚀 快速安装

#### 方法1：自动安装（推荐）
```bash
# 运行自动安装脚本
python install_dependencies.py
```

#### 方法2：使用requirements.txt
```bash
# 安装所有依赖
pip install -r requirements.txt

# 或分步安装
pip install pandas>=1.5.0 numpy>=1.24.0
pip install mootdx>=1.2.0 pytdx>=1.72
pip install openpyxl>=3.0.0 xlrd>=2.0.0
pip install requests>=2.28.0 tqdm>=4.64.0 retry>=0.9.2
```

#### 方法3：使用conda环境
```bash
# 创建新环境
conda env create -f environment.yml

# 激活环境
conda activate mythquant
```

### 🔍 依赖检查

```bash
# 检查依赖状态
python dependency_check.py

# 显示完整包列表
python dependency_check.py --full
```

## ✨ 核心功能

### 📊 数据处理能力
- **分钟级数据处理**: 支持1分钟、5分钟等不同时间周期
- **前复权处理**: 使用pytdx兼容的前复权算法，确保价格连续性
- **技术指标计算**: 模拟Level-2市场数据，计算主买主卖指标
- **多时间框架**: 支持分钟、日线、周线数据重采样

### 🔧 前复权方法

系统使用**pytdx兼容的前复权方法**，具有以下特点：

- ✅ **实用性强**: 价格调整幅度合理，避免过度调整
- ✅ **兼容性好**: 与pytdx库结果一致
- ✅ **性能优秀**: 只处理最近分红事件，计算快速
- ✅ **逻辑清晰**: 对分红应用87%折扣，避免历史价格失真

#### 配置说明

在`user_config.py`中配置前复权参数：

```python
# 前复权配置 - 只保留pytdx方法
adjustment_config = {
    'pytdx_discount_rate': 0.87,  # pytdx分红折扣率 (0.87 = 87%折扣)
    'min_dividend_threshold': 0.01,  # 最小分红阈值
}
```

## 🚀 快速开始

### 1. 环境准备

```bash
# 自动安装所有依赖
python install_dependencies.py

# 或手动安装
pip install pandas numpy mootdx pytdx openpyxl xlrd requests tqdm retry
```

### 2. 配置文件

修改`user_config.py`中的通达信路径：

```python
tdx = {
    'tdx_path': 'H:/MPV1.17',  # 指定通达信目录
    'tdx_test': 'D:/TDXdata/TDX_ROOT',  # 测试数据目录
    # ... 其他配置
}
```

### 3. 运行处理

```python
from main_v20230219_optimized import StockDataProcessor
import user_config as ucfg
import datetime

# 创建处理器
processor = StockDataProcessor(ucfg.tdx['tdx_path'], ucfg.tdx['tdx_test'])

# 处理分钟数据
start_time = datetime.datetime(2025, 6, 25, 9, 30)
end_time = datetime.datetime(2025, 6, 25, 10, 0)

df_minute = processor.load_and_process_minute_data(
    'sz000617', start_time, end_time
)

print(f"前复权因子: {df_minute['adj'].iloc[0]:.6f}")
print(f"调整后价格: {df_minute['close'].iloc[0]:.4f}")
```

## 📁 项目结构

```
MythQuant/
├── main_v20230219_optimized.py  # 主处理程序
├── user_config.py               # 用户配置文件
├── func_Util.py                 # 工具函数
├── func_Tdx.py                  # 通达信数据读取
├── README.md                    # 项目说明
├── requirements.txt             # pip依赖文件
├── environment.yml              # conda环境文件
├── install_dependencies.py     # 自动安装脚本
├── dependency_check.py          # 依赖检查脚本
├── test_pytdx_only.py          # 功能测试脚本
└── ...
```

## 🔍 技术细节

### 前复权算法

使用pytdx兼容的前复权算法：

1. **数据筛选**: 只使用最近的分红事件
2. **分红调整**: 对分红金额应用87%折扣
3. **复权计算**: 使用标准复权公式

```python
# 核心算法
factor = (10 - adjusted_dividend + peigu * peigujia) / (10 + peigu + songgu)
```

### 技术指标

- **路径总长**: 价格变化的总幅度
- **主买主卖**: 基于价格变化特征推算的资金流向
- **资金效率**: 成交量与价格变化的关系
- **买卖差**: 主买与主卖的差值

## 📈 使用示例

### 基本数据处理

```python
# 处理单只股票
df = processor.load_and_process_minute_data('sz000617', start_time, end_time)

# 查看前复权结果
print(f"原始价格: {df['original_close'].iloc[0]:.4f}")
print(f"复权价格: {df['close'].iloc[0]:.4f}")
print(f"复权因子: {df['adj'].iloc[0]:.6f}")
```

### 批量处理

```python
# 运行主程序进行批量处理
if __name__ == "__main__":
    from main_v20230219_optimized import main
    main()
```

### 依赖检查和测试

```python
# 检查依赖状态
python dependency_check.py

# 运行功能测试
python test_pytdx_only.py
```

## ⚠️ 注意事项

1. **数据源**: 需要有效的通达信数据目录
2. **权限**: 确保对数据目录有读取权限
3. **内存**: 处理大量数据时注意内存使用
4. **时间**: 分钟数据处理需要较长时间
5. **依赖**: 确保所有必需依赖库已正确安装

## 🔧 配置参数

### 核心配置

- `pytdx_discount_rate`: 分红折扣率，默认0.87
- `min_dividend_threshold`: 最小分红阈值，默认0.01

### 路径配置

- `tdx_path`: 通达信主目录
- `tdx_test`: 测试数据目录
- `目标股票代码`: Excel文件路径（可选）

## 📊 性能优化

- 使用多线程处理提高效率
- 只处理最近分红事件减少计算量
- 缓存机制避免重复读取
- 内存管理优化大数据处理

## 🛠️ 依赖管理最佳实践

### 虚拟环境管理

```bash
# 创建虚拟环境
python -m venv mythquant_env

# 激活环境 (Windows)
mythquant_env\Scripts\activate

# 激活环境 (Linux/Mac)
source mythquant_env/bin/activate

# 安装依赖
pip install -r requirements.txt
```

### 依赖更新

```bash
# 检查过时的包
pip list --outdated

# 更新特定包
pip install --upgrade pandas numpy

# 生成新的requirements.txt
pip freeze > requirements.txt
```

### 环境导出

```bash
# 导出pip环境
pip freeze > requirements.txt

# 导出conda环境
conda env export > environment.yml
```

## 🤝 贡献

欢迎提交Issue和Pull Request来改进项目。

## �� 许可证

本项目遵循MIT许可证。
