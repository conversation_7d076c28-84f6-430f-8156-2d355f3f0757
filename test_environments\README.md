# 测试环境目录

## 目录结构

### 标准测试环境
- `minute_data_tests/` - 1分钟数据专项测试环境
- `unit_tests/` - 单元测试环境
- `integration_tests/` - 集成测试环境
- `performance_tests/` - 性能测试环境
- `regression_tests/` - 回归测试环境
- `data_quality_tests/` - 数据质量测试环境

### 数据源测试
- `data_sources/tdx/` - TDX数据源测试数据

### 遗留脚本
- `legacy_scripts/` - 整理后的遗留测试脚本

## 使用说明

1. 使用 `test_config.py` 进行统一配置管理
2. 运行 `comprehensive_test_with_proper_config.py` 进行完整测试
3. 各专项测试环境独立运行，互不干扰

## 维护指南

- 新增测试数据放入对应的测试环境目录
- 遗留脚本经过验证后可以删除或迁移到标准测试中
- 定期清理过期的测试结果和报告
