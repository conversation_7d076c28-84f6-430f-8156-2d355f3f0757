#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
FAQ知识库管理工具
用于添加、检索和维护FAQ条目
"""

import os
import re
from datetime import datetime
from typing import List, Dict, Optional

class FAQManager:
    """FAQ知识库管理器"""
    
    def __init__(self, faq_file_path: str = "knowledge_base/faq_database.md"):
        self.faq_file_path = faq_file_path
        self.ensure_knowledge_base_dir()
    
    def ensure_knowledge_base_dir(self):
        """确保知识库目录存在"""
        os.makedirs(os.path.dirname(self.faq_file_path), exist_ok=True)
    
    def add_faq_entry(self, question: str, answer: str, tags: List[str], 
                     domain: str = "", module: str = "", tech_stack: str = ""):
        """
        添加新的FAQ条目
        
        Args:
            question: 问题描述
            answer: 答案内容
            tags: 标签列表
            domain: 领域
            module: 模块
            tech_stack: 技术栈
        """
        try:
            # 读取现有内容
            if os.path.exists(self.faq_file_path):
                with open(self.faq_file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
            else:
                content = self._get_initial_template()
            
            # 生成新条目
            current_time = datetime.now().strftime('%Y-%m-%d')
            tag_str = f"[{domain}] [{module}] [{tech_stack}]" if all([domain, module, tech_stack]) else str(tags)
            
            # 获取下一个问题编号
            next_q_num = self._get_next_question_number(content)
            
            new_entry = f"""
### Q{next_q_num}: {question}
**时间**: {current_time}  
**标签**: {tag_str}

**问题描述**:
{self._format_question_content(question)}

**解决方案**:
{self._format_answer_content(answer)}

---
"""
            
            # 插入新条目（在"## 检索索引"之前）
            index_pos = content.find("## 检索索引")
            if index_pos != -1:
                content = content[:index_pos] + new_entry + "\n" + content[index_pos:]
            else:
                content += new_entry
            
            # 更新检索索引
            content = self._update_search_index(content, next_q_num, current_time, domain, module, tech_stack)
            
            # 保存文件（添加时间戳后缀）
            timestamp = datetime.now().strftime('%Y%m%d%H%M')
            base_name = self.faq_file_path.rsplit('.', 1)[0]
            extension = self.faq_file_path.rsplit('.', 1)[1] if '.' in self.faq_file_path else ''
            timestamped_path = f"{base_name}（{timestamp}）.{extension}" if extension else f"{base_name}（{timestamp}）"

            with open(timestamped_path, 'w', encoding='utf-8') as f:
                f.write(content)

            # 更新内部路径引用
            self.faq_file_path = timestamped_path

            print(f"✅ 成功添加FAQ条目 Q{next_q_num}: {question[:50]}...")
            print(f"📁 文件已保存为: {timestamped_path}")
            return True
            
        except Exception as e:
            print(f"❌ 添加FAQ条目失败: {e}")
            return False
    
    def search_faq(self, keyword: str = "", tag: str = "", date: str = "") -> List[Dict]:
        """
        搜索FAQ条目
        
        Args:
            keyword: 关键词
            tag: 标签
            date: 日期
            
        Returns:
            匹配的FAQ条目列表
        """
        try:
            if not os.path.exists(self.faq_file_path):
                return []
            
            with open(self.faq_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取所有FAQ条目
            entries = re.findall(r'### (Q\d+): (.+?)\n\*\*时间\*\*: (.+?)\n\*\*标签\*\*: (.+?)\n\n(.+?)(?=\n---|\n## |$)', 
                               content, re.DOTALL)
            
            results = []
            for entry in entries:
                q_num, title, time, tags, content_part = entry
                
                # 应用搜索条件
                if keyword and keyword.lower() not in (title + content_part).lower():
                    continue
                if tag and tag not in tags:
                    continue
                if date and date not in time:
                    continue
                
                results.append({
                    'question_num': q_num,
                    'title': title,
                    'time': time,
                    'tags': tags,
                    'content': content_part[:200] + "..." if len(content_part) > 200 else content_part
                })
            
            return results
            
        except Exception as e:
            print(f"❌ 搜索FAQ失败: {e}")
            return []
    
    def _get_next_question_number(self, content: str) -> int:
        """获取下一个问题编号"""
        q_numbers = re.findall(r'### Q(\d+):', content)
        if q_numbers:
            return max(int(num) for num in q_numbers) + 1
        return 1
    
    def _format_question_content(self, question: str) -> str:
        """格式化问题内容"""
        # 这里可以根据需要进行格式化
        return question
    
    def _format_answer_content(self, answer: str) -> str:
        """格式化答案内容"""
        # 如果答案太长，提取关键部分
        if len(answer) > 2000:
            # 提取关键信息（可以根据需要优化）
            lines = answer.split('\n')
            key_lines = []
            for line in lines:
                if any(keyword in line.lower() for keyword in ['解决', '修复', '方案', '问题', '原因', '效果']):
                    key_lines.append(line)
            
            if key_lines:
                return '\n'.join(key_lines[:10])  # 最多保留10行关键信息
        
        return answer
    
    def _update_search_index(self, content: str, q_num: int, date: str, 
                           domain: str, module: str, tech_stack: str) -> str:
        """更新检索索引"""
        # 这里可以实现索引更新逻辑
        # 为简化，暂时返回原内容
        return content
    
    def _get_initial_template(self) -> str:
        """获取初始模板"""
        return """# MythQuant项目FAQ知识库

## 使用说明
- **时间格式**: YYYY-MM-DD
- **标签格式**: [领域] [模块] [技术栈]
- **检索方式**: 使用Ctrl+F搜索关键词、时间、标签

---

## FAQ条目

## 检索索引

### 按时间检索

### 按领域检索

### 按模块检索

### 按技术栈检索

---

## 待补充问题

---

*最后更新: {date}*  
*维护者: AI Assistant*
""".format(date=datetime.now().strftime('%Y-%m-%d'))

def demonstrate_faq_usage():
    """演示FAQ管理器使用"""
    print("🚀 FAQ知识库管理器演示")
    print("=" * 50)
    
    faq_manager = FAQManager()
    
    # 演示搜索功能
    print("\n🔍 搜索现有FAQ条目:")
    results = faq_manager.search_faq(keyword="正则表达式")
    for result in results:
        print(f"  {result['question_num']}: {result['title']}")
        print(f"    时间: {result['time']}, 标签: {result['tags']}")
    
    print(f"\n📋 FAQ知识库位置: {faq_manager.faq_file_path}")
    print("✅ FAQ管理器初始化完成")

if __name__ == '__main__':
    demonstrate_faq_usage()
