2025-08-03 22:14:03,162 - TaskManager - INFO - 股票数据处理器初始化完成: ./test_tdx
2025-08-03 22:14:03,162 - TaskManager - INFO - 🔄 开始加载和处理分钟数据: sz000617
2025-08-03 22:14:03,162 - TaskManager - INFO - 📅 时间范围: 2025-08-01 09:30:00 至 2025-08-03 15:00:00
2025-08-03 22:14:03,163 - TaskManager - INFO - 🌐 尝试从pytdx获取000617的分钟数据
2025-08-03 22:14:04,141 - TaskManager - INFO - ✅ pytdx数据获取成功: 240 条记录
2025-08-03 22:14:04,141 - TaskManager - INFO - 📊 原始数据加载成功: 240 条记录
2025-08-03 22:14:04,141 - TaskManager - INFO - 🔧 开始预处理分钟数据
2025-08-03 22:14:04,145 - TaskManager - INFO - ✅ 数据预处理完成: 240 条有效记录
2025-08-03 22:14:04,145 - TaskManager - INFO - 📊 开始计算前复权价格
2025-08-03 22:14:04,148 - TaskManager - INFO - ✅ 前复权价格计算完成（简化方法）
2025-08-03 22:14:04,148 - TaskManager - INFO - 🧮 开始计算L2指标
2025-08-03 22:14:04,177 - TaskManager - INFO - ✅ 新架构L2指标计算成功
2025-08-03 22:14:04,177 - TaskManager - INFO - 📋 格式化输出数据
2025-08-03 22:14:04,179 - TaskManager - INFO - ✅ 数据格式化完成: 240 条记录
2025-08-03 22:14:04,179 - TaskManager - INFO - ✅ 分钟数据处理完成: 240 条记录
2025-08-03 22:14:04,180 - TaskManager - INFO - 股票数据处理器初始化完成: ./test_tdx
2025-08-03 22:14:04,182 - TaskManager - INFO - 执行分钟级数据生成任务（DDD架构）
2025-08-03 22:14:04,183 - TaskManager - INFO - 🚀 开始生成分钟数据 (输出到: H:/MPV1.17/T0002/signals)
2025-08-03 22:14:04,183 - TaskManager - INFO - 🔄 开始加载和处理分钟数据: sz000617
2025-08-03 22:14:04,184 - TaskManager - INFO - 📅 时间范围: 2025-08-01 09:30:00 至 2025-08-03 15:00:00
2025-08-03 22:14:04,184 - TaskManager - INFO - 🌐 尝试从pytdx获取000617的分钟数据
2025-08-03 22:14:05,097 - TaskManager - INFO - ✅ pytdx数据获取成功: 240 条记录
2025-08-03 22:14:05,098 - TaskManager - INFO - 📊 原始数据加载成功: 240 条记录
2025-08-03 22:14:05,098 - TaskManager - INFO - 🔧 开始预处理分钟数据
2025-08-03 22:14:05,101 - TaskManager - INFO - ✅ 数据预处理完成: 240 条有效记录
2025-08-03 22:14:05,101 - TaskManager - INFO - 📊 开始计算前复权价格
2025-08-03 22:14:05,101 - TaskManager - INFO - ✅ 前复权价格计算完成（简化方法）
2025-08-03 22:14:05,102 - TaskManager - INFO - 🧮 开始计算L2指标
2025-08-03 22:14:05,120 - TaskManager - INFO - ✅ 新架构L2指标计算成功
2025-08-03 22:14:05,121 - TaskManager - INFO - 📋 格式化输出数据
2025-08-03 22:14:05,122 - TaskManager - INFO - ✅ 数据格式化完成: 240 条记录
2025-08-03 22:14:05,122 - TaskManager - INFO - ✅ 分钟数据处理完成: 240 条记录
2025-08-03 22:14:05,134 - TaskManager - INFO - 成功写入分钟级别txt文件: H:/MPV1.17/T0002/signals\1min_0_000617_20250801-20250803.txt, 数据行数: 240
2025-08-03 22:14:05,134 - TaskManager - INFO - 
分钟级别txt文件生成完成汇总:
2025-08-03 22:14:05,134 - TaskManager - INFO - 📊 成功生成: 1 个文件
2025-08-03 22:14:05,134 - TaskManager - INFO - ❌ 处理失败: 0 个股票
2025-08-03 22:14:05,135 - TaskManager - INFO - 📈 成功率: 100.0%
2025-08-03 22:14:05,135 - TaskManager - INFO - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-08-03 22:14:05,135 - TaskManager - INFO - ✅ 分钟数据生成完成，耗时: 0.95 秒
2025-08-03 22:14:05,136 - TaskManager - INFO - 成功写入分钟级别txt文件: test_1min_output.txt, 数据行数: 1
2025-08-03 22:14:05,484 - TaskManager - INFO - 股票数据处理器初始化完成: ./test_tdx
2025-08-03 22:14:05,484 - TaskManager - INFO - 正在加载任务配置...
2025-08-03 22:14:05,484 - TaskManager - INFO - 加载了 1 个任务配置
2025-08-03 22:14:05,485 - TaskManager - INFO - 执行分钟级数据生成任务（DDD架构）
2025-08-03 22:14:05,485 - TaskManager - INFO - 🚀 开始生成分钟数据 (输出到: H:/MPV1.17/T0002/signals)
2025-08-03 22:14:05,485 - TaskManager - INFO - 🔄 开始加载和处理分钟数据: sz000617
2025-08-03 22:14:05,485 - TaskManager - INFO - 📅 时间范围: 2025-08-01 09:30:00 至 2025-08-03 15:00:00
2025-08-03 22:14:05,485 - TaskManager - INFO - 🌐 尝试从pytdx获取000617的分钟数据
2025-08-03 22:14:06,400 - TaskManager - INFO - ✅ pytdx数据获取成功: 240 条记录
2025-08-03 22:14:06,400 - TaskManager - INFO - 📊 原始数据加载成功: 240 条记录
2025-08-03 22:14:06,400 - TaskManager - INFO - 🔧 开始预处理分钟数据
2025-08-03 22:14:06,403 - TaskManager - INFO - ✅ 数据预处理完成: 240 条有效记录
2025-08-03 22:14:06,403 - TaskManager - INFO - 📊 开始计算前复权价格
2025-08-03 22:14:06,403 - TaskManager - INFO - ✅ 前复权价格计算完成（简化方法）
2025-08-03 22:14:06,404 - TaskManager - INFO - 🧮 开始计算L2指标
2025-08-03 22:14:06,425 - TaskManager - INFO - ✅ 新架构L2指标计算成功
2025-08-03 22:14:06,425 - TaskManager - INFO - 📋 格式化输出数据
2025-08-03 22:14:06,427 - TaskManager - INFO - ✅ 数据格式化完成: 240 条记录
2025-08-03 22:14:06,427 - TaskManager - INFO - ✅ 分钟数据处理完成: 240 条记录
2025-08-03 22:14:06,438 - TaskManager - INFO - 成功写入分钟级别txt文件: H:/MPV1.17/T0002/signals\1min_0_000617_20250801-20250803.txt, 数据行数: 240
2025-08-03 22:14:06,438 - TaskManager - INFO - 
分钟级别txt文件生成完成汇总:
2025-08-03 22:14:06,438 - TaskManager - INFO - 📊 成功生成: 1 个文件
2025-08-03 22:14:06,438 - TaskManager - INFO - ❌ 处理失败: 0 个股票
2025-08-03 22:14:06,438 - TaskManager - INFO - 📈 成功率: 100.0%
2025-08-03 22:14:06,438 - TaskManager - INFO - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-08-03 22:14:06,439 - TaskManager - INFO - ✅ 分钟数据生成完成，耗时: 0.95 秒
