#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDD架构 - 异步IO处理器

提供异步文件IO处理功能
"""

import asyncio
from typing import List, Dict, Any, Optional, Union
from pathlib import Path
import pandas as pd
from src.mythquant.shared.logging import get_smart_logger


class AsyncIOProcessor:
    """异步IO处理器"""
    
    def __init__(self):
        self.logger = get_smart_logger()
    
    async def read_file_async(self, file_path: Union[str, Path], encoding: str = 'utf-8') -> str:
        """异步读取文件（使用线程池模拟异步）"""
        try:
            def _read_file():
                with open(file_path, 'r', encoding=encoding) as f:
                    return f.read()

            loop = asyncio.get_event_loop()
            content = await loop.run_in_executor(None, _read_file)

            self.logger.debug(f"异步读取文件成功: {file_path}")
            return content

        except Exception as e:
            self.logger.error(f"异步读取文件失败: {file_path}, 错误: {e}")
            raise
    
    async def write_file_async(self,
                              file_path: Union[str, Path],
                              content: str,
                              encoding: str = 'utf-8') -> bool:
        """异步写入文件（使用线程池模拟异步）"""
        try:
            def _write_file():
                # 确保目录存在
                Path(file_path).parent.mkdir(parents=True, exist_ok=True)
                with open(file_path, 'w', encoding=encoding) as f:
                    f.write(content)
                return True

            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(None, _write_file)

            self.logger.debug(f"异步写入文件成功: {file_path}")
            return result

        except Exception as e:
            self.logger.error(f"异步写入文件失败: {file_path}, 错误: {e}")
            return False
    
    async def read_multiple_files_async(self, 
                                       file_paths: List[Union[str, Path]], 
                                       encoding: str = 'utf-8') -> Dict[str, str]:
        """异步读取多个文件"""
        tasks = []
        
        for file_path in file_paths:
            task = self.read_file_async(file_path, encoding)
            tasks.append((str(file_path), task))
        
        results = {}
        
        for file_path, task in tasks:
            try:
                content = await task
                results[file_path] = content
            except Exception as e:
                self.logger.error(f"读取文件失败: {file_path}, 错误: {e}")
                results[file_path] = None
        
        successful_count = sum(1 for v in results.values() if v is not None)
        self.logger.info(f"异步读取多文件完成: {successful_count}/{len(file_paths)} 成功")
        
        return results
    
    async def write_multiple_files_async(self, 
                                        file_data: Dict[Union[str, Path], str], 
                                        encoding: str = 'utf-8') -> Dict[str, bool]:
        """异步写入多个文件"""
        tasks = []
        
        for file_path, content in file_data.items():
            task = self.write_file_async(file_path, content, encoding)
            tasks.append((str(file_path), task))
        
        results = {}
        
        for file_path, task in tasks:
            try:
                success = await task
                results[file_path] = success
            except Exception as e:
                self.logger.error(f"写入文件失败: {file_path}, 错误: {e}")
                results[file_path] = False
        
        successful_count = sum(1 for v in results.values() if v)
        self.logger.info(f"异步写入多文件完成: {successful_count}/{len(file_data)} 成功")
        
        return results
    
    async def process_dataframe_async(self, 
                                     df: pd.DataFrame, 
                                     output_path: Union[str, Path],
                                     format_type: str = 'csv') -> bool:
        """异步处理DataFrame并保存"""
        try:
            # 确保目录存在
            Path(output_path).parent.mkdir(parents=True, exist_ok=True)
            
            # 根据格式类型保存
            if format_type.lower() == 'csv':
                # 使用同步方法保存CSV（pandas不支持异步）
                await asyncio.get_event_loop().run_in_executor(
                    None, 
                    lambda: df.to_csv(output_path, index=False, encoding='utf-8')
                )
            elif format_type.lower() == 'parquet':
                await asyncio.get_event_loop().run_in_executor(
                    None, 
                    lambda: df.to_parquet(output_path, index=False)
                )
            else:
                raise ValueError(f"不支持的格式类型: {format_type}")
            
            self.logger.info(f"异步保存DataFrame成功: {output_path} ({format_type})")
            return True
            
        except Exception as e:
            self.logger.error(f"异步保存DataFrame失败: {output_path}, 错误: {e}")
            return False
    
    def run_async_task(self, coro):
        """运行异步任务（同步接口）"""
        try:
            loop = asyncio.get_event_loop()
        except RuntimeError:
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)

        return loop.run_until_complete(coro)

    def cleanup(self):
        """清理资源"""
        self.logger.info("AsyncIOProcessor资源清理完成")
