---
title: "文档命名规范改进计划"
version: "v1.0"
date: "2025-08-02"
author: "MythQuant 文档团队"
status: "发布"
category: "管理"
tags: ["文档标准", "命名规范", "改进计划"]
last_updated: "2025-08-02"
---

# 文档命名规范改进计划

## 概述

基于文档结构评价结果，制定具体的文档命名规范改进计划，将现有文档名称标准化。

## 🎯 改进目标

1. **统一命名风格**: 使用英文小写+下划线格式
2. **消除特殊字符**: 移除中文字符和特殊符号
3. **简化文件名**: 控制文件名长度在合理范围内
4. **增强可读性**: 使文件名更具描述性和专业性

## 📋 重命名映射表

### 根目录文档

| 当前文件名 | 建议文件名 | 改进说明 |
|-----------|-----------|----------|
| `AI_model_identification_request__2025-07-24T16-49-21.md` | `ai_model_identification_request_20250724.md` | 移除特殊字符，简化时间格式 |
| `PYTDX_使用指南.md` | `pytdx_usage_guide.md` | 中文转英文，小写化 |
| `详细计算显示控制说明.md` | `calculation_display_control_guide.md` | 中文转英文 |

### guides/ 目录

| 当前文件名 | 建议文件名 | 改进说明 |
|-----------|-----------|----------|
| `运行指南.md` | `operation_guide.md` | 中文转英文 |
| `精度配置使用指南.md` | `precision_config_guide.md` | 中文转英文，简化 |
| `扩展GBBQ缓存系统实施指南.md` | `gbbq_cache_implementation_guide.md` | 中文转英文 |
| `集成gbbq优化指南.md` | `gbbq_optimization_guide.md` | 中文转英文 |

### reports/ 目录

| 当前文件名 | 建议文件名 | 改进说明 |
|-----------|-----------|----------|
| `数据缺失透明化改进报告.md` | `data_transparency_improvement_report.md` | 中文转英文 |
| `文件整理完成报告.md` | `file_organization_completion_report.md` | 中文转英文 |
| `模块化拆分后续阶段规划.md` | `modularization_next_phase_plan.md` | 中文转英文 |
| `模块化重构集成报告.md` | `modularization_refactoring_report.md` | 中文转英文 |
| `第三阶段UI模块拆分报告.md` | `phase3_ui_module_split_report.md` | 中文转英文，简化 |
| `第二阶段文件IO模块拆分报告.md` | `phase2_fileio_module_split_report.md` | 中文转英文，简化 |
| `规则转换完成报告.md` | `rules_conversion_completion_report.md` | 中文转英文 |
| `规则转换实施完成报告.md` | `rules_conversion_implementation_report.md` | 中文转英文 |

### legacy/ 目录

| 当前文件名 | 建议文件名 | 改进说明 |
|-----------|-----------|----------|
| `faq_manager（202507262151）.py` | `faq_manager_20250726.py` | 移除中文括号，简化时间 |
| `knowledge_graph（202507262151）.py` | `knowledge_graph_20250726.py` | 移除中文括号，简化时间 |
| `problem_classifier（202507262151）.py` | `problem_classifier_20250726.py` | 移除中文括号，简化时间 |
| `session_summarizer（202507262151）.py` | `session_summarizer_20250726.py` | 移除中文括号，简化时间 |
| `solution_templates（202507262151）.py` | `solution_templates_20250726.py` | 移除中文括号，简化时间 |

## 🚀 实施计划

### 第一阶段：准备工作
1. ✅ 创建备份目录
2. ✅ 生成重命名脚本
3. ✅ 验证重命名映射

### 第二阶段：执行重命名
1. 🔄 批量重命名文件
2. 🔄 更新内部链接引用
3. 🔄 验证重命名结果

### 第三阶段：后续处理
1. ⏳ 更新文档索引
2. ⏳ 修复断开的链接
3. ⏳ 生成改进报告

## 📝 重命名脚本

```python
import os
import shutil
from pathlib import Path
from datetime import datetime

# 重命名映射
RENAME_MAP = {
    # 根目录
    "docs/AI_model_identification_request__2025-07-24T16-49-21.md": 
        "docs/ai_model_identification_request_20250724.md",
    "docs/PYTDX_使用指南.md": 
        "docs/pytdx_usage_guide.md",
    "docs/详细计算显示控制说明.md": 
        "docs/calculation_display_control_guide.md",
    
    # guides目录
    "docs/guides/运行指南.md": 
        "docs/guides/operation_guide.md",
    "docs/guides/精度配置使用指南.md": 
        "docs/guides/precision_config_guide.md",
    "docs/guides/扩展GBBQ缓存系统实施指南.md": 
        "docs/guides/gbbq_cache_implementation_guide.md",
    "docs/guides/集成gbbq优化指南.md": 
        "docs/guides/gbbq_optimization_guide.md",
    
    # reports目录
    "docs/reports/数据缺失透明化改进报告.md": 
        "docs/reports/data_transparency_improvement_report.md",
    "docs/reports/文件整理完成报告.md": 
        "docs/reports/file_organization_completion_report.md",
    "docs/reports/模块化拆分后续阶段规划.md": 
        "docs/reports/modularization_next_phase_plan.md",
    "docs/reports/模块化重构集成报告.md": 
        "docs/reports/modularization_refactoring_report.md",
    "docs/reports/第三阶段UI模块拆分报告.md": 
        "docs/reports/phase3_ui_module_split_report.md",
    "docs/reports/第二阶段文件IO模块拆分报告.md": 
        "docs/reports/phase2_fileio_module_split_report.md",
    "docs/reports/规则转换完成报告.md": 
        "docs/reports/rules_conversion_completion_report.md",
    "docs/reports/规则转换实施完成报告.md": 
        "docs/reports/rules_conversion_implementation_report.md",
}

def execute_rename():
    """执行重命名操作"""
    # 创建备份
    backup_dir = Path("docs_rename_backup")
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    backup_path = backup_dir / f"backup_{timestamp}"
    backup_path.mkdir(parents=True, exist_ok=True)
    
    success_count = 0
    error_count = 0
    
    for old_path, new_path in RENAME_MAP.items():
        try:
            old_file = Path(old_path)
            new_file = Path(new_path)
            
            if old_file.exists():
                # 备份原文件
                shutil.copy2(old_file, backup_path / old_file.name)
                
                # 确保目标目录存在
                new_file.parent.mkdir(parents=True, exist_ok=True)
                
                # 执行重命名
                shutil.move(str(old_file), str(new_file))
                print(f"✅ {old_file.name} → {new_file.name}")
                success_count += 1
            else:
                print(f"⚠️ 文件不存在: {old_path}")
        
        except Exception as e:
            print(f"❌ 重命名失败 {old_path}: {e}")
            error_count += 1
    
    print(f"\n📊 重命名统计:")
    print(f"   成功: {success_count}")
    print(f"   失败: {error_count}")
    print(f"💾 备份位置: {backup_path}")

if __name__ == "__main__":
    execute_rename()
```

## 🔗 链接更新计划

重命名后需要更新的内部链接：

### 主要索引文件
- `docs/DOCUMENTATION_INDEX.md` - 更新所有文档链接
- `docs/README.md` - 更新相关链接
- 各目录的 `README.md` - 更新子文档链接

### 交叉引用文件
- 技术文档中的相互引用
- 报告文档中的参考链接
- 知识库中的相关链接

## ✅ 验证清单

### 重命名验证
- [ ] 所有目标文件已创建
- [ ] 原文件已删除或移动
- [ ] 文件内容完整无损
- [ ] 文件权限正确

### 链接验证
- [ ] 内部链接正常工作
- [ ] 图片引用正确显示
- [ ] 交叉引用有效
- [ ] 索引文件更新完成

### 功能验证
- [ ] 文档可正常访问
- [ ] 搜索功能正常
- [ ] 导航结构完整
- [ ] 用户体验良好

## 📊 预期效果

### 改进指标
- **命名规范性**: 从60% → 95%
- **可读性**: 从70% → 90%
- **专业性**: 从75% → 95%
- **维护性**: 从65% → 85%

### 用户体验提升
- 文件名更易理解和记忆
- 搜索和定位更加便捷
- 整体专业形象提升
- 维护成本降低

## 🔄 后续维护

### 持续改进
1. 定期检查新增文档的命名规范
2. 建立命名规范检查机制
3. 培训团队成员遵循标准
4. 收集用户反馈持续优化

### 监控机制
- 每月进行命名规范审查
- 自动化检查工具运行
- 用户反馈收集和处理
- 规范更新和发布

---

**执行时间**: 2025-08-02  
**预计完成**: 2025-08-02  
**负责人**: MythQuant 文档团队  
**审核人**: 项目管理团队
