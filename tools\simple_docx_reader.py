#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
简单DOCX读取工具

使用标准库读取.docx文件的基本内容（不依赖python-docx）
"""

import zipfile
import xml.etree.ElementTree as ET
from pathlib import Path
import re
import sys


class SimpleDocxReader:
    """简单DOCX读取器"""
    
    def __init__(self):
        # Word文档的XML命名空间
        self.namespaces = {
            'w': 'http://schemas.openxmlformats.org/wordprocessingml/2006/main',
            'r': 'http://schemas.openxmlformats.org/officeDocument/2006/relationships'
        }
    
    def read_docx_text(self, file_path: str) -> str:
        """读取DOCX文件的纯文本内容"""
        try:
            with zipfile.ZipFile(file_path, 'r') as docx_zip:
                # 读取主文档内容
                document_xml = docx_zip.read('word/document.xml')
                
                # 解析XML
                root = ET.fromstring(document_xml)
                
                # 提取所有文本
                text_content = []
                for text_elem in root.iter():
                    if text_elem.tag.endswith('}t'):  # w:t 元素包含文本
                        if text_elem.text:
                            text_content.append(text_elem.text)
                    elif text_elem.tag.endswith('}br'):  # w:br 换行符
                        text_content.append('\n')
                    elif text_elem.tag.endswith('}p'):  # w:p 段落结束
                        text_content.append('\n')
                
                return ''.join(text_content)
                
        except Exception as e:
            raise Exception(f"读取DOCX文件失败: {e}")
    
    def extract_basic_info(self, file_path: str) -> dict:
        """提取DOCX文件的基本信息"""
        info = {
            'text_content': '',
            'word_count': 0,
            'paragraph_count': 0,
            'has_tables': False,
            'has_images': False
        }
        
        try:
            with zipfile.ZipFile(file_path, 'r') as docx_zip:
                # 读取文档内容
                document_xml = docx_zip.read('word/document.xml')
                root = ET.fromstring(document_xml)
                
                # 提取文本
                info['text_content'] = self.read_docx_text(file_path)
                
                # 统计字数
                words = re.findall(r'\b\w+\b', info['text_content'])
                info['word_count'] = len(words)
                
                # 统计段落数
                paragraphs = info['text_content'].split('\n')
                info['paragraph_count'] = len([p for p in paragraphs if p.strip()])
                
                # 检查是否有表格
                for elem in root.iter():
                    if elem.tag.endswith('}tbl'):  # w:tbl 表格元素
                        info['has_tables'] = True
                        break
                
                # 检查是否有图片
                try:
                    rels_xml = docx_zip.read('word/_rels/document.xml.rels')
                    rels_root = ET.fromstring(rels_xml)
                    for rel in rels_root.iter():
                        if 'image' in rel.get('Type', '').lower():
                            info['has_images'] = True
                            break
                except:
                    pass  # 如果没有关系文件，忽略
                
        except Exception as e:
            raise Exception(f"提取文档信息失败: {e}")
        
        return info
    
    def convert_to_text(self, file_path: str, output_path: str = None) -> str:
        """将DOCX转换为纯文本文件"""
        text_content = self.read_docx_text(file_path)
        
        # 清理文本格式
        cleaned_text = self._clean_text(text_content)
        
        if output_path:
            with open(output_path, 'w', encoding='utf-8') as f:
                f.write(cleaned_text)
            print(f"✅ 文本文件已保存: {output_path}")
        
        return cleaned_text
    
    def _clean_text(self, text: str) -> str:
        """清理文本格式"""
        # 移除多余的换行符
        text = re.sub(r'\n\s*\n', '\n\n', text)
        
        # 移除行首行尾空白
        lines = [line.strip() for line in text.split('\n')]
        
        # 重新组合
        cleaned_lines = []
        for line in lines:
            if line:  # 非空行
                cleaned_lines.append(line)
            elif cleaned_lines and cleaned_lines[-1]:  # 空行，且前一行不是空行
                cleaned_lines.append('')
        
        return '\n'.join(cleaned_lines)
    
    def print_info(self, file_path: str):
        """打印DOCX文件信息"""
        info = self.extract_basic_info(file_path)
        
        print(f"📄 文件: {Path(file_path).name}")
        print(f"📊 字数: {info['word_count']}")
        print(f"📝 段落数: {info['paragraph_count']}")
        print(f"📋 包含表格: {'是' if info['has_tables'] else '否'}")
        print(f"🖼️ 包含图片: {'是' if info['has_images'] else '否'}")
        print("=" * 60)
        print("📖 文档内容:")
        print(info['text_content'][:1000] + "..." if len(info['text_content']) > 1000 else info['text_content'])


def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("用法: python simple_docx_reader.py <docx文件路径> [选项]")
        print("选项:")
        print("  --info    显示文档信息")
        print("  --text    转换为文本文件")
        print("  --output  指定输出文件路径")
        sys.exit(1)
    
    file_path = sys.argv[1]
    
    if not Path(file_path).exists():
        print(f"❌ 文件不存在: {file_path}")
        sys.exit(1)
    
    if not file_path.lower().endswith('.docx'):
        print(f"❌ 不是DOCX文件: {file_path}")
        sys.exit(1)
    
    try:
        reader = SimpleDocxReader()
        
        if '--info' in sys.argv:
            reader.print_info(file_path)
        elif '--text' in sys.argv:
            output_path = None
            if '--output' in sys.argv:
                output_idx = sys.argv.index('--output')
                if output_idx + 1 < len(sys.argv):
                    output_path = sys.argv[output_idx + 1]
            
            if not output_path:
                output_path = Path(file_path).with_suffix('.txt')
            
            reader.convert_to_text(file_path, str(output_path))
        else:
            # 默认显示信息
            reader.print_info(file_path)
    
    except Exception as e:
        print(f"❌ 处理失败: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
