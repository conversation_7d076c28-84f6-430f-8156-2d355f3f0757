#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试pytdx连接
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

def test_pytdx_basic():
    """测试pytdx基本功能"""
    print("🧪 测试pytdx基本功能")
    print("=" * 40)
    
    try:
        import pytdx
        print("✅ pytdx导入成功")
        
        from pytdx.hq import TdxHq_API
        print("✅ TdxHq_API导入成功")
        
        api = TdxHq_API()
        print("✅ TdxHq_API实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ pytdx基本功能测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_pytdx_downloader():
    """测试pytdx下载器"""
    print("\n🔧 测试pytdx下载器")
    print("=" * 40)
    
    try:
        from utils.pytdx_downloader import PytdxDownloader
        print("✅ PytdxDownloader导入成功")
        
        downloader = PytdxDownloader()
        print("✅ PytdxDownloader实例创建成功")
        
        return True
        
    except Exception as e:
        print(f"❌ pytdx下载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_stock_data_downloader():
    """测试股票数据下载器"""
    print("\n📊 测试股票数据下载器")
    print("=" * 40)
    
    try:
        from utils.stock_data_downloader import StockDataDownloader
        print("✅ StockDataDownloader导入成功")
        
        # 创建配置管理器
        from src.mythquant.config import get_config_manager
        config_manager = get_config_manager()
        print("✅ 配置管理器创建成功")
        
        # 创建股票数据下载器（不传参数，让它从配置中读取）
        downloader = StockDataDownloader()
        print("✅ StockDataDownloader实例创建成功")
        
        # 检查数据源
        print(f"📊 可用数据源数量: {len(downloader.data_sources)}")
        for source in downloader.data_sources:
            print(f"   - {source['name']}: {'可用' if source['available'] else '不可用'}")
        
        return True
        
    except Exception as e:
        print(f"❌ 股票数据下载器测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("🚀 pytdx连接测试")
    print("=" * 50)
    
    # 测试pytdx基本功能
    basic_ok = test_pytdx_basic()
    
    if not basic_ok:
        print("\n❌ pytdx基本功能测试失败，停止后续测试")
        return False
    
    # 测试pytdx下载器
    downloader_ok = test_pytdx_downloader()
    
    # 测试股票数据下载器
    stock_downloader_ok = test_stock_data_downloader()
    
    print("\n" + "=" * 50)
    if basic_ok and downloader_ok and stock_downloader_ok:
        print("🎉 所有pytdx测试通过！")
        return True
    else:
        print("❌ 部分测试失败，需要进一步检查")
        return False

if __name__ == '__main__':
    success = main()
    sys.exit(0 if success else 1)
