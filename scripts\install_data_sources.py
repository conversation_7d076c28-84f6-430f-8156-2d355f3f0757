#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据源安装脚本
安装股票数据下载所需的第三方库
"""

import sys
import subprocess
import importlib
from typing import List, Dict


def check_package_installed(package_name: str) -> bool:
    """检查包是否已安装"""
    try:
        importlib.import_module(package_name)
        return True
    except ImportError:
        return False


def install_package(package_name: str) -> bool:
    """安装单个包"""
    try:
        print(f"📦 正在安装 {package_name}...")
        subprocess.check_call([
            sys.executable, "-m", "pip", "install", package_name, 
            "--upgrade", "--quiet"
        ])
        print(f"✅ {package_name} 安装成功")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ {package_name} 安装失败: {e}")
        return False


def install_data_sources() -> Dict[str, bool]:
    """安装所有数据源"""
    
    # 定义数据源包
    data_sources = {
        'baostock': {
            'package': 'baostock',
            'description': '证券宝 - 免费A股数据源',
            'priority': 1
        },
        'akshare': {
            'package': 'akshare',
            'description': 'AKShare - 开源财经数据接口',
            'priority': 2
        },
        'yfinance': {
            'package': 'yfinance',
            'description': 'Yahoo Finance - 全球股票数据',
            'priority': 3
        }
    }
    
    print("🚀 MythQuant 数据源安装工具")
    print("=" * 60)
    
    results = {}
    
    for name, info in data_sources.items():
        package = info['package']
        description = info['description']
        
        print(f"\n📋 检查 {name} ({description})")
        
        if check_package_installed(package):
            print(f"✅ {package} 已安装")
            results[name] = True
        else:
            print(f"❌ {package} 未安装")
            success = install_package(package)
            results[name] = success
    
    return results


def test_data_sources(results: Dict[str, bool]):
    """测试数据源是否可用"""
    print("\n" + "=" * 60)
    print("🧪 测试数据源连接")
    print("=" * 60)
    
    # 测试BaoStock
    if results.get('baostock', False):
        try:
            import baostock as bs
            lg = bs.login()
            if lg.error_code == '0':
                print("✅ BaoStock 连接测试成功")
                bs.logout()
            else:
                print(f"⚠️ BaoStock 连接测试失败: {lg.error_msg}")
        except Exception as e:
            print(f"❌ BaoStock 测试异常: {e}")
    
    # 测试AKShare
    if results.get('akshare', False):
        try:
            import akshare as ak
            # 简单测试：获取股票基本信息
            df = ak.stock_info_a_code_name()
            if df is not None and not df.empty:
                print("✅ AKShare 连接测试成功")
            else:
                print("⚠️ AKShare 连接测试失败")
        except Exception as e:
            print(f"❌ AKShare 测试异常: {e}")
    
    # 测试yfinance
    if results.get('yfinance', False):
        try:
            import yfinance as yf
            # 简单测试：获取一只股票信息
            ticker = yf.Ticker("AAPL")
            info = ticker.info
            if info:
                print("✅ yfinance 连接测试成功")
            else:
                print("⚠️ yfinance 连接测试失败")
        except Exception as e:
            print(f"❌ yfinance 测试异常: {e}")


def display_usage_guide():
    """显示使用指南"""
    print("\n" + "=" * 60)
    print("📖 使用指南")
    print("=" * 60)
    
    print("\n🎯 推荐的数据源优先级:")
    print("   1. BaoStock - 专门针对中国A股，免费无限制")
    print("   2. AKShare - 功能强大，支持多种金融产品")
    print("   3. yfinance - 主要用于港股、美股数据")
    
    print("\n🚀 开始下载数据:")
    print("   python scripts/download_internet_data.py")
    
    print("\n💡 注意事项:")
    print("   - 首次使用建议先测试单只股票")
    print("   - 批量下载时会自动添加请求间隔")
    print("   - 下载的文件会自动添加'来源互联网'标识")
    print("   - 数据保存在与现有txt文件相同的目录")
    
    print("\n📁 输出文件格式:")
    print("   day_0_{股票代码}_{开始日期}-{结束日期}_来源互联网.txt")
    
    print("\n🔧 如果遇到问题:")
    print("   1. 检查网络连接")
    print("   2. 确认防火墙设置")
    print("   3. 尝试使用VPN（某些数据源可能需要）")
    print("   4. 查看日志文件获取详细错误信息")


def main():
    """主函数"""
    try:
        # 安装数据源
        results = install_data_sources()
        
        # 统计安装结果
        success_count = sum(results.values())
        total_count = len(results)
        
        print(f"\n📊 安装结果统计:")
        print(f"   成功安装: {success_count}/{total_count} 个数据源")
        print(f"   安装成功率: {success_count/total_count*100:.1f}%")
        
        if success_count == 0:
            print("\n❌ 没有成功安装任何数据源")
            print("💡 请检查网络连接和pip配置")
            return 1
        
        # 测试数据源
        test_data_sources(results)
        
        # 显示使用指南
        display_usage_guide()
        
        print(f"\n🎉 数据源安装完成！")
        print(f"✨ 现在可以使用 python scripts/download_internet_data.py 下载股票数据")
        
        return 0
        
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断安装")
        return 130
        
    except Exception as e:
        print(f"\n❌ 安装过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        return 1


if __name__ == '__main__':
    try:
        result = main()
        sys.exit(result)
    except Exception as e:
        print(f"程序异常退出: {e}")
        sys.exit(1)
