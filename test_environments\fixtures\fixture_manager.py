#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试素材管理器

管理测试环境中的标准测试素材，确保：
1. 测试素材的完整性和一致性
2. 测试素材的保鲜机制
3. 测试素材的自动发现和验证
4. 测试素材的元数据管理

作者: AI Assistant
创建时间: 2025-08-01
"""

import os
import json
import shutil
from pathlib import Path
from typing import Dict, List, Optional, Any
from datetime import datetime


class FixtureManager:
    """测试素材管理器"""
    
    def __init__(self, fixtures_root: str = None):
        """
        初始化测试素材管理器
        
        Args:
            fixtures_root: 测试素材根目录
        """
        if fixtures_root is None:
            fixtures_root = os.path.join('test_environments', 'fixtures')
        
        self.fixtures_root = Path(fixtures_root)
        self.metadata_file = self.fixtures_root / 'fixtures_metadata.json'
        
        # 确保目录存在
        self.fixtures_root.mkdir(parents=True, exist_ok=True)
        
        # 加载元数据
        self.metadata = self._load_metadata()
    
    def _load_metadata(self) -> Dict[str, Any]:
        """加载测试素材元数据"""
        if self.metadata_file.exists():
            try:
                with open(self.metadata_file, 'r', encoding='utf-8') as f:
                    return json.load(f)
            except Exception as e:
                print(f"⚠️ 加载测试素材元数据失败: {e}")
        
        return {
            'version': '1.0',
            'created_at': datetime.now().isoformat(),
            'fixtures': {}
        }
    
    def _save_metadata(self):
        """保存测试素材元数据"""
        try:
            self.metadata['updated_at'] = datetime.now().isoformat()
            with open(self.metadata_file, 'w', encoding='utf-8') as f:
                json.dump(self.metadata, f, indent=2, ensure_ascii=False)
        except Exception as e:
            print(f"⚠️ 保存测试素材元数据失败: {e}")
    
    def discover_fixtures(self) -> Dict[str, List[str]]:
        """
        自动发现测试素材
        
        Returns:
            Dict: 按类型分组的测试素材列表
        """
        fixtures = {
            'minute_data': [],
            'day_data': [],
            'reference_data': [],
            'other': []
        }
        
        for root, dirs, files in os.walk(self.fixtures_root):
            for file in files:
                if file.endswith('.txt'):
                    relative_path = os.path.relpath(os.path.join(root, file), self.fixtures_root)
                    
                    # 根据路径和文件名分类
                    if 'minute_data' in root or file.startswith('1min_'):
                        fixtures['minute_data'].append(relative_path)
                    elif 'day_data' in root or file.startswith('day_'):
                        fixtures['day_data'].append(relative_path)
                    elif 'reference_data' in root:
                        fixtures['reference_data'].append(relative_path)
                    else:
                        fixtures['other'].append(relative_path)
        
        return fixtures
    
    def get_fixture_path(self, fixture_name: str) -> Optional[str]:
        """
        获取测试素材的完整路径
        
        Args:
            fixture_name: 测试素材名称（可以是相对路径）
            
        Returns:
            str: 完整路径，如果不存在返回None
        """
        # 尝试直接路径
        full_path = self.fixtures_root / fixture_name
        if full_path.exists():
            return str(full_path)
        
        # 尝试在各个子目录中查找
        for subdir in ['minute_data', 'day_data', 'reference_data']:
            full_path = self.fixtures_root / subdir / fixture_name
            if full_path.exists():
                return str(full_path)
        
        return None
    
    def validate_fixture(self, fixture_path: str) -> Dict[str, Any]:
        """
        验证测试素材的完整性
        
        Args:
            fixture_path: 测试素材路径
            
        Returns:
            Dict: 验证结果
        """
        result = {
            'valid': False,
            'file_exists': False,
            'has_header': False,
            'line_count': 0,
            'columns': [],
            'sample_data': [],
            'errors': []
        }
        
        try:
            if not os.path.exists(fixture_path):
                result['errors'].append(f"文件不存在: {fixture_path}")
                return result
            
            result['file_exists'] = True
            
            with open(fixture_path, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            result['line_count'] = len(lines)
            
            if lines:
                # 检查表头
                header_line = lines[0].strip()
                if '|' in header_line:
                    result['has_header'] = True
                    result['columns'] = header_line.split('|')
                else:
                    result['errors'].append("缺少表头或格式不正确")
                
                # 获取样本数据
                sample_lines = min(3, len(lines) - 1)
                for i in range(1, sample_lines + 1):
                    if i < len(lines):
                        result['sample_data'].append(lines[i].strip())
                
                # 基本验证
                if result['has_header'] and result['line_count'] > 1:
                    result['valid'] = True
            else:
                result['errors'].append("文件为空")
        
        except Exception as e:
            result['errors'].append(f"验证过程出错: {e}")
        
        return result
    
    def register_fixture(self, fixture_name: str, description: str = "", 
                        tags: List[str] = None) -> bool:
        """
        注册测试素材
        
        Args:
            fixture_name: 测试素材名称
            description: 描述
            tags: 标签列表
            
        Returns:
            bool: 是否成功注册
        """
        fixture_path = self.get_fixture_path(fixture_name)
        if not fixture_path:
            print(f"❌ 测试素材不存在: {fixture_name}")
            return False
        
        # 验证素材
        validation = self.validate_fixture(fixture_path)
        if not validation['valid']:
            print(f"❌ 测试素材验证失败: {fixture_name}")
            for error in validation['errors']:
                print(f"   {error}")
            return False
        
        # 注册到元数据
        self.metadata['fixtures'][fixture_name] = {
            'description': description,
            'tags': tags or [],
            'path': fixture_path,
            'registered_at': datetime.now().isoformat(),
            'validation': validation
        }
        
        self._save_metadata()
        print(f"✅ 测试素材注册成功: {fixture_name}")
        return True
    
    def list_fixtures(self, tag: str = None) -> List[Dict[str, Any]]:
        """
        列出测试素材
        
        Args:
            tag: 按标签过滤
            
        Returns:
            List: 测试素材列表
        """
        fixtures = []
        
        for name, info in self.metadata['fixtures'].items():
            if tag is None or tag in info.get('tags', []):
                fixtures.append({
                    'name': name,
                    'description': info.get('description', ''),
                    'tags': info.get('tags', []),
                    'path': info.get('path', ''),
                    'line_count': info.get('validation', {}).get('line_count', 0),
                    'valid': info.get('validation', {}).get('valid', False)
                })
        
        return fixtures
    
    def prepare_fresh_fixture(self, fixture_name: str, target_dir: str) -> Optional[str]:
        """
        准备新鲜的测试素材（复制到目标目录）
        
        Args:
            fixture_name: 测试素材名称
            target_dir: 目标目录
            
        Returns:
            str: 复制后的文件路径，失败返回None
        """
        fixture_path = self.get_fixture_path(fixture_name)
        if not fixture_path:
            print(f"❌ 测试素材不存在: {fixture_name}")
            return None
        
        # 确保目标目录存在
        os.makedirs(target_dir, exist_ok=True)
        
        # 复制文件
        target_path = os.path.join(target_dir, os.path.basename(fixture_name))
        try:
            shutil.copy2(fixture_path, target_path)
            print(f"📋 测试素材已复制: {fixture_name} -> {target_path}")
            return target_path
        except Exception as e:
            print(f"❌ 复制测试素材失败: {e}")
            return None
    
    def generate_report(self) -> str:
        """
        生成测试素材报告
        
        Returns:
            str: 报告内容
        """
        fixtures = self.discover_fixtures()
        registered_fixtures = self.list_fixtures()
        
        report = []
        report.append("# 测试素材管理报告")
        report.append(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        report.append("")
        
        # 发现的素材统计
        report.append("## 发现的测试素材")
        total_discovered = sum(len(files) for files in fixtures.values())
        report.append(f"总计: {total_discovered} 个文件")
        
        for category, files in fixtures.items():
            if files:
                report.append(f"### {category} ({len(files)} 个)")
                for file in files:
                    report.append(f"- {file}")
                report.append("")
        
        # 注册的素材
        report.append("## 注册的测试素材")
        report.append(f"总计: {len(registered_fixtures)} 个")
        
        for fixture in registered_fixtures:
            status = "✅" if fixture['valid'] else "❌"
            report.append(f"### {status} {fixture['name']}")
            report.append(f"- 描述: {fixture['description']}")
            report.append(f"- 标签: {', '.join(fixture['tags'])}")
            report.append(f"- 行数: {fixture['line_count']}")
            report.append("")
        
        return "\n".join(report)


# ==================== 便捷函数 ====================
_global_fixture_manager = None


def get_fixture_manager() -> FixtureManager:
    """获取全局测试素材管理器"""
    global _global_fixture_manager
    if _global_fixture_manager is None:
        _global_fixture_manager = FixtureManager()
    return _global_fixture_manager


def get_fixture_path(fixture_name: str) -> Optional[str]:
    """便捷函数：获取测试素材路径"""
    return get_fixture_manager().get_fixture_path(fixture_name)


def prepare_fresh_fixture(fixture_name: str, target_dir: str) -> Optional[str]:
    """便捷函数：准备新鲜测试素材"""
    return get_fixture_manager().prepare_fresh_fixture(fixture_name, target_dir)


# ==================== 使用示例 ====================
if __name__ == '__main__':
    print("🧪 测试素材管理器演示")
    print("=" * 60)
    
    # 创建管理器
    manager = FixtureManager()
    
    # 发现测试素材
    print("1. 发现测试素材:")
    fixtures = manager.discover_fixtures()
    for category, files in fixtures.items():
        if files:
            print(f"   {category}: {len(files)} 个文件")
            for file in files[:3]:  # 只显示前3个
                print(f"     - {file}")
            if len(files) > 3:
                print(f"     ... 还有 {len(files) - 3} 个文件")
    
    # 注册测试素材
    print(f"\n2. 注册测试素材:")
    for category, files in fixtures.items():
        for file in files:
            manager.register_fixture(
                file, 
                f"{category}测试素材", 
                [category, 'standard']
            )
    
    # 列出注册的素材
    print(f"\n3. 注册的测试素材:")
    registered = manager.list_fixtures()
    for fixture in registered:
        status = "✅" if fixture['valid'] else "❌"
        print(f"   {status} {fixture['name']} ({fixture['line_count']} 行)")
    
    # 生成报告
    print(f"\n4. 生成报告:")
    report = manager.generate_report()
    print("   报告已生成（内容较长，此处省略）")
    
    print(f"\n✅ 测试素材管理器演示完成")
