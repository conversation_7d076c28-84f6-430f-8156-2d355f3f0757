2025-07-27 17:13:34,628 - Main - INFO - info:307 - ℹ️ 程序启动，日志文件: logs\mythquant_20250727_171334.log
2025-07-27 17:13:34,628 - Main - INFO - info:307 - ℹ️ pytdx库可用，开始服务器检测
2025-07-27 17:13:34,648 - Main - INFO - info:307 - ℹ️ ✅ 从pytdx官方加载了 54 个服务器
2025-07-27 17:13:34,649 - Main - INFO - info:307 - ℹ️ 🔍 开始自动检测通达信服务器...
2025-07-27 17:13:34,650 - Main - INFO - info:307 - ℹ️ 🧠 智能检测模式：使用黑名单过滤的服务器测试...
2025-07-27 17:13:34,660 - Main - INFO - info:307 - ℹ️ 开始并行测试 54 个服务器...
2025-07-27 17:13:37,676 - Main - WARNING - warning:311 - ⚠️ ❌ 广州行情主站 (119.147.171.206:443) - 连接失败
2025-07-27 17:13:37,677 - Main - WARNING - warning:311 - ⚠️ ❌ 北京行情主站1 (106.120.74.86:7711) - 连接失败
2025-07-27 17:13:37,677 - Main - WARNING - warning:311 - ⚠️ ❌ 上海行情主站 (114.80.80.222:7711) - 连接失败
2025-07-27 17:13:37,677 - Main - WARNING - warning:311 - ⚠️ ❌ 深圳行情主站 (113.105.73.88:7709) - 连接失败
2025-07-27 17:13:37,677 - Main - WARNING - warning:311 - ⚠️ ❌ 移动行情主站 (117.184.140.156:7711) - 连接失败
2025-07-27 17:13:37,677 - Main - WARNING - warning:311 - ⚠️ ❌ 杭州行情主站 (218.108.50.178:7711) - 连接失败
2025-07-27 17:13:37,677 - Main - WARNING - warning:311 - ⚠️ ❌ 广州行情主站 (119.147.171.206:80) - 连接失败
2025-07-27 17:13:37,677 - Main - WARNING - warning:311 - ⚠️ ❌ 深圳行情主站 (113.105.73.88:7711) - 连接失败
2025-07-27 17:13:40,680 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_106.120.74.86 (106.120.74.86:7709) - 连接失败
2025-07-27 17:13:40,680 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.74 (112.95.140.74:7709) - 连接失败
2025-07-27 17:13:40,681 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.22 (114.80.149.22:7709) - 连接失败
2025-07-27 17:13:40,681 - Main - WARNING - warning:311 - ⚠️ ❌ 北京行情主站2 (221.194.181.176:7711) - 连接失败
2025-07-27 17:13:40,681 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.19 (114.80.149.19:7709) - 连接失败
2025-07-27 17:13:40,681 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.93 (112.95.140.93:7709) - 连接失败
2025-07-27 17:13:40,681 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_112.95.140.92 (112.95.140.92:7709) - 连接失败
2025-07-27 17:13:40,681 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.67.61.70 (114.67.61.70:7709) - 连接失败
2025-07-27 17:13:40,976 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_************** (**************:7709) - 0.194s
2025-07-27 17:13:41,000 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_115.238.56.198 (115.238.56.198:7709) - 0.212s
2025-07-27 17:13:43,692 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_117.184.140.156 (117.184.140.156:7709) - 连接失败
2025-07-27 17:13:43,692 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.29.51.30 (119.29.51.30:7709) - 连接失败
2025-07-27 17:13:43,693 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.147.171.206 (119.147.171.206:7709) - 连接失败
2025-07-27 17:13:43,693 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.80.222 (114.80.80.222:7709) - 连接失败
2025-07-27 17:13:43,693 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_114.80.149.84 (114.80.149.84:7709) - 连接失败
2025-07-27 17:13:43,693 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_119.147.164.60 (119.147.164.60:7709) - 连接失败
2025-07-27 17:13:43,985 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.104.70 (121.14.104.70:7709) - 连接失败
2025-07-27 17:13:44,000 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.104.72 (121.14.104.72:7709) - 连接失败
2025-07-27 17:13:44,327 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_180.153.18.170 (180.153.18.170:7709) - 0.226s
2025-07-27 17:13:45,805 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_123.125.108.24 (123.125.108.24:7709) - 连接失败
2025-07-27 17:13:45,811 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_123.125.108.23 (123.125.108.23:7709) - 连接失败
2025-07-27 17:13:46,698 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.2.7 (121.14.2.7:7709) - 连接失败
2025-07-27 17:13:46,698 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_124.160.88.183 (124.160.88.183:7709) - 连接失败
2025-07-27 17:13:46,698 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.18.17 (180.153.18.17:7709) - 连接失败
2025-07-27 17:13:46,699 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_121.14.110.194 (121.14.110.194:7709) - 连接失败
2025-07-27 17:13:47,007 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.18.171 (180.153.18.171:7709) - 连接失败
2025-07-27 17:13:47,018 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_218.75.126.9 (218.75.126.9:7709) - 0.211s
2025-07-27 17:13:47,321 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.12.136.250 (60.12.136.250:7709) - 0.209s
2025-07-27 17:13:47,330 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_180.153.39.51 (180.153.39.51:7709) - 连接失败
2025-07-27 17:13:47,662 - Main - INFO - info:307 - ℹ️ ✅ 官方服务器_60.191.117.167 (60.191.117.167:7709) - 0.223s
2025-07-27 17:13:48,815 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.47.69 (218.108.47.69:7709) - 连接失败
2025-07-27 17:13:48,815 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.50.178 (218.108.50.178:7709) - 连接失败
2025-07-27 17:13:49,710 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.108.98.244 (218.108.98.244:7709) - 连接失败
2025-07-27 17:13:49,710 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_218.9.148.108 (218.9.148.108:7709) - 连接失败
2025-07-27 17:13:49,710 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_221.194.181.176 (221.194.181.176:7709) - 连接失败
2025-07-27 17:13:50,022 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_59.173.18.69 (59.173.18.69:7709) - 连接失败
2025-07-27 17:13:50,336 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_60.28.29.69 (60.28.29.69:7709) - 连接失败
2025-07-27 17:13:50,678 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.135.142.73 (61.135.142.73:7709) - 连接失败
2025-07-27 17:13:51,817 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.152.107.168 (61.152.107.168:7721) - 连接失败
2025-07-27 17:13:51,817 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.135.142.88 (61.135.142.88:7709) - 连接失败
2025-07-27 17:13:52,685 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_113.105.142.162 (113.105.142.162:7721) - 连接失败
2025-07-27 17:13:52,711 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.153.144.179 (61.153.144.179:7709) - 连接失败
2025-07-27 17:13:52,711 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.153.209.138 (61.153.209.138:7709) - 连接失败
2025-07-27 17:13:52,711 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_61.152.249.56 (61.152.249.56:7709) - 连接失败
2025-07-27 17:13:53,037 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_************** (**************:7709) - 连接失败
2025-07-27 17:13:53,692 - Main - WARNING - warning:311 - ⚠️ ❌ 官方服务器_************** (**************:7721) - 连接失败
2025-07-27 17:13:53,736 - Main - INFO - info:307 - ℹ️ 配置文件已备份到: user_config.py.backup
2025-07-27 17:13:53,736 - Main - INFO - info:307 - ℹ️ 配置文件更新成功: user_config.py
2025-07-27 17:13:53,737 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器IP已更新为: **************
2025-07-27 17:13:53,737 - Main - INFO - info:307 - ℹ️ ✅ pytdx服务器端口已更新为: 7709
2025-07-27 17:13:53,737 - Main - INFO - info:307 - ℹ️ 最佳服务器列表已保存到: tdx_servers.json
2025-07-27 17:13:53,737 - Main - INFO - info:307 - ℹ️ pytdx服务器配置已更新: **************:7709
2025-07-27 17:13:53,737 - Main - INFO - info:307 - ℹ️ 正在初始化核心组件...
2025-07-27 17:13:53,738 - Main - INFO - info:307 - ℹ️ 正在初始化股票数据处理器...
2025-07-27 17:13:53,738 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-27 17:13:53,738 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-27 17:13:54,311 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-27 17:13:54,314 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-27 17:13:54,329 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-27 17:13:54,329 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-27 17:13:54,330 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-27 17:13:54,331 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-27 17:13:54,331 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-27 17:13:54,332 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-27 17:13:54,333 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-27 17:13:54,340 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-27 17:13:54,340 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-27 17:13:54,340 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-27 17:13:54,341 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-27 17:13:54,350 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-27 17:13:54,827 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.48秒
2025-07-27 17:13:54,827 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.48秒
2025-07-27 17:13:54,827 - Main - INFO - info:307 - ℹ️ 股票数据处理器初始化完成
2025-07-27 17:13:54,827 - Main - INFO - info:307 - ℹ️ 正在加载任务配置...
2025-07-27 17:13:54,827 - Main - INFO - info:307 - ℹ️ 加载了 7 个任务配置
2025-07-27 17:13:54,827 - Main - INFO - info:307 - ℹ️ 核心组件初始化完成
2025-07-27 17:13:54,827 - Main - INFO - info:307 - ℹ️ 应用程序初始化完成
2025-07-27 17:13:54,828 - Main - INFO - info:307 - ℹ️ 开始执行所有任务
2025-07-27 17:13:54,828 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: TDX日线级别数据生成
2025-07-27 17:13:54,828 - Main - INFO - info:307 - ℹ️ 📊 发现 4 个现有数据文件，开始稽核
2025-07-27 17:13:55,084 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 3 个文件质量不达标
2025-07-27 17:13:55,084 - Main - INFO - info:307 - ℹ️ 开始执行任务: TDX日线级别数据生成
2025-07-27 17:13:55,085 - main_v20230219_optimized - INFO - generate_daily_data_task:3494 - 🚀 开始生成日线数据 (输出到: H:/MPV1.17/T0002/signals)
2025-07-27 17:13:55,085 - main_v20230219_optimized - INFO - generate_daily_buysell_txt_mt:4033 - 🧵 启动多线程日线级别txt文件生成
2025-07-27 17:13:55,085 - file_io.excel_reader - INFO - load_target_stocks_from_excel:31 - 尝试读取目标股票代码文件: C:/Users/<USER>/Desktop/全部A股数据v20250629.xlsx
2025-07-27 17:13:55,086 - file_io.excel_reader - INFO - load_target_stocks_from_excel:33 - 文件大小: 4656647 字节
2025-07-27 17:13:55,228 - file_io.excel_reader - INFO - load_target_stocks_from_excel:71 - 从Excel文件读取到 1 个目标股票代码
2025-07-27 17:13:55,229 - file_io.excel_reader - INFO - load_target_stocks_from_excel:72 - 目标股票代码: ['000617']
2025-07-27 17:13:55,235 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 10000, 内存限制: 1024MB, 系统内存: 8.0GB
2025-07-27 17:13:55,235 - utils.chunk_processor - INFO - __init__:52 - 分块处理器初始化 - 默认分块大小: 5000, 内存限制: 512MB, 系统内存: 8.0GB
2025-07-27 17:13:55,236 - utils.async_io_processor - INFO - __init__:60 - 异步IO处理器初始化 - 最大并发文件: 8, 最大并发操作: 16, 执行器类型: 线程池
2025-07-27 17:13:55,236 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:243 - 📁 使用配置的TDX主路径（已取消TEST_MODE机制）
2025-07-27 17:13:55,236 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:244 - 📁 TDX主路径: H:/MPV1.17
2025-07-27 17:13:55,236 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:245 - 📁 分钟线路径配置: /vipdoc/sz/minline;/vipdoc/sh/minline;/vipdoc/bj/minline
2025-07-27 17:13:55,236 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:249 - 📁 解析后的市场路径: {'sz': '/vipdoc/sz/minline', 'sh': '/vipdoc/sh/minline', 'bj': '/vipdoc/bj/minline'}
2025-07-27 17:13:55,239 - main_v20230219_optimized - INFO - _get_optimal_tdx_path:252 - 📁 数据可用性检查: 找到3个市场的分钟数据文件: sz市场, sh市场, bj市场 (共3个文件)
2025-07-27 17:13:55,240 - main_v20230219_optimized - INFO - __init__:208 - 初始化股票数据处理器成功，使用路径: H:/MPV1.17，缓存方法: memory
2025-07-27 17:13:55,240 - core.logging_service - INFO - verbose_log:67 - 启用高性能gbbq缓存系统
2025-07-27 17:13:55,240 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】开始
2025-07-27 17:13:55,246 - core.logging_service - INFO - verbose_log:67 - 正在预加载gbbq数据到内存...
2025-07-27 17:13:55,947 - core.logging_service - INFO - verbose_log:67 - gbbq内存缓存初始化完成，耗时: 0.70秒
2025-07-27 17:13:55,947 - core.logging_service - INFO - verbose_log:67 - 【初始化gbbq缓存】完成 - 内存模式，耗时0.70秒
2025-07-27 17:14:06,684 - core.logging_service - INFO - verbose_log:67 - 使用统一缓存管理器获取股票000617的除权除息数据
2025-07-27 17:14:31,445 - core.logging_service - INFO - verbose_log:67 - 统一缓存未命中 - 股票000617，尝试传统方法
2025-07-27 17:14:31,445 - core.logging_service - INFO - verbose_log:67 - 使用传统方法获取股票000617的除权除息数据
2025-07-27 17:14:31,445 - core.logging_service - WARNING - verbose_log:67 - 内存缓存未初始化，切换到pickle模式
2025-07-27 17:14:31,454 - main_v20230219_optimized - INFO - _ensure_pickle_cache:438 - ✅ pickle缓存是最新的
2025-07-27 17:14:32,027 - core.logging_service - INFO - verbose_log:67 - 开始前复权处理流程（遵循用户建议：全量历史数据，无筛选，无经验公式）
2025-07-27 17:14:32,027 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】开始
2025-07-27 17:14:32,027 - core.logging_service - INFO - verbose_log:67 - 待处理分钟数据: 320880条
2025-07-27 17:14:32,027 - core.logging_service - INFO - verbose_log:67 - 全量历史除权除息事件: 20条
2025-07-27 17:14:32,028 - core.logging_service - INFO - verbose_log:67 - 原始收盘价已保存
2025-07-27 17:14:32,029 - core.logging_service - INFO - verbose_log:67 - 应用高精度前复权算法
2025-07-27 17:14:32,029 - core.logging_service - INFO - verbose_log:67 - 特征：高精度计算 + 标准化数据处理 + 无距离补偿
2025-07-27 17:14:32,029 - core.logging_service - INFO - verbose_log:67 - 开始高精度前复权算法处理股票sz000617
2025-07-27 17:14:32,029 - core.logging_service - INFO - verbose_log:67 - 特征：使用Decimal高精度计算，避免浮点数误差，不使用任何距离补偿
2025-07-27 17:14:32,065 - core.logging_service - INFO - verbose_log:67 - 标准化了13个分红金额的浮点数误差
2025-07-27 17:14:32,562 - core.logging_service - INFO - verbose_log:67 - 构建高精度复权因子映射表（使用Decimal精度计算）
2025-07-27 17:14:32,574 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件1: 2025-07-03, 单次因子: 0.992328, 累积因子: 0.992328 (使用累积因子)
2025-07-27 17:14:32,591 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件2: 2025-01-08, 单次因子: 0.991018, 累积因子: 0.983415 (使用累积因子)
2025-07-27 17:14:32,608 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件3: 2024-07-11, 单次因子: 0.978131, 累积因子: 0.961909 (使用累积因子)
2025-07-27 17:14:32,625 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件4: 2023-07-11, 单次因子: 0.983705, 累积因子: 0.946234 (使用累积因子)
2025-07-27 17:14:32,642 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件5: 2022-07-07, 单次因子: 0.971552, 累积因子: 0.919316 (使用累积因子)
2025-07-27 17:14:32,652 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件6: 2021-07-06, 单次因子: 0.968781, 累积因子: 0.890616 (使用累积因子)
2025-07-27 17:14:32,670 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件7: 2020-07-09, 单次因子: 0.698634, 累积因子: 0.622215 (使用累积因子)
2025-07-27 17:14:32,682 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件8: 2019-07-03, 单次因子: 0.981039, 累积因子: 0.610417 (使用累积因子)
2025-07-27 17:14:32,696 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件9: 2018-07-03, 单次因子: 0.979140, 累积因子: 0.597683 (使用累积因子)
2025-07-27 17:14:32,712 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件10: 2011-08-19, 单次因子: 0.998039, 累积因子: 0.596511 (使用累积因子)
2025-07-27 17:14:32,727 - core.logging_service - INFO - verbose_log:67 - 高精度事件11: 2010-08-24, 无法获取股权登记日收盘价，跳过
2025-07-27 17:14:32,745 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件12: 2009-07-30, 单次因子: 0.832201, 累积因子: 0.496417 (使用累积因子)
2025-07-27 17:14:32,757 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件13: 2008-08-26, 单次因子: 0.997783, 累积因子: 0.495317 (使用累积因子)
2025-07-27 17:14:32,774 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件14: 2007-07-11, 单次因子: 0.999300, 累积因子: 0.494970 (使用累积因子)
2025-07-27 17:14:32,791 - core.logging_service - INFO - verbose_log:67 - 高精度事件15: 2007-03-06, 无法获取股权登记日收盘价，跳过
2025-07-27 17:14:32,808 - core.logging_service - INFO - verbose_log:67 - 高精度事件16: 2006-08-21, 无法获取股权登记日收盘价，跳过
2025-07-27 17:14:32,824 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件17: 2005-07-12, 单次因子: 0.830340, 累积因子: 0.410993 (使用累积因子)
2025-07-27 17:14:32,837 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件18: 2004-09-27, 单次因子: 0.623324, 累积因子: 0.256182 (使用累积因子)
2025-07-27 17:14:32,852 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件19: 2003-10-16, 单次因子: 0.992661, 累积因子: 0.254302 (使用累积因子)
2025-07-27 17:14:32,869 - core.logging_service - INFO - verbose_log:67 - Decimal高精度事件20: 1997-05-27, 单次因子: 0.769231, 累积因子: 0.195617 (使用累积因子)
2025-07-27 17:14:32,869 - core.logging_service - INFO - verbose_log:67 - 高精度复权因子映射表构建完成，包含17个时间点
2025-07-27 17:14:37,401 - core.logging_service - INFO - verbose_log:67 - 股票sz000617高精度前复权完成:
2025-07-27 17:14:37,402 - core.logging_service - INFO - verbose_log:67 -   样例调整: 12.230000 → 7.609685
2025-07-27 17:14:37,402 - core.logging_service - INFO - verbose_log:67 -   调整比例: 0.62221464
2025-07-27 17:14:37,402 - core.logging_service - INFO - verbose_log:67 -   复权事件数: 17
2025-07-27 17:14:37,402 - core.logging_service - INFO - verbose_log:67 -   算法特征: 高精度Decimal计算，无距离补偿
2025-07-27 17:14:37,405 - core.logging_service - INFO - verbose_log:67 - 纯数据驱动前复权处理完成，返回320880条数据
2025-07-27 17:14:37,497 - main_v20230219_optimized - INFO - apply_forward_adjustment:2130 - 保留交易时间数据后: 320880条
2025-07-27 17:14:37,498 - core.logging_service - INFO - verbose_log:67 - 【前复权处理】完成
2025-07-27 17:14:37,498 - core.logging_service - INFO - verbose_log:67 - 前复权完成（用户建议的纯数据驱动算法）- 数据量: 320880条, 调整比例: 0.62221464
2025-07-27 17:14:37,498 - core.logging_service - INFO - verbose_log:67 - 处理了全量20个历史除权除息事件（无任何筛选）
2025-07-27 17:14:37,498 - core.logging_service - INFO - verbose_log:67 - 原始价格样例: 12.230000
2025-07-27 17:14:37,498 - core.logging_service - INFO - verbose_log:67 - 前复权价格样例: 7.609685
2025-07-27 17:14:37,498 - core.logging_service - INFO - verbose_log:67 - 算法严格按照用户建议：全量数据+无筛选+无经验公式
2025-07-27 17:14:37,510 - core.logging_service - INFO - verbose_log:67 - 第一行数据修复：上周期C设置为开盘价 7.6844
2025-07-27 17:14:37,641 - main_v20230219_optimized - INFO - load_and_process_minute_data:2599 - sz000617读取分钟数据成功: 320880条（已前复权并重新计算L2指标）
2025-07-27 17:14:38,108 - algorithms.resampling - INFO - resample_to_timeframes:100 - 日线重采样完成: 1337条记录
2025-07-27 17:14:38,276 - algorithms.resampling - INFO - resample_to_timeframes:113 - 周线重采样完成: 283条记录
2025-07-27 17:14:38,277 - algorithms.resampling - INFO - resample_to_timeframes:120 - 重采样完成 - 日线:1337条, 周线:283条
2025-07-27 17:14:38,338 - file_io.file_writer - INFO - write_daily_txt_file:143 - ✅ 000617 日线级别txt文件生成成功: H:/MPV1.17/T0002/signals\day_0_000617_20200101-20250724.txt (72149字节, 1337条记录)
2025-07-27 17:14:38,338 - file_io.file_writer - INFO - write_daily_txt_file:144 - 📋 输出列: 股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
2025-07-27 17:14:38,339 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4212 - 
日线级别txt文件生成完成汇总(多线程):
2025-07-27 17:14:38,339 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4213 - 📊 成功生成: 1 个文件
2025-07-27 17:14:38,339 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4214 - ❌ 处理失败: 0 个股票
2025-07-27 17:14:38,339 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4215 - 📈 成功率: 100.0%
2025-07-27 17:14:38,339 - main_v20230219_optimized - INFO - _generate_daily_txt_files_mt:4216 - 📁 输出目录: H:/MPV1.17/T0002/signals
2025-07-27 17:14:38,353 - main_v20230219_optimized - INFO - generate_daily_data_task:3504 - ✅ 日线数据生成完成，耗时: 43.27 秒
2025-07-27 17:14:38,353 - Main - INFO - info:307 - ℹ️ 任务执行成功: TDX日线级别数据生成
2025-07-27 17:14:38,353 - Main - INFO - info:307 - ℹ️ 🔍 执行任务后数据质量稽核: TDX日线级别数据生成
2025-07-27 17:14:38,354 - Main - INFO - info:307 - ℹ️ 📊 发现 1 个新生成文件，开始稽核
2025-07-27 17:14:38,403 - Main - INFO - info:307 - ℹ️ ✅ 执行后稽核通过: 0 优秀, 1 良好
2025-07-27 17:14:38,404 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: 互联网分钟级数据下载
2025-07-27 17:14:38,404 - Main - INFO - info:307 - ℹ️ 📊 发现 5 个现有数据文件，开始稽核
2025-07-27 17:14:38,662 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 4 个文件质量不达标
2025-07-27 17:14:38,662 - Main - INFO - info:307 - ℹ️ 开始执行任务: 互联网分钟级数据下载
2025-07-27 17:14:38,662 - Main - INFO - info:307 - ℹ️ 开始执行互联网分钟级数据下载任务
2025-07-27 17:14:38,668 - Main - INFO - info:307 - ℹ️ 成功加载互联网数据下载配置
2025-07-27 17:14:38,669 - Main - INFO - info:307 - ℹ️ pytdx数据源初始化成功
2025-07-27 17:14:39,013 - Main - INFO - info:307 - ℹ️ AKShare数据源初始化成功
2025-07-27 17:14:39,025 - Main - INFO - info:307 - ℹ️ BaoStock数据源初始化成功
2025-07-27 17:14:39,025 - Main - INFO - info:307 - ℹ️ 已启用数据源: ['pytdx', 'akshare', 'baostock']
2025-07-27 17:14:39,025 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载参数:
2025-07-27 17:14:39,025 - Main - INFO - info:307 - ℹ️   时间范围: 20250101 - 20250727
2025-07-27 17:14:39,025 - Main - INFO - info:307 - ℹ️   数据频率: 1min (1分钟)
2025-07-27 17:14:39,026 - Main - INFO - info:307 - ℹ️ 将下载 1 只股票的分钟级数据
2025-07-27 17:14:39,026 - Main - INFO - info:307 - ℹ️ 开始批量下载1只股票数据，请求间隔: 0.5秒
2025-07-27 17:14:39,026 - Main - INFO - info:307 - ℹ️ 下载进度: 1/1 - 000617
2025-07-27 17:14:39,028 - Main - INFO - info:307 - ℹ️ 🔄 开始智能增量下载验证: 000617
2025-07-27 17:14:39,028 - core.logging_service - INFO - verbose_log:67 - ✅ 加载智能文件选择器配置: 策略=smart_comprehensive
2025-07-27 17:14:39,028 - Main - INFO - info:307 - ℹ️ 🔍 智能查找000617的minute数据文件
2025-07-27 17:14:39,029 - core.logging_service - INFO - verbose_log:67 - 找到唯一文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:14:39,029 - Main - INFO - info:307 - ℹ️ ✅ 智能选择文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:14:39,029 - Main - INFO - info:307 - ℹ️ 找到现有文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:14:39,029 - Main - INFO - info:307 - ℹ️ 📊 开始智能时间范围分析
2025-07-27 17:14:39,029 - core.logging_service - INFO - verbose_log:67 - 📊 分析现有文件时间跨度: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:14:39,031 - core.logging_service - INFO - verbose_log:67 - 📅 现有文件时间跨度: 20250303 ~ 20250704 (20381条记录)
2025-07-27 17:14:39,041 - core.logging_service - WARNING - verbose_log:67 - ⚠️ 发现2个不完整的交易日
2025-07-27 17:14:39,041 - core.logging_service - WARNING - verbose_log:67 -   20250303: 234/240条 (缺失开始0930-0937)
2025-07-27 17:14:39,041 - core.logging_service - WARNING - verbose_log:67 -   20250704: 227/240条 (缺失开始0930-0931; 缺失结束1447-1500)
2025-07-27 17:14:39,041 - core.logging_service - WARNING - verbose_log:67 - ⚠️ 检测到数据不完整，可能需要补充下载
2025-07-27 17:14:39,041 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-27 17:14:39,042 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250704
2025-07-27 17:14:39,042 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-27 17:14:39,042 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-27 17:14:39,042 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-27 17:14:39,042 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-27 17:14:39,042 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-27 17:14:39,042 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-27 17:14:39,042 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-27 17:14:39,043 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250705 ~ 20250727
2025-07-27 17:14:39,043 - core.logging_service - INFO - verbose_log:67 - 🔍 验证最后一条记录的数据一致性
2025-07-27 17:14:39,044 - core.logging_service - INFO - verbose_log:67 - 📋 文件最后记录: 时间=202507041447, 前复权价=7.55
2025-07-27 17:14:39,044 - core.logging_service - INFO - verbose_log:67 - 🔄 从API获取 20250704 的数据进行比对验证
2025-07-27 17:14:39,044 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-27 17:14:39,045 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-27 17:14:39,045 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250704 - 20250704
2025-07-27 17:14:39,045 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:39,224 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:39,225 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4608条 (目标日期: 20250704, 交易日: 16天, 频率: 1min)
2025-07-27 17:14:39,225 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4608条 -> 实际请求800条 (配置限制:800)
2025-07-27 17:14:39,225 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-27 17:14:39,225 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-27 17:14:39,283 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3808条
2025-07-27 17:14:39,283 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:39,450 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:39,500 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-27 17:14:39,500 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:39,672 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:39,725 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-27 17:14:39,725 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:39,895 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:39,946 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-27 17:14:39,946 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:40,119 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:40,169 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-27 17:14:40,170 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:40,340 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:40,390 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +608条，总计4608条
2025-07-27 17:14:40,390 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4608条数据
2025-07-27 17:14:40,402 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 240 条 1min 数据
2025-07-27 17:14:40,404 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共240条记录
2025-07-27 17:14:40,404 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-27 17:14:40,405 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.360, 前复权=7.360
2025-07-27 17:14:40,406 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-27 17:14:40,406 - Main - INFO - info:307 - ℹ️   日期: 202507040931
2025-07-27 17:14:40,406 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.36
2025-07-27 17:14:40,406 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.36
2025-07-27 17:14:40,406 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-27 17:14:40,406 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 240 >= 5
2025-07-27 17:14:40,406 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-27 17:14:40,407 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-27 17:14:40,407 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-27 17:14:40,410 - core.logging_service - INFO - verbose_log:67 - 📋 API对应记录: 时间=202507041447, 前复权价=7.55
2025-07-27 17:14:40,410 - core.logging_service - INFO - verbose_log:67 - 📊 数据一致性比对结果:
2025-07-27 17:14:40,410 - core.logging_service - INFO - verbose_log:67 -   时间比对: 文件=202507041447 vs API=202507041447 -> ✅ 一致
2025-07-27 17:14:40,410 - core.logging_service - INFO - verbose_log:67 -   价格比对: 文件=7.55 vs API=7.55 -> ✅ 一致
2025-07-27 17:14:40,410 - core.logging_service - INFO - verbose_log:67 - 🎯 最终判断: ✅ 数据一致，可以使用增量下载
2025-07-27 17:14:40,411 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，可以使用智能增量下载
2025-07-27 17:14:40,413 - core.logging_service - INFO - verbose_log:67 - 获取最后一条记录: 时间=202507041447, 前复权价=7.55
2025-07-27 17:14:40,413 - core.logging_service - INFO - verbose_log:67 - 时间格式对比: 文件=202507041447, API=202507041447, 级别=1min
2025-07-27 17:14:40,413 - core.logging_service - INFO - verbose_log:67 - ✅ 时间格式一致性验证通过
2025-07-27 17:14:40,413 - core.logging_service - INFO - verbose_log:67 - ✅ 价格一致性验证通过
2025-07-27 17:14:40,413 - Main - INFO - info:307 - ℹ️ ✅ 数据一致性验证通过，执行增量下载
2025-07-27 17:14:40,413 - Main - INFO - info:307 - ℹ️ 从文件名解析时间范围: 20250303 - 20250704
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 - 🔍 时间范围比较分析
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 -   现有文件: 20250303 ~ 20250704
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 -   任务要求: 20250101 ~ 20250727
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 - 📊 时间范围分析结果:
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 -   有重叠: 是
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 -   需要前置补充: 是
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 -   需要后置补充: 是
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 -   现有文件完全覆盖: 否
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 -   前置补充范围: 20250101 ~ 20250302
2025-07-27 17:14:40,414 - core.logging_service - INFO - verbose_log:67 -   后置补充范围: 20250705 ~ 20250727
2025-07-27 17:14:40,415 - Main - INFO - info:307 - ℹ️ 需要下载早期数据: 20250101 - 20250302
2025-07-27 17:14:40,415 - Main - INFO - info:307 - ℹ️ 需要下载最新数据: 20250705 - 20250727
2025-07-27 17:14:40,426 - Main - INFO - info:307 - ℹ️ 读取现有数据: 20381 条记录
2025-07-27 17:14:40,426 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250101 - 20250302
2025-07-27 17:14:40,426 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-27 17:14:40,426 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-27 17:14:40,426 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250101 - 20250302
2025-07-27 17:14:40,426 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:40,609 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:40,609 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 42624条 (目标日期: 20250101, 交易日: 148天, 频率: 1min)
2025-07-27 17:14:40,609 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计42624条 -> 实际请求800条 (配置限制:800)
2025-07-27 17:14:40,609 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-27 17:14:40,609 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-27 17:14:40,661 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需41824条
2025-07-27 17:14:40,661 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:40,834 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:40,885 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-27 17:14:40,885 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:41,066 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:41,119 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-27 17:14:41,120 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:41,296 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:41,348 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-27 17:14:41,348 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:41,521 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:41,573 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-27 17:14:41,574 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:41,749 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:41,804 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4800条
2025-07-27 17:14:41,804 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:41,998 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:42,054 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计5600条
2025-07-27 17:14:42,054 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:42,236 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:42,289 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计6400条
2025-07-27 17:14:42,289 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:42,472 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:42,537 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计7200条
2025-07-27 17:14:42,538 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:42,710 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:42,768 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8000条
2025-07-27 17:14:42,768 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:42,941 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:42,994 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计8800条
2025-07-27 17:14:42,994 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:43,166 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:43,216 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计9600条
2025-07-27 17:14:43,216 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:43,385 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:43,436 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计10400条
2025-07-27 17:14:43,436 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:43,609 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:43,661 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计11200条
2025-07-27 17:14:43,662 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:43,839 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:43,890 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12000条
2025-07-27 17:14:43,890 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:44,062 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:44,112 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计12800条
2025-07-27 17:14:44,112 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:44,283 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:44,333 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计13600条
2025-07-27 17:14:44,334 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:44,504 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:44,554 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计14400条
2025-07-27 17:14:44,554 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:44,725 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:44,782 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计15200条
2025-07-27 17:14:44,782 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:44,962 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:45,014 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16000条
2025-07-27 17:14:45,014 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:45,193 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:45,246 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计16800条
2025-07-27 17:14:45,246 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:45,423 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:45,474 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计17600条
2025-07-27 17:14:45,474 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:45,658 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:45,711 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计18400条
2025-07-27 17:14:45,711 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:45,882 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:45,934 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计19200条
2025-07-27 17:14:45,934 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:46,104 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:46,162 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20000条
2025-07-27 17:14:46,163 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:46,343 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:46,395 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计20800条
2025-07-27 17:14:46,395 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:46,565 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:46,617 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计21600条
2025-07-27 17:14:46,618 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:46,794 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:46,846 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计22400条
2025-07-27 17:14:46,846 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:47,020 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:47,072 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计23200条
2025-07-27 17:14:47,072 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:47,245 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:47,296 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计24000条
2025-07-27 17:14:47,297 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:47,471 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:47,516 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计24000条数据
2025-07-27 17:14:47,516 - Main - WARNING - warning:311 - ⚠️ ⚠️ 数据覆盖不足: 需要42624条，实际获取24000条
2025-07-27 17:14:47,516 - Main - WARNING - warning:311 - ⚠️ ⚠️ 缺少约18624条数据（约77个交易日）
2025-07-27 17:14:47,516 - Main - WARNING - warning:311 - ⚠️ ⚠️ pytdx服务器限制：最多提供最近100个交易日的分钟数据
2025-07-27 17:14:47,516 - Main - WARNING - warning:311 - ⚠️ ⚠️ 建议：使用其他数据源（如akshare、baostock）获取更长历史数据
2025-07-27 17:14:47,569 - Main - WARNING - warning:311 - ⚠️ ⚠️ 时间范围内无数据: 20250101 - 20250302
2025-07-27 17:14:47,574 - Main - WARNING - warning:311 - ⚠️ pytdx未获取到000617的数据
2025-07-27 17:14:47,574 - Main - ERROR - error:315 - ❌ pytdx不可用，无法下载000617的分钟数据
2025-07-27 17:14:47,574 - Main - INFO - info:307 - ℹ️ 下载增量数据: 20250705 - 20250727
2025-07-27 17:14:47,575 - Main - INFO - info:307 - ℹ️ 分钟数据强制使用pytdx下载000617数据
2025-07-27 17:14:47,575 - Main - INFO - info:307 - ℹ️ 📊 频率转换: 1 -> 1min
2025-07-27 17:14:47,575 - Main - INFO - info:307 - ℹ️ 🔄 开始下载 000617 1min 数据: 20250705 - 20250727
2025-07-27 17:14:47,575 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:47,752 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:47,752 - Main - INFO - info:307 - ℹ️ 📊 智能计算数据量: 4320条 (目标日期: 20250705, 交易日: 15天, 频率: 1min)
2025-07-27 17:14:47,752 - Main - INFO - info:307 - ℹ️ 📊 数据量限制: 预计4320条 -> 实际请求800条 (配置限制:800)
2025-07-27 17:14:47,752 - Main - INFO - info:307 - ℹ️ 📊 预计获取 800 条 1min 数据
2025-07-27 17:14:47,752 - Main - INFO - info:307 - ℹ️ 📊 调用get_security_bars: category=8, market=0, code=000617, count=800
2025-07-27 17:14:47,804 - Main - INFO - info:307 - ℹ️ 🔄 需要分批获取更多数据: 已获取800条，还需3520条
2025-07-27 17:14:47,805 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:47,994 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:48,046 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计1600条
2025-07-27 17:14:48,046 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:48,222 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:48,273 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计2400条
2025-07-27 17:14:48,274 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:48,451 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:48,503 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计3200条
2025-07-27 17:14:48,503 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:48,672 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:48,723 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +800条，总计4000条
2025-07-27 17:14:48,723 - Main - INFO - info:307 - ℹ️ 🔄 尝试连接配置的最佳服务器: 60.12.136.250:7709
2025-07-27 17:14:48,906 - Main - INFO - info:307 - ℹ️ ✅ 连接配置的最佳服务器成功: 60.12.136.250:7709
2025-07-27 17:14:48,964 - Main - INFO - info:307 - ℹ️ 📊 分批获取成功: +320条，总计4320条
2025-07-27 17:14:48,964 - Main - INFO - info:307 - ℹ️ ✅ 分批获取完成: 总计4320条数据
2025-07-27 17:14:48,975 - Main - INFO - info:307 - ℹ️ ✅ 成功获取 3600 条 1min 数据
2025-07-27 17:14:48,977 - Main - INFO - info:307 - ℹ️ pytdx成功获取000617数据，共3600条记录
2025-07-27 17:14:48,977 - Main - INFO - info:307 - ℹ️ 使用pytdx成功下载000617分钟数据
2025-07-27 17:14:48,990 - Main - INFO - info:307 - ℹ️ ✅ 使用前复权数据，价格差异样例: 原始=7.640, 前复权=7.640
2025-07-27 17:14:48,991 - Main - INFO - info:307 - ℹ️ 数据转换完成，样本数据:
2025-07-27 17:14:48,991 - Main - INFO - info:307 - ℹ️   日期: 202507070931
2025-07-27 17:14:48,992 - Main - INFO - info:307 - ℹ️   当日收盘价C: 7.64
2025-07-27 17:14:48,992 - Main - INFO - info:307 - ℹ️   前复权收盘价C: 7.64
2025-07-27 17:14:48,992 - Main - WARNING - warning:311 - ⚠️ ⚠️ 不复权和前复权价格差异较小(0.0000)，阈值: 0.01
2025-07-27 17:14:48,992 - Main - INFO - info:307 - ℹ️ ✅ 数据点数量检查通过: 3600 >= 5
2025-07-27 17:14:48,992 - Main - INFO - info:307 - ℹ️ ✅ 股票代码格式检查通过: 000617
2025-07-27 17:14:48,992 - Main - INFO - info:307 - ℹ️ ✅ 价格数据有效性检查通过
2025-07-27 17:14:48,993 - Main - WARNING - warning:311 - ⚠️ 前复权价格差异较小，平均差异: 0.0000 <= 0.01
2025-07-27 17:14:48,993 - Main - INFO - info:307 - ℹ️ 获得新数据: 3600 条记录
2025-07-27 17:14:48,997 - Main - INFO - info:307 - ℹ️ 时间列数据类型统一完成，数据类型: object
2025-07-27 17:14:49,005 - Main - INFO - info:307 - ℹ️ 合并后数据: 23981 条记录
2025-07-27 17:14:49,008 - core.logging_service - INFO - verbose_log:67 - 📝 时间范围变化，需要重命名:
2025-07-27 17:14:49,008 - core.logging_service - INFO - verbose_log:67 -   原始: 20250303-20250704
2025-07-27 17:14:49,008 - core.logging_service - INFO - verbose_log:67 -   更新: 202503030937-202507251500
2025-07-27 17:14:49,008 - core.logging_service - INFO - verbose_log:67 -   新文件名: 1min_0_000617_202503030937-202507251500_来源互联网（202507271714）.txt
2025-07-27 17:14:49,076 - Main - INFO - info:307 - ℹ️ 删除旧文件: 1min_0_000617_20250303-20250704_来源互联网（202507262158）.txt
2025-07-27 17:14:49,076 - Main - INFO - info:307 - ℹ️ ✅ 智能重命名完成: 1min_0_000617_202503030937-202507251500_来源互联网（202507271714）.txt
2025-07-27 17:14:49,077 - Main - INFO - info:307 - ℹ️ ✅ 智能增量下载完成
2025-07-27 17:14:49,077 - Main - INFO - info:307 - ℹ️ ✅ 000617 下载成功
2025-07-27 17:14:49,077 - Main - INFO - info:307 - ℹ️ 批量下载完成: 1/1 成功
2025-07-27 17:14:49,077 - Main - INFO - info:307 - ℹ️ 互联网分钟数据下载完成: 1/1 成功 (100.0%)
2025-07-27 17:14:49,077 - Main - INFO - info:307 - ℹ️ 任务执行成功: 互联网分钟级数据下载
2025-07-27 17:14:49,077 - Main - INFO - info:307 - ℹ️ 🔍 执行任务后数据质量稽核: 互联网分钟级数据下载
2025-07-27 17:14:49,078 - Main - INFO - info:307 - ℹ️ 📊 发现 2 个新生成文件，开始稽核
2025-07-27 17:14:49,498 - Main - INFO - info:307 - ℹ️ ✅ 执行后稽核通过: 1 优秀, 1 良好
2025-07-27 17:14:49,498 - Main - INFO - info:307 - ℹ️ 🔍 执行任务前数据质量稽核: 前复权数据比较分析
2025-07-27 17:14:49,499 - Main - INFO - info:307 - ℹ️ 📊 发现 5 个现有数据文件，开始稽核
2025-07-27 17:14:49,816 - Main - WARNING - warning:311 - ⚠️ ⚠️ 执行前稽核发现问题: 4 个文件质量不达标
2025-07-27 17:14:49,817 - Main - INFO - info:307 - ℹ️ 开始执行任务: 前复权数据比较分析
2025-07-27 17:14:49,817 - Main - INFO - info:307 - ℹ️ 开始执行前复权数据比较分析任务
2025-07-27 17:14:49,818 - Main - INFO - info:307 - ℹ️ 将分析 1 只股票的前复权数据差异
2025-07-27 17:14:49,818 - Main - INFO - info:307 - ℹ️ 正在分析股票: 000617
2025-07-27 17:14:49,818 - Main - INFO - info:307 - ℹ️ 开始比较股票 000617 的前复权数据
2025-07-27 17:14:49,818 - Main - INFO - info:307 - ℹ️ 找到文件 - TDX: day_0_000617_20200101-20250724.txt
2025-07-27 17:14:49,818 - Main - INFO - info:307 - ℹ️ 找到文件 - 互联网: 未找到
2025-07-27 17:14:49,818 - Main - WARNING - warning:311 - ⚠️ ❌ 000617 分析失败: 数据文件不完整 - TDX: True, 互联网: False
2025-07-27 17:14:49,819 - Main - INFO - info:307 - ℹ️ 分析报告已保存到: H:/MPV1.17/T0002/signals/comparison_summary_20250727_171449.txt
2025-07-27 17:14:49,819 - Main - INFO - info:307 - ℹ️ 前复权数据比较分析完成: 0/1 成功 (0.0%)
2025-07-27 17:14:49,819 - Main - ERROR - error:315 - ❌ 任务执行失败: 前复权数据比较分析
2025-07-27 17:14:49,820 - Main - INFO - info:307 - ℹ️ 🔍 执行最终数据质量稽核汇总
2025-07-27 17:14:49,820 - Main - INFO - info:307 - ℹ️ 📊 对 5 个数据文件进行最终稽核
2025-07-27 17:14:50,118 - Main - INFO - info:307 - ℹ️ 📊 最终数据质量稽核汇总:
2025-07-27 17:14:50,119 - Main - INFO - info:307 - ℹ️   总文件数: 2
2025-07-27 17:14:50,119 - Main - INFO - info:307 - ℹ️   优秀文件: 1 (50.0%)
2025-07-27 17:14:50,119 - Main - INFO - info:307 - ℹ️   良好文件: 1 (50.0%)
2025-07-27 17:14:50,119 - Main - INFO - info:307 - ℹ️   较差文件: 0 (0.0%)
2025-07-27 17:14:50,119 - Main - INFO - info:307 - ℹ️   平均完整率: 95.89%
2025-07-27 17:14:50,119 - Main - INFO - info:307 - ℹ️ 任务执行完成 - 成功率: 66.7%
2025-07-27 17:14:50,120 - Main - INFO - info:307 - ℹ️ 开始清理应用程序资源
2025-07-27 17:14:50,120 - Main - INFO - info:307 - ℹ️ 开始清理股票处理器资源
2025-07-27 17:14:50,120 - utils.async_io_processor - INFO - cleanup:351 - 异步IO处理器资源清理完成
2025-07-27 17:14:50,120 - Main - INFO - info:307 - ℹ️ 股票处理器资源清理完成
2025-07-27 17:14:50,120 - Main - INFO - info:307 - ℹ️ 开始清理任务管理器资源
2025-07-27 17:14:50,120 - Main - INFO - info:307 - ℹ️ 任务管理器资源清理完成
2025-07-27 17:14:50,120 - Main - INFO - info:307 - ℹ️ 应用程序资源清理完成
