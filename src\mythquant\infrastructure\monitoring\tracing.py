#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
分布式追踪实现

提供分布式系统的请求追踪、链路分析等功能
"""

import time
import uuid
import threading
import logging
from typing import Dict, List, Any, Optional, ContextManager
from datetime import datetime
from dataclasses import dataclass, field
from contextlib import contextmanager
from collections import defaultdict
import functools

logger = logging.getLogger(__name__)


@dataclass
class TraceContext:
    """追踪上下文"""
    trace_id: str
    span_id: str
    parent_span_id: Optional[str] = None
    baggage: Dict[str, str] = field(default_factory=dict)
    
    def create_child_context(self) -> 'TraceContext':
        """创建子上下文"""
        return TraceContext(
            trace_id=self.trace_id,
            span_id=self._generate_span_id(),
            parent_span_id=self.span_id,
            baggage=self.baggage.copy()
        )
    
    def add_baggage(self, key: str, value: str):
        """添加行李数据"""
        self.baggage[key] = value
    
    def get_baggage(self, key: str) -> Optional[str]:
        """获取行李数据"""
        return self.baggage.get(key)
    
    @staticmethod
    def _generate_span_id() -> str:
        """生成跨度ID"""
        return uuid.uuid4().hex[:16]
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'trace_id': self.trace_id,
            'span_id': self.span_id,
            'parent_span_id': self.parent_span_id,
            'baggage': self.baggage
        }


@dataclass
class Span:
    """跨度"""
    trace_id: str
    span_id: str
    operation_name: str
    start_time: float
    end_time: Optional[float] = None
    parent_span_id: Optional[str] = None
    tags: Dict[str, Any] = field(default_factory=dict)
    logs: List[Dict[str, Any]] = field(default_factory=list)
    status: str = "started"  # started, finished, error
    
    @property
    def duration(self) -> Optional[float]:
        """持续时间"""
        if self.end_time is not None:
            return self.end_time - self.start_time
        return None
    
    def set_tag(self, key: str, value: Any):
        """设置标签"""
        self.tags[key] = value
    
    def set_tags(self, tags: Dict[str, Any]):
        """批量设置标签"""
        self.tags.update(tags)
    
    def log(self, message: str, level: str = "info", **kwargs):
        """添加日志"""
        log_entry = {
            'timestamp': time.time(),
            'message': message,
            'level': level,
            **kwargs
        }
        self.logs.append(log_entry)
    
    def finish(self, status: str = "finished"):
        """结束跨度"""
        self.end_time = time.time()
        self.status = status
    
    def mark_error(self, error: Exception):
        """标记错误"""
        self.status = "error"
        self.set_tag("error", True)
        self.set_tag("error.type", type(error).__name__)
        self.set_tag("error.message", str(error))
        self.log(f"Error: {error}", level="error")
    
    def to_dict(self) -> Dict[str, Any]:
        """转换为字典"""
        return {
            'trace_id': self.trace_id,
            'span_id': self.span_id,
            'operation_name': self.operation_name,
            'start_time': self.start_time,
            'end_time': self.end_time,
            'duration': self.duration,
            'parent_span_id': self.parent_span_id,
            'tags': self.tags,
            'logs': self.logs,
            'status': self.status
        }


class SpanContext:
    """跨度上下文管理器"""
    
    def __init__(self, span: Span, tracer: 'DistributedTracer'):
        self._span = span
        self._tracer = tracer
    
    def __enter__(self) -> Span:
        return self._span
    
    def __exit__(self, exc_type, exc_val, exc_tb):
        if exc_type is not None:
            self._span.mark_error(exc_val)
        
        self._span.finish()
        self._tracer._finish_span(self._span)


class DistributedTracer:
    """分布式追踪器"""
    
    def __init__(self, service_name: str = "mythquant"):
        self._service_name = service_name
        self._active_spans: Dict[str, Span] = {}
        self._finished_spans: List[Span] = []
        self._traces: Dict[str, List[Span]] = defaultdict(list)
        self._lock = threading.RLock()
        self._local = threading.local()
        
        # 配置
        self._max_spans = 10000
        self._sampling_rate = 1.0  # 100% 采样
    
    def start_trace(self, operation_name: str, 
                   parent_context: Optional[TraceContext] = None) -> TraceContext:
        """开始新的追踪"""
        if parent_context:
            trace_id = parent_context.trace_id
            parent_span_id = parent_context.span_id
        else:
            trace_id = self._generate_trace_id()
            parent_span_id = None
        
        span_id = self._generate_span_id()
        
        context = TraceContext(
            trace_id=trace_id,
            span_id=span_id,
            parent_span_id=parent_span_id
        )
        
        # 设置当前上下文
        self._set_current_context(context)
        
        return context
    
    def start_span(self, operation_name: str, 
                  parent_context: Optional[TraceContext] = None,
                  tags: Optional[Dict[str, Any]] = None) -> SpanContext:
        """开始新的跨度"""
        # 确定父上下文
        if parent_context is None:
            parent_context = self.get_current_context()
        
        if parent_context is None:
            # 创建新的追踪
            parent_context = self.start_trace(operation_name)
        
        # 创建跨度
        span = Span(
            trace_id=parent_context.trace_id,
            span_id=parent_context.span_id,
            operation_name=operation_name,
            start_time=time.time(),
            parent_span_id=parent_context.parent_span_id
        )
        
        # 设置标签
        span.set_tag("service.name", self._service_name)
        if tags:
            span.set_tags(tags)
        
        # 注册跨度
        with self._lock:
            self._active_spans[span.span_id] = span
            self._traces[span.trace_id].append(span)
        
        logger.debug(f"开始跨度: {operation_name} ({span.span_id})")
        
        return SpanContext(span, self)
    
    def get_current_context(self) -> Optional[TraceContext]:
        """获取当前追踪上下文"""
        return getattr(self._local, 'context', None)
    
    def set_current_context(self, context: TraceContext):
        """设置当前追踪上下文"""
        self._set_current_context(context)
    
    def inject_context(self, context: TraceContext) -> Dict[str, str]:
        """注入上下文到载体"""
        return {
            'trace-id': context.trace_id,
            'span-id': context.span_id,
            'parent-span-id': context.parent_span_id or '',
            'baggage': ','.join(f"{k}={v}" for k, v in context.baggage.items())
        }
    
    def extract_context(self, carrier: Dict[str, str]) -> Optional[TraceContext]:
        """从载体提取上下文"""
        trace_id = carrier.get('trace-id')
        span_id = carrier.get('span-id')
        
        if not trace_id or not span_id:
            return None
        
        parent_span_id = carrier.get('parent-span-id') or None
        
        # 解析行李数据
        baggage = {}
        baggage_str = carrier.get('baggage', '')
        if baggage_str:
            for item in baggage_str.split(','):
                if '=' in item:
                    key, value = item.split('=', 1)
                    baggage[key.strip()] = value.strip()
        
        return TraceContext(
            trace_id=trace_id,
            span_id=span_id,
            parent_span_id=parent_span_id,
            baggage=baggage
        )
    
    def get_trace(self, trace_id: str) -> List[Span]:
        """获取追踪的所有跨度"""
        with self._lock:
            return self._traces.get(trace_id, []).copy()
    
    def get_active_spans(self) -> List[Span]:
        """获取活跃跨度"""
        with self._lock:
            return list(self._active_spans.values())
    
    def get_finished_spans(self, limit: int = 100) -> List[Span]:
        """获取已完成跨度"""
        with self._lock:
            return self._finished_spans[-limit:]
    
    def get_trace_summary(self, trace_id: str) -> Optional[Dict[str, Any]]:
        """获取追踪摘要"""
        spans = self.get_trace(trace_id)
        if not spans:
            return None
        
        # 找到根跨度
        root_span = None
        for span in spans:
            if span.parent_span_id is None:
                root_span = span
                break
        
        if not root_span:
            root_span = spans[0]
        
        # 计算统计信息
        total_duration = root_span.duration or 0
        span_count = len(spans)
        error_count = sum(1 for span in spans if span.status == "error")
        
        return {
            'trace_id': trace_id,
            'root_operation': root_span.operation_name,
            'total_duration': total_duration,
            'span_count': span_count,
            'error_count': error_count,
            'start_time': root_span.start_time,
            'end_time': max((span.end_time or span.start_time) for span in spans),
            'service_name': self._service_name
        }
    
    def get_stats(self) -> Dict[str, Any]:
        """获取追踪统计"""
        with self._lock:
            active_count = len(self._active_spans)
            finished_count = len(self._finished_spans)
            trace_count = len(self._traces)
            
            return {
                'service_name': self._service_name,
                'active_spans': active_count,
                'finished_spans': finished_count,
                'total_traces': trace_count,
                'sampling_rate': self._sampling_rate,
                'max_spans': self._max_spans
            }
    
    def _finish_span(self, span: Span):
        """完成跨度"""
        with self._lock:
            # 从活跃跨度中移除
            if span.span_id in self._active_spans:
                del self._active_spans[span.span_id]
            
            # 添加到已完成跨度
            self._finished_spans.append(span)
            
            # 限制已完成跨度数量
            if len(self._finished_spans) > self._max_spans:
                self._finished_spans = self._finished_spans[-self._max_spans:]
        
        logger.debug(f"完成跨度: {span.operation_name} ({span.span_id}), "
                    f"耗时: {span.duration:.3f}s")
    
    def _set_current_context(self, context: TraceContext):
        """设置当前上下文"""
        self._local.context = context
    
    def _generate_trace_id(self) -> str:
        """生成追踪ID"""
        return uuid.uuid4().hex
    
    def _generate_span_id(self) -> str:
        """生成跨度ID"""
        return uuid.uuid4().hex[:16]


# 装饰器支持
def trace(operation_name: Optional[str] = None,
         tags: Optional[Dict[str, Any]] = None):
    """追踪装饰器"""
    def decorator(func):
        op_name = operation_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        def wrapper(*args, **kwargs):
            tracer = get_tracer()
            
            with tracer.start_span(op_name, tags=tags) as span:
                # 添加函数参数标签
                if args:
                    span.set_tag("args.count", len(args))
                if kwargs:
                    span.set_tag("kwargs.count", len(kwargs))
                
                try:
                    result = func(*args, **kwargs)
                    span.set_tag("success", True)
                    return result
                except Exception as e:
                    span.set_tag("success", False)
                    raise
        
        return wrapper
    
    return decorator


def trace_async(operation_name: Optional[str] = None,
               tags: Optional[Dict[str, Any]] = None):
    """异步追踪装饰器"""
    def decorator(func):
        op_name = operation_name or f"{func.__module__}.{func.__name__}"
        
        @functools.wraps(func)
        async def wrapper(*args, **kwargs):
            tracer = get_tracer()
            
            with tracer.start_span(op_name, tags=tags) as span:
                try:
                    result = await func(*args, **kwargs)
                    span.set_tag("success", True)
                    return result
                except Exception as e:
                    span.set_tag("success", False)
                    raise
        
        return wrapper
    
    return decorator


# 全局追踪器
_global_tracer: Optional[DistributedTracer] = None


def get_tracer() -> DistributedTracer:
    """获取全局追踪器"""
    global _global_tracer
    if _global_tracer is None:
        _global_tracer = DistributedTracer()
    return _global_tracer


def set_tracer(tracer: DistributedTracer):
    """设置全局追踪器"""
    global _global_tracer
    _global_tracer = tracer


# 便捷函数
def start_span(operation_name: str, 
              tags: Optional[Dict[str, Any]] = None) -> SpanContext:
    """开始新跨度"""
    return get_tracer().start_span(operation_name, tags=tags)


def get_current_trace_id() -> Optional[str]:
    """获取当前追踪ID"""
    context = get_tracer().get_current_context()
    return context.trace_id if context else None


def get_current_span_id() -> Optional[str]:
    """获取当前跨度ID"""
    context = get_tracer().get_current_context()
    return context.span_id if context else None


def add_baggage(key: str, value: str):
    """添加行李数据到当前上下文"""
    context = get_tracer().get_current_context()
    if context:
        context.add_baggage(key, value)


def get_baggage(key: str) -> Optional[str]:
    """从当前上下文获取行李数据"""
    context = get_tracer().get_current_context()
    return context.get_baggage(key) if context else None
