#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
流程优化器集成测试

验证流程优化器在实际程序中的集成效果和性能表现

位置: test_environments/integration_tests/configs/
作者: AI Assistant
创建时间: 2025-07-31
"""

import sys
import os
import subprocess
import time
import re
from datetime import datetime
from typing import List, Dict, Any

# 添加项目根目录到路径
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(os.path.dirname(os.path.dirname(current_dir)))
sys.path.append(project_root)

from utils.structured_output_formatter import (
    print_banner, print_main_process, print_step, print_result,
    print_info, print_warning, print_error, print_completion, print_stats_table
)


class FlowOptimizerIntegrationTester:
    """流程优化器集成测试器"""
    
    def __init__(self):
        self.project_root = project_root
        self.test_results = {}
        self.performance_metrics = {}
        
    def test_main_program_integration(self) -> bool:
        """测试主程序集成"""
        print_step("主程序集成测试", 1, 5)
        
        try:
            # 检查主程序是否正确导入流程优化器
            main_file = os.path.join(self.project_root, 'main.py')
            with open(main_file, 'r', encoding='utf-8') as f:
                main_content = f.read()
            
            # 验证导入
            if 'from utils.process_flow_optimizer import create_unified_flow_manager' in main_content:
                print_result("主程序已导入流程优化器", True, level=2)
            else:
                print_error("主程序未导入流程优化器", level=2)
                return False
            
            # 验证使用
            if 'flow_manager = create_unified_flow_manager()' in main_content:
                print_result("主程序已创建流程管理器", True, level=2)
            else:
                print_warning("主程序未使用流程管理器", level=2)
            
            return True
            
        except Exception as e:
            print_error(f"主程序集成测试失败: {e}", level=2)
            return False
    
    def test_application_core_integration(self) -> bool:
        """测试应用程序核心集成"""
        print_step("应用程序核心集成测试", 2, 5)
        
        try:
            # 检查应用程序核心是否集成流程优化器
            app_file = os.path.join(self.project_root, 'core', 'application.py')
            with open(app_file, 'r', encoding='utf-8') as f:
                app_content = f.read()
            
            # 验证导入
            if 'from utils.process_flow_optimizer import ProcessFlowOptimizer' in app_content:
                print_result("应用程序核心已导入流程优化器", True, level=2)
            else:
                print_error("应用程序核心未导入流程优化器", level=2)
                return False
            
            # 验证初始化
            if 'self.flow_optimizer = ProcessFlowOptimizer()' in app_content:
                print_result("应用程序核心已初始化流程优化器", True, level=2)
            else:
                print_warning("应用程序核心未初始化流程优化器", level=2)
            
            return True
            
        except Exception as e:
            print_error(f"应用程序核心集成测试失败: {e}", level=2)
            return False
    
    def test_cache_output_optimization(self) -> bool:
        """测试缓存输出优化"""
        print_step("缓存输出优化测试", 3, 5)
        
        try:
            # 检查GBBQ缓存是否已优化
            cache_files = [
                'cache/gbbq_cache.py',
                'gbbq_cache_solution.py'
            ]
            
            optimized_count = 0
            for cache_file in cache_files:
                file_path = os.path.join(self.project_root, cache_file)
                if os.path.exists(file_path):
                    with open(file_path, 'r', encoding='utf-8') as f:
                        content = f.read()
                    
                    # 检查是否使用结构化输出
                    if 'from utils.structured_output_formatter import' in content:
                        print_result(f"{cache_file} 已优化", True, level=2)
                        optimized_count += 1
                    else:
                        print_warning(f"{cache_file} 未完全优化", level=2)
            
            success_rate = optimized_count / len(cache_files)
            print_info(f"缓存文件优化率: {success_rate:.1%}", level=2)
            
            return success_rate >= 0.5  # 至少50%的文件已优化
            
        except Exception as e:
            print_error(f"缓存输出优化测试失败: {e}", level=2)
            return False
    
    def test_actual_program_execution(self) -> bool:
        """测试实际程序执行效果"""
        print_step("实际程序执行测试", 4, 5)
        
        try:
            print_info("启动主程序进行输出效果测试...", level=2)
            
            # 切换到项目根目录
            original_cwd = os.getcwd()
            os.chdir(self.project_root)
            
            try:
                # 运行主程序，捕获输出
                start_time = time.time()
                process = subprocess.Popen(
                    [sys.executable, 'main.py'],
                    stdout=subprocess.PIPE,
                    stderr=subprocess.PIPE,
                    text=True,
                    encoding='utf-8',
                    errors='replace',
                    cwd=self.project_root
                )
                
                # 等待程序执行，最多等待60秒
                try:
                    stdout, stderr = process.communicate(timeout=60)
                    execution_time = time.time() - start_time
                    return_code = process.returncode
                    
                    # 记录性能指标
                    self.performance_metrics['execution_time'] = execution_time
                    self.performance_metrics['return_code'] = return_code
                    
                    print_info(f"程序执行完成，耗时: {execution_time:.2f}秒", level=2)
                    
                    # 分析输出质量
                    output_quality = self._analyze_output_quality(stdout)
                    self.performance_metrics.update(output_quality)
                    
                    # 检查是否有严重错误（区分业务失败和系统错误）
                    if "Traceback" in stderr:
                        print_error("程序执行出现系统错误", level=2)
                        print_info(f"错误信息: {stderr[:200]}...", level=3)
                        return False
                    elif return_code != 0:
                        # 返回码非0可能是业务逻辑失败，不是系统错误
                        if "部分任务失败" in stdout:
                            print_warning("程序执行完成，但部分任务失败（业务逻辑）", level=2)
                        else:
                            print_error("程序执行失败", level=2)
                            return False
                    
                    print_result("程序执行成功", True, level=2)
                    return True
                    
                except subprocess.TimeoutExpired:
                    print_warning("程序执行超时，终止进程", level=2)
                    process.kill()
                    return False
                    
            finally:
                os.chdir(original_cwd)
                
        except Exception as e:
            print_error(f"实际程序执行测试失败: {e}", level=2)
            return False
    
    def test_performance_impact(self) -> bool:
        """测试性能影响"""
        print_step("性能影响测试", 5, 5)
        
        try:
            # 分析性能指标
            if not self.performance_metrics:
                print_warning("缺少性能数据，跳过性能分析", level=2)
                return True
            
            execution_time = self.performance_metrics.get('execution_time', 0)
            output_lines = self.performance_metrics.get('total_lines', 0)
            structured_lines = self.performance_metrics.get('structured_lines', 0)
            
            # 性能评估
            performance_good = True
            
            if execution_time > 120:  # 超过2分钟
                print_warning(f"执行时间较长: {execution_time:.2f}秒", level=2)
                performance_good = False
            else:
                print_result(f"执行时间合理: {execution_time:.2f}秒", True, level=2)
            
            if output_lines > 0:
                structured_ratio = structured_lines / output_lines
                if structured_ratio >= 0.7:  # 70%以上使用结构化输出
                    print_result(f"结构化输出比例良好: {structured_ratio:.1%}", True, level=2)
                else:
                    print_warning(f"结构化输出比例偏低: {structured_ratio:.1%}", level=2)
            
            return performance_good
            
        except Exception as e:
            print_error(f"性能影响测试失败: {e}", level=2)
            return False
    
    def _analyze_output_quality(self, output: str) -> Dict[str, Any]:
        """分析输出质量"""
        lines = output.split('\n')
        
        # 统计各种输出类型
        structured_patterns = [
            r'🚀.*',  # 主流程
            r'📊.*',  # 子流程
            r'🔍.*',  # 步骤
            r'✅.*',  # 成功结果
            r'❌.*',  # 失败结果
            r'⚠️.*',  # 警告
            r'ℹ️.*',  # 信息
        ]
        
        structured_lines = 0
        chaotic_patterns = 0
        
        for line in lines:
            line = line.strip()
            if not line:
                continue
                
            # 检查结构化输出
            for pattern in structured_patterns:
                if re.match(pattern, line):
                    structured_lines += 1
                    break
            
            # 检查混乱模式（连续的技术细节）
            if any(keyword in line for keyword in ['💾', '🧠', '📊 【', '💾 正在', '🔄 刷新']):
                chaotic_patterns += 1
        
        return {
            'total_lines': len([l for l in lines if l.strip()]),
            'structured_lines': structured_lines,
            'chaotic_patterns': chaotic_patterns,
            'chaos_ratio': chaotic_patterns / max(len(lines), 1)
        }
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        print_banner("流程优化器集成测试", "验证流程优化器在实际程序中的集成效果")
        
        print_info(f"项目根目录: {self.project_root}")
        print_info(f"测试时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        
        print_main_process("执行流程优化器集成测试")
        
        # 执行所有测试
        test_methods = [
            ('主程序集成', self.test_main_program_integration),
            ('应用程序核心集成', self.test_application_core_integration),
            ('缓存输出优化', self.test_cache_output_optimization),
            ('实际程序执行', self.test_actual_program_execution),
            ('性能影响', self.test_performance_impact)
        ]
        
        results = {}
        passed_tests = 0
        
        for test_name, test_method in test_methods:
            try:
                result = test_method()
                results[test_name] = result
                if result:
                    passed_tests += 1
            except Exception as e:
                print_error(f"测试 {test_name} 执行异常: {e}")
                results[test_name] = False
        
        # 生成测试报告
        total_tests = len(test_methods)
        pass_rate = passed_tests / total_tests
        
        print_main_process("集成测试结果汇总")
        
        # 显示详细结果
        for test_name, result in results.items():
            status = "✅ 通过" if result else "❌ 失败"
            print_result(f"{test_name}: {status}", result, level=1)
        
        # 显示统计信息
        stats = {
            "总测试数": total_tests,
            "通过测试数": passed_tests,
            "失败测试数": total_tests - passed_tests,
            "通过率": f"{pass_rate:.1%}",
            "执行时间": f"{self.performance_metrics.get('execution_time', 0):.2f}秒",
            "输出质量": f"{(1 - self.performance_metrics.get('chaos_ratio', 0)):.1%}"
        }
        
        print_stats_table("集成测试统计", stats)
        
        # 判断整体结果
        overall_success = pass_rate >= 0.8 and self.performance_metrics.get('chaos_ratio', 1) < 0.3
        
        completion_message = "流程优化器集成成功！" if overall_success else "流程优化器集成需要改进"
        print_completion(completion_message, overall_success, stats)
        
        return {
            'overall_success': overall_success,
            'pass_rate': pass_rate,
            'performance_metrics': self.performance_metrics,
            'detailed_results': results
        }


def main():
    """主函数"""
    tester = FlowOptimizerIntegrationTester()
    result = tester.run_comprehensive_test()
    
    # 根据测试结果返回适当的退出码
    exit_code = 0 if result['overall_success'] else 1
    
    print_info(f"测试完成，退出码: {exit_code}")
    
    return exit_code


if __name__ == '__main__':
    exit_code = main()
    sys.exit(exit_code)
