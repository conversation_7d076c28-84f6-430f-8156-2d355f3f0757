# MythQuant pytdx 1分钟数据使用指南

## 🎯 功能概述

本系统集成了pytdx通达信数据接口，支持：
- ✅ 真正的1分钟K线数据
- ✅ 自动服务器发现和优选
- ✅ 多种数据频率（1min, 5min, 15min, 30min, 60min）
- ✅ 无需MongoDB数据库
- ✅ 完全免费使用

## 🚀 快速开始

### 方法1：一键快速启动（推荐新手）

```bash
python start_pytdx.py
```

这个脚本会：
1. 自动检查pytdx安装
2. 自动发现最佳服务器
3. 使用默认参数下载数据
4. 一键完成整个流程

### 方法2：交互式完整功能

```bash
python main_with_pytdx.py
```

提供完整的交互菜单：
- 选择数据源（pytdx或传统）
- 查看服务器状态
- 重新发现服务器
- 自定义下载参数

### 方法3：传统方式（5分钟数据）

```bash
python main.py
```

使用原有的BaoStock + AKShare数据源。

## 📋 使用步骤详解

### 第一步：安装依赖

```bash
# 安装pytdx（如果未安装）
pip install pytdx
```

### 第二步：首次运行

首次运行时，系统会自动：

1. **服务器发现**：
   ```
   🔍 正在查找最佳通达信服务器...
   ✅ 发现 5 个可用服务器
   🏆 最佳服务器: 通达信主服务器1 (**************:7709)
   ```

2. **配置保存**：
   - 自动保存到 `tdx_servers.json`
   - 缓存5分钟，避免频繁测试
   - 24小时后自动过期重新检测

### 第三步：数据下载

支持的参数：
- **股票代码**：如 000617, 600036
- **日期范围**：YYYYMMDD格式，如 20250724
- **数据频率**：1min, 5min, 15min, 30min, 60min

示例：
```
📊 pytdx数据下载配置:
请输入股票代码 (如000617): 000617
请输入开始日期 (如20250724): 20250724
请输入结束日期 (如20250724): 20250724
请输入数据频率 (1min/5min/15min/30min/60min): 1min
```

### 第四步：查看结果

下载成功后，文件保存在：
```
H:/MPV1.17/T0002/signals/min_0_000617_20250724-20250724_来源pytdx.txt
```

文件格式：
```
股票编码|时间|买卖差|当日收盘价C|前复权收盘价C|路径总长|主买|主卖
000617|20250724 09:31|0.0|12.345|12.345|0.0|0.0|0.0
000617|20250724 09:32|0.0|12.350|12.350|0.0|0.0|0.0
...
```

## 🔧 高级功能

### 服务器管理

#### 查看服务器状态
```bash
python main_with_pytdx.py
# 选择选项 3
```

显示：
- 当前可用服务器列表
- 响应时间排序
- 连接状态信息

#### 重新发现服务器
```bash
python main_with_pytdx.py
# 选择选项 4
```

适用于：
- 服务器连接变慢
- 发现新的服务器
- 定期维护更新

### 批量下载

可以通过编程方式批量下载：

```python
from utils.pytdx_downloader import PytdxDownloader

downloader = PytdxDownloader()

# 批量下载多只股票
stock_codes = ['000617', '600036', '000001']
results = downloader.batch_download(
    stock_codes=stock_codes,
    start_date='20250724',
    end_date='20250724',
    frequency='1min'
)
```

## ⚠️ 注意事项

### 数据限制
- **单次获取量**：最多8000条数据
- **历史范围**：1分钟数据通常只有1-2年
- **网络依赖**：需要连接通达信服务器

### 服务器稳定性
- 服务器IP可能变化
- 建议定期更新服务器列表
- 保留多个备用服务器

### 数据质量
- 当前使用原始价格
- 后续可集成复权因子计算
- 建议与其他数据源交叉验证

## 🛠️ 故障排除

### 常见问题

#### 1. pytdx未安装
```
❌ pytdx库未安装
💡 请先安装: pip install pytdx
```

**解决方案**：
```bash
pip install pytdx
```

#### 2. 无可用服务器
```
❌ 未发现可用服务器
```

**解决方案**：
1. 检查网络连接
2. 重新运行服务器发现
3. 手动添加服务器IP

#### 3. 连接超时
```
❌ 连接服务器超时
```

**解决方案**：
1. 尝试其他服务器
2. 检查防火墙设置
3. 稍后重试

#### 4. 数据获取失败
```
❌ 数据获取失败
```

**解决方案**：
1. 检查股票代码格式
2. 确认日期范围合理
3. 尝试其他频率

### 日志查看

详细日志保存在：
```
logs/mythquant_YYYYMMDD_HHMMSS.log
```

可以查看具体的错误信息和调试信息。

## 📊 性能对比

| 数据源 | 1分钟数据 | 历史范围 | 稳定性 | 速度 |
|--------|-----------|----------|--------|------|
| **pytdx** | ✅ 支持 | 1-2年 | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| **BaoStock** | ❌ 不支持 | 5年+ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐ |
| **AKShare** | ⚠️ 不稳定 | 有限 | ⭐⭐ | ⭐⭐ |

## 🎯 使用建议

### 适用场景
- **短期策略**：使用pytdx的1分钟数据
- **长期回测**：使用BaoStock的5分钟数据
- **数据验证**：多数据源交叉验证

### 最佳实践
1. **首次使用**：运行 `start_pytdx.py` 快速体验
2. **日常使用**：使用 `main_with_pytdx.py` 完整功能
3. **定期维护**：每周更新一次服务器列表
4. **数据备份**：重要数据及时备份

## 📞 技术支持

如遇问题，请：
1. 查看日志文件
2. 检查网络连接
3. 尝试重新发现服务器
4. 参考故障排除指南

---

🎉 **现在您可以享受真正的1分钟数据了！**
