#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
DDD架构 - 配置管理器

提供统一的配置管理功能
"""

import os
from typing import Any, Dict, List, Optional
from pathlib import Path


class ConfigManager:
    """配置管理器"""
    
    def __init__(self):
        self._config = self._load_default_config()
    
    def _load_default_config(self) -> Dict[str, Any]:
        """加载默认配置"""
        return {
            'system': {
                'debug_mode': False,
                'log_level': 'INFO',
                'max_workers': 4,
                'enable_console': False  # 禁用控制台日志输出
            },
            'data_sources': {
                'pytdx': {
                    'enabled': True,
                    'timeout': 30
                }
            },
            'caching': {
                'enabled': True,
                'ttl': 3600
            },
            'monitoring': {
                'enabled': True,
                'metrics_interval': 60
            }
        }
    
    def get(self, key: str, default: Any = None) -> Any:
        """获取配置值"""
        keys = key.split('.')
        value = self._config
        
        try:
            for k in keys:
                value = value[k]
            return value
        except (KeyError, TypeError):
            return default
    
    def set(self, key: str, value: Any) -> None:
        """设置配置值"""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
    
    def get_config(self) -> Dict[str, Any]:
        """获取完整配置"""
        return self._config.copy()
    
    def validate_config(self) -> bool:
        """验证配置"""
        # 简单的配置验证
        required_keys = [
            'system.log_level',
            'system.max_workers'
        ]

        for key in required_keys:
            if self.get(key) is None:
                return False

        return True

    def get_tdx_path(self) -> str:
        """获取TDX路径"""
        return self.get('data_sources.tdx.path', '')

    def get_output_path(self) -> str:
        """获取输出路径"""
        return self.get('output.base_path', './output')

    def get_task_configs(self) -> List[Dict[str, Any]]:
        """获取任务配置（从user_config.py读取）"""
        try:
            import user_config
            time_ranges = getattr(user_config, 'time_ranges', {})

            task_configs = []

            # 分钟级数据任务配置
            minute_config = time_ranges.get('internet_minute', {})
            if minute_config.get('enabled', False):
                # 转换日期格式：20250101 -> 2025-01-01
                start_date = minute_config.get('start_date', '20250101')
                end_date = minute_config.get('end_date', '20250807')  # 修复：使用正确的默认值

                start_time = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
                end_time = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

                task_configs.append({
                    'name': '分钟级数据生成',
                    'data_type': 'internet_minute',  # 修复：使用internet_minute类型以启用四步流程
                    'enabled': True,
                    'start_time': start_time,
                    'end_time': end_time,
                    'use_multithread': True,
                    'max_workers': 4,
                    'frequency': minute_config.get('frequency', '1min')
                })

            # 日级数据任务配置
            daily_config = time_ranges.get('internet_daily', {})
            if daily_config.get('enabled', False):
                # 转换日期格式
                start_date = daily_config.get('start_date', '20240101')
                end_date = daily_config.get('end_date', '20241231')

                start_time = f"{start_date[:4]}-{start_date[4:6]}-{start_date[6:8]}"
                end_time = f"{end_date[:4]}-{end_date[4:6]}-{end_date[6:8]}"

                task_configs.append({
                    'name': '日级数据生成',
                    'data_type': 'daily',
                    'enabled': True,
                    'start_time': start_time,
                    'end_time': end_time,
                    'use_multithread': True,
                    'max_workers': 4,
                    'frequency': 'daily'
                })

            # 如果没有启用的任务，返回默认配置（使用当前时间）
            if not task_configs:
                import datetime
                today = datetime.datetime.now()
                yesterday = today - datetime.timedelta(days=1)

                task_configs = [
                    {
                        'name': '分钟级数据生成',
                        'data_type': 'minute',
                        'enabled': True,
                        'start_time': yesterday.strftime('%Y-%m-%d'),
                        'end_time': today.strftime('%Y-%m-%d'),
                        'use_multithread': True,
                        'max_workers': 4,
                        'frequency': '1min'
                    }
                ]

            return task_configs

        except Exception as e:
            # 回退到默认配置（使用当前时间）
            import datetime
            today = datetime.datetime.now()
            yesterday = today - datetime.timedelta(days=1)

            return [
                {
                    'name': '分钟级数据生成',
                    'data_type': 'minute',
                    'enabled': True,
                    'start_time': yesterday.strftime('%Y-%m-%d'),
                    'end_time': today.strftime('%Y-%m-%d'),
                    'use_multithread': True,
                    'max_workers': 4,
                    'frequency': '1min'
                }
            ]

    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        return {
            'tdx_path': self.get_tdx_path(),
            'output_dir': self.get_output_path(),
            'target_stocks_count': 1,  # 默认值
            'has_target_stocks': True,
            'config_loaded': True
        }

    def get_verbose_config(self, category: str) -> bool:
        """获取特定类别的详细日志配置（兼容性方法）"""
        category_switches = {
            'FORWARD_ADJ': True,
            'PERFORMANCE': True,
            'DATA_PROC': True,
            'CACHE': True,
            'CRITICAL': True,
            'GENERAL': True
        }
        return category_switches.get(category, True)

    def is_verbose_enabled(self) -> bool:
        """检查是否启用详细日志（兼容性方法）"""
        return True  # 默认启用

    def highlight_critical_info(self, message: str) -> str:
        """高亮显示关键信息"""
        return f"🔥 {message}"

    def get_time_ranges(self) -> Dict[str, Any]:
        """获取时间范围配置"""
        try:
            import user_config
            return getattr(user_config, 'time_ranges', {})
        except Exception:
            return {}

    def get_internet_data_config(self) -> Dict[str, Any]:
        """获取互联网数据配置（向后兼容）"""
        try:
            import user_config
            if hasattr(user_config, 'internet_data_config'):
                return user_config.internet_data_config
            else:
                # 返回默认配置
                return {
                    'enabled': True,
                    'data_sources': {
                        'pytdx': {'enabled': True, 'priority': 1, 'auto_server_discovery': True},
                        'akshare': {'enabled': True, 'priority': 2},
                        'baostock': {'enabled': True, 'priority': 3},
                        'yfinance': {'enabled': False, 'priority': 4}
                    },
                    'download_params': {
                        'start_date': '20150101',
                        'end_date': 'current',
                        'request_delay': 0.5,
                        'max_retries': 3,
                        'timeout': 30
                    },
                    'output': {
                        'directory': './output',
                        'filename_suffix': '_来源互联网',
                        'encoding': 'utf-8'
                    }
                }
        except Exception as e:
            # 返回默认配置
            return {
                'enabled': True,
                'data_sources': {
                    'pytdx': {'enabled': True, 'priority': 1},
                    'akshare': {'enabled': True, 'priority': 2}
                },
                'download_params': {
                    'start_date': '20150101',
                    'end_date': 'current'
                }
            }


# 全局配置管理器实例
config_manager = ConfigManager()
