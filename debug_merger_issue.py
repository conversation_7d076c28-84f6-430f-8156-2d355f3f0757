#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试高效合并器问题
用于确认为什么高效合并器没有被调用
"""

import os
import sys

# 设置强制测试环境标识
os.environ['FORCE_TEST_ENV'] = '1'
os.environ['AUGMENT_MODE'] = '1'

def debug_merger_issue():
    """调试合并器问题"""
    print("🔍 调试高效合并器问题")
    print("=" * 50)
    
    try:
        # 1. 检查高效合并器是否可导入
        print("1. 检查高效合并器导入...")
        try:
            from utils.efficient_data_merger import EfficientDataMerger
            print("✅ 高效合并器导入成功")
            
            # 创建实例
            merger = EfficientDataMerger()
            print("✅ 高效合并器实例化成功")
            
        except ImportError as e:
            print(f"❌ 高效合并器导入失败: {e}")
            return
        except Exception as e:
            print(f"❌ 高效合并器实例化失败: {e}")
            return
        
        # 2. 检查测试环境配置
        print("\n2. 检查测试环境配置...")
        from user_config import detect_environment, get_environment_config
        
        current_env = detect_environment()
        print(f"当前环境: {current_env}")
        
        if current_env == 'test':
            test_config = get_environment_config('minute_data_download')
            input_path = test_config.get('input_data_path', '')
            print(f"测试输入路径: {input_path}")
            
            # 检查测试文件
            test_files = test_config.get('test_files', {})
            for stock_code, file_config in test_files.items():
                working_file = file_config.get('working_file', '')
                if working_file:
                    working_file_path = os.path.join(input_path, working_file)
                    exists = os.path.exists(working_file_path)
                    print(f"测试文件 {stock_code}: {working_file} - {'存在' if exists else '不存在'}")
        
        # 3. 模拟missing_structure数据
        print("\n3. 模拟missing_structure数据...")
        
        # 创建模拟的missing_periods
        mock_missing_periods = [
            {
                'start_time': '202503201030',
                'end_time': '202503201130',
                'missing_count': 60,
                'period_type': 'continuous'
            },
            {
                'start_time': '202507031430',
                'end_time': '202507031500',
                'missing_count': 30,
                'period_type': 'continuous'
            }
        ]
        
        print(f"模拟缺失时间段: {len(mock_missing_periods)} 个")
        for period in mock_missing_periods:
            print(f"  • {period['start_time']} - {period['end_time']} ({period['missing_count']}分钟)")
        
        # 4. 测试高效合并器的时间戳解析
        print("\n4. 测试时间戳解析...")
        try:
            missing_timestamps = merger._build_missing_timestamp_set(mock_missing_periods)
            print(f"✅ 时间戳解析成功: {len(missing_timestamps)} 个时间点")
            
            # 显示前几个时间戳
            sorted_timestamps = sorted(list(missing_timestamps))[:10]
            print(f"前10个时间戳: {sorted_timestamps}")
            
        except Exception as e:
            print(f"❌ 时间戳解析失败: {e}")
            import traceback
            traceback.print_exc()
        
        # 5. 检查测试文件的时间戳格式
        print("\n5. 检查测试文件时间戳格式...")
        if current_env == 'test':
            test_config = get_environment_config('minute_data_download')
            input_path = test_config.get('input_data_path', '')
            test_files = test_config.get('test_files', {})
            
            for stock_code, file_config in test_files.items():
                working_file = file_config.get('working_file', '')
                if working_file:
                    working_file_path = os.path.join(input_path, working_file)
                    if os.path.exists(working_file_path):
                        try:
                            with open(working_file_path, 'r', encoding='utf-8') as f:
                                lines = f.readlines()
                            
                            print(f"文件 {working_file}:")
                            print(f"  总行数: {len(lines)}")
                            
                            # 分析前几行的时间戳格式
                            for i, line in enumerate(lines[1:6], 1):  # 跳过表头，看前5行
                                if line.strip():
                                    parts = line.strip().split('|')
                                    if len(parts) >= 2:
                                        timestamp = parts[1]
                                        print(f"  第{i}行时间戳: {timestamp} (长度: {len(timestamp)})")
                        
                        except Exception as e:
                            print(f"❌ 读取测试文件失败: {e}")
        
        # 6. 测试现有时间戳索引构建
        print("\n6. 测试现有时间戳索引构建...")
        if current_env == 'test':
            test_config = get_environment_config('minute_data_download')
            input_path = test_config.get('input_data_path', '')
            test_files = test_config.get('test_files', {})
            
            for stock_code, file_config in test_files.items():
                working_file = file_config.get('working_file', '')
                if working_file:
                    working_file_path = os.path.join(input_path, working_file)
                    if os.path.exists(working_file_path):
                        try:
                            existing_timestamps = merger._build_existing_timestamp_index(working_file_path)
                            print(f"✅ 现有时间戳索引构建成功: {len(existing_timestamps)} 个时间点")
                            
                            # 显示前几个时间戳
                            sorted_existing = sorted(list(existing_timestamps))[:10]
                            print(f"前10个现有时间戳: {sorted_existing}")
                            
                        except Exception as e:
                            print(f"❌ 现有时间戳索引构建失败: {e}")
                            import traceback
                            traceback.print_exc()
        
        print("\n🎯 调试完成")
        
    except Exception as e:
        print(f"❌ 调试过程异常: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    debug_merger_issue()
