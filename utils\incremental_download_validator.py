#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
增量下载验证器
实现智能的增量下载逻辑，确保数据一致性
"""

import os
import re
import pandas as pd
from datetime import datetime
from typing import Optional, Dict, Tuple, Any
from decimal import Decimal

from core.logging_service import logging_service


class IncrementalDownloadValidator:
    """增量下载验证器"""
    
    def __init__(self):
        self.smart_logger = logging_service
        
        # 文件名格式解析正则（支持时间戳后缀）
        self.filename_patterns = {
            'minute': r'(\d+min)_0_(\d{6})_(\d{8})-(\d{8})_来源互联网(?:（\d{12}）)?\.txt',
            'daily': r'day_0_(\d{6})_(\d{8})-(\d{8})_来源互联网(?:（\d{12}）)?\.txt'
        }
    
    def parse_filename_info(self, filename: str) -> Optional[Dict[str, Any]]:
        """
        解析文件名信息
        
        Args:
            filename: 文件名
            
        Returns:
            解析结果字典或None
        """
        try:
            # 尝试分钟级文件格式
            minute_match = re.match(self.filename_patterns['minute'], filename)
            if minute_match:
                frequency_str, stock_code, start_date, end_date = minute_match.groups()
                
                # 提取频率数字
                freq_match = re.match(r'(\d+)min', frequency_str)
                if freq_match:
                    frequency = int(freq_match.group(1))
                else:
                    frequency = 1  # 默认1分钟
                
                return {
                    'type': 'minute',
                    'frequency': frequency,
                    'frequency_str': frequency_str,
                    'stock_code': stock_code,
                    'start_date': start_date,
                    'end_date': end_date,
                    'data_level': f'{frequency}min'
                }
            
            # 尝试日线文件格式
            daily_match = re.match(self.filename_patterns['daily'], filename)
            if daily_match:
                stock_code, start_date, end_date = daily_match.groups()
                
                return {
                    'type': 'daily',
                    'frequency': 'daily',
                    'frequency_str': 'daily',
                    'stock_code': stock_code,
                    'start_date': start_date,
                    'end_date': end_date,
                    'data_level': 'daily'
                }
            
            self.smart_logger.verbose_log('warning', f"无法解析文件名格式: {filename}")
            return None

        except Exception as e:
            self.smart_logger.log_error(f"解析文件名失败: {e}")
            return None
    
    def get_last_record_from_file(self, filepath: str) -> Optional[Dict[str, Any]]:
        """
        获取文件中的最后一条记录
        
        Args:
            filepath: 文件路径
            
        Returns:
            最后一条记录的字典或None
        """
        try:
            if not os.path.exists(filepath):
                return None
            
            # 读取文件最后几行（避免读取整个大文件）
            with open(filepath, 'r', encoding='utf-8') as f:
                lines = f.readlines()
            
            if len(lines) < 2:  # 至少需要表头和一条数据
                return None
            
            # 获取表头和最后一行数据
            header_line = lines[0].strip()
            last_data_line = lines[-1].strip()
            
            if not last_data_line:
                return None
            
            # 解析表头
            headers = header_line.split('|')
            data_values = last_data_line.split('|')
            
            if len(headers) != len(data_values):
                self.smart_logger.verbose_log('warning', f"数据列数不匹配: 表头{len(headers)}列，数据{len(data_values)}列")
                return None

            # 构建记录字典
            record = {}
            for i, header in enumerate(headers):
                if i < len(data_values):
                    record[header] = data_values[i]

            self.smart_logger.verbose_log('info', f"获取最后一条记录: 时间={record.get('时间', 'N/A')}, 前复权价={record.get('前复权收盘价C', 'N/A')}")
            return record

        except Exception as e:
            self.smart_logger.log_error(f"读取文件最后记录失败: {e}")
            return None
    
    def validate_time_format_consistency(self, file_record: Dict[str, Any], 
                                       api_record: Dict[str, Any], 
                                       data_level: str) -> bool:
        """
        验证时间格式一致性
        
        Args:
            file_record: 文件中的记录
            api_record: API获取的记录
            data_level: 数据级别（1min, 5min, daily等）
            
        Returns:
            是否一致
        """
        try:
            file_time = file_record.get('时间', '')
            api_time = api_record.get('时间', '')
            
            self.smart_logger.verbose_log('info', f"时间格式对比: 文件={file_time}, API={api_time}, 级别={data_level}")

            # 根据数据级别验证时间格式
            if data_level.endswith('min'):
                # 分钟级数据应该有12位时间格式 YYYYMMDDHHMM
                expected_length = 12

                # 检查文件时间格式
                if len(str(file_time).replace('.0', '')) != expected_length:
                    self.smart_logger.verbose_log('warning', f"文件时间格式不正确: {file_time}, 期望{expected_length}位")
                    return False

                # 检查API时间格式
                if len(str(api_time)) != expected_length:
                    self.smart_logger.verbose_log('warning', f"API时间格式不正确: {api_time}, 期望{expected_length}位")
                    return False

                # 检查是否包含分钟信息（不应该以0000结尾）
                if str(file_time).replace('.0', '').endswith('0000'):
                    self.smart_logger.verbose_log('warning', f"文件时间缺少分钟信息: {file_time}")
                    return False

            elif data_level == 'daily':
                # 日线数据应该有8位时间格式 YYYYMMDD
                expected_length = 8

                if len(str(file_time).replace('.0', '')) != expected_length:
                    self.smart_logger.verbose_log('warning', f"文件时间格式不正确: {file_time}, 期望{expected_length}位")
                    return False

            # 时间值比较（去除小数点）
            file_time_clean = str(file_time).replace('.0', '')
            api_time_clean = str(api_time)

            if file_time_clean != api_time_clean:
                self.smart_logger.verbose_log('warning', f"时间值不匹配: 文件={file_time_clean}, API={api_time_clean}")
                return False

            self.smart_logger.verbose_log('info', "✅ 时间格式一致性验证通过")
            return True

        except Exception as e:
            self.smart_logger.log_error(f"时间格式验证失败: {e}")
            return False
    
    def validate_price_consistency(self, file_record: Dict[str, Any], 
                                 api_record: Dict[str, Any], 
                                 tolerance: float = 0.001) -> bool:
        """
        验证价格一致性
        
        Args:
            file_record: 文件中的记录
            api_record: API获取的记录
            tolerance: 容差
            
        Returns:
            是否一致
        """
        try:
            file_price = file_record.get('前复权收盘价C', '0')
            api_price = api_record.get('前复权收盘价C', '0')
            
            # 转换为Decimal进行精确比较
            file_price_decimal = Decimal(str(file_price))
            api_price_decimal = Decimal(str(api_price))
            
            diff = abs(file_price_decimal - api_price_decimal)
            tolerance_decimal = Decimal(str(tolerance))
            
            if diff > tolerance_decimal:
                self.smart_logger.verbose_log('warning', f"价格不一致: 文件={file_price}, API={api_price}, 差异={diff}")
                return False

            self.smart_logger.verbose_log('info', "✅ 价格一致性验证通过")
            return True

        except Exception as e:
            self.smart_logger.log_error(f"价格一致性验证失败: {e}")
            return False
    
    def should_use_incremental_download(self, filepath: str, 
                                      latest_api_record: Dict[str, Any]) -> Tuple[bool, str]:
        """
        判断是否应该使用增量下载
        
        Args:
            filepath: 现有文件路径
            latest_api_record: API获取的最新记录
            
        Returns:
            (是否使用增量下载, 原因说明)
        """
        try:
            # 解析文件名信息
            filename = os.path.basename(filepath)
            file_info = self.parse_filename_info(filename)
            
            if not file_info:
                return False, "无法解析文件名格式"
            
            # 获取文件最后记录
            last_file_record = self.get_last_record_from_file(filepath)
            
            if not last_file_record:
                return False, "无法读取文件最后记录"
            
            # 验证时间格式一致性
            time_consistent = self.validate_time_format_consistency(
                last_file_record, latest_api_record, file_info['data_level']
            )
            
            if not time_consistent:
                return False, "时间格式不一致，需要全量重新下载"
            
            # 验证价格一致性
            price_consistent = self.validate_price_consistency(
                last_file_record, latest_api_record
            )
            
            if not price_consistent:
                return False, "价格数据不一致，需要全量重新下载"
            
            # 注意：这里只验证数据一致性，不重复输出判断结果
            # 最终的增量下载判断由调用方的智能分析器统一处理
            return True, "数据一致，可以增量下载"

        except Exception as e:
            error_msg = f"增量下载判断失败: {e}"
            self.smart_logger.log_error(error_msg)
            return False, error_msg
    
    def get_incremental_date_range(self, filepath: str) -> Optional[Tuple[str, str]]:
        """
        获取增量下载的日期范围
        
        Args:
            filepath: 现有文件路径
            
        Returns:
            (开始日期, 结束日期) 或 None
        """
        try:
            # 获取文件最后记录
            last_record = self.get_last_record_from_file(filepath)
            
            if not last_record:
                return None
            
            last_time = last_record.get('时间', '')
            if not last_time:
                return None
            
            # 解析时间，获取日期部分
            time_str = str(last_time).replace('.0', '')
            
            if len(time_str) >= 8:
                last_date = time_str[:8]  # YYYYMMDD
                
                # 增量下载从下一天开始
                from datetime import datetime, timedelta
                last_datetime = datetime.strptime(last_date, '%Y%m%d')
                next_datetime = last_datetime + timedelta(days=1)
                start_date = next_datetime.strftime('%Y%m%d')
                
                # 结束日期使用当前日期
                end_date = datetime.now().strftime('%Y%m%d')
                
                return start_date, end_date
            
            return None
            
        except Exception as e:
            self.smart_logger.log_error(f"获取增量日期范围失败: {e}")
            return None
