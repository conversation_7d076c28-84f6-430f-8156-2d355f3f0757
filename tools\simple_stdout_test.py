#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
最简单的stdout测试，不导入任何项目模块
"""

import sys

print("🔍 最简单的stdout测试")
print(f"stdout类型: {type(sys.stdout)}")
print(f"stdout编码: {getattr(sys.stdout, 'encoding', 'unknown')}")
print(f"stdout是否关闭: {sys.stdout.closed if hasattr(sys.stdout, 'closed') else 'unknown'}")

# 测试基本print
try:
    print("✅ 基本print测试成功")
except Exception as e:
    print(f"❌ 基本print测试失败: {e}")

print("🏁 简单测试完成")
