#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数学算法模块测试

测试数学算法的正确性、边界条件和异常处理
"""

import pytest
import math
from decimal import Decimal
from src.mythquant.domain.algorithms.mathematical import (
    linear_interpolation, exponential_smoothing, moving_average,
    weighted_average, compound_growth_rate, standard_deviation,
    variance, correlation, covariance, percentile, z_score,
    min_max_normalize, robust_scale, cumulative_sum, diff, pct_change
)
from src.mythquant.domain.exceptions import (
    InsufficientDataError, InvalidParameterError
)


class TestLinearInterpolation:
    """线性插值测试"""
    
    def test_basic_interpolation(self):
        """基本插值测试"""
        result = linear_interpolation(1.5, 1.0, 10.0, 2.0, 20.0)
        assert result == 15.0
    
    def test_boundary_values(self):
        """边界值测试"""
        # 插值点在端点
        assert linear_interpolation(1.0, 1.0, 10.0, 2.0, 20.0) == 10.0
        assert linear_interpolation(2.0, 1.0, 10.0, 2.0, 20.0) == 20.0
    
    def test_extrapolation(self):
        """外推测试"""
        # 插值点在范围外
        result = linear_interpolation(0.5, 1.0, 10.0, 2.0, 20.0)
        assert result == 5.0
    
    def test_zero_division_error(self):
        """除零错误测试"""
        with pytest.raises(InvalidParameterError):
            linear_interpolation(1.5, 1.0, 10.0, 1.0, 20.0)


class TestExponentialSmoothing:
    """指数平滑测试"""
    
    def test_basic_smoothing(self):
        """基本平滑测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        result = exponential_smoothing(data, 0.5)
        
        assert len(result) == len(data)
        assert result[0] == 1.0  # 第一个值不变
        assert result[1] == 1.5  # 0.5 * 2.0 + 0.5 * 1.0
    
    def test_alpha_boundary(self):
        """alpha边界测试"""
        data = [1.0, 2.0, 3.0]
        
        # alpha = 1.0 (完全跟随新值)
        result = exponential_smoothing(data, 1.0)
        assert result == data
        
        # alpha接近0 (几乎不变)
        result = exponential_smoothing(data, 0.01)
        assert abs(result[1] - 1.01) < 1e-10
    
    def test_invalid_alpha(self):
        """无效alpha测试"""
        data = [1.0, 2.0, 3.0]
        
        with pytest.raises(InvalidParameterError):
            exponential_smoothing(data, 0.0)
        
        with pytest.raises(InvalidParameterError):
            exponential_smoothing(data, 1.5)
    
    def test_empty_data(self):
        """空数据测试"""
        with pytest.raises(InsufficientDataError):
            exponential_smoothing([], 0.5)


class TestMovingAverage:
    """移动平均测试"""
    
    def test_basic_moving_average(self):
        """基本移动平均测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        result = moving_average(data, 3)
        
        expected = [2.0, 3.0, 4.0]  # (1+2+3)/3, (2+3+4)/3, (3+4+5)/3
        assert result == expected
    
    def test_window_equals_data_length(self):
        """窗口等于数据长度测试"""
        data = [1.0, 2.0, 3.0]
        result = moving_average(data, 3)
        
        assert len(result) == 1
        assert result[0] == 2.0
    
    def test_insufficient_data(self):
        """数据不足测试"""
        data = [1.0, 2.0]
        
        with pytest.raises(InsufficientDataError):
            moving_average(data, 3)
    
    def test_invalid_window(self):
        """无效窗口测试"""
        data = [1.0, 2.0, 3.0]
        
        with pytest.raises(InvalidParameterError):
            moving_average(data, 0)
        
        with pytest.raises(InvalidParameterError):
            moving_average(data, -1)


class TestWeightedAverage:
    """加权平均测试"""
    
    def test_basic_weighted_average(self):
        """基本加权平均测试"""
        data = [1.0, 2.0, 3.0]
        weights = [1.0, 2.0, 3.0]
        
        result = weighted_average(data, weights)
        expected = (1*1 + 2*2 + 3*3) / (1 + 2 + 3)  # 14/6
        
        assert abs(result - expected) < 1e-10
    
    def test_equal_weights(self):
        """等权重测试"""
        data = [1.0, 2.0, 3.0]
        weights = [1.0, 1.0, 1.0]
        
        result = weighted_average(data, weights)
        expected = sum(data) / len(data)
        
        assert abs(result - expected) < 1e-10
    
    def test_mismatched_lengths(self):
        """长度不匹配测试"""
        data = [1.0, 2.0, 3.0]
        weights = [1.0, 2.0]
        
        with pytest.raises(InvalidParameterError):
            weighted_average(data, weights)
    
    def test_zero_weight_sum(self):
        """权重和为零测试"""
        data = [1.0, 2.0, 3.0]
        weights = [1.0, -1.0, 0.0]
        
        with pytest.raises(InvalidParameterError):
            weighted_average(data, weights)


class TestCompoundGrowthRate:
    """复合增长率测试"""
    
    def test_basic_cagr(self):
        """基本CAGR测试"""
        result = compound_growth_rate(100.0, 200.0, 2)
        expected = (200.0 / 100.0) ** (1/2) - 1  # 约0.414
        
        assert abs(result - expected) < 1e-10
    
    def test_no_growth(self):
        """无增长测试"""
        result = compound_growth_rate(100.0, 100.0, 5)
        assert abs(result) < 1e-10
    
    def test_negative_growth(self):
        """负增长测试"""
        result = compound_growth_rate(200.0, 100.0, 2)
        expected = (100.0 / 200.0) ** (1/2) - 1  # 约-0.293
        
        assert abs(result - expected) < 1e-10
    
    def test_invalid_parameters(self):
        """无效参数测试"""
        with pytest.raises(InvalidParameterError):
            compound_growth_rate(0.0, 100.0, 2)
        
        with pytest.raises(InvalidParameterError):
            compound_growth_rate(100.0, -50.0, 2)
        
        with pytest.raises(InvalidParameterError):
            compound_growth_rate(100.0, 200.0, 0)


class TestStatisticalFunctions:
    """统计函数测试"""
    
    def test_standard_deviation(self):
        """标准差测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        
        # 总体标准差
        result_pop = standard_deviation(data, ddof=0)
        expected_pop = math.sqrt(2.0)  # 方差为2.0
        assert abs(result_pop - expected_pop) < 1e-10
        
        # 样本标准差
        result_sample = standard_deviation(data, ddof=1)
        expected_sample = math.sqrt(2.5)  # 方差为2.5
        assert abs(result_sample - expected_sample) < 1e-10
    
    def test_variance(self):
        """方差测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        
        result = variance(data, ddof=0)
        expected = 2.0  # ((1-3)² + (2-3)² + (3-3)² + (4-3)² + (5-3)²) / 5
        
        assert abs(result - expected) < 1e-10
    
    def test_correlation(self):
        """相关系数测试"""
        x = [1.0, 2.0, 3.0, 4.0, 5.0]
        y = [2.0, 4.0, 6.0, 8.0, 10.0]  # 完全正相关
        
        result = correlation(x, y)
        assert abs(result - 1.0) < 1e-10
        
        # 完全负相关
        y_neg = [10.0, 8.0, 6.0, 4.0, 2.0]
        result_neg = correlation(x, y_neg)
        assert abs(result_neg - (-1.0)) < 1e-10
    
    def test_percentile(self):
        """百分位数测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        
        assert percentile(data, 0) == 1.0
        assert percentile(data, 50) == 3.0
        assert percentile(data, 100) == 5.0
    
    def test_z_score(self):
        """Z分数测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        result = z_score(data)
        
        # Z分数的均值应该接近0
        mean_z = sum(result) / len(result)
        assert abs(mean_z) < 1e-10
        
        # Z分数的标准差应该接近1
        std_z = standard_deviation(result, ddof=1)
        assert abs(std_z - 1.0) < 1e-10


class TestNormalization:
    """标准化测试"""
    
    def test_min_max_normalize(self):
        """最小-最大标准化测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        result = min_max_normalize(data)
        
        assert min(result) == 0.0
        assert max(result) == 1.0
        assert result[2] == 0.5  # 中间值
    
    def test_min_max_normalize_custom_range(self):
        """自定义范围标准化测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        result = min_max_normalize(data, feature_range=(-1.0, 1.0))
        
        assert min(result) == -1.0
        assert max(result) == 1.0
        assert result[2] == 0.0  # 中间值
    
    def test_constant_data_normalization(self):
        """常数数据标准化测试"""
        data = [5.0, 5.0, 5.0, 5.0]
        result = min_max_normalize(data)
        
        # 所有值应该是目标范围的中点
        assert all(x == 0.5 for x in result)
    
    def test_robust_scale(self):
        """鲁棒标准化测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0, 100.0]  # 包含异常值
        result = robust_scale(data)
        
        # 中位数对应的值应该接近0
        median_index = len(data) // 2
        assert abs(result[median_index]) < 0.5


class TestSequenceOperations:
    """序列操作测试"""
    
    def test_cumulative_sum(self):
        """累积和测试"""
        data = [1.0, 2.0, 3.0, 4.0, 5.0]
        result = cumulative_sum(data)
        
        expected = [1.0, 3.0, 6.0, 10.0, 15.0]
        assert result == expected
    
    def test_diff(self):
        """差分测试"""
        data = [1.0, 3.0, 6.0, 10.0, 15.0]
        result = diff(data)
        
        expected = [2.0, 3.0, 4.0, 5.0]
        assert result == expected
    
    def test_pct_change(self):
        """百分比变化测试"""
        data = [100.0, 110.0, 121.0, 133.1]
        result = pct_change(data)
        
        expected = [0.1, 0.1, 0.1]  # 10%增长
        for i, val in enumerate(result):
            assert abs(val - expected[i]) < 1e-10
    
    def test_pct_change_with_zero(self):
        """包含零值的百分比变化测试"""
        data = [0.0, 1.0, 2.0]
        result = pct_change(data)
        
        assert result[0] == float('inf')  # 从0到1
        assert result[1] == 1.0  # 从1到2，100%增长


if __name__ == "__main__":
    pytest.main([__file__])
