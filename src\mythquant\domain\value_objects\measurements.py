#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
度量值对象

用于表示各种度量值的值对象
"""

from decimal import Decimal, ROUND_HALF_UP
from dataclasses import dataclass
from typing import Union, Optional
from ..exceptions import InvalidPriceError, NegativePriceError, ZeroPriceError, InvalidVolumeError, NegativeVolumeError
from ...shared.patterns.architectural_patterns import ValueObject


@dataclass(frozen=True)
class Price(ValueObject):
    """价格值对象"""
    
    value: Decimal
    currency: str = "CNY"
    
    def __post_init__(self):
        if not isinstance(self.value, Decimal):
            object.__setattr__(self, 'value', Decimal(str(self.value)))
        
        if self.value < 0:
            raise NegativePriceError(self.value)
        
        # 价格精度：保留3位小数
        rounded_value = self.value.quantize(Decimal('0.001'), rounding=ROUND_HALF_UP)
        object.__setattr__(self, 'value', rounded_value)
    
    @classmethod
    def from_float(cls, value: float, currency: str = "CNY") -> 'Price':
        """从浮点数创建价格"""
        return cls(Decimal(str(value)), currency)
    
    @classmethod
    def from_string(cls, value: str, currency: str = "CNY") -> 'Price':
        """从字符串创建价格"""
        try:
            return cls(Decimal(value), currency)
        except (ValueError, TypeError) as e:
            raise InvalidPriceError(value, f"无法解析价格: {e}")
    
    @classmethod
    def zero(cls, currency: str = "CNY") -> 'Price':
        """创建零价格"""
        return cls(Decimal('0'), currency)
    
    def add(self, other: 'Price') -> 'Price':
        """价格相加"""
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return Price(self.value + other.value, self.currency)
    
    def subtract(self, other: 'Price') -> 'Price':
        """价格相减"""
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        result_value = self.value - other.value
        if result_value < 0:
            raise NegativePriceError(result_value)
        return Price(result_value, self.currency)
    
    def multiply(self, factor: Union[Decimal, float, int]) -> 'Price':
        """价格乘法"""
        if isinstance(factor, (float, int)):
            factor = Decimal(str(factor))
        result_value = self.value * factor
        if result_value < 0:
            raise NegativePriceError(result_value)
        return Price(result_value, self.currency)
    
    def divide(self, divisor: Union[Decimal, float, int]) -> 'Price':
        """价格除法"""
        if isinstance(divisor, (float, int)):
            divisor = Decimal(str(divisor))
        if divisor == 0:
            raise ZeroDivisionError("除数不能为零")
        result_value = self.value / divisor
        if result_value < 0:
            raise NegativePriceError(result_value)
        return Price(result_value, self.currency)
    
    def percentage_change(self, other: 'Price') -> 'Percentage':
        """计算价格变化百分比"""
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        if other.value == 0:
            raise ZeroPriceError(other.value)
        
        change = (self.value - other.value) / other.value * 100
        return Percentage(change)
    
    def is_zero(self) -> bool:
        """是否为零价格"""
        return self.value == 0
    
    def is_positive(self) -> bool:
        """是否为正价格"""
        return self.value > 0
    
    def to_float(self) -> float:
        """转换为浮点数"""
        return float(self.value)
    
    def __str__(self) -> str:
        return f"{self.value} {self.currency}"
    
    def __lt__(self, other: 'Price') -> bool:
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return self.value < other.value
    
    def __le__(self, other: 'Price') -> bool:
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return self.value <= other.value
    
    def __gt__(self, other: 'Price') -> bool:
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return self.value > other.value
    
    def __ge__(self, other: 'Price') -> bool:
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return self.value >= other.value


@dataclass(frozen=True)
class Volume(ValueObject):
    """成交量值对象"""
    
    value: int
    
    def __post_init__(self):
        if not isinstance(self.value, int):
            try:
                object.__setattr__(self, 'value', int(self.value))
            except (ValueError, TypeError):
                raise InvalidVolumeError(self.value, "成交量必须是整数")
        
        if self.value < 0:
            raise NegativeVolumeError(self.value)
    
    @classmethod
    def from_string(cls, value: str) -> 'Volume':
        """从字符串创建成交量"""
        try:
            return cls(int(value))
        except (ValueError, TypeError) as e:
            raise InvalidVolumeError(value, f"无法解析成交量: {e}")
    
    @classmethod
    def zero(cls) -> 'Volume':
        """创建零成交量"""
        return cls(0)
    
    def add(self, other: 'Volume') -> 'Volume':
        """成交量相加"""
        return Volume(self.value + other.value)
    
    def subtract(self, other: 'Volume') -> 'Volume':
        """成交量相减"""
        result_value = self.value - other.value
        if result_value < 0:
            raise NegativeVolumeError(result_value)
        return Volume(result_value)
    
    def multiply(self, factor: Union[int, float]) -> 'Volume':
        """成交量乘法"""
        result_value = int(self.value * factor)
        if result_value < 0:
            raise NegativeVolumeError(result_value)
        return Volume(result_value)
    
    def is_zero(self) -> bool:
        """是否为零成交量"""
        return self.value == 0
    
    def is_positive(self) -> bool:
        """是否为正成交量"""
        return self.value > 0
    
    def __str__(self) -> str:
        return f"{self.value:,}"
    
    def __lt__(self, other: 'Volume') -> bool:
        return self.value < other.value
    
    def __le__(self, other: 'Volume') -> bool:
        return self.value <= other.value
    
    def __gt__(self, other: 'Volume') -> bool:
        return self.value > other.value
    
    def __ge__(self, other: 'Volume') -> bool:
        return self.value >= other.value


@dataclass(frozen=True)
class Percentage(ValueObject):
    """百分比值对象"""
    
    value: Decimal
    
    def __post_init__(self):
        if not isinstance(self.value, Decimal):
            object.__setattr__(self, 'value', Decimal(str(self.value)))
        
        # 百分比精度：保留6位小数
        rounded_value = self.value.quantize(Decimal('0.000001'), rounding=ROUND_HALF_UP)
        object.__setattr__(self, 'value', rounded_value)
    
    @classmethod
    def from_float(cls, value: float) -> 'Percentage':
        """从浮点数创建百分比"""
        return cls(Decimal(str(value)))
    
    @classmethod
    def from_string(cls, value: str) -> 'Percentage':
        """从字符串创建百分比"""
        try:
            # 处理百分号
            if value.endswith('%'):
                value = value[:-1]
            return cls(Decimal(value))
        except (ValueError, TypeError) as e:
            raise ValueError(f"无法解析百分比: {e}")
    
    @classmethod
    def from_ratio(cls, numerator: Union[Decimal, float], 
                   denominator: Union[Decimal, float]) -> 'Percentage':
        """从比率创建百分比"""
        if denominator == 0:
            raise ZeroDivisionError("分母不能为零")
        
        if isinstance(numerator, float):
            numerator = Decimal(str(numerator))
        if isinstance(denominator, float):
            denominator = Decimal(str(denominator))
        
        ratio = numerator / denominator * 100
        return cls(ratio)
    
    @classmethod
    def zero(cls) -> 'Percentage':
        """创建零百分比"""
        return cls(Decimal('0'))
    
    def add(self, other: 'Percentage') -> 'Percentage':
        """百分比相加"""
        return Percentage(self.value + other.value)
    
    def subtract(self, other: 'Percentage') -> 'Percentage':
        """百分比相减"""
        return Percentage(self.value - other.value)
    
    def multiply(self, factor: Union[Decimal, float, int]) -> 'Percentage':
        """百分比乘法"""
        if isinstance(factor, (float, int)):
            factor = Decimal(str(factor))
        return Percentage(self.value * factor)
    
    def to_decimal(self) -> Decimal:
        """转换为小数"""
        return self.value / 100
    
    def to_float(self) -> float:
        """转换为浮点数"""
        return float(self.value)
    
    def is_zero(self) -> bool:
        """是否为零百分比"""
        return self.value == 0
    
    def is_positive(self) -> bool:
        """是否为正百分比"""
        return self.value > 0
    
    def is_negative(self) -> bool:
        """是否为负百分比"""
        return self.value < 0
    
    def abs(self) -> 'Percentage':
        """绝对值"""
        return Percentage(abs(self.value))
    
    def __str__(self) -> str:
        return f"{self.value}%"
    
    def __lt__(self, other: 'Percentage') -> bool:
        return self.value < other.value
    
    def __le__(self, other: 'Percentage') -> bool:
        return self.value <= other.value
    
    def __gt__(self, other: 'Percentage') -> bool:
        return self.value > other.value
    
    def __ge__(self, other: 'Percentage') -> bool:
        return self.value >= other.value


@dataclass(frozen=True)
class Amount(ValueObject):
    """金额值对象"""
    
    value: Decimal
    currency: str = "CNY"
    
    def __post_init__(self):
        if not isinstance(self.value, Decimal):
            object.__setattr__(self, 'value', Decimal(str(self.value)))
        
        # 金额精度：保留2位小数
        rounded_value = self.value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
        object.__setattr__(self, 'value', rounded_value)
    
    @classmethod
    def from_float(cls, value: float, currency: str = "CNY") -> 'Amount':
        """从浮点数创建金额"""
        return cls(Decimal(str(value)), currency)
    
    @classmethod
    def from_string(cls, value: str, currency: str = "CNY") -> 'Amount':
        """从字符串创建金额"""
        try:
            return cls(Decimal(value), currency)
        except (ValueError, TypeError) as e:
            raise ValueError(f"无法解析金额: {e}")
    
    @classmethod
    def zero(cls, currency: str = "CNY") -> 'Amount':
        """创建零金额"""
        return cls(Decimal('0'), currency)
    
    def add(self, other: 'Amount') -> 'Amount':
        """金额相加"""
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return Amount(self.value + other.value, self.currency)
    
    def subtract(self, other: 'Amount') -> 'Amount':
        """金额相减"""
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return Amount(self.value - other.value, self.currency)
    
    def multiply(self, factor: Union[Decimal, float, int]) -> 'Amount':
        """金额乘法"""
        if isinstance(factor, (float, int)):
            factor = Decimal(str(factor))
        return Amount(self.value * factor, self.currency)
    
    def divide(self, divisor: Union[Decimal, float, int]) -> 'Amount':
        """金额除法"""
        if isinstance(divisor, (float, int)):
            divisor = Decimal(str(divisor))
        if divisor == 0:
            raise ZeroDivisionError("除数不能为零")
        return Amount(self.value / divisor, self.currency)
    
    def is_zero(self) -> bool:
        """是否为零金额"""
        return self.value == 0
    
    def is_positive(self) -> bool:
        """是否为正金额"""
        return self.value > 0
    
    def is_negative(self) -> bool:
        """是否为负金额"""
        return self.value < 0
    
    def abs(self) -> 'Amount':
        """绝对值"""
        return Amount(abs(self.value), self.currency)
    
    def to_float(self) -> float:
        """转换为浮点数"""
        return float(self.value)
    
    def __str__(self) -> str:
        return f"{self.value} {self.currency}"
    
    def __lt__(self, other: 'Amount') -> bool:
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return self.value < other.value
    
    def __le__(self, other: 'Amount') -> bool:
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return self.value <= other.value
    
    def __gt__(self, other: 'Amount') -> bool:
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return self.value > other.value
    
    def __ge__(self, other: 'Amount') -> bool:
        if self.currency != other.currency:
            raise ValueError(f"货币不匹配: {self.currency} vs {other.currency}")
        return self.value >= other.value


# 工厂函数
def create_price(value: Union[str, float, int, Decimal], currency: str = "CNY") -> Price:
    """创建价格的便捷函数"""
    if isinstance(value, str):
        return Price.from_string(value, currency)
    elif isinstance(value, float):
        return Price.from_float(value, currency)
    else:
        return Price(Decimal(str(value)), currency)


def create_volume(value: Union[str, int]) -> Volume:
    """创建成交量的便捷函数"""
    if isinstance(value, str):
        return Volume.from_string(value)
    else:
        return Volume(value)


def create_percentage(value: Union[str, float, Decimal]) -> Percentage:
    """创建百分比的便捷函数"""
    if isinstance(value, str):
        return Percentage.from_string(value)
    elif isinstance(value, float):
        return Percentage.from_float(value)
    else:
        return Percentage(value)


def create_amount(value: Union[str, float, int, Decimal], currency: str = "CNY") -> Amount:
    """创建金额的便捷函数"""
    if isinstance(value, str):
        return Amount.from_string(value, currency)
    elif isinstance(value, float):
        return Amount.from_float(value, currency)
    else:
        return Amount(Decimal(str(value)), currency)
