#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
直接性能测试脚本
直接导入主程序模块进行性能测试，避免subprocess问题
"""

import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, Any
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)

# 可选依赖：psutil用于系统监控
try:
    import psutil
    PSUTIL_AVAILABLE = True
except ImportError:
    PSUTIL_AVAILABLE = False


class DirectPerformanceTest:
    """直接性能测试器"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.results_dir = os.path.join(self.project_root, "benchmark_results")
        
        # 确保结果目录存在
        os.makedirs(self.results_dir, exist_ok=True)
    
    def get_system_info(self) -> Dict[str, Any]:
        """获取系统信息"""
        info = {
            'timestamp': datetime.now().isoformat(),
            'python_version': sys.version,
            'platform': sys.platform
        }
        
        if PSUTIL_AVAILABLE:
            try:
                info.update({
                    'cpu_count': psutil.cpu_count(),
                    'memory_total_gb': psutil.virtual_memory().total / (1024**3),
                    'memory_available_gb': psutil.virtual_memory().available / (1024**3),
                    'cpu_percent': psutil.cpu_percent(interval=1),
                    'psutil_available': True
                })
            except Exception as e:
                info['psutil_error'] = str(e)
                info['psutil_available'] = False
        else:
            info['psutil_available'] = False
        
        return info
    
    def run_direct_test(self) -> Dict[str, Any]:
        """直接运行性能测试"""
        logger.info("🚀 开始直接性能测试")
        
        # 获取测试前系统状态
        pre_test_info = self.get_system_info()
        
        start_time = time.time()
        start_datetime = datetime.now()
        
        try:
            # 直接导入并运行主程序
            import main_v20230219_optimized
            
            # 运行主函数
            main_v20230219_optimized.main()
            
            end_time = time.time()
            end_datetime = datetime.now()
            execution_time = end_time - start_time
            
            # 获取测试后系统状态
            post_test_info = self.get_system_info()
            
            # 验证输出文件
            output_verification = self._verify_output_files()
            
            test_result = {
                'test_name': '直接性能测试',
                'timestamp': start_datetime.isoformat(),
                'execution_time': execution_time,
                'success': True,
                'output_verification': output_verification,
                'system_info': {
                    'pre_test': pre_test_info,
                    'post_test': post_test_info
                }
            }
            
            logger.info(f"✅ 直接性能测试完成 - {execution_time:.2f}秒")
            return test_result
            
        except Exception as e:
            end_time = time.time()
            execution_time = end_time - start_time
            
            logger.error(f"❌ 直接性能测试失败: {e}")
            
            return {
                'test_name': '直接性能测试',
                'timestamp': start_datetime.isoformat(),
                'execution_time': execution_time,
                'success': False,
                'error': str(e),
                'system_info': {'pre_test': pre_test_info}
            }
    
    def _verify_output_files(self) -> Dict[str, Any]:
        """验证输出文件"""
        output_dir = "H:/MPV1.17/T0002/signals/"
        expected_files = ["day_0_000617_20150101-20250731.txt"]
        
        verification = {
            'files_checked': len(expected_files),
            'files_found': 0,
            'files_valid': 0,
            'total_size_bytes': 0,
            'total_lines': 0,
            'file_details': {}
        }
        
        for file_name in expected_files:
            file_path = os.path.join(output_dir, file_name)
            
            if os.path.exists(file_path):
                verification['files_found'] += 1
                
                try:
                    file_stats = os.stat(file_path)
                    file_size = file_stats.st_size
                    verification['total_size_bytes'] += file_size
                    
                    # 验证文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    line_count = len(lines)
                    verification['total_lines'] += line_count
                    
                    header_correct = len(lines) > 0 and "股票编码|时间|买卖差" in lines[0]
                    has_data = line_count > 1
                    
                    # 验证前复权数据
                    forward_adj_correct = False
                    if has_data and len(lines) > 1:
                        try:
                            data_line = lines[1].strip().split('|')
                            if len(data_line) >= 5:
                                original_price = float(data_line[3])
                                adjusted_price = float(data_line[4])
                                forward_adj_correct = abs(original_price - adjusted_price) > 0.01
                        except (ValueError, IndexError):
                            pass
                    
                    is_valid = header_correct and has_data and forward_adj_correct
                    if is_valid:
                        verification['files_valid'] += 1
                    
                    verification['file_details'][file_name] = {
                        'exists': True,
                        'size_bytes': file_size,
                        'size_mb': file_size / (1024 * 1024),
                        'line_count': line_count,
                        'header_correct': header_correct,
                        'has_data': has_data,
                        'forward_adj_correct': forward_adj_correct,
                        'valid': is_valid
                    }
                    
                except Exception as e:
                    verification['file_details'][file_name] = {
                        'exists': True,
                        'error': str(e),
                        'valid': False
                    }
            else:
                verification['file_details'][file_name] = {
                    'exists': False,
                    'valid': False
                }
        
        return verification
    
    def run_performance_baseline(self) -> Dict[str, Any]:
        """建立性能基线"""
        logger.info("📊 建立性能基线")
        
        # 运行3次测试
        test_results = []
        execution_times = []
        
        for i in range(3):
            logger.info(f"🔄 执行第 {i+1}/3 次测试")
            
            result = self.run_direct_test()
            test_results.append(result)
            
            if result.get('success', False):
                execution_times.append(result['execution_time'])
            
            # 如果不是最后一次，等待5秒
            if i < 2:
                logger.info("⏳ 等待5秒...")
                time.sleep(5)
        
        # 分析结果
        successful_tests = [r for r in test_results if r.get('success', False)]
        
        if not successful_tests:
            return {
                'baseline_established': False,
                'error': '所有基线测试都失败了',
                'test_results': test_results
            }
        
        baseline = {
            'baseline_established': True,
            'timestamp': datetime.now().isoformat(),
            'test_count': len(test_results),
            'successful_count': len(successful_tests),
            'success_rate': len(successful_tests) / len(test_results),
            'performance_metrics': {
                'avg_execution_time': sum(execution_times) / len(execution_times) if execution_times else 0,
                'min_execution_time': min(execution_times) if execution_times else 0,
                'max_execution_time': max(execution_times) if execution_times else 0,
                'execution_times': execution_times,
                'performance_stable': (max(execution_times) - min(execution_times)) / sum(execution_times) * len(execution_times) < 0.1 if execution_times else False
            },
            'output_verification': successful_tests[0]['output_verification'],
            'system_info': successful_tests[0]['system_info']['pre_test'],
            'test_results': test_results
        }
        
        # 保存基线数据
        baseline_file = os.path.join(self.results_dir, "performance_baseline.json")
        with open(baseline_file, 'w', encoding='utf-8') as f:
            json.dump(baseline, f, indent=2, ensure_ascii=False, default=str)
        
        logger.info(f"📊 基线基准已建立并保存到: {baseline_file}")
        
        return baseline
    
    def generate_performance_report(self, baseline: Dict[str, Any]) -> str:
        """生成性能报告"""
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        report_file = os.path.join(self.results_dir, f"performance_baseline_report_{timestamp}.txt")
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("MythQuant 性能基线报告\n")
            f.write("=" * 60 + "\n")
            f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
            
            if baseline.get('baseline_established'):
                metrics = baseline['performance_metrics']
                f.write("📊 性能基线数据:\n")
                f.write(f"  测试次数: {baseline['test_count']}\n")
                f.write(f"  成功次数: {baseline['successful_count']}\n")
                f.write(f"  成功率: {baseline['success_rate']:.1%}\n")
                f.write(f"  平均执行时间: {metrics['avg_execution_time']:.2f} 秒\n")
                f.write(f"  最短执行时间: {metrics['min_execution_time']:.2f} 秒\n")
                f.write(f"  最长执行时间: {metrics['max_execution_time']:.2f} 秒\n")
                f.write(f"  性能稳定性: {'良好' if metrics['performance_stable'] else '需要关注'}\n\n")
                
                # 历史性能对比
                f.write("📈 历史性能对比:\n")
                f.write("  原始版本: 118.79 秒\n")
                f.write("  缓存优化后: 49.47 秒 (+58.4%)\n")
                f.write("  向量化优化后: 51.46 秒 (+56.7%)\n")
                f.write("  数据类型优化后: 51.79 秒 (+56.4%)\n")
                f.write("  分块处理优化后: 50.95 秒 (+57.1%)\n")
                f.write("  异步IO优化后: 50.45 秒 (+57.5%)\n")
                f.write("  错误处理优化后: 50.61 秒 (+57.4%)\n")
                f.write(f"  当前基线: {metrics['avg_execution_time']:.2f} 秒 "
                       f"({(118.79 - metrics['avg_execution_time']) / 118.79 * 100:+.1f}%)\n\n")
                
                # 输出验证信息
                verification = baseline['output_verification']
                f.write("📋 输出文件验证:\n")
                f.write(f"  检查文件数: {verification['files_checked']}\n")
                f.write(f"  找到文件数: {verification['files_found']}\n")
                f.write(f"  有效文件数: {verification['files_valid']}\n")
                f.write(f"  总文件大小: {verification.get('total_size_bytes', 0):,} bytes\n")
                f.write(f"  总数据行数: {verification.get('total_lines', 0):,} 行\n\n")
                
                # 系统信息
                sys_info = baseline['system_info']
                f.write("💻 系统环境:\n")
                f.write(f"  Python版本: {sys_info.get('python_version', 'Unknown')}\n")
                f.write(f"  平台: {sys_info.get('platform', 'Unknown')}\n")
                if sys_info.get('psutil_available'):
                    f.write(f"  CPU核心数: {sys_info.get('cpu_count', 'Unknown')}\n")
                    f.write(f"  总内存: {sys_info.get('memory_total_gb', 0):.1f} GB\n")
                    f.write(f"  可用内存: {sys_info.get('memory_available_gb', 0):.1f} GB\n")
                
                # 性能建议
                f.write("\n💡 性能建议:\n")
                if metrics['avg_execution_time'] < 60:
                    f.write("  ✅ 当前性能表现良好，执行时间在1分钟以内\n")
                else:
                    f.write("  ⚠️ 执行时间较长，建议进一步优化\n")
                
                if metrics['performance_stable']:
                    f.write("  ✅ 性能稳定性良好，执行时间波动较小\n")
                else:
                    f.write("  ⚠️ 性能波动较大，建议检查系统资源使用情况\n")
                
            else:
                f.write("❌ 基准测试失败\n")
                f.write(f"错误: {baseline.get('error', '未知错误')}\n")
        
        logger.info(f"📋 性能报告已生成: {report_file}")
        return report_file


def main():
    """主函数"""
    print("🚀 MythQuant 直接性能测试系统")
    print("=" * 60)
    
    # 初始化测试器
    tester = DirectPerformanceTest()
    
    # 建立性能基线
    print("📊 建立性能基线...")
    baseline = tester.run_performance_baseline()
    
    # 生成报告
    print("📋 生成性能报告...")
    report_file = tester.generate_performance_report(baseline)
    
    # 输出摘要
    print("\n📈 性能基线摘要:")
    if baseline.get('baseline_established'):
        metrics = baseline['performance_metrics']
        print(f"   平均执行时间: {metrics['avg_execution_time']:.2f} 秒")
        print(f"   性能稳定性: {'良好' if metrics['performance_stable'] else '需要关注'}")
        print(f"   成功率: {baseline['success_rate']:.1%}")
        print(f"   输出文件验证: {baseline['output_verification']['files_valid']}/{baseline['output_verification']['files_checked']} 通过")
        
        # 计算相对于原始版本的改进
        improvement = (118.79 - metrics['avg_execution_time']) / 118.79 * 100
        print(f"   相对原始版本改进: {improvement:.1f}%")
    else:
        print("   ❌ 基准测试失败")
    
    print(f"\n📊 详细报告: {report_file}")
    
    return baseline.get('baseline_established', False)


if __name__ == '__main__':
    try:
        success = main()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
