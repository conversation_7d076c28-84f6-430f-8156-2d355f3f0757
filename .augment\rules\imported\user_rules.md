---
type: "always_apply"
---

# MythQuant项目用户规则

## 交互偏好
- 始终使用中文简体回应
- 当用户提及功能描述时，AI应自动定位到正确的文件，而不是等待用户@特定文件
- 当用户@错误的文件时，AI应主动识别并询问是否要修改正确的文件
- 优先修改已经模块化拆分的文件，而不是主文件

## 技术偏好
- 避免使用人工距离补偿来改进代码精度，因为这可能掩盖潜在问题
- 研究时应完整阅读文章，避免断章取义
- 所有金融计算必须使用Decimal进行高精度计算
- 始终遵循测试驱动的验证流程

## 输出文件命名规范
- 日级前复权结果文件应命名为 'day_{股票代码}_{开始日期}-{结束日期}.txt'
- 分钟级文件应命名为 'min_{频率}_{股票代码}_{开始日期}-{结束日期}.txt'

## 代码修改工作流
1. 进行依赖关系分析
2. 识别功能所属的正确模块
3. 创建测试脚本验证修改
4. 提供备份建议（如果修改核心文件）

## 架构升级偏好 (2025-08-06)
- 优先采用渐进式升级，避免破坏性变更
- 保持用户配置接口的简单性（如user_config.py）
- 新架构必须完全向后兼容，现有代码无需修改
- 使用门面模式隐藏内部复杂性
- 重视用户体验，技术升级对用户透明

## 问题解决偏好 (2025-08-06)
- 重视"测试好用，生产报错"类型的问题
- 要求进行端到端验证，不仅仅是单元测试
- 发现问题时要求系统性分析根本原因
- 优先解决影响用户体验的问题
- 重视配置管理的统一性和一致性

## 质量标准偏好 (2025-08-06)
- 代码修改后必须在生产环境中验证
- 重要功能必须有完整的测试覆盖
- 配置变更必须保持向后兼容
- 错误处理必须用户友好
- 文档和代码必须同步更新