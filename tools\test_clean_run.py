#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Clean run test - no patches, just observe
"""

import os
import sys

# Add project root to path
current_dir = os.path.dirname(os.path.abspath(__file__))
project_root = os.path.dirname(current_dir)
sys.path.insert(0, project_root)

def run_clean_test():
    """Run a clean test without any patches"""
    print("Clean Run Test - No Patches")
    print("=" * 80)
    
    try:
        # Just import and run main
        from main import main
        
        print("Starting main.py execution...")
        result = main()
        
        print(f"\nMain completed with result: {result}")
        return True
        
    except Exception as e:
        print(f"Test failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function"""
    success = run_clean_test()
    
    print(f"\nClean test completed: {'SUCCESS' if success else 'FAILED'}")
    
    return 0 if success else 1

if __name__ == '__main__':
    sys.exit(main())
