#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
轻量级主程序入口测试脚本
对比测试新旧两种入口的功能一致性和性能表现
"""

import sys
import os
import time
import json
from datetime import datetime
from typing import Dict, Any, List
import logging

# 添加项目根目录到路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 配置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
logger = logging.getLogger(__name__)


class LightweightMainTester:
    """轻量级主程序测试器"""
    
    def __init__(self):
        self.project_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
        self.results_dir = os.path.join(self.project_root, "test_results")
        self.output_dir = "H:/MPV1.17/T0002/signals/"
        
        # 确保结果目录存在
        os.makedirs(self.results_dir, exist_ok=True)
        
        # 测试结果存储
        self.test_results = {}
    
    def backup_output_files(self) -> Dict[str, str]:
        """备份现有输出文件"""
        logger.info("📦 备份现有输出文件...")
        
        backup_timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        backup_mapping = {}
        
        expected_files = ["day_0_000617_20150101-20250731.txt"]
        
        for file_name in expected_files:
            file_path = os.path.join(self.output_dir, file_name)
            if os.path.exists(file_path):
                backup_name = f"{file_name}.backup_{backup_timestamp}"
                backup_path = os.path.join(self.output_dir, backup_name)
                
                import shutil
                shutil.copy2(file_path, backup_path)
                backup_mapping[file_name] = backup_name
                logger.info(f"  ✅ 备份: {file_name} -> {backup_name}")
        
        logger.info(f"📦 备份完成，共备份 {len(backup_mapping)} 个文件")
        return backup_mapping
    
    def test_new_main_entry(self) -> Dict[str, Any]:
        """测试新的轻量级主程序入口"""
        logger.info("🚀 测试新的轻量级主程序入口")
        
        start_time = time.time()
        
        try:
            # 导入并运行新的主程序
            import main
            
            # 运行主函数
            result_code = main.main()
            
            execution_time = time.time() - start_time
            
            # 验证输出文件
            output_verification = self._verify_output_files()
            
            test_result = {
                'test_name': '新轻量级主程序',
                'execution_time': execution_time,
                'result_code': result_code,
                'success': result_code == 0,
                'output_verification': output_verification,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 新主程序测试完成 - {execution_time:.2f}秒")
            return test_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ 新主程序测试失败: {e}")
            
            return {
                'test_name': '新轻量级主程序',
                'execution_time': execution_time,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def test_original_main_entry(self) -> Dict[str, Any]:
        """测试原始主程序入口"""
        logger.info("🔄 测试原始主程序入口")
        
        start_time = time.time()
        
        try:
            # 导入并运行原始主程序
            import main_v20230219_optimized
            
            # 运行主函数
            main_v20230219_optimized.main()
            
            execution_time = time.time() - start_time
            
            # 验证输出文件
            output_verification = self._verify_output_files()
            
            test_result = {
                'test_name': '原始主程序',
                'execution_time': execution_time,
                'success': True,
                'output_verification': output_verification,
                'timestamp': datetime.now().isoformat()
            }
            
            logger.info(f"✅ 原始主程序测试完成 - {execution_time:.2f}秒")
            return test_result
            
        except Exception as e:
            execution_time = time.time() - start_time
            logger.error(f"❌ 原始主程序测试失败: {e}")
            
            return {
                'test_name': '原始主程序',
                'execution_time': execution_time,
                'success': False,
                'error': str(e),
                'timestamp': datetime.now().isoformat()
            }
    
    def _verify_output_files(self) -> Dict[str, Any]:
        """验证输出文件"""
        expected_files = ["day_0_000617_20150101-20250731.txt"]
        
        verification = {
            'files_checked': len(expected_files),
            'files_found': 0,
            'files_valid': 0,
            'total_size_bytes': 0,
            'total_lines': 0,
            'file_details': {}
        }
        
        for file_name in expected_files:
            file_path = os.path.join(self.output_dir, file_name)
            
            if os.path.exists(file_path):
                verification['files_found'] += 1
                
                try:
                    file_stats = os.stat(file_path)
                    file_size = file_stats.st_size
                    verification['total_size_bytes'] += file_size
                    
                    # 验证文件内容
                    with open(file_path, 'r', encoding='utf-8') as f:
                        lines = f.readlines()
                    
                    line_count = len(lines)
                    verification['total_lines'] += line_count
                    
                    # 验证文件格式
                    header_correct = len(lines) > 0 and "股票编码|时间|买卖差" in lines[0]
                    has_data = line_count > 1
                    data_complete = line_count >= 2400  # 预期约2448行
                    
                    # 验证前复权数据
                    forward_adj_correct = False
                    if has_data and len(lines) > 1:
                        try:
                            data_line = lines[1].strip().split('|')
                            if len(data_line) >= 5:
                                original_price = float(data_line[3])
                                adjusted_price = float(data_line[4])
                                forward_adj_correct = abs(original_price - adjusted_price) > 0.01
                        except (ValueError, IndexError):
                            pass
                    
                    is_valid = header_correct and has_data and forward_adj_correct and data_complete
                    if is_valid:
                        verification['files_valid'] += 1
                    
                    verification['file_details'][file_name] = {
                        'exists': True,
                        'size_bytes': file_size,
                        'size_mb': file_size / (1024 * 1024),
                        'line_count': line_count,
                        'header_correct': header_correct,
                        'has_data': has_data,
                        'data_complete': data_complete,
                        'forward_adj_correct': forward_adj_correct,
                        'valid': is_valid
                    }
                    
                except Exception as e:
                    verification['file_details'][file_name] = {
                        'exists': True,
                        'error': str(e),
                        'valid': False
                    }
            else:
                verification['file_details'][file_name] = {
                    'exists': False,
                    'valid': False
                }
        
        return verification
    
    def compare_results(self, new_result: Dict[str, Any], original_result: Dict[str, Any]) -> Dict[str, Any]:
        """对比测试结果"""
        comparison = {
            'both_successful': new_result.get('success', False) and original_result.get('success', False),
            'performance_comparison': {},
            'output_comparison': {},
            'functionality_equivalent': False
        }
        
        # 性能对比
        if 'execution_time' in new_result and 'execution_time' in original_result:
            new_time = new_result['execution_time']
            original_time = original_result['execution_time']
            
            comparison['performance_comparison'] = {
                'new_time': new_time,
                'original_time': original_time,
                'time_difference': new_time - original_time,
                'performance_change_percent': (new_time - original_time) / original_time * 100 if original_time > 0 else 0,
                'new_is_faster': new_time < original_time
            }
        
        # 输出对比
        new_output = new_result.get('output_verification', {})
        original_output = original_result.get('output_verification', {})
        
        comparison['output_comparison'] = {
            'files_match': new_output.get('files_valid', 0) == original_output.get('files_valid', 0),
            'size_match': abs(new_output.get('total_size_bytes', 0) - original_output.get('total_size_bytes', 0)) < 1000,  # 1KB容差
            'lines_match': abs(new_output.get('total_lines', 0) - original_output.get('total_lines', 0)) < 10  # 10行容差
        }
        
        # 功能等价性
        comparison['functionality_equivalent'] = (
            comparison['both_successful'] and
            comparison['output_comparison']['files_match'] and
            comparison['output_comparison']['size_match'] and
            comparison['output_comparison']['lines_match']
        )
        
        return comparison
    
    def run_comprehensive_test(self) -> Dict[str, Any]:
        """运行综合测试"""
        logger.info("🚀 开始轻量级主程序综合测试")
        logger.info("=" * 80)
        
        try:
            # 1. 备份现有输出
            backup_mapping = self.backup_output_files()
            
            # 2. 测试新的轻量级主程序
            logger.info("\n📋 第一阶段：测试新轻量级主程序")
            new_result = self.test_new_main_entry()
            
            # 等待5秒
            logger.info("⏳ 等待5秒...")
            time.sleep(5)
            
            # 3. 测试原始主程序
            logger.info("\n📋 第二阶段：测试原始主程序")
            original_result = self.test_original_main_entry()
            
            # 4. 对比结果
            logger.info("\n📋 第三阶段：对比测试结果")
            comparison = self.compare_results(new_result, original_result)
            
            # 5. 生成测试报告
            test_summary = {
                'test_timestamp': datetime.now().isoformat(),
                'new_main_result': new_result,
                'original_main_result': original_result,
                'comparison': comparison,
                'backup_mapping': backup_mapping
            }
            
            # 保存测试结果
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            report_file = os.path.join(self.results_dir, f"lightweight_main_test_{timestamp}.json")
            
            with open(report_file, 'w', encoding='utf-8') as f:
                json.dump(test_summary, f, indent=2, ensure_ascii=False, default=str)
            
            logger.info(f"📊 测试报告已保存: {report_file}")
            
            # 6. 输出测试摘要
            self._display_test_summary(test_summary)
            
            return test_summary
            
        except Exception as e:
            logger.error(f"❌ 综合测试过程中发生异常: {e}")
            import traceback
            traceback.print_exc()
            return {'error': str(e)}
    
    def _display_test_summary(self, test_summary: Dict[str, Any]):
        """显示测试摘要"""
        logger.info("\n🎯 测试摘要:")
        logger.info("=" * 60)
        
        new_result = test_summary['new_main_result']
        original_result = test_summary['original_main_result']
        comparison = test_summary['comparison']
        
        # 功能测试结果
        logger.info("📋 功能测试:")
        logger.info(f"  新主程序: {'✅ 成功' if new_result.get('success') else '❌ 失败'}")
        logger.info(f"  原主程序: {'✅ 成功' if original_result.get('success') else '❌ 失败'}")
        logger.info(f"  功能等价: {'✅ 是' if comparison.get('functionality_equivalent') else '❌ 否'}")
        
        # 性能对比
        if 'performance_comparison' in comparison:
            perf = comparison['performance_comparison']
            logger.info("\n⚡ 性能对比:")
            logger.info(f"  新主程序: {perf.get('new_time', 0):.2f} 秒")
            logger.info(f"  原主程序: {perf.get('original_time', 0):.2f} 秒")
            logger.info(f"  性能变化: {perf.get('performance_change_percent', 0):+.1f}%")
            logger.info(f"  新版更快: {'✅ 是' if perf.get('new_is_faster') else '❌ 否'}")
        
        # 输出验证
        if 'output_comparison' in comparison:
            output = comparison['output_comparison']
            logger.info("\n📄 输出验证:")
            logger.info(f"  文件匹配: {'✅ 是' if output.get('files_match') else '❌ 否'}")
            logger.info(f"  大小匹配: {'✅ 是' if output.get('size_match') else '❌ 否'}")
            logger.info(f"  行数匹配: {'✅ 是' if output.get('lines_match') else '❌ 否'}")
        
        # 总体评估
        logger.info("\n🎉 总体评估:")
        if comparison.get('functionality_equivalent'):
            logger.info("  ✅ 轻量级主程序功能完全等价，可以安全替换")
        else:
            logger.info("  ⚠️ 轻量级主程序存在功能差异，需要进一步调试")
        
        logger.info("=" * 60)


def main():
    """主函数"""
    print("🚀 轻量级主程序入口测试系统")
    print("=" * 80)
    
    # 初始化测试器
    tester = LightweightMainTester()
    
    # 运行综合测试
    test_summary = tester.run_comprehensive_test()
    
    # 返回测试结果
    if 'error' in test_summary:
        return 1
    
    comparison = test_summary.get('comparison', {})
    return 0 if comparison.get('functionality_equivalent', False) else 1


if __name__ == '__main__':
    try:
        result = main()
        sys.exit(result)
    except KeyboardInterrupt:
        print("\n⚠️ 用户中断测试")
        sys.exit(1)
    except Exception as e:
        print(f"❌ 测试过程中发生异常: {e}")
        import traceback
        traceback.print_exc()
        sys.exit(1)
