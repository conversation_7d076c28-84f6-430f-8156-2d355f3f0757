---
归档原因: 规则文件精简重组优化
归档时间: 2025-08-10 14:30
归档说明: 本文件因内容过多(88行)、包含应该属于Auto类型的内容、存在重复规则而被归档。
         新版本将专注于用户偏好和工作流程规范，移除重复内容，精简为核心偏好。
原始文件: .augment/rules/user_rules.md
优化目标: 从88行精简到60行以内，专注用户偏好，移除重复内容
---

# MythQuant项目用户规则 (归档版本)

## 交互偏好
- **中文简体回应**：始终使用中文简体回应
- **主动文件定位**：当用户提及功能描述时，AI应自动定位到正确的文件，而不是等待用户@特定文件
- **错误文件识别**：当用户@错误的文件时，AI应主动识别并询问是否要修改正确的文件
- **模块化优先**：优先修改已经模块化拆分的文件，而不是主文件

## 技术偏好
- **避免人工补偿**：避免使用人工距离补偿来改进代码精度，因为这可能掩盖潜在问题
- **完整研究**：研究时应完整阅读文章，避免断章取义
- **高精度计算**：所有金融计算必须使用Decimal进行高精度计算
- **测试驱动**：始终遵循测试驱动的验证流程

## 输出文件命名规范
- **日级文件**：日级前复权结果文件应命名为 'day_{股票代码}_{开始日期}-{结束日期}.txt'
- **分钟级文件**：分钟级文件应命名为 'min_{频率}_{股票代码}_{开始日期}-{结束日期}.txt'
- **实际数据范围**：文件名必须使用文件内实际数据的起止时间，而不是用户请求的时间范围

## 代码修改工作流
1. **依赖关系分析**：进行依赖关系分析
2. **模块识别**：识别功能所属的正确模块
3. **测试脚本创建**：创建测试脚本验证修改
4. **备份建议**：提供备份建议（如果修改核心文件）

## 架构升级偏好
- **渐进式升级**：优先采用渐进式升级，避免破坏性变更
- **用户接口简单性**：保持用户配置接口的简单性（如user_config.py）
- **向后兼容**：新架构必须完全向后兼容，现有代码无需修改
- **门面模式**：使用门面模式隐藏内部复杂性
- **用户体验保护**：重视用户体验，技术升级对用户透明

## 问题解决偏好
- **生产问题重视**：重视"测试好用，生产报错"类型的问题
- **端到端验证**：要求进行端到端验证，不仅仅是单元测试
- **根本原因分析**：发现问题时要求系统性分析根本原因
- **用户体验优先**：优先解决影响用户体验的问题
- **配置统一性**：重视配置管理的统一性和一致性

## 质量标准偏好
- **测试环境验证**：代码修改后必须在测试环境中验证，避免直接在生产环境测试
- **测试覆盖完整**：重要功能必须有完整的测试覆盖
- **配置向后兼容**：配置变更必须保持向后兼容
- **用户友好错误**：错误处理必须用户友好
- **文档同步**：文档和代码必须同步更新

## 环境使用规范
### **分阶段环境使用**
- **AI调试阶段**：必须使用测试环境，严禁直接运行main.py
- **开发验证阶段**：在测试环境中完成功能验证
- **发布前验证**：在受控的生产环境中进行最终验证（需要手动引用此规则）
- **生产环境保护**：除发布验证外，禁止在生产环境中进行开发测试

### **测试验证流程**
- **测试优先**：代码修改类任务每次代码改动后必须测试是否引入错误
- **结果导向**：程序变更完成的唯一标准是成功生成对应的前复权txt文档
- **实际验证**：必须实际生成文件如：`day_0_000617_20150101-20250731.txt`、`1min_0_000617_20250101-20250625.txt` 等才算是测试验证通过
- **智能回退**：如果通过测试发现有问题请帮助智能回退到最近的无问题代码并再次测试确认无误

## 保存现场原则
- **成果物备份**：每次执行完指令后针对程序整体改动正确与否的测试工作中，需要对上一次生成的成果物进行妥当的备份保存
- **备份方法**：保存上一次程序成果物的方法可以是在成果物如*.txt后缀后面再次追加一个.bak，变成*.txt.bak
- **代码备份**：对于代码，也建议在本次修改代码之前，先做好备份还原点

## 咨询顾问原则
- **规则改进**：如果发现当前的rules有不合理、自相矛盾、不够专业、效率不高、架构设计本来可以更好等问题请随时提出来询问是否需要加以改进
- **主动质疑**：不要盲目执行认为不合理的rules，而是要主动提出来与我交互具体是哪一条rules在什么情况下有不合理问题、怎么解决
- **改进建议**：如果发现我当前的提问不够明确、专业、不够符合逻辑上下文或者跳步、存在项目管理风险、影响系统，请主动向我提出改进建议
- **问题发现**：如果在每次主动阅读代码过程中，发现一些不合理的问题时，请主动提出来发现的问题、以及意见建议

## 定期更新原则
- **知识沉淀**：每次回答问题的最后都进行对本次回答问题过程中发现的新知识的总结沉淀和可能涉及到rules的修改更新操作
- **举一反三**：协助做好举一反三和文档的沉淀
- **规则检视**：每次回答问题的同时检视并按需更新rules（包括增加、调优措辞、调优顺序、增加/重新为rules分类等）
- **时间戳更新**：每条rules请在尾部增加一个最后修改时间，对于未修改的条目，不用修改后面的时间戳
- **确认机制**：请在真正修改/填加rules之前，先告知我并得到我的认可再操作

---

**归档文件类型**: Manual规则 - 需要手动引用 (归档版本)
**原始更新时间**: 2025-08-10
**归档时间**: 2025-08-10 14:30
**归档原因**: 精简重组优化，内容过多，包含应属于Auto类型的内容
**后续版本**: 精简为核心用户偏好版本
