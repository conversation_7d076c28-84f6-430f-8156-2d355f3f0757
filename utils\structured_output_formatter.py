#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结构化输出格式器

统一管理所有print输出的格式和结构，提供一致的用户体验

作者: AI Assistant
创建时间: 2025-07-30
"""

import sys
from typing import Optional, Dict, Any
from datetime import datetime
import os

# 安全输出函数
def safe_print(*args, **kwargs):
    """安全的print函数，处理stdout关闭的情况"""
    try:
        # 首先尝试正常的print
        print(*args, **kwargs)
    except (ValueError, OSError, AttributeError):
        # 如果print失败，尝试直接写入文件描述符
        try:
            import os
            message = ' '.join(str(arg) for arg in args) + '\n'
            os.write(1, message.encode('utf-8', errors='replace'))
        except Exception:
            # 最后的备用方案：写入stderr
            try:
                import sys
                message = ' '.join(str(arg) for arg in args) + '\n'
                sys.stderr.write(message)
                sys.stderr.flush()
            except Exception:
                # 完全静默失败
                pass

# 安全的stdout状态检查和恢复
def _ensure_stdout_available():
    """确保stdout可用，如果被关闭则尝试恢复"""
    try:
        # 检查stdout是否可用
        if hasattr(sys.stdout, 'closed') and sys.stdout.closed:
            # stdout被关闭，尝试恢复
            import io
            sys.stdout = io.TextIOWrapper(
                io.BufferedWriter(io.FileIO(1, 'wb')),
                encoding='utf-8',
                errors='replace'
            )

        # 测试stdout是否可写
        sys.stdout.write('')
        sys.stdout.flush()

    except (ValueError, OSError, AttributeError):
        # 如果stdout完全不可用，创建一个新的
        try:
            import io
            sys.stdout = io.TextIOWrapper(
                io.BufferedWriter(io.FileIO(1, 'wb')),
                encoding='utf-8',
                errors='replace'
            )
        except Exception:
            # 最后的备用方案：使用原始的文件描述符
            import os
            sys.stdout = os.fdopen(1, 'w', encoding='utf-8', errors='replace')

# 在模块加载时确保stdout可用
_ensure_stdout_available()


class StructuredOutputFormatter:
    """结构化输出格式器"""
    
    # 统一的符号体系
    SYMBOLS = {
        # 主要流程符号
        'main_process': '🚀',      # 主要流程开始
        'sub_process': '📊',       # 子流程
        'step': '🔍',              # 步骤
        'action': '⚡',            # 具体操作
        
        # 状态符号
        'success': '✅',           # 成功
        'error': '❌',             # 错误
        'warning': '⚠️',          # 警告
        'info': 'ℹ️',             # 信息
        'progress': '🔄',          # 进行中
        
        # 数据符号
        'file': '📁',              # 文件
        'data': '📋',              # 数据
        'time': '⏰',              # 时间
        'stats': '📈',             # 统计
        'config': '⚙️',           # 配置
        
        # 结果符号
        'result': '🎯',            # 结果
        'summary': '📊',           # 汇总
        'complete': '🎉',          # 完成
    }
    
    # 层级缩进
    INDENT = {
        'level_0': '',             # 主标题
        'level_1': '  ',           # 一级内容
        'level_2': '    ',         # 二级内容
        'level_3': '      ',       # 三级内容
        'level_4': '        ',     # 四级内容
    }
    
    # 分隔符
    SEPARATORS = {
        'major': '=' * 80,         # 主要分隔符
        'minor': '-' * 60,         # 次要分隔符
        'section': '-' * 40,       # 章节分隔符
    }
    
    def __init__(self, enable_colors: bool = False):
        """
        初始化格式器
        
        Args:
            enable_colors: 是否启用颜色（暂未实现）
        """
        self.enable_colors = enable_colors
        self.current_level = 0
        self.step_counters = {}  # 各级别的步骤计数器
    
    def print_banner(self, title: str, subtitle: str = None):
        """打印程序横幅"""
        safe_print(self.SEPARATORS['major'])
        safe_print(f"{self.SYMBOLS['main_process']} {title}")
        if subtitle:
            safe_print(f"📋 {subtitle}")
        safe_print(self.SEPARATORS['major'])
        safe_print(f"{self.SYMBOLS['time']} 启动时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        safe_print()
    
    def print_main_process(self, process_name: str, details: str = None):
        """打印主要流程"""
        safe_print(f"\n{self.SYMBOLS['main_process']} {process_name}")
        if details:
            safe_print(f"{self.INDENT['level_1']}{self.SYMBOLS['info']} {details}")
        safe_print(self.SEPARATORS['minor'])
    
    def print_sub_process(self, process_name: str, current: int = None, total: int = None):
        """打印子流程"""
        progress_info = f" 【{current}/{total}】" if current and total else ""
        print(f"\n{self.SYMBOLS['sub_process']}{progress_info} {process_name}")
        print(self.SEPARATORS['section'])
    
    def print_step(self, step_name: str, step_num: int = None, total_steps: int = None, level: int = 1):
        """打印步骤"""
        indent = self.INDENT[f'level_{level}']
        progress_info = f"[{step_num}/{total_steps}] " if step_num and total_steps else ""
        print(f"{indent}{self.SYMBOLS['step']} {progress_info}{step_name}")
    
    def print_action(self, action_name: str, level: int = 2):
        """打印具体操作"""
        indent = self.INDENT[f'level_{level}']
        print(f"{indent}{self.SYMBOLS['action']} {action_name}")
    
    def print_result(self, message: str, success: bool = True, level: int = 2):
        """打印结果"""
        indent = self.INDENT[f'level_{level}']
        symbol = self.SYMBOLS['success'] if success else self.SYMBOLS['error']
        print(f"{indent}{symbol} {message}")
    
    def print_info(self, message: str, level: int = 2):
        """打印信息"""
        indent = self.INDENT[f'level_{level}']
        print(f"{indent}{self.SYMBOLS['info']} {message}")
    
    def print_warning(self, message: str, level: int = 2):
        """打印警告"""
        indent = self.INDENT[f'level_{level}']
        print(f"{indent}{self.SYMBOLS['warning']} {message}")
    
    def print_error(self, message: str, level: int = 2):
        """打印错误"""
        indent = self.INDENT[f'level_{level}']
        print(f"{indent}{self.SYMBOLS['error']} {message}")
    
    def print_file_info(self, filename: str, details: str = None, level: int = 2):
        """打印文件信息"""
        indent = self.INDENT[f'level_{level}']
        print(f"{indent}{self.SYMBOLS['file']} {filename}")
        if details:
            print(f"{indent}  {self.SYMBOLS['info']} {details}")
    
    def print_data_info(self, data_name: str, value: Any, level: int = 2):
        """打印数据信息"""
        indent = self.INDENT[f'level_{level}']
        print(f"{indent}{self.SYMBOLS['data']} {data_name}: {value}")
    
    def print_progress(self, message: str, level: int = 2):
        """打印进度信息"""
        indent = self.INDENT[f'level_{level}']
        print(f"{indent}{self.SYMBOLS['progress']} {message}")
    
    def print_stats_table(self, title: str, stats: Dict[str, Any], level: int = 1):
        """打印统计表格"""
        indent = self.INDENT[f'level_{level}']
        print(f"{indent}{self.SYMBOLS['stats']} {title}:")
        
        # 计算最大键长度用于对齐
        max_key_length = max(len(str(key)) for key in stats.keys()) if stats else 0
        
        for key, value in stats.items():
            key_padded = str(key).ljust(max_key_length)
            print(f"{indent}  {key_padded}: {value}")
    
    def print_summary(self, title: str, items: list, level: int = 1):
        """打印汇总信息"""
        indent = self.INDENT[f'level_{level}']
        print(f"\n{indent}{self.SYMBOLS['summary']} {title}")
        print(f"{indent}{self.SEPARATORS['section']}")
        
        for item in items:
            if isinstance(item, dict):
                name = item.get('name', '未知项目')
                status = item.get('status', '未知状态')
                symbol = self.SYMBOLS['success'] if status == 'success' else self.SYMBOLS['error']
                print(f"{indent}  {symbol} {name}: {status}")
            else:
                print(f"{indent}  {self.SYMBOLS['info']} {item}")
    
    def print_completion(self, message: str, success: bool = True, stats: Dict[str, Any] = None):
        """打印完成信息"""
        print(f"\n{self.SEPARATORS['major']}")
        symbol = self.SYMBOLS['complete'] if success else self.SYMBOLS['error']
        print(f"{symbol} {message}")

        if stats:
            self.print_stats_table("执行统计", stats, level=0)

        print(f"{self.SYMBOLS['time']} 完成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
        print(self.SEPARATORS['major'])

    def format_step_output(self, step_number: int, step_name: str, content: str, status: str = "info") -> str:
        """
        格式化步骤输出（按照1min_workflow_improved.md标准）

        Args:
            step_number: 步骤编号
            step_name: 步骤名称
            content: 步骤内容
            status: 状态（info, success, warning, error）

        Returns:
            格式化后的输出字符串
        """
        try:
            # 状态图标映射
            status_icons = {
                'info': '🔍',
                'success': '✅',
                'warning': '⚠️',
                'error': '❌',
                'progress': '🔄'
            }

            icon = status_icons.get(status, '📋')

            # 格式化步骤标题
            step_title = f"{icon} 【第{step_number}步】{step_name}"

            # 格式化内容
            formatted_content = []
            for line in content.split('\n'):
                if line.strip():
                    formatted_content.append(f"   {line}")

            # 组合输出
            output_lines = [step_title] + formatted_content
            return '\n'.join(output_lines)

        except Exception as e:
            return f"❌ 步骤输出格式化失败: {e}"


# 全局格式器实例
_formatter = None

def get_formatter() -> StructuredOutputFormatter:
    """获取全局格式器实例"""
    global _formatter
    if _formatter is None:
        _formatter = StructuredOutputFormatter()
    return _formatter


# 便捷函数
def print_banner(title: str, subtitle: str = None):
    """便捷函数：打印横幅"""
    try:
        # 确保stdout可用
        _ensure_stdout_available()
        get_formatter().print_banner(title, subtitle)
    except Exception as e:
        # 如果所有方法都失败，尝试最基本的输出
        try:
            import os
            message = f"=== {title} ===\n"
            if subtitle:
                message += f"--- {subtitle} ---\n"
            os.write(1, message.encode('utf-8', errors='replace'))
        except Exception:
            # 完全静默失败
            pass

def print_main_process(process_name: str, details: str = None):
    """便捷函数：打印主要流程"""
    get_formatter().print_main_process(process_name, details)

def print_sub_process(process_name: str, current: int = None, total: int = None):
    """便捷函数：打印子流程"""
    get_formatter().print_sub_process(process_name, current, total)

def print_step(step_name: str, step_num: int = None, total_steps: int = None, level: int = 1):
    """便捷函数：打印步骤"""
    get_formatter().print_step(step_name, step_num, total_steps, level)

def print_result(message: str, success: bool = True, level: int = 2):
    """便捷函数：打印结果"""
    get_formatter().print_result(message, success, level)

def print_info(message: str, level: int = 2):
    """便捷函数：打印信息"""
    get_formatter().print_info(message, level)

def print_warning(message: str, level: int = 2):
    """便捷函数：打印警告"""
    get_formatter().print_warning(message, level)

def print_error(message: str, level: int = 2):
    """便捷函数：打印错误"""
    get_formatter().print_error(message, level)

def print_completion(message: str, success: bool = True, stats: Dict[str, Any] = None):
    """便捷函数：打印完成信息"""
    get_formatter().print_completion(message, success, stats)

def print_stats_table(title: str, stats: Dict[str, Any], level: int = 1):
    """便捷函数：打印统计表格"""
    get_formatter().print_stats_table(title, stats, level)

def print_data_info(data_name: str, value: Any, level: int = 2):
    """便捷函数：打印数据信息"""
    get_formatter().print_data_info(data_name, value, level)

def print_action(action_name: str, level: int = 2):
    """便捷函数：打印具体操作"""
    get_formatter().print_action(action_name, level)


if __name__ == '__main__':
    # 示例用法
    formatter = StructuredOutputFormatter()
    
    # 示例：程序启动
    formatter.print_banner("MythQuant 量化交易数据处理系统", "前复权数据生成 & L2指标计算")
    
    # 示例：主要流程
    formatter.print_main_process("数据处理任务", "处理1只股票的1分钟数据")
    
    # 示例：子流程
    formatter.print_sub_process("互联网分钟级数据下载", 1, 2)
    
    # 示例：步骤
    formatter.print_step("智能文件选择", 1, 4)
    formatter.print_action("加载智能文件选择器配置")
    formatter.print_result("找到候选文件: test.txt")
    
    formatter.print_step("增量下载判断", 2, 4)
    formatter.print_info("检查时间范围重叠")
    formatter.print_warning("发现数据不完整")
    
    # 示例：统计信息
    stats = {
        "执行任务数": 2,
        "成功任务数": 1,
        "成功率": "50.0%",
        "总执行时间": "7.21秒"
    }
    
    # 示例：汇总
    summary_items = [
        {"name": "互联网分钟级数据下载", "status": "success"},
        {"name": "前复权数据比较分析", "status": "failed"}
    ]
    formatter.print_summary("任务执行结果", summary_items)
    
    # 示例：完成
    formatter.print_completion("程序执行完成，但部分任务失败", False, stats)
