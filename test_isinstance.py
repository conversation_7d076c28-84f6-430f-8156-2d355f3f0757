#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试isinstance判断
"""

from collections import OrderedDict

def test_isinstance():
    """测试isinstance判断"""
    print("🔍 测试isinstance判断")
    print("=" * 50)
    
    # 创建OrderedDict
    od = OrderedDict([('datetime', '2025-07-22 09:30:00'), ('close', 10.5)])
    
    print(f"OrderedDict内容: {dict(od)}")
    print(f"OrderedDict类型: {type(od)}")
    print(f"是否为dict: {isinstance(od, dict)}")
    print(f"是否为OrderedDict: {isinstance(od, OrderedDict)}")
    print(f"是否为(dict, OrderedDict): {isinstance(od, (dict, OrderedDict))}")
    print(f"hasattr __dict__: {hasattr(od, '__dict__')}")
    print(f"vars(od): {vars(od) if hasattr(od, '__dict__') else 'No __dict__'}")
    
    # 测试键值访问
    print(f"od.get('datetime'): {od.get('datetime')}")
    print(f"od.keys(): {list(od.keys())}")

if __name__ == '__main__':
    test_isinstance()
