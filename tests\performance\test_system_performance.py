#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
系统性能验证测试

验证系统在各种负载下的性能表现，确保满足10/10架构的性能要求
"""

import pytest
import time
import asyncio
import threading
from concurrent.futures import Thread<PERSON>oolExecutor, as_completed
from decimal import Decimal
from typing import List, Dict, Any
import statistics
import psutil
import gc

from src.mythquant.domain.models.stock import Stock, StockCode, Price
from src.mythquant.domain.algorithms.technical import sma, rsi, macd, bollinger_bands
from src.mythquant.domain.algorithms.financial import simple_return, sharpe_ratio, max_drawdown
from src.mythquant.infrastructure.caching.cache_manager import get_default_cache
from src.mythquant.infrastructure.monitoring.apm import get_apm_manager
from src.mythquant.infrastructure.monitoring.metrics import counter, gauge, histogram, timer
from src.mythquant.infrastructure.database.connection_pool import create_sqlite_pool


class PerformanceBenchmark:
    """性能基准测试"""
    
    def __init__(self):
        self.results = {}
        self.process = psutil.Process()
    
    def measure_execution_time(self, func, *args, **kwargs):
        """测量执行时间"""
        start_time = time.perf_counter()
        start_memory = self.process.memory_info().rss
        
        result = func(*args, **kwargs)
        
        end_time = time.perf_counter()
        end_memory = self.process.memory_info().rss
        
        execution_time = end_time - start_time
        memory_delta = end_memory - start_memory
        
        return {
            'result': result,
            'execution_time': execution_time,
            'memory_delta': memory_delta,
            'start_memory': start_memory,
            'end_memory': end_memory
        }
    
    def measure_throughput(self, func, operations_count: int, *args, **kwargs):
        """测量吞吐量"""
        start_time = time.perf_counter()
        
        for _ in range(operations_count):
            func(*args, **kwargs)
        
        end_time = time.perf_counter()
        total_time = end_time - start_time
        throughput = operations_count / total_time
        
        return {
            'operations_count': operations_count,
            'total_time': total_time,
            'throughput': throughput,
            'avg_time_per_operation': total_time / operations_count
        }


class TestAlgorithmPerformance:
    """算法性能测试"""
    
    @pytest.fixture
    def benchmark(self):
        return PerformanceBenchmark()
    
    @pytest.fixture
    def large_dataset(self):
        """生成大数据集"""
        data = []
        base_price = 100.0
        
        for i in range(10000):
            # 模拟真实的价格波动
            noise = (i % 20 - 10) * 0.01
            trend = i * 0.001
            price = base_price + trend + noise
            data.append(price)
        
        return data
    
    def test_sma_performance(self, benchmark, large_dataset):
        """测试SMA算法性能"""
        # 测试不同周期的SMA性能
        periods = [5, 10, 20, 50, 100, 200]
        
        for period in periods:
            result = benchmark.measure_execution_time(
                sma, large_dataset, period=period
            )
            
            # 性能要求：10000个数据点的SMA计算应在100ms内完成
            assert result['execution_time'] < 0.1, f"SMA({period}) 性能不达标: {result['execution_time']:.3f}s"
            
            # 验证结果正确性
            sma_result = result['result']
            assert len(sma_result) == len(large_dataset) - period + 1
            
            print(f"SMA({period}): {result['execution_time']:.3f}s, "
                  f"内存: {result['memory_delta']/1024/1024:.2f}MB")
    
    def test_rsi_performance(self, benchmark, large_dataset):
        """测试RSI算法性能"""
        periods = [14, 21, 30]
        
        for period in periods:
            result = benchmark.measure_execution_time(
                rsi, large_dataset, period=period
            )
            
            # 性能要求：RSI计算应在200ms内完成
            assert result['execution_time'] < 0.2, f"RSI({period}) 性能不达标: {result['execution_time']:.3f}s"
            
            # 验证结果正确性
            rsi_result = result['result']
            assert len(rsi_result) == len(large_dataset) - period
            assert all(0 <= val <= 100 for val in rsi_result)
            
            print(f"RSI({period}): {result['execution_time']:.3f}s")
    
    def test_macd_performance(self, benchmark, large_dataset):
        """测试MACD算法性能"""
        result = benchmark.measure_execution_time(
            macd, large_dataset, fast_period=12, slow_period=26, signal_period=9
        )
        
        # 性能要求：MACD计算应在300ms内完成
        assert result['execution_time'] < 0.3, f"MACD 性能不达标: {result['execution_time']:.3f}s"
        
        # 验证结果正确性
        macd_result = result['result']
        assert 'macd_line' in macd_result
        assert 'signal_line' in macd_result
        assert 'histogram' in macd_result
        
        print(f"MACD: {result['execution_time']:.3f}s")
    
    def test_batch_algorithm_performance(self, benchmark, large_dataset):
        """测试批量算法性能"""
        def batch_calculation():
            results = {}
            results['sma_20'] = sma(large_dataset, period=20)
            results['sma_50'] = sma(large_dataset, period=50)
            results['rsi_14'] = rsi(large_dataset, period=14)
            results['macd'] = macd(large_dataset, fast_period=12, slow_period=26, signal_period=9)
            results['bb'] = bollinger_bands(large_dataset, period=20, std_dev=2)
            return results
        
        result = benchmark.measure_execution_time(batch_calculation)
        
        # 性能要求：批量计算应在500ms内完成
        assert result['execution_time'] < 0.5, f"批量算法 性能不达标: {result['execution_time']:.3f}s"
        
        print(f"批量算法计算: {result['execution_time']:.3f}s, "
              f"内存: {result['memory_delta']/1024/1024:.2f}MB")


class TestCachePerformance:
    """缓存性能测试"""
    
    @pytest.fixture
    def benchmark(self):
        return PerformanceBenchmark()
    
    @pytest.fixture
    def cache_manager(self):
        cache = get_default_cache()
        cache.clear()
        return cache
    
    def test_cache_write_performance(self, benchmark, cache_manager):
        """测试缓存写入性能"""
        def write_operations():
            for i in range(1000):
                key = f"test:key:{i}"
                value = {
                    'id': i,
                    'data': f"test_data_{i}",
                    'timestamp': time.time(),
                    'values': list(range(10))
                }
                cache_manager.set(key, value, ttl=300)
        
        result = benchmark.measure_execution_time(write_operations)
        
        # 性能要求：1000次写入应在1秒内完成
        assert result['execution_time'] < 1.0, f"缓存写入 性能不达标: {result['execution_time']:.3f}s"
        
        # 计算吞吐量
        throughput = 1000 / result['execution_time']
        assert throughput > 1000, f"缓存写入吞吐量不达标: {throughput:.0f} ops/s"
        
        print(f"缓存写入: {result['execution_time']:.3f}s, 吞吐量: {throughput:.0f} ops/s")
    
    def test_cache_read_performance(self, benchmark, cache_manager):
        """测试缓存读取性能"""
        # 预填充缓存
        for i in range(1000):
            key = f"test:read:{i}"
            value = {'id': i, 'data': f"test_data_{i}"}
            cache_manager.set(key, value, ttl=300)
        
        def read_operations():
            results = []
            for i in range(1000):
                key = f"test:read:{i}"
                value = cache_manager.get(key)
                results.append(value)
            return results
        
        result = benchmark.measure_execution_time(read_operations)
        
        # 性能要求：1000次读取应在0.5秒内完成
        assert result['execution_time'] < 0.5, f"缓存读取 性能不达标: {result['execution_time']:.3f}s"
        
        # 验证读取结果
        read_results = result['result']
        assert len(read_results) == 1000
        assert all(r is not None for r in read_results)
        
        # 计算吞吐量
        throughput = 1000 / result['execution_time']
        assert throughput > 2000, f"缓存读取吞吐量不达标: {throughput:.0f} ops/s"
        
        print(f"缓存读取: {result['execution_time']:.3f}s, 吞吐量: {throughput:.0f} ops/s")
    
    def test_cache_hit_rate_performance(self, benchmark, cache_manager):
        """测试缓存命中率性能"""
        # 预填充缓存
        for i in range(500):
            key = f"test:hit:{i}"
            value = {'id': i, 'data': f"test_data_{i}"}
            cache_manager.set(key, value, ttl=300)
        
        def mixed_operations():
            hits = 0
            misses = 0
            
            for i in range(1000):
                key = f"test:hit:{i % 750}"  # 66.7%命中率
                value = cache_manager.get(key)
                
                if value is not None:
                    hits += 1
                else:
                    misses += 1
            
            return hits, misses
        
        result = benchmark.measure_execution_time(mixed_operations)
        hits, misses = result['result']
        
        hit_rate = hits / (hits + misses)
        
        # 验证命中率
        assert hit_rate > 0.6, f"缓存命中率过低: {hit_rate:.2%}"
        
        print(f"缓存命中率测试: {result['execution_time']:.3f}s, 命中率: {hit_rate:.2%}")


class TestConcurrencyPerformance:
    """并发性能测试"""
    
    @pytest.fixture
    def benchmark(self):
        return PerformanceBenchmark()
    
    def test_thread_pool_performance(self, benchmark):
        """测试线程池性能"""
        def cpu_intensive_task(n):
            # CPU密集型任务
            result = 0
            for i in range(n):
                result += i ** 2
            return result
        
        def single_threaded_execution():
            results = []
            for i in range(100):
                result = cpu_intensive_task(1000)
                results.append(result)
            return results
        
        def multi_threaded_execution():
            results = []
            with ThreadPoolExecutor(max_workers=4) as executor:
                futures = [executor.submit(cpu_intensive_task, 1000) for _ in range(100)]
                for future in as_completed(futures):
                    results.append(future.result())
            return results
        
        # 单线程性能
        single_result = benchmark.measure_execution_time(single_threaded_execution)
        
        # 多线程性能
        multi_result = benchmark.measure_execution_time(multi_threaded_execution)
        
        # 多线程应该比单线程快（在多核系统上）
        speedup = single_result['execution_time'] / multi_result['execution_time']
        
        print(f"单线程: {single_result['execution_time']:.3f}s")
        print(f"多线程: {multi_result['execution_time']:.3f}s")
        print(f"加速比: {speedup:.2f}x")
        
        # 在多核系统上，加速比应该 > 1
        if psutil.cpu_count() > 1:
            assert speedup > 1.0, f"多线程性能未提升: {speedup:.2f}x"
    
    def test_concurrent_cache_access(self, benchmark):
        """测试并发缓存访问"""
        cache_manager = get_default_cache()
        cache_manager.clear()
        
        def cache_worker(worker_id, operations_count):
            for i in range(operations_count):
                key = f"worker:{worker_id}:item:{i}"
                value = {'worker_id': worker_id, 'item': i, 'data': f"data_{i}"}
                
                # 写入
                cache_manager.set(key, value, ttl=300)
                
                # 读取
                retrieved = cache_manager.get(key)
                assert retrieved is not None
        
        def concurrent_cache_test():
            with ThreadPoolExecutor(max_workers=8) as executor:
                futures = []
                for worker_id in range(8):
                    future = executor.submit(cache_worker, worker_id, 100)
                    futures.append(future)
                
                for future in as_completed(futures):
                    future.result()  # 等待完成并检查异常
        
        result = benchmark.measure_execution_time(concurrent_cache_test)
        
        # 性能要求：8个线程各执行100次操作应在5秒内完成
        assert result['execution_time'] < 5.0, f"并发缓存访问 性能不达标: {result['execution_time']:.3f}s"
        
        # 验证缓存统计
        stats = cache_manager.get_stats()
        assert stats['manager_stats']['sets'] >= 800
        assert stats['manager_stats']['hits'] >= 800
        
        print(f"并发缓存访问: {result['execution_time']:.3f}s")


class TestMemoryPerformance:
    """内存性能测试"""
    
    @pytest.fixture
    def benchmark(self):
        return PerformanceBenchmark()
    
    def test_memory_usage_under_load(self, benchmark):
        """测试负载下的内存使用"""
        initial_memory = psutil.Process().memory_info().rss
        
        def memory_intensive_operations():
            # 创建大量股票对象
            stocks = []
            for i in range(1000):
                stock = Stock.create(
                    stock_code=StockCode(f"{i:06d}"),
                    name=f"股票{i}",
                    market="SZ"
                )
                
                # 添加价格历史
                for j in range(100):
                    price = Price(Decimal(f"{10 + j * 0.01:.2f}"))
                    stock.update_price(price)
                
                stocks.append(stock)
            
            return stocks
        
        result = benchmark.measure_execution_time(memory_intensive_operations)
        stocks = result['result']
        
        peak_memory = psutil.Process().memory_info().rss
        memory_increase = peak_memory - initial_memory
        
        # 清理对象
        del stocks
        gc.collect()
        
        final_memory = psutil.Process().memory_info().rss
        memory_released = peak_memory - final_memory
        
        print(f"内存测试:")
        print(f"  初始内存: {initial_memory/1024/1024:.2f}MB")
        print(f"  峰值内存: {peak_memory/1024/1024:.2f}MB")
        print(f"  内存增长: {memory_increase/1024/1024:.2f}MB")
        print(f"  释放内存: {memory_released/1024/1024:.2f}MB")
        print(f"  最终内存: {final_memory/1024/1024:.2f}MB")
        
        # 内存要求：峰值内存不应超过500MB
        assert peak_memory < 500 * 1024 * 1024, f"内存使用过高: {peak_memory/1024/1024:.2f}MB"
        
        # 内存释放率应该 > 80%
        release_rate = memory_released / memory_increase if memory_increase > 0 else 0
        assert release_rate > 0.8, f"内存释放不足: {release_rate:.2%}"
    
    def test_memory_leak_detection(self, benchmark):
        """测试内存泄漏检测"""
        def create_and_destroy_objects():
            for _ in range(100):
                # 创建对象
                stock = Stock.create(
                    stock_code=StockCode("000001"),
                    name="测试股票",
                    market="SZ"
                )
                
                # 使用对象
                for j in range(10):
                    price = Price(Decimal(f"{10 + j * 0.1:.2f}"))
                    stock.update_price(price)
                
                # 对象应该自动被垃圾回收
                del stock
        
        # 多次执行相同操作
        memory_samples = []
        
        for i in range(10):
            gc.collect()  # 强制垃圾回收
            memory_before = psutil.Process().memory_info().rss
            
            create_and_destroy_objects()
            
            gc.collect()  # 强制垃圾回收
            memory_after = psutil.Process().memory_info().rss
            
            memory_samples.append(memory_after - memory_before)
        
        # 分析内存增长趋势
        avg_memory_increase = statistics.mean(memory_samples)
        memory_trend = statistics.linear_regression(range(len(memory_samples)), memory_samples).slope
        
        print(f"内存泄漏检测:")
        print(f"  平均内存增长: {avg_memory_increase/1024:.2f}KB")
        print(f"  内存增长趋势: {memory_trend/1024:.2f}KB/iteration")
        
        # 内存增长趋势应该接近0（无明显泄漏）
        assert abs(memory_trend) < 10 * 1024, f"检测到内存泄漏: {memory_trend/1024:.2f}KB/iteration"


@pytest.mark.asyncio
class TestAsyncPerformance:
    """异步性能测试"""
    
    async def test_async_throughput(self):
        """测试异步吞吐量"""
        async def async_operation(delay=0.01):
            await asyncio.sleep(delay)
            return time.time()
        
        # 测试并发执行
        start_time = time.time()
        
        tasks = [async_operation() for _ in range(100)]
        results = await asyncio.gather(*tasks)
        
        end_time = time.time()
        total_time = end_time - start_time
        
        # 异步执行应该比同步快很多
        assert total_time < 0.5, f"异步性能不达标: {total_time:.3f}s"
        assert len(results) == 100
        
        print(f"异步并发执行100个任务: {total_time:.3f}s")


if __name__ == "__main__":
    pytest.main([__file__, "-v", "-s"])
