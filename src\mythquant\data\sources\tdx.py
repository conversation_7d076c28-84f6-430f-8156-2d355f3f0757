#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
TDX数据源模块

整合原有的func_Tdx.py、func_Tdx1.py、readTDX_cw.py等TDX相关功能
提供统一的TDX数据访问接口
"""

import os
import sys
import time
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
from pathlib import Path
import logging

# 导入新架构的配置系统
from src.mythquant.config.manager import ConfigManager

logger = logging.getLogger(__name__)


class TDXDataSource:
    """TDX数据源类 - 统一的TDX数据访问接口"""
    
    def __init__(self, config_manager: ConfigManager):
        """
        初始化TDX数据源
        
        Args:
            config_manager: 配置管理器
        """
        self.config_manager = config_manager
        self.tdx_path = config_manager.get_tdx_path()
        self.gbbq_path = config_manager.get_gbbq_path()
        
        # 初始化路径
        self._initialize_paths()
        
        # 缓存
        self._gbbq_cache = None
        self._cw_cache = None
    
    def _initialize_paths(self):
        """初始化TDX相关路径"""
        if not self.tdx_path:
            raise ValueError("TDX路径未配置")
        
        self.tdx_path = Path(self.tdx_path)
        if not self.tdx_path.exists():
            logger.warning(f"TDX路径不存在: {self.tdx_path}")
        
        # 设置各种数据路径
        self.day_path = self.tdx_path / "vipdoc"
        self.minute_path = self.tdx_path / "vipdoc"
        self.gbbq_full_path = self.tdx_path / self.gbbq_path
    
    def get_stock_list(self, market: str = "all") -> List[str]:
        """
        获取股票列表
        
        Args:
            market: 市场类型 ("sh", "sz", "all")
            
        Returns:
            股票代码列表
        """
        stock_list = []
        
        try:
            if market in ["sh", "all"]:
                # 上海市场
                sh_path = self.day_path / "sh" / "lday"
                if sh_path.exists():
                    for file in sh_path.glob("sh*.day"):
                        stock_code = file.stem[2:]  # 去掉sh前缀
                        if stock_code.startswith(('60', '68')):  # A股代码
                            stock_list.append(stock_code)
            
            if market in ["sz", "all"]:
                # 深圳市场
                sz_path = self.day_path / "sz" / "lday"
                if sz_path.exists():
                    for file in sz_path.glob("sz*.day"):
                        stock_code = file.stem[2:]  # 去掉sz前缀
                        if stock_code.startswith(('00', '30')):  # A股代码
                            stock_list.append(stock_code)
            
            logger.info(f"获取到{len(stock_list)}只股票")
            return sorted(stock_list)
            
        except Exception as e:
            logger.error(f"获取股票列表失败: {e}")
            return []
    
    def read_day_data(self, stock_code: str) -> Optional[pd.DataFrame]:
        """
        读取日线数据
        
        Args:
            stock_code: 股票代码
            
        Returns:
            日线数据DataFrame或None
        """
        try:
            # 确定市场和文件路径
            if stock_code.startswith(('60', '68')):
                market = "sh"
                file_path = self.day_path / "sh" / "lday" / f"sh{stock_code}.day"
            elif stock_code.startswith(('00', '30')):
                market = "sz"
                file_path = self.day_path / "sz" / "lday" / f"sz{stock_code}.day"
            else:
                logger.warning(f"不支持的股票代码: {stock_code}")
                return None
            
            if not file_path.exists():
                logger.warning(f"日线数据文件不存在: {file_path}")
                return None
            
            # 读取二进制数据
            data = self._read_tdx_day_file(file_path)
            if data is not None:
                logger.debug(f"成功读取{stock_code}日线数据，共{len(data)}条记录")
            
            return data
            
        except Exception as e:
            logger.error(f"读取{stock_code}日线数据失败: {e}")
            return None
    
    def read_minute_data(self, stock_code: str, frequency: int = 1) -> Optional[pd.DataFrame]:
        """
        读取分钟数据
        
        Args:
            stock_code: 股票代码
            frequency: 分钟频率 (1, 5, 15, 30, 60)
            
        Returns:
            分钟数据DataFrame或None
        """
        try:
            # 确定市场和文件路径
            if stock_code.startswith(('60', '68')):
                market = "sh"
                if frequency == 1:
                    file_path = self.minute_path / "sh" / "minline" / f"sh{stock_code}.lc1"
                elif frequency == 5:
                    file_path = self.minute_path / "sh" / "fzline" / f"sh{stock_code}.lc5"
                else:
                    logger.warning(f"不支持的分钟频率: {frequency}")
                    return None
            elif stock_code.startswith(('00', '30')):
                market = "sz"
                if frequency == 1:
                    file_path = self.minute_path / "sz" / "minline" / f"sz{stock_code}.lc1"
                elif frequency == 5:
                    file_path = self.minute_path / "sz" / "fzline" / f"sz{stock_code}.lc5"
                else:
                    logger.warning(f"不支持的分钟频率: {frequency}")
                    return None
            else:
                logger.warning(f"不支持的股票代码: {stock_code}")
                return None
            
            if not file_path.exists():
                logger.warning(f"分钟数据文件不存在: {file_path}")
                return None
            
            # 读取二进制数据
            data = self._read_tdx_minute_file(file_path, frequency)
            if data is not None:
                logger.debug(f"成功读取{stock_code} {frequency}分钟数据，共{len(data)}条记录")
            
            return data
            
        except Exception as e:
            logger.error(f"读取{stock_code} {frequency}分钟数据失败: {e}")
            return None
    
    def _read_tdx_day_file(self, file_path: Path) -> Optional[pd.DataFrame]:
        """
        读取TDX日线文件
        
        Args:
            file_path: 文件路径
            
        Returns:
            DataFrame或None
        """
        try:
            import struct
            
            with open(file_path, 'rb') as f:
                data = []
                while True:
                    buffer = f.read(32)  # 每条记录32字节
                    if len(buffer) < 32:
                        break
                    
                    # 解析二进制数据
                    record = struct.unpack('<IIIIIfII', buffer)
                    
                    # 转换数据格式
                    date = record[0]
                    open_price = record[1] / 100.0
                    high_price = record[2] / 100.0
                    low_price = record[3] / 100.0
                    close_price = record[4] / 100.0
                    amount = record[5]
                    volume = record[6]
                    
                    # 转换日期格式
                    year = date // 10000
                    month = (date % 10000) // 100
                    day = date % 100
                    date_str = f"{year:04d}-{month:02d}-{day:02d}"
                    
                    data.append({
                        'date': date_str,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': volume,
                        'amount': amount
                    })
                
                if data:
                    df = pd.DataFrame(data)
                    df['date'] = pd.to_datetime(df['date'])
                    return df
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"读取TDX日线文件失败 {file_path}: {e}")
            return None
    
    def _read_tdx_minute_file(self, file_path: Path, frequency: int) -> Optional[pd.DataFrame]:
        """
        读取TDX分钟文件
        
        Args:
            file_path: 文件路径
            frequency: 分钟频率
            
        Returns:
            DataFrame或None
        """
        try:
            import struct
            
            with open(file_path, 'rb') as f:
                data = []
                while True:
                    buffer = f.read(32)  # 每条记录32字节
                    if len(buffer) < 32:
                        break
                    
                    # 解析二进制数据
                    record = struct.unpack('<HHIIIIfII', buffer)
                    
                    # 转换数据格式
                    date = record[0]
                    time = record[1]
                    open_price = record[2] / 100.0
                    high_price = record[3] / 100.0
                    low_price = record[4] / 100.0
                    close_price = record[5] / 100.0
                    amount = record[6]
                    volume = record[7]
                    
                    # 转换日期时间格式
                    year = (date >> 11) + 2004
                    month = (date >> 7) & 0x0F
                    day = date & 0x1F
                    hour = time // 60
                    minute = time % 60
                    
                    datetime_str = f"{year:04d}-{month:02d}-{day:02d} {hour:02d}:{minute:02d}:00"
                    
                    data.append({
                        'datetime': datetime_str,
                        'open': open_price,
                        'high': high_price,
                        'low': low_price,
                        'close': close_price,
                        'volume': volume,
                        'amount': amount
                    })
                
                if data:
                    df = pd.DataFrame(data)
                    df['datetime'] = pd.to_datetime(df['datetime'])
                    return df
                else:
                    return None
                    
        except Exception as e:
            logger.error(f"读取TDX分钟文件失败 {file_path}: {e}")
            return None
    
    def get_gbbq_data(self) -> Optional[pd.DataFrame]:
        """
        获取股本变迁数据
        
        Returns:
            GBBQ数据DataFrame或None
        """
        if self._gbbq_cache is not None:
            return self._gbbq_cache
        
        try:
            if not self.gbbq_full_path.exists():
                logger.warning(f"GBBQ文件不存在: {self.gbbq_full_path}")
                return None
            
            # 这里需要实现GBBQ文件的读取逻辑
            # 暂时返回空DataFrame作为占位符
            self._gbbq_cache = pd.DataFrame()
            logger.info("GBBQ数据加载完成")
            
            return self._gbbq_cache
            
        except Exception as e:
            logger.error(f"读取GBBQ数据失败: {e}")
            return None
    
    def cleanup(self):
        """清理资源"""
        self._gbbq_cache = None
        self._cw_cache = None
        logger.debug("TDX数据源资源已清理")


# 导出
__all__ = ['TDXDataSource']
